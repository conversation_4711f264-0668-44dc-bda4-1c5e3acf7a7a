<template>
  <div class="container-wrapper">
    <div class="risk-management-ledger">
      <h2 class="table-title">食品安全风险管控清单</h2>
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="foodCategory" label="食品类别" width="100"></el-table-column>
        <el-table-column prop="categoryName" label="类别名称" width="150"></el-table-column>
        <el-table-column prop="riskType" label="风险类型" width="100"></el-table-column>
        <el-table-column prop="processControl" label="过程控制环节" width="120"></el-table-column>
        <el-table-column prop="riskPoint" label="风险点" width="150"></el-table-column>
        <el-table-column prop="riskDescription" label="风险描述" width="200"></el-table-column>
        <el-table-column prop="controlMeasures" label="管控措施" width="300">
          <template slot-scope="scope">
            <div v-html="formatMeasures(scope.row.controlMeasures)"></div>
          </template>
        </el-table-column>
        <el-table-column prop="controlFrequency" label="管控频次" width="150"></el-table-column>
        <el-table-column prop="controlTarget" label="管控目标" width="200"></el-table-column>
        <el-table-column prop="responsible" label="责任人" width="100"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FengXianManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      totalItems: 0,
      tableData: [
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '承包经营企业管理',
          riskPoint: '承包经营企业选择',
          riskDescription: '筛选把关不严，选择的承包经营企业不具备经营集中用餐单位食堂的资质或管理能力，后期难以保证供餐食品安全。',
          controlMeasures: '1.加强承包经营企业资质审核；\n2.以招投标等方式公开选择；\n3.依法签订合同，明确双方在食品安全方面的责任和义务。',
          controlFrequency: '需选择或更换承包经营企业时',
          controlTarget: '用餐单位确定的承包经营企业应依法取得管理资质类食品经营许可，食品安全管理制度健全、社会信誉良好，能够履行食品安全责任。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '承包经营企业管理',
          riskPoint: '日常管理',
          riskDescription: '对承包经营企业疏于管理，存在"一包了之"思想，未督促承包经营企业严格按照法律、法规、规章、食品安全标准以及合同约定进行经营和落实各项食品安全管理要求。',
          controlMeasures: '1.明确食品安全管理人员，每月不定时对承包经营企业食品安全管理情况开展抽查并向单位负责人报告相关情况；\n2.督促承包经营企业严格落实人员管理、进货查验、加工操作、食品留样、餐饮具清洗消毒等各项管理制度，认真执行"日管控、周排查、月调度"工作机制；\n3.督促承包经营企业对食堂从业人员定期开展培训考核，提升食品安全责任意识；\n4.督促承包经营企业针对自查、检查发现的问题及时整改。',
          controlFrequency: '每月',
          controlTarget: '能够建立有效监督管理机制，切实履行对承包经营企业的日常管理责任，督促承包经营企业落实食品安全主体责任。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '供货商管理',
          riskPoint: '供货商选择',
          riskDescription: '选择的供货商未依法取得相关资质，不具备承担食品安全责任的能力。',
          controlMeasures: '1.选择取得合法资质的供货商，留存其资质证明；\n2.建立相对固定的供货渠道；\n3.与固定供货商签订供货协议，明确食品安全责任和义务。',
          controlFrequency: '需选择或更换供货商时',
          controlTarget: '确保供货商资质合法。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '供货商管理',
          riskPoint: '供货商食品安全状况评价',
          riskDescription: '对供货商食品安全状况不了解，未对供货商食品安全状况进行跟踪评价。',
          controlMeasures: '1.建立供货商评价和退出机制，自行或委托第三方机构对供货商的食品安全状况进行定期评价；\n2.及时更换不符合要求的供货商。',
          controlFrequency: '半年',
          controlTarget: '确保供货商保持正常的食品安全管理水准。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '许可管理',
          riskPoint: '食堂食品经营许可证办理',
          riskDescription: '食品经营许可证载明的被许可人非集中用餐单位，为食堂承包经营企业或其他组织、个人，导致食品安全责任界定不明。',
          controlMeasures: '严格执行以集中用餐单位作为办证主体的规定。',
          controlFrequency: '食堂新改扩建时',
          controlTarget: '被许可主体为集中用餐单位。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '许可管理',
          riskPoint: '对许可证有效期疏于管理',
          riskDescription: '许可证已经超过有效期限，或者未按照规定的时限要求提出延续申请，影响到正常经营活动。',
          controlMeasures: '加强许可证有效期的管理，到期前严格按照规定的时限要求，及时向原发证部门提出延续申请。',
          controlFrequency: '必要时',
          controlTarget: '杜绝发生许可过期导致无证经营情况。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '许可管理',
          riskPoint: '许可条件发生变化',
          riskDescription: '加工场所、设备设施、经营布局、操作流程等许可条件发生变化，使食品安全受到影响。',
          controlMeasures: '1.改变加工经营条件前进行充分评估；\n2.严格按照食品经营许可相关法规要求进行调整；\n3.及时办理许可变更报告手续。',
          controlFrequency: '食堂改扩建或布局、流程等调整时',
          controlTarget: '许可条件变化时及时办理许可变更或报告，不因变化影响供餐食品安全。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '许可管理',
          riskPoint: '经营模式发生变化',
          riskDescription: '集中用餐单位食堂原有的自营或承包经营模式发生变化，改变了原有管理机制。',
          controlMeasures: '1.在改变经营模式前进行充分评估；\n2.明确模式转变后各方食品安全责任和义务；\n3.做好管理交接；\n4.及时完善相关许可手续。',
          controlFrequency: '经营模式变化时',
          controlTarget: '经营模式发生变化时不影响食品安全管理，依规及时完善许可相关手续。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '许可管理',
          riskPoint: '超许可范围经营',
          riskDescription: '在不具备相关许可条件情况下超范围加工餐食进而影响食品安全。',
          controlMeasures: '严格按许可核准项目开展经营。',
          controlFrequency: '每日',
          controlTarget: '实际经营项目不超过许可范围。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '人员管理',
          riskPoint: '接触直接入口食品从业人员健康证明持有情况。',
          riskDescription: '接触直接入口食品从业人员无有效健康证明，罹患有碍食品安全的疾病。',
          controlMeasures: '接触直接入口食品从业人员每年进行健康检查，取得健康证明后方可上岗。',
          controlFrequency: '每年',
          controlTarget: '所有在岗接触直接入口食品从业人员持有效健康证明。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '人员管理',
          riskPoint: '从业人员身体健康状况',
          riskDescription: '从业人员患有发热、呕吐、腹泻、咽部严重炎症等病症，皮肤有伤口或者感染等。',
          controlMeasures: '每日开展晨检，健康状况异常员工及时调离工作岗位。',
          controlFrequency: '每日',
          controlTarget: '在岗从业人员身体健康状况良好。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '人员管理',
          riskPoint: '从业人员个人卫生',
          riskDescription: '头发、皮屑、饰物、化妆用品等掉落或衣上污渍、唾沫等导致食品被污染。',
          controlMeasures: '1.从业人员穿戴清洁的工作衣帽；\n2.进入食品处理区的从业人员不留长指甲、涂指甲油，不应化妆，佩戴的饰物不外露；\n3.专间和专用操作区内的从业人员操作时，应佩戴清洁的口罩，口罩应遮住口鼻。',
          controlFrequency: '每日',
          controlTarget: '避免各类异物、污渍污染食品。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '基础风险',
          processControl: '人员管理',
          riskPoint: '从业人员手部卫生',
          riskDescription: '不洁手部携带的细菌及病毒污染食品。',
          controlMeasures: '1.加工食品前按规范洗净手部；\n2.接触直接入口食品的，加工食品前进行手部消毒；\n3.使用卫生间、接触可能污染食品的物品或者从事与食品加工无关的其他活动后，再次从事接触食品、食品容器、工具、设备等活动前应重新洗手，从事接触直接人口食品工作的还应重新消毒手部。',
          controlFrequency: '每餐次',
          controlTarget: '手部清洁、消毒到位。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '过程风险',
          processControl: '原辅材料管理',
          riskPoint: '原辅材料索证索票',
          riskDescription: '未按规定索取票证，不能实现产品溯源。',
          controlMeasures: '按规定查验并留存相关票证。',
          controlFrequency: '每批次',
          controlTarget: '所有原辅材料来源正规、可溯源。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '过程风险',
          processControl: '原辅材料管理',
          riskPoint: '进货查验',
          riskDescription: '未在入库前对各类原辅材料进行认真查验，导致不合格原辅材料进入后续环节造成食品安全隐患。',
          controlMeasures: '1.查看感官性状，无腐败、变质、污染等现象；\n2.预包装食品应包装完整、清洁、无破损，内容物与产品标识一致；标签标识完整、清晰，载明的事项符合食品安全标准和要求；食品在保质期内；\n3.验收时食品温度符合食品安全要求。',
          controlFrequency: '每批次',
          controlTarget: '各类待入库的原辅材料品质合格。',
          responsible: ''
        },
        {
          foodCategory: '餐饮食品',
          categoryName: '集中用餐单位食堂食品',
          riskType: '过程风险',
          processControl: '原辅材料管理',
          riskPoint: '储存温度',
          riskDescription: '原辅材料未按规定温度储存，易腐败变质。',
          controlMeasures: '严格按储存要求控制温度，确保冷藏温度控制在0℃～8℃、冷冻温度低于-12℃。',
          controlFrequency: '每日',
          controlTarget: '储存温度达标。',
          responsible: ''
        }
      ],
      allData: [] // 存储所有数据，用于分页展示
    }
  },
  created() {
    // 在实际应用中，这里可能需要从API获取数据
    this.loadFullData();
  },
  methods: {
    loadFullData() {
      // 这里可以添加所有表格数据，或者从API获取
      // 为了演示，这里只使用已有的几条数据
      this.allData = this.tableData;
      this.totalItems = this.allData.length;
      this.updatePageData();
    },
    updatePageData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.tableData = this.allData.slice(start, end);
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.updatePageData();
    },
    handleCurrentChange(page) {
      this.currentPage = page;
      this.updatePageData();
    },
    formatMeasures(text) {
      if (!text) return '';
      return text.replace(/\n/g, '<br>');
    },
    cellStyle() {
      return {
        padding: '5px 10px',
        fontSize: '12px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '13px',
        padding: '10px'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.risk-management-ledger {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: bold;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
