<!--批量修改分类 使用 el-drawer 组件-->
<template>
  <div>
    <custom-drawer
      :show="visible"
      :title="`打印明细-${printInfo.print_no}`"
      size="60%"
      @close="handleClose"
      @cancel="handleClose"
      @confirm="handleSubmit"
      :confirmShow="true"
      :cancelShow="false"
      confirm-text="关闭"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <!-- form搜索栏 -->
      <div class="search-bar collapse-wrapper ps-small-box">
        <el-form
          ref="detailFormRef"
          :inline="true"
          :model="searchForm"
          @submit.native.prevent
          label-width="80px"
          :rules="rules"
        >
          <el-form-item label="序号" prop="serialNumber">
            <div class="flex col-center" style="text-align: center">
              <el-input
                class="w-120 ps-input-number"
                placeholder="开始序号"
                v-model="searchForm.serialNumber.start"
              ></el-input>
              <span style="width: 10px">~</span>
              <el-input
                class="w-120 ps-input-number"
                placeholder="结束序号"
                v-model="searchForm.serialNumber.end"
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item label="订单号">
            <el-input
              class="search-item-w ps-input"
              v-model="searchForm.trade_no"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="打印状态">
            <el-select
              class="search-item-w ps-select"
              v-model="searchForm.task_status"
              placeholder="全部"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="未开始" value="not_started" />
              <el-option label="已打印" value="success" />
            </el-select>
          </el-form-item>
          <el-form-item class="m-l-10">
            <el-button type="primary" @click="searchHandle">筛选</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="openDialog">再次打印</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="content-detail-box">
        <div class="table-content">
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            border
            header-row-class-name="ps-table-header-row"
            :span-method="objectSpanMethod"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              prop="selection"
              align="center"
              class-name="ps-checkbox"
            ></el-table-column>
            <table-column v-for="item in tableSetting" :key="item.key" :col="item">
              <template #extra="{ row }">
                <div v-if="row.extra && row.extra.food_index">
                  {{ row.extra.food_index }}/{{ row.extra.food_total_index }}
                </div>
                <div v-else>--</div>
              </template>
              <template #operation="{ row }">
                <el-button type="text" size="small" @click="onPrintAgain(row)">再次打印</el-button>
              </template>
            </table-column>
          </el-table>
          <!-- table end -->

          <!-- 分页 start -->
          <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[5, 10, 20, 50, 100, 500]"
              :page-size="pageSize"
              layout="total, prev, pager, next, sizes, jumper"
              :total="totalCount"
              background
              class="ps-text"
              popper-class="ps-popper-select"
            ></el-pagination>
          </div>
        </div>
      </div>
    </custom-drawer>
    <!-- 打印 -->
    <print-ticket
      :isshow.sync="printTicketVisible"
      type="printAgainTask"
      title="再次打印"
      :select-list-id="printMode === 'multi' ? selectListId : printRowId"
      :serial-number-range="printMode === 'multi' ? serialNumberRange : printRowRange"
      @confirm="confirmPrintTicket"
      append-to-body
    ></print-ticket>
  </div>
</template>

<script>
import { PRINT_DETAIL_TABLE } from './constants'
import { debounce } from '@/utils'
import PrintTicket from '@/components/PrintTicket'
import { mergeHandle, mergeRowAction } from '@/utils/table'
export default {
  name: 'PrintListDetail',
  components: {
    PrintTicket
  },
  props: {
    printInfo: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    var checkSerialNumber = (rule, value, callback) => {
      if (!value) {
        return callback()
      }
      if (!value.start && !value.end) {
        return callback()
      }
      if (!value.start || !value.end) {
        return callback(new Error('开始序号或结束序号不能为空'))
      }
      if (!/^\d+$/.test(value.start)) {
        return callback(new Error('请输入正确的开始序号'))
      }
      if (!/^\d+$/.test(value.end)) {
        return callback(new Error('请输入正确的结束序号'))
      }
      if (parseInt(value.start) < 1) {
        return callback(new Error('开始序号必须大于等于1'))
      }
      if (parseInt(value.end) < 1) {
        return callback(new Error('结束序号必须大于等于1'))
      }
      if (parseInt(value.start) > parseInt(value.end)) {
        return callback(new Error('开始序号必须小于等于结束序号'))
      }
      callback()
    }
    return {
      mergeOpts: {
        useKeyList: {
          trade_no: ['selection', 'operation', 'trade_no', 'index_no']
        }, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        mergeKeyList: [] // 通用的合并字段，根據值合并
      },
      rules: {
        serialNumber: [{ validator: checkSerialNumber, trigger: ['blur', 'change'] }]
      },
      isLoading: false,
      tableData: [],
      tableSetting: PRINT_DETAIL_TABLE,
      searchForm: {
        serialNumber: { start: null, end: null },
        trade_no: '',
        task_status: ''
      },
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      selectListId: [],
      printTicketVisible: false,
      serialNumberRange: {},
      printRowId: null,
      printRowRange: {},
      printMode: 'multi'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.currentPage = 1
        this.getPrintTaskList()
      }
    }
  },
  methods: {
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    /**
     * 根据传入的ID数组查找具有相同trade_no的所有记录的ID集合
     * @param {Array} data - 原始数据数组
     * @param {Array} inputIds - 传入的ID数组
     * @returns {Array} - 包含所有相关ID的数组（去重且按升序排列）
     */
    findRelatedIds(data, inputIds) {
      // 1. 提取传入ID对应的trade_no
      const tradeNos = new Set();
      inputIds.forEach(id => {
        const item = data.find(record => record.id === id);
        if (item) {
          tradeNos.add(item.trade_no);
        }
      })

      // 2. 查找所有具有这些trade_no的记录的ID
      const relatedIds = [];
      data.forEach(record => {
        if (tradeNos.has(record.trade_no)) {
          relatedIds.push(record.id)
        }
      })

      // 3. 去重并排序
      return [...new Set(relatedIds)].sort((a, b) => a - b);
    },
    // 获取列表数据
    async getPrintTaskList() {
      this.isLoading = true
      let params = {
        task_manager: this.printInfo.id,
        page: this.currentPage,
        page_size: this.pageSize
      }

      if (this.searchForm.trade_no) {
        params.trade_no = this.searchForm.trade_no
      }

      if (this.searchForm.task_status) {
        params.task_status = this.searchForm.task_status
      }

      if (this.searchForm.serialNumber) {
        params.start_index_no = Number(this.searchForm.serialNumber.start)
        params.end_index_no = Number(this.searchForm.serialNumber.end)
      }

      const [err, res] = await await this.$to(this.$apis.apiBackgroundPrinterPrintTaskListPost(params))
      this.isLoading = false

      if (err) {
        this.$message.error(err.message)
        return
      }

      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 搜索
    searchHandle: debounce(function () {
      this.$refs.detailFormRef.validate(valid => {
        if (valid) {
          this.currentPage = 1
          this.getPrintTaskList()
        }
      })
    }, 300),
    // 历史记录再次打印确认
    confirmPrintTicket() {
      this.printTicketVisible = false
      this.searchHandle()
    },
    // 选择
    handleSelectionChange(val) {
      const ids = val.map(item => item.id)
      console.log('原始选择项', ids)
      this.selectListId = this.findRelatedIds(this.tableData, ids)
      console.log('加工补全选择项', this.selectListId)
      // this.selectListId = val.map(item => item.id)
    },
    // 单条再次打印
    onPrintAgain(row) {
      this.printMode = 'single'
      const ids = this.findRelatedIds(this.tableData, [row.id])
      console.log(ids);
      this.printRowId = ids
      this.printRowRange = {
        // start: row.index_no
        // end: row.extra.food_total_index
      }
      this.printTicketVisible = true
    },
    // 单挑或多条再次打印
    openDialog() {
      if (!this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.printMode = 'multi'
      this.printTicketVisible = true
    },
    // 提交表单
    handleSubmit() {
      this.handleClose()
    },
    // 关闭弹窗
    handleClose() {
      this.searchForm.serialNumber = { start: null, end: null }
      this.searchForm.trade_no = ''
      this.searchForm.task_status = ''
      this.$emit('update:visible', false)
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getPrintTaskList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPrintTaskList()
    }
  }
}
</script>

<style lang="scss" scoped>
.content-detail-box {
  padding: 0;
}

.search-bar {
  padding-top: 20px;
}

.search-item-w {
  width: 200px;
}
</style>
