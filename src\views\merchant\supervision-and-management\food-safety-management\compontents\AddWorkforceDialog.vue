<template>
  <el-drawer :visible.sync="visible" :title="title" :loading.sync="isLoading" @close="handleClose" size="1278px"
    :wrapperClosable="false" class="ps-el-drawer">
    <div class="ps-flex">
      <div class="left">
        <div class="dialog-content m-t-20 m-l-20">
          <div> 待选择（<span v-if="type != 'confirm'">{{
            memberOpts.tableData.length }}</span>）</div>
          <!-- <el-form ref="cardruleForm" label-width="100px" class="demo-ruleForm m-t-10" v-if="dialogType === 'default'">
            <div style="display:flex;">
              <el-form-item label="职工名字">
                <el-input class="ps-input" @input="changePersonNo" v-model="memberOpts.person_name"
                  placeholder="请输入"></el-input>
              </el-form-item>
              <el-form-item label="所属岗位">
                <user-group-select :multiple="true" :collapse-tags="true" class="search-item-w ps-input w-180"
                  v-model="memberOpts.selectGroup" placeholder="请下拉选择"></user-group-select>
              </el-form-item>
              <div class="m-l-20"><el-button class="ps-origin-btn" @click="handlerSearch">搜索</el-button></div>
            </div>
          </el-form> -->
          <div class="table-wrap m-t-10">
            <el-table ref="userListRef" :data="memberOpts.tableData" tooltip-effect="dark"
              header-row-class-name="table-header-row" v-loading="isLoading" @selection-change="handleSelectionChange">
              <el-table-column class-name="ps-checkbox" type="selection" width="37"
                :selectable="selectableHandle"></el-table-column>
              <el-table-column prop="name" label="姓名" align="center"></el-table-column>
              <el-table-column prop="job_title" label="所属岗位" align="center"></el-table-column>
              <el-table-column prop="phone" label="联系号码" align="center"></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="dialog-content m-t-20 m-l-20">
          <div> 已选择 （{{ selectListId.length }}）</div>
          <!-- <el-form ref="cardruleForm" label-width="100px" class="demo-ruleForm m-t-10" v-if="dialogType === 'default'">
            <div style="display:flex;">
              <el-form-item label="职工名字">
                <el-input class="ps-input" @input="changePersonNo" v-model="memberOpts.person_name"
                  placeholder="请输入"></el-input>
              </el-form-item>
              <el-form-item label="所属岗位">
                <user-group-select :multiple="true" :collapse-tags="true" class="search-item-w ps-input w-180"
                  v-model="memberOpts.selectGroup" placeholder="请下拉选择"></user-group-select>
              </el-form-item>
              <div class="m-l-20"><el-button class="ps-origin-btn" @click="handlerSearch">搜索</el-button></div>
            </div>
          </el-form> -->
          <div class="table-wrap m-t-10">
            <el-table ref="useralreadyListRef" :data="cloneConfirmList" tooltip-effect="dark"
              header-row-class-name="table-header-row" v-loading="isLoading">
              <el-table-column prop="name" label="姓名" align="center"></el-table-column>
              <el-table-column prop="job_title" label="所属岗位" align="center"></el-table-column>
              <el-table-column prop="phone" label="联系号码" align="center"></el-table-column>
              <el-table-column prop="" label="操作" align="center">
                <template slot-scope="scope">
                  <el-button type="text" class="delete-btn"
                    @click="handlerDelete(scope.row, scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

    </div>
    <div class="ps-el-drawer-footer m-l-20">
      <el-button :disabled="isBtnLoading" class="ps-cancel-btn" @click="clickCancleHandle">
        取消
      </el-button>
      <el-button class="ps-btn" type="primary" @click="clickConfirmHandle"
        :disabled="isBtnLoading || !selectListId || selectListId.length <= 0" v-loading="isBtnLoading">
        确定
      </el-button>
    </div>
  </el-drawer>
  <!-- end -->
</template>

<script>
import { debounce, to, deepClone } from '@/utils'

export default {
  name: 'AddWorkforceDialog',
  props: {
    loading: Boolean,
    isshow: Boolean,
    title: {
      type: String,
      default: '选择值班人员'
    },
    limitId: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: 'default'
    },
    useDate: {
      type: String,
      default: ''
    },
    confirm: Function
  },

  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      groupOpts: {
        value: 'id',
        label: 'group_name',
        children: 'children_list',
        checkStrictly: true
      },
      memberOpts: {
        tableData: [],
        person_name: '',
        selectGroup: [],
        departmentList: []
      },
      selectListId: [],
      personList: [], // 已选择人员列表
      pageSize: 999999, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      isBtnLoading: false,
      type: this.dialogType,
      cloneTableList: [],
      cloneConfirmList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        // this.getCardUserList()
        console.log("this.visible", this.personList)
        this.getCardUserList()
      }
    }
  },
  mounted() { },
  methods: {
    // 获取人员
    async getCardUserList() {
      this.isLoading = true
      let params = {
        page_size: this.pageSize,
        page: this.currentPage,
        appointment_state: true
      }
      if (this.memberOpts.person_name) { params.person_name = this.memberOpts.person_name }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionJobPersonJobPersonListPost(params))
      if (err) {
        this.isLoading = false
        return this.$message.error('获取人员信息失败')
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        let result = data.results || []
        let list = []
        if (this.selectListId) {
          result = result.filter(item => {
            return !this.selectListId.includes(item.id)
          })
        }
        list = deepClone(result)
        this.isLoading = false
        this.totalCount = data.count || 0
        this.memberOpts.tableData = list
        this.cloneTableList = deepClone(list)
        this.setCheckoutId()
        console.log("this.memberOpts.tableData", this.memberOpts.tableData);
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置选择
    setCheckoutId() {
      this.memberOpts.tableData = this.memberOpts.tableData.map(user => {
        if (this.dialogType === 'default') {
          this.personList.forEach(selectId => {
            if (user.id === selectId.id && this.$refs.userListRef) {
              this.$nextTick(() => {
                console.log("user 1111", user.id);
                this.$refs.userListRef.toggleRowSelection(user, true);
              });
            }
          })
        }
        return user
      })
    },
    // 输入监听
    changePersonNo: debounce(function (value) {
      // this.currentPage = 1
      // this.getCardUserList()
      if (this.type === 'confirm') {
        this.selectListId = this.cloneConfirmList.filter(item => {
          return item.name.indexOf(value) !== -1
        })
      } else {
        this.memberOpts.tableData = this.cloneTableList.filter(item => {
          return item.name.indexOf(value) !== -1
        })
      }
    }, 500),
    // 人员选择dialog 多选框选中事件
    handleSelectionChange(val) {
      console.log("val", val);
      let data = deepClone(val)
      data.forEach(item => {
        let findItem = this.cloneConfirmList.find(itemSub => itemSub.id === item.id)
        if (!findItem) {
          this.cloneConfirmList.push(deepClone(item))
          this.selectListId.push(item.id)
        }
      })
      setTimeout(() => {
        let newList = this.memberOpts.tableData.filter(itemMember => {
          return !this.selectListId.includes(itemMember.id)
        })
        this.$set(this.memberOpts, 'tableData', deepClone(newList))
        console.log("this.memberOpts", this.memberOpts.tableData, newList, this.selectListId);
      }, 100)
    },
    clickCancleHandle() {
      this.handleClose()
    },
    // 确认选择
    async clickConfirmHandle() {
      if (!this.selectListId.length) {
        return this.$message.error("请选择用户")
      }
      this.isBtnLoading = true
      let params = {
        job_person_ids: this.selectListId,
        start_date: this.useDate || '',
        end_date: this.useDate || '',
        operate_type: 'modify'
      }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionCanteenSafetyManagementModifyPersonSchedulePost(params))
      this.isBtnLoading = false
      if (err) {
        return this.$message.error('保存失败')
      }
      if (res && res.code === 0) {
        this.$message.success('保存成功')
        this.$emit('confirm', this.cloneConfirmList, this.useDate)
      } else {
        this.$message.error(res.msg)
      }
    },
    updatePersonList() {
      this.memberOpts.tableData.forEach(item => {
        if (this.selectListId.includes(item.id)) {
          this.personList.push(item)
        }
      })
    },
    // 关闭
    handleClose(e) {
      this.isLoading = false
      this.memberOpts = {
        tableData: [],
        person_name: '',
        selectGroup: []
      }
      this.visible = false
      this.type = 'default'
      this.cloneConfirmList = []
      this.$emit('close', false)
    },
    // 设置人员列表
    setPersonList(list) {
      this.personList = deepClone(list)
      this.cloneConfirmList = deepClone(list)
      this.selectListId = list.map(item => item.id)
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getCardUserList()
    },
    handlerSearch() {
      this.updatePersonList()
      this.currentPage = 1
      this.getCardUserList()
    },
    selectableHandle(row) {
      if (this.dialogType === 'default') {
        return true
      } else {
        return row.is_enable
      }
    },
    // 删除
    handlerDelete(row, index) {
      console.log("handlerDelete", row, index);
      let id = row.id
      let node = deepClone(row)
      let list = deepClone(this.memberOpts.tableData)
      list.push(deepClone(node))
      console.log("handlerDelete", list);
      this.$set(this.memberOpts, 'tableData', deepClone(list))
      this.selectListId = this.selectListId.filter(item => item !== id)
      this.cloneConfirmList.splice(index, 1)
    }

  }
}
</script>

<style lang="scss" scoped>
.delete-btn {
  padding: 0 !important;
}

.left {
  width: 658px;
}

.right {
  width: 658px;
}

.table-wrap {
  max-height: 550px;
  overflow: auto;
}
</style>
