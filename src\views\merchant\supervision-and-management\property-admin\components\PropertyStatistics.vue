<template>
  <!-- 晨检记录-->
  <div class="container-wrapper">
    <div class="tab-item m-t-20">
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <!-- <div class="align-r">
            <button-icon color="plain" @click="gotoExport">导出</button-icon>
          </div> -->
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
            empty-text="暂无数据，请查询"
          >
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item"></table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
  </div>
</template>

<script>
import { TABLE_PROPERTY_STATISTICS, SEARCH_PROPERTY_STATISTICS } from './constants'
import { debounce, deepClone } from '@/utils'
import * as dayjs from 'dayjs'
export default {
  name: 'PropertyStatistics',
  components: {},
  data() {
    return {
      isLoading: false,
      tableSetting: deepClone(TABLE_PROPERTY_STATISTICS),
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: deepClone(SEARCH_PROPERTY_STATISTICS)
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getDataList()
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            let yearMonth = dayjs(data[key].value[1])
            // 获取下个月的第一天
            let startOfNextMonth = yearMonth.add(1, 'month').startOf('month')
            // 回退一天得到本月的最后一天
            let lastDayOfMonth = startOfNextMonth.subtract(1, 'day')
            params.start_date = data[key].value[0] + '-01'
            params.end_date = lastDayOfMonth.format('YYYY-MM-DD')
          }
        }
      }
      return params
    },
    // 筛选
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    // 请求列表数据
    async getDataList() {
      this.isLoading = true
      const params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const res = await this.$apis.apiBackgroundFundSupervisionAssetAssetInfoStatisticsListPost(params)
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        let results = data.results.map(v => {
          v.statistics_date = v.statistics_date ? dayjs(v.statistics_date).format('YYYY年MM月') : ''
          return v
        })
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    }
    // gotoExport() {
    //   const params = {
    //     ...this.formatQueryParams(this.searchFormSetting),
    //     page: this.currentPage,
    //     page_size: this.totalCount
    //   }
    //   const option = {
    //     url: 'apiBackgroundFundSupervisionChannelCanteenManagementDemocraticFeedbackListExportPost',
    //     params: params
    //   }
    //   this.exportHandle(option)
    // }
  }
}
</script>

<style lang="scss" scoped></style>
