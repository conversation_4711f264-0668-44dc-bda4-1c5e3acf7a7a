<template>
  <div class="page">
    <div class="container-wrapper">
      <div class="table-wrapper">
        <div class="ps-flex-bw p-t-20 p-l-20 p-b-20 p-r-20">
          <div class="tab">
            <div
              :class="['tab-item', foodMenuType === 'foodSeting' ? 'active' : '']"
              @click="tabClick('foodSeting')"
            >
              菜品配置
            </div>
            <div
              v-if="isNutritionGuidance"
              :class="['tab-item', foodMenuType === 'nutritionAnalysis' ? 'active' : '']"
              @click="tabClick('nutritionAnalysis')"
            >
              营养分析
            </div>
          </div>
          <div>
            <button-icon
            v-if="foodMenuType === 'foodSeting'"
            color="plain"
            type="Import"
            @click="openImport()"
            >
              导入周菜谱
          </button-icon>

          <el-button
            v-if="foodMenuType === 'foodSeting'"
            size="mini"
            color="plain"
            icon="el-icon-view"
            @click="clickMenuPreview"
          >
            预览菜谱
          </el-button>
          </div>
        </div>
        <week-menu-food-seting
          v-if="foodMenuType === 'foodSeting'"
          :menuType="menu_type"
          :menuId="menu_id"
          @guidanceChange="guidanceChange"
          ref="weekMenuFoodSeting"
        />
        <menu-nutrition-analysis
          :menuType="menu_type"
          :menuId="menu_id"
          v-if="foodMenuType === 'nutritionAnalysis'"
        />
      </div>
    </div>
    <menu-preview-dialog
      v-if="menuPreviewDialogVisible"
      :isshow.sync="menuPreviewDialogVisible"
      :formDataDialog="dialogMenuPeviewInfo"
      width="900px"
      ref="menuPreviewDialog"
    />
  </div>
</template>

<script>
// import EditMealFoods from '../components/editMealFoods'
import weekMenuFoodSeting from './weekMenuFoodSeting'
import menuPreviewDialog from '../components/menu/menuPreviewDialog'
import menuNutritionAnalysis from '../components/menu/menuNutritionAnalysis'
export default {
  name: 'AddWeekRecipesSeparate',
  data() {
    return {
      menuPreviewDialogVisible: false,
      dialogMenuPeviewInfo: {},
      foodMenuType: 'foodSeting',
      menu_type: '',
      menu_id: '',
      isNutritionGuidance: false, // 是否开启营养指导
      guidance: false
    }
  },
  components: {
    weekMenuFoodSeting,
    menuPreviewDialog,
    menuNutritionAnalysis
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    guidanceChange(data) {
      this.isNutritionGuidance = data
    },
    initLoad() {
      this.menu_type = this.$route.query.menu_type
      this.menu_id = String(this.$route.query.id)
    },
    clickMenuPreview() {
      this.menuPreviewDialogVisible = true
      this.dialogMenuPeviewInfo = {
        menuId: this.menu_id,
        menuType: this.menu_type
      }
    },
    // tab 栏点击事件
    tabClick(type) {
      this.foodMenuType = type
    },
    /**
     * 导入月菜谱
     */
    openImport() {
      this.$router.push({
        name: 'MerchantImportRecipesSeparate',
        query: {
          type: 'week',
          menu: this.menu_id
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '../styles/addWeekRecipes.scss';

.tab {
  .tab-item {
    display: inline-block;
    width: 90px;
    height: 28px;
    line-height: 28px;
    margin-right: 10px;
    text-align: center;
    border-radius: 14px;
    border: solid 1px #dae1ea;
    font-size: 16px;
    color: #7b7c82;
    vertical-align: middle;
    cursor: pointer;
    &.active {
      color: #ffffff;
      background-color: #fd953c;
    }
  }
}
</style>
