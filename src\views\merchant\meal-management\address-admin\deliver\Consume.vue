<template>
  <div class="ShuReport container-wrapper">
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <el-checkbox v-model="isSelectAll" @change="selectAllChange" :disabled="tableData.length <= 0">
            全部选择
          </el-checkbox> -->
          <button-icon
            color="plain"
            type="export"
            @click="handleExport"
            v-permission="['background_order.order_reservation.get_delivery_collect_by_y_export']"
          >
            导出报表
          </button-icon>
          <!-- <button-icon color="plain" @click="openDialog">小票打印</button-icon> -->
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <!-- stripe -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          border
          header-row-class-name="ps-table-header-row"
          :span-method="objectSpanMethod"
          @select="selectSelection"
          @select-all="selectSelectionAll"
        >
          <!-- <el-table-column type="selection" prop="selection" align="center" class-name="ps-checkbox"></el-table-column> -->
          <table-column v-for="(item, index) in currentTableSetting" :key="index" :col="item"></table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <print-ticket
      :isshow.sync="printVisible"
      type="printForOrgOrder"
      :select-list-id="selectOrderId"
      @confirm="searchHandle"
    ></print-ticket>
    <!-- 报表设置 -->
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, getSevenDateRange, deepClone } from '@/utils'
import { MEAL_TYPES } from '@/utils/constants'
import { mergeHandle, mergeRowAction } from '@/utils/table'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import PrintTicket from '@/components/PrintTicket'
import searchInfoMixin from '@/mixins/searchMixin'
import { TABLE_SETTING_CONSUME, SEARCH_FORM_SETTING_CONSUME } from './constants.js'
import report from '@/mixins/report' // 混入
export default {
  name: 'Consume',
  components: { PrintTicket },
  mixins: [exportExcel, searchInfoMixin, report],
  data() {
    return {
      // 报表设置
      dialogPrintVisible: false,
      printType: 'Consume',
      currentTableSetting: deepClone(TABLE_SETTING_CONSUME),
      tableSetting: deepClone(TABLE_SETTING_CONSUME),
      tableData: [],
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      isLoading: false, // 刷新数据
      searchFormSetting: this.initSearchFormSetting(),
      mergeOpts: {
        useKeyList: {
          org_id: ['selection', 'org_name', 'set_meal_name', 'foods_names']
        }, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        mergeKeyList: [] // 通用的合并字段，根據值合并
      },
      printVisible: false,
      selectOrderId: [],
      isSelectAll: false, // 是否全部选择
      selectListIdCount: 0,
      organizationList: [] // 组织列表
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    // 监听父组件的tabType变化，当切换到当前组件时恢复搜索信息
    '$parent.tabType': {
      handler(newVal) {
        if (newVal === 'consume') {
          // 切换到shu tab时恢复搜索信息
          this._restore_searchInfo('consume')
        }
      },
      immediate: true
    },
    'searchFormSetting.org_ids.value': function () {
      this.getAddressAreaList()
    },
    // 配送区域
    'searchFormSetting.area_ids.value': {
      handler() {
        this.searchFormSetting.l1_addr.dataList = []
        // this.searchFormSetting.l1_addr.value = []
        this.loadAddress(1, this.searchFormSetting.area_ids.value)
      },
      immediate: true,
      deep: true
    },
    // 一级地址
    'searchFormSetting.l1_addr.value': {
      handler() {
        this.searchFormSetting.l2_addr.dataList = []
        // this.searchFormSetting.l2_addr.value = []
        if (this.searchFormSetting.l1_addr.value.length) {
          this.loadAddress(2)
        }
      },
      immediate: true,
      deep: true
    },
    'searchFormSetting.l2_addr.value': {
      handler() {
        this.searchFormSetting.l3_addr.dataList = []
        // this.searchFormSetting.l3_addr.value = ''
        if (this.searchFormSetting.l2_addr.value.length) {
          this.loadAddress(3)
        }
      },
      immediate: true,
      deep: true
    },
    'searchFormSetting.l3_addr.value': {
      handler() {
        this.searchFormSetting.l4_addr.dataList = []
        // this.searchFormSetting.l4_addr.value = ''
        if (this.searchFormSetting.l3_addr.value.length) {
          this.loadAddress(4)
        }
      },
      immediate: true,
      deep: true
    },
    'searchFormSetting.l4_addr.value': {
      handler() {
        this.searchFormSetting.l5_addr.dataList = []
        // this.searchFormSetting.l5_addr.value = ''
        if (this.searchFormSetting.l4_addr.value.length) {
          this.loadAddress(5)
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.getOrganizationList()
  },
  beforeDestroy() {
    // 组件销毁前保存搜索信息
    this._save_searchInfo('consume')
  },
  methods: {
    /**
     * 初始化搜索表单配置
     */
    initSearchFormSetting() {
      const searchFormSetting = deepClone(SEARCH_FORM_SETTING_CONSUME)

      // 设置日期范围
      searchFormSetting.select_date.value = getSevenDateRange(1)

      // 设置餐段数据
      searchFormSetting.take_meal_time.dataList = MEAL_TYPES

      // 设置组织ID
      searchFormSetting.org_ids.value = [this.$store.getters.organization]

      return searchFormSetting
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = this.currentTableSetting.filter(item => {
        return item.key !== 'img'
      })
      // console.log(this.findNamesByIds(this.organizationList, params.org_ids));
      // return
      const organizationStrs = this.findNamesByIds(this.organizationList, params.org_ids).join('、')
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          isUstomTableHeaders: true,
          print_type: this.printType,
          print_title: '消费点配餐',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundOrderOrderReservationGetDeliveryOrgCollect', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          // collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          show_current_page: true, // 显示底部当前页码
          mergeOpts: JSON.stringify(this.mergeOpts),
          params: JSON.stringify({
            ...params,
            start_date: params.reservation_date_start,
            end_date: params.reservation_date_end,
            mealType: MEAL_TYPES.find(item => item.value === params.take_meal_time)?.label || '',
            organizationStrs,
            page: 1,
            page_size: this.totalCount ? this.totalCount : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    initLoad() {
      this.getShuList()
      this.getAddressAreaList()
      this.loadAddress(1)
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.printVisible = false
        this.currentPage = 1
        this.resetSelectAll()
        // 保存搜索信息
        this._save_searchInfo('consume')
        this.getShuList()
      }
      // 特殊情况处理  不能放在watch中,watch做切换回显时会触发 所以放在这里
      this.clearSubAddress(e.label)
    }, 300),
    // 清空下级地址
    clearSubAddress(label) {
      // 定义地址层级映射关系
      const ADDRESS_LEVEL_MAP = {
        '配送区域': ['l1_addr', 'l2_addr', 'l3_addr', 'l4_addr', 'l5_addr'],
        '一级地址': ['l2_addr', 'l3_addr', 'l4_addr', 'l5_addr'],
        '二级地址': ['l3_addr', 'l4_addr', 'l5_addr'],
        '三级地址': ['l4_addr', 'l5_addr'],
        '四级地址': ['l5_addr']
      }
      const levelsToClear = ADDRESS_LEVEL_MAP[label]
      if (levelsToClear) {
        levelsToClear.forEach(level => {
          this.searchFormSetting[level].value = []
        })
      }
    },
    resetSearchHandle() {
      this.$refs.searchRef.resetForm()
      this.searchHandle()
    },
    resetHandle() {
      this.resetSelectAll()
      this.searchFormSetting.org_ids.value = [this.$store.getters.organization]
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' || data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.reservation_date_start = data[key].value[0]
            params.reservation_date_end = data[key].value[1]
          }
        }
      }
      return params
    },
    async getShuList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderReservationGetDeliveryOrgCollect({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = this.flattenDataList(res.data.results || [])
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 加工数组扁平化返回
    flattenDataList(dataList) {
      if (!dataList.length) return []
      return dataList.flatMap(org =>
        org.set_meal_name_list.flatMap(setMeal =>
          setMeal.food_names_list.map(foodItem => ({
            org_id: org.org_id,
            org_name: org.org_name,
            set_meal_name: setMeal.set_meal_name,
            foods_names: foodItem.foods_names,
            set_meal_count: foodItem.set_meal_count,
            // 1、套餐数量有， 菜品数量 直接显示 套餐数量
            // 2、非套餐那种不用变
            food_count: foodItem.set_meal_count ? foodItem.set_meal_count : foodItem.food_count
          }))
        )
      )
    },
    // 表格合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    // 分页页数change事件
    async handleSizeChange(val) {
      this.pageSize = val
      await this.getShuList()
      this.changeTableSelection()
    },
    // 分页页码change事件
    async handleCurrentChange(val) {
      this.currentPage = val
      await this.getShuList()
      this.changeTableSelection()
    },
    // 单选
    selectSelection(val, row) {
      console.log(val, row);
      this.changeSelectSelection(val, row)
    },
    // 全选
    selectSelectionAll(val) {
      console.log(val);
      let ids = val.map(v => v.org_id)
      this.selectOrderId = [...new Set(ids)]
    },
    // 单选
    changeSelectSelection(val, row) {
      if (!row.org_id) {
        this.$message.error('消费点不在，无法发起打印')
        this.$refs.tableData.toggleRowSelection(row, false);
        return
      }
      // isSelect true为选中 false 为取消
      let isSelect = val.length && val.indexOf(row) > -1
      // findIndex返回-1则不存在，>0则存在
      let n = this.selectOrderId.findIndex(v => {
        return v === row.org_id
      })
      if (isSelect) {
        if (n < 0) this.selectOrderId.push(row.org_id)
      } else {
        if (n > -1) this.selectOrderId.splice(n, 1)
      }
    },
    changeTableSelection() {
      if (this.tableData.length) {
        this.tableData.forEach((item, index) => {
          // 匹配勾选上
          if (this.selectOrderId.includes(item.org_id)) {
            this.$nextTick(() => {
              this.$refs.tableData.toggleRowSelection(item)
            })
          }
        })
      }
    },
    async getAddressAreaList() {
      const res = await this.$apis.apiAddressAddersAreaAllPost({
        used_org_ids: this.searchFormSetting.org_ids.value
      })
      if (res.code === 0) {
        this.searchFormSetting.area_ids.dataList = [
          {
            name: '未命名区域',
            id: 0
          },
          ...res.data
        ]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态配送点数据
    async loadAddress(level, areaIds) {
      // 这里的level是这样的：一级组织的level=1，传给后端需要-1
      let params = {
        page: 1,
        page_size: 99999,
        level: level - 1,
        used_for_web: true
      }
      if (level === 2) {
        params.parent_id = this.searchFormSetting.l1_addr.value
      } else if (level === 3) {
        params.parent_id = this.searchFormSetting.l2_addr.value
      } else if (level === 4) {
        params.parent_id = this.searchFormSetting.l3_addr.value
      } else if (level === 5) {
        params.parent_id = this.searchFormSetting.l4_addr.value
      }
      if (areaIds) {
        params.area_id = areaIds
      }
      const res = await this.$apis.apiAddressAddersCenterListPost(params)
      if (res.code === 0) {
        if (level === 1) {
          this.searchFormSetting.l1_addr.dataList = res.data.results
        } else if (level === 2) {
          this.searchFormSetting.l2_addr.dataList = res.data.results
        } else if (level === 3) {
          this.searchFormSetting.l3_addr.dataList = res.data.results
        } else if (level === 4) {
          this.searchFormSetting.l4_addr.dataList = res.data.results
        } else if (level === 5) {
          this.searchFormSetting.l5_addr.dataList = res.data.results
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出报表
    handleExport() {
      const option = {
        type: 'ShuReport',
        url: 'apiBackgroundOrderOrderReservationGetDeliveryOrgCollect',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          is_export: true
        }
      }
      this.exportHandle(option)
    },
    openDialog() {
      if (!this.selectOrderId.length) {
        return this.$message.error('请先选择数据！')
      }
      console.log('selectOrderId', this.selectOrderId);
      this.printVisible = true
    },
    /**
     * 全选监听
     */
    async selectAllChange(value) {
      this.selectOrderId = []
      this.$refs.tableData.clearSelection()
      if (value) {
        // 全选
        const res = await this.$apis.apiBackgroundOrderOrderReservationGetDeliveryCollectByOrgIds({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
        this.isLoading = false
        let results = res.data.org_ids || []
        if (results.length > 0) {
          this.selectOrderId = deepClone(results)
          this.selectListIdCount = results.length
          this.changeTableSelection()
        } else {
          this.isSelectAll = false
        }
      } else {
        // 反选列表数据
        this.$refs.tableData.clearSelection()
        this.selectOrderId = []
      }
    },
    /**
     * 重置全选
     */
    resetSelectAll() {
      // if (this.isSelectAll) {
      this.isSelectAll = false
      this.selectOrderId = []
      this.$refs.tableData.clearSelection()
      // }
    },
    // 通过组织id获取组织名称
    findNamesByIds(treeData, selectedIds) {
      // 创建映射表：id -> name
      const idToNameMap = new Map();
      // 递归遍历树形结构
      function traverse(nodes) {
        nodes.forEach(node => {
          // 将当前节点的id和name存入映射表
          idToNameMap.set(node.id, node.name);
          // 如果存在子节点，递归遍历子节点
          if (node.children_list && node.children_list.length > 0) {
            traverse(node.children_list);
          }
        });
      }
      // 从根节点开始遍历
      traverse(treeData);
      // 根据选中的id列表获取对应的名称
      return selectedIds.map(id => idToNameMap.get(id) || null);
    },
    // 获取下级组织数据
    async getOrganizationList(id) {
      const res = await this.$apis.apiBackgroundOrganizationOrganizationTreeListPost({
        org_id: id
      })
      if (res.code === 0) {
        this.organizationList = res.data
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
</style>
