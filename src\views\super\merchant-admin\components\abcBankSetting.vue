<template>
  <div class="container-wrapper abcSetting" v-loading="isLoading">
    <div class="l-title clearfix">
      <span class="float-l min-title-h">农行配置</span>
      <el-button @click="showPointDialog" size="mini" class="float-r" v-if="type == 'root'">埋点字典编辑</el-button>
    </div>
    <div class="form-wrapper m-t-10"  v-if="type == 'root'">
      <el-form ref="abcBanksetting" :model="formData" size="small" label-width="100px">
        <div class="m-b-20 ps-origin">农行埋点配置</div>
        <el-form-item label="埋点：" prop="point">
          <div class="ps-flex">
          <el-select v-model="formData.point" class="ps-select w-300" filterable>
            <el-option v-for="item in pointList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
          <div class="add-wrapper">
            <el-button type="primary" size="small" class="ps-origin-btn" @click="saveSettingHandle('point')">保存</el-button>
         </div>
        </div>
        </el-form-item>
        <div class="m-b-20 ps-origin">农行清分配置</div>
        <el-form-item label="清分方式：" prop="payWay">
          <div class="ps-flex">
            <el-select v-model="formData.payWay" class="ps-select w-300" filterable>
            <el-option v-for="item in payWayList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
            <div class="add-wrapper">
              <el-button type="primary" size="small" class="ps-origin-btn" @click="saveSettingHandle('payWay')">保存</el-button>
            </div>
          </div>
        </el-form-item>
        <div class="m-t-10 m-b-20 ps-origin">农行硬件对接配置</div>
        <el-form-item label="农行硬件对接" label-width="120px">
          <el-switch v-model="formData.isOpenAbcHardware" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <el-form-item label="companyID" label-width="120px" v-if="formData.isOpenAbcHardware">
          <el-input v-model="formData.abcCompanyId" class="margin-input w-180 ps-input"></el-input>
        </el-form-item>
        <div class="add-wrapper">
          <el-button type="primary" size="small" class="ps-origin-btn" @click="saveSettingHandle('hardware')">保存</el-button>
        </div>
      </el-form>
    </div>
    <div v-else class="empty-style m-t-20 ">
      <img class="empty-img" src="@/assets/img/table-no-data.png"/>
      <div class="ps-text">请在第一级组织进行配置</div>
    </div>
    <!-- 编辑修改埋点字典弹窗 start-->
    <el-drawer
      title="埋点字典编辑"
      :visible.sync="isShowPointDialog"
      size="600px"
      :modal-append-to-body="false"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      class="ps-el-drawer"
      direction="rtl">
      <div class="drawer-content">
        <el-button size="small" class="float-r m-b-10" @click="addPointDialog">添加</el-button>
        <el-table ref="pointTable" width="100%" :data="tableDataList" tooltip-effect="dark" max-height="700"
          header-row-class-name="ps-table-header-row" row-key="id" stripe v-loading="isTableLoading">
          <el-table-column label="埋点项目名称" prop="name" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.type != 'add'">{{ scope.row.name }}</div>
              <el-input v-if="scope.row.type == 'add'" placeholder="请输入" class="ps-input" v-model="scope.row.name"
                type="text"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="" align="center" width="200px" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="editPointDialog(scope.row)">
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                @click="getOrEditPoint('delete', scope.row, scope.$index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="drawer-footer">
          <el-button @click="isShowPointDialog = false">关 闭</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 编辑修改埋点字典弹窗 end-->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="isShowAddPointDialog"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="handleDialogClosed">
      <el-form :model="pointForm" ref="pointForm" label-width="100px" :rules="rules">
        <el-form-item label="埋点名称" prop="name" required>
          <el-input
            v-model="pointForm.name"
            placeholder="请输入埋点名称"
            maxlength="30"
            show-word-limit>
          </el-input>
        </el-form-item>
        <el-form-item label="产品代码" prop="firefly_prod_code">
          <el-input
            v-model="pointForm.firefly_prod_code"
            placeholder="请输入产品代码"
            maxlength="30"
            show-word-limit>
          </el-input>
        </el-form-item>
        <el-form-item label="产品名称" prop="firefly_prod_name">
          <el-input
            v-model="pointForm.firefly_prod_name"
            placeholder="请输入产品名称"
            maxlength="30"
            show-word-limit>
          </el-input>
        </el-form-item>
        <el-form-item label="消费场景" prop="firefly_prod_type">
          <el-input
            v-model="pointForm.firefly_prod_type"
            placeholder="请输入消费场景"
            maxlength="30"
            show-word-limit>
          </el-input>
        </el-form-item>
        <el-form-item label="备用字段1" prop="page_prod_info_1">
          <el-input
            v-model="pointForm.page_prod_info_1"
            placeholder="请输入备用字段1"
            maxlength="99"
            show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitPointForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { to } from '@/utils'
import { deepClone } from '@/assets/js/util'

export default {
  name: 'AbcBankSetting',
  props: {
    type: String, // 区别是否是顶级的
    infoData: { // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    organizationData: Object,
    restoreHandle: Function
  },
  data() {
    return {
      isLoading: false, // 页面加载
      isTableLoading: false, // 表格数据加载
      pointList: [], // 埋点列表
      formData: {
        point: '',
        payWay: "0",
        isOpenAbcHardware: false,
        abcCompanyId: ''
      }, // 配置数据
      isShowPointDialog: false, // 是否显示编辑埋点弹窗
      isAddPoint: false, // 是否添加埋点
      tableDataList: [], // 弹窗表格数据
      payWayList: [ // 清分方式列表
        {
          name: '默认清分',
          id: "0"
        },
        {
          name: '充值清分',
          id: "1"
        },
        {
          name: '消费清分',
          id: "2"
        }
      ],
      isShowAddPointDialog: false,
      dialogTitle: '添加埋点字典',
      pointForm: {
        name: '',
        firefly_prod_code: '',
        firefly_prod_name: '',
        firefly_prod_type: '',
        page_prod_info_1: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入埋点名称', trigger: ['blur', 'change'] },
          { max: 30, message: '长度不能超过30个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      // 获取埋点
      this.getOrEditPoint("get")
      this.getOrEditPoint("getByOrgs")
      // 获取清分
      this.modifyOrGetSettlementType('get')
      this.modifyAbcSetting(false)
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 发送请求
    async saveSettingHandle(type) {
      if (this.isLoading) {
        return
      }
      if (this.isShowPointDialog) {
        return this.$message.error('请先关闭弹窗')
      }
      if (type === 'point' && !this.formData.point) {
        return this.$message.error('请先选择埋点项目')
      }
      if (type === 'payWay' && !this.formData.payWay) {
        return this.$message.error('请先选择清方式')
      }
      if (type === 'point') {
        // 保存埋点
        this.modifySetting()
      }
      if (type === 'payWay') {
        // 保存清分
        this.modifyOrGetSettlementType()
      }
      if (type === 'hardware' && this.formData.isOpenAbcHardware && !this.formData.abcCompanyId) {
        return this.$message.error('请先填写农行硬件对接公司ID')
      }
      if (type === 'hardware') {
        this.modifyAbcSetting(true)
      }
    },
    // 修改埋点配置
    async modifySetting() {
      console.log("modifySetting", this.organizationData);
      let params = {
        id: this.formData.point,
        company_id: this.organizationData.company
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationSetComBuryInfoPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    //  设置清分模式
    async modifyOrGetSettlementType(type) {
      let params = {
        company_id: this.organizationData.company
      }
      if (type === 'get') {
        params.is_get = true
      } else {
        params.settlement_type = this.formData.payWay
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationSetOrGetSettlementTypePost(params))
      this.isLoading = false
      if (err) {
        return this.$message.success(err.message)
      }
      if (res && res.code === 0) {
        if (type === 'get') {
          var data = res.data || {}
          if (data && Reflect.has(data, "id")) {
            this.formData.payWay = data.id.toString() || ''
          }
        } else {
          this.$message.success('修改成功')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取/修改农行硬件配置
    async modifyAbcSetting(type) {
      let params = {
        type,
        company_id: this.organizationData.company,
        switch: this.formData.isOpenAbcHardware
      }
      if (this.formData.isOpenAbcHardware) {
        params.abc_com_id = this.formData.abcCompanyId
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationSetComAbcIdPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (type) {
          this.$message.success('修改成功')
        } else {
          this.formData.isOpenAbcHardware = res.data.abc_setting.switch
          this.formData.abcCompanyId = res.data.abc_setting.abc_com_id
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    //  显示隐藏弹窗
    showPointDialog() {
      this.tableDataList = deepClone(this.pointList)
      this.isShowPointDialog = !this.isShowPointDialog
    },
    //  打开添加弹窗
    addPointDialog() {
      this.dialogTitle = '添加埋点字典'
      this.resetForm()
      this.isShowAddPointDialog = true
    },
    // 打开编辑弹窗
    editPointDialog(row) {
      this.dialogTitle = '编辑埋点字典'
      this.resetForm()
      this.$nextTick(() => {
        this.pointForm = {
          name: row.name || '',
          firefly_prod_code: row.firefly_prod_code || '',
          firefly_prod_name: row.firefly_prod_name || '',
          firefly_prod_type: row.firefly_prod_type || '',
          page_prod_info_1: row.page_prod_info_1 || '',
          id: row.id || ''
        }
      })
      this.isShowAddPointDialog = true
    },
    // 重置表单
    resetForm() {
      this.pointForm = {
        name: '',
        firefly_prod_code: '',
        firefly_prod_name: '',
        firefly_prod_type: '',
        page_prod_info_1: ''
      }
      // 如果表单ref存在，则清除验证
      if (this.$refs.pointForm) {
        this.$refs.pointForm.clearValidate()
      }
    },
    // 处理取消按钮
    handleCancel() {
      this.isShowAddPointDialog = false
      this.resetForm()
    },
    // 处理弹窗关闭
    handleDialogClosed() {
      this.resetForm()
    },
    // 提交表单
    submitPointForm() {
      this.$refs.pointForm.validate((valid) => {
        if (valid) {
          // 这里添加表单提交逻辑
          // 可以根据 dialogTitle 判断是添加还是编辑操作
          if (this.dialogTitle === '添加埋点字典') {
            // 添加逻辑
            this.getOrEditPoint('add', this.pointForm, -1)
          } else {
            // 编辑逻辑
            this.getOrEditPoint('edit', this.pointForm, -1)
          }
          this.handleCancel() // 成功后关闭弹窗并重置表单
        } else {
          return false
        }
      })
    },
    // 保存埋点
    async getOrEditPoint(type, item, index) {
      var params = {}
      var message = ''
      console.log("savePoint", item, "index", index);
      switch (type) {
        case "get": // 获取埋点列表
          params.is_get_list = true
          break;
        case "getByOrgs": // 根据组织id 获取配置数据
          params.company_id = this.organizationData.company
          params.is_get_list = true
          break;
        case "add": // 添加埋点数据
        case "edit": // 编辑埋点数据
          if (!item.name || item.name.length === 0) {
            return this.$message.error('请先填写名称')
          }
          params.name = item.name
          params.firefly_prod_code = item.firefly_prod_code
          params.firefly_prod_name = item.firefly_prod_name
          params.firefly_prod_type = item.firefly_prod_type
          params.page_prod_info_1 = item.page_prod_info_1
          if (type === "edit") {
            params.id = item.id
            params.is_delete = false
          }
          message = '添加成功'
          break;
        case "delete":
          params.is_delete = true
          params.id = item.id
          message = '删除成功'
          break;
        default:
          break;
      }
      this.isTableLoading = true
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationGetOrCreateBuryinfoPost(params))
      this.isTableLoading = false
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (type === 'add' || type === 'edit') {
          this.getOrEditPoint("get")
        } else {
          this.updateTableList(type, index, message, res.data)
        }
      } else {
        this.$message.error(res.msg)
      }
      this.isAddPoint = false
    },
    //  更新数据列表
    updateTableList(type, index, message, data) {
      var tableList = deepClone(this.tableDataList)
      // 获取数据
      if (type === 'get') {
        this.pointList = data || []
        this.$set(this, 'tableDataList', this.pointList)
      }
      if (type === 'getByOrgs') {
        console.log("getByOrgs", data);
        if (data && typeof data === 'object' && Reflect.has(data, 'id')) {
          this.$set(this.formData, 'point', data.id)
          console.log("this.formData.point", this.formData.point);
        }
      }
      // 添加数据
      if (type === 'add') {
        if (index >= 0 && index < tableList.length) {
          tableList[index].type = 'get'
        }
        this.$set(this, 'tableDataList', tableList)
        console.log("this.tableDataList", this.tableDataList);
        this.$message.success(message)
        this.getOrEditPoint('get')
        this.isAddPoint = false
      }
      // 删除数据
      if (type === "delete") {
        if (index >= 0 && index < tableList.length) {
          tableList.splice(index, 1)
          this.pointList.splice(index, 1)
        }
        this.$set(this, 'tableDataList', tableList)
        this.$message.success(message)
      }
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.abcSetting {
  .add-wrapper {
    margin-left: 60px;
    .el-button {
      width: 120px;
    }
  }
  .empty-style {
    display: flex;
    flex-direction: column;
    align-items: center;
    .empty-img {
      display: inline-block;
      width: 127px;
      height: 99px;
      vertical-align: middle;
      margin: 0 20px;
    }
  }
}

.drawer-content {
  padding: 20px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  text-align: left;
}
</style>
