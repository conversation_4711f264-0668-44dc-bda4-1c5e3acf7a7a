<template>
  <div class="permisson-manager container-wrapper">
    <!--刷新页面-->
    <refresh-tool @refreshPage="searchHandler" />
    <!--搜索层-->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler" v-if="false"
      @reset="resetHandler"></search-form>
    <!--表格-->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="handlerAddorEditRecord('add')" type="add">新增</button-icon>
        </div>
      </div>
      <div class="table-content m-t-20">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" height="460" stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #image="{ row }">
              <el-image class="img-item" :src="row.url" :fit="'fill'" :preview-src-list="getPreViewList(row)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>

            <template #UIType="{ row }">
              {{ getUIType(row) }}
            </template>
            <template #status="{ row }">
              <el-switch v-model="row.is_release" active-color="#ff9b45" inactive-color="#ffcda2" @change="memberStatusChange($event, row, row.index)" v-loading="row.isSwitchLoading"></el-switch>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text-origin"
                @click="handlerAddorEditRecord('edit', row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-text-warn" @click="handlerDeleteRecord(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination @size-change="handlerSizeChange" @current-change="handlerPageChange" :current-page.sync="currentPage"
          :page-size.sync="pageSize" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount"></pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>
<script>
import { deepClone, to, debounce, setSessionStorage } from '@/utils';
import { SEARCH_FORM_PROMOTIONAL_PICTURE_DATA, TABLE_HEAD_PROMOTIONAL_PICTURE_DATA } from './constants.js'

export default {
  name: 'PromotionalPictureSetting',
  data() {
    return {
      searchFormSetting: deepClone(SEARCH_FORM_PROMOTIONAL_PICTURE_DATA), // 表单数据
      tableSettings: deepClone(TABLE_HEAD_PROMOTIONAL_PICTURE_DATA), // 表头数据
      tableData: [], // 表格数据
      isLoading: false,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页数量
      totalCount: 0,
      dialogVisible: false, // 弹窗是否可见
      dialogTitle: '', // 弹窗标题
      dialogType: 'permission', // 弹窗类型
      type: 'add', // 类型
      selectInfo: {}, // 选择的数据
      srcList: ['https://img2.baidu.com/it/u=4603732,1550247081&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800']
    }
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      this.getDataList()
    },
    // 节下流咯
    searchHandler: debounce(function () {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getDataList()
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.currentPage = 1;
      this.getDataList()
    },
    // 表格项目编辑
    handlerTableItemEdit(row, index, key) {
      console.log("row", row, index);
      this.$set(this.tableData[index], key, true)
    },
    // 保存
    async handlerTableItemSave(row, index, key) {
      var params = {
        id: row.id,
        remark: row.remark,
        price: row.price
      }
      const [err, res] = await to(this.$api.save(params))
      if (err) {
        return this.$message.error(err.message || '保存失败')
      }
      if (res && res.code === 0) {
        this.$message.success("保存成功")
        this.$set(this.tableData[index], key, false)
      } else {
        this.$message.error(res.msg || '保存失败')
      }
    },
    // 编辑记录
    handlerEditRecord(row) {
      console.log("handlerDetail", row);
    },
    // 跳转键值管理
    handlerDetailKeyManager() {
      this.$router.push({
        name: 'MemberKeyManager'
      })
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    handlerPageChange(val) {
      console.log("handlerPageChange", val);
      this.currentPage = val
      this.getDataList()
    },
    /**
     * 页面条数改变监听
     */
    handlerSizeChange(val) {
      console.log("handlerSizeChange", val);
      this.pageSize = val
      this.getDataList()
    },
    // 删除记录
    handlerDeleteRecord(item) {
      var id = item.id || ''
      // 弹窗二次确认
      this.$confirm(`是否删除这条推广记录？`, '提示', {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundMemberPromotionalPictureDeletePost({
                ids: [id]
              })
            )
            instance.confirmButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.searchHandler()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => { })
    },
    /**
   * 获取权限列表
   */
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundMemberPromotionalPictureListPost(params))
      console.log("apiBackgroundMemberPromotionalPictureListPost", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        var resultList = res.data.results || []
        if (Array.isArray(resultList) && resultList.length > 0) {
          // 给列表增加序号
          resultList.map((item, index) => {
            item.index = index + 1
            return item
          })
        }
        this.tableData = deepClone(resultList)
        this.totalCount = res.data.count || -1
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
   * 格式化查询参数
   * @param {} data
   */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'is_passed') {
            params[key] = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    // 新增或者编辑弹窗
    handlerAddorEditRecord(type, itemData) {
      this.type = type
      if (type === 'edit' && itemData.is_release) {
        return this.$message.error("亲，已启动的记录不可以编辑喔！")
      }
      if (itemData) {
        this.selectInfo = deepClone(itemData)
        setSessionStorage('PictureSettingInfo', JSON.stringify(itemData))
      }
      this.$router.push({
        name: 'PromotionalPictureSetting',
        query: {
          type: this.type
        }
      })
    },
    // 图片预览
    getPreViewList(row) {
      var url = row.url || ''
      if (url) {
        return [url]
      }
      return []
    },
    // 获取UITYPe
    getUIType(row) {
      var list = row.ui_type_alias
      if (list && Array.isArray(list)) {
        var nameList = list.join("，")
        return nameList
      }
      return ''
    },
    // 是否会员
    async memberStatusChange(val, item, index) {
      console.log("memberStatusChange", val, item, index);
      var currentIndex = index - 1 >= 0 ? index - 1 : 0
      var params = deepClone(item)
      this.$set(this.tableData[currentIndex], 'isSwitchLoading', true)
      var [err, res] = await to(this.$apis.apiBackgroundMemberPromotionalPictureModifyPost(params))
      this.$set(this.tableData[currentIndex], 'isSwitchLoading', false)
      if (err) {
        this.$set(this.tableData[currentIndex], 'is_release', !item.is_release)
        return this.$message.error(err.message || '修改失败')
      }
      if (res && res.code === 0) {
        this.$message.success('修改成功')
      } else {
        this.$set(this.tableData[currentIndex], 'is_release', !item.is_release)
        this.$message.error(res.msg || '修改失败')
      }
    }
  }
}
</script>
<style scoped lang="scss">
.permisson-manager {
  .tag-item {
    cursor: pointer;
  }

  .color-green {
    color: #14ce84;
  }

  .img-item {
    width: 60px;
    height: 60px;
  }
}
</style>
