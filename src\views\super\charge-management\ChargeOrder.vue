
<template>
  <div id="chargeOrder">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" @search="searchHandle" :autocompleteDataList="autocompleteDataList" @autocompleteValue="getAutocompleteValue"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="showAddDialog = true">新增订单</button-icon>
          <button-icon color="plain" plain @click="goExport">批量导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
         <!-- :selectable="selectableHandle" -->
        <el-table ref="tableView" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #realFee="{ row }">
              <span>{{ row.order_status === 'ORDER_SUCCESS' ? realFee(row.real_fee) : realFee(row.origin_fee) }}</span>
            </template>
            <template #voucherUrl="{ row }">
              <el-image
                v-if="row.voucher_url"
                style="width: 36px; height: 24px; margin-top: 9px;"
                :src="checkUrl"
                :preview-src-list="[row.voucher_url]">
              </el-image>
              <!-- <el-button v-if="row.voucher_url" type="text" size="small" class="ps-text" @click="checkDialogShow('voucherUrl', row)">查看</el-button> -->
              <span v-else>--</span>
            </template>
            <template #invoiceStatus="{ row }">
              <span v-if="row.invoice_status === 2" >--</span>
              <el-button v-else type="text" size="small" class="ps-text" @click="checkDialogShow('invoiceStatus', row)">查看</el-button>
            </template>
            <template #operation="{ row }">
              <el-button v-if="!orderStatus.includes(row.order_status) && row.pay_method === 'transfer'" type="text" size="small" class="ps-text" @click="finishOrder(row)">完成订单</el-button>
            </template>
            <template #transactionContent="{ row }">
              <div>{{ row.transaction_type === 'expansion' ? row.user_scale : (row.renew_days || row.renew_year) }}{{ row.transaction_type === 'expansion' ? '人' : (row.renew_days ? '天' : '年') }}</div>
            </template>
          </table-column>
        </el-table>
      </div>
    </div>
    <ul class="total m-t-10">
      <li>
        支付总金额:
        <span>￥{{ realFee(total_fee) }}</span>
      </li>
    </ul>
    <!-- 分页 start -->
    <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[10, 20, 50, 100, 500]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount"
        background
        class="ps-text"
        popper-class="ps-popper-select"
      ></el-pagination>
    </div>
    <!-- 分页 end -->

    <!-- 新增弹窗 -->
    <AddDialog
      :isShow.sync="showAddDialog"
      title="新增订单"
      @confirm="searchHandle"
      width="400px"
      :formatClue="formatClue" />
    <!-- 查看弹窗 -->
    <CheckDialog
      ref="checkDialogRef"
      :isShow.sync="showCheckDialog"
      :title="checkDialogType"
      :data="checkData"
      width="500px"
    />
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import * as dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import AddDialog from './components/AddDialog.vue'
import CheckDialog from './components/CheckDialog.vue'
import { RECENTSEVEN, chargeOrderTableSetting, divide } from './constants'

export default {
  name: 'ChargeOrder',
  mixins: [exportExcel],
  components: {
    AddDialog,
    CheckDialog
  },
  data() {
    return {
      showAddDialog: false,
      showCheckDialog: false,
      showFinishOrderDialog: false,
      total_fee: 0,
      isLoading: false,
      tableData: [],
      searchForm: {
        query_time_type: {
          type: 'select',
          value: 'create',
          dataList: [
            {
              label: '创建时间',
              value: 'create'
            },
            {
              label: '支付时间',
              value: 'pay'
            },
            {
              label: '到账时间',
              value: 'finish'
            }
          ]
        },
        select_date: {
          type: 'daterange',
          label: '',
          clearable: false,
          value: RECENTSEVEN
        },
        company_id: {
          label: '商户',
          type: 'autocomplete',
          value: '',
          placeholder: '请输入',
          placement: 'bottom-start',
          clearable: true
        },
        trade_no: {
          label: '订单号',
          type: 'input',
          placeholder: '请输入',
          maxlength: 21,
          value: ''
        },
        pay_method: {
          label: '支付方式',
          type: 'select',
          value: '',
          placeholder: '请选择',
          clearable: true,
          dataList: [
            { label: '微信支付', value: 'wxpay' },
            { label: '支付宝支付', value: 'alipay' },
            { label: '对公转账', value: 'transfer' }
          ]
        },
        transaction_type: {
          label: '交易类型',
          type: 'select',
          value: '',
          placeholder: '请选择',
          clearable: true,
          dataList: [
            { label: '扩容', value: 'expansion' },
            { label: '续费', value: 'renew' }
          ]
        },
        order_status: {
          label: '订单状态',
          type: 'select',
          value: ['ORDER_SUCCESS', 'ORDER_PAYING'],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          clearable: true,
          dataList: [
            { label: '待支付', value: 'ORDER_PAYING' },
            { label: '等待支付', value: 'ORDER_WATTING_PAY' },
            { label: '失败', value: 'ORDER_FAILED' },
            { label: '交易冲正中', value: 'ORDER_REVERSALING' },
            { label: '交易冲正', value: 'ORDER_REVERSAL' },
            { label: '成功', value: 'ORDER_SUCCESS' },
            { label: '退款中', value: 'ORDER_REFUNDING' },
            { label: '关闭（用户未支付）', value: 'ORDER_CLOSE' },
            { label: '未知', value: 'ORDER_UNKNOWN' },
            { label: '已退款', value: 'ORDER_REFUND_SUCCESS' },
            { label: '过期', value: 'ORDER_TIME_OUT' }
          ]
        }
      },
      currentTableSetting: chargeOrderTableSetting,
      // 搜索量
      page: 1,
      pageSize: 10,
      totalCount: 0,
      formatClue: [],
      checkDialogType: '', // 传入的title
      checkData: '', // 传入的内容
      companyValue: '',
      autocompleteDataList: [],
      orderStatus: [
        'ORDER_FAILED',
        'ORDER_SUCCESS',
        'ORDER_CLOSE',
        'ORDER_REFUND_SUCCESS',
        'ORDER_TIME_OUT'
      ],
      checkUrl: require('@/assets/img/check.png')
    }
  },
  computed: {
    realFee() {
      return d => {
        return divide(d)
      }
    }
  },
  created() {
    // this.getTollClue()
    this.getTollClueForSearch()
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getChargeOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.isLoading = true
      this.showAddDialog = false
      this.showCheckDialog = false
      this.showFinishOrderDialog = false
      this.page = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.isLoading = true
      this.$refs.searchRef.resetForm()
      this.page = 1
      this.initLoad()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.isLoading = true
      this.pageSize = val
      this.page = 1
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.isLoading = true
      this.page = val
      this.pageSize = 10
      this.initLoad()
    },
    // 获取订单列表数据
    async getChargeOrderList() {
      this.isLoading = true
      if (!this.searchForm.company_id.value) {
        console.log(this.searchForm.company_id.value)
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminBackgroundTollOrderListPost(this.formatQueryParams(this.searchForm))
      )
      if (err) {
        return this.$message.error(err.msg)
      }
      if (res.code === 0) {
        res.data.results.forEach(item => {
          item.finish_time = item.finish_time ? dayjs(item.finish_time).format('YYYY-MM-DD HH:mm:ss') : '--'
          item.pay_time = item.pay_time ? dayjs(item.pay_time).format('YYYY-MM-DD HH:mm:ss') : '--'
        })
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.total_fee = res.data.total_fee
        this.isLoading = false
      } else {
        return this.$message.error(res.msg)
      }
      this.isLoading = false
    },
    // 格式化表单参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_date' && key !== 'company_id') {
            params[key] = data[key].value
          } else if (key === 'company_id') {
            let flag = false
            this.autocompleteDataList.forEach(item => {
              if (item.value === this.searchForm.company_id.value) {
                params.company_id = item.id
                flag = true
              }
            })
            if (!flag) {
              this.$message.error('尚无此商户，请确认后重试')
            }
          } else if (data[key].value.length > 0 && key !== 'company_id') {
            params.start_time = dayjs(data[key].value[0]).format('YYYY-MM-DD HH:mm:ss')
            params.end_time = dayjs(data[key].value[1]).endOf('date').format('YYYY-MM-DD HH:mm:ss')
          }
        }
      }
      params.page = this.page
      params.page_size = this.pageSize
      console.log('params', params)
      return params
    },
    // 获取收费线索
    async getTollClue() {
      const [err, res] = await to(this.$apis.apiBackgroundAdminBackgroundTollOrderGetCompanyListPost())
      if (err) {
        return this.$message.error(err.msg)
      }
      if (res.code === 0) {
        console.log('商户列表', res.data)
        res.data.forEach(item => {
          let obj = {}
          obj.label = item.name
          obj.value = item.id
          obj.toll_type = item.toll_type
          this.searchForm.company_id.dataList.push(obj)
        })
        this.formatClue = this.searchForm.company_id.dataList
      } else {
        return this.$message.error(res.msg)
      }
    },
    getTollClueForSearch() {
      this.$apis.apiBackgroundAdminBackgroundTollOrderGetCompanyListPost()
        .then(res => {
          if (res.code === 0) {
            res.data.forEach(item => {
              let obj = {}
              obj.id = item.id
              obj.value = item.name
              obj.toll_type = item.toll_type
              this.autocompleteDataList.push(obj)
            })
            this.formatClue = this.autocompleteDataList
          } else {
            return this.$message.error(res.msg)
          }
        })
    },
    getAutocompleteValue(e) {
      this.searchForm.company_id.value = e
    },
    // 完成订单
    finishOrder(e) {
      this.$confirm(`确认到账后将完成订单，完成后不可撤回，是否确认收款到账。`, '完成订单', {
        confirmButtonText: '确认到账',
        closeOnClickModal: false,
        showCancelButton: false,
        confirmButtonClass: 'ps-btn',
        // action代表点击哪个按钮，instance表示实例，done用于关闭弹窗
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminBackgroundTollOrderFinishOrderPost({
                id: e.id
              })
            )
            if (err) {
              this.$message.error(err.msg)
              this.visible = false
              instance.confirmButtonLoading = false
              done()
              return
            }
            if (res.code === 0) {
              this.getChargeOrderList()
            } else {
              this.$message.error(res.msg)
            }
            this.visible = false
            instance.confirmButtonLoading = false
            done()
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              this.visible = false
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 导出
    goExport() {
      const option = {
        type: 'SuperChargeOrder',
        // type: "ThroughRecordExport",
        url: 'apiBackgroundAdminBackgroundTollOrderListExportPost',
        params: this.formatQueryParams(this.searchForm)
      }
      this.exportHandle(option)
    },
    // 显示查看弹窗
    checkDialogShow(type, e) {
      this.checkDialogType = type
      this.checkData = e.id
      // switch (type) {
      //   case 'voucherUrl':
      //     this.checkData = e.voucher_url
      //     break
      //   case 'invoiceStatus':
      //     this.checkData = e.id
      //     break
      // }
      this.showCheckDialog = true
    }
  }
}
</script>

<style>

</style>
