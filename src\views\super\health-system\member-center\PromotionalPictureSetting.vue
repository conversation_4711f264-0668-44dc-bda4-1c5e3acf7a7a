<template>
  <div class="picture-setting container-wrapper">
    <!--刷新页面-->
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper p-l-20 p-t-20">
      <el-form :model="memberForm" :rules="memberFormRules" ref="memberFormRef" label-width="160px">
        <el-form-item label="名称：" prop="name">
          <el-input v-model="memberForm.name" maxlength="15" class="ps-input w-333" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="图片：" prop="img">
          <div class="ps-flex">
            <el-upload v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload"
              :action="serverUrl" :data="uploadParams" :file-list="fileLists" :on-success="uploadSuccess"
              :before-upload="beforeFoodImgUpload" :limit="1" :multiple="false" :show-file-list="false"
              :headers="headersOpts" accept=".jpeg,.jpg,.png,.bmp">
              <img v-if="memberForm.img" :src="memberForm.img" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <div class="inline-block upload-tips m-l-10">
              仅支持jpg,png格式，比例：333px*183px,图片最大不超过10MB
            </div>
          </div>
        </el-form-item>
        <el-form-item label="显示界面：" prop="type">
          <el-select v-model="memberForm.type" placeholder="请选择显示界面" class="w-333" multiple>
            <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="以下项目点不显示：" prop="orgIds">
          <el-select v-model="memberForm.orgIds" placeholder="请选择项目点" class="ps-select w-333" multiple filterable clearable collapse-tags>
            <el-option v-for="item in orgsList" :key="item.id" :label="item.name" :value="item.company"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="键值：" prop="label" v-if="memberForm.type == 'model'">
          <el-select v-model="memberForm.label" placeholder="请选择标签" class="ps-input w-333">
            <el-option v-for="item in keyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级：" prop="priority">
          <el-input class="ps-input w-333" placeholder="请输入优先级" v-model="memberForm.priority" maxlength="10" :min="0" :max="100"
            type="number"></el-input>
          <div class="ps-text">优先级数值小的优先显示</div>
        </el-form-item>
        <el-form-item>
          <el-button size="small" class="ps-origin-plain-btn w-100" @click="handlerCancel">取消</el-button>
          <el-button size="small" type="primary" class="ps-origin-btn w-100 m-l-40" @click="saveSetting('release')"
            v-loading="isLoading">确认发布</el-button>
          <el-button size="small" type="primary" class="ps-origin-btn w-100 m-l-40" @click="saveSetting('noRelease')"
            v-loading="isLoading">保存不发布</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { deepClone, to, getToken, getSuffix, getSessionStorage } from '@/utils/index'
export default {
  name: 'PromotionalPictureSetting',
  props: {},
  data() {
    let validateNumber = (rule, value, callback) => {
      let reg = /^\d+$/
      if (!reg.test(value)) {
        callback(new Error('请输入小于100的数字'))
      } else {
        if (value > 100) {
          return callback(new Error('请输入小于100的数字'))
        }
        callback()
      }
    }
    let validataPrice = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        let number = /^[1-9][0-9]*$/;
        if (!number.test(value)) {
          callback(new Error('请输入正整数'))
        } else {
          callback()
        }
      }
    };
    return {
      isLoading: false, // 刷新数据
      typeList: [
        {
          name: '首页',
          value: 'front_page'
        },
        {
          name: '预约点餐支付成功界面',
          value: 'reservation'
        },
        {
          name: '报餐支付成功界面',
          value: 'report_meal'
        },
        {
          name: '充值成功界面',
          value: 'recharge'
        },
        {
          name: '客服与帮助界面',
          value: 'service_agent'
        }
      ],
      memberForm: {
        id: '',
        name: '',
        img: '',
        imageList: [],
        type: '', // 显示界面
        orgIds: [], // 组织
        priority: '' // 优先级
      },
      memberFormRules: {
        balanceNum: [{ validator: validataPrice, trigger: 'blur' }],
        priority: [
          { required: false, validator: validateNumber, trigger: "blur" }
        ]
      },
      serverUrl: '/api/background/file/upload',
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      fileLists: [],
      uploadParams: { // 上传头
        prefix: 'super_food_img'
      },
      uploading: false, // 上传加载中
      type: '', // 类型
      orgsList: [] // 组织列表
    }
  },
  created() {
    this.initData()
  },
  mounted() { },
  methods: {
    // 初始化数据
    initData() {
      console.log(" this.$route", this.$route.query, getSessionStorage('PictureSettingInfo'));
      if (this.$route.query) {
        this.type = this.$route.query.type || ''
        if (this.type === 'edit') {
          // 编辑 从缓存拿数据赋值
          var info = JSON.parse(getSessionStorage('PictureSettingInfo') || "{}")
          if (info) {
            // 赋值
            var memberForm = deepClone(this.memberForm)
            memberForm.name = info.name || ''
            memberForm.img = info.url || ''
            memberForm.imageList = info.url ? [info.url] : []
            memberForm.type = info.ui_type || ''
            memberForm.orgIds = info.company || ''
            memberForm.priority = info.priority || ''
            if (this.type === 'edit') {
              memberForm.id = info.id
            }
            this.$set(this, 'memberForm', memberForm)
          }
        }
      }
      this.getOrgList()
    },
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initLoad()
    },
    saveSetting(flag) {
      this.$refs.memberFormRef.validate(valid => {
        if (valid) {
          console.log("valid");
          var params = {
            name: this.memberForm.name,
            url: this.memberForm.img ? this.memberForm.img : '',
            ui_type: this.memberForm.type,
            company: this.memberForm.orgIds,
            priority: parseInt(this.memberForm.priority),
            is_release: flag === 'release'
          }
          if (this.type === 'edit') {
            params.id = this.memberForm.id
          }
          var api = this.type === 'edit' ? this.$apis.apiBackgroundMemberPromotionalPictureModifyPost(params) : this.$apis.apiBackgroundMemberPromotionalPictureAddPost(params)
          this.confirmOperation(api)
        }
      })
    },
    // 保存配置
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(api)
      this.isLoading = false
      if (err) {
        return this.$message.error(err.message || '保存失败')
      }
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 会员等级
    async getMemberLevel() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundMemberMemberGradeListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.levelList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    tabClick() {
      console.log("tabClick");
    },
    // 切换开关
    hanlderSwitchChange(val, type) {
      console.log("hanlderSwitchChange", val, type);
      if (type === 'isReservation') {
        this.$set(this.memberForm, "deadlineTimes", val ? "1" : '')
      }
      if (type === 'isBalance') {
        this.$set(this.memberForm, "balanceNum", val ? "20" : '')
      }
      if (type === 'isMealReporting') {
        this.$set(this.memberForm, "mealReportingHour", val ? "1" : '')
      }
    },
    // 取消返回
    handlerCancel() {
      this.$router.back()
    },
    // 图片上传前检测
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 10
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        return false
      }
      this.uploading = true
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        this.fileLists = fileList
        this.memberForm.img = res.data.public_url
        this.memberForm.imageList = [res.data.public_url]
        console.log("this.dialogForm.img", this.dialogForm.img)
      } else {
        this.memberForm.img = ''
        this.$message.error(res.msg)
      }
    },
    // 移除图片
    removeFoodImg(index) {
      this.memberForm.imageList.splice(index, 1)
      this.fileLists.splice(index, 1)
    },
    // 获取项目点
    async getOrgList() {
      // const [err, res] = await this.$to(this.$apis.apiBackgroundAdminMarketingPopupGetOrgsPost())
      // if (err) {
      //   this.$message.error(err.message)
      //   return
      // }
      // console.log(res)
      // if (res && res.code === 0) {
      //   this.orgsList = res.data || []
      // } else {
      //   this.$message.error(res.msg)
      // }
      this.$apis.apiBackgroundAdminOrganizationListPost({
        parent__is_null: "1",
        status: "enable",
        page: 1,
        page_size: 999999
      }).then(res => {
        if (res.code === 0) {
          this.orgsList = res.data.results || []
          console.log('orgsList', this.orgsList)
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.picture-setting {

  .m-l-100 {
    margin-left: 100px;
  }

  .label-list {
    display: flex;
    flex-wrap: wrap;
    color: #fff;

    .label-list-item {
      margin-bottom: 10px;
      line-height: 30px;
      background-color: #ff9b45;
      padding: 0 10px;
      margin-right: 15px;
      border-radius: 5px;

      .del-icon {
        cursor: pointer;
      }
    }
  }

  .ps-flex {
    display: inline-flex;
    align-items: center;

    .switch-tag {
      align-self: center;
    }
  }

  .upload-w ::v-deep.el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .upload-w ::v-deep.el-upload:hover {
    border-color: #ff9b45;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 333px;
    height: 183px;
    line-height: 183px;
    text-align: center;
  }

  .avatar {
    width: 333px;
    height: 183px;
    display: block;
  }

  .upload-tips {
    width: 150px;
    line-height: 20px;
    font-size: 12px;
    margin-top: 30px;
  }

  .w-333 {
    width: 333px;
  }
}
</style>
