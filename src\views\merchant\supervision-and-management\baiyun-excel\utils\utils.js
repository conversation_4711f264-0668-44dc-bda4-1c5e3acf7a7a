export const getOperatorUsername = (row) => {
  const operatorUsername = row.operator_username || '-'
  const operatorMemberName = row.operator_member_name || ''
  if (operatorMemberName) {
    return operatorMemberName + (operatorUsername ? `(${operatorUsername})` : '')
  } else {
    return operatorUsername
  }
}
// 获取复核人
export const getConfirmerUsername = (row) => {
  const confirmerUsername = row.confirmer_username || ''
  const confirmerMemberName = row.confirmer_member_name || ''
  if (confirmerMemberName) {
    return confirmerMemberName + (confirmerUsername ? `(${confirmerUsername})` : '')
  } else {
    return confirmerUsername
  }
}
// 表25根据JSON数据转换table方法
export const convertMockFormDataToTableList = (mockFormData) => {
  const tableList = []

  // 遍历所有表单数据项
  Object.keys(mockFormData).forEach(key => {
    const formData = mockFormData[key]
    const inspectionItems = formData.inspectionItems

    // 遍历每个检查内容分类
    formData.inspectionContents.forEach(content => {
      const secondaryIndicators = content.secondaryIndicators

      // 如果是"其他"类型，特殊处理
      if (content.isOther) {
        content.formItemList.forEach(item => {
          // 提取编号，从tip中获取
          const serialNumberMatch = item.tip.match(/^(\d+\.\d+)/)
          const serialNumber = serialNumberMatch ? serialNumberMatch[1] : ''

          tableList.push({
            inspection_items: inspectionItems,
            inspection_contents_secondary_indicators: secondaryIndicators,
            serial_number: serialNumber,
            key_points_inspection_third: item.tip,
            inspection_results: item.isClean ? '是' : '否',
            inspection_remarks: content.remarks || item.remarks || ''
          })
        })
      } else {
        // 常规类型处理
        content.formItemList.forEach((item, index) => {
          // 提取编号，从tip中获取，如果没有则根据索引生成
          let serialNumber = ''
          const serialNumberMatch = item.tip.match(/^(\d+\.\d+)/)

          if (serialNumberMatch) {
            serialNumber = serialNumberMatch[1]
          } else {
            // 如果tip中没有编号，尝试从secondaryIndicators中提取主编号并生成子编号
            const mainNumberMatch = secondaryIndicators.match(/^(\d+)/)
            if (mainNumberMatch) {
              const mainNumber = mainNumberMatch[1]
              serialNumber = `${mainNumber}.${index + 1}`
            }
          }

          tableList.push({
            inspection_items: inspectionItems,
            inspection_contents_secondary_indicators: secondaryIndicators,
            serial_number: serialNumber,
            key_points_inspection_third: item.tip,
            inspection_results: item.isClean ? '是' : '否',
            inspection_remarks: item.remarks || ''
          })
        })
      }
    })
  })

  return tableList
}
// 获取复核人根据列表
export const getConfirmerUsernameByList = (row) => {
  const reviewersList = row.reviewers_list || []
  if (reviewersList && reviewersList.length > 0) {
    return reviewersList.map((item) => item.member_name + "(" + item.username + ")").join(",")
  } else {
    return "-"
  }
}
