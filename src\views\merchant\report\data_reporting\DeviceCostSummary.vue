<template>
  <div>
    <div class="DeviceCostSummary booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-button
              size="mini"
              @click="gotoExport"
              v-permission="['background_order.finance_report.device_consume_summary_list_export']"
            >
              导出Excel
            </el-button>
            <el-button size="mini" @click="gotoPrint">打印</el-button>
            <el-button size="mini" @click="openPrintSetting">报表设置</el-button>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            :index="indexMethod"
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row"
            :row-class-name="tableRowClassNameDeviceCostSummary"
          />
          <!-- table content end -->
        </div>
        <!-- 统计 start -->
        <!-- <table-statistics :statistics="collect" /> -->
        <!-- table content end -->
        <!-- 分页 start -->
        <!-- table 有小计 不做分页 -->
        <!-- <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="total"
        ></pagination> -->
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { DeviceCostSummary } from './constantsConfig'
import { debounce, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
export default {
  name: 'DeviceCostSummary',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        // { label: '设备类型', key: 'device_type_alias' },
        { label: '设备名称', key: 'device_name' },
        { label: '营业笔数', key: 'consume_count' },
        { label: '营业金额', key: 'consume', type: 'money' },
        { label: '退款笔数', key: 'refund_count' },
        { label: '退款金额', key: 'refund_fee', type: 'money' },
        { label: '实收金额', key: 'real_fee', type: 'money' },
        { label: '结算金额', key: 'settle', type: 'money' }
      ],
      tableData: [],
      currentPage: 1,
      page: 1,
      pageSize: 999999,
      total: 0,
      searchFormSetting: deepClone(DeviceCostSummary),
      // collect: [
      //   // 统计
      //   {
      //     key: 'text',
      //     value: '消费总金额-退款总金额=实收总金额，消费笔数没有减去退款的订单笔数。',
      //     label: ''
      //   }
      // ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'DeviceCostSummary',
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
  },
  mounted() {
    this.getLevelNameList()
    this.requestDeviceType()
    this.getDeviceSelectList()
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        this.getDeviceConsumeSummaryList()
      }
    },
    tableRowClassNameDeviceCostSummary({ row, rowIndex }) {
      if (row.primary === '小计') {
        return 'hang-row-class'
      }
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDeviceConsumeSummaryList()
        this.isFirstSearch = false
      }
    }, 300),
    // 获取设备名称
    async getDeviceSelectList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportDeviceSelectListPost({
        org_ids: this.searchFormSetting.org_ids.value
      })
      if (res.code === 0) {
        this.searchFormSetting.device_ids.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设备列表
    async requestDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost({})
      if (res.code === 0) {
        this.searchFormSetting.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表数据
    async getDeviceConsumeSummaryList() {
      this.isLoading = true
      // params
      const res = await this.$apis.apiBackgroundReportCenterDataReportDeviceConsumeSummaryListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        // 处理小计 需要处理数据 canteen这个字段改了 打印那边也要改
        // 创建一个映射，以便快速查找 total 中的数据
        const totalMap = new Map(res.data.org_collect.map(item => [item.org_id, item]))
        // 新数组用于存储处理后的数据
        let processedArea = []
        // 遍历 area 数据，找到每个 id 组的最后一项并插入总计
        for (let i = 0; i < res.data.result.length; i++) {
          processedArea.push(res.data.result[i])
          const current = res.data.result[i]
          const next = res.data.result[i + 1]
          // 如果当前项是某个 id 组的最后一项
          if (!next || next.org_id !== current.org_id) {
            // 从 totalMap 中获取对应的总计值
            const totalItem = totalMap.get(current.org_id)
            if (totalItem) {
              let newObj = deepClone(totalItem)
              let obj = {}
              obj = {
                primary: '小计',
                consume_count: newObj.consume_count,
                consume: newObj.consume,
                refund_count: newObj.refund_count,
                refund_fee: newObj.refund_fee,
                real_fee: newObj.real_fee,
                settle: newObj.settle
              }
              // 插入总计行
              processedArea.push(obj)
            }
          }
        }

        this.tableData = processedArea
        // 统计
        // this.setCollectData(res)
        this.setSummaryData(res)
      } else {
        this.$message.error(res.msg)
      }
    },

    // 翻页
    // onPaginationChange(val) {
    //   this.currentPage = val.current
    //   this.pageSize = val.pageSize
    //   this.getDeviceConsumeSummaryList()
    // },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetLevelNamePost()
      // this.currentTableSetting = this.tableSetting
      let result = res.data.map(v => {
        return {
          label: v.name,
          key: v.level
        }
      })
      this.tableSetting.unshift(...result)
      this.initPrintSetting()
    },
    // 导出
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const option = {
        type: 'DeviceCostSummary',
        url: 'apiBackgroundReportCenterDataReportDeviceConsumeSummaryListExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    gotoPrint() {
      // const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '设备汇总表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportDeviceConsumeSummaryListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          push_summary: true, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...this.formatQueryParams(this.searchFormSetting),
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.DeviceCostSummary {
  ::v-deep .hang-row-class {
    background: #eef1f6 !important;
  }
  .search-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 10px;
  }
}
</style>
