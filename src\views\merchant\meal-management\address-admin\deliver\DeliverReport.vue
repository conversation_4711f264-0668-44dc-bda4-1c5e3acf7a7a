<template>
  <div class="DeliverReport container-wrapper">
      <div class="top-btn">
        <el-radio-group v-model="tabType" class="ps-radio-btn" @change="changeTab">
          <el-radio-button v-for="item in tabTypeList" :key="item.value" :label="item.value" v-permission="[`${item.permission}`]">{{ item.label }}</el-radio-button>
        </el-radio-group>
        <img @click="refreshHandle" class="refresh" src="@/assets/img/refresh.png" alt="">
      </div>
      <heng-report ref="deliverChild" v-if="tabType==='heng'"></heng-report>
      <shu-report ref="deliverChild" v-if="tabType==='shu'"></shu-report>
      <area-food ref="deliverChild" v-if="tabType==='areaFood'"></area-food>
  </div>
</template>

<script>
// import { debounce } from '@/utils'
import HengReport from './HengReport.vue'
import ShuReport from './ShuReport.vue'
import AreaFood from './AreaFood.vue'
export default {
  name: 'DeliverReport',
  components: { HengReport, ShuReport, AreaFood },
  data() {
    return {
      tabType: 'heng',
      tabTypeList: [{
        value: 'heng',
        label: '配送汇总表-横表',
        permission: 'background_order.order_reservation.get_delivery_collect_by_y'
      }, {
        value: 'shu',
        label: '配送汇总表-竖表',
        permission: 'background_order.order_reservation.get_delivery_collect_by_x'
      }, {
        value: 'areaFood',
        label: '区域配餐表',
        permission: 'background_order.reservation_order.get_delivery_area_collect'
      }]
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    changeTab() {

    },
    refreshHandle() {
      this.$refs.deliverChild.resetSearchHandle()
    },
    /**
     * 获取配餐表ids
     */
    getDeliveryAreaCollectIds(params) {
      return new Promise((resolve) => {
        this.$apis.apiBackgroundOrderOrderReservationGetDeliveryAreaCollectIdsPost(params).then(res => {
          console.log("apiBackgroundOrderOrderReservationGetDeliveryAreaCollectIdsPost", res);
          if (res.code === 0) {
            var list = res.data.results || []
            if (list) {
              // 去除数组中的null undefined 等脏数据
              list = list.filter(item => {
                return item && item.toString().trim()
              })
            }
            resolve(list)
          } else {
            // this.$message.error("全选失败，" + res.msg)
            resolve([])
          }
        }).then(error => {
          console.log("error", error);
          // this.$message.error("全选失败，" + error.message)
          resolve([])
        }
        )
      })
    },
    /**
     * 配送汇总横竖表ids
     */
    getDeliveryCollectByXyIds(params) {
      return new Promise((resolve) => {
        this.$apis.apiBackgroundOrderOrderReservationGetDeliveryCollectByXyIdsPost(params).then(res => {
          console.log("apiBackgroundOrderOrderReservationGetDeliveryAreaCollectIdsPost", res);
          if (res.code === 0) {
            var list = res.data.results || []
            if (list) {
              // 去除数组中的null undefined 等脏数据
              list = list.filter(item => {
                return item && item.toString().trim()
              })
            }
            resolve(list)
          } else {
            // this.$message.error("全选失败，" + res.msg)
            resolve([])
          }
        }).then(error => {
          console.log("error", error);
          // this.$message.error("全选失败，" + error.message)
          resolve([])
        }
        )
      })
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.DeliverReport {
  .top-btn{
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
  }
  .refresh{
    cursor: pointer;
    padding: 14px 0;
  }
}
</style>
