<template>
  <div class="approve-cancel-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle">
      <template slot="title">
        <div class="">
          <el-button :class="[orderType === item.value ? 'ps-origin-btn' : '']" v-for="(item, index) in orderTypeList" :key="index" @click="changeOrderStatus(item.value)">{{ item.label }}</el-button>
        </div>
      </template>
    </refresh-tool>
    <search-form
      ref="searchRef"
      label-width="105px"
      :loading="isLoading"
      :form-setting="searchSetting"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" v-if="orderType === 'wait'" @click="mulOperation">批量操作</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport" v-permission="['background_order.order_review.list_export']">导出报表</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" row-key="id" stripe
          header-row-class-name="ps-table-header-row" @selection-change="handleOrderSelectionChange">
          <el-table-column type="selection" align="center" class-name="ps-checkbox"></el-table-column>
          <table-column v-for="(item, i) in currentTableSetting" :key="i" :col="item">
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDetailDrawer(row, 1)"
              >
                处理
              </el-button>
              <el-button type="text" size="small" class="ps-text" @click="openDetailDrawer(row, 2)">
                查看详情
              </el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
    <!-- 详情弹窗 -->
     <approve-cancel-detail-drawer
      :show.sync="isShowDetailDrawer"
      :type="detailDrawerType"
      :title="detailDrawerType === 1 ? '取消订单详情' : '详情'"
      :drawerInfo="detailDrawerInfo"
      @confirm="confirmDetailDrawer"
     />
     <!-- 二次确认 -->
    <el-dialog
      title="批量操作"
      :visible.sync="dialogVisible"
      width="400px"
      top="30vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <div>确认对共{{selectList.length}}笔订单进行批量操作？</div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" class="ps-cancel-btn" :disabled="isLoading" @click="dialogVisible = false">取消</el-button>
        <el-button size="small" type="primary" class="ps-origin-btn" :disabled="isLoading"  @click="confirmHandle('reject')">批量拒绝</el-button>
        <el-button size="small" type="primary" class="ps-origin-btn" :disabled="isLoading"  @click="confirmHandle('agree')">批量同意</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { debounce, to, camelToUnderline, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { APPROVE_CANCEL_ORDER } from './constants'
import report from '@/mixins/report' // 混入
import ApproveCancelDetailDrawer from './component/ApproveCancelDetailDrawer.vue'

export default {
  name: 'ApproveCancelOrder',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  components: {
    ApproveCancelDetailDrawer
  },
  data() {
    return {
      isLoading: false,
      searchSetting: deepClone(APPROVE_CANCEL_ORDER),
      orderType: 'wait',
      orderTypeList: [
        { label: '待审核', value: 'wait' },
        { label: '已同意', value: 'success' },
        { label: '已拒绝', value: 'reject' },
        { label: '已撤回', value: 'cancel' }
      ],
      // 报表设置相关
      tableSetting: [
        { label: '序号', key: 'index', type: 'index', width: '80px' },
        { label: '审核编号', key: 'review_no' },
        { label: '原订单号', key: 'trade_no' },
        { label: '申请时间', key: 'apply_time' },
        { label: '就餐时间', key: 'dining_time' },
        { label: '餐段', key: 'meal_type_alias' },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        { label: '实收金额', key: 'pay_fee', type: 'money' },
        { label: '手续费', key: 'rate_fee', type: 'money' },
        { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
        { label: '储值动账', key: 'wallet_fee', type: 'money' },
        { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
        { label: '动账组织', key: 'wallet_organization_name' },
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '手机号', key: 'phone' },
        { label: '分组', key: 'payer_group_name' },
        { label: '部门', key: 'payer_department_group_name' },
        { label: '审核状态', key: 'review_status_alias' },
        { label: '退款状态', key: 'refund_type_alias' },
        { label: '历史状态', key: 'history_review_status_alias' },
        { label: '申请类型', key: 'order_type_alias' },
        { label: '申请原因', key: 'review_type_alias' },
        { label: '备注', key: 'review_reason' },
        { label: '操作员', key: 'account_alias' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'ApproveCancelOrder',
      // 数据列表
      tableData: [],
      collect: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      isFirstSearch: true,
      // 详情弹窗
      isShowDetailDrawer: false,
      detailDrawerType: 1,
      detailDrawerInfo: {},
      // 二次确认
      selectList: [],
      dialogVisible: false
    }
  },
  created() {
    this.initPrintSetting()
    this.initLoad(true)
  },
  mounted() {},
  computed: {},
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        this.getReviewOrderList()
      }
      // this.getReviewOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.initLoad()
        this.isFirstSearch = false
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        let k = camelToUnderline(key)
        if (data[key].value !== '' && data[key].value !== null) {
          if (k !== 'select_time') {
            params[k] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 切换审核状态
    changeOrderStatus(e) {
      this.orderType = e
      this.currentPage = 1
      this.initPrintSetting()
      this.isFirstSearch = true
      this.tableData = []
      // this.getReviewOrderList()
    },
    // 获取数据列表
    async getReviewOrderList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (this.orderType) {
        params.account_review_status = this.orderType
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderReviewOrderPaymentReviewListPost(params))
      this.isLoading = false
      if (err) {
        this.tableData = []
        this.collect = []
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.collect = []
        this.collect = [
          { key: 'wait_count', value: res.data.collect.wait_count, label: '待审核合计：', unit: '条' }
        ]
        if (this.orderType === 'success') {
          this.collect.push(
            { key: 'total_origin_fee', value: res.data.collect.total_origin_fee, label: '订单金额合计：', type: 'money' },
            { key: 'total_rate_fee', value: res.data.collect.total_rate_fee, label: '手续费合计：', type: 'money' }
          )
        }
      } else {
        this.tableData = []
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getReviewOrderList()
    },
    handleOrderSelectionChange(val) {
      this.selectList = val.map(item => {
        return item.id
      })
    },
    openDetailDrawer(data, type) {
      this.isShowDetailDrawer = true
      this.detailDrawerType = type
      this.detailDrawerInfo = data
    },
    confirmDetailDrawer() {
      this.isShowDetailDrawer = false
      this.getReviewOrderList()
    },
    mulOperation() {
      if (this.selectList.length === 0) {
        return this.$message.error('请先选择数据！')
      }
      this.dialogVisible = true
    },
    async confirmHandle(type) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      let api
      let params = {
        ids: this.selectList
      }
      if (type === 'agree') {
        api = this.$apis.apiBackgroundOrderOrderReviewBulkOrderPaymentReviewSuccessPost
      } else if (type === 'reject') {
        api = this.$apis.apiBackgroundOrderOrderReviewBulkOrderPaymentReviewRejectPost
      }
      const [err, res] = await to(api(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogVisible = false
        this.$message.success(res.msg)
        this.getReviewOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出报表
    handleExport() {
      let params = {
        ...this.formatQueryParams(this.searchSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      if (this.orderType) {
        params.review_status = this.orderType
      }
      const option = {
        type: 'ApproveCancelOrder',
        url: 'apiBackgroundOrderOrderReviewListExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.approve-cancel-order {
}
</style>
