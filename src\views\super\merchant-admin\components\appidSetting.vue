<template>
  <div class="container-wrapper bindappid" v-loading="isLoading">
    <!-- tab end -->
    <div class="l-title clearfix">
      <span class="float-l min-title-h">绑定公众号</span>
      <el-button v-if="formOperate === 'detail'" @click="changeOperate" size="mini" class="float-r">编辑</el-button>
    </div>
    <div class="appid-box">
      <!-- <span style="margin-right: 15px;">绑定公众号</span> -->
      <!-- <el-select ref="appidRef" :disabled="!checkIsFormStatus" size="small" v-model="appid" placeholder="">
        <el-option v-for="option in appidList" :key="option.appid" :label="option.name" :value="option.appid"></el-option>
      </el-select> -->
      <el-form
        ref="appidRef"
        :rules="formDataRuls"
        :model="formData"
        size="small"
        label-width="100px"
      >
        <el-form-item label="appid" prop="appid">
          <el-input :disabled="!checkIsFormStatus" style="max-width: 300px;" v-model="formData.appid" placeholder="请输入appid"></el-input>
        </el-form-item>
        <el-form-item label="secret_key" prop="secret_key">
          <el-input :disabled="!checkIsFormStatus" style="max-width: 300px;" v-model="formData.secret_key" placeholder="请输入appid"></el-input>
        </el-form-item>
        <el-form-item label="地址" prop="" v-if="!checkIsFormStatus && qrcodeValue">
          <el-input v-model="qrcodeValue" readonly style="width: 300px;"><el-button slot="append"  v-clipboard:copy='qrcodeValue' v-clipboard:success='clipboardSuccess'>复制</el-button></el-input>
        </el-form-item>
        <el-form-item label="二维码" prop="" v-if="!checkIsFormStatus && qrcodeValue">
          <qrcode :value="qrcodeValue" :options="qrcodeOptions" tag="img" :margin="10" alt class="face-img" />
        </el-form-item>
        <el-form-item label="功能菜单配置" prop="menuList">
          <!-- <el-select :disabled="!checkIsFormStatus" v-model="formData.menuList" :multiple="true" class="ps-select w-300">
            <el-option
              v-for="item in allMenuList"
              :key="item.key"
              :label="item.verbose_name"
              :value="item.key"
            ></el-option>
          </el-select> -->
          <el-button size="small" type="text" class="w-80" :disabled="!checkIsFormStatus" @click="showDrawer('app')">去配置</el-button>
          <span class="font-size-12 origin">（{{ formData.menuList.length }} 个）</span>
        </el-form-item>
        <el-form-item label-width="0" prop="templateId">
          <span class="m-r-10">人脸更新提醒模板ID：</span>
          <el-input :disabled="!checkIsFormStatus" style="max-width: 300px;" v-model="formData.templateId" placeholder="请输入模板id"></el-input>
        </el-form-item>
      </el-form>
      <div class="add-wrapper" v-if="checkIsFormStatus">
        <el-button type="primary" size="small" class="ps-origin-btn" @click="saveAppidHandle">保存</el-button>
      </div>
    </div>
    <!-- 抽屉 -->
    <ConfigurationList :isShow.sync="drawerShow" :type="drawerType" @refreshPermission="refreshPermission" />
  </div>
</template>

<script>
import { to, debounce, deepClone } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import qrcode from '@chenfengyuan/vue-qrcode'
import ConfigurationList from './ConfigurationList.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'SuperBindAppid',
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: { // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    organizationData: Object,
    restoreHandle: Function
  },
  components: {
    qrcode,
    ConfigurationList
  },
  data() {
    return {
      formOperate: 'detail',
      isLoading: false,
      formData: {
        appid: '',
        secret_key: '',
        menuList: [],
        templateId: ''
      },
      appid: '',
      secret_key: '',
      auth_time: '',
      appidList: [],
      formDataRuls: {
        appid: [{ required: false, message: '请先输入appid', trigger: "blur" }],
        secret_key: [{ required: false, message: '请先输入secret_key', trigger: "blur" }]
      },
      qrcodeOptions: {
        width: 256,
        height: 256
        // errorCorrectionLevel: 'H' // L M Q H
      },
      qrcodeValue: 'pushi',
      allMenuList: [],
      drawerShow: false,
      drawerType: 'app'
    }
  },
  computed: {
    ...mapGetters([
      'permissionData',
      'versionPermissionData'
    ]),
    checkIsFormStatus: function() {
      let show = false
      switch (this.formOperate) {
        case 'detail':
          show = false
          break;
        case 'modify':
          show = true
          break;
      }
      return show
    }
  },
  watch: {
    infoData: function(val) {
      this.appid = val.appid
    }
  },
  created() {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.formData.appid = this.infoData.appid
      this.formData.secret_key = this.infoData.secret_key
      this.formData.templateId = this.infoData.template_id
      // this.getAppidList()
      this.getOrgIsBindAppid()
      this.formData.menuList = [
        ...this.permissionData.app_permission // vuex里保存的当前组织已勾选的
      ]
      this.getAppPermission()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
    }, 300),
    changeOperate() {
      if (this.formOperate !== 'modify') {
        this.formOperate = 'modify'
      } else {
        this.formOperate = 'detail'
      }
    },
    async getAppidList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationGetAppidListPost())
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.appidList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取当前组织是否已绑定公众号
    async getOrgIsBindAppid() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationGetOrgAppidPost({
        id: this.organizationData.id,
        company: this.organizationData.company
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // if (res.data.appid) {
        //   this.formOperate = ''
        // }
        this.formData.appid = res.data.appid
        this.formData.secret_key = res.data.secret_key
        this.qrcodeValue = res.data.booking_url
        // this.formData.menuList = res.data.app_permission
      } else {
        this.$message.error(res.msg)
      }
    },
    // 发送请求
    async saveAppidHandle() {
      if (this.isLoading) {
        return
      }
      this.$refs.appidRef.validate(valid => {
        if (valid) {
          this.modifyOrganization()
        }
      })
      // this.isLoading = true
      // const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationModifyPost({
      //   id: this.organizationData.id,
      //   status: 'enable',
      //   appid: this.appid
      // }))
      // this.isLoading = false
      // if (err) {
      //   this.$message.error(err.message)
      //   return
      // }
      // if (res.code === 0) {
      //   this.$message.success(res.msg)
      //   this.formOperate = 'detail'
      //   this.restoreHandle('bindAppid', this.type)
      // } else {
      //   this.$message.error(res.msg)
      // }
    },
    async modifyOrganization() {
      // 权限处理
      let params = {
        id: this.organizationData.id,
        appid: this.formData.appid,
        secret_key: this.formData.secret_key,
        company: this.organizationData.company,
        app_permission: this.formData.menuList
        // already_permission: this.formData.menuList,
        // not_app_permission: []
      }
      if (this.formData.templateId) {
        params.template_id = this.formData.templateId
      }
      // 如果不是自由配置，即选了版本的，只提交版本没有的permission就可以了
      if (this.permissionData.tollVersion && this.permissionData.tollVersion !== -1) {
        let newArr1 = this.getDifference(this.formData.menuList, this.versionPermissionData.app_permission)
        params.app_permission = deepClone(newArr1)
      } else {
        params.app_permission = deepClone(this.formData.menuList)
      }
      this.isLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.formOperate = 'detail'
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    },
    clipboardSuccess() {
      this.$message({
        message: '复制成功',
        type: 'success',
        duration: 1500
      })
    },
    // 获取移动端权限配置
    async getAppPermission() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationGetAppPermissionsPost())
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.allMenuList = res.data[0].children
      } else {
        this.$message.error(res.msg)
      }
    },
    // 显示抽屉
    showDrawer(type) {
      this.drawerType = type
      this.drawerShow = true
    },
    refreshPermission(data) {
      let arr = this.removeDuplicates(data)
      this.formData.menuList = deepClone(arr)
      let obj = deepClone(this.permissionData)
      obj.app_permission = deepClone(arr)
      this.$store.dispatch('permission/setPermissionData', obj)
    },
    // 数组去重
    removeDuplicates(arr) {
      return [...new Set(arr)]
    },
    // 返回数组差异
    getDifference(arr1, arr2) {
      const diff1 = arr1.filter(item => !arr2.includes(item))
      const diff2 = arr2.filter(item => !arr1.includes(item))
      return diff1.concat(diff2)
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.bindappid {
  position: relative;
  .appid-box{
    margin-top: 10px;
  }
  .add-wrapper{
    margin: 60px 0 60px 150px;
    .el-button{
      width: 120px;
    }
  }
}
</style>
