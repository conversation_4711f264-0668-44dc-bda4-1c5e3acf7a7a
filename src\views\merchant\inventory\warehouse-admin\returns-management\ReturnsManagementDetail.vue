<template>
  <div class="ReturnsManagementDetail container-wrapper">
    <h3 class="m-t-20">仓库管理/退货管理/详情</h3>
    <div class="table-wrapper p-20">
      <el-button size="small" class="ps-btn float-r m-t-10" v-if="detailData.refund_order_status === 'agree'" @click="showDialogHandle">申请退货</el-button>
      <div style="width: 460px">
        <table style="width: 100%" class="status-table table-box" border="0" cellspacing="0" cellpadding="0" align="center">
          <tbody>
            <tr align="center">
              <td colspan="1" class="p-b-10">物资合计金额（元）</td>
              <td colspan="1" class="p-b-10">创建时间</td>
              <td colspan="1" class="p-b-10">状态</td>
            </tr>
            <tr align="center">
              <td colspan="1">{{ totalPrice | formatMoney }}</td>
              <td colspan="1">{{ detailData.create_time }}</td>
              <td colspan="1">{{ detailData.refund_status_alias }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="table-wrapper">
      <el-tabs v-model="activePane" @tab-click="handleClick" class="pane-wrapper">
        <el-tab-pane label="入库物资" name="first" key="first">
          <el-table
            v-loading="isLoading"
            :data="materialsInfo"
            ref="materialsInfo"
            stripe
            size="small"
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in materialsTableSettings" :key="item.key" :col="item"></table-column>
          </el-table>
          <div class="m-t-10">合计：{{ totalPrice | formatMoney }}元</div>
        </el-tab-pane>
        <el-tab-pane label="审批流程" name="second" key="second">
          <!-- <ul class="approve-box">
            <li class="step">
              <div class="step-item step-icon"><i class="el-icon-circle-plus"></i></div>
              <div class="step-item step-status">发起申请</div>
              <div class="step-item step-name">房道网1111111111</div>
              <div class="step-item step-no">库管员</div>
              <div class="step-item step-time">2024-03-01 14:00:00</div>
              <div class="step-item step-reason">拒绝，退货金额有问题，请调整后重新提交</div>
            </li>
            <li class="step">
              <div class="step-item step-icon"><i class="el-icon-circle-plus"></i></div>
              <div class="step-item step-status">发起申请</div>
              <div class="step-item step-name">房道网</div>
              <div class="step-item step-no">库管员</div>
              <div class="step-item step-time">2024-03-01 14:00:00</div>
              <div class="step-item step-reason">拒绝，退货金额有问题，请调整后重新提交</div>
            </li>
          </ul> -->
          <div class="approve-box">
            <template v-if="detailData.record && detailData.record.length > 0">
              <el-timeline class="m-t-20 m-l-20">
                <el-timeline-item hide-timestamp size="large" v-for="(item, index) in detailData.record" :key="index">
                  <div class="step">
                    <div class="step-item step-status">{{ item.status || '--' }}</div>
                    <div class="step-item step-name">{{ item.operator || '--' }}</div>
                    <div class="step-item step-no">{{ item.role_name || '--' }}</div>
                    <div class="step-item step-time">{{ item.time || '--' }}</div>
                    <div class="step-item step-reason">{{ item.content || '--' }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </template>
            <div v-else class="m-l-20" style="font-size: 13px;">空</div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退货记录" name="third" key="third">
          <el-table
            v-loading="isLoading"
            :data="refundInfo"
            ref="refundInfo"
            stripe
            size="small"
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in refundTableSettings" :key="item.key" :col="item"></table-column>
          </el-table>
          <div class="m-t-10">合计：{{ refundTotalPrice | formatMoney }}元</div>
        </el-tab-pane>
        <el-tab-pane label="操作记录" name="fourth" key="fourth">
          <div class="m-t-10" style="width: 80%">
            <table class="table-box" style="width: 100%" border="0" cellspacing="0" cellpadding="0">
              <colgroup>
                <col style="width: 100px" />
                <col style="width: 100px" />
                <col style="width: 200px" />
                <col />
              </colgroup>
              <tbody>
                <template v-if="detailData.operator_record && detailData.operator_record.length > 0">
                  <tr v-for="(record, index)  in detailData.operator_record" :key="index">
                    <td colspan="1" class="p-b-20">{{ record.operator || '--' }}</td>
                    <td colspan="1" class="p-b-20">{{ record.role_name || '--' }}</td>
                    <td colspan="1" class="p-b-20">{{ record.time || '--' }}</td>
                    <td colspan="1" class="p-b-20">{{ record.content || '--' }}</td>
                  </tr>
                </template>
                <div v-else class="m-l-20" style="font-size: 13px;">空</div>
              </tbody>
            </table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- dialog start -->
    <returns-dialog
      :showreturns.sync="showReturnsDialog"
      :staticList="materialsInfo"
      :params="returnsParams"
      @confirmReturn="confirmReturnHandle"
    ></returns-dialog>
    <!-- dialog end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import ReturnsDialog from './ReturnsDialog'
import NP from 'number-precision'

export default {
  name: 'ProcureOfferList',
  mixins: [exportExcel],
  directives: {
    print
  },
  components: { ReturnsDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      warehouseId: '',
      id: '',
      activePane: 'first',
      detailData: {},
      totalPrice: 0, // 物资合计
      materialsInfo: [], // 入库物资
      materialsTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '入库数量', key: 'entry_count' },
        { label: '单位', key: 'unit_name' },
        { label: '参考单价', key: 'ref_unit_price', type: 'money' },
        { label: '入库价', key: 'entry_fee', type: 'money' },
        { label: '金额合计', key: 'total_fee', type: 'money' },
        { label: '供应商', key: 'supplier_manage_name' }
      ],
      refundInfo: [], // 退货记录
      refundTotalPrice: 0,
      refundTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '采购数量', key: 'entry_count' },
        { label: '退货数量', key: 'refund_count' },
        { label: '单位', key: 'unit_name' },
        { label: '入库价', key: 'entry_fee', type: 'money' },
        { label: '金额合计', key: 'total_fee', type: 'money' },
        { label: '退货金额', key: 'refund_fee', type: 'money' }
      ],
      showReturnsDialog: false,
      returnsParams: {}
    }
  },
  created() {
    this.warehouseId = +this.$route.query.warehouse_id
    this.id = +this.$route.query.id
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getReturnsManagementDetail()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.initLoad()
    }, 300),
    // 获取list接口数据
    async getReturnsManagementDetail() {
      if (!this.warehouseId) return this.$message.error('获取仓库id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: this.warehouseId,
        id: this.id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpExitInfoRefundDetailInfoPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalPrice = 0
        this.refundTotalPrice = 0
        this.detailData = res.data
        this.materialsInfo = res.data.materials_info.map(v => {
          this.totalPrice = NP.plus(v.total_fee, this.totalPrice)
          return v
        })
        this.refundInfo = res.data.refund_info.map(v => {
          this.refundTotalPrice = NP.plus(v.refund_fee, this.refundTotalPrice)
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // tab事件
    handleClick() {},
    showDialogHandle() {
      this.returnsParams = {
        id: this.id,
        warehouse_id: this.warehouseId
      }
      this.showReturnsDialog = true
    },
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = 'apiBackgroundDrpPurchaseInfoSupplierPurchaseModifyPost'
      let params = {}
      switch (type) {
        case 'refuse':
          params = {
            id: data.id,
            supplier_refuse: true
          }
          title = '确定拒收订单吗？'
          break
        case 'accept':
          params = {
            id: data.id,
            accept_order: true
          }
          title = '确定接收订单吗？'
          break
      }
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg || '成功')
              this.getReturnsManagementDetail()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    confirmReturnHandle() {
      this.getReturnsManagementDetail()
    }
  }
}
</script>

<style lang="scss">
.ReturnsManagementDetail {
  width: 100%;
  .w-200 {
    width: 200px !important;
  }
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
  .el-descriptions__body {
    background-color: unset;
  }
  .pane-wrapper {
    padding: 10px 20px 20px;
  }
  .approve-box {
    .step {
      // display: flex;
      // align-items: center;
      padding: 10px;
      background-color: #f2f2f2;
      border-radius: 4px;
    }
    .step-item {
      display: table-cell;
      // margin-right: 10px;
      min-width: 80px;
      padding: 0 10px;
      text-align: center;
    }
    .step-status {
      width: 100px;
    }
    .step-name {
      width: 120px;
    }
    .step-no {
      width: 120px;
    }
    .step-time {
      // padding-right: 10px;
    }
    .step-icon {
      margin-right: 0;
      min-width: 40px;
      width: 40px;
    }
    .el-timeline-item__wrapper {
      top: -12px;
    }
    .el-timeline-item {
      padding-bottom: 36px;
    }
  }
  .table-box {
    td {
      vertical-align: top;
    }
  }
}
</style>
