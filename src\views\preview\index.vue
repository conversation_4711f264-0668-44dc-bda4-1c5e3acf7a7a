<template>
  <div class="preview-wrapper">
    <div class="preview-container">
      <el-form ref="previewForm" :model="formData" label-position="top" v-loading="loading">
        <!-- 问卷标题 -->
        <div class="preview-header">
          <h2>{{ questionnaireData.name }}</h2>
          <p class="overview">{{ questionnaireData.overview }}</p>
        </div>

        <!-- 问题列表 -->
        <div class="preview-content">
          <div v-for="(question, index) in questionnaireData.questions" :key="index" class="question-item">
            <el-form-item
              :label="`${index + 1}. ${question.caption}`"
              :prop="`answers.${index}`"
              :rules="{ required: question.required, message: '此题为必答题', trigger: 'change' }">

              <!-- 单选题 -->
              <template v-if="question.question_type === 0">
                <el-radio-group v-model="formData.answers[index]" v-removeAriaHidden>
                  <div class="flex flex-col">
                    <el-radio
                      class="p-t-10 p-b-10"
                      v-for="(choice, choiceIndex) in question.choices"
                      :key="choiceIndex"
                      :label="choiceIndex"
                      disabled>
                      <span v-if="choice.type === 'default'">
                        {{ choice.description }}
                      </span>
                      <div v-else style="display: inline-block;">
                        <div class="flex flex-col">
                          <span class="p-b-10">{{ choice.description }}</span>
                          <el-input
                            class="w-350"
                            type="textarea"
                            :rows="3"
                            :placeholder="choice.other_content"
                            disabled>
                          </el-input>
                        </div>
                      </div>
                    </el-radio>
                  </div>
                </el-radio-group>
              </template>

              <!-- 多选题 -->
              <template v-if="question.question_type === 1">
                <el-checkbox-group
                  v-model="formData.answers[index]"
                  :min="question.least_choose_count || 0"
                  @change="(val) => handleChange(val, index)">
                  <div class="flex flex-col">
                    <el-checkbox
                      v-for="(choice, choiceIndex) in question.choices"
                      :key="choiceIndex"
                      :label="choiceIndex"
                      disabled>
                      <span v-if="choice.type === 'default'">
                          {{ choice.description }}
                        </span>
                        <div v-else style="display: inline-block;">
                          <div class="flex flex-col">
                            <span class="p-b-10">{{ choice.description }}</span>
                            <el-input
                              class="w-350"
                              type="textarea"
                              :rows="3"
                              :placeholder="choice.other_content"
                              disabled>
                            </el-input>
                          </div>
                        </div>
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </template>

              <!-- 评分题 -->
              <template v-if="question.question_type === 2">
                <div class="mark-topic-content">
                  <div class="mark-topic-content-item">
                    <div class="mark-topic-content-item-top">
                      <div class="point">1</div>
                      <div class="point">{{ question.top_score }}</div>
                    </div>
                    <div class="mark-topic-content-item-bottom">
                      <div v-for="item in question.top_score" :key="item" :class="['mark', 'selectScore']">
                        {{ item }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>

              <!-- 评价题 -->
              <template v-if="question.question_type === 3">
                <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex" class="evaluate-item">
                  <span class="evaluate-label">{{ choice.description }}</span>
                  <el-rate
                    v-model="formData.answers[index][choiceIndex]"
                    :max="question.top_score"
                    disabled>
                  </el-rate>
                </div>
              </template>

              <!-- 填空题 -->
              <template v-if="question.question_type === 4">
                <el-input
                  v-model="formData.answers[index]"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入您的答案"
                  disabled>
                </el-input>
              </template>

              <!-- 文件上传 -->
              <template v-if="question.question_type === 6">
                <el-upload
                  class="upload-demo"
                  action="https://jsonplaceholder.typicode.com/posts/"
                  multiple
                  disabled>
                  <div class="flex-center">
                    <el-button class="m-r-20" size="small" icon="el-icon-plus" :disabled="true">添加文件</el-button>
                    <div slot="tip" class="el-upload__tip">不超过10M</div>
                  </div>
                </el-upload>
              </template>

              <!-- 图片上传 -->
              <template v-if="question.question_type === 5">
                <el-upload
                  action="https://jsonplaceholder.typicode.com/posts/"
                  :on-preview="handlePictureCardPreview"
                  :on-remove="handleRemove"
                  disabled>
                  <div class="flex w-100-p flex-wrap">
                    <div class="flex-center upload-border m-5">
                      <div>
                        <i class="el-icon-plus" style="font-size: 22px;"></i>
                      </div>
                    </div>
                  </div>
                  <div slot="tip" class="el-upload__tip">不超过5M</div>
                </el-upload>
              </template>

            </el-form-item>
          </div>
        </div>

        <!-- 添加提交按钮 -->
        <div class="preview-footer">
          <el-button
            type="primary"
            size="large">
            提交问卷
          </el-button>
        </div>
      </el-form>

      <div class="flex-center p-b-20">本次问卷以{{ questionnaireData.commit_type === 'real_name' ? '实名' : '匿名' }}形式提交</div>
    </div>
  </div>
</template>

<script>
import { getSessionStorage } from '@/utils'
export default {
  name: 'preview',
  data() {
    return {
      questionnaireData: {
        name: '问卷标题',
        overview: '问卷概述',
        questions: []
      },
      formData: {
        answers: []
      },
      loading: false,
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  created() {
    const data = getSessionStorage('SurveyData')
    this.questionnaireData = JSON.parse(data)
    console.log('this.questionnaireData', this.questionnaireData)
    // 初始化答案数组
    this.initAnswers()
  },
  methods: {
    handleChange(value, index) {
      // 确保多选题的值始终是数组
      if (Array.isArray(value)) {
        this.$set(this.formData.answers, index, value)
      }
    },
    initAnswers() {
      // 使用 Vue.set 来确保响应性
      this.formData.answers = this.questionnaireData.questions.map((question, index) => {
        let answer;
        if (question.question_type === 1) { // 多选题返回空数组
          answer = [];
        } else if (question.question_type === 3) { // 评价题返回空对象
          answer = {};
          question.choices.forEach((_, choiceIndex) => {
            answer[choiceIndex] = 0;
          });
        } else { // 其他题型返回空值
          answer = '';
        }
        return answer;
      });
      console.log('初始化答案数组：', this.formData.answers);
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$primary-color: #409EFF;
$border-color: #EBEEF5;
$text-primary: #303133;
$text-regular: #606266;
$bg-color: #f5f7fa;
$container-width: 800px;
$spacing-base: 8px;

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
}

@mixin hover-effect {
  transition: all 0.3s ease;
  &:hover {
    border-color: $primary-color;
    box-shadow: 0 0 8px rgba($primary-color, 0.1);
  }
}

@mixin section-padding {
  padding: $spacing-base * 3 $spacing-base * 2.5;
}

// 重置最后一个元素的边距
@mixin reset-last-margin {
  &:last-child {
    margin-bottom: 0;
  }
}

.preview-wrapper {
  min-height: 100vh;
  background-color: $bg-color;
  padding: 20px 0;
  margin: 0;
}

.preview-container {
  max-width: $container-width;
  margin: 0 auto;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(#000, 0.1);
  border-radius: 10px;

  .preview-header {
    text-align: center;
    margin-bottom: $spacing-base * 3;
    @include section-padding;

    h2 {
      margin: 0 0 $spacing-base * 1.5;
      color: $text-primary;
      font-size: 24px;
      font-weight: 500;
    }

    .overview {
      color: $text-regular;
      font-size: 14px;
      margin: 0;
      line-height: 1.6;
    }
  }

  .preview-content {
    @include section-padding;

    .question-item {
      margin-bottom: $spacing-base * 3;
      padding: $spacing-base * 2.5;
      border: 1px solid $border-color;
      border-radius: 4px;
      @include hover-effect;
      @include reset-last-margin;
    }
  }

  .evaluate-item {
    @include flex-center;
    margin-bottom: $spacing-base * 1.5;
    @include reset-last-margin;

    .evaluate-label {
      max-width: 120px;
      margin-right: $spacing-base * 2;
      margin-left: $spacing-base * 2;
      color: $text-regular;
    }
  }

  .preview-footer {
    text-align: center;
    margin-top: 0;
    @include section-padding;

    .el-button {
      width: $spacing-base * 25; // 200px
      height: $spacing-base * 5.5; // 44px
      font-size: 16px;
    }
  }
}

// 响应式设计
@media screen and (max-width: #{$container-width + $spacing-base * 4}) {
  .preview-container {
    margin: 0 $spacing-base * 2;
  }
}

@media screen and (max-width: 480px) {
  .preview-container {
    margin: 0;

    .preview-content {
      padding: $spacing-base * 2;

      .question-item {
        padding: $spacing-base * 2;
      }
    }

    .evaluate-item {
      flex-direction: column;
      align-items: flex-start;

      .evaluate-label {
        width: 100%;
        margin-bottom: $spacing-base;
      }
    }
  }
}
.mark-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      border: 1px solid #E7ECF2;
      border-radius: 6px;
      padding: 5px;
      &-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px;
        border-bottom: 1px solid #E7ECF2;
        .point {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      &-bottom {
        margin-top: 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .mark {
          width: 32px;
          height: 32px;
          margin: 0 5px;
          border: 1px solid #E7ECF2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .selectScore {
          background-color: #F5F7FA;
          color: #C0C4CC;
        }
      }
    }
  }
}

.upload-border {
  border: 1px dotted #DCDFE6;
  width: 120px;
  height: 120px;
  border-radius: 8px;
}
</style>
