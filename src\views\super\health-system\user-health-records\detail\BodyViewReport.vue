<template>
  <div class="BodyViewReport">
    <div class="table-wrapper">
      <div class="table-content">
        <div class="p-t-20">
          <div class="title p-l-40">体检一体机</div>
          <div class="p-l-10 p-t-20" v-for="(item, key, index) in formData" :key="index">
            <div class="title-content p-b-5">{{ item.name }}</div>
            <div class="content p-b-5" v-for="(info, infoKey, infoIndex) in item.children" :key="infoIndex">
              <span class="text">{{ info.name }}：</span>
              <span>{{ info.value }} {{ info.unit }}</span>
            </div>
          </div>
        </div>
        <div class="m-t-20 p-l-20">
          <el-button class="ps-btn" type="primary" size="small" @click="handleExport">
            下载报告
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  mixins: [exportExcel],
  data() {
    return {
      params: {},
      formData: {}
    }
  },
  created() {
    this.params = this.$route.query
    this.getBodyDataDetail(this.params)
  },
  mounted() {},
  methods: {
    // 体检数据详情
    async getBodyDataDetail(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminHealthyInfoBodyDataDetailPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.formData = res.data.results
        console.log(123)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出报表
    handleExport() {
      const option = {
        type: 'ExportHealthyInfoBodyData',
        params: {
          ids: this.params.id,
          type: this.params.type,
          user_id: this.params.user_id
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.BodyViewReport {
  .table-content {
    .title {
      font-size: 23px;
      font-weight: bold;
    }
    .title-content {
      font-weight: bold;
      font-size: 18px;
    }
    .content {
      font-size: 15px;
    }
  }
}
</style>
