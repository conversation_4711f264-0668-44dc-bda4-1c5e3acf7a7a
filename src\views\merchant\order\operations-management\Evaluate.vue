<template>
  <div class="evaluate-wrapper container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
    >
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <el-button v-permission="['background_operation_management.evaluation_setting.list']" size="small" class="ps-origin-btn" @click="showSettingDialog">基础设置</el-button>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table :data="tableData" v-loading="isLoading" header-row-class-name="ps-table-header-row" @selection-change="handleSelectionChange">
          <table-column  v-for="item in tableSetting" :key="item.key" :col="item">
            <template #score="{ row }">
              <!-- <el-rate class="rate reset-rate" v-model="row.order_score" disabled></el-rate> -->
              <div>
                <template v-for="i in 5">
                  <el-image v-if="i <= row.order_score" :key="i" src="https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/icons/rate2_1.png " class="" style="width: 18px; height: 18px; margin-right: 4px; vertical-align: middle;"></el-image>
                  <el-image v-if="i > row.order_score" :key="i" src="https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/icons/rate2_0.png " class="" style="width: 18px; height: 18px; margin-right: 4px; vertical-align: middle;"></el-image>
                </template>
              </div>
            </template>
            <template #operation="{ row }">
            <el-button v-if="!row.is_reply" v-permission="['background_operation_management.order_evaluation.reply_order']" type="text" size="small" class="ps-text" @click="showDetailHandle('reply', row)">回复</el-button>
            <el-button type="text" size="small" class="ps-text" @click="showDetailHandle('detail', row)">详情</el-button>
            <!-- <el-button type="text" size="small" class="ps-warn"  @click="deleteHandle(row)">删除</el-button> -->
          </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :page-sizes="[10, 20, 50, 100, 200, 500, 1000]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 -->
    <evaluateDialog
      :isshow.sync="showEvaluateDialog"
      :dialogInfo="evaluateDialogInfo"
      :type="evaluateDialogType"
      @confirm="confirmDialog"
    />
    <evaluateSettingDialog
      :isshow.sync="showEvaluateSettingDialog"
      :dialogInfo="evaluateSettingDialogInfo"
      @confirm="confirmDialog"
    />
  </div>
</template>

<script>
import { EVALUATE_LIST } from './constants'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone, parseTime } from '@/utils'
import evaluateDialog from '../component/evaluateDialog'
import evaluateSettingDialog from '../component/evaluateSettingDialog'

export default {
  name: 'EvaluateList',
  components: { evaluateDialog, evaluateSettingDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: deepClone(EVALUATE_LIST),
      tableSetting: [
        { label: '评价时间', key: 'create_time' },
        { label: '整单评价', key: 'order_score', type: "slot", slotName: "score", minWidth: '140px' },
        { label: '评价内容', key: 'evaluation_content', showTooltip: true },
        { label: '是否匿名', key: 'is_anonymous_alias' },
        { label: '商家回复', key: 'reply_content', showTooltip: true },
        { label: '回复时间', key: 'reply_time'},
        { label: '订单号', key: 'trade_no'},
        { label: '所属组织', key: 'organization_name'},
        { label: '操作人', key: 'account_alias' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
      ],
      showEvaluateDialog: false,
      evaluateDialogType: '',
      evaluateDialogInfo: {},
      showEvaluateSettingDialog: false,
      evaluateSettingDialogInfo: {}
    }
  },
  mounted() {
    // this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getEvaluateList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getEvaluateList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getEvaluateList() {
      const params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOperationManagementOrderEvaluationListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          v.is_anonymous_alias = v.is_anonymous ? '是' : '否'
          if (v.reply_time) {
            v.reply_time = parseTime(v.reply_time)
          }
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除
    deleteHandle(col) {
      this.$confirm(`是否删除？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // instance.cancelButtonLoading = true
            const [err, res] = await to(
              this.$apis[this.getModifyApi()]({
                id: col.id,
                feedback_status: 'delete'
              })
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getEvaluateList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getEvaluateList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    showDetailHandle(type, data) {
      this.evaluateDialogType = type
      this.evaluateDialogInfo = data
      this.showEvaluateDialog = true
    },
    showSettingDialog (data) {
      // this.evaluateSettingDialogInfo = data
      this.showEvaluateSettingDialog = true
    },
    confirmDialog() {
      this.getEvaluateList()
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
