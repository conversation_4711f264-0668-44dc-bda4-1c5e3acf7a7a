<template>
  <div class="drawer-box">
    <customDrawer
      :show.sync="visible"
      :loading="isLoading"
      :title="'订单详情'"
      :confirmShow="false"
      @confirm="saveSetting"
      :cancelText="'关 闭'"
      cancelClass="ps-btn"
      :size="800"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form
            :model="drawerFormData"
            @submit.native.prevent
            ref="drawerFormDataRef"
            :inline="true"
          >
            <div>订单信息</div>
            <div class="" style="margin-top:10px; background-color: #edf2fa;border-radius: 5px;">
              <el-form-item label="订单号:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.trade_no }}</div>
              </el-form-item>
              <el-form-item label="创建时间:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.create_time }}</div>
              </el-form-item>
              <el-form-item label="支付时间:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.pay_time }}</div>
              </el-form-item>
              <el-form-item label="兑换积分:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.points }}</div>
              </el-form-item>
              <el-form-item label="支付金额:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.origin_fee | formatMoney }}</div>
              </el-form-item>
              <el-form-item label="购买数量:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.num }}</div>
              </el-form-item>
              <el-form-item label="订单状态:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.order_status_alias }}</div>
              </el-form-item>
              <el-form-item label="兑换账号:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.phone }}</div>
              </el-form-item>
              <!-- <el-form-item label="兑换地址:" label-width="80px">
                <div></div>
              </el-form-item> -->
            </div>
            <div class="m-t-20">用户信息</div>
            <div  style="margin-top:10px; background-color: #edf2fa;border-radius: 5px;">
              <el-form-item label="姓名:" label-width="80px">
                <div style="width: 165px">{{ drawerFormData.user_name }}</div>
              </el-form-item>
              <el-form-item label="手机号:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.user_phone }}</div>
              </el-form-item>
            </div>
            <div class="m-t-20">商品信息</div>
            <div  style="margin-top:10px; background-color: #edf2fa;border-radius: 5px;">
              <el-form-item label="商品名称:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.commodity_name }}</div>
              </el-form-item>
              <el-form-item label="商品类型:" label-width="80px">
                <div style="width: 170px">{{ drawerFormData.commodity_type_alias }}</div>
              </el-form-item>
              <el-form-item label="商品价格:" label-width="80px">
                <div style="width: 170px">
                  {{ drawerFormData.origin_fee | formatMoney }}元+{{ drawerFormData.points }}积分
                </div>
              </el-form-item>
            </div>
            <div  style="margin-top: 20px;">
                <el-button class="ps-btn" type="primary" @click="clickDetailsCommodity">
                  查看商品
                </el-button>
              </div>
          </el-form>
        </div>
      </div>
    </customDrawer>
    <!-- 要返回一个商品的Id -->
    <commodiy-details-drawer
      v-if="commodiyDetailsDrawerVisible"
      :isshow.sync="commodiyDetailsDrawerVisible"
      :commodiyId="drawerFormData.points_commodity"
    ></commodiy-details-drawer>
  </div>
</template>

<script>
import { to } from '@/utils'
import CommodiyDetailsDrawer from './commodiyDetailsDrawer.vue'
export default {
  props: {
    isshow: Boolean,
    tradeNo: String
    // drawerModifyData: {
    //   type: Object,
    //   default() {
    //     return {}
    //   }
    // }
  },
  components: { CommodiyDetailsDrawer },
  data() {
    return {
      isLoading: false,
      drawerFormData: {
        // name: ''
      },
      drawerFormDataRuls: {
        // name: [{ required: true, message: '请输入退款原因', trigger: 'blur' }]
      },
      commodiyDetailsDrawerVisible: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    // console.log(this.tradeNo)
    this.getPointsOrderList()
  },
  methods: {
    closeClick() {
      this.visible = false
    },
    // 获取列表数据
    async getPointsOrderList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMemberPointsPointsOrderListPost({
          trade_no: this.tradeNo,
          page: 1,
          page_size: 99
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data.results.length) {
          this.drawerFormData = res.data.results[0]
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    clickDetailsCommodity(row) {
      this.commodiyDetailsDrawerVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    padding: 30px;
    .drawer-content {
    }
  }
  ::v-deep .el-form-item {
    margin-bottom: 0px;
  }
}
</style>
