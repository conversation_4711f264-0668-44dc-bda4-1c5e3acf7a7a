<template>
  <div class="ps-el-drawer">
    <el-drawer
      :visible="visible"
      :show-close="false"
      size="75%">
      <template #title>
        <div class="ps-flex-align-c flex-align-c">
          <div class="m-r-20">权限设置</div>
        </div>
      </template>
      <div class="p-20" v-loading="isLoading">
        <el-tabs type="card" tab-position="left" class="version-configuration-content-box">
          <el-tab-pane :label="item.verbose_name" v-for="(item, index) in merchantFeatureList" :key="index">
            <div class="p-20">
              <div class="m-b-10 w-150">
                <el-checkbox v-model="item.isSelect" :indeterminate="item.isIndeterminate" class="ps-flex-align-c flex-align-c" @change="dataListHandle(item.isSelect, item, index, true)" :disabled="item.canNotSelect">
                  <span class="font-size-18 f-w-500">全选当前页</span>
                </el-checkbox>
              </div>
              <div v-for="(item1, index1) in item.children" :key="index1" class="m-b-20">
                <div class="w-150">
                  <el-checkbox v-model="item1.isSelect" :indeterminate="item1.isIndeterminate" class="ps-flex-align-c flex-align-c m-b-10" @change="dataListHandle(item1.isSelect, item1, index, false)" :disabled="item1.canNotSelect">
                    <span class="font-size-18 f-w-700">{{item1.verbose_name}}</span>
                  </el-checkbox>
                </div>
                <div style="border-top: 1px solid #e5e7ea;">
                  <div class="box-item flex-start " :style="index2 % 2 === 0 ? {backgroundColor: '#ffffff'} : {backgroundColor: '#f8f9fa'}" v-for="(item2, index2) in item1.children" :key="index2">
                    <div :class="[item2.children.length ? '' : 'box-item-left', 'p-20']">
                      <el-checkbox v-model="item2.isSelect"  :indeterminate="item2.isIndeterminate" @change="dataListHandle(item2.isSelect, item2, index, false)" :disabled="item2.canNotSelect">
                        <span class="ellipsis w-100">{{ item2.verbose_name }}</span>
                      </el-checkbox>
                    </div>
                    <!-- 只有四层的显示布局 -->
                    <div :class="[item2.children.length ? 'box-item-right1' : '', 'p-20', 'w-100-p']" v-if="item2.children.length && !hasChildren(item2.children)">
                      <div v-for="(item3, index3) in item2.children" :key="index3">
                        <el-checkbox v-model="item3.isSelect" @change="dataListHandle(item3.isSelect, item3, index, false)" :disabled="item3.canNotSelect">
                          <span class="ellipsis w-150">{{ item3.verbose_name }}</span>
                        </el-checkbox>
                      </div>
                    </div>
                    <!-- 有五层的显示布局 -->
                    <div :class="[item2.children.length ? 'box-item-right2' : '', 'w-100-p']" v-if="item2.children.length && hasChildren(item2.children)">
                      <div v-for="(item3, index3) in item2.children" :key="index3" class="three-level flex-start" :style="index3 < item2.children.length -1 ? {borderBottom: '1px solid #e5e7ea'}  : {}">
                        <el-checkbox v-model="item3.isSelect" class="p-20" @change="dataListHandle(item3.isSelect, item3, index, false)" :disabled="item3.canNotSelect">
                          <span class="ellipsis w-150">{{ item3.verbose_name }}</span>
                        </el-checkbox>
                        <div v-if="item3.children.length" class="three-level-right p-20 w-100-p">
                          <div v-for="(item4, index4) in item3.children" :key="index4">
                            <el-checkbox v-model="item4.isSelect" @change="dataListHandle(item4.isSelect, item4, index, false)" :disabled="item4.canNotSelect">
                              <span class="ellipsis w-150">{{ item4.verbose_name }}</span>
                            </el-checkbox>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="version-configuration-content-footer">
          <div class="button-area m-r-40">
            <el-button size="small" class="w-100" @click="cancel">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="save">保存</el-button>
          </div>
          <div class="checkbox-area m-r-40">
            <el-checkbox v-model="selectAll" @change="isSelectAll('selectAll', true)">
              <span class="font-size-16">全选</span>
            </el-checkbox>
            <el-checkbox v-model="selectNone" @change="isSelectAll('selectNone', false)">
              <span class="font-size-16">全不选</span>
            </el-checkbox>
          </div>
          <div>定制数量：{{ computedSelectCount }}/{{ computedTotalCount }}</div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
export default {
  props: {
    isShow: Boolean,
    allPermissionList: {
      type: Array,
      default: () => {
        return []
      }
    }, // 全部权限
    selectedPermissionList: {
      type: Array,
      default: () => {
        return []
      }
    } // 已选的权限
  },
  data() {
    return {
      isLoading: false,
      merchantFeatureList: [],
      selectNone: false,
      selectAll: false,
      haveBeenSelectKey: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    },
    hasChildren() {
      return d => {
        let flag = false
        d.forEach(item => {
          if (item.children.length) {
            flag = true
          }
        })
        return flag
      }
    },
    computedSelectCount() {
      let count = 0
      this.merchantFeatureList.forEach(item => {
        count += item.tabSelectCount
      })
      return count
    },
    computedTotalCount() {
      let count = 0
      this.merchantFeatureList.forEach(item => {
        count += item.tabTotalCount
      })
      return count
    }
  },
  watch: {
    selectAll(newVal, oldVal) {
      if (newVal) {
        if (this.selectNone) {
          this.selectNone = false
        }
      }
    },
    selectNone(newVal, oldVal) {
      if (newVal) {
        if (this.selectAll) {
          this.selectAll = false
        }
      }
    },
    visible(newVal) {
      if (newVal) {
        // 获取已选权限
        this.haveBeenSelectKey = deepClone(this.selectedPermissionList)
        this.initLoad(this.allPermissionList)
      }
    }
  },
  created() {
  },
  methods: {
    initLoad(data) {
      this.merchantFeatureList = data.map((item, index) => {
        // 插入必要的key
        Object.assign(item, {
          tabSelectCount: 0,
          tabTotalCount: 0
        })
        // 逐级判断item.key是否存在haveBeenSelectKey里面
        this.switchStatus(item)
        // 逐级改变勾选状态
        this.switchIndeterminate(true, item)
        return item
      })
      // 计数
      let arr = this.merchantFeatureList.map((item, index) => {
        this.setCount(item, index)
        return item
      })
      this.merchantFeatureList = deepClone(arr)
      this.isLoading = false
    },
    switchIndeterminate(status, data) {
      this.changeSelfIndeterminate(status, data)
      if (data.children && data.children.length) {
        data.children.map(item => {
          if (item.isSelect) {
            this.switchIndeterminate(status, item)
          }
          return item
        })
      }
    },
    dataListHandle(status, data, index, selectAllOrNot) {
      // 先改变isSelect状态
      if (status) {
        this.selectNone = false
      } else {
        this.selectAll = false
      }
      // debugger
      this.selectAllItem(status, data, index)
      // 为了实现全选当前页的效果，这里需要再遍历判断（我实在想不到什么更好的办法了）
      this.changeSelfIndeterminate(status, data)
      // 判断是否是点击全选或全不选触发的，是的话没必要走多一次循环
      if (!selectAllOrNot) {
        // 判断是否全选了，未全选要显示对应的样式
        this.merchantFeatureList.forEach(item => {
          this.changeParentIndeterminate(status, item, data)
        })
      }
      // 再重新计数
      this.merchantFeatureList[index].tabSelectCount = 0
      if (this.merchantFeatureList[index].isSelect) {
        this.merchantFeatureList[index].tabSelectCount++
      }
      if (this.merchantFeatureList[index].children && this.merchantFeatureList[index].children.length) {
        this.resetTabSelectCount(this.merchantFeatureList[index].children, index)
      }
    },
    changeSelfIndeterminate(status, data) {
      // 点击前改变当前节点的状态
      if (status) {
        // 看底下children情况
        if (!data.children.some(item => item.isSelect)) {
          data.isIndeterminate = false
        } else if (data.children.some(item => !item.isSelect)) {
          data.isIndeterminate = true
        } else {
          data.isIndeterminate = false
        }
      } else {
        if (data.children.some(item => item.isSelect)) {
          data.isIndeterminate = true
        } else if (!data.children.some(item => item.isSelect)) {
          data.isIndeterminate = false
        } else {
          data.isIndeterminate = false
        }
      }
      if (data.children && data.children.length) {
        data.children.forEach(item => {
          this.changeSelfIndeterminate(status, item)
        })
      }
    },
    changeParentIndeterminate(status, father, data) {
      // 先判断item的index是不是data的parent
      if (father.index === data.parent) {
        if (status) {
          // 判断此父级的勾选状态
          if (!father.children.some(item => item.isSelect)) {
            father.isIndeterminate = false
            father.isSelect = true
          } else if (father.children.some(item => !item.isSelect)) {
            father.isIndeterminate = true
            father.isSelect = true
          } else {
            father.isIndeterminate = false
            father.isSelect = true
          }
        } else {
          // 判断此父级的勾选状态
          if (father.children.some(item => item.isSelect)) {
            father.isIndeterminate = true
            father.isSelect = true
          } else if (!father.children.some(item => item.isSelect)) {
            father.isIndeterminate = false
            if (father.level === 0) {
              father.isSelect = false
            } else {
              father.isSelect = true
            }
          } else {
            father.isIndeterminate = false
            father.isSelect = true
          }
        }
        // 更往上的状态也要变动
        if (father.level !== 0) {
          this.merchantFeatureList.forEach(item => {
            this.changeParentIndeterminate(status, item, father)
          })
        }
      } else {
        if (father.children && father.children.length) {
          father.children.forEach(item => {
            this.changeParentIndeterminate(status, item, data)
          })
        }
      }
    },
    // 遍历数组计数
    resetTabSelectCount(arr, index) {
      if (arr.length) {
        arr.forEach(item => {
          if (item.isSelect) {
            this.merchantFeatureList[index].tabSelectCount++
          }
          if (item.children && item.children.length) {
            this.resetTabSelectCount(item.children, index)
          }
        })
      }
    },
    // 初始化设置计数
    setCount(data, index) {
      if (Object.keys(data).includes('isSelect')) {
        this.merchantFeatureList[index].tabTotalCount++
      }
      if (data.isSelect) {
        this.merchantFeatureList[index].tabSelectCount++
      }
      // 判断该层是否有children
      if (data.children && data.children.length) {
        data.children.forEach(item => {
          this.setCount(item, index)
        })
      }
    },
    // 初始化改变状态
    switchStatus(data) {
      // 插入必要key
      Object.assign(data, {
        isSelect: false,
        isIndeterminate: false,
        canNotSelect: false
      })
      if (this.haveBeenSelectKey.length && this.haveBeenSelectKey.includes(data.key)) {
        data.isSelect = true
      } else {
        data.isSelect = false
      }
      // 判断该层是否有children
      if (data.children && data.children.length) {
        data.children.forEach(item => {
          this.switchStatus(item)
        })
      }
    },
    // 返回数组差异
    getDifference(arr1, arr2) {
      const diff1 = arr1.filter(item => !arr2.includes(item))
      const diff2 = arr2.filter(item => !arr1.includes(item))
      return diff1.concat(diff2)
    },
    // 选中下面的全部权限
    selectAllItem(status, data, index) {
      data.isSelect = status
      if (data.children && data.children.length) {
        data.children.forEach(item => {
          this.selectAllItem(status, item, index)
        })
      }
    },
    // 是否全选全部功能
    isSelectAll(btn, status) {
      if ((btn === 'selectAll' && status) || (btn === 'selectNone' && !status)) {
        this.isLoading = true
        this.merchantFeatureList.forEach((item, index) => {
          if (!item.canNotSelect) {
            this.searchCanNoSelect(item, status, index)
          }
          this.isLoading = false
          item.tabSelectCount = 0
          if (status) {
            item.tabSelectCount = item.tabTotalCount
          } else {
            if (item.isSelect) {
              this.merchantFeatureList[index].tabSelectCount++
            }
            if (item.children && item.children.length) {
              this.resetTabSelectCount(item.children, index)
            }
          }
        })
      }
    },
    // 逐级查找并判断item.canNoSelect
    searchCanNoSelect(data, status, index) {
      if (!data.canNotSelect) {
        data.isSelect = status
        this.dataListHandle(status, data, index, true)
      } else {
        if (data.children && data.children.length) {
          data.children.forEach(item => {
            this.searchCanNoSelect(item, status, index)
          })
        }
      }
    },
    // 保存
    save() {
      this.selectAll = false
      this.selectNone = false
      this.haveBeenSelectKey = deepClone(this.traverseGroups(this.merchantFeatureList))
      console.log('已勾选的权限', this.haveBeenSelectKey)
      this.merchantFeatureList = []
      this.visible = false
      this.$emit('savePermission', this.haveBeenSelectKey)
    },
    // 取消
    cancel() {
      // this.$emit('refreshPermission')
      this.selectAll = false
      this.selectNone = false
      this.merchantFeatureList = []
      this.visible = false
    },
    traverseGroups(permissionTree) {
      let arr = []
      this.traverseGroupsDetail(permissionTree, arr)
      return arr
    },
    // 遍历权限数组重新赋值
    traverseGroupsDetail(permissionTree, permissionArr) {
      permissionTree.forEach(item => {
        if (item.isSelect) {
          permissionArr.push(item.key)
        }
        if (item.children && item.children.length) {
          this.traverseGroupsDetail(item.children, permissionArr)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.version-configuration {
  &-content {
    &-box {
      height: calc(100vh - 160px);
      .box-item {
        background-color: #D7D7D7;
        border-bottom: 1px solid #e5e7ea;
        // border-radius: 4px;
        &-left {
          border-right: 1px solid #e5e7ea;
        }
        &-right1 {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          grid-gap: 10px 20px;
          border-left: 1px solid #e5e7ea;
        }
        &-right2 {
          display: flex;
          flex-direction: column;
          .three-level {
            display: grid;
            grid-template-columns: 1fr 3fr;
            border-left: 1px solid #e5e7ea;
            &-right {
              margin-left: -5px;
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              grid-gap: 10px 20px;
              border-left: 1px solid #e5e7ea;
            }
          }
        }

      }
    }
    &-box2 {
      background-color: #f8f9fa;
      border-radius: 4px;
    }
    &-footer {
      z-index: 10;
      position: sticky;
      bottom: 0px;
      padding: 20px 0px 0px;
      background-color: #fff;
      display: flex;
      align-items: center;
      .button-area {
        display: flex;
        align-items: center;
      }
      .checkbox-area {
        display: flex;
        align-items: center;
      }
    }
  }
  .bg-style {
    height: calc(86vh - 75px);
    background-color: #fff;
    border-radius: 8px;
  }
}
::v-deep .el-tabs--card>.el-tabs__header {
  border-bottom: none;
  & .el-tabs__nav {
    border: none;
  }
}
::v-deep .el-tabs--left.el-tabs--card .el-tabs__item.is-left {
  border: none;
  &.is-active {
    background-color: #f8f9fa;
  }
}
::v-deep .el-tabs--left .el-tabs__header.is-left {
  margin-right: 0px;
}
::v-deep .el-tabs__content {
  background-color: #f8f9fa;
  overflow: auto;
  height: 100%;
}
::v-deep .el-checkbox {
  display: flex;
  align-items: center;
  &__label {
    display: flex;
    align-items: center;
  }
}
</style>
