<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
        </div>
        <div class="align-r">
          <el-button size="mini" @click="gotoExport" v-permission="['background_order.finance_report.person_charge_list_export']">导出Excel</el-button>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :span-method="arraySpanMethod"
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row" />
        </div>
        <!-- 统计 start -->
        <table-statistics v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText" :statistics="collect" />
        <!-- end -->
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="total"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { PERSONAL_RECHARGE_SUMMARY } from './constantsConfig'
import { debounce, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { mergeHandle, mergeRowAction } from '@/utils/table'
import report from '@/mixins/report' // 混入

export default {
  name: 'PersonalRechargeSummary',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      isLoadingCollect: false,
      elementLoadingText: "数据正在加载，请耐心等待...",
      tableSetting: [
        // { label: '序号', key: 'index', type: 'index' },
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '手机号', key: 'phone' },
        { label: '分组', key: 'payer_group' },
        { label: '部门', key: 'payer_department_group' },
        { label: '原充值金额', key: 'origin_fee', type: 'money' },
        { label: '充值金额到账', key: 'wallet_fee', type: 'money' },
        { label: '赠送金额到账', key: 'complimentary_fee', type: 'money' },
        { label: '手续费', key: 'rate_fee', type: 'money' }
        // { label: '现金充值', key: 'cash_pay', type: 'money' },
        // { label: '支付宝充值', key: 'ali_pay', type: 'money' },
        // { label: '农行充值', key: 'abc_pay', type: 'money' },
        // { label: '微信充值', key: 'wechat_pay', type: 'money' }
        // { label: '第三方充值', key: '1', type: 'money' }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: deepClone(PERSONAL_RECHARGE_SUMMARY),
      rowMergeArrs: [],
      mergeOpts: {
        useKeyList: {
          person_no: ['name', 'person_no', 'phone', 'payer_group', 'payer_department_group']
        } // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        // mergeKeyList: [] // 通用的合并字段，根據值合并
      },
      collect: [ // 统计
        { key: 'total_origin_fee', value: 0, label: '原充值金额:￥', type: 'money' },
        { key: 'total_wallet_fee', value: 0, label: '充值钱包到账:￥', type: 'money' },
        { key: 'total_complimentary_fee', value: 0, label: '赠送钱包到账:￥', type: 'money' }
      ],
      printType: 'PersonalRechargeSummary',
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
  },
  mounted() {
    this.getPechargeMethod()
    // this.initLoad()
  },
  methods: {
    async initLoad(isFirst) {
      if (!isFirst) {
        // await this.getOrgWallet()
        this.getRechargeList()
        this.getRechargeCollectList()
      }
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1;
      this.initLoad()
      this.isFirstSearch = true
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getRechargeList()
        this.getRechargeCollectList()
        this.isFirstSearch = false
      }
    }, 300),
    // 充值方式
    async getPechargeMethod() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportPechargeMethod({
        page: 1,
        page_size: 999,
        org_ids: []
      })
      if (res.code === 0) {
        const result = []
        res.data.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.payway.dataList = [{ label: '全部', value: '' }, ...result]
        result.forEach(v => {
          // let key = v.value.toLowerCase().replace('pay', '_pay')
          this.tableSetting.push({ label: v.label, key: v.value, type: 'money' })
        })
        // this.currentTableSetting = this.tableSetting
        this.initPrintSetting()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getRechargeList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportPersonChargeListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.results
        this.collectList = res.data.collect
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表合计数据
    async getRechargeCollectList() {
      this.isLoadingCollect = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportPersonChargeListCollectPost({
        ...this.formatQueryParams(this.searchFormSetting)
      })
      if (res.code === 0) {
        this.elementLoadingText = '数据正在加载，请耐心等待...'
        this.isLoadingCollect = false
        // 统计
        this.setCollectData(res)
      } else {
        this.elementLoadingText = '汇总数据加载失败，请重试。'
        this.$message.error('汇总数据加载失败，请重试。')
      }
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getRechargeList()
    },
    gotoExport() {
      const option = {
        type: "PersonalRechargeSummary",
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '个人充值汇总',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportPersonChargeListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss">
.el-table {
  text-align: center;
  font-size: 12px;
}
.el-loading-wrapp{
  .el-loading-spinner{
    margin-top:0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
