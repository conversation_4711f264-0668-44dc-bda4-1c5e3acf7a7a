<template>
  <div class="container-wrapper">
    <div class="table-wrapper p-t-20">
      <menu-nutrition-analysis :menuType="menu_type" :menuId="menu_id" />
    </div>
  </div>
</template>

<script>
import menuNutritionAnalysis from '../components/menu/menuNutritionAnalysis'
export default {
  name: 'AddWeekRecipesSeparate',
  data() {
    return {
      menu_type: '',
      menu_id: ''
    }
  },
  components: {
    menuNutritionAnalysis
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.menu_type = this.$route.query.menu_type
      this.menu_id = String(this.$route.query.id)
    }
  }
}
</script>

<style scoped lang="scss"></style>
