<template>
  <!-- 晨检记录-->
  <div class="container-wrapper">
    <div class="tab-item m-t-20">
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" @click="clickHistory">历史记录</button-icon>
            <button-icon color="plain" @click="gotoExport">导出</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
            tooltip-effect="dark property-details-tooltips"
            empty-text="暂无数据，请查询"
          >
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #operation="{ row }">
                <el-button type="text" size="small" class="ps-red"  :disabled="row.is_sys" @click="clickDel(row)">删除</el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- 历史记录 -->
    <property-details-history-drawer
      :isShow.sync="propertyDetailsHistoryDrawerShow"
      v-if="propertyDetailsHistoryDrawerShow"
    ></property-details-history-drawer>
  </div>
</template>

<script>
import { TABLE_PROPERTY_DETAILS, SEARCH_PROPERTY_DETAILS } from './constants'
import { debounce, deepClone } from '@/utils'
import PropertyDetailsHistoryDrawer from './PropertyDetailsHistoryDrawer.vue'
import * as dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'PropertyDetails',
  mixins: [exportExcel],
  components: {
    PropertyDetailsHistoryDrawer
  },
  data() {
    return {
      isLoading: false,
      tableSetting: deepClone(TABLE_PROPERTY_DETAILS),
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: deepClone(SEARCH_PROPERTY_DETAILS),
      printType: 'PropertyInfo',
      propertyDetailsHistoryDrawerShow: false,
      selectTypeAll: [
        {
          label: '长期投资',
          value: 'long_term_investment'
        },
        {
          label: '设备',
          value: 'device'
        },
        {
          label: '短期投资',
          value: 'short_term_investment'
        },
        {
          label: '货币资金',
          value: 'monetary_funds'
        },
        {
          label: '预付账款',
          value: 'prepayment'
        },
        {
          label: '应收账款',
          value: 'accounts_receivable'
        },
        {
          label: '存货',
          value: 'stock'
        }
      ]
    }
  },
  watch: {
    'searchFormSetting.asset_categorys.value'(val) {
      // 清空asset_types的相关数据
      this.searchFormSetting.asset_types.dataList = []
      this.searchFormSetting.asset_types.value = []

      if (val.length) {
        // 检查是否选择了全部的选项
        let hasFixedAsset = val.includes('fixed')
        let hasCurrentAsset = val.includes('flow')

        if (hasFixedAsset && hasCurrentAsset) {
          // 如果两者都被选中，则显示全部选项
          this.searchFormSetting.asset_types.dataList = this.selectTypeAll
        } else if (hasFixedAsset) {
          // 只选择了固定资产
          this.searchFormSetting.asset_types.dataList = [
            {
              label: '长期投资',
              value: 'long_term_investment'
            },
            {
              label: '设备',
              value: 'device'
            }
          ]
        } else if (hasCurrentAsset) {
          // 只选择了流动资产
          this.searchFormSetting.asset_types.dataList = [
            {
              label: '短期投资',
              value: 'short_term_investment'
            },
            {
              label: '货币资金',
              value: 'monetary_funds'
            },
            {
              label: '预付账款',
              value: 'prepayment'
            },
            {
              label: '应收账款',
              value: 'accounts_receivable'
            },
            {
              label: '存货',
              value: 'stock'
            }
          ]
        }
      }
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getAssetAssetClassifyList()
      this.getDataList()
    },
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time' && key !== 'upload_start') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            if (key === 'upload_start') {
              // 日期归属
              let yearMonth = dayjs(data[key].value[1])
              // 获取下个月的第一天
              let startOfNextMonth = yearMonth.add(1, 'month').startOf('month')
              // 回退一天得到本月的最后一天
              let lastDayOfMonth = startOfNextMonth.subtract(1, 'day')
              params.upload_start_date = data[key].value[0] + '-01'
              params.upload_end_date = lastDayOfMonth.format('YYYY-MM-DD')
            }
            if (key === 'select_time') {
              params.start_date = data[key].value[0]
              params.end_date = data[key].value[1]
            }
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getDataList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundFundSupervisionAssetAssetInfoDetailsSummaryListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        let results = data.results.map(v => {
          v.upload_date = v.upload_date ? dayjs(v.upload_date).format('YYYY年MM月') : ''
          return v
        })
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 资产分类
    async getAssetAssetClassifyList() {
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundFundSupervisionAssetAssetClassifyListPost({
          page: 1,
          page_size: 9999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data.results.length) {
          this.searchFormSetting.asset_classify_ids.dataList = deepClone(res.data.results)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    clickHistory() {
      this.propertyDetailsHistoryDrawerShow = true
    },
    clickDel(itme) {
      this.$confirm(`确定删除该内容？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundFundSupervisionAssetAssetInfoDetailsSummaryDeletePost({
              id: itme.id
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                }
              }
              this.getDataList()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    gotoExport() {
      const option = {
        url: 'apiBackgroundFundSupervisionAssetAssetInfoDetailsSummaryListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
.property-details-tooltips {
  white-space: normal; /* 允许换行 */
  word-break: break-all; /* 长单词自动换行 */
  max-width: 200px; /* 设置最大宽度，超出后换行 */
}
</style>
