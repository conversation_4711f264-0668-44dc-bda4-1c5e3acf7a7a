<template>
  <div class="upgrade-service container-wrapper">
    <refresh-tool :show-refresh="false" />
    <div class="table-wrapper" style="padding: 15px 40px;">
      <el-radio-group v-model="chargeTypeRadio" v-loading="isLoading" @input="gotoRecord">
        <el-radio-button :label="'expansion'" v-if="serviceType === 1">扩容</el-radio-button>
        <el-radio-button :label="'renew'" v-if="serviceType !== 3">续费</el-radio-button>
        <el-radio-button :label="'record'">交易记录</el-radio-button>
      </el-radio-group>
      <upgradeServiceContent
        ref="upgradeServiceContent"
        v-if="chargeTypeRadio !== 'record'"
        :companyInfo="companyInfo"
        :serviceType="serviceType"
        :chargeTypeRadioNum="chargeTypeRadio"
        @refresh="getMerchantInfo"
        @showQRCode="showQRCode"
        >
      </upgradeServiceContent>
      <div v-if="chargeTypeRadio === 'record'">
        <div class="table-header">
          <div class="table-title">交易记录</div>
          <div class="float-r">
            <button-icon color="origin" @click="goExport">导出Excel</button-icon>
          </div>
        </div>
        <div class="table-content">
          <el-table ref="tableView" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
            <table-column  v-for="item in tableSetting" :key="item.key" :col="item">
              <template #realFee="{ row }">
                <div>
                  ￥{{ row.order_status === 'ORDER_SUCCESS' ? realFee(row.real_fee) : realFee(row.origin_fee) }}
                </div>
              </template>
              <template #voucherUrl="{ row }">
                <div>
                  {{ row.order_status === 'ORDER_SUCCESS' || row.pay_method === 'alipay' || row.pay_method === 'wxpay' ? '--' : (row.voucher_url ? '已上传' : '未上传')}}
                </div>
              </template>
              <template #operation="{ row }">
                <el-button v-if="row.pay_method === 'transfer'" type="text" size="small" class="ps-text" @click="showCredentialDialog(row)">{{ !row.voucher_url ? '上传凭证' : '重新上传' }}</el-button>
                <el-button v-if="row.invoice_status !== 3" type="text" size="small" class="ps-text" @click="showInvoiceApplyDialog(row)">{{row.invoice_status === 2 ? '申请发票' : '重新申请'}}</el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <ul class="total m-t-10">
          <li>
            交易总金额:
            <span>￥{{ realFee(total_fee) }}</span>
          </li>
        </ul>
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 对公转账弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'对公转账流程'"
        :visible="flowDialogShow"
        :show-close="false"
        size="55%">
        <div class="p-20">
          <template>
            <div class="dialog-content">
              <div class="dialog-content-item">
                <div class="dialog-content-item-title">
                  1、已生成订单
                </div>
                <div class="dialog-content-item-content">
                  订单号：<span id="dialog-tradeNo">{{ tradeNO }}</span>
                  <span
                    style="margin-left: 20px; color: #02A7F0;"
                    data-clipboard-target="#dialog-tradeNo"
                    id="copy-btn"
                    @click="copyFun"
                  >
                    点此复制
                  </span>
                </div>
              </div>
              <div class="dialog-content-item">
                <div class="dialog-content-item-title">
                  2、线下汇款
                </div>
                <div class="dialog-content-item-content">
                  汇款信息：
                </div>
                <div class="dialog-content-item-content">
                  金额：￥{{ totalPrice }}
                </div>
                <div class="dialog-content-item-content">
                  开户名：广州市派克朴食科技责任有限公司
                </div>
                <div class="dialog-content-item-content">
                  开户行：中国建设银行股份有限公司广州太古汇支行
                </div>
                <div class="dialog-content-item-content">
                  账号：1234567890987654321
                </div>
                <div class="dialog-content-item-content">
                  备注：{{ tradeNO }}
                  <span class="tip">*汇款备注：打款时请务必填写此备注信息（点击上方复制订单号）</span>
                </div>
              </div>
              <div class="dialog-content-item">
                <div class="dialog-content-item-title">
                  3、等待处理
                </div>
                <div class="dialog-content-item-content">
                  <div>汇款后，请前往交易记录处上传汇款凭证，我们将及时为您处理（3~5个工作日）</div>
                  <div>如有任何疑问，请联系客服：4008082098</div>
                </div>
              </div>
            </div>
          </template>
          <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
            <div class="m-r-30">
              <el-button size="small" class="w-100" @click="cancelOrder">取消</el-button>
              <el-button size="small" class="w-100 ps-btn" @click="orderConfirmation">保存</el-button>
            </div>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'扫码支付'"
        :visible="QRcodeShow"
        :show-close="false"
        size="25%">
        <div class="p-20">
          <div>
            <qrcode :value="paymentQRCode" :options="{ width: 300 }" :margin="10" alt />
          </div>
          <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
            <div class="m-r-30">
              <el-button size="small"  class="w-100" @click="handleClose">取消</el-button>
              <el-button size="small" class="w-100 ps-btn" @click="confirmPayment">我已完成付款</el-button>
            </div>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'对公转账流程'"
        :visible="invoiceApplyDialogShow"
        :show-close="false"
        size="30%">
        <div class="p-20">
          <el-form
            :model="invoiceInfoData"
            @submit.native.prevent
            status-icon
            ref="invoiceForm"
            :rules="invoiceInfoData.invoice_type === 1 ? invoiceInfoRules1 : invoiceInfoRules2"
            label-width="80px"
            :label-position="'left'"
            class="invoice-form"
          >
            <div class="invoice-form-title">发票类型：</div>
            <el-form-item>
              <el-radio-group v-model="invoiceInfoData.invoice_type" @change="changeInvoiceType">
                <el-radio :label="1">电子普通发票</el-radio>
                <el-radio :label="2">增值税专用发票</el-radio>
              </el-radio-group>
            </el-form-item>
            <div class="invoice-form-title">公司信息：</div>
            <el-form-item label="发票抬头" v-if="invoiceInfoData.invoice_type === 1" prop="title">
              <el-input placeholder="请填写" v-model="invoiceInfoData.title" class="w-220"></el-input>
            </el-form-item>
            <el-form-item label="客户名称" v-if="invoiceInfoData.invoice_type === 2" prop="customer_name">
              <el-input placeholder="请填写" v-model="invoiceInfoData.customer_name" class="w-220"></el-input>
            </el-form-item>
            <el-form-item label="税号" prop="tax_no">
              <el-input :placeholder="invoiceInfoData.invoice_type === 1 ? '请填写纳税人识别号' : '请填写'" v-model="invoiceInfoData.tax_no" class="w-220"></el-input>
            </el-form-item>
            <el-form-item label="地址" v-if="invoiceInfoData.invoice_type === 2" prop="company_address">
              <el-input placeholder="请填写" v-model="invoiceInfoData.company_address" class="w-220"></el-input>
            </el-form-item>
            <el-form-item label="电话" v-if="invoiceInfoData.invoice_type === 2" prop="company_phone">
              <el-input placeholder="请填写" v-model="invoiceInfoData.company_phone" class="w-220"></el-input>
            </el-form-item>
            <el-form-item label="开户行" v-if="invoiceInfoData.invoice_type === 2" prop="bank_name">
              <el-input placeholder="请填写" v-model="invoiceInfoData.bank_name" class="w-220"></el-input>
            </el-form-item>
            <el-form-item label="账户" v-if="invoiceInfoData.invoice_type === 2" prop="account">
              <el-input placeholder="请填写" v-model="invoiceInfoData.account" class="w-220"></el-input>
            </el-form-item>
            <div class="invoice-form-title">接收信息：</div>
            <el-form-item label="邮箱地址" v-if="invoiceInfoData.invoice_type === 1" prop="email">
              <el-input placeholder="请填写接收发票的邮箱地址" v-model="invoiceInfoData.email" class="w-220"></el-input>
            </el-form-item>
            <el-form-item label="邮寄地址" v-if="invoiceInfoData.invoice_type === 2" prop="mailing_address">
              <el-input placeholder="请填写接收发票的邮寄地址" v-model="invoiceInfoData.mailing_address" class="w-220"></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
            <div class="m-r-30">
              <el-button size="small"  class="w-100" @click="handleClose">取消</el-button>
              <el-button
                :disabled="isLoading"
                size="small"
                class="w-100 ps-btn"
                type="primary"
                @click="submitApply"
              >
                提交申请
              </el-button>
            </div>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'对公转账流程'"
        :visible="uploadCredentialsShow"
        :show-close="false"
        size="35%">
        <div class="p-20">
          <template>
            <div class="upload-dialog-content">
              <div class="content-detail m-b-20">
                <span class="content-title">上传汇款凭证后，我们将及时为您处理（3~5个工作日）</span>
              </div>
              <div class="inline-block upload-w">
                <el-upload
                  v-loading="uploading"
                  class="upload-demo"
                  element-loading-text="上传中"
                  ref="fileUpload"
                  :action="serverUrl"
                  :data="uploadParams"
                  :file-list="fileLists"
                  :on-success="uploadSuccess"
                  :before-upload="beforeFoodImgUpload"
                  :limit="1"
                  :multiple="false"
                  :show-file-list="false"
                  :headers="headersOpts"
                  accept=".jpeg,.jpg,.png,.bmp"
                >
                  <slot>
                    <el-button
                      class="upload-button m-b-20"
                      v-if="!formData.imageList.length"
                      size="small"
                      type="primary"
                    >
                      选择文件
                    </el-button>
                    <el-image
                      class="upload-image m-b-20"
                      v-if="formData.imageList.length"
                      :src="formData.imageList[0]"
                      fit="contain"
                    ></el-image>
                  </slot>
                </el-upload>
              </div>
              <div class="content-detail font-size-12">
                <span class="m-b-5">上传格式：JPG、PNG</span>
                <span>文件大小：不大于5M</span>
              </div>
            </div>
          </template>
          <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
            <div class="m-r-30">
              <el-button size="small"  class="w-100" @click="handleClose">取消</el-button>
              <el-button size="small" class="w-100 ps-btn" @click="submitCredential">确认上传</el-button>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- <dialog-message
      :show.sync="flowDialogShow"
      :title="'对公转账流程'"
      :loading.sync="isLoading"
      @close="handleClose"
      customClass="ps-dialog"
      :width="'1000px'">
      <template>
        <div class="dialog-content">
          <div class="dialog-content-item">
            <div class="dialog-content-item-title">
              1、已生成订单
            </div>
            <div class="dialog-content-item-content">
              订单号：<span id="dialog-tradeNo">{{ tradeNO }}</span>
              <span
                style="margin-left: 20px; color: #02A7F0;"
                data-clipboard-target="#dialog-tradeNo"
                id="copy-btn"
                @click="copyFun"
              >
                点此复制
              </span>
            </div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-content-item-title">
              2、线下汇款
            </div>
            <div class="dialog-content-item-content">
              汇款信息：
            </div>
            <div class="dialog-content-item-content">
              金额：￥{{ totalPrice }}
            </div>
            <div class="dialog-content-item-content">
              开户名：广州市派克朴食科技责任有限公司
            </div>
            <div class="dialog-content-item-content">
              开户行：中国建设银行股份有限公司广州太古汇支行
            </div>
            <div class="dialog-content-item-content">
              账号：1234567890987654321
            </div>
            <div class="dialog-content-item-content">
              备注：{{ tradeNO }}
              <span class="tip">*汇款备注：打款时请务必填写此备注信息（点击上方复制订单号）</span>
            </div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-content-item-title">
              3、等待处理
            </div>
            <div class="dialog-content-item-content">
              <div>汇款后，请前往交易记录处上传汇款凭证，我们将及时为您处理（3~5个工作日）</div>
              <div>如有任何疑问，请联系客服：4008082098</div>
            </div>
          </div>
        </div>
      </template>
      <template #tool>
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
          <el-button type="danger" @click="cancelOrder">取消订单</el-button>
          <el-button class="ps-btn" @click="orderConfirmation">确认下单</el-button>
        </div>
      </template>
    </dialog-message> -->

    <!-- 购买后返显url -->
    <!-- <dialog-message
      :show.sync="QRcodeShow"
      :title="'扫码支付'"
      :loading.sync="isLoading"
      @close="handleClose"
      customClass="ps-dialog"
      :width="'290px'">
      <div>
        <qrcode :value="paymentQRCode" :options="{ width: 250 }" :margin="10" alt />
      </div>
      <template #tool>
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
          <el-button class="ps-btn" @click="confirmPayment">我已完成付款</el-button>
        </div>
      </template>
    </dialog-message> -->

    <!-- 申请发票弹窗 -->
    <!-- <dialog-message
      :show.sync="invoiceApplyDialogShow"
      :title="'申请发票'"
      :loading.sync="isLoading"
      @close="handleClose"
      customClass="ps-dialog"
      :width="'500px'"
    >
      <el-form
        :model="invoiceInfoData"
        @submit.native.prevent
        status-icon
        ref="invoiceForm"
        :rules="invoiceInfoData.invoice_type === 1 ? invoiceInfoRules1 : invoiceInfoRules2"
        label-width="80px"
        :label-position="'left'"
        class="invoice-form"
        inline
      >
        <div class="invoice-form-title">发票类型：</div>
        <el-form-item>
          <el-radio-group v-model="invoiceInfoData.invoice_type" @change="changeInvoiceType">
            <el-radio :label="1">电子普通发票</el-radio>
            <el-radio :label="2">增值税专用发票</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="invoice-form-title">公司信息：</div>
        <el-form-item label="发票抬头" v-if="invoiceInfoData.invoice_type === 1" prop="title">
          <el-input placeholder="请填写" v-model="invoiceInfoData.title"></el-input>
        </el-form-item>
        <el-form-item label="客户名称" v-if="invoiceInfoData.invoice_type === 2" prop="customer_name">
          <el-input placeholder="请填写" v-model="invoiceInfoData.customer_name"></el-input>
        </el-form-item>
        <el-form-item label="税号" prop="tax_no">
          <el-input :placeholder="invoiceInfoData.invoice_type === 1 ? '请填写纳税人识别号' : '请填写'" v-model="invoiceInfoData.tax_no"></el-input>
        </el-form-item>
        <el-form-item label="地址" v-if="invoiceInfoData.invoice_type === 2" prop="company_address">
          <el-input placeholder="请填写" v-model="invoiceInfoData.company_address"></el-input>
        </el-form-item>
        <el-form-item label="电话" v-if="invoiceInfoData.invoice_type === 2" prop="company_phone">
          <el-input placeholder="请填写" v-model="invoiceInfoData.company_phone"></el-input>
        </el-form-item>
        <el-form-item label="开户行" v-if="invoiceInfoData.invoice_type === 2" prop="bank_name">
          <el-input placeholder="请填写" v-model="invoiceInfoData.bank_name"></el-input>
        </el-form-item>
        <el-form-item label="账户" v-if="invoiceInfoData.invoice_type === 2" prop="account">
          <el-input placeholder="请填写" v-model="invoiceInfoData.account"></el-input>
        </el-form-item>
        <div class="invoice-form-title">接收信息：</div>
        <el-form-item label="邮箱地址" v-if="invoiceInfoData.invoice_type === 1" prop="email">
          <el-input placeholder="请填写接收发票的邮箱地址" v-model="invoiceInfoData.email"></el-input>
        </el-form-item>
        <el-form-item label="邮寄地址" v-if="invoiceInfoData.invoice_type === 2" prop="mailing_address">
          <el-input placeholder="请填写接收发票的邮寄地址" v-model="invoiceInfoData.mailing_address"></el-input>
        </el-form-item>
      </el-form>
      <template slot="tool">
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: center;">
          <el-button
            :disabled="isLoading"
            class="ps-btn"
            type="primary"
            @click="submitApply"
          >
            提交申请
          </el-button>
        </div>
      </template>
    </dialog-message> -->

    <!-- 申请凭证弹窗 -->
    <!-- <dialog-message
      width="500px"
      title="上传凭证"
      :show.sync="uploadCredentialsShow"
      customClass="expire-dialog"
      :show-footer="false"
    >
      <template>
        <div class="upload-dialog-content">
          <div class="content-detail m-b-20">
            <span class="content-title">上传汇款凭证后，我们将及时为您处理（3~5个工作日）</span>
          </div>
          <div class="inline-block upload-w">
            <el-upload
              v-loading="uploading"
              class="upload-demo"
              element-loading-text="上传中"
              ref="fileUpload"
              :action="serverUrl"
              :data="uploadParams"
              :file-list="fileLists"
              :on-success="uploadSuccess"
              :before-upload="beforeFoodImgUpload"
              :limit="1"
              :multiple="false"
              :show-file-list="false"
              :headers="headersOpts"
              accept=".jpeg,.jpg,.png,.bmp"
            >
              <slot>
                <el-button
                  class="upload-button m-b-20"
                  v-if="!formData.imageList.length"
                  size="small"
                  type="primary"
                >
                  选择文件
                </el-button>
                <el-image
                  class="upload-image m-b-20"
                  v-if="formData.imageList.length"
                  :src="formData.imageList[0]"
                  fit="contain"
                ></el-image>
              </slot>
            </el-upload>
          </div>
          <div class="content-detail font-size-12">
            <span class="m-b-5">上传格式：JPG、PNG</span>
            <span>文件大小：不大于5M</span>
          </div>
        </div>
      </template>
      <template #tool>
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
          <el-button class="ps-btn" @click="submitCredential">确认上传</el-button>
        </div>
      </template>
    </dialog-message> -->
  </div>
</template>

<script>
import { getToken, getSuffix } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import upgradeServiceContent from './upgradeServiceContent.vue'
import { divide } from '@/utils/constants'
import qrcode from '@chenfengyuan/vue-qrcode'
import dayjs from 'dayjs'
import Clipboard from 'clipboard'
export default {
  name: 'upgradeServive',
  mixins: [exportExcel],
  components: {
    upgradeServiceContent,
    qrcode
  },
  data() {
    return {
      chargeTypeRadio: '',
      isLoading: false,
      companyInfo: {},
      serviceType: 0,
      tableSetting: [
        { label: '订单号', key: 'trade_no' },
        { label: '创建时间', key: 'create_time' },
        { label: '支付时间', key: 'pay_time' },
        { label: '到账时间', key: 'finish_time' },
        { label: '支付金额', key: 'real_fee', type: "slot", slotName: "realFee" },
        { label: '支付方式', key: 'pay_method_alias' },
        { label: '交易类型', key: 'transaction_type_alias' },
        { label: '订单状态', key: 'order_status_alias' },
        { label: '转账凭证', key: 'voucher_url', type: "slot", slotName: "voucherUrl" },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "200" }
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      totalCount: 0,
      total_fee: 0,
      orderId: '',
      invoiceApplyDialogShow: false,
      invoiceInfoData: {
        invoice_type: 1,
        title: '',
        tax_no: '',
        customer_name: '',
        company_address: '',
        company_phone: '',
        bank_name: '',
        account: '',
        email: '',
        mailing_address: ''
      },
      invoiceInfoRules1: {
        title: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        tax_no: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        email: [
          { required: true, message: '此项不能为空', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
            message: '请输入正确的邮箱地址',
            trigger: 'blur'
          }
        ]
      },
      invoiceInfoRules2: {
        tax_no: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        customer_name: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        company_address: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        company_phone: [
          { required: true, message: '此项不能为空', trigger: 'blur' },
          {
            pattern: /^[0-9]*$/,
            message: '只能输入数字',
            trigger: 'change'
          }
        ],
        bank_name: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        account: [{ required: true, message: '此项不能为空', trigger: 'blur' }],
        mailing_address: [{ required: true, message: '此项不能为空', trigger: 'blur' }]
      },
      uploadCredentialsShow: false,
      uploading: false,
      serverUrl: '/api/background/file/upload',
      uploadParams: {
        prefix: 'be_on_loan_img'
      },
      fileLists: [],
      headersOpts: {
        TOKEN: getToken()
      },
      formData: {
        imageList: []
      },
      // 申请发票相关
      invoiceList: [],
      sameInvoiceInfo: [],
      isAdd: false,
      invoiceStatus: 0,
      paymentQRCode: '',
      QRcodeShow: false,
      tradeNO: '', // 暂存生成的订单详情
      clauseShow: false,
      flowDialogShow: false,
      totalPrice: 0 // 实付金额
    }
  },
  created() {
    this.getMerchantInfo()
    // this.initLoad()
  },
  watch: {
    serviceType(newVal) {
      if (this.serviceType === 1) {
        this.chargeTypeRadio = 'expansion'
      } else if (this.serviceType === 2) {
        this.chargeTypeRadio = 'renew'
      } else {
        this.chargeTypeRadio = 'record'
      }
    }
  },
  computed: {
    realFee() {
      return d => {
        return divide(d)
      }
    }
  },
  methods: {
    initLoad() {
      this.getChargeOrderList()
      this.getInvoiceList()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.isLoading = true
      this.pageSize = val
      this.page = 1
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.isLoading = true
      this.page = val
      this.pageSize = 10
      this.initLoad()
    },
    getMerchantInfo() {
      this.isLoading = true
      this.$apis.apiBackgroundTollBackgroundTollGetSettingsPost()
        .then(res => {
          if (res.code === 0) {
            this.isLoading = false
            this.companyInfo = res.data
            this.serviceType = res.data.toll_type
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    // 导出
    goExport() {
      const option = {
        type: 'Transaction',
        url: 'apiBackgroundTollBackgroundTollOrderListExportPost'
      }
      this.exportHandle(option)
    },
    getChargeOrderList() {
      this.isLoading = true
      let params = {
        page: this.page,
        page_size: this.pageSize
      }
      this.$apis.apiBackgroundTollBackgroundTollOrderListPost(params)
        .then(res => {
          if (res.code === 0) {
            res.data.results.map(item => {
              if (item.pay_time) {
                item.pay_time = dayjs(item.pay_time).format('YYYY-MM-DD HH:mm:ss')
              }
              if (item.finish_time) {
                item.finish_time = dayjs(item.finish_time).format('YYYY-MM-DD HH:mm:ss')
              }
              return item
            })
            this.tableData = res.data.results
            this.totalCount = res.data.count
            this.total_fee = res.data.total_fee
          } else {
            this.$message.error(res.msg)
          }
        })
      this.isLoading = false
    },
    handleClose() {
      this.flowDialogShow = false
      this.clauseShow = false
      this.QRcodeShow = false
      this.invoiceApplyDialogShow = false
      this.uploadCredentialsShow = false
    },
    // 图片上传之前
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 5
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      this.uploading = true
    },
    // 图片上传成功以后
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res.code === 0) {
        this.fileLists = fileList
        this.formData.imageList = [res.data.public_url]
      } else {
        this.$message.error(res.msg)
      }
    },
    submitCredential() {
      if (!this.formData.imageList[0]) return this.$message.error('请先选择文件')
      this.$apis.apiBackgroundTollBackgroundTollOrderAddVoucherUrlPost({
        id: this.orderId,
        url: this.formData.imageList[0]
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('上传成功')
          this.uploadCredentialsShow = false
          this.formData.imageList = []
          this.fileLists = []
          this.isLoading = true
          this.initLoad()
        } else {
          this.$message.error(res.msg)
          this.uploadCredentialsShow = false
        }
      })
      this.isLoading = false
    },
    showCredentialDialog(row) {
      this.orderId = row.id
      this.uploadCredentialsShow = true
    },
    showInvoiceApplyDialog(row) {
      // 显示弹窗前获取一遍已有的发票信息，是否有订单id相同的
      this.sameInvoiceInfo = this.invoiceList.filter(item => item.id === row.invoice_info)
      if (this.sameInvoiceInfo.length !== 0) {
        this.invoiceInfoData = { ...this.sameInvoiceInfo[0] }
      }
      this.orderId = row.id
      this.invoiceStatus = row.invoice_status
      this.invoiceApplyDialogShow = true
    },
    changeInvoiceType() {
      this.$refs.invoiceForm.clearValidate()
    },
    // 获取发票列表
    getInvoiceList() {
      this.$apis.apiBackgroundTollBackgroundInvoiceInfoListPost()
        .then(res => {
          if (res.code === 0) {
            this.invoiceList = res.data.results
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    async submitApply() {
      // 先新增
      this.$refs.invoiceForm.validate(async (valid) => {
        if (valid) {
          let tempObj = {}
          if (this.invoiceInfoData.invoice_type === 1) {
            tempObj = {
              invoice_type: 1,
              title: this.invoiceInfoData.title,
              tax_no: this.invoiceInfoData.tax_no,
              email: this.invoiceInfoData.email
            }
          } else {
            tempObj = {
              invoice_type: 2,
              tax_no: this.invoiceInfoData.tax_no,
              customer_name: this.invoiceInfoData.customer_name,
              company_address: this.invoiceInfoData.company_address,
              company_phone: this.invoiceInfoData.company_phone,
              bank_name: this.invoiceInfoData.bank_name,
              account: this.invoiceInfoData.account,
              mailing_address: this.invoiceInfoData.mailing_address
            }
          }
          // 如果有
          if (this.sameInvoiceInfo.length !== 0) {
            let modifyParams = {
              id: this.sameInvoiceInfo[0].id,
              ...tempObj
            }
            this.modifyInvoiceInfo(modifyParams)
          } else {
            // 如果没有则新增后申请
            let newAddInvoiceInfo = await this.invoiceInfoAdd(tempObj)
            let addParams = {
              id: this.orderId,
              invoice_id: newAddInvoiceInfo.data.id
            }
            this.addInvoiceInfo(addParams)
          }
          this.$refs.invoiceForm.resetFields()
        } else {
          return this.$message.error('请确认信息是否填写正确')
        }
      })

      // 分割线
      // this.$refs.invoiceForm.validate((valid) => {
      //   if (valid) {
      //     let tempObj = {}
      //     if (this.invoiceInfoData.invoice_type === 1) {
      //       tempObj = {
      //         invoice_type: 1,
      //         title: this.invoiceInfoData.title,
      //         tax_no: this.invoiceInfoData.tax_no,
      //         email: this.invoiceInfoData.email
      //       }
      //     } else {
      //       tempObj = {
      //         invoice_type: 2,
      //         tax_no: this.invoiceInfoData.tax_no,
      //         customer_name: this.invoiceInfoData.customer_name,
      //         company_address: this.invoiceInfoData.company_address,
      //         company_phone: this.invoiceInfoData.company_phone,
      //         bank_name: this.invoiceInfoData.bank_name,
      //         account: this.invoiceInfoData.account,
      //         mailing_address: this.invoiceInfoData.mailing_address
      //       }
      //     }
      //     let flag = false // flag为true说明现有的发票信息表有该信息
      //     this.invoiceList.forEach(item => {
      //       if (this.invoiceInfoData.tax_no === item.id) {
      //         tempObj.id = item.id
      //         flag = true
      //       }
      //     })
      //     // 发票信息新增
      //     let params = {}
      //     // 先看有无
      //     if (flag) {
      //       // 看发票状态
      //       if (this.invoiceStatus === 2) {
      //         // 若有，但发票类型不同则直接申请
      //         params = {
      //           id: this.orderId,
      //           invoice_id: tempObj.id
      //         }
      //         this.addInvoiceInfo(params)
      //         this.$refs.invoiceForm.resetFields()
      //       } else {
      //         // 否则就修改
      //         params = {
      //           id: this.orderId,
      //           ...tempObj
      //         }
      //         this.modifyInvoiceInfo(params)
      //         this.$refs.invoiceForm.resetFields()
      //       }
      //     } else {
      //       // 若没有，则先新增再申请
      //       this.invoiceInfoAdd(tempObj)
      //       this.$refs.invoiceForm.resetFields()
      //     }
      //   } else {
      //     return this.$message.error('请确认信息是否填写正确')
      //   }
      // })
    },
    // 新增发票信息
    invoiceInfoAdd(params) {
      return new Promise((resolve, reject) => {
        this.$apis.apiBackgroundTollBackgroundInvoiceInfoAddPost(params)
          .then(data => {
            resolve(data)
          }).catch(error => {
            reject(error)
          })
      })
    },
    // 申请发票信息
    addInvoiceInfo(params) {
      this.$apis.apiBackgroundTollBackgroundTollOrderAddInvoiceInfoPost(params)
        .then(res => {
          if (res.code === 0) {
            this.$message.success('申请发票成功')
            this.invoiceApplyDialogShow = false
            this.initLoad()
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    // 修改发票信息
    modifyInvoiceInfo(params) {
      this.$apis.apiBackgroundTollBackgroundInvoiceInfoModifyPost(params)
        .then(res => {
          if (res.code === 0) {
            this.$message.success('申请发票成功')
            this.invoiceApplyDialogShow = false
            this.initLoad()
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    // 返显二维码
    showQRCode(e, price) {
      if (e.url) {
        this.QRcodeShow = true
        this.paymentQRCode = e.url
      } else {
        this.flowDialogShow = true
        this.totalPrice = price
      }
      this.tradeNO = e.trade_no
    },
    // 查询该订单是否完成
    confirmPayment() {
      this.isLoading = true
      this.$apis.apiBackgroundTollBackgroundTollOrderListPost({
        trade_no: this.tradeNO
      }).then(res => {
        if (res.code === 0) {
          switch (res.data.results[0].order_status) {
            case 'ORDER_SUCCESS':
              this.$message.success('交易成功')
              break
            case 'ORDER_FAILED':
              this.$message.error('交易失败，请重试')
              break
            case 'ORDER_PAYING':
              this.$message.error('交易失败，该订单待支付，请重试')
              break
            case 'ORDER_WATTING_PAY':
              this.$message.error('该订单等待支付中，请稍等')
              break
            case 'ORDER_CLOSE':
              this.$message.error('该订单已关闭，请重新购买')
              break
            case 'ORDER_TIME_OUT':
              this.$message.error('该订单已过期')
              break
          }
        } else {
          this.$message.error(res.msg)
        }
        this.QRcodeShow = false
        this.getMerchantInfo()
        this.initLoad()
      })
    },
    // 取消对公转账订单
    cancelOrder() {
      this.$apis.apiBackgroundAdminBackgroundTollOrderCancelPost({
        trade_no: this.tradeNO
      }).then(res => {
        if (res.code === 0) {
          this.flowDialogShow = false
          this.$message.success('已取消本次订单')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 一键复制
    copyFun() {
      let clipboard = new Clipboard('#copy-btn') // 获取点击按钮的元素
      /* 注意此事件监听是异步的   */
      clipboard.on('success', e => {
        console.info('Text:', e.text)
        e.clearSelection()
        // 释放内存
        clipboard.destroy()
        return this.$message.success('复制成功')
      })
      // 复制失败
      clipboard.on('error', e => {
        console.error('Action:', e.action)
        console.error('Trigger:', e.trigger)
        // 释放内存
        clipboard.destroy()
        return this.$message.error('该浏览器不支持复制')
      })
    },
    orderConfirmation() {
      this.flowDialogShow = false
      this.$message.success('下单成功')
    },
    gotoRecord() {
      if (this.chargeTypeRadio === 'record') {
        this.initLoad()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice-form {
  padding: 0px 20px;
  &-title {
    font-weight: bold;
    color: black;
    font-size: 14px;
  }
}
.upload-dialog-content {
  padding: 0 20px 0 20px;
  .content-detail {
    display: flex;
    flex-direction: column;
    .ps-btn {
      margin-right: 5px;
    }
  }
}
.dialog-content {
  padding: 0px 30px;
  &-item {
    margin-bottom: 10px;
    &-title {
      font-size: 18px;
      color: black;
    }
    &-content {
      margin: 10px 30px;
    }
  }
  .tip {
    float: right;
    color: #F56C6C;
  }
}

</style>
