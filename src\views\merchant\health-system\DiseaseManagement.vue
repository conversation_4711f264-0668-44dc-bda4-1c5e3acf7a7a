<template>
  <div class="disease container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div id="classification-container">
      <!-- 疾病列表 start -->
      <div class="disease-tree p-t-20 p-b-20" v-loading="diseaseLoading">
        <div>
          <div class="p-l-20 p-b-20 p-r-20">
            <el-button class="ps-origin-btn m-b-20" @click="clickAddDiseaseName('add')" v-permission="['background_healthy.disease.add']">
              添加疾病
            </el-button>
            <el-input
              v-model="diseaseName"
              placeholder="输入关键词搜索"
              class="tree-search ps-input"
              @input="searchHandle"
            ></el-input>
          </div>
          <el-scrollbar wrap-class="group-scrollbar-wrapper">
            <ul class="infinite-list">
              <li v-for="(item, index) in diseaseList" class="" :key="index">
                <div class="primary-disease" :class="{'active': activeIndex === index}" @click="clickDiseaseName(item, index)">
                  <div class="disease-name ellipsis">{{ item.name }}</div>
                  <el-popover
                    v-if="!item.is_admin"
                    placement="bottom-start"
                    ref="popoverRef"
                    trigger="hover"
                    popper-class="popper-wrapp"
                  >
                    <div class="popper-button">
                      <div>
                        <el-button
                          type="text"
                          size="small"
                          class="ps-text"
                          @click="clickAddDiseaseName('modify', item)"
                          v-permission="['background_healthy.disease.modify']"
                        >
                          重命名
                        </el-button>
                      </div>
                      <div>
                        <el-button
                          type="text"
                          size="small"
                          class="ps-red"
                          @click="deleteDiseaseHandler(item)"
                          v-permission="['background_healthy.disease.delete']"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                    <div slot="reference">
                      <i slot="reference" class="el-icon-more tree-icon"></i>
                    </div>
                  </el-popover>
                </div>
              </li>
            </ul>
          </el-scrollbar>
        </div>
      </div>
      <!-- 疾病列表 end -->
      <!-- 疾病详情 start -->
      <div class="disease-data p-20" v-loading="diseaseDetailsLoading">
        <el-form
        :model="diseaseDetails"
        @submit.native.prevent
        status-icon
        ref="diseaseForm"
        :rules="dialogDiseaseRules"
        v-loading="diseaseDetailsLoading"
        label-width="80px"
      >
        <div class="m-b-20">选择标签</div>
        <el-form-item v-for="label in labelList" :key="label.key" :label="label.name">
          <el-button class="ps-origin-btn m-r-10" size="small" @click="showRightDialogHandle(label.type, diseaseDetails[label.key])" :disabled="diseaseDetails.is_admin || !allPermissions.includes('background_healthy.disease.modify')">添加</el-button>
          <el-tag
            v-for="(labelItem, labelIndex) in diseaseDetails[label.key]"
            :key="labelIndex"
            class="m-r-5 collapse-data"
            size="medium"
            effect="plain"
            type="info"
            color="#fff"
            :closable="!diseaseDetails.is_admin"
            @close="deleteLableHandler(label.type, labelItem, diseaseDetails[label.key], labelIndex)"
          >
            {{ labelItem.label_name }}
          </el-tag>
        </el-form-item>
        <div class="m-t-60 m-b-20">
          <span>营养素推荐</span>
          <el-button class="ps-origin-btn float-r" size="small" @click="showRightDialogHandle('addNutrition')" :disabled="diseaseDetails.is_admin || !allPermissions.includes('background_healthy.disease.modify')">添加</el-button>
        </div>
        <!-- table start -->
        <el-table
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="showRightDialogHandle('modifyNutrition', row)"
                :disabled="diseaseDetails.is_admin"
              >
                编辑
              </el-button>
              <span style="margin: 0 10px; color: #e2e8f0">|</span>
              <el-button
                type="text"
                size="small"
                class="ps-warn-text"
                @click="deleteNutritionHandler('single', row)"
                :disabled="diseaseDetails.is_admin"
              >
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </el-form>
      </div>
    </div>
    <!-- 疾病详情 end -->

    <div class="ps-el-drawer">
      <el-drawer
        :title="dialogDiseaseTitle"
        v-if="showDialogDisease"
        :visible="showDialogDisease"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form
            :model="dialogDiseaseForm"
            @submit.native.prevent
            status-icon
            ref="dialogDiseaseForm"
            :rules="dialogDiseaseRules"
            v-loading="dialogDiseaseFormLoading"
            label-width="80px"
          >
            <el-form-item label="疾病名称" prop="name">
              <el-input
                v-model="dialogDiseaseForm.name"
                :maxlength="10"
                class="ps-input"
                style="width: 190px"
              ></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button @click="showDialogDisease = false">取 消</el-button>
            <el-button
              class="ps-btn"
              type="primary"
              :loading="dialogDiseaseFormLoading"
              @click="determineDiseaseDialog"
            >
              确 定
        </el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- <el-dialog
      v-if="showDialogDisease"
      :title="dialogDiseaseTitle"
      :visible.sync="showDialogDisease"
      width="400px"
      custom-class="ps-dialog dialogDisease"
    >
      <el-form
        :model="dialogDiseaseForm"
        @submit.native.prevent
        status-icon
        ref="dialogDiseaseForm"
        :rules="dialogDiseaseRules"
        v-loading="dialogDiseaseFormLoading"
        label-width="80px"
      >
        <el-form-item label="疾病名称" prop="name">
          <el-input
            v-model="dialogDiseaseForm.name"
            :maxlength="10"
            class="ps-input"
            style="width: 190px"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDialogDisease = false">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          :loading="dialogDiseaseFormLoading"
          @click="determineDiseaseDialog"
        >
          确 定
        </el-button>
      </span>
    </el-dialog> -->
    <!--  -->
    <disease-dialog v-model="showRightDialog" :type="rightDialogType" :info-data="rightDialogInfo" :nutrition-list="nutritionData" @confirmdialog="confirmdialogHandle" />
  </div>
</template>

<script>
import { debounce, to, deepClone } from '@/utils'
import DiseaseDialog from './components/DiseaseDialog.vue'
import { NUTRITION_KEY } from '@/constants'
import { mapGetters } from 'vuex'

export default {
  name: 'DiseaseManagement',
  props: {},
  components: { DiseaseDialog },
  // mixins: [activatedLoadData],
  data() {
    var diseaseNumberValidate = (rule, value, callback) => {
      let diseaseNumberReg = /^\d{1,4}$/
      if (value && !diseaseNumberReg.test(value)) {
        callback(new Error('请输入正确的疾病编号,8位数字'))
      } else {
        callback()
      }
    }

    return {
      activeIndex: 0,
      diseaseInfo: {}, // 单条数据
      diseaseName: '', // 疾病名称
      diseaseList: [], // 左侧疾病名称数据
      dialogDiseaseTitle: '',
      dialogDiseaseType: '',
      showDialogDisease: false,
      dialogDiseaseFormLoading: false,
      dialogDiseaseForm: { // 疾病弹窗
        name: ''
      },
      dialogDiseaseRules: {
        name: [{ required: true, message: '请输入疾病名称', trigger: ['blur', 'change'] }],
        disease_number: [
          { required: false, validator: diseaseNumberValidate, trigger: ['blur', 'change'] }
        ]
        // color: [{ required: true, trigger: ['blur', 'change'] }]
      },
      dialogDiseaseInfo: {},
      labelList: [
        { name: '少食', key: 'suitable_label', type: 'suitable' },
        { name: '禁食', key: 'not_recommend_label', type: 'not_recommend' },
        { name: '推荐', key: 'recommend_label', type: 'recommend' }
      ],
      diseaseDetails: {
        name: "",
        nutrition_control: {},
        suitable_label: [],
        not_recommend_label: [],
        recommend_label: []
      },
      diseaseDetailsLoading: false, // 右侧加载
      diseaseLoading: false, // 左侧加载
      tableSettings: [
        { label: '营养素名称', key: 'name' },
        { label: '推荐范围', key: 'ranges' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      tableData: [],
      showRightDialog: false,
      rightDialogType: '',
      rightDialogInfo: {},
      nutritionKeyList: {},
      allNutrition: [],
      nutritionData: []
    }
  },
  created() {
    // 删除前4个营养
    // this.nutrition_key = deepClone(NUTRITION_KEY.splice(4))
    deepClone(NUTRITION_KEY).forEach(v => {
      this.nutritionKeyList[v.key] = v
    })
    this.initLoad()
  },
  computed: {
    ...mapGetters(['allPermissions'])
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDiseaseList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 获取疾病列表
    async getDiseaseList() {
      this.diseaseLoading = true
      let params = {
        page: 1,
        page_size: 9999
      }
      if (this.diseaseName) {
        params.name = this.diseaseName
      }
      const [err, res] = await to(this.$apis.apiBackgroundHealthyDiseaseListPost(params))
      this.diseaseLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.diseaseList = res.data.results
        if (res.data.results.length) {
          this.activeIndex = 0
          this.diseaseInfo = res.data.results[0]
          this.getDiseaseDetails()
        } else {
          // 初始化
          this.initFormatData({})
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增疾病名称
    clickAddDiseaseName(type, row) {
      this.dialogDiseaseType = type
      this.dialogDiseaseForm = {
        name: type === 'modify' ? row.name : ''
      }
      if (type === 'modify') {
        this.dialogDiseaseTitle = '修改'
        this.dialogDiseaseInfo = row
      } else {
        this.dialogDiseaseTitle = '新增'
        this.dialogDiseaseInfo = {}
      }
      this.showDialogDisease = true
    },
    deleteDiseaseHandler(row) {
      this.$confirm(`删除后数据不可恢复，确定要删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogDiseaseFormLoading) return this.$message.error('请勿重复提交！')
            this.dialogDiseaseFormLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundHealthyDiseaseDeletePost({
                id: row.id
              })
            )
            this.dialogDiseaseFormLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getDiseaseList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    determineDiseaseDialog() {
      this.$refs.dialogDiseaseForm.validate(valid => {
        if (valid) {
          // disease_number 如果没填 就随机生成8位数
          let params = {
            name: this.dialogDiseaseForm.name
          }
          // color colorIndex 如果是0 就传空
          if (this.dialogDiseaseType === 'add') {
            this.getDiseaseAdd(params)
          } else {
            this.getDiseaseModify({ ...params, id: this.dialogDiseaseInfo.id })
          }
        }
      })
    },
    async getDiseaseAdd(params) {
      this.dialogDiseaseFormLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundHealthyDiseaseAddPost(params))
      this.dialogDiseaseFormLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogDisease = false
        this.getDiseaseList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async getDiseaseModify(params) {
      this.dialogDiseaseFormLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundHealthyDiseaseModifyPost(params))
      this.dialogDiseaseFormLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogDisease = false
        this.getDiseaseList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 右侧数据
    async getDiseaseDetails() {
      this.diseaseDetailsLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyDiseaseDetailsPost({
          id: this.diseaseInfo.id
        })
      )
      this.diseaseDetailsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.diseaseDetails = {
          disease_id: this.diseaseInfo.id,
          is_admin: this.diseaseInfo.is_admin,
          ...res.data
        }
        this.initFormatData(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    //  格式化初始数据
    initFormatData(data) {
      this.allNutrition = []
      this.tableData = data.nutrition_control.map(v => {
        this.allNutrition.push(v.key)
        v.name = this.nutritionKeyList[v.key].name
        v.ranges = `${v.range_one}~${v.range_two}${this.nutritionKeyList[v.key].unit}`
        return v
      })
    },
    clickDiseaseName(row, index) {
      this.activeIndex = index
      this.diseaseInfo = row
      this.getDiseaseDetails()
    },
    // 右侧详情数据
    async getDiseaseSaveSetting() {
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyDiseaseSaveSettingPost(this.formatData())
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.getDiseaseDetails()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除标签
    deleteLableHandler(type, row) {
      this.$confirm(`确定删除该标签吗？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogDiseaseFormLoading) return this.$message.error('请勿重复提交！')
            this.dialogDiseaseFormLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundHealthyDiseaseDeleteLabelPost({
                id: this.diseaseInfo.id,
                label_ids: [row.id],
                label_type: type
              })
            )
            this.dialogDiseaseFormLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getDiseaseDetails()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 删除标签
    deleteNutritionHandler(type, row) {
      this.$confirm(`删除后数据不可恢复，确定要删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogDiseaseFormLoading) return this.$message.error('请勿重复提交！')
            this.dialogDiseaseFormLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundHealthyDiseaseDeleteNutritionControlPost({
                id: this.diseaseInfo.id,
                nutrition_key: row.key
              })
            )
            this.dialogDiseaseFormLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getDiseaseDetails()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 显示详情添加/编辑弹窗
    showRightDialogHandle(type, data) {
      this.rightDialogType = type
      console.log(2222, data)
      if (type === 'addNutrition' || type === 'modifyNutrition') {
        let disabledNutrition = []
        if (data) {
          disabledNutrition = deepClone(this.allNutrition).splice(this.allNutrition.indexOf(data.key), 1)
        } else {
          disabledNutrition = deepClone(this.allNutrition)
        }
        this.nutritionData = deepClone(NUTRITION_KEY).map(v => {
          v.disabled = disabledNutrition.includes(v.key)
          return v
        })
      }
      if (data) {
        if (type === 'modifyNutrition') {
          this.rightDialogInfo = { ...deepClone(data), disease_id: this.diseaseInfo.id }
        } else {
          this.rightDialogInfo = deepClone(this.diseaseDetails)
        }
      } else {
        this.rightDialogInfo = {
          disease_id: this.diseaseInfo.id
        }
      }
      this.showRightDialog = true
    },
    // 右侧弹窗确认事件
    confirmdialogHandle() {
      this.showRightDialog = false
      this.rightDialogType = ''
      this.getDiseaseDetails()
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
#classification-container {
  display: flex;
  position: relative;
  .group-scrollbar-wrapper{
    height: calc(100vh - 350px);
  }
  .disease-tree {
    max-width: 300px;
    background-color: #f8f9fa;
    margin-right: 20px;
    border-radius: 12px;
    .disease-name{
      max-width: 80%;
    }
    .el-icon-more{
      transform: rotate(90deg);
      color: #a1a1a1;
    }
    .active{
      background-color: #eef0f7;
      border-radius: 4px;
    }
  }
  .disease-title-wrapp {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .disease-data {
    flex: 1;
    background-color: #f8f9fa;
    border-radius: 12px;
  }
  .infinite-list {
    .primary-disease {
      padding: 6px 20px;
      margin: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 32px;
      cursor: pointer;
    }
  }
  .nutrition-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .nutrition-tips {
    font-size: 14px;
    color: #7f7f7f;
    padding-left: 10px;
  }
}
.dialogDisease {
  .color-block {
    width: 30px;
    height: 30px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #f2f2f2;
    .el-icon-check {
      color: #fff;
      font-size: 22px;
    }
  }
}

.popper-wrapp {
  min-width: 80px !important;
  .popper-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .ps-red {
      color: red !important;
    }
  }
}
</style>
