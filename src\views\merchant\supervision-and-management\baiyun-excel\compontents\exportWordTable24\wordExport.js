import { saveAs } from 'file-saver'
import {
  Document,
  Paragraph,
  TextRun,
  Table,
  TableRow,
  TableCell,
  WidthType,
  AlignmentType,
  // HeadingLevel,
  BorderStyle,
  Packer,
  ImageRun
} from 'docx'
import dayjs from 'dayjs'

export const exportMeetingRecord = async data => {
  // 创建文档
  const doc = new Document({
    sections: [
      {
        properties: {
          page: {
            margin: {
              top: 700,
              right: 700,
              bottom: 700,
              left: 700
            }
          }
        },
        children: await createDocumentContent(data) // 改为异步
      }
    ]
  })

  // 生成并下载文档
  Packer.toBlob(doc).then(blob => {
    saveAs(blob, `${data.schoolName}食堂每月食品安全调度会议纪要.docx`)
  })
}

async function createDocumentContent(data) {
  const children = []

  // 主表格
  children.push(createMainTable(data))

  // 图片占位区
  children.push(
    new Paragraph({
      alignment: AlignmentType.CENTER,
      children: [
        new TextRun({
          text: '粘贴校长/法人每月不少于一次察看明厨亮灶的工作照片',
          bold: true
        })
      ]
    })
  )

  // 处理图片 - 横向排列
  if (data.fileList && data.fileList.length > 0) {
    const maxWidth = 800 // 页面可用宽度(根据页面边距调整)
    const imgWidth = 200 // 单张图片宽度
    const imgHeight = 150 // 单张图片高度
    const imgMargin = 20 // 图片间距

    let currentLineWidth = 0
    let currentLineChildren = []

    for (const url of data.fileList) {
      try {
        const response = await fetch(url)
        const arrayBuffer = await response.arrayBuffer()

        // 检查当前行是否还能放下这张图片
        if (currentLineWidth + imgWidth > maxWidth && currentLineChildren.length > 0) {
          // 换行
          children.push(
            new Paragraph({
              alignment: AlignmentType.LEFT,
              children: currentLineChildren
            })
          )
          currentLineChildren = []
          currentLineWidth = 0
        }

        // 添加图片到当前行
        currentLineChildren.push(
          new ImageRun(
            {
              data: arrayBuffer,
              transformation: {
                width: imgWidth,
                height: imgHeight
              }
            },
            new TextRun({
              text: ' ', // 图片间添加空格
              size: imgMargin // 使用字体大小模拟间距
            })
          )
        )
        currentLineWidth += imgWidth + imgMargin
      } catch (error) {
        console.error('图片加载失败:', url, error)
      }
    }

    // 添加最后一行
    if (currentLineChildren.length > 0) {
      children.push(
        new Paragraph({
          alignment: AlignmentType.LEFT,
          children: currentLineChildren
        })
      )
    }
  } else {
    children.push(
      new Paragraph({
        alignment: AlignmentType.CENTER,
        children: [new TextRun('（无照片）')]
      })
    )
  }

  // 页脚信息
  children.push(...createFooterContent(data))

  return children
}

function createMainTable(data) {
  const meetingDate = dayjs(data.meetingDate)

  return new Table({
    width: { size: 100, type: WidthType.PERCENTAGE },
    borders: {
      top: { style: BorderStyle.SINGLE, size: 4 },
      bottom: { style: BorderStyle.SINGLE, size: 4 },
      left: { style: BorderStyle.SINGLE, size: 4 },
      right: { style: BorderStyle.SINGLE, size: 4 },
      insideHorizontal: { style: BorderStyle.SINGLE, size: 4 },
      insideVertical: { style: BorderStyle.SINGLE, size: 4 }
    },
    rows: [
      // 标题行
      new TableRow({
        children: [
          new TableCell({
            columnSpan: 4,
            children: [
              new Paragraph({
                alignment: AlignmentType.CENTER,
                children: [
                  new TextRun({
                    text: '学校食堂每月食品安全调度会议纪要',
                    bold: true,
                    size: 28
                  })
                ]
              })
            ]
          })
        ]
      }),

      // 记录编号
      new TableRow({
        children: [
          new TableCell({
            columnSpan: 4,
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: '记录编号：',
                    bold: true
                  }),
                  new TextRun(data.recordNumber)
                ]
              })
            ]
          })
        ]
      }),

      // 会议名称和日期
      new TableRow({
        children: [
          new TableCell({
            width: { size: 25, type: WidthType.PERCENTAGE },
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: '会议名称:',
                    bold: true
                  })
                ]
              })
            ]
          }),
          new TableCell({
            width: { size: 25, type: WidthType.PERCENTAGE },
            children: [
              new Paragraph({
                alignment: AlignmentType.CENTER,
                children: [
                  new TextRun({
                    text: `${meetingDate.format('YYYY年MM月')}`,
                    bold: true
                  }),
                  new TextRun({
                    text: '\n食品安全调度会议纪要',
                    break: 1
                  })
                ]
              })
            ]
          }),
          new TableCell({
            width: { size: 25, type: WidthType.PERCENTAGE },
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: '主持人:',
                    bold: true
                  })
                ]
              })
            ]
          }),
          new TableCell({
            width: { size: 25, type: WidthType.PERCENTAGE },
            children: [
              new Paragraph({
                alignment: AlignmentType.CENTER,
                children: [
                  new TextRun({
                    text: `(${data.host})`,
                    bold: true
                  })
                ]
              })
            ]
          })
        ]
      }),

      // 会议日期和地点
      new TableRow({
        children: [
          new TableCell({
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: '会议日期:',
                    bold: true
                  })
                ]
              })
            ]
          }),
          new TableCell({
            children: [
              new Paragraph({
                alignment: AlignmentType.CENTER,
                children: [
                  new TextRun({
                    text: `${meetingDate.format('YYYY年MM月DD日')}`,
                    bold: true
                  })
                ]
              })
            ]
          }),
          new TableCell({
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: '会议地点:',
                    bold: true
                  })
                ]
              })
            ]
          }),
          new TableCell({
            children: [
              new Paragraph({
                children: [new TextRun(data.meetingPlace)]
              })
            ]
          })
        ]
      }),

      // 出席人员
      new TableRow({
        children: [
          new TableCell({
            columnSpan: 4,
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: '出席人员:',
                    bold: true
                  })
                ]
              }),
              new Paragraph({
                text: '(包括且不限于食品安全总监、食品安全员、食品监督员)'
              }),
              new Paragraph({
                text: data.attendees.join('、')
              })
            ]
          })
        ]
      }),

      // 会议内容
      new TableRow({
        children: [
          new TableCell({
            columnSpan: 4,
            children: [
              // 主标题
              new Paragraph({
                spacing: { after: 200 },
                children: [
                  new TextRun({
                    text: '会议内容记录：',
                    bold: true,
                    size: 22
                  })
                ]
              }),

              // 一级标题
              new Paragraph({
                spacing: { before: 100, after: 100 },
                children: [
                  new TextRun({
                    text: '一、食品安全管理工作情况汇报',
                    bold: true,
                    size: 22
                  })
                ]
              }),

              // 1. 互联网+明厨亮灶
              new Paragraph({
                indent: { left: 200 },
                spacing: { before: 80, after: 60 },
                children: [
                  new TextRun({
                    text: '1. "互联网+明厨亮灶"智慧系统发现的问题及整改落实情况',
                    bold: true
                  })
                ]
              }),
              ...data.internetIssues.map(
                issue =>
                  new Paragraph({
                    indent: { left: 400, hanging: 300 },
                    spacing: { line: 300 },
                    children: [
                      new TextRun({
                        text: `${issue.description} - 整改措施: ${issue.solution}`
                      })
                    ]
                  })
              ),

              // 2. 食品原料验收情况
              new Paragraph({
                indent: { left: 200 },
                spacing: { before: 80, after: 60 },
                children: [
                  new TextRun({
                    text: '2. 食品原料验收情况：',
                    bold: true
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `原料：本月到货总批数 ${data.materialStats.totalBatches} 批次，`
                  }),
                  new TextRun({
                    text: `合格 ${data.materialStats.qualifiedBatches} 批次，`,
                    bold: true
                  }),
                  new TextRun({
                    text: `不合格 ${data.materialStats.unqualifiedBatches} 批次，`
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun('到货合格率 '),
                  new TextRun({
                    text: `${data.materialStats.qualifiedRate}%`,
                    underline: {}
                  }),
                  new TextRun('。')
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun('不合格原料为：'),
                  new TextRun({
                    text: data.materialStats.unqualifiedMaterials.join('、'),
                    underline: {}
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `不合格原因：${data.materialStats.unqualifiedReasons.join('，')}，`
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `对不合格原料采取的处理措施：${data.materialStats.handlingMeasures}。`
                  })
                ]
              }),

              // 3. 投诉处理情况
              new Paragraph({
                indent: { left: 200 },
                spacing: { before: 80, after: 60 },
                children: [
                  new TextRun({
                    text: '3. 学生及家长投诉处理情况（学校食堂或供餐单位当月接收和处理的质量投诉）',
                    bold: true
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `本月质量投诉起数为 ${data.complaintStats.total} 起，有效投诉 ${data.complaintStats.valid} 起，无效投诉 ${data.complaintStats.invalid} 起。`
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `投诉产品主要为：${data.complaintStats.products.join('，')}；`
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `投诉主要原因为：${data.complaintStats.reasons.join('，')}；`
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `采取的主要改进措施为：${data.complaintStats.improvementMeasures.join('，')}。`
                  })
                ]
              }),

              // 4. 供应商管理
              new Paragraph({
                indent: { left: 200 },
                spacing: { before: 80, after: 60 },
                children: [
                  new TextRun({
                    text: '4. 供应商管理（对供应商审核及管理情况）',
                    bold: true
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `本月对供应商审核情况：${data.supplierManagement}`
                  })
                ]
              }),

              // 5. 培训情况
              new Paragraph({
                indent: { left: 200 },
                spacing: { before: 80, after: 60 },
                children: [
                  new TextRun({
                    text: '5. 从业人员食品安全相关培训情况',
                    bold: true
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `本月开展员工食品安全相关培训情况：本月开展食品安全培训 ${data.trainingStats.times} 次，合计 ${data.trainingStats.hours} 学时，参加培训人员合计 ${data.trainingStats.participants} 人。`
                  })
                ]
              }),

              // 6. 日管控检查
              new Paragraph({
                indent: { left: 200 },
                spacing: { before: 80, after: 60 },
                children: [
                  new TextRun({
                    text: '6. 食品安全日管控检查问题落实情况',
                    bold: true
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `1、本月日常检查问题数为 ${data.dailyCheckStats.totalIssues} 项，已落实整改 ${data.dailyCheckStats.resolvedIssues} 项，待整改 ${data.dailyCheckStats.pendingIssues} 项，计划完成整改时间为 ${data.dailyCheckStats.planCompletionDate}；`
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 400, hanging: 300 },
                children: [
                  new TextRun({
                    text: `2. 相关责任部门整改配合情况：${data.dailyCheckStats.cooperationStatus}。`
                  })
                ]
              }),

              // 二、较大风险隐患排查
              new Paragraph({
                spacing: { before: 150, after: 100 },
                children: [
                  new TextRun({
                    text: '二、较大风险隐患排查情况（包括但不限于以下部分）',
                    bold: true,
                    size: 22
                  })
                ]
              }),
              ...data.riskIssues.map(
                (issue, index) =>
                  new Paragraph({
                    indent: { left: 200 },
                    spacing: { line: 300 },
                    children: [
                      new TextRun({
                        text: `${index + 1}. ${issue.description} - 风险等级: ${issue.level} - 整改措施: ${
                          issue.solution
                        }`,
                        bold: true
                      })
                    ]
                  })
              ),

              // 三、食品安全相关部门发言
              new Paragraph({
                spacing: { before: 150, after: 100 },
                children: [
                  new TextRun({
                    text: '三、食品安全相关部门发言及达成的共识',
                    bold: true,
                    size: 22
                  })
                ]
              }),
              ...data.departmentSpeeches.map(
                speech =>
                  new Paragraph({
                    indent: { left: 200 },
                    spacing: { line: 300 },
                    children: [
                      new TextRun({
                        text: `· ${speech.department}: ${speech.content}`,
                        bold: true
                      })
                    ]
                  })
              ),

              // 四、主要负责人工作指示
              new Paragraph({
                spacing: { before: 150, after: 100 },
                children: [
                  new TextRun({
                    text: `四、学校主要负责人工作指示（负责人: ${data.principal})`,
                    bold: true,
                    size: 22
                  })
                ]
              }),
              new Paragraph({
                indent: { left: 200 },
                spacing: { line: 300 },
                children: [
                  new TextRun({
                    text: data.principalInstructions
                  })
                ]
              }),

              // 五、下个月工作计划
              new Paragraph({
                spacing: { before: 150, after: 100 },
                children: [
                  new TextRun({
                    text: '五、下个月食品安全管理重点工作调度计划',
                    bold: true,
                    size: 22
                  })
                ]
              }),
              ...data.nextMonthPlans.map(
                (plan, index) =>
                  new Paragraph({
                    indent: { left: 200 },
                    spacing: { line: 300 },
                    children: [
                      new TextRun({
                        text: `${index + 1}. ${plan}`,
                        bold: true
                      })
                    ]
                  })
              )
            ]
          })
        ]
      }),

      // 记录人和负责人
      new TableRow({
        children: [
          new TableCell({
            columnSpan: 4,
            children: [
              new Paragraph({
                text: `记 录 人：${data.recorder}    日期：${meetingDate.format('YYYY年MM月DD日')}`
              })
            ]
          })
        ]
      }),
      new TableRow({
        children: [
          new TableCell({
            columnSpan: 4,
            children: [
              new Paragraph({
                text: `负责人(校长/法人）：${data.responsiblePerson}    日期：${meetingDate.format('YYYY年MM月DD日')}`
              })
            ]
          })
        ]
      })
    ]
  })
}

function createFooterContent(data) {
  // const year = dayjs().format('YYYY')
  // const startDate = dayjs().startOf('month').format('YYYY-MM-DD')
  // const endDate = dayjs().endOf('month').format('YYYY-MM-DD')
  return [
    // new Paragraph({
    //   alignment: AlignmentType.CENTER,
    //   children: [
    //     new TextRun({
    //       text: '编号：024',
    //       bold: true
    //     })
    //   ]
    // }),

    // new Paragraph({
    //   alignment: AlignmentType.CENTER,
    //   children: [
    //     new TextRun({
    //       text: data.schoolName,
    //       bold: true,
    //       size: 24
    //     })
    //   ]
    // }),

    // new Paragraph({
    //   alignment: AlignmentType.CENTER,
    //   children: [
    //     new TextRun({
    //       text: '学校食堂每月食品安全调度会议记录',
    //       bold: true
    //     })
    //   ]
    // }),

    // new Paragraph({
    //   alignment: AlignmentType.CENTER,
    //   children: [
    //     new TextRun({
    //       text: '管理台账',
    //       bold: true
    //     })
    //   ]
    // }),

    // new Paragraph({
    //   alignment: AlignmentType.CENTER,
    //   children: [
    //     new TextRun({
    //       text: `${year}年`,
    //       bold: true
    //     })
    //   ]
    // }),

    // new Paragraph({
    //   text: '使用部门：',
    //   children: [
    //     new TextRun({
    //       text: data.department,
    //       underline: {}
    //     })
    //   ]
    // }),

    // new Paragraph({
    //   text: `使用部门负责人: ${data.departmentHead}`
    // }),

    // new Paragraph({
    //   text: `填写人：${data.filler}`
    // }),

    // new Paragraph({
    //   text: `使用期限：${startDate} 至 ${endDate}`
    // }),

    new Paragraph({
      alignment: AlignmentType.CENTER, // 添加居中样式
      children: [
        new TextRun({
          text: '学校食品安全月调度工作制度',
          bold: true,
          size: 42 // 二号字体大小约为32pt
        })
      ]
    }),

    new Paragraph({
      children: [
        new TextRun({
          text: '（一）由学校校长组织召开月调度会议，听取食品安全总监关于学校食堂或供餐单位食品安全管理工作的情况汇报，主要参与人员包括学校主要负责人、食品安全总监、相关部门负责人。',
          size: 32 // 三号字体约为16pt
        })
      ]
    }),

    new Paragraph({
      children: [
        new TextRun({
          text: '（二）由学校食品安全总监汇总最近一个月度内学校食品安全管理工作情况，主要包括日管控、周排查中发现的重大食品安全风险问题及整改情况，日常食品安全管理情况的汇总分析，内容包括但不限于以下方面：原料验收情况、供应商管理、食品安全日常检查问题落实情况、较大风险隐患排查情况、下个月重点工作计划等。对当月食品安全日常管理、风险隐患排查治理等情况进行工作总结。',
          size: 32
        })
      ]
    }),

    new Paragraph({
      children: [
        new TextRun({
          text: '（三）食品安全总监根据当月食品安全管理工作情况、会议讨论决议及学校主要负责人指示，制定下个月食品安全管理重点工作计划，并形成《学校每月食品安全调度会议纪要》。',
          size: 32
        })
      ]
    })
  ]
}
