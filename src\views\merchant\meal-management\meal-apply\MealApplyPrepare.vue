<template>
  <div class="ApproveRules container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="export" @click="handleExport('add')" v-permission="['background_approve.order_approve_visitor.preparation_list_export']">导出Excel</button-icon>
          <button-icon color="origin" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
          />
      </div>
      <!-- table content end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { ORDER_PERPARE_SEARCH, ORDER_PERPARE_TABLE, getRequestParams } from './constants'
export default {
  name: 'ApproveRulesList',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: ORDER_PERPARE_TABLE,
      searchFormSetting: {},
      selectListId: [],
      printType: 'MealApplyPrepare'
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.searchFormSetting = deepClone(ORDER_PERPARE_SEARCH)
      this.initPrintSetting()
      this.getVisitorPrepareList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getVisitorPrepareList()
    }, 300),
    async getVisitorPrepareList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting)
      const res = await this.$apis.apiBackgroundApproveOrderApproveVisitorPreparationListPost({
        ...params,
        date_type: 'create_time'
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出报表
    handleExport() {
      let params = getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      const option = {
        type: 'MealApplyTotal',
        url: 'apiBackgroundApproveOrderApproveVisitorPreparationListExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss">
  .tips{
    color: #ff9b45;
    font-weight: bold;
    font-size: 16px;
    margin-top: 15px;
  }
</style>
