<!--食材分类 使用 el-drawer 组件-->
<template>
  <custom-drawer :show="visible" title="食材分类" size="40%" @close="handleClose" @cancel="handleClose"
    @confirm="handleClose" :confirmShow="true" :cancelShow="false" confirm-text="关闭" v-bind="$attrs" v-on="$listeners">
    <div class="ingredients-type-container" v-loading="isLoading">
      <div class="category-header">
        <div class="primary-title">一级分类</div>
        <div class="secondary-title">二级分类</div>
      </div>
      <div class="category-content">
        <!-- 左侧一级分类列表 -->
        <div class="primary-list">
          <div
            v-for="category in treeTableData"
            :key="category.uniqueId"
            :class="['primary-item', { active: activeCategory === category.uniqueId }]"
            @click="activeCategory = category.uniqueId"
          >
            {{ category.name }}
          </div>
        </div>

        <!-- 右侧二级分类列表 -->
        <div class="secondary-list">
          <div v-if="currentCategory && currentCategory.hasChildren && currentCategory.children.length > 0">
            <div v-for="sort in currentCategory.children"
              :key="sort.uniqueId"
              class="secondary-item"
            >
              {{ sort.name }}
            </div>
          </div>
          <el-empty v-else description="暂无二级分类"></el-empty>
        </div>
      </div>
    </div>
  </custom-drawer>
</template>

<script>
export default {
  name: 'IngredientsTypeDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      treeTableData: [],
      isLoading: false,
      activeCategory: '' // 新增：当前激活的分类
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.treeTableData.length === 0) {
        this.processCategoryData();
      }
    }
  },
  created() {
    if (this.visible) {
      this.processCategoryData();
    }
  },
  methods: {
    handleClose() {
      this.$emit('close', false);
    },
    async processCategoryData() {
      this.isLoading = true;
      this.treeTableData = [];
      try {
        const res = await this.$apis.apiBackgroundFoodIngredientSortCategoryNameListPost();
        if (res && res.code === 0) {
          const rawData = res.data || [];
          const processedTreeData = [];

          rawData.forEach(category => {
            const hasChildren = category.sort_list && category.sort_list.length > 0;
            const primaryNode = {
              uniqueId: `category-${category.id}`,
              id: category.id,
              name: category.name,
              children: [],
              hasChildren: hasChildren
            };

            if (hasChildren) {
              category.sort_list.forEach(sort => {
                primaryNode.children.push({
                  uniqueId: `sort-${sort.id}`,
                  id: sort.id,
                  name: sort.name
                });
              });
            }
            processedTreeData.push(primaryNode);
          });

          this.treeTableData = processedTreeData;
          // 设置默认选中第一个分类
          if (processedTreeData.length > 0) {
            this.activeCategory = processedTreeData[0].uniqueId;
          }
        } else {
          this.$message.error(res.msg || '获取分类数据失败');
        }
      } catch (error) {
        console.error("Error fetching or processing category data:", error);
        this.$message.error('获取分类数据时出错');
      } finally {
        this.isLoading = false;
      }
    }
  },
  computed: {
    currentCategory() {
      return this.treeTableData.find(item => item.uniqueId === this.activeCategory);
    }
  }
}
</script>

<style lang="scss" scoped>
.ingredients-type-container {
  height: calc(100vh - 180px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.category-header {
  display: flex;
  height: 40px;
  line-height: 40px;
  background-color: #F5F7FA;
  border-bottom: 1px solid #DCDFE6;

  .primary-title {
    width: 250px;
    padding-left: 20px;
    font-weight: 500;
    color: #303133;
    border-right: 1px solid #DCDFE6;
  }

  .secondary-title {
    flex: 1;
    padding-left: 20px;
    font-weight: 500;
    color: #303133;
  }
}

.category-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.primary-list {
  width: 250px;
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid #DCDFE6;
  background: #fff;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
  }

  .primary-item {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    cursor: pointer;

    &:hover {
      background-color: #f5f7fa;
    }

    &.active {
      background-color: #f5f7fa;
      color: #ff9b45;
    }
  }
}

.secondary-list {
  flex: 1;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  box-sizing: border-box;
  background: #fff;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
  }

  .secondary-item {
    padding: 12px;
    margin-bottom: 8px;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #EBEEF5;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
