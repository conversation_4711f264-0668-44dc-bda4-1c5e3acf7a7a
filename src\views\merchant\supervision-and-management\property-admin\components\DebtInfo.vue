<template>
  <div class="container-wrapper debt-info-wrapper">
    <div class="tab-item m-t-20">
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        label-width="120px"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" @click="clickHistory">历史记录</button-icon>
            <button-icon color="plain" @click="gotoExport">导出</button-icon>
            <button-icon color="plain" @click="clickDebtInfoDrawerShow('add')">添加</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            tooltip-effect="dark debt-info-tooltips"
            header-row-class-name="ps-table-header-row"
            empty-text="暂无数据，请查询"
          >
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #images="{ row }">
                <el-button
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="handleClick(row)"
                  :disabled="!row.file.length"
                >
                  查看
                </el-button>
              </template>
              <template #operation="{ row }">
                <el-button
                  type="text"
                  :disabled="disabledyReversal(row)"
                  size="small"
                  class="ps-red"
                  @click="clickLiabilityReversal(row)"
                >
                  冲销
                </el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <!--  -->
    <debt-info-add
      :isShow.sync="debtInfoDrawerShow"
      v-if="debtInfoDrawerShow"
      @clickConfirm="clickConfirm"
    ></debt-info-add>
    <!-- 历史记录 -->
    <debt-info-history-drawer
      :isShow.sync="debtInfoHistoryDrawerShow"
      v-if="debtInfoHistoryDrawerShow"
    ></debt-info-history-drawer>
    <!-- 图片预览 -->
    <image-viewer v-model="showImagePreview" :z-index="3000" :on-close="closePreview" :preview-src-list="previewList" />
  </div>
</template>

<script>
import { SEARCH_DEBT_INFO, TABLE_DEBT_INFO, DEBT_TYPE_LIST } from './constants'
import { debounce, getRequestParams, deepClone } from '@/utils'
import DebtInfoHistoryDrawer from './DebtInfoHistoryDrawer.vue'
import DebtInfoAdd from './DebtInfoAdd.vue'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'DebtInfo',
  mixins: [exportExcel],
  components: {
    DebtInfoHistoryDrawer,
    DebtInfoAdd
  },
  data() {
    return {
      isLoading: false,
      tableSetting: deepClone(TABLE_DEBT_INFO),
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: deepClone(SEARCH_DEBT_INFO),
      printType: 'DebtInfo',
      debtInfoHistoryDrawerShow: false,
      debtInfoDrawerShow: false,
      showImagePreview: false,
      previewList: []
    }
  },
  watch: {
    'searchFormSetting.liability_category.value'(val) {
      this.searchFormSetting.liability_type.dataList = []
      this.searchFormSetting.liability_type.value = []

      if (val.length) {
        // 检查是否选择了全部的选项
        let hasFixedAsset = val.includes('non_flowing_liability')
        let hasCurrentAsset = val.includes('flowing_liability')

        if (hasFixedAsset && hasCurrentAsset) {
          // 如果两者都被选中，则显示全部选项
          this.searchFormSetting.liability_type.dataList = DEBT_TYPE_LIST
        } else if (hasFixedAsset) {
          // 只选择了非流动负债
          this.searchFormSetting.liability_type.dataList = [
            {
              label: '长期借款',
              value: 'long_borrowing'
            },
            {
              label: '长期应付款',
              value: 'long_payable'
            },
            {
              label: '其他负债',
              value: 'other_liability'
            }
          ]
        } else if (hasCurrentAsset) {
          // 只选择了流动负债
          this.searchFormSetting.liability_type.dataList = [
            {
              label: '短期借款',
              value: 'short_borrowing'
            },
            {
              label: '应付账款',
              value: 'payable'
            },
            {
              label: '预收账款',
              value: 'pre_harvest_payable'
            },
            {
              label: '其他负债',
              value: 'other_liability'
            }
          ]
        }
      }
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getDataList()
    },
    // 筛选
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    // 请求列表数据
    async getDataList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundFundSupervisionBusLiabilityV4LiabilityListPost(params)
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        this.tableData = deepClone(data.results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    clickConfirm() {
      this.currentPage = 1;
      this.getDataList()
    },
    clickDebtInfoDrawerShow(type) {
      this.debtInfoDrawerShow = true
    },
    clickHistory() {
      this.debtInfoHistoryDrawerShow = true
    },
    disabledyReversal(item) {
      let status = item.data_source === 'system' || item.liability_state !== 'not_repayment'
      return status
    },
    clickLiabilityReversal(item) {
      this.$confirm(`冲销后，该负债记录将不被统计！！是否确认冲销该负债记录？`, `冲销`, {
        title: '提示',
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const res = await this.$apis.apiBackgroundFundSupervisionBusLiabilityV4LiabilityReversalPost({
              id: item.id
            })
            if (res.code === 0) {
              this.$message.success('冲销成功')
              this.getDataList()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    handleClick(row) {
      console.log(row.file)
      this.previewList = row.file
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    gotoExport() {
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.totalCount)
      const option = {
        url: 'apiBackgroundFundSupervisionBusLiabilityV4LiabilityExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
.debt-info-wrapper {
  .oneLine {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.debt-info-tooltips {
  white-space: normal; /* 允许换行 */
  word-break: break-all; /* 长单词自动换行 */
  max-width: 200px; /* 设置最大宽度，超出后换行 */
}
</style>
