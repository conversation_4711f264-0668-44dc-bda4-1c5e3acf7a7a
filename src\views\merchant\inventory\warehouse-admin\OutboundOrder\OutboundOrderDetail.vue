<template>
  <div class="OutboundOrderDetail container-wrapper">
    <h3 class="m-t-20">仓库管理/出库单/详情</h3>
    <div class="">
      <div class="absolute-btn align-r p-t-20 p-b-20 float-r">
        <el-button class="ps-btn" size="small" @click="backHandle">返回</el-button>
        <el-button class="ps-btn" size="small" @click="handleExport">导出</el-button>
        <el-button class="ps-btn" size="small" v-print="printObj">打印</el-button>
      </div>
      <!-- 需打印的内容部分 start -->
      <div id="print-box">
        <div class="box-wrapper p-20">
          <div class="text-center m-b-20 font-size-26">出库单</div>
          <div class="header m-b-20">
            <span class="inline-block m-r-30">
              当前仓库：
              <span style="font-weight: 600">{{ detailData.warehouse_name }}</span>
            </span>
            <span class="inline-block m-r-30">单据编号：{{ detailData.trade_no }}</span>
            <span class="inline-block m-r-30">出库时间：{{ detailData.exit_time }}</span>
            <span class="inline-block m-r-30">出库类型：{{ detailData.inventory_exit_type_alias }}</span>
            <span class="inline-block m-r-30">经手人：{{ detailData.account_name }}</span>
          </div>
          <div class="">
            <!-- table start -->
            <el-table
              v-loading="isLoading"
              :data="tableData"
              ref="tableData"
              style="width: 100%"
              stripe
              size="small"
              header-row-class-name="ps-table-header-row"
            >
              <table-column v-for="item in tableSettings" :key="item.key" :col="item">
                <template #fujian="{ row }">
                  <el-button type="text" class="ps-text" size="small" @click="clickViewerHandler(row)" :disabled="!row.image_json || row.image_json.length === 0">查看</el-button>
                </template>
              </table-column>
            </el-table>
            <!-- table end -->
          </div>
          <div class="footer">
            <p class="p">合计：￥{{ detailData.all_total_amount | formatMoney }}</p>
            <p class="p">出库备注：{{ detailData.remark || '--' }}</p>
            <p>附件:</p>
            <p class="p noprint">
              <span v-if="detailData.image_json && detailData.image_json.length > 0">
                <el-image
                  v-for="(img, index) in detailData.image_json"
                  :key="img"
                  :preview-src-list="detailData.image_json"
                  :initial-index="index"
                  class="file-img m-r-6"
                  :src="img"
                  fit="contain"
                ></el-image>
              </span>
              <span v-else>空</span>
            </p>
            <p class="p">
              制单人：{{ detailData.account_name }}
              <span class="m-l-30">制单日期：{{ new Date() | formatDate('YYYY-MM-DD HH:mm') }}</span>
            </p>
          </div>
        </div>
        <div class="approve-box">
          <h3 style="margin: 0 0 30px;">审批进度</h3>
          <template v-if="detailData.operator_record && detailData.operator_record.length > 0">
            <el-timeline class="m-t-20 m-l-20">
              <el-timeline-item hide-timestamp size="large" v-for="(item, index) in detailData.operator_record" :key="index">
                <div class="step">
                  <div class="step-item step-status">{{ item.approve_status || '--' }}</div>
                  <div class="step-item step-name">{{ item.operator }}</div>
                  <div class="step-item step-no">{{ item.role_name }}</div>
                  <div class="step-item step-time">{{ item.time }}</div>
                  <div class="step-item step-reason">{{ item.content }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </template>
          <div v-else class="m-l-20" style="font-size: 13px;">空</div>
        </div>
      </div>
      <!-- 需打印的内容部分 end -->
    </div>
    <!-- 预览 -->
    <image-viewer  v-model="showViewer" :initial-index="0" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, divide, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import printUtils from '@/mixins/printUtils' // 导出混入
// import report from '@/mixins/report' // 混入
import print from 'vue-print-nb'

export default {
  name: 'ProcureOfferList',
  mixins: [exportExcel, printUtils],
  directives: {
    print
  },
  components: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      warehouseId: '',
      accountName: this.$store.getters.userInfo.member_name,
      detailData: {},
      tableData: [],
      tableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '物资分类', key: 'materail_classification_name' },
        { label: '供应商名称', key: 'supplier_manage_name' },
        { label: '出库数量', key: 'count' },
        { label: '最小单位', key: 'unit_name' },
        { label: '成本价', key: 'entry_fee' },
        { label: '合计', key: 'total_fee' },
        { label: '附件信息', key: 'fujian', type: 'slot', slotName: 'fujian' }
      ],
      printObj: {
        id: '#print-box', // 这里是要打印元素的ID
        popTitle: '&nbsp', // 打印的标题
        extraCss: '', // 打印可引入外部的一个 css 文件
        extraHead: '<style>.el-image{width: 100px;height: 100px;display:inline-block;}.approve-box {margin-top: 20px;padding: 20px;border-radius: 12px;background-color: #fff;}.approve-box .step {padding: 10px;background-color: #f2f2f2;border-radius: 4px;}.approve-box .step-item {display: table-cell;min-width: 80px;padding: 0 10px;text-align: center;}.approve-box .step-status {width: 100px;}.approve-box .step-name {width: 120px;}.approve-box .step-no {width: 120px;}.approve-box .el-timeline-item__wrapper {top: -12px;}.approve-box .el-timeline-item:not(:last-child) {padding-bottom: 36px;}.approve-box .el-timeline-item:last-child {padding-bottom: 0;}</style>' // 打印头部文字
      },
      previewSrcList: [],
      showViewer: false
    }
  },
  created() {
    this.warehouseId = +this.$route.query.warehouse_id
    this.initLoad()
  },
  mounted() {
    this.initResize()
  },
  methods: {
    async initLoad() {
      this.getOutboundOrderDetail()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getOutboundOrderDetail() {
      if (!this.warehouseId) return this.$message.error('获取仓库id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: this.warehouseId,
        id: +this.$route.query.id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpExitInfoDetailsPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        // is_apporve 为0时表示无需审批
        if (res.data.is_apporve === 0) {
          res.data.operator_record = [
            { approve_status: '审批通过', operator: '', role_name: '', time: '', content: '' }
          ]
        }
        this.detailData = res.data
        let result = []
        // 出库单关联不同的入库单的，物资的供应商对应入库单的的供应商，需要将数据拆分
        res.data.exit_data.forEach(item => {
          if (item.contact_trade_list && item.contact_trade_list.length > 0) {
            item.contact_trade_list.forEach(v => {
              result.push({
                materials_name: item.materials_name,
                materail_classification_name: item.materail_classification_name,
                image_json: item.image_json,
                supplier_manage_name: v.supplier_manage_name,
                count: v.count,
                unit_name: item.unit_name,
                entry_fee: '￥' + divide(item.entry_fee),
                total_fee: '￥' + divide(v.total_amount)
              })
            })
          } else {
            result.push({
              materials_name: item.materials_name,
              materail_classification_name: item.materail_classification_name,
              image_json: item.image_json,
              supplier_manage_name: '',
              count: item.exit_count,
              unit_name: item.unit_name,
              entry_fee: '￥' + divide(item.entry_fee),
              total_fee: '￥' + divide(item.entry_fee * item.exit_count)
            })
          }
        })
        this.tableData = result
      } else {
        this.$message.error(res.msg)
      }
    },
    handleExport() {
      const option = {
        type: 'OutboundOrderDetail',
        url: 'apiBackgroundDrpExitInfoDetailsExportPost',
        params: {
          id: +this.$route.query.id,
          warehouse_id: this.warehouseId
        }
      }
      this.exportHandle(option)
    },
    backHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'OutboundOrder')
    },
    // 查看预览图
    clickViewerHandler(row) {
      console.log("clickViewerHandler", row);
      // don't show viewer when preview is false
      let imgList = row.image_json || []
      if (imgList) {
        imgList = Array.isArray(imgList) ? imgList : JSON.parse(imgList)
      }
      this.previewSrcList = imgList
      if (!this.previewSrcList || this.previewSrcList.length === 0) {
        return this.$message.error('暂无图片');
      }
      this.showViewer = true;
    },
    // 关闭图片预览
    closeViewer() {
      this.showViewer = false;
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/print.scss';
.OutboundOrderDetail {
  width: 100%;
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
  table {
    width: 100% !important;
    font-weight: 500;
    th {
      font-weight: 500;
    }
  }
  .absolute-btn {
    position: absolute;
    right: 30px;
  }
  .box-wrapper{
    margin-top: 20px;
    box-shadow: 6px 6px 10px 0px rgba(202, 210, 221, 0.3), inset 2px 2px 0px 0px #ffffff;
    border-radius: 12px;
    overflow: hidden;
    background-color: #f8f9fa;
  }
  #print-box {
    width: 100%;
    div {
      // width: 100%;
    }
    .el-table thead,
    .ps-table-header-row {
      width: 100%;
    }
    .footer {
      p {
        margin: 10px 0 0;
      }
    }
  }
  .file-img {
    width: 100px;
    height: 100px;
  }
  .approve-box {
    margin-top: 20px;
    padding: 20px;
    border-radius: 12px;
    background-color: #fff;
    .step {
      // width: 100%;
      // display: flex;
      // align-items: center;
      padding: 10px;
      background-color: #f2f2f2;
      border-radius: 4px;
    }
    .step-item {
      display: table-cell;
      // margin-right: 10px;
      min-width: 80px;
      padding: 0 10px;
      text-align: center;
    }
    .step-status {
      width: 100px;
    }
    .step-name {
      width: 120px;
    }
    .step-no {
      width: 120px;
    }
    .step-time {
      // padding-right: 10px;
    }
    // .step-icon {
    //   margin-right: 0;
    //   min-width: 40px;
    //   width: 40px;
    // }
    .el-timeline-item__wrapper {
      top: -12px;
    }
    .el-timeline-item:not(:last-child) {
      padding-bottom: 36px;
    }
    .el-timeline-item:last-child {
      padding-bottom: 0;
    }
  }
}
</style>
