<template>
  <div class="ConsumptionOrder container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" label-width="105px"
      @search="searchHandle" @reset="resetHandler">
      <template #perv>
        <div class="searchref_top">
          <el-button :class="{ active: current === 0 }" @click="tabHandler(0)" v-permission="['background_order.vending_machine.order_list']">消费订单</el-button>
          <el-button :class="{ active: current === 1 }" @click="tabHandler(1)" v-permission="['background_order.vending_machine.refund_order_list']">退款订单</el-button>
          <el-button :class="{ active: current === 2 }" @click="tabHandler(2)" v-permission="['background_order.vending_machine.food_sale_summary']">销售统计</el-button>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="plain" @click="mulRefundHandle">批量退款</button-icon> -->
          <!-- <button-icon color="plain" type="export" @click="handleExportLive">导出实况</button-icon> -->
          <button-icon color="plain" @click="gotoPrint" v-if="current === 2">打印</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon v-if="current === 0" color="plain" type="export" @click="handleExport" v-permission="['background_order.vending_machine.order_list_export']">导出报表</button-icon>
          <button-icon v-if="current === 1" color="plain" type="export" @click="handleExport" v-permission="['background_order.vending_machine.refund_order_list_export']">导出报表</button-icon>
          <button-icon v-if="current === 2" color="plain" type="export" @click="handleExport" v-permission="['background_order.vending_machine.food_sale_summary_export']">导出报表</button-icon>
          <!-- <button-icon color="plain" @click="openDialog" >小票打印</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <!-- :selectable="selectableHandle"  -->
        <el-table :data="tableData" v-loading="isLoading" stripe :empty-text="isFirstSearch ? '暂无数据，请查询' : ''" header-row-class-name="ps-table-header-row"
          @selection-change="handleOrderSelectionChange" :span-method="collectSpanMethod" @sort-change="sortChange">
          <!-- <el-table-column type="selection" width="55" v-if="current === 2"></el-table-column> -->
          <table-column v-for="(item, index) in currentTableSetting" :key="index" :col="item">
            <template #index="{ row, index }">
              {{ getPageIndex(index, row) }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoDetail(row)">详情</el-button>
              <el-button type="text" class="ps-text" size="small" :disabled="!row.can_refund"
                @click="openRefundDialog(row)" v-permission="['background_order.order_payment.instore_refund']">退款</el-button>
            </template>
          </table-column>
        </el-table>
        <ul class="total" v-if="current === 0 || current === 1">
          <li>
            {{ current === 0 ? '合计笔数:' : '退款笔数' }}
            <span>{{ total_count }}</span>
          </li>
          <li>
            {{ current === 0 ? '合计订单金额:￥' : '合计退款金额:￥' }}
            <span>{{ total_amount | formatMoney }}</span>
          </li>
          <li v-if="current === 0">
            合计实收金额:￥
            <span>{{ total_origin_amount | formatMoney }}</span>
          </li>
          <li>
            手续费合计:￥
            <span>{{ total_rate_fee | formatMoney }}</span>
          </li>
        </ul>
        <div class="ps-text m-t-20" v-if="current === 2">注：该表基于商品售卖原金额进行计算，仅供参考，不能作为最终营业额的依据。</div>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- 退款对话框 -->
    <div class="refund-confirm">
      <el-dialog top="200px" title="退款" :visible.sync="outerVisible" width="800px" customClass="ps-dialog">
        <el-table :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }"
          :data="refundData.food_list" border row-key="food_id" style="width: 100%;" ref="refundTable"
          @selection-change="handleRefundSelectionChange">
          <el-table-column type="selection" :show-overflow-tooltip="true" :reserve-selection="true"
            v-if="refundMethod === 'part'" width="55" :selectable="selectDisabled"></el-table-column>
          <el-table-column prop="food_url" label="图片">
            <template slot-scope="scope">
              <img style="width:60px"
                :src="scope.row.food_url ? scope.row.food_url : 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"
                alt="">
            </template>
          </el-table-column>
          <el-table-column prop="food_name" label="商品名称"></el-table-column>
          <el-table-column prop="raw_fee" label="销售价格">
            <template slot-scope="scope">
              <!-- raw_fee -->
              <span>￥{{ scope.row.raw_fee | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="数量"></el-table-column>
          <el-table-column prop="cargo_lane" label="货道"></el-table-column>
          <el-table-column prop="buy_price" label="消费金额">
            <template slot-scope="scope">
              <span>￥{{ scope.row.all_raw_fee | formatMoney }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="shipping_status_alias" label="出货状态"></el-table-column> -->
          <el-table-column prop="food_status" label="退款状态">
            <template slot-scope="scope">
              <span>{{ scope.row.food_status === 'ORDER_REFUND_SUCCESS' ? '退款成功' : '未退款' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="refund-radio">
          <el-radio text-color="#FF9B45" v-model="refundMethod" @change="changeRefundType" label="all"
            class="ps-radio">全额退款</el-radio>
          <el-radio text-color="#FF9B45" v-model="refundMethod" @change="changeRefundType" label="part"
            class="ps-radio">部分退款</el-radio>
        </div>
        <div class="refund-info">
          <el-form :model="dialogForm" @submit.native.prevent status-icon ref="dialogFormRef" :rules="dialogFormRules"
            label-width="100px" class="attendance-form" inline>
            <div class="inline-box m-r-20">
              <span>可退款余额：</span>
              <span v-if="refundMethod === 'all'">{{ refundData.net_fee | formatMoney }}</span>
              <span v-if="refundMethod === 'part'">&lt;{{ refundData.part_net_fee }}</span>
            </div>
            <div v-if="refundMethod === 'all'" class="inline-box">
              <span>退款金额：</span>
              <span>{{ refundData.pay_fee | formatMoney }}</span>
            </div>
            <div v-if="refundMethod === 'part' && refundData.payway !== 'PushiPay'" class="inline-box">
              <el-form-item label="退款金额：" prop="refundMoney">
                <el-input class="w-180 ps-input" placeholder="请输入退款金额" v-model="dialogForm.refundMoney"></el-input>
              </el-form-item>
            </div>
            <div v-if="refundMethod === 'part' && refundData.payway === 'PushiPay'">
              <el-form-item label="补贴钱包：" prop="refundSubsidyMoney">
                <el-input class="w-180 ps-input" :placeholder="'可退金额<' + refundData.part_subsidy_fee"
                  :disabled="!Number(refundData.part_subsidy_fee)" v-model="dialogForm.refundSubsidyMoney"></el-input>
              </el-form-item>
              <el-form-item label="储值钱包：" prop="refundWalletMoney">
                <el-input class="w-180 ps-input" :placeholder="'可退金额<' + refundData.part_wallet_fee"
                  :disabled="!Number(refundData.part_wallet_fee)" v-model="dialogForm.refundWalletMoney"></el-input>
              </el-form-item>
              <el-form-item label="赠送钱包：" prop="refundComplimentaryMoney">
                <el-input class="w-180 ps-input" :placeholder="'可退金额<' + refundData.part_complimentary_fee"
                  :disabled="!Number(refundData.part_complimentary_fee)"
                  v-model="dialogForm.refundComplimentaryMoney"></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <el-dialog width="30%" title="温馨提示" customClass="ps-dialog" :visible.sync="innerVisible" append-to-body
          top="280px">
          <p class="twoRefund" v-if="refundMethod === 'all'" style="font-size: 20px;">
            确定要对该订单进行退款吗
          </p>
          <p class="twoRefund" v-else style="font-size: 20px;">
            确定要对该订单进行
            <span style="font-weight: bold;">部分退款吗?</span>
          </p>
          <p class="twoRefund" style="color:#E0364C;">温馨提示: 确定后不可撤销</p>
          <div slot="footer" class="footer-btn">
            <el-button class="ps-cancel-btn" @click="innerVisible = false">取消</el-button>
            <el-button class="ps-btn" :disabled="dialogLoading" @click="handleConfirm" v-loading="isLoadingRefund">确定</el-button>
          </div>
        </el-dialog>
        <div slot="footer">
          <el-button class="ps-cancel-btn" @click="outerVisible = false">取 消</el-button>
          <el-button class="ps-btn" @click="handleRefund" v-loading="isLoadingRefund">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <print-setting v-if="dialogPrintVisible" :extraParams="{ printType: printType }" :tableSetting="tableSetting" :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible" @confirm="confirmPrintDialog"></print-setting>
    <print-ticket ref="printTicket" :isshow.sync="printTicketVisible" :type="current == '0' ? 'onScence' : 'order'"
      title="小票打印" :select-list-id="selectListId" :print-type="printTypeList" :confirm="searchHandle"></print-ticket>
  </div>
</template>

<script>
import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, deepClone, divide, times, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import PrintTicket from '@/components/PrintTicket'
import {
  // DEVICE_STATUS,
  CONSUMPTION_SCENE_AUTO_TABLE,
  REFUND_ORDER_AUTO_TABLE,
  COLLECT_ORDER_AUTO_TABLE,
  CONSUMPTION_SCENE_AUTO_SEARCH,
  REFUND_ORDER_AUTO_SEARCH,
  COLLECT_ORDER_AUTO_SEARCH,
  getRequestParams
} from './constants'
import report from '@/mixins/report' // 混入
export default {
  name: 'ConsumptionAutoSelling',
  components: { PrintTicket },
  mixins: [activatedLoadData, exportExcel, report],
  data() {
    let validataRefundMoney = (rule, value, callback) => {
      let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      console.log(rule)
      let price
      if (rule.field === 'refundWalletMoney') {
        price = this.refundData.part_wallet_fee
      } else if (rule.field === 'refundSubsidyMoney') {
        price = this.refundData.part_subsidy_fee
      } else if (rule.field === 'refundComplimentaryMoney') {
        price = this.refundData.part_complimentary_fee
      } else if (rule.field === 'refundMoney') {
        price = this.refundData.part_net_fee
      }
      console.log("price", price);
      if (value) {
        if (Number(value) === 0) {
          callback(new Error('金额不能为0'))
        } else if (value >= Number(price)) {
          callback(new Error('金额不能大于等于可退金额'))
        } else if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      current: 0, // 0代表堂食订单 1代表预约订单
      // 搜索筛选相关
      sceneSearchForm: deepClone(CONSUMPTION_SCENE_AUTO_SEARCH),
      // 退款订单的搜索
      reservationSearchForm: deepClone(REFUND_ORDER_AUTO_SEARCH),
      // 销售统计
      collectSearchForm: deepClone(COLLECT_ORDER_AUTO_SEARCH),
      searchForm: {},
      tableData: [],
      isLoading: false, // 刷新数据
      isLoadingRefund: false, // 退款按钮loading
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      page: 1, // 第几页
      total_count: 0,
      total_amount: 0,
      total_origin_amount: 0,
      total_rate_fee: 0,
      // 退款相关
      refundMethod: 'all', // all代表全额退款 part代表部分退款
      dialogForm: {
        refundMoney: '', // 部分退款金额
        refundWalletMoney: '', // 部分退款金额
        refundSubsidyMoney: '', // 部分退款金额
        refundComplimentaryMoney: '' // 部分退款金额
      },
      dialogFormRules: {
        refundMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }],
        refundWalletMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }],
        refundSubsidyMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }],
        refundComplimentaryMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }]
      },
      refundFoodId: [], // 部分退款菜品id
      outerVisible: false,
      innerVisible: false,
      dialogLoading: false,
      // 退款弹框的数组
      refundData: [],
      refundStatus: ['ORDER_REFUNDING', 'ORDER_REFUND_SUCCESS'],
      // 报表设置相关
      tableSetting: [],
      sceneTableSetting: deepClone(CONSUMPTION_SCENE_AUTO_TABLE),
      reservationSableSetting: deepClone(REFUND_ORDER_AUTO_TABLE),
      collectionTableSetting: deepClone(COLLECT_ORDER_AUTO_TABLE),
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'VendingMachineOrder',
      printTicketVisible: false,
      refundOrderIds: [], // 可以退款的id
      selectListId: [], // 多选功能
      totalServeFee: 0, // 服务费合计
      printTypeList: [ // 打印列表  ，
        {
          label: '后厨单',
          value: 'kitchen'
        },
        {
          label: '结账单',
          value: 'bill'
        }
      ],
      isFirstSearch: true,
      deviceList: [] // 设备列表
    }
  },
  created() {
    // this.sceneSearchForm.device_org.value = [this.$store.getters.organization]
    this.initLoad()
  },
  watch: {
    'collectSearchForm.consume_organization_ids.value'(value) {
      // 消费点更改
      console.log("watch", value);
      this.getDeviceList(value)
    }
  },
  mounted() {
    this.getLevelNameList() // 根据项目点获取公司的层级组织
    this.getpayList() // 支付方式 / 支付类型
    this.getDeviceList()
    this.getDeviceType()
  },
  methods: {
    initLoad() {
      if (this.current === 1) {
        // 退款
        this.searchForm = this.reservationSearchForm
        this.currentTableSetting = deepClone(this.reservationSableSetting)
        if (!this.isFirstSearch) {
          this.getConsumptionList()
        }
      } else if (this.current === 2) {
        // 统计
        this.searchForm = this.collectSearchForm
        this.tableSetting = deepClone(this.collectionTableSetting)
        this.currentTableSetting = deepClone(this.collectionTableSetting)
        // 获取当天
        this.initPrintSetting()
        if (!this.isFirstSearch) {
          this.getCollectList()
        }
      } else {
        // 消费订单
        this.searchForm = this.sceneSearchForm
        this.currentTableSetting = deepClone(this.sceneTableSetting)
        if (!this.isFirstSearch) {
          this.getOnSceneList()
        }
      }
      this.$nextTick(() => {
        if (Reflect.has(this.$refs, "printTicket")) {
          this.$refs.printTicket.setPrintType('bill')
        }
      })
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.printTicketVisible = false
        this.page = 1
        this.isFirstSearch = false
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.page = 1
      this.isFirstSearch = true
      this.initLoad()
    },
    tabHandler(type) {
      if (this.isLoading) {
        return this.$message.error('页面更新中请稍后再切换')
      }
      this.current = type
      this.page = 1
      this.isFirstSearch = true
      this.$nextTick(() => {
        if (type === 0) {
          this.printType = 'VendingMachineOrder'
          this.searchForm = this.sceneSearchForm
          this.tableSetting = this.sceneTableSetting
          this.currentTableSetting = deepClone(this.sceneTableSetting)
          this.initPrintSetting()
          this.getOnSceneList()
        } else if (type === 1) {
          this.printType = 'VendingMachineRefundOrder'
          this.searchForm = this.reservationSearchForm
          this.tableSetting = this.reservationSableSetting
          this.currentTableSetting = deepClone(this.reservationSableSetting)
          console.log("this.reservationSableSetting", this.reservationSableSetting);
          this.initPrintSetting()
          this.getConsumptionList()
        } else {
          this.printType = 'VendingMachineFoodSaleSummary'
          this.searchForm = this.collectSearchForm
          this.tableSetting = this.collectionTableSetting
          this.currentTableSetting = deepClone(this.collectionTableSetting)
          this.searchForm.device_name_list.dataList = deepClone(this.deviceList)
          console.log("this.collectionTableSetting", this.collectionTableSetting, this.searchForm.pay_time.value);
          this.initPrintSetting()
          this.getCollectList()
        }
        if (Reflect.has(this.$refs, "printTicket")) {
          this.$refs.printTicket.setPrintType(type === 0 ? 'bill' : '')
        }
      })
    },
    // 获取退款订单
    async getConsumptionList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(this.$apis.apiBackgroundOrderVendingMachineRefundOrderListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.total_amount = res.data.total_amount
        this.total_count = res.data.count
        this.total_origin_amount = res.data.total_pay_amount
        this.total_rate_fee = res.data.total_rate_fee
        this.totalServeFee = res.data.total_fuwu_fee || 0
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取消费订单
    async getOnSceneList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(this.$apis.apiBackgroundOrderVendingMachineOrderListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.total_amount = res.data.total_amount
        this.total_count = res.data.count
        this.total_origin_amount = res.data.total_pay_amount
        this.total_rate_fee = res.data.total_rate_fee
        this.totalServeFee = res.data.total_fuwu_fee || 0
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取统计订单
    async getCollectList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      console.log("getCollectList", this.searchForm, params);
      const [err, res] = await to(this.$apis.apiBackgroundOrderVendingMachineFoodSaleSummaryPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // this.tableData = res.data.results
        this.tableData = this.setCollectData(res.data.results, res.data.summary_data)
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.initLoad()
    },
    // 动态获取组织的层级
    async getLevelNameList() {
      // const res = await this.$apis.apiBackgroundGetlevelNameListPost()
      // let arr = JSON.parse(JSON.stringify(res.data).replace(/name/g, 'label'))
      // let arr2 = JSON.parse(JSON.stringify(arr).replace(/level/g, 'key'))
      // 初始化每个tableSetting
      this.sceneTableSetting = deepClone(CONSUMPTION_SCENE_AUTO_TABLE)
      this.reservationSableSetting = deepClone(REFUND_ORDER_AUTO_TABLE)
      // this.sceneTableSetting.splice(25, 0, ...arr2)
      // this.reservationSableSetting.splice(22, 0, ...arr2)
      if (this.current === 0) {
        this.tableSetting = this.sceneTableSetting
      } else if (this.current === 1) {
        this.tableSetting = this.reservationSableSetting
      }
      this.initPrintSetting()
    },
    // 获取支付方式 / 支付类型
    async getpayList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost()
      if (res.code === 0) {
        const result = []
        const result2 = []
        res.data.result.payways.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        res.data.result.sub_payways.forEach(d => {
          Object.keys(d).forEach(key => result2.push({ label: d[key], value: key }))
        })
        this.sceneSearchForm.payway_list.dataList = result
        this.sceneSearchForm.sub_payway_list.dataList = result2
        // this.reservationSearchForm.payway.dataList = result
        // this.reservationSearchForm.sub_payway.dataList = result2
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设备列表
    async getDeviceList(value) {
      this.isLoading = true
      var params = {
        device_type: "ZDSHG",
        page: 1,
        page_size: 99999
      }
      if (value && value.length > 0) {
        params.organization_id = value[0]
      }
      const res = await this.$apis.apiBackgroundDeviceDeviceListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.deviceList = deepClone(res.data.results || [])
        if (this.current === 2) {
          this.collectSearchForm.device_name_list.dataList = res.data.results || []
        } else {
          this.sceneSearchForm.device_name_list.dataList = res.data.results || []
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳转详情
    gotoDetail(row) {
      this.$router.push({
        // path: `/order/consumption_auto_selling_detail`,
        name: 'MerchantConsumptionAutoDetail',
        query: {
          id: row.id,
          type: this.current,
          isAutoCell: true
        }
      })
    },
    // 导出报表
    handleExport() {
      let type
      if (this.current === 1) {
        type = 'ExportAutoRefundOrder'
      } else if (this.current === 0) {
        type = 'ExportAutoOrderConsumption'
      } else {
        type = 'ExportAutoOrderCollect'
      }
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const option = {
        type,
        params
      }
      this.exportHandle(option)
    },
    // 导出实况
    handleExportLive() {
      let params = getRequestParams(this.searchForm, this.page, this.pageSize)
      if (this.current === 1) {
        params.export_order_types = 'reservation'
      } else {
        params.export_order_types = 'instore'
      }
      const option = {
        type: 'OrderPaymentLiveExport',
        url: 'apiBackgroundOrderOrderPaymentLiveExportPost',
        params
      }
      this.exportHandle(option)
    },
    // 退款start
    openRefundDialog(data) {
      this.dialogForm = {
        refundMoney: '', // 部分退款金额
        refundWalletMoney: '', // 部分退款金额
        refundSubsidyMoney: '', // 部分退款金额
        refundComplimentaryMoney: '' // 部分退款金额
      }
      this.refundData = deepClone(data)
      this.refundData.part_wallet_fee = divide(Math.abs(this.refundData.part_wallet_fee))
      this.refundData.part_subsidy_fee = divide(Math.abs(this.refundData.part_subsidy_fee))
      this.refundData.part_complimentary_fee = divide(Math.abs(this.refundData.part_complimentary_fee))
      this.refundData.part_net_fee = divide(Math.abs(this.refundData.part_net_fee))
      this.outerVisible = true
      this.refundMethod = 'all'
      console.log("openRefundDialog", this.refundData);
    },
    changeRefundType() {
      if (this.refundMethod === 'part') {
        this.$refs.refundTable.clearSelection()
      }
    },
    handleRefund() {
      if ((this.refundMethod === 'part' && this.refundData.payway === 'PushiPay' &&
        !this.dialogForm.refundWalletMoney && !this.dialogForm.refundSubsidyMoney && !this.dialogForm.refundComplimentaryMoney) ||
        (this.refundMethod === 'part' && this.refundData.payway !== 'PushiPay' && !this.dialogForm.refundMoney)) {
        return this.$message.error("请输入退款金额")
      }

      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          this.innerVisible = true
        }
      })
    },
    // 退款弹出框 退款成功
    handleConfirm() {
      let params = {
        trade_no: this.refundData.trade_no
      }
      if (this.refundMethod === 'all') {
        params.refund_fee = this.refundData.pay_fee
      } else {
        if (this.refundData.payway === 'PushiPay') {
          params.refund_wallet_fee = times(this.dialogForm.refundWalletMoney)
          params.refund_subsidy_fee = times(this.dialogForm.refundSubsidyMoney)
          params.refund_complimentary_fee = times(this.dialogForm.refundComplimentaryMoney)
        } else {
          params.refund_fee = times(this.dialogForm.refundMoney)
        }
        params.third_food_record_ids = this.refundFoodId
      }
      this.orderRefund(params)
    },
    async orderRefund(params) {
      if (this.dialogLoading) return
      this.dialogLoading = true
      let [err, res] = []
      this.isLoadingRefund = true
      this.innerVisible = false
      if (this.current) {
        // 预约退款
        [err, res] = await to(this.$apis.apiBackgroundOrderOrderReservationReservationOrderRefundPost(params))
      } else {
        // 堂食退款
        [err, res] = await to(this.$apis.apiBackgroundOrderOrderPaymentInstoreRefundPost(params))
      }
      this.isLoadingRefund = false
      this.dialogLoading = false
      if (err) {
        return this.$message.error(err.message || '失败')
      }
      if (res && res.code === 0) {
        this.innerVisible = false
        this.outerVisible = false
        this.initLoad()
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 多选框  当选择项发生变化时会触发该事件
    handleRefundSelectionChange(val) {
      this.dialogForm.refundMoney = 0
      this.refundFoodId = []
      val.map(item => {
        this.dialogForm.refundMoney += item.real_fee
        this.refundFoodId.push(item.id)
      })
      this.dialogForm.refundMoney = divide(this.dialogForm.refundMoney)
    },
    // 退款end
    selectDisabled(row, index) {
      return !this.refundStatus.includes(row.refund_status)
      // return false
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        if (this.current !== 2) {
          this.sceneSearchForm.device_name_list.dataList = res.data
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // // 多选禁用
    // selectableHandle(row) {
    //   return row.can_refund
    // },
    // 订单的多选
    handleOrderSelectionChange(val) {
      this.refundOrderIds = []
      this.selectListId = val.map(item => {
        if (item.can_refund) {
          this.refundOrderIds.push(item.id)
        }
        return item.id
      })
    },
    async mulRefundHandle() {
      if (this.dialogLoading) return
      if (!this.selectListId.length) return this.$message.error('请选择要退款的订单！')
      if (!this.refundOrderIds.length) return this.$message.error('当前所选订单不存在可退款订单！')
      this.dialogLoading = true
      this.$confirm(`确定要将这些订单进行退款？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        // customClass: 'ps-confirm',
        // cancelButtonClass: 'ps-cancel-btn',
        // confirmButtonClass: 'ps-btn',
        closeOnClickModal: false,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {
              order_ids: this.refundOrderIds
            }
            let res
            if (this.current) {
              // 预约退款
              res = await this.$apis.apiBackgroundOrderOrderReservationReservationOrderRefundPost(params)
            } else {
              // 堂食退款
              res = await this.$apis.apiBackgroundOrderOrderPaymentInstoreRefundPost(params)
            }
            this.dialogLoading = false
            if (res.code === 0) {
              this.initLoad()
              this.$message.success(`操作成功，其中不可退款订单数${this.selectListId.length - this.refundOrderIds.length}笔`)
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              this.dialogLoading = false
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => { })
    },
    openDialog() {
      if (!this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.printTicketVisible = true
    },
    // 获取数据下标
    getPageIndex(index, row) {
      var thisIndex = row.index
      if (thisIndex && typeof thisIndex === 'string' && thisIndex.indexOf('合计') !== -1) {
        return row.index
      }
      return (index + 1) + (this.page - 1) * this.pageSize
    },
    // 重置
    resetHandler() {
      this.page = 1
      this.initLoad()
    },
    // 设置合并数据
    setCollectData(dataList, summaryData) {
      console.log("dataList", dataList);
      if (dataList && Array.isArray(dataList) && dataList.length > 0) {
        // 有数据在数据后面加上合计
        var listIndex = deepClone(COLLECT_ORDER_AUTO_TABLE)
        var itemDataList = [{}, {}]
        itemDataList.forEach((item, index) => {
          listIndex.forEach(listIndexItem => {
            // 当页统计
            if (index === 0) {
              item[listIndexItem.key] = this.getSumByKey(dataList, listIndexItem.key)
            }
            // 总数统计
            if (index === 1) {
              item[listIndexItem.key] = summaryData['total_' + listIndexItem.key] ? summaryData['total_' + listIndexItem.key] : 0
            }
            // 把倒数的index改一下名字
            if (listIndexItem.key === 'index') {
              item[listIndexItem.key] = index === 0 ? '当页合计' : '总合计'
            }
          }
          )
        })
        dataList = dataList.concat(itemDataList)
      }
      console.log("dataList new", dataList);
      return dataList
    },
    // 根据key值算总数
    getSumByKey(list, key) {
      var sum = 0
      if (list && Array.isArray(list)) {
        list.forEach(item => {
          sum = item[key] && key !== 'name' ? sum + item[key] : sum
        })
      }
      return sum
    },
    // 统计合并list
    collectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log("collectSpanMethod", row, column, rowIndex, columnIndex);
      var listLength = this.tableData.length
      // 最后两行做成合计
      if (this.current === 2 && (rowIndex === listLength - 1 || rowIndex === listLength - 2)) {
        // 第一，二，三，四行合并
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 3
          };
        } else if (columnIndex === 1 || columnIndex === 2) {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    // 打印
    gotoPrint() {
      const params = getRequestParams(this.searchForm)
      const { href } = this.$router.resolve({
        name: 'ConsumptionAutoSellingPrint',
        query: {
          print_type: this.printType,
          print_title: '售货柜订单销售统计',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundOrderVendingMachineFoodSaleSummaryPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          show_summary: false, // 合计
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          // collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          isAutoCollect: true, // 展示表格末合计列
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 排序改变 order 升序降序 ascending升序 descending 降序
    sortChange({ column, prop, order }) {
      console.log("sortChange", column, prop, order);
      this.searchForm.sort_name = prop
      this.searchForm.sort_type = order === 'ascending' ? 'asc' : 'desc'
      // 判断key 确认是哪个列表的字段进行排序
      switch (prop) {
        // 销售数量
        case 'sale_count':
          break;
        // 销售额
        case 'sale_money':
          break;
        // 实际利润
        case 'real_sale_profit':
          break;
        default:
          break;
      }
      this.getCollectList()
    }
  }
}
</script>

<style lang="scss" scoped>
// 日历
// .el-range-editor--mini.el-input__inner {
//   height: 32px !important;
//   width: 320px;
// }
.container-wrapper {
  // font-size: 12px !important;

  .active {
    background-color: #ff9b45!important;
    color: #fff!important;
  }
  .el-button:focus.el-button--default:not(.is-plain):not(.el-button--primary), .el-button:hover.el-button--default:not(.is-plain):not(.el-button--primary) {
    background-color: #fff;
    color: #ff9b45;
  }

  .searchref_top {
    margin-bottom: 10px;

    .el-button {
      width: 120px;
    }
  }

  .ps-small-box {
    .block {
      display: inline-block;
    }

    .el-select {
      width: 200px !important;
    }

    .el-input {
      width: 180px !important;
    }
  }
}

.table-wrapper {
  .el-table {
    text-align: center;
    font-size: 12px;
  }

  .total {
    margin-top: 10px;

    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 14px;
    }
  }
}

.twoRefund {
  text-align: center;
}

.search-form-wrapper {
  overflow: hidden;

  // border-radius: 6px;
  .search-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 20px;

    &:after {
      content: '';
      position: absolute;
      left: 24px;
      right: 24px;
      bottom: 0;
      height: 1px;
      background-color: #e7ecf2;
    }

    .search-h-l {
      border-left: 4px solid #ff9b45;
      padding-left: 18px;
      font-size: 20px;
      color: #23282d;
    }

    .search-h-r {
      display: flex;
      align-items: center;

      .search-h-r-btn {
        margin-right: 10px;
        min-width: auto;
      }

      .search-collapse-btn {
        width: 121px;
        height: 16px;
        cursor: pointer;

        img {
          display: inline-block;
          width: 100%;
          vertical-align: middle;
        }
      }
    }
  }

  .collapse-wrapper {
    padding: 0 20px;
    // overflow: hidden;
  }

  .search-item-w {
    width: 200px;
  }

  .vue-treeselect__control {
    height: 40px;
  }

  .vue-treeselect__placeholder {
    line-height: 40px;
    font-size: 13px;
  }
}

.refund-confirm {
  .refund-radio {
    margin: 25px 0 10px;
  }

  .refund-info {
    line-height: 40px;

    .inline-box {
      display: inline-block;
    }

    .refund-info-item {
      min-width: 150px;
    }
  }
}

.footer-btn {
  .ps-btn {
    background-color: #ff9b45;
    color: #fff;
    border: #ff9b45;
  }
}
</style>
