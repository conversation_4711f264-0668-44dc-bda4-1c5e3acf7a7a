<template>
  <div class="UserRechargeRefundSummary container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    >
      <template slot="append">
        <!-- <span>为了数据准确性</span> -->
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon
            color="origin"
            type="export"
            v-permission="['background_order.ordering_food.ordering_charge_list_export']"
            @click="gotoExport"
          >
            导出EXCEL
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="tableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        />
        <!-- table end -->
      </div>
      <!-- 统计 start -->
      <table-statistics class="m-b-10" :statistics="collect" />
      <!-- end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce } from '@/utils'
import { USERREChARGEREFUNDSUMMARY } from './constants'
import report from '@/mixins/report' // 混入
export default {
  name: 'UserRechargeRefundSummary',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false, // 刷新数据
      tableData: [],
      tableSetting: [
        {
          label: '2022-10-10至2022-10-11',
          key: 'date',
          children: [
            { label: '操作渠道', key: 'payway_alias' },
            { label: '充值金额', key: 'charge_fee', type: 'money' },
            { label: '充值笔数', key: 'charge_count' },
            { label: '退费金额', key: 'refund_fee', type: 'money' },
            { label: '退费笔数', key: 'refund_count' },
            { label: '提现金额', key: 'withdraw_fee', type: 'money' },
            { label: '提现笔数', key: 'withdraw_count' }
          ]
        }
      ],
      searchFormSetting: USERREChARGEREFUNDSUMMARY,
      collect: [
        // 统计
        { key: 'total_charge_fee', value: 0, label: '累计充值订单总额:', type: 'money' },
        { key: 'total_refund_fee', value: 0, label: '累计退费订单金额:', type: 'money' },
        { key: 'total_cash_charge_fee', value: 0, label: '累计现金订单金额:', type: 'money' },
        { key: 'total_withdraw_fee', value: 0, label: '累计提现订单金额:', type: 'money' },
        { key: 'total_wechat_charge_fee', value: 0, label: '累计微信充值金额:', type: 'money' },
        { key: 'total_alipay_charge_fee', value: 0, label: '累计支付宝充值金额:', type: 'money' }
      ]
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getFoodOrderingChargeList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.getFoodOrderingChargeList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getFoodOrderingChargeList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderingFoodOrderingChargeListPost({
        ...this.formatQueryParams(this.searchFormSetting)
      })
      this.isLoading = false
      if (res.code === 0) {
        let startDate = this.formatQueryParams(this.searchFormSetting).start_date
        let endDate = this.formatQueryParams(this.searchFormSetting).end_date
        // 日期
        this.tableSetting[0].label = `${startDate}至${endDate}`
        this.tableData = res.data.results
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'UserRechargeRefundSummary',
        url: 'apiBackgroundOrderOrderingFoodOrderingChargeListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting)
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
.UserRechargeRefundSummary {
  .row--striped {
    background-color: #f5f7fa;
    .el-table__cell {
      background-color: #f5f7fa;
    }
  }
}
</style>
