<template>
  <div class="canteen-summary container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle" :autoSearch="false">
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="export" @click="handleExport" v-permission="['background_order.order_report_meal.collect_list_export']">导出Excel</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
          :span-method="objectSpanMethod"
        >
          <!-- <el-table-column prop="date" label="日期"></el-table-column>
          <el-table-column prop="organization_name" label="消费点"></el-table-column>
          <el-table-column prop="meal_type_name" label="餐段"></el-table-column>
          <el-table-column prop="success_count" label="成功报餐点餐笔数" width="150"
          ></el-table-column>
          <el-table-column prop="take_out_count" label="就餐笔数"></el-table-column>
          <el-table-column prop="time_out_count" label="未就餐笔数"></el-table-column>
          <el-table-column prop="online_fee" label="线上支付"></el-table-column>
          <el-table-column prop="offline_fee" label="线下支付"></el-table-column>
          <el-table-column prop="total_fee" label="订单金额"></el-table-column> -->
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, getSevenDateRange, divide } from '@/utils'
import { MEAL_TYPES } from '@/utils/constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
export default {
  name: 'CanteenMealReport',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    const defaultdate = getSevenDateRange(7)
    return {
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '时间',
          value: 'create_time',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '报餐时间',
              value: 'report_date'
            },
            {
              label: '用餐时间',
              value: 'dining_time'
            }
          ]
        },
        select_date: {
          clearable: false,
          type: 'daterange',
          value: [defaultdate[0], defaultdate[1]]
        },
        // meal_type: {
        //   type: 'select',
        //   value: '',
        //   label: '餐段',
        //   dataList: [
        //     { value: '', label: '全部' },
        //     ...MEAL_TYPES
        //   ]
        // },
        meal_type_list: {
          type: 'select',
          label: '餐段',
          value: [],
          multiple: true,
          placeholder: '请选择',
          collapseTags: true,
          dataList: MEAL_TYPES
        },
        organization_id: {
          type: 'organizationSelect',
          multiple: true,
          isLazy: false,
          checkStrictly: true,
          label: '消费点',
          value: [],
          placeholder: '请选择消费点'
        },
        payer_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          dataList: [],
          multiple: true,
          collapseTags: true,
          clearable: true
        },
        payer_department_group_ids: {
          type: 'organizationDepartmentSelect',
          multiple: true,
          checkStrictly: true,
          flat: false,
          label: '部门',
          value: [],
          placeholder: '请选择部门',
          dataList: [],
          limit: 1,
          level: 1,
          clearable: true
          // normalizer: this.departmentNode
        }
      },
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'CanteenMealReport',
      tableSetting: [
        { label: '日期', key: 'date' },
        { label: '消费点', key: 'organization_name' },
        { label: '餐段', key: 'meal_type_name' },
        { label: '成功报餐点餐笔数', key: 'success_count' },
        { label: '成功报餐份数', key: 'success_report_count' },
        { label: '就餐笔数', key: 'take_out_count' },
        { label: '就餐份数', key: 'take_out_report_count' },
        { label: '未就餐笔数', key: 'time_out_count' },
        { label: '未就餐份数', key: 'time_out_report_count' },
        { label: '停餐笔数', key: 'stop_count' },
        { label: '停餐份数', key: 'stop_report_count' },
        { label: '线上支付', key: 'online_fee' },
        { label: '线下支付', key: 'offline_fee' },
        { label: '订单金额', key: 'total_fee' }
      ],
      tableData: [],
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  created() {
    this.initPrintSetting()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getCanteenMealReport()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getCanteenMealReport()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getCanteenMealReport() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderReportMealCollectListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.tableData.push({
          date: '合计',
          success_count: res.data.collect_data.total_success_count,
          take_out_count: res.data.collect_data.total_take_out_count,
          time_out_count: res.data.collect_data.total_time_out_count,
          online_fee: res.data.collect_data.total_online_fee,
          offline_fee: res.data.collect_data.total_offline_fee,
          total_fee: res.data.collect_data.collect_total_fee,
          success_report_count: res.data.collect_data.total_success_report_count,
          take_out_report_count: res.data.collect_data.total_take_out_report_count,
          time_out_report_count: res.data.collect_data.total_time_out_report_count,
          stop_count: res.data.collect_data.stop_count,
          stop_report_count: res.data.collect_data.stop_report_count
        })
        this.tableData.map(item => {
          item.online_fee = divide(item.online_fee)
          item.offline_fee = divide(item.offline_fee)
          item.total_fee = divide(item.total_fee)
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getCanteenMealReport()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getCanteenMealReport()
    },
    // 表格垂直合并 // row当前行 rowIndex当前行号 column当前列 columnIndex当前列号
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const dataProvider = this.tableData
        const cellValue = row[column.property]
        if (cellValue) {
          const prevRow = dataProvider[rowIndex - 1]
          let nextRow = dataProvider[rowIndex + 1]
          if (prevRow && prevRow[column.property] === cellValue) {
            return { rowspan: 0, colspan: 0 }
          } else {
            let rowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              rowspan++
              nextRow = dataProvider[rowspan + rowIndex]
            }
            if (rowspan > 1) {
              return { rowspan, colspan: 1 }
            }
          }
        }
      }
    },
    handleExport() {
      const option = {
        type: 'ExportCanteenMealReport',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.canteen-summary {
  .top {
    margin-bottom: 20px;
    border-bottom: 1px solid #e7ecf2;
  }
}
</style>
