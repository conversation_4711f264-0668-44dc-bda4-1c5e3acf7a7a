<template>
  <!-- 添加/编辑 -->
  <dialog-message
    :show.sync="visible"
    :title="dialogTitle"
    :showFooter="showFooter"
    :loading.sync="isLoading"
    :width="dialogWidth"
    top="200px"
    class="returns-dialog"
    @closed="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <transition-group :name="slideTransition" v-on:after-enter="afterEnter">
      <div v-if="dialogType === 'returnType'" class="return-type" key="returnType">
        <div class="text-center m-b-20">
          <el-button size="medium" class="ps-btn" @click="changeDialogType('returnAll')">整批退货</el-button>
        </div>
        <div class="text-center">
          <el-button size="medium" class="ps-btn" @click="changeDialogType('returnMul')">批量退货</el-button>
        </div>
      </div>
      <div v-if="dialogType === 'returnAll' || dialogType === 'returnMul'" class="return-table" key="tableForm">
        <el-form v-loading="isLoading" ref="formRef" :model="formData" :rules="formRules" label-width="0" label-position="top" size="small">
          <div v-if="dialogType === 'returnMul'" class="m-b-20">
            <el-table
              :data="formData.tableData"
              ref="tableData"
              style="width: 100%"
              size="small"
              stripe
              header-row-class-name="ps-table-header-row"
              max-height="400"
              :row-key="rowKey"
              reserve-selection
              @selection-change="handleSelectionChange"
            >
              <table-column v-for="item in tableSettings" :key="item.key" :col="item">
                <template #count="{ row, index }">
                  <!-- 为什么要给个key呢，大概是使用了v-if,v-show,三元表达式等,对校验的el-form-item做了动态的判定，需要让变化后的key也发生改变,才能重新渲染,让校验恢复正常 -->
                  <!-- 简单来说就是rule这些属性只使用了第一次的渲染结果，后续不会根据row.select的值进行变化。所以需要给个变化的key让其根据值变化时重新渲染节点 -->
                  <el-form-item
                    :key="item.key + index + row.select"
                    label=""
                    :prop="'tableData.' + index + '.returnCount'"
                    :rules="row.select ? formRules.returnCount : []"
                    :width="item.width"
                    :show-message="false"
                    class="m-b-0 max-w-220"
                  >
                    <el-input v-model="row.returnCount" :disabled="!row.select" class=""></el-input>
                  </el-form-item>
                </template>
                <template #money="{ row, index }">
                  <el-form-item
                    :key="item.key + index + row.select"
                    label=""
                    :prop="'tableData.' + index + '.returnMoney'"
                    :rules="row.select ? formRules.returnMoney : []"
                    :width="item.width"
                    :show-message="false"
                    class="m-b-0 max-w-220"
                  >
                    <el-input v-model="row.returnMoney" :disabled="!row.select" class=""></el-input>
                  </el-form-item>
                </template>
                <!-- <template #operation="{ row }">
                  <el-button type="text" size="small" class="ps-text" @click="showViewHandle(row)">查看</el-button>
                  <el-button v-if="type === 'template'" type="text" size="small" class="ps-warn" @click="deleteHandle('single', row)">删除</el-button>
                </template> -->
              </table-column>
            </el-table>
            <!-- 分页 start -->
            <!-- <div v-if="total > pageSize" class="block" style="text-align: right">
              <pagination
                :onPaginationChange="onPaginationChange"
                :current-page.sync="currentPage"
                :page-size.sync="pageSize"
                :layout="'total, prev, pager, next, jumper'"
                :total="total"
              ></pagination>
            </div> -->
          </div>
          <div class="">
            <el-form-item label="退货说明" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                class="ps-textarea w-280"
                :rows="4"
                :maxlength="50"
              ></el-input>
            </el-form-item>
            <el-form-item label="上传附件" prop="imageFile">
              <el-upload
                ref="uploadFoodImage"
                :class="{'upload-food': true, 'hide-upload':formData.imageFileList.length>8}"
                drag
                :data="uploadParams"
                :action="actionUrl"
                :multiple="false"
                :file-list="formData.imageFileList"
                list-type="picture-card"
                :on-change="handelChange"
                :on-success="handleImgSuccess"
                :before-upload="beforeImgUpload"
                :limit="3"
                :headers="headersOpts"
              >
                <i v-if="formData.imageFileList.length<3" class="el-icon-plus"></i>
                <div slot="file" slot-scope="{file}" v-loading="file.status==='uploading'" element-loading-text="上传中">
                  <div class="upload-food-img"><img :src="file.url" alt=""></div>
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span class="el-upload-list__item-delete" @click="handleImgRemove(file, 'imageFile')">
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </transition-group>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { debounce, getSuffix, getToken, deepClone, divide, times } from '@/utils'
import { integer, positiveMoney, oneDecimal } from '@/utils/validata'
// 查看
export default {
  name: 'ReturnsDialog',
  props: {
    showreturns: Boolean,
    loading: Boolean,
    // 类型
    type: {
      type: String,
      default: 'static' // static静态数据，xhr通过请求获取的数据
    },
    // 请求的接口
    api: {
      type: String,
      default: 'apiBackgroundDrpExitInfoMaterialsRefundPost'
    },
    rowKey: {
      type: String,
      default: 'custon_id'
    },
    // 接口请求的参数
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    tableSettings: {
      type: Array,
      default() {
        return [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '物资名称', key: 'materials_name' },
          { label: '入库数量', key: 'entry_count' },
          { label: '当前数量', key: 'current_num' },
          { label: '单位', key: 'unit_name' },
          { label: '入库价', key: 'entry_fee', type: 'money' },
          { label: '合计金额', key: 'total_fee', type: 'money' },
          { label: '退货数量', key: 'returnCount', type: 'slot', slotName: 'count' },
          { label: '退货金额', key: 'returnMoney', type: 'slot', slotName: 'money' },
          { label: '供应商', key: 'supplier_manage_name' }
        ]
      }
    },
    // 静态数据
    staticList: {
      type: Array,
      default() {
        return []
      }
    },
    // 格式化请求回来的数据
    formatResult: Function
  },
  // mixins: [activatedLoadData],
  data() {
    const validateCount = (rule, value, callback) => {
      if (value) {
        if (!integer(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入数量'))
      }
    }
    let validataPrice = (rule, value, callback) => {
      if (value !== '') {
        if (!positiveMoney(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    return {
      isLoading: false,
      dialogTitle: '选择退货类型',
      dialogType: 'returnType',
      dialogWidth: '400px',
      dateRange: [],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      slideTransition: 'slide-left', // 默认切换动画
      formData: {
        tableData: [],
        imageFile: [],
        imageFileList: [],
        remark: ''
      },
      formRules: {
        remark: [{ required: true, message: '请填写退货说明', trigger: 'change' }],
        // returnCount: [{ validator: validateCount, trigger: 'change' }],
        // returnMoney: [{ validator: validataPrice, trigger: 'change' }],
        returnCount: [{ validator: this.validateCount, trigger: 'change' }],
        returnMoney: [{ validator: this.validataPrice, trigger: 'change' }],
      },
      uploadParams: {
        prefix: 'inventoryImage'
      },
      actionUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      // 图片放大查看
      dialogImageUrl: [],
      dialogVisible: false,
      showFooter: false, // 是否显示底部操作按钮
      selectList: [] // 选中的数据
    }
  },
  computed: {
    visible: {
      get() {
        return this.showreturns
      },
      set(val) {
        this.$emit('update:showreturns', val)
      }
    },
    // 当前table显示的数据
    // viewTableData() {
    //   return this.tableData.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
    // },
    // 总条数
    total() {
      if (this.type === 'static') {
        return this.staticList.length
      } else {
        return this.totalCount
      }
    }
  },
  watch: {
    showreturns(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 初始化
    init() {
      this.slideTransition = 'slide-left'
      // 每次打开时重置页码为第一页
      this.currentPage = 1
      let result = deepClone(this.staticList)
      this.formData.tableData = result.map(v => {
        // 自定义id，因为一个物资可能会对应多个供应商
        v.custon_id = `${v.id}_${v.supplier_manage_id}`
        v.returnCount = v.entry_count
        v.returnMoney = divide(v.total_fee)
        return v
      })
    },
    setDefaultSelectHandle() {
      this.$nextTick(_ => {
        this.formData.tableData.forEach(v => {
          this.$refs.tableData.toggleRowSelection(v, true)
        })
      })
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
    },
    changeDialogType(type) {
      this.dialogType = type
      this.showFooter = true
      this.dialogWidth = '860px'
      if (type === 'returnAll') {
        this.dialogTitle = '整批退货'
      }
      if (type === 'returnMul') {
        this.dialogTitle = '批量退货'
        // 设置选中
        this.setDefaultSelectHandle()
      }
    },
    afterEnter() {
    },
    // 设置文件名
    handelChange(file, fileList) {
      this.uploadParams.key =
        this.uploadParams.prefix + '_' + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    },
    // 上传成功数据处理
    handleImgSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.formData.imageFileList = fileList
        this.formData.imageFile.push(res.data.public_url)
        this.$nextTick(() => {
          this.$refs.formRef.clearValidate('imageFile')
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除图片
    handleImgRemove(file, type) {
      let index = this.formData[type + 'List'].findIndex(item => item.url === file.url)
      this.formData[type].splice(index, 1)
      this.formData[type + 'List'].splice(index, 1)
    },
    // 上传图片前校验文件类型
    beforeImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.svg']
      const isLt5M = file.size / 1024 / 1024 > 5
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是PNG/JPG/SVG格式!')
        return false
      }

      if (isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log('select', val)
      let selectId = []
      if (val) {
        selectId = val.map(v => v[this.rowKey])
      }
      // 设置选中数据的标识，校验时需要先判断当前数据是否选中，选中才校验输入框的值
      this.formData.tableData.forEach(v => {
        this.$set(v, 'select', selectId.includes(v[this.rowKey]))
      })
      this.selectList = val
    },
    // 校验输入
    validateCount(rule, value, callback, source, options) {
      // 校验走到这应该都会有这数据的
      // field：tableData.0.changeCount，tableData.1.changeCount，还有tableData[0].changeCount这种（先不做这种的兼容吧，记住prop的设置不能用这种）
      // 切割拿值做校验
      try {
        const fieldArr = rule.field.split('.')
        console.log(rule, value, fieldArr)
        const row = this.formData[fieldArr[0]][Number(fieldArr[1])]
        // console.log(111, row)
          if (value) {
            if (!oneDecimal(value)) {
              callback(new Error('格式错误'))
            } else {
              if (row && Number(row.current_num) < Number(value)) {
                callback(new Error('不能大于当前库存'))
              } else {
                callback()
              }
            }
          } else {
            callback(new Error('退货数量不能为空'))
          }
      } catch (error) {
        console.log(error)
        callback(new Error('校验出错了'))
      }
    },
    validataPrice(rule, value, callback, source, options) {
      // 校验走到这应该都会有这数据的
      // field：tableData.0.changeCount，tableData.1.changeCount，还有tableData[0].changeCount这种（先不做这种的兼容吧，记住prop的设置不能用这种）
      // 切割拿值做校验
      try {
        const fieldArr = rule.field.split('.')
        console.log(rule, value, fieldArr)
        const row = this.formData[fieldArr[0]][Number(fieldArr[1])]
        // console.log(111, row)
          if (value) {
            if (!positiveMoney(value)) {
              callback(new Error('格式错误'))
            } else {
              if (row && times(value) > row.total_fee) {
                callback(new Error('退货金额不能大于当前合计金额'))
              } else {
                callback()
              }
            }
          } else {
            callback(new Error('退货金额不能为空'))
          }
      } catch (error) {
        console.log(error)
        callback(new Error('校验出错了'))
      }
    },
    // 弹窗关闭
    handlerClose(e) {
      this.slideTransition = 'slide-right'
      this.isLoading = false
      this.showFooter = false
      this.dialogTitle = '选择退货类型'
      this.dialogType = 'returnType'
      this.dialogWidth = '400px'
      this.formData = {
        tableData: [],
        imageFile: [],
        imageFileList: [],
        remark: ''
      }
    },
    // 取消
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    // 检查是否
    checkedFormHandle() {
      let isPass = true
      if (!this.formData.remark) {
        isPass = false
      }
      for (let index = 0; index < this.formData.tableData.length; index++) {
        const item = this.formData.tableData[index];
        if (+item.returnCount > item.current_num) {
          isPass = false
        }
        if (times(item.returnMoney || 0) > item.total_fee) {
          isPass = false
        }
      }
      return isPass
    },
    // 格式化参数
    formatParams() {
      let params = {
        ...this.params,
        refund_remark: this.formData.remark,
        image_json: this.formData.imageFile
      }
      if (this.dialogType === 'returnAll') {
        params.refund_type = 'all_refund'
      } else {
        params.refund_type = 'bulk_refund'
        params.refund_info = this.selectList.map(v => {
          return {
            materials_id: v.id,
            refund_count: +v.returnCount,
            refund_fee: times(v.returnMoney),
            supplier_manage_id: v.supplier_manage_id
          }
        })
      }
      return params
    },
    // 确定
    clickConfirmHandle() {
      this.$refs.formRef.validate(valid => {
        console.log(valid)
        if (valid) {
          this.submitHandle(this.formatParams())
        }
      })
    },
    // 发送数据
    async submitHandle(params) {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpExitInfoMaterialsRefundPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '出错了')
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$emit('confirmReturn')
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
.returns-dialog {
  .el-dialog__body {
    position: relative;
    overflow: hidden;
    min-height: 128px;
    padding-top: 10px;
  }
  .return-type, .return-table {
    width: 100%;
    background-color: #fff;
  }
  .el-form-item {
    &.m-b-0 {
      margin-bottom: 0;
    }
    // &.is-error {
    //   margin-bottom: 18px;
    // }
    .el-input__inner {
      padding: 0 4px;
    }
  }
  .upload-food{
    overflow: hidden;
    max-height: 830px;
    &.hide-upload{
      .el-upload--picture-card{
        display: none;
      }
    }
    .el-upload--picture-card{
      border: none;
    }
    .el-upload-list__item {
      width: 128px;
      height: 128px;
    }
    .el-upload-dragger{
      width: 128px;
      height: 128px;
    }
    .upload-food-img{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 125px;
      height: 125px;
      img{
        max-width: 125px;
        max-height: 125px;
      }
    }
  }
}
</style>
