<template>
  <div class="special-date-picker">
    <el-popover
      placement="bottom"
      width="320"
      trigger="click">
      <div class="special-date-picker-top">
        <div class="special-date-picker-top-left">
          <i class="el-icon-d-arrow-left m-r-10" @click="changeDate('last', 'year')"></i>
          <i class="el-icon-arrow-left" @click="changeDate('last', 'month')"></i>
        </div>
        <span class="font-size-16">{{ dateType === 'day' ? showDate(selectedDate) : showMonth(selectedDate) }}</span>
        <div class="special-date-picker-top-right">
          <i class="el-icon-arrow-right" @click="changeDate('next', 'month')"></i>
          <i class="el-icon-d-arrow-right m-l-10" @click="changeDate('next', 'year')"></i>
        </div>
      </div>
      <date-table
        @pick="handleDatePick"
        :selection-mode="dateType"
        :first-day-of-week="firstDayOfWeek"
        :value="selectedDate"
        :default-value="defaultValue ? defaultValue : null"
        :date="date"
        :disabled-date="disabledDateHandle">
      </date-table>
      <div class="tip">
        <span v-if="dateType === 'day'" class="m-r-10"><span class="color-box origin"></span>已记录</span>
        <span><span class="color-box blue"></span>当前选择</span>
      </div>
      <el-input
        slot="reference"
        :value="dateType === 'day' ? showDate(selectedDate) : showDateRange(selectedDate)"
        size="small"
        class="w-220"
        prefix-icon="el-icon-date">
      </el-input>
    </el-popover>
  </div>
</template>

<script>
import { deepClone } from '@/utils';
import DateTable from './DateTable.vue'
import dayjs from 'dayjs'

export default {
  props: {
    selectedId: Number,
    showRecord: Boolean,
    selectType: String
  },
  components: {
    DateTable
  },
  data() {
    return {
      selectedDate: new Date(),
      firstDayOfWeek: 7, // 一周的以什么为开始
      dateType: 'day', // 左侧日历的类型，日/周的模式
      defaultValue: [], // 默认高亮的日期
      date: new Date(), // 当前显示的日历日期年月
      cellClassName: '',
      allowDate: [], // 允许点击的日期
      isClick: false
    }
  },
  watch: {
    showRecord: {
      handler: function(newVal) {
        if (newVal) {
          this.getdayAnalysisData()
        } else {
          this.selectedDate = new Date()
          this.date = new Date()
        }
      },
      immediate: true
    },
    selectType: {
      handler: function(newVal) {
        this.dateType = newVal
        switch (this.dateType) {
          case 'day':
            this.getdayAnalysisData()
            break;
          case 'week':
            this.getWeekAnalysisData()
            break;
        }
      },
      immediate: true
    }
  },
  computed: {
    showDate() {
      return d => {
        return dayjs(d).format('YYYY-MM-DD')
      }
    },
    showMonth() {
      return d => {
        return dayjs(d).format('YYYY 年 MM 月')
      }
    },
    showDateRange() {
      return d => {
        let str = dayjs(d).startOf('week').format('YYYY-MM-DD') + '   至   ' + dayjs(d).endOf('week').format('YYYY-MM-DD')
        return str
      }
    }
  },
  methods: {
    disabledDateHandle(time) {
      return !this.allowDate.includes(dayjs(time).format('YYYY-MM-DD')) || time > new Date().getTime()
    },
    handleDatePick(e) {
      this.isClick = true
      if (this.dateType === 'day') {
        this.selectedDate = e
      } else if (this.dateType === 'week') {
        // 周需要往前倒一天，因为控件默认从周3开始，但后台的数据默认周是从周日开始
        this.selectedDate = new Date(e.date.getTime() - 24 * 60 * 60 * 1000)
      }
      this.isLoading = true
      switch (this.dateType) {
        case 'day':
          this.getdayAnalysisData()
          break;
        case 'week':
          this.getWeekAnalysisData()
          break;
      }
    },
    changeDate(type, dateType) {
      this.isClick = false
      if (type === 'last') {
        this.selectedDate = new Date(dayjs(this.selectedDate).subtract(1, dateType).format('YYYY-MM-DD'))
      } else {
        this.selectedDate = new Date(dayjs(this.selectedDate).add(1, dateType).format('YYYY-MM-DD'))
      }
      this.date = this.selectedDate
      switch (this.dateType) {
        case 'day':
          this.getdayAnalysisData()
          break;
        case 'week':
          this.getWeekAnalysisData()
          break;
      }
    },
    getdayAnalysisData() {
      let params = {
        id: this.selectedId,
        month: dayjs(this.selectedDate).get('month') + 1,
        year: dayjs(this.selectedDate).get('year')
      }
      this.$apis.apiBackgroundHealthyHealthyInfoDayNutritionAnalyzeCalendarPost(params).then(res => {
        if (res.code === 0) {
          this.allowDate = deepClone(res.data) || []
          this.defaultValue = res.data.filter(v => {
            let data = new Date(v).getTime()
            let now = new Date(dayjs(new Date()).format('YYYY-MM-DD')).getTime()
            return now >= data
          })
          this.defaultValue = []
          res.data.forEach(v => {
            let data = new Date(v).getTime()
            let now = new Date(dayjs(new Date()).format('YYYY-MM-DD')).getTime()
            if (now >= data) {
              this.defaultValue.push(new Date(v))
            }
          })
          if (this.defaultValue.length) {
            // 默认拿最新那条
            this.selectedDate = this.isClick ? this.selectedDate : this.defaultValue[this.defaultValue.length - 1]
            this.date = new Date(dayjs(this.selectedDate))
            this.getDayHealthyInfo()
          } else {
            this.isLoading = false
            this.$emit('setHealthyInfo', '')
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取获取日的数据
    getWeekAnalysisData() {
      this.$apis.apiBackgroundHealthyHealthyInfoWeeklyNutritionAnalyzeCalendarPost({
        id: this.selectedId,
        month: dayjs(this.selectedDate).get('month') + 1,
        year: dayjs(this.selectedDate).get('year')
      }).then(res => {
        if (res.code === 0) {
          if (res.data && res.data.length) {
            this.allowDate = []
            let diff = 24 * 60 * 60 * 1000
            res.data.forEach((item, index) => {
              if (!index) this.date = new Date(item)
              this.allowDate.push(item)
              let time = new Date(item).getTime()
              for (let index = 1; index <= 6; index++) {
                const d = time + index * diff
                this.allowDate.push(dayjs(d).format('YYYY-MM-DD'))
              }
            })
            if (!res.data.length) {
              this.isLoading = false
              this.$emit('setHealthyInfo', '')
            } else {
              this.selectedDate = this.isClick ? this.selectedDate : new Date(res.data[res.data.length - 1])
              this.date = this.selectedDate
              this.getWeekHealthyInfo()
            }
            this.defaultValue = []
          } else {
            this.$emit('setHealthyInfo', '')
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getDayHealthyInfo() {
      this.$apis.apiBackgroundHealthyHealthyInfoDayNutritionAnalyzePost({
        id: this.selectedId,
        date: dayjs(this.selectedDate).format('YYYY-MM-DD')
      }).then(res => {
        if (res.code === 0) {
          this.$emit('setHealthyInfo', res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getWeekHealthyInfo() {
      this.$apis.apiBackgroundHealthyHealthyInfoWeeklyNutritionAnalyzePost({
        id: this.selectedId,
        date: dayjs(this.selectedDate).format('YYYY-MM-DD')
      }).then(res => {
        if (res.code === 0) {
          this.$emit('setHealthyInfo', res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.special-date-picker {
  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0px 20px;
    &-left {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-right {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.tip {
  margin-top: 20px;
  .color-box {
    display: inline-block;
    width: 14px;
    height: 14px;
    vertical-align: middle;
    margin-right: 4px;
    &.origin {
      background-color: #429cf5;
    }
    &.blue {
      margin-left: 10px;
      background-color: #ff9b45;
    }
  }
}
</style>
