<template>
  <div class="SupplierManagement container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon v-permission="['background_drp.supplier_manage.add']" color="origin" type="add" @click="gotoAddHandle('add')">新建供应商</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button v-permission="['background_drp.materials.supplier_manage_details']" type="text" size="small" class="ps-text" :disabled="organization !== row.organization_id" @click="gotoRelatedMaterials(row)">关联物资</el-button>
              <el-button v-permission="['background_drp.materials.supplier_manage_details']" type="text" size="small" class="ps-text" :disabled="organization !== row.organization_id" @click="clickCycleHandle(row)">结算周期</el-button>
              <!-- <el-button v-permission="['background_drp.materials.supplier_manage_details']" type="text" size="small" class="ps-text" :disabled="organization !== row.organization_id" @click="clickEvaluateHandle(row)">查看评价</el-button> -->
              <el-button v-permission="['background_drp.supplier_manage.modify']" type="text" size="small" class="ps-text" :disabled="organization !== row.organization_id" @click="gotoAddHandle('modify', row)">编辑</el-button>
              <el-button v-permission="['background_drp.supplier_manage.delete']" type="text" size="small" class="ps-text" :disabled="organization !== row.organization_id" @click="deleteHandle('del', row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right; ">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <cycle-dialog
      :showdialog.sync="showCycleDialog"
      :supplierManageId="supplierManageId"
      :oldFormData="formData"
      @confirmForm="confirmCycleHandle"
    ></cycle-dialog>
    <supplier-evaluate-dialog
      :showdialog.sync="showEvaluateDialog"
    ></supplier-evaluate-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce } from '@/utils'
import { mapGetters } from 'vuex'
import CycleDialog from '../components/CycleDialog'
import SupplierEvaluateDialog from '../components/SupplierEvaluateDialog'

export default {
  name: 'MaterialWarehouse',
  components: {
    CycleDialog,
    SupplierEvaluateDialog
  },
  mixins: [exportExcel],
  data() {
    return {
      importLink: '',
      supplierId: '',
      tableData: [],
      isLoading: false, // 刷新数据
      searchFormSetting: {
        name: {
          type: 'input',
          value: '',
          label: '供应商名称',
          placeholder: '请输入供应商名称',
          maxlength: 20
        }
      },
      tableSettings: [
        { label: '编号', key: 'supplier_no' },
        { label: '供应商名称', key: 'name' },
        { label: '联系人名称', key: 'contact_name' },
        { label: '联系电话', key: 'contact_phone' },
        { label: '供应商地址', key: 'address', showTooltip: true },
        { label: '登录账号', key: 'login_account' },
        // { label: '来源', key: 'organization_name1' },
        { label: '所属组织', key: 'organization_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right" }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      showCycleDialog: false,
      showEvaluateDialog: false,
      supplierManageId: 0,
      formData: {}
    }
  },
  computed: {
    ...mapGetters(['organization'])
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSupplierManagementList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getSupplierManagementList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getSupplierManagementList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDrpSupplierManageListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    deleteHandle(type, data) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const res = await this.$apis.apiBackgroundDrpSupplierManageDeletePost({
              id: data.id
            })
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getSupplierManagementList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSupplierManagementList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    gotoAddHandle(type, data) {
      let query = {}
      if (data) {
        query.id = data.id
      }
      this.$router.push({
        name: type === 'add' ? 'AddSupplierManagementMiddle' : 'AddSupplierManagement',
        params: {
          type
        },
        query
      })
    },
    gotoRelatedMaterials(data) {
      this.$router.push({
        name: 'RelatedMaterials',
        params: {
        },
        query: {
          id: data.id
        }
      })
    },
    clickCycleHandle(row) {
      this.supplierManageId = row.id
      this.formData = row.settlement_interval || {}
      this.showCycleDialog = true
    },
    confirmCycleHandle() {
      this.getSupplierManagementList()
    },
    clickEvaluateHandle(row) {
      this.showEvaluateDialog = true
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
