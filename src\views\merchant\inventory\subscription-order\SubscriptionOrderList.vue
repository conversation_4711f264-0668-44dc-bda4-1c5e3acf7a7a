<template>
  <div class="inquiry-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="showDraft = true">草稿箱</button-icon>
          <button-icon color="origin" v-permission="['background_drp.subscribe_info.add']" @click="gotoHandle('add')">新建申购单</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
              <el-button
                v-if="row.deal_result === 'to_purchase_info'"
                type="text"
                size="small"
                class="ps-text"
                @click="showVieWHandle('to_purchase_info', row)"
              >
                查看采购单
              </el-button>
              <el-button
                v-if="row.deal_result === 'to_exit_info'"
                type="text"
                size="small"
                class="ps-text"
                @click="showVieWHandle('to_exit_info', row)"
              >
                查看出库单
              </el-button>
              <el-button v-show="row.subscribe_status === 'initiated' && row.approve_status === 'REJECT'" v-permission="['background_drp.subscribe_info.initiated']" type="text" size="small" class="ps-text" @click="clickOperationHandle('re_initiated', row)">
                重新发起
              </el-button>
              <el-button v-show="row.subscribe_status === 'not_initiated' && row.approve_status === 'AGREE'" v-permission="['background_drp.subscribe_info.initiated']" type="text" size="small" class="ps-text" @click="clickOperationHandle('initiated', row)">
                发起
              </el-button>
              <el-button v-show="(row.subscribe_status === 'initiated' && row.approve_status === 'REJECT') || row.subscribe_status === 'not_initiated'" v-permission="['background_drp.subscribe_info.delete']" type="text" size="small" class="ps-text" @click="clickOperationHandle('delete', row)">
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- 草稿箱 star -->
    <draft-box-dialog
      :showdialog.sync="showDraft"
      :api="draftApi"
      :params="draftParams"
      :tableSettings="drafttableSettings"
      @recovery="recoveryHandle"
    />
    <!-- 草稿箱 end -->
    <view-dialog
      :showview.sync="showViewDialog"
      type="static"
      title="选择单据"
      :tableSettings="viewTableSettings"
      :staticList="viewStaticList"
      @operation="operationViewHandle"
    ></view-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import DraftBoxDialog from '../components/DraftBoxDialog'
import ViewDialog from '../components/ViewDialog'

export default {
  name: 'SubscriptionOrderList',
  components: { DraftBoxDialog, ViewDialog },
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '创建时间', key: 'create_time' },
        { label: '单据编号', key: 'trade_no' },
        { label: '单据状态', key: 'subscribe_status_alias' },
        { label: '审批状态', key: 'approve_status_alias' },
        { label: '处理结果', key: 'deal_result_alias' },
        { label: '申请人', key: 'subscribe_person_name' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '创建时间',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        },
        approve_status: {
          type: 'select',
          label: '审批状态',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '待审批',
              value: 'PENDING'
            },
            // {
            //   label: '审批中',
            //   value: 'PROCESSING'
            // },
            {
              label: '审批通过',
              value: 'AGREE'
            },
            {
              label: '拒绝申请',
              value: 'REJECT'
            }
            // {
            //   label: '撤销申请',
            //   value: 'REVOKE'
            // }
          ]
        },
        deal_result: {
          type: 'select',
          label: '处理结果',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '转采购单',
              value: 'to_purchase_info'
            },
            {
              label: '转出库单',
              value: 'to_exit_info'
            }
          ]
        },
        subscribe_person_name: {
          type: 'input',
          value: '',
          label: '申请人',
          placeholder: '请输入'
        },
        subscribe_status: {
          type: 'select',
          label: '单据状态',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '已发起',
              value: 'initiated'
            },
            {
              label: '未发起',
              value: 'not_initiated'
            }
          ]
        }
      },
      // 草稿弹窗
      showDraft: false,
      drafttableSettings: [
        { label: '草稿名称', key: 'name' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right' }
      ],
      draftApi: 'apiBackgroundDrpTemplateInfoTempListPost',
      draftParams: {
        inventory_info_type: 'subscribe_info',
        temp_type: 'draft'
      },
      showViewDialog: false,
      viewDialogType: '',
      viewStaticList: [],
      viewTableSettings: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.$route.query.currentPage) {
        this.currentPage = Number(this.$route.query.currentPage)
      }
      this.getSubscriptionOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key.indexOf('time') < 0) {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 切换tab
    changeTabHandle(e) {
      this.searchHandle()
      this.replaceHash()
    },
    // 保存下参数
    replaceHash() {
      this.$router.replace({
        name: 'SubscriptionOrderList',
        query: {
          currentPage: this.currentPage
        }
      })
    },
    // 获取列表数据
    async getSubscriptionOrderList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoListPost(params))
      // this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSubscriptionOrderList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = ''
      switch (type) {
        case 're_initiated':
          title = '确定重新发起吗？'
          apiUrl = 'apiBackgroundDrpSubscribeInfoReInitiatedPost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break
        case 'delete':
          title = '确定删除吗？'
          apiUrl = 'apiBackgroundDrpSubscribeInfoDeletePost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break
        case 'initiated':
          title = '确定发起吗？'
          apiUrl = 'apiBackgroundDrpSubscribeInfoInitiatedPost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break
      }
    },
    // 显示操作弹窗
    showOperationDialog(title, apiUrl, params) {
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getSubscriptionOrderList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 跳转
    gotoHandle(type, row) {
      // 新增
      if (type === 'add') {
        this.$router.push({
          name: 'AddSubscriptionOrder',
          query: this.$route.query,
          params: {
            type
          }
        })
        return
      }
      if (type === 'modify') {
        this.$router.push({
          name: 'AddSubscriptionOrder',
          query: {
            ...this.$route.query,
            id: row.id
          },
          params: {
            type
          }
        })
        return
      }
      // 详情
      if (type === 'detail') {
        this.$router.push({
          name: 'SubscriptionOrderDetail',
          query: {
            id: row.id,
            ...this.$route.query
          }
        })
        return
      }
    },
    // 导出
    handleExport(row) {
      const option = {
        type: 'InquiryList',
        url: 'apiBackgroundDrpInquiryExportInquiryPost',
        params: {
          id: row.id
        }
      }
      this.exportHandle(option)
    },
    // 草稿恢复编辑
    recoveryHandle(data) {
      let query = {
        ...this.$route.query,
        id: data.id, // 草稿id
        type: 'recovery' // 类型
      }
      this.$router.push({
        name: 'AddSubscriptionOrder',
        query: query,
        params: {
          type: 'add'
        }
      })
    },
    showVieWHandle(type, data) {
      this.viewDialogType = type
      if (type === 'to_purchase_info') {
        this.viewTableSettings = [
          { label: '单据编号', key: 'trade_no' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', operationList: [{ label: '查看', key: 'check' }] }
        ]
        this.viewStaticList = data.purchase_info
      }
      if (type === 'to_exit_info') {
        this.viewTableSettings = [
          { label: '单据编号', key: 'trade_no' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', operationList: [{ label: '查看', key: 'check' }] }
        ]
        this.viewStaticList = data.exit_info
      }
      this.showViewDialog = true
    },
    operationViewHandle(type, data) {
      let query = {
        id: data.id,
        warehouse_id: data.warehouse_id,
        warehouse_name: data.warehouse_name
      }
      let pageName = ''
      if (this.viewDialogType === 'to_purchase_info') {
        pageName = 'PurchaseDetail'
      }
      if (this.viewDialogType === 'to_exit_info') {
        pageName = 'OutboundOrderDetail'
      }
      this.$router.push({
        name: pageName,
        query: query
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.inquiry-list {
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
}
</style>
