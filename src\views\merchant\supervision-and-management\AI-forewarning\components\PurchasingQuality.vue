<template>
  <div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item"></table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
  </div>
</template>

<script>
import { debounce, deepClone } from '@/utils'
import dayjs from 'dayjs'

export default {
  data() {
    return {
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '预警时间',
          clearable: false,
          value: [
            dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD')
          ]
        },
        drp_type: {
          type: 'select',
          label: '类型',
          value: '',
          placeholder: '请选择类型',
          isLinkage: true,
          dataList: []
        }
      },
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '预警时间', key: 'warn_time' },
        { label: '类型', key: 'drp_type_alias' },
        { label: '预警情况', key: 'materials_name' }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    getDataList() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>
