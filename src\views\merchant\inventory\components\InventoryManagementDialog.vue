<template>
  <!-- dialog start -->
  <dialog-message :show.sync="showDialog" :loading="dialogLoading" title="设置物资" width="435px" footer-center @close="closeDialog">
    <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogrules" label-position="left" label-width="120px" size="medium">
      <el-form-item label="物资库存上限" prop="upperLimit" :rules="dialogrules.number">
        <el-input v-model="dialogForm.upperLimit" :maxlength="6" :disabled="isDisabled" class="ps-input w-100"></el-input>
      </el-form-item>
      <el-form-item label="物资库存下限" prop="lowerLimit" :rules="dialogrules.number">
        <el-input v-model="dialogForm.lowerLimit" :maxlength="6" :disabled="isDisabled" class="ps-input w-100"></el-input>
      </el-form-item>
      <el-form-item label="物资临近" prop="nearExpired" :rules="dialogrules.number" label-width="80px">
        <el-input v-model="dialogForm.nearExpired" :maxlength="6" :disabled="isDisabled" class="ps-input w-100"></el-input>
        <span class="m-l-10">天预警告示</span>
      </el-form-item>
    </el-form>
    <div slot="tool" class="footer-center">
      <el-button :disabled="dialogLoading" class="ps-cancel-btn w-150" @click="cancleDialog">取消</el-button>
        <el-button :disabled="dialogLoading" class="ps-btn w-150" type="primary" @click="confirmDialog">确定</el-button>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
// import { to } from '@/utils'
import { validateNumber } from '@/utils/form-validata'

export default {
  name: '',
  components: {
  },
  props: {
    show: {
      type: Boolean
    },
    type: {
      type: String,
      default: 'add'
    },
    infoData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    closehandle: Function,
    confirmhandle: Function
  },
  data() {
    return {
      showDialog: false,
      dialogLoading: false,
      dialogContent: '',
      dialogForm: {
        upperLimit: '',
        lowerLimit: '',
        nearExpired: ''
      },
      dialogrules: {
        number: [{ validator: validateNumber, trigger: "change" }]
      }
    }
  },
  computed: {
    isDisabled (val) {
      return this.type === 'modify'
    }
  },
  watch: {
    show(value) {
      this.showDialog = value
      if (value) {
        this.initData()
      }
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    initData() {
      this.dialogForm = {
        upperLimit: this.infoData.upper_limit,
        lowerLimit: this.infoData.lower_limit,
        nearExpired: this.infoData.near_expired
      }
    },
    // 重置表单
    resetForm() {
      this.$refs.dialogFormRef && this.$refs.dialogFormRef.resetFields()
    },
    // 关闭回调
    closeDialog() {
      this.closehandle && this.closehandle()
    },
    // 取消
    cancleDialog() {
      this.closehandle && this.closehandle()
    },
    // 确定
    confirmDialog() {
      this.$refs.dialogFormRef.validate(async valid => {
        if (valid) {
          if (this.dialogLoading) return
          if (Number(this.dialogForm.upperLimit) === Number(this.dialogForm.lowerLimit)) return this.$message.error('物资库存上限和下限不能相同')
          if (Number(this.dialogForm.upperLimit) < Number(this.dialogForm.lowerLimit)) return this.$message.error('物资库存上限必须大于下限')
          this.dialogLoading = true
          const [err, res] = await this.$to(
            this.$apis.apiBackgroundDrpInventoryInfoModifyPost({
              inventoryinfo_id: Number(this.infoData.id),
              upper_limit: Number(this.dialogForm.upperLimit),
              lower_limit: Number(this.dialogForm.lowerLimit),
              near_expired: Number(this.dialogForm.nearExpired)
            })
          )
          this.dialogLoading = false
          if (err) {
            this.$message.error(err.message)
            return
          }
          if (res.code === 0) {
            this.$message.success(res.msg || '成功')
            this.resetForm()
            this.confirmhandle && this.confirmhandle()
          } else {
            this.$message.error(res.msg)
          }
        }
      })
    }
  }
};
</script>

<style scoped lang="scss">

</style>
