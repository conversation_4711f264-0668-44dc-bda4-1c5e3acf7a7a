<template>
  <div class="reference-data-card">
    <div class="reference-data-card-header">
      <div class="reference-data-card-header-title">{{ title }}</div>
      <div v-if="showIncome">{{ totalTitle }}： {{ totalIncome }} 元</div>
    </div>
    <div class="reference-data-card-body">
      <div class="reference-data-card-body-item" v-for="(item, index) in referenceData" :key="index">
        <div class="reference-data-card-body-item-text">
          <div class="label">{{ item.label }}：</div>
          <div class="text">
            <span>{{ item.amount }}{{ item.unit }}</span>
            <span v-if="isRatio">，{{ item.ratio }}%</span>
          </div>
        </div>
        <el-button type="text" class="el-btn" v-if="item.showButton" @click="showDrawer(item.amount)">修改</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { type } from '@/utils/type';

export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    referenceData: {
      type: Array,
      default: () => {
        return []
      }
    },
    isRatio: {
      type: Boolean,
      default: false
    },
    showIncome: {
      type: Boolean,
      default: false
    },
    totalIncome: {
      type: String,
      default: "0.00"
    },
    totalTitle: {
      type: String,
      default: "合计收入"
    }
  },
  data() {
    return {}
  },
  methods: {
    showDrawer(count) {
      this.$emit('showDrawer', count)
    }
  }
}
</script>

<style lang="scss" scoped>
.reference-data-card {
  font-size: 14px;
  background-color: #F8F9FA;
  padding: 0 20px 12px;
  border-radius: 16px;
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 1px solid #E7ECF2;
    &-title {
      padding: 10px 0;
      font-size: 16px;
      font-weight: 900;
    }
  }
  &-body {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    &-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      &-text {
        max-width: 100%;
        margin-right: 10px;
        padding: 4px 0px;
        display: flex;
      }
      .el-btn {
        padding: 4px 0px !important;
      }
    }
  }
}
</style>
