<template>
  <div class="GoodsCategory container-wrapper">
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addOrEditGoodsCategory('add')" v-permission="['background_store.goods_category.add']">
            新增
          </button-icon>
          <button-icon color="origin" type="add" @click="addOrEditGoodsCategory('batch')" v-permission="['background_store.goods_category.add']">
            批量新增
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button
                class="ps-origin"
                type="text"
                size="small"
                @click="addOrEditGoods('add', row)"
                v-permission="['background_store.goods.add']"
              >
                新增商品
              </el-button>
              <el-button type="text" size="small" @click="addOrEditGoodsCategory('modify', row)" v-permission="['background_store.goods_category.modify']">
                编辑
              </el-button>
              <el-button type="text" size="small"
              :disabled="row.goods_nums > 0"
              class="ps-red" @click="clickDelete(row)"
              v-permission="['background_store.goods_category.delete']">
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <add-goods-dialog
      v-if="deviceGoodsVisible"
      :isshow.sync="deviceGoodsVisible"
      :type="type"
      :goodsCategoryId="goodsCategoryId"
      @confirm="initLoad"
    />
    <goods-category-dialog
      v-if="goodsCategoryDialogVisible"
      :isshow.sync="goodsCategoryDialogVisible"
      :type="goodsCategoryTypeDialog"
      :title="goodsCategoryTitleDialog"
      @confirm="initLoad"
      :dialog-info="dialogInfo"
    />
  </div>
</template>
<script>
import { debounce, to } from '@/utils'
import AddGoodsDialog from '../GoodsInfo/AddGoodsDialog.vue'
import GoodsCategoryDialog from './GoodsCategoryDialog.vue'
export default {
  name: 'GoodsCategory',
  components: { GoodsCategoryDialog, AddGoodsDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      totalPageSize: 0,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '序号', type: 'index', width: '80' },
        { label: '分类名称', key: 'name' },
        { label: '商品数量', key: 'goods_nums' },
        { label: '创建时间', key: 'create_time' },
        {
          label: '操作',
          key: 'operation',
          type: 'slot',
          slotName: 'operation',
          fixed: 'right',
          width: '220'
        }
      ],
      goodsCategoryDialogVisible: false, // 新增 批量新增 修改
      goodsCategoryTypeDialog: 'add',
      goodsCategoryTitleDialog: '',
      dialogInfo: {},
      deviceGoodsVisible: false,
      goodsCategoryId: '',
      searchFormSetting: {
        name: {
          type: 'input',
          label: '分类名称',
          value: '',
          placeholder: '请输入分类名称'
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.currentPage = 1
      this.getGoodsCategoryList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getGoodsCategoryList()
      }
    }, 300),
    async getGoodsCategoryList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategoryListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
      } else {
        this.$message.error(res.msg)
      }
    },
    addOrEditGoods(type, row) {
      this.type = type
      this.goodsCategoryId = row.id
      this.deviceGoodsVisible = true
    },
    clickDelete(row) {
      this.$confirm('确定删除该商品分类？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundStoreGoodsCategoryDeletePost({
                id: row.id
              })
            )
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                }
              }
              this.getGoodsCategoryList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 新增和批量新增
    addOrEditGoodsCategory(type, row) {
      let title = ''
      switch (type) {
        case 'add':
          title = '新增'
          break
        case 'batch':
          title = '批量新增'
          break
        case 'modify':
          title = '修改'
          this.dialogInfo = row
          break
        default:
          break
      }
      this.goodsCategoryTitleDialog = title
      this.goodsCategoryTypeDialog = type
      this.goodsCategoryDialogVisible = true
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getGoodsCategoryList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getGoodsCategoryList()
    }
  }
}
</script>
<style lang="scss" scoped></style>
