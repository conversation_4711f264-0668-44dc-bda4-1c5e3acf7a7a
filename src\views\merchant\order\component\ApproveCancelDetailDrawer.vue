<template>
  <div>
    <custom-drawer
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :wrapperClosable="false"
    size="760"
    destroy-on-close
    v-bind="$attrs"
    v-on="$listeners"
    :confirmShow="false"
    :cancelShow="false"
    @close="handlerClose"
    @cancel="clickCancleHandle"
  >
    <div class="drawer-container">
      <!-- 基本信息 -->
      <div class="info-section">
        <div class="section-title">基本信息</div>
        <div class="basic-info">
          <div class="user-avatar">
            <el-avatar v-if="drawerInfo.img_url" :size="60" :src="drawerInfo.img_url"></el-avatar>
            <el-avatar v-else :size="60" :src="require('@/assets/img/account-img.png')"></el-avatar>
          </div>
          <div class="user-details">
            <div class="detail-row">
              <span class="label">姓名：{{ drawerInfo.name }}</span>
              <span class="label m-l-20">部门：{{ drawerInfo.payer_department_group_name }}</span>
              <span class="label m-l-20">分组：{{ drawerInfo.payer_group_name }}</span>
            </div>
            <div class="detail-row">
              <span class="label">卡号：{{ drawerInfo.card_no }}</span>
              <span class="label m-l-20">人员编号：{{ drawerInfo.person_no }}</span>
              <span class="label m-l-20">手机号：{{ drawerInfo.phone }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核信息 -->
      <div class="info-section">
        <div class="section-title">审核信息</div>
        <div class="approval-info">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">审核编号：</span>
              <span class="value">{{ drawerInfo.review_no }}</span>
            </div>
            <div class="info-item">
              <span class="label">申请时间：</span>
              <span class="value">{{ drawerInfo.apply_time }}</span>
            </div>
            <div class="info-item">
              <span class="label">申请类型：</span>
              <span class="value">{{ drawerInfo.order_type_alias }}</span>
            </div>
            <div class="info-item">
              <span class="label">报餐时间：</span>
              <span class="value">{{ drawerInfo.dining_time }}</span>
            </div>
            <div class="info-item">
              <span class="label">申请原因：</span>
              <span class="value">{{ drawerInfo.review_type_alias }}</span>
            </div>
            <div class="info-item">
              <span class="label">申请餐段：</span>
              <span class="value">{{ drawerInfo.meal_type_alias }}</span>
            </div>
          </div>
          <div class="info-item">
            <span class="label">备注内容：</span>
            <span class="value">{{ drawerInfo.remark }}</span>
          </div>

          <!-- 费用信息 -->
          <div class="fee-info">
            <div class="fee-row">
              <div class="fee-item">
                <div class="fee-label">订单金额</div>
                <div class="fee-value">￥{{ drawerInfo.origin_fee | formatMoney }}</div>
              </div>
              <div class="fee-item">
                <div class="fee-label">优惠金额</div>
                <div class="fee-value">￥{{ drawerInfo.discount_fee | formatMoney }}</div>
              </div>
              <div class="fee-item">
                <div class="fee-label">补贴金额</div>
                <div class="fee-value">￥{{ drawerFormData.subsidy_fee | formatMoney }}</div>
              </div>
              <div class="fee-item">
                <div class="fee-label">服务费</div>
                <div class="fee-value">￥{{ drawerInfo.fuwu_fee | formatMoney }}</div>
              </div>
              <div class="fee-item">
                <div class="fee-label">手续费</div>
                <div class="fee-value">￥{{ drawerInfo.rate_fee | formatMoney }}</div>
              </div>
              <div class="fee-item">
                <div class="fee-label">实收金额</div>
                <div class="fee-value">￥{{ drawerInfo.real_fee | formatMoney }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 审核流程 -->
      <div class="info-section">
        <div class="section-title">审核流程</div>
        <div v-for="(flowItem, flowIndex) in flowInfo" :key="flowIndex" class="approval-flow">
          <div class="flow-header">第{{ flowIndex + 1 }}次提交审批流程</div>
          <div class="flow-timeline">
            <div
              v-for="(stepItem, stepIndex) in flowItem.approve_step_lists"
              :key="stepIndex"
              class="timeline-item"
              :class="{ 'active': stepItem.review_status === 'active', 'success': stepItem.review_status === 'success', 'reject': stepItem.review_status === 'reject' }"
            >
              <div class="timeline-icon">
                <i v-if="stepItem.review_status === 'success'" class="el-icon-check"></i>
                <i v-else-if="stepItem.review_status === 'reject'" class="el-icon-close"></i>
                <i v-else class="el-icon-user"></i>
              </div>
              <div class="timeline-content">
                <div class="step-header">
                  <span class="step-title">{{ stepItem.account_username }}</span>
                  <span class="step-time">{{ stepItem.finish_time }}</span>
                </div>
                <div class="step-operator" v-if="stepItem.type === 'user'">{{ stepItem.operator }}</div>
                <div class="step-operator" v-else>{{ stepItem.account_username }} {{ stepItem.review_status_alias }}</div>
                <div class="step-operator" v-if="stepItem.type === 'user'">申请备注：{{ stepItem.review_reason }}</div>
                <div class="step-operator" v-if="stepItem.type !== 'user' && stepItem.review_status === 'reject'">拒绝原因：{{ stepItem.remark }}</div>
                <div v-if="flowItem.apporve_account_lists && stepIndex === 0" class="step-content">
                  <div style="color: #000;">审批规则：{{ flowItem.approve_method_alias }}</div>
                  <div v-for="(contentItem, contentIndex) in flowItem.apporve_account_lists" :key="contentIndex" class="content-item">
                    <div>审批人{{ contentIndex + 1 }}：</div>
                    <div class="account-item" :class="getAccountItemClass(flowIndex, contentItem)">
                      <div v-for="(accountItem, accountidx) in contentItem.account_list" :key="accountidx">
                        <span class="account-name" :class="getAccountNameClass(flowIndex, accountItem.account_id)">{{ accountItem.account_username }}</span><span class="m-l-5 m-r-5" v-if="accountidx !== contentItem.account_list.length - 1">/</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="ps-el-drawer-footer">
      <el-button class="ps-cancel-btn m-l-20" @click="clickCancleHandle">取消</el-button>
      <el-button class="ps-btn" type="primary" @click="clickConfirmHandle('reject')">拒绝</el-button>
      <el-button class="ps-btn" type="primary" @click="clickConfirmHandle('agree')">同意</el-button>
    </div>
    </custom-drawer>

    <!-- 拒绝原因弹窗 -->
    <el-dialog
      title="请填写拒绝原因"
      :visible.sync="rejectDialogVisible"
      width="400px"
      top="30vh"
      custom-class="appeal-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <el-form
        ref="rejectFormRef"
        :model="rejectForm"
        :rules="rejectRules"
      >
        <el-form-item label="" prop="reason">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" class="ps-cancel-btn" :disabled="isLoading" @click="cancelReject">取消</el-button>
        <el-button size="small" type="primary" class="ps-origin-btn" :disabled="isLoading"  @click="confirmReject">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { confirm } from '@/utils/message'
import { to } from '@/utils'

export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    },
    title: {
      type: String,
      default: '详情'
    },
    drawerInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      // 拒绝弹窗相关
      rejectDialogVisible: false,
      rejectForm: {
        reason: ''
      },
      rejectRules: {
        reason: [
          { required: true, message: '请输入拒绝原因', trigger: 'blur' },
          { min: 1, max: 200, message: '拒绝原因长度在 1 到 200 个字符', trigger: 'blur' }
        ]
      },
      flowInfo: [],
      drawerFormData: {
        userInfo: {
          name: '张三',
          department: '部门名称1部门名称1部门名称1',
          group: '分组名称1',
          cardNo: 'AF2454F',
          employeeNo: '24541',
          phone: '18200962410',
          avatar: 'https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/images/logo.png'
        },
        approvalInfo: {
          approvalNo: 'SS260425126855427477',
          applyTime: '2025-04-26 15:28:55',
          approvalType: '取消申请',
          review_status: '审核中',
          applyDate: '2025-04-26',
          reason: '我要退款',
          mealPeriod: '早餐',
          remark: '哈哈哈哈我要退款哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈'
        },
        feeInfo: {
          orderAmount: '10.00',
          discountAmount: '2.00',
          subsidyAmount: '0.00',
          serviceAmount: '0.00',
          handlingFee: '0.00',
          actualAmount: '8.00'
        },
        flowInfo: [
          {
            flowTime: '2025年4月25日16:56:29',
            apporve_account_lists: [
              {
                account_list: [
                  {
                    account_id: 1,
                    account_username: '张三'
                  },
                  {
                    account_id: 2,
                    account_username: '李四'
                  },
                  {
                    account_id: 7,
                    account_username: '小郑'
                  }
                ]
              },
              {
                account_list: [
                  {
                    account_id: 3,
                    account_username: '王五'
                  },
                  {
                    account_id: 4,
                    account_username: '老六'
                  }
                ]
              },
              {
                account_list: [
                  {
                    account_id: 5,
                    account_username: '小刘'
                  },
                  {
                    account_id: 6,
                    account_username: '小黄'
                  }
                ]
              }
            ],
            steps: [
              {
                account_username: '用户名称',
                type: 'user',
                operator: '提交申请 (个人提交)',
                remark: '申请备注：hhhhhhhhhhhhhhhhhhhhh',
                finish_time: '2025年4月25日17:27:53',
                review_status: 'success'
              },
              {
                account_username: '张三',
                account_id: 1,
                type: 'approve_account',
                operator: '7456 (已同意)',
                finish_time: '2025年4月25日17:27:53',
                review_status: 'success'
              },
              {
                account_username: '王五',
                account_id: 3,
                type: 'approve_account',
                operator: '8872 (已拒绝)',
                finish_time: '2025年4月25日17:27:56',
                review_status: 'reject',
                remark: '拒绝原因：高效便民让市民获得优质的服务'
              }
            ]
          },
          {
            flowTime: '2025年4月25日16:56:29',
            apporve_account_lists: [
              {
                account_list: [
                  {
                    account_id: 1,
                    account_username: '张三'
                  },
                  {
                    account_id: 2,
                    account_username: '李四'
                  },
                  {
                    account_id: 7,
                    account_username: '小郑'
                  }
                ]
              },
              {
                account_list: [
                  {
                    account_id: 3,
                    account_username: '王五'
                  },
                  {
                    account_id: 4,
                    account_username: '老六'
                  }
                ]
              },
              {
                account_list: [
                  {
                    account_id: 5,
                    account_username: '小刘'
                  },
                  {
                    account_id: 6,
                    account_username: '小黄'
                  }
                ]
              }
            ],
            steps: [
              {
                account_username: '用户名称',
                type: 'user',
                operator: '提交申请 (个人提交)',
                remark: '申请备注：hhhhhhhhhhhhhhhhhhhhh',
                finish_time: '2025年4月25日17:27:53',
                review_status: 'success'
              },
              {
                account_username: '张三',
                account_id: 1,
                type: 'approve_account',
                operator: '7456 (已同意)',
                finish_time: '2025年4月25日17:27:53',
                review_status: 'success'
              },
              {
                account_username: '王五',
                account_id: 3,
                type: 'approve_account',
                operator: '8872 (已拒绝)',
                finish_time: '2025年4月25日17:27:56',
                review_status: 'reject',
                remark: '拒绝原因：高效便民让市民获得优质的服务'
              }
            ]
          }
        ]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.flowInfo = this.drawerInfo.approve_record.map(recordItem => {
          recordItem.approve_step_lists = [
            {
              account_username: this.drawerInfo.name,
              type: 'user',
              operator: '提交申请 (个人提交)',
              remark: '申请备注：hhhhhhhhhhhhhhhhhhhhh',
              review_reason: recordItem.review_reason,
              finish_time: recordItem.apply_time,
              review_status: 'success'
            },
            ...recordItem.approve_steps
          ]
          let apporveAccountList = []
          recordItem.account_info.map(accountItem => {
            let accountList = []
            accountItem.ids.map((idItem, idIndex) => {
              accountList.push({
                account_id: idItem,
                account_username: accountItem.names[idIndex]
              })
            })
            apporveAccountList.push({
              account_list: accountList
            })
          })
          recordItem.apporve_account_lists = apporveAccountList
          return recordItem
        })
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    clickConfirmHandle(type) {
      if (type === 'reject') {
        // 点击拒绝按钮，显示拒绝原因弹窗
        this.showRejectDialog()
      } else if (type === 'agree') {
        // 点击同意按钮，直接处理同意逻辑
        confirm({
          content: '<p>确定同意该笔订单的审批吗？</p><p style="color: red;">确定后不可撤销</p>'
        }).then(_ => {
          this.handleApprove('agree')
        })
      }
    },
    // 显示拒绝原因弹窗
    showRejectDialog() {
      this.rejectForm.reason = ''
      this.rejectDialogVisible = true
      this.$nextTick(() => {
        this.$refs.rejectFormRef && this.$refs.rejectFormRef.clearValidate()
      })
    },
    // 取消拒绝
    cancelReject() {
      this.rejectDialogVisible = false
      this.rejectForm.reason = ''
    },
    // 确认拒绝
    confirmReject() {
      this.$refs.rejectFormRef.validate((valid) => {
        if (valid) {
          this.handleApprove('reject')
        }
      })
    },
    // 处理审批操作
    async handleApprove(type) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      let api
      let params = {
        id: this.drawerInfo.id
      }
      if (type === 'agree') {
        api = this.$apis.apiBackgroundOrderOrderReviewOrderPaymentReviewSuccessPost
      } else if (type === 'reject') {
        api = this.$apis.apiBackgroundOrderOrderReviewOrderPaymentReviewRejectPost
        params.reject_reason = this.rejectForm.reason
      }
      const [err, res] = await to(api(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogVisible = false
        this.rejectDialogVisible = false
        this.$message.success(res.msg)
        this.$emit('confirm')
      } else {
        this.$message.error(res.msg)
      }
    },
    handlerClose() {
      this.visible = false
    },
    clickCancleHandle() {
      this.visible = false
    },
    getAccountNameClass(index, id) {
      let list = this.flowInfo[index].approve_step_lists.filter(item => item.account_id === id && item.type !== 'user')
      if (list.length) {
        if (list[0].review_status === 'success' || list[0].review_status === 'reject') return 'approved'
      }
    },
    getAccountItemClass(index, item) {
      let list = this.flowInfo[index].approve_step_lists.filter(itemIn => item.account_list.some(itemInIn => itemInIn.account_id === itemIn.account_id) && itemIn.type !== 'user')
      if (list.length) {
        if (list[0].review_status === 'success' || list[0].review_status === 'reject') return 'approved-item'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-container {
  padding: 15px 0;

  .info-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      border-left: 4px solid #ff9b45;
      // margin-left: -10px;
      padding-left: 15px;
      color: #23282d;
      min-width: 250px;
      line-height: 24px;
      height: 22px;
    }
  }

  // 基本信息样式
  .basic-info {
    display: flex;
    align-items: flex-start;
    margin: 16px;
    padding: 16px;
    background-color: #f3f3f3;
    border-radius: 8px;

    .user-avatar {
      margin-right: 20px;
    }

    .user-details {
      flex: 1;

      .detail-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
          font-weight: 500;
          width: 30%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  // 审核信息样式
  .approval-info {
    margin: 16px;
    padding: 16px;
    background-color: #f3f3f3;
    border-radius: 8px;
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px 40px;
      margin-bottom: 16px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          color: #666;
          font-weight: 500;
          min-width: 80px;
        }

        .value {
          color: #333;
          flex: 1;
        }
      }
    }

    .fee-info {
      margin-top: 20px;

      .fee-row {
        display: flex;
        justify-content: space-between;
        background-color: #fff;
        padding: 16px;
        border-radius: 8px;

        .fee-item {
          text-align: center;

          .fee-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
          }

          .fee-value {
            color: #333;
            font-weight: 600;
            font-size: 16px;
          }
        }
      }
    }
  }

  // 审核流程样式
  .approval-flow {
    margin: 16px;
    padding: 16px;
    background-color: #f3f3f3;
    border-radius: 8px;
    .flow-header {
      text-align: center;
      margin-bottom: 15px;
      font-weight: bold;
    }

    .flow-timeline {
      .timeline-item {
        display: flex;
        margin-bottom: 20px;
        position: relative;

        &:not(:last-child)::after {
          content: '';
          position: absolute;
          left: 9px;
          top: 28px;
          width: 2px;
          height: calc(100% - 10px);
          border-left: 2px dashed #d9d9d9;
        }

        &.success {
          .timeline-icon {
            background-color: #ff9b45;
            color: white;
          }
        }

        &.reject {
          .timeline-icon {
            background-color: #ff4d4f;
            color: white;
          }
        }

        &.active {
          .timeline-icon {
            background-color: #1890ff;
            color: white;
          }
        }

        .timeline-icon {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: #d9d9d9;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          margin-top: 4px;
          flex-shrink: 0;
          position: relative;
          z-index: 1;

          i {
            font-size: 14px;
          }
        }

        .timeline-content {
          flex: 1;

          .step-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;

            .step-title {
              color: #333;
            }

            .step-time {
              color: #999;
              font-size: 12px;
            }
          }

          .step-operator {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
          }

          .step-content {
            background-color: #fafafa;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 8px;

            .content-item {
              color: #333;
              font-size: 14px;
              line-height: 1.5;
              display: flex;
              align-items: center;
              margin-top: 10px;

              &:last-child {
                margin-bottom: 0;
              }

              .account-item{
                display: flex;
                align-items: center;
                border: 1px solid #999;
                border-radius: 5px;
                padding: 5px 10px;
                .account-name{
                  color: #999;
                }
                .approved{
                  color: #000;
                }
                .success {
                  color: #ff9b45;
                }
                .reject {
                  color: #ff4d4f;
                }
              }
              .approved-item{
                border: 1px solid #ff9b45;
              }
            }
          }

          .step-remark {
            color: #666;
            font-size: 14px;
            font-style: italic;
          }
        }
      }
      .timeline-item:last-child{
        margin-bottom: 0px;
      }
    }
  }
}
</style>
