<template>
  <div class="AddApproveRules container-wrapper">
    <div class="table-wrapper">
      <div class="table-header" style="display: flex; justify-content: space-between;">
        <div class="table-title">{{type==='add'?'新增':'编辑'}}规则</div>
      </div>
      <el-form
        v-loading="isLoading"
        :model="approveRuleInfo"
        ref="deviceForm"
        :rules="approveRuleFormRules"
        style="padding: 0 25px;"
        label-width="auto"
      >
        <div class="setting-wrap">
          <div class="title">基础设置</div>
          <el-form-item label="规则名称" prop="approveRuleName">
            <el-input v-model="approveRuleInfo.approveRuleName" :disabled="type==='detail'" max="20" class="ps-input w-300"></el-input>
          </el-form-item>
          <el-form-item label="审批类型" prop="approveRuleType">
            <el-select v-model="approveRuleInfo.approveRuleType" :disabled="type==='detail'" class="ps-select w-300">
              <el-option
                v-for="item in approveTypeList"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="setting-wrap">
          <div class="title">审批设置</div>
          <el-form-item label="审批方式" prop="approveMethod">
            <el-radio-group v-model="approveRuleInfo.approveMethod" :disabled="type==='detail'">
              <el-radio label="one_by_one_approve" class="ps-radio">依次审批（本环节内审批账号依次审批）</el-radio>
              <el-radio label="and_approve" class="ps-radio">会签（需所有审批账号同意）</el-radio>
              <el-radio label="or_approve" class="ps-radio">或签（一名审批账号同意或拒绝即可）</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批人" required>
            <el-form-item
              v-for="(item,index) in approveRuleInfo.approveAccounts"
              :key="'approveAccounts'+index"
              :label="'审批账号'+ (index + 1)"
              label-width="90px"
              class="approve-accounts"
              :prop="`approveAccounts[${index}]`"
              :rules="approveRuleFormRules.approveAccounts">
              <el-select
                multiple
                :collapse-tags="false"
                class="ps-input w-300"
                v-model="approveRuleInfo.approveAccounts[index]"
                filterable
                placeholder="请选择审批人"
                :disabled="type==='detail'">
                <el-option
                  v-for="account in accountList"
                  :key="account.id"
                  :label="account.name"
                  :value="account.id"
                  :disabled="account.disabled">
                </el-option>
              </el-select>
              <img src="@/assets/img/plus.png" alt="" @click="addApproveAccounts">
              <img src="@/assets/img/reduce.png" alt="" v-if="index > 0" @click="delApproveAccounts(index)">
            </el-form-item>
          </el-form-item>
          <el-form-item
            v-if="approveRuleInfo.approveRuleType === 'finance'"
            label="审批通过备注" :rules="approveRuleInfo.approveRuleType === 'finance' ? [{required: true, message: '此项为必填项', trigger: ['change', 'blur']}] : [{required: false}]">
            <el-radio-group v-model="approveRuleInfo.isNeedRemark">
              <el-radio :label="true">必填</el-radio>
              <el-radio :label="false">非必填</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <el-button size="small" type="primary" class="ps-origin-btn w-150 form-btn" @click="checkForm" :disabled="type==='detail'">保存</el-button>
      </el-form>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'AddApproveRules',
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      type: 'add',
      orderType: 'company',
      progress: '',
      approveRuleData: {},
      approveRuleInfo: {
        approveRuleName: '',
        approveRuleType: '',
        approveMethod: 'one_by_one_approve',
        approveAccounts: [''],
        isNeedRemark: false
      },
      approveRuleFormRules: {
        approveRuleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
        approveRuleType: [{ required: true, message: '请选择审批类型', trigger: 'blur' }],
        approveMethod: [{ required: true, message: '请选择审批方式', trigger: 'blur' }],
        approveAccounts: [{ required: true, message: '请选择审批账号', trigger: 'blur' }]
      },
      approveTypeList: [
        {
          key: 'approve_order_visitor',
          name: '访客就餐'
        },
        {
          key: 'register',
          name: '自注册'
        },
        {
          key: 'reservation',
          name: '预约取消'
        },
        {
          key: 'report_meal',
          name: '报餐取消'
        },
        {
          key: 'report_meal_pack',
          name: '餐包取消'
        }
      ],
      accountList: []
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'organization'
    ])
  },
  created() {
    console.log('userInfo', this.userInfo, this.organization)
    let orgData = this.userInfo.organizationList.filter(item => item.id === this.organization)
    console.log('orgData', orgData)
    if (orgData[0].level === 0) {
      this.approveTypeList = [
        ...this.approveTypeList,
        {
          key: 'fund',
          name: '资金上传'
        },
        {
          key: 'finance',
          name: '财务审批'
        }
      ]
    }
    if (this.$route.query.data) {
      this.approveRuleData = JSON.parse(this.$route.query.data)
      console.log('this.approveRuleData', this.approveRuleData)
    }
    if (this.$route.query.type) {
      this.type = this.$route.query.type
    }
    if (this.$route.query.rule_type) {
      this.orderType = this.$route.query.rule_type
    }
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.type === 'edit' || this.type === 'detail') {
        this.approveRuleInfo.approveRuleName = this.approveRuleData.name
        this.approveRuleInfo.approveRuleType = this.approveRuleData.approve_type
        this.approveRuleInfo.approveMethod = this.approveRuleData.approve_method
        this.approveRuleInfo.approveAccounts = this.approveRuleData.approve_json.account_ids
        this.approveRuleInfo.isNeedRemark = this.approveRuleData.is_remark
      }
      this.getAccountList()
    },
    checkForm() {
      if (this.type === 'detail') return
      this.$refs.deviceForm.validate(valid => {
        if (valid) {
          let params = {
            rule_type: this.orderType,
            name: this.approveRuleInfo.approveRuleName,
            approve_type: this.approveRuleInfo.approveRuleType,
            approve_method: this.approveRuleInfo.approveMethod,
            approve_json: {
              account_ids: this.approveRuleInfo.approveAccounts,
              approve_method: this.approveRuleInfo.approveMethod
            },
            is_remark: this.approveRuleInfo.approveRuleType === 'finance' ? this.approveRuleInfo.isNeedRemark : undefined
          }
          let api
          if (this.type === 'add') {
            api = this.$apis.apiBackgroundApproveApproveRuleAddPost
          } else if (this.type === 'edit') {
            params.id = this.approveRuleData.id
            api = this.$apis.apiBackgroundApproveApproveRuleModifyPost
          }
          this.saveSetting(params, api)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    async saveSetting(params, api) {
      const res = await api(params)
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    addApproveAccounts() {
      if (this.type === 'detail') return
      this.approveRuleInfo.approveAccounts.push('')
    },
    delApproveAccounts(index) {
      if (this.type === 'detail') return
      this.approveRuleInfo.approveAccounts.splice(index, 1);
    },
    async getAccountList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrganizationAccountListPost({
        page: 1,
        page_size: 9999,
        status: 1
      })
      this.isLoading = false
      if (res.code === 0) {
        this.accountList = res.data.results.map(item => {
          item.name = item.username + '-' + item.member_name
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
.AddApproveRules{
  .setting-wrap{
    box-shadow: 0px 0px 15px rgba(179, 180, 183, 0.3);
    padding: 20px;
    margin-top: 20px;
    .title{
      margin-bottom: 20px;
      font-weight: bold;
    }
  }
  .form-btn{
    margin: 20px 0;
  }
  .approve-accounts{
    margin-bottom: 10px;
    .el-form-item__content{
      display: flex;
      align-items: center;
      img {
        margin-left: 10px;
      }
    }
  }
}
</style>
