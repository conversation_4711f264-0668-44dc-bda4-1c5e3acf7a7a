<template>
  <div class="DepartmentReportCollect container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="export" @click="gotoExport" v-permission="['background_order.ordering_food.ordering_food_summary_export']">
            导出EXCEL
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          border
          header-row-class-name="ps-table-header-row"
          :span-method="arraySpanMethod"
          :row-class-name="tableRowClassName"
          @cell-mouse-enter="handleCellMouseEnter"
          @cell-mouse-leave="handleCellMouseLeave"
        >
        <!-- :row-class-name="addRowClassName" -->
          <el-table-column label="序号" prop="index" align="center" width="80px" :index="indexMethod"></el-table-column>
          <el-table-column prop="group_name" label="科室" align="center"></el-table-column>
          <el-table-column prop="dietary_status_alias" label="饮食" align="center">
            <template slot-scope="scope">
              <span :class="[dietaryClass[scope.row.dietary_status]]">{{ scope.row.dietary_status_alias }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="total_count" label="数量" align="center"></el-table-column>
          <el-table-column prop="reservation_date" label="就餐日期" align="center"></el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, replaceDate } from '@/utils'
import { DEPARTMENT_REPORT_COLLECT, dietaryStatus } from './constants'
import { mergeHandle, mergeRowAction } from '@/utils/table'

export default {
  name: 'DepartmentReportCollect',
  mixins: [exportExcel],
  data() {
    return {
      tableData: [],
      isLoading: false, // 刷新数据
      searchFormSetting: DEPARTMENT_REPORT_COLLECT,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      collect: [ // 统计
        { key: 'ALL', value: 0, label: '合计总数:' },
        { key: 'PS', value: 0, label: '普食:' },
        { key: 'LS', value: 0, label: '流食:' }
        // { key: 'TS', value: 0, label: '停送:' }
      ],
      dietaryStatus,
      selectList: [],
      formRules: {
        dietaryType: [{ required: true, message: '请选择' }],
        remark: [{ required: true, message: '请先填写备注' }]
      },
      mergeOpts: {
        useKeyList: {
          collect_key: ['index', 'group_name', 'dietary_status_alias'],
          total_key: ['total_count', 'reservation_date']
        } // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        // mergeKeyList: ['index', 'group_name'] // 通用的合并字段，根據值合并
      },
      dietaryClass: {
        PS: 'ps-span-text',
        LS: 'ls-span-text'
      },
      currentIndex: ''
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getDataList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getDataList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderingFoodOrderingFoodSummaryPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        let resultsObj = {} // 处理的结果 { date: { group_id: [] } }
        this.totalCount = res.data.count
        // this.tableData = res.data.results
        if (res.data.results && res.data.results.length) {
          res.data.results.forEach((v, index) => { // 循环处理不同分组的数据
            if (v.data && v.data.length) {
              let list = {} // 用于统计的数据
              v.data.forEach(item => {
                if (!list[item.reservation_date]) list[item.reservation_date] = [] // 初始化
                list[item.reservation_date].push({
                  group_name: v.group_name,
                  group_id: v.group_id,
                  ...item
                })
              })
              // console.log(list)
              // 计算统计数据, 当前统计规则同一天同一个分组执行分组.序号.饮食
              for (const key in list) {
                let countList = {} // 统计同一时间同一分组不同类型餐饮的数量
                list[key].forEach(countItem => {
                  if (!countList[countItem.dietary_status]) {
                    countList[countItem.dietary_status] = countItem.count
                  } else {
                    countList[countItem.dietary_status] = countList[countItem.dietary_status] + countItem.count
                  }
                })
                list[key].sort((a, b) => {
                  let str = a.dietary_status
                  return str.localeCompare(b.dietary_status)
                })
                // 重新赋值
                let id = v.group_id
                list[key].forEach(item => {
                  if (!resultsObj[key]) resultsObj[key] = {}
                  if (!resultsObj[key][id]) resultsObj[key][id] = []
                  if (resultsObj[key] && resultsObj[key][id]) {
                    resultsObj[key][id].push({
                      ...item,
                      total_key: `${key}-${id}-${item.dietary_status}`, // 用于合并相同时间相同分组相同饮食数量的key
                      total_count: countList[item.dietary_status],
                      collect_key: `${key}-${id}` // 合并相同时间相同分组的数据的key
                    })
                  }
                })
              }
            }
          })
        }
        let results = []
        let dateList = Object.keys(resultsObj)
        // 排序下，保证数据是按照时间降序
        dateList.sort((a, b) => {
          return new Date(replaceDate(b.reservation_date)).getTime() - new Date(replaceDate(a.reservation_date)).getTime()
        })
        // console.log(11, dateList)
        let index = 0
        dateList.forEach(v => {
          for (const key in resultsObj[v]) {
            index++;
            let item = resultsObj[v][key]
            let data = item.map(k => {
              return {
                ...k,
                index: index
              }
            })
            results.push(...data)
          }
        })
        this.tableData = results
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
        if (res.data.total) {
          res.data.total.forEach(v => {
            this.collect.forEach(item => {
              if (item.key === v.dietary_status) {
                item.value = v.count
              }
            })
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    addRowClassName({ row, rowIndex }) {
      // console.log(row, rowIndex)
      if (!(row.index % 2)) {
        return 'hover-row--striped'
      }
    },
    tableRowClassName({ row }) {
      return row.index === this.currentIndex ? 'row--striped' : '';
    },
    handleCellMouseEnter(row, column, cell, event) {
      this.currentIndex = row.index;
    },
    handleCellMouseLeave() {
      this.currentIndex = '';
    },
    // 列表序号
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'DepartmentReportCollect',
        url: 'apiBackgroundOrderOrderingFoodOrderingFoodSummaryExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
.DepartmentReportCollect{
  .ps-span-text {
    color: #4da4fe;
  }
  .ls-span-text {
    color: #1dcf88;
  }
  .row--striped{
    background-color: #f5f7fa;
    .el-table__cell{
      background-color: #f5f7fa;
    }
  }
}
</style>
