<template>
  <div class="store-goods container-wrapper">
    <div class="top-btn">
      <el-radio-group v-model="tabType" class="ps-radio-btn" @change="changeTab">
        <el-radio-button v-for="item in tabTypeList" :key="item.value" :label="item.value" v-permission="[`${item.permission}`]">
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
      <!-- <img @click="refreshHandle" class="refresh" src="@/assets/img/refresh.png" alt="" /> -->
    </div>
    <goods-info ref="storeGoods" v-if="tabType === 'info'"></goods-info>
    <goods-category ref="storeGoods" v-if="tabType === 'category'"></goods-category>
    <goods-unit ref="storeGoods" v-if="tabType === 'unit'"></goods-unit>
  </div>
</template>

<script>
// import { debounce } from '@/utils'
import GoodsInfo from './components/GoodsInfo/index'
import GoodsCategory from './components/GoodsCategory/index'
import GoodsUnit from './components/GoodsUnit/index'
export default {
  name: 'DeliverReport',
  components: { GoodsInfo, GoodsCategory, GoodsUnit },
  data() {
    return {
      tabType: 'info',
      tabTypeList: [
        {
          value: 'info',
          label: '商品信息',
          permission: 'background_store.goods.list'
        },
        {
          value: 'category',
          label: '商品分类',
          permission: 'background_store.goods_category.list'
        },
        {
          value: 'unit',
          label: '商品单位',
          permission: 'background_store.goods_unit.list'
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    changeTab() {},
    refreshHandle() {
      // this.$refs.deliverChild.resetSearchHandle()
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.store-goods {
  .top-btn {
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
  }
  .refresh {
    cursor: pointer;
    padding: 14px 0;
  }
}
</style>
