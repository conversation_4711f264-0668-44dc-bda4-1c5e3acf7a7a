<template>
  <div class="ApproveRules container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">满意度统计</div>
        <div class="align-r">
          <el-button class="ps-btn export-btn" size="mini" @click="gotoExport">导出</el-button>
        </div>
      </div>

      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column prop="quarter_alias" label="统计周期" align="center"></el-table-column>
          <el-table-column prop="reply_time" label="创建时间" align="center">
            <template slot-scope="scope">
              <div>{{ scope.row.create_time | formatDate }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="company_alias" label="所属项目" align="center"></el-table-column>
          <el-table-column prop="username" label="用户名" align="center">
            <template slot-scope="scope">
              {{ scope.row.username || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="手机号" align="center">
            <template slot-scope="scope">
              <el-tooltip effect="dark" :content="scope.row.phone" placement="top-start">
                <div class="ellipsis">
                  {{ scope.row.phone.slice(0, 3) + '****' + scope.row.phone.slice(7) }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="access_type_alias" label="统计终端" align="center"></el-table-column>
          <el-table-column prop="module_alias" label="功能" align="center"></el-table-column>
          <el-table-column prop="satisfied_alias" label="评价类型" align="center"></el-table-column>
          <el-table-column prop="tags" label="评价标签" align="center">
            <template slot-scope="scope">
              <el-tooltip v-if="scope.row.tags && scope.row.tags.length" effect="dark" placement="top-end">
                <div slot="content" style="max-width: 500px;">
                  {{ scope.row.tags.join('、') }}
                </div>
                <div class="ellipsis">{{ scope.row.tags.join('、') }}</div>
              </el-tooltip>
              <template v-else>
                <div class="ellipsis">--</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="不满意原因" align="center">
            <template slot-scope="scope">
              <el-tooltip v-if="scope.row.reason" effect="dark" placement="top-start">
                <div slot="content" style="max-width: 500px;">
                  {{ scope.row.reason }}
                </div>
                <div class="ellipsis">{{ scope.row.reason }}</div>
              </el-tooltip>
              <template v-else>
                <div class="ellipsis">--</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="reply" label="回复信息" align="center">
            <template slot-scope="scope">
              <el-tooltip v-if="!scope.row.is_satisfied" effect="dark" placement="top-start">
                <div slot="content" style="max-width: 500px;">
                  {{ scope.row.reply }}
                </div>
                <div class="ellipsis">{{ scope.row.reply }}</div>
              </el-tooltip>
              <template v-else>
                <div class="ellipsis">--</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="150">
            <template slot-scope="scope">
              <template v-if="!scope.row.is_satisfied">
                <el-button
                  v-if="scope.row.reply"
                  type="text"
                  size="small"
                  class="ps-bule"
                  @click="gotoAddOrEdit('edit', scope.row)"
                >
                  修改回复
                </el-button>
                <el-button v-else type="text" size="small" class="ps-bule" @click="gotoAddOrEdit('add', scope.row)">
                  回复
                </el-button>
              </template>
              <template v-else>
                <div>--</div>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <div style="display: flex; justify-content: space-between; align-items: center">
        <div class="font-size-14" style="padding-left: 20px">
          <span>满意人数：{{ totalData.satisfied_counts }}</span>
          <span style="margin-left: 50px">不满意人数：{{ totalData.unsatisfied_counts }}</span>
        </div>
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <customDrawer
      :show.sync="drawerEditorVisible"
      :loading="isDrawerEditorLoading"
      :fixedFooter="true"
      :title="'编辑'"
      :cancelText="'取 消'"
      cancelClass="ps-btn"
      @confirm="confirmDialogHandle('drawerFormDataRef')"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form :model="drawerFormData" ref="drawerFormDataRef" :rules="rulesDrawerForm" label-width="100px">
            <el-form-item label="统计终端">{{ drawerFormData.access_type_alias }}</el-form-item>
            <el-form-item label="评价类型">{{ drawerFormData.is_satisfied ? '滿意' : '不滿意' }}</el-form-item>

            <template v-for="(item, index) in drawerFormData.message_history">
              <div style="background: #f5f5f5; border-radius: 10px"  :key="index">
                <el-form-item label="功能">{{ item.module_alias }}</el-form-item>
                <el-form-item label="不满意原因">{{ item.reason }}</el-form-item>
                <el-form-item label="回复信息">{{ item.reply }}</el-form-item>
              </div>
            </template>

            <div style="background: #f5f5f5; border-radius: 10px; padding-bottom: 10px">
              <el-form-item label="功能">{{ drawerFormData.module_alias }}</el-form-item>
              <el-form-item label="评价标签">
                {{ drawerFormData.tags && drawerFormData.tags.length ? drawerFormData.tags.join('、') : '' }}
              </el-form-item>
              <el-form-item label="不满意原因" style="word-wrap: break-word;">{{ drawerFormData.reason }}</el-form-item>
              <el-form-item label="回复信息" prop="reply" required>
                <el-input
                  type="textarea"
                  style="width: 80%"
                  :rows="4"
                  placeholder="请输入回复信息"
                  show-word-limit
                  maxlength="100"
                  v-model="drawerFormData.reply"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { to, getRequestParams, deepClone, debounce } from '@/utils'
import dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'ApproveRulesList',
  mixins: [exportExcel],
  data() {
    return {
      // 满意度 抽屉弹窗
      rulesDrawerForm: {
        reply: [{ required: true, message: '请输入回复信息', trigger: ['blur', 'change'] }]
      },
      // 动态存储所有模块类型数据
      moduleData: {},
      drawerEditorVisible: false,
      isDrawerEditorLoading: false,
      drawerFormData: {
        module_alias: '',
        tips: '',
        is_every_day: false,
        enable: false
      },

      totalData: {
        satisfied_counts: 0,
        total_users: 0,
        unsatisfied_counts: 0
      },
      isLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: {
        quarter: {
          type: 'select',
          label: '统计周期',
          value: '',
          clearable: true,
          placeholder: '',
          dataList: []
        },
        select_time: {
          type: 'daterange',
          label: '评价时间',
          value: [],
          clearable: true
        },
        company_ids: {
          type: 'CompanySelect',
          value: '',
          label: '所属项目',
          dataList: [],
          multiple: true,
          collapseTags: true,
          checkStrictly: true,
          clearable: true,
          labelWidth: '100px',
          companyOpts: {
            label: 'name',
            value: 'company'
          },
          role: 'super'
        },
        username: {
          label: '用户名',
          type: 'input',
          value: '',
          placeholder: '请输入',
          clearable: true,
          maxlength: 20
        },
        phone: {
          label: '手机号',
          type: 'input',
          value: '',
          placeholder: '请输入',
          clearable: true,
          maxlength: 11
        },
        access_type: {
          type: 'select',
          label: '统计终端',
          value: '',
          clearable: true,
          placeholder: '',
          dataList: [
            {
              label: '全部',
              value: ''
            }
          ]
        },
        module_keys: {
          type: 'select',
          label: '功能',
          value: [],
          clearable: true,
          multiple: true,
          filterable: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: []
        },
        is_satisfied: {
          type: 'select',
          label: '评价类型',
          value: '',
          clearable: true,
          placeholder: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '满意',
              value: true
            },
            {
              label: '不满意',
              value: false
            }
          ]
        }
      },
      selectListId: [],
      selectList: [] // 选择的列表
    }
  },
  watch: {
    // 监听统计终端变化
    'searchFormSetting.access_type.value'(newVal) {
      this.handleAccessTypecValue(newVal)
    }
  },
  created() {
    if (this.$route.query.data) {
      this.queryData = JSON.parse(this.$route.query.data)
      this.$set(this.searchFormSetting.access_type, 'value', this.queryData.access_type || '')
      this.$set(this.searchFormSetting.quarter, 'value', this.queryData.quarter || '')
      this.$set(this.searchFormSetting.select_time, 'value', [])
    } else {
      this.$set(this.searchFormSetting.select_time, 'value', this.getFirstAndLastDayOfRecentSevenDays() || [])
    }
  },
  async mounted() {
    // 统计周期下拉表
    this.$set(this.searchFormSetting.quarter, 'dataList', this.getLastNYearsWithQuarters(3))
    this.initLoad()
    this.getAccessTypeList()
    this.getModuleList()
  },
  computed: {
    ...mapGetters(['allPermissions', 'userInfo'])
  },
  methods: {
    // 获取近七天的日期
    getFirstAndLastDayOfRecentSevenDays() {
      const today = dayjs() // 获取今天的日期
      const firstDay = today.subtract(7, 'day') // 七天前的日期（第一天）
      const lastDay = today // 今天的日期（第七天）
      // 格式化为 YYYY-MM-DD
      const firstDayFormatted = firstDay.format('YYYY-MM-DD')
      const lastDayFormatted = lastDay.format('YYYY-MM-DD')
      // 返回一个数组
      return [firstDayFormatted, lastDayFormatted]
    },
    // 监听统计终端变化
    handleAccessTypecValue(newVal) {
      // 转换模块数据为下拉选项格式
      const transformModuleData = (moduleArray) => {
        return (moduleArray || []).map(item => ({
          label: item.name,
          value: item.key
        }))
      }

      // 如果指定了具体的访问类型，只显示该类型的模块
      if (newVal && this.moduleData[newVal]) {
        this.searchFormSetting.module_keys.dataList = transformModuleData(this.moduleData[newVal])
      } else {
        // 如果没有指定或指定的类型不存在，显示所有模块
        const allModules = []
        Object.values(this.moduleData).forEach(moduleArray => {
          allModules.push(...transformModuleData(moduleArray))
        })
        this.searchFormSetting.module_keys.dataList = allModules
      }

      // 重置选中值
      this.searchFormSetting.module_keys.value = ''
    },
    // 获取近年年份-季度
    getLastNYearsWithQuarters(n) {
      const result = [
        {
          label: '全部',
          value: ''
        }
      ]
      const currentYear = dayjs().year()
      for (let year = 0; year < n; year++) {
        const yearValue = currentYear - year
        for (let quarter = 4; quarter >= 1; quarter--) {
          result.push({
            label: `${yearValue}年第${quarter}季度`,
            value: `${yearValue}-${String(quarter)}`
          })
        }
      }
      return result
    },
    // 获取统计终端下拉框
    async getAccessTypeList() {
      const [err, res] = await to(this.$apis.apiBackgroundAdminSatisfactionAccessTypeListPost({}))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const datas = res.data || []
        this.$set(this.searchFormSetting.access_type, 'dataList', [
          {
            label: '全部',
            value: ''
          },
          ...datas.map(item => ({
            label: item.name,
            value: item.key
          }))
        ])
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取功能下拉框
    async getModuleList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminSatisfactionModuleListPost())
      if (err) {
        this.$message.error(err.message)
        return
      }

      if (res.code === 0) {
        // 动态处理所有模块类型数据
        this.moduleData = res.data || {}

        // 手动触发  避免queryData更新不触发监听
        if (this.$route.query.data && this.queryData) {
          this.handleAccessTypecValue(this.queryData.access_type)
        } else {
          this.handleAccessTypecValue()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出
    gotoExport() {
      const option = {
        url: 'apiBackgroundAdminSatisfactionDetailListExportPost',
        params: getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      }
      this.exportHandle(option)
    },
    initLoad() {
      this.getApproveRulesList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getApproveRulesList()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getApproveRulesList() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundAdminSatisfactionDetailListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )

      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }

      if (res.code === 0) {
        this.tableData = res.data.results || []
        this.totalCount = res.data.count || 0
        // 统计人数
        this.totalData.satisfied_counts = res.data.satisfied_counts || 0
        this.totalData.total_users = res.data.total_users || 0
        this.totalData.unsatisfied_counts = res.data.unsatisfied_counts || 0
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getApproveRulesList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      this.selectList = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
        this.selectList.push(item)
      })
    },
    // 操作提示
    gotoAddOrEdit(type, data) {
      this.drawerFormData = deepClone(data)
      this.drawerEditorVisible = true
    },
    async onSaveDrawer() {
      const params = {
        id: this.drawerFormData.id,
        reply: this.drawerFormData.reply
      }
      this.isDrawerEditorLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminSatisfactionReplyPost(params))
      this.isDrawerEditorLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.drawerEditorVisible = false
        this.getApproveRulesList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 保存成功
    confirmDialogHandle(formName) {
      this.drawerFormData.reply = this.drawerFormData.reply.trim() // 清除首尾空格
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.onSaveDrawer()
        } else {
          return false
        }
      })
    }
  }
}
</script>
