import { mapGetters } from 'vuex'
// 权限控制
export default {
  data() {
    return {
      isUpdateForm: false, // 数据是否有点动
      unwatchHandle: null // 需要取消监听的watch
    }
  },
  computed: {
  },
  mounted () {
  },
  methods: {
    /**
     * @description 监听一次之后取消，用于数据变动后做弹窗提醒
     * @param {Array} key
     * @returns void
     */
    watchOneHandle(key,) {
      this.unwatchHandle = this.$watch(key,function () {
          this.isUpdateForm = true
          if (this.unwatchHandle) {
            this.unwatchHandle()
          }
        },
        { deep: true }
      )
    }
  },
  beforeDestroy() {}
}
