<template>
  <div class="container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="super-organization">
      <!-- 组织结构 start -->
      <div class="organization-tree">
        <div class="m-b-20">
          <el-button size="mini" class="ps-origin-btn" @click="checkChannelDrawerShow  = true">查询</el-button>
          <el-button size="mini" class="ps-origin-btn" @click="showAddChannelDrawer('parent')">新建</el-button>
        </div>
        <el-input
          class="tree-search ps-input"
          type="primary"
          placeholder="请输入"
          clearable
          v-model="treeFilterText">
        </el-input>
        <el-tree
          v-loading="treeLoading"
          :data="treeList"
          :props="treeProps"
          :load="loadTree"
          :lazy="isLazy"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :highlight-current="true"
          :current-node-key="selectId"
          :class="{ 'tree-box': selectId} "
          ref="treeRef"
          node-key="id"
          @node-click="treeHandleNodeClick($event)"
        >
          <div class="custom-tree-node" slot-scope="{ node, data }">
            <span class="ellipsis tree-lable">
              {{ node.label }}
              <span class="stop-box" v-if="data.status === 'disable'">停</span>
            </span>
            <span>
              <el-popover
                placement="right-start"
                width="auto"
                popper-class="custon-tree-popper"
                trigger="hover">
                <div :class="['popover-btn-box',treeLoading?'no-pointer':'']">
                  <el-button :disabled="data.status === 'disable'" type="text" @click="addChildTreeHandle('child', data)">添加下级</el-button>
                  <el-button type="text" @click="changeStatus(data)">停用/启用</el-button>
                </div>
                <i slot="reference" class="el-icon-more tree-icon"></i>
              </el-popover>
            </span>
          </div>
        </el-tree>
      </div>
      <!-- end -->
      <div class="organization-r" v-loading="isLoading" :key="refreshNum">
        <!-- tab start -->
        <div key="tab" class="organization-tab-group">
          <div v-for="item in tabList" :key="item.value" :class="['organization-tab', item.value===tabType?'is-checked':'', item.disable?'is-disable':'']" @click="clickTabHandle(item)" :label="item.value"><span class="tab-label">{{ item.name }}</span></div>
        </div>
        <!-- tab end -->
        <transition-group :name="slideTransition">
          <div v-if="tabType === 'channelInfo'" key="channelInfo" class="">
            <channel-info  v-if="!isLoading" :tabType="tabType" :organizationData="selectTree" :pageLoading="isLoading" @refresh="refresh" />
          </div>
          <div v-if="tabType === 'accountManagement'" key="accountManagement" class="">
            <account-management  v-if="!isLoading" :tabType="tabType" :organizationData="selectTree" :pageLoading="isLoading" />
          </div>
          <div v-if="tabType === 'functionConfiguration'" key="functionConfiguration" class="">
            <function-configuration  v-if="!isLoading" :tabType="tabType" :organizationData="selectTree" :pageLoading="isLoading" />
          </div>
        </transition-group>
      </div>
    </div>
    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'新建渠道'"
        :visible="addChannelDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="addChannelFormRef" :model="addChannelForm" label-width="80px" label-position="left">
            <el-form-item :label="'渠道名称'" prop="name" :rules="[{ required: true, message: '请输入渠道名称', trigger: ['change', 'blur'] }]">
              <el-input v-model="addChannelForm.name" class="w-300" placeholder="请输入渠道名称，不超过30个字" maxlength="30"></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('addChannelForm')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'组织查询'"
        :visible="checkChannelDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20" v-loading="checkChannelLoading">
          <el-form ref="checkChannelFormRef" :model="checkChannelForm" label-width="80px" label-position="left">
            <el-form-item :label="'项目名称'" prop="name" :rules="{ required: checkChannelForm.address ? false : true, message: '请输入项目名称', trigger: ['change', 'blur']}">
              <!-- <el-input v-model="checkChannelForm.name" class="w-300" placeholder="输入项目点名称进行查询"></el-input> -->
              <el-select
                v-model="checkChannelForm.name"
                class="w-300"
                filterable
                clearable
                placeholder="输入项目点名称进行查询">
                <el-option
                  v-for="(item, index) in orgList"
                  :key="index"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="'项目地址'" prop="address" :rules="{ required: checkChannelForm.name ? false : true, message: '请输入项目地址', trigger: ['change', 'blur']}">
              <el-cascader
                ref="cascaderRef"
                class="w-300 m-r-20"
                placeholder="请选择项目点地址进行查询"
                :options="addrOptions"
                v-model="checkChannelForm.address"
                filterable
              ></el-cascader>
              <el-button class="ps-origin-btn" @click="checkOrg">查询</el-button>
            </el-form-item>
          </el-form>
          <el-table
            :data="checkChannelTableData"
            v-loading="bindLoading"
            stripe
            default-expand-all
            header-row-class-name="ps-table-header-row"
            row-key="id"
            :tree-props="{children: 'children_list', hasChildren: 'hasChildren'}">
            <table-column v-for="(item, i) in checkChannelTableSetting" :key="item.key + i" :col="item">
              <template #districtAlias="{ row }">
                <span>{{ computedAddress(row.district_alias) }}</span>
              </template>
              <template #operation="{ row }">
                <div v-show="!row.supervision_channel_bind">--</div>
                <el-button v-show="row.supervision_channel_bind" type="text" size="small" class="ps-red" @click="untieHandle(row)">解绑</el-button>
              </template>
            </table-column>
          </el-table>
          <div class="ps-el-drawer-footer">
            <el-button size="small" type="primary" class="w-100" @click="cancelHandle('checkChannelForm')">关闭</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { to, debounce, deepClone } from '@/utils'
import { hasClass } from '@/utils/dom'
import channelInfo from './components/channelInfo.vue'
import accountManagement from './components/accountManagement.vue'
import functionConfiguration from './components/functionConfiguration.vue'
import { DEFAULT_CHANNEL_TABLE_SETTING } from './constants/bankMerchantConstants'
import { regionData } from 'element-china-area-data'

export default {
  name: 'SuperOrganizationAdmin',
  components: {
    channelInfo,
    accountManagement,
    functionConfiguration
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      addrOptions: regionData,
      treeSelectId: 0,
      treeLoading: false,
      treeList: [],
      isLazy: true,
      treeFilterText: '',
      treeProps: {
        children: 'children_list',
        label: 'name',
        isLeaf: (data, node) => {
          return !data.has_children
        }
      },
      addType: '', // 区分是一级渠道还是下级渠道用的
      selectTree: {}, // 选中的tree数据
      selectId: '', // 点击选中的tree id
      parentTreeData: {},
      operate: '', // 操作类型，仅用于左侧的添加
      tabType: 'channelInfo', // tab类型
      tabList: [
        { name: '渠道信息', value: 'channelInfo', index: 1 }, // disable: true
        { name: '账号管理', value: 'accountManagement', index: 2 },
        { name: '功能配置', value: 'functionConfiguration', index: 3 }
      ],
      time: new Date().getTime(),
      isLoading: false,
      bindLoading: false,
      checkChannelLoading: false,
      slideTransition: 'slide-left', // 默认切换动画
      addChannelDrawerShow: false,
      addChannelForm: {
        name: ''
      },
      orgList: [],
      checkChannelDrawerShow: false,
      checkChannelForm: {
        name: '',
        address: ''
      },
      checkChannelTableData: [],
      checkChannelTableSetting: DEFAULT_CHANNEL_TABLE_SETTING,
      refreshNum: 0
    }
  },
  watch: {
    treeFilterText(val) {
      this.filterHandle()
    },
    checkChannelDrawerShow(newVal) {
      if (newVal) {
        this.getOrganizationList()
      }
    }
  },
  computed: {
    computedAddress() {
      return d => {
        let str = d.replace(/^\[|\]$/g, "")
        return str
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    initLoad() {
      this.getChannelList()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.treeFilterText = ''
      this.tabType = 'channelInfo'
      this.initLoad()
    },
    async refresh(e) {
      console.log('解绑了', e)
      await this.getChannelList()
      this.treeHandleNodeClick(e)
    },
    // 懒加载组织结构
    async loadTree(tree, resolve) {
      // 0级直接退出执行
      if (tree.level === 0) {
        return;
      }
      let params = {
        status__in: ['enable', 'disable'],
        page: 1,
        page_size: 99999
      }
      if (tree.data && tree.data.id) {
        params.parent__in = tree.data.id
      } else {
        params.parent__is_null = '1'
        this.treeLoading = true
      }
      // 强制睡眠
      // await this.$sleep(1000);
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionSupervisionChannelListPost(params));
      this.treeLoading = false
      this.isLoading = false
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    // 名称搜索
    filterHandle: debounce(function() {
      this.getChannelList(this.treeFilterText)
    }, 300),
    // 获取组织，用于顶级的获取or搜索
    async getChannelList(name, parentId, callback) {
      let params = {
        status__in: ['enable', 'disable']
      }
      if (parentId) {
        params.parent__in = parentId
      } else {
        params.parent__is_null = '1'
      }
      if (name) {
        params.name__contains = name
      }
      this.treeLoading = true
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionSupervisionChannelListPost(params));
      this.isLoading = false
      this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let results = res.data.results
        // 当初为什么不要显示展开下级的呢
        if (name && !true) {
          results = res.data.results.map(v => {
            if (name) {
              v.has_children = false
            }
            return v
          })
        } else {
          if (results.length && results[0].has_children) {
            this.$nextTick(() => {
              // 只能通过class去触发click事件了
              const expandEls = document.querySelectorAll('.el-tree-node__expand-icon')
              if (!hasClass(expandEls[0], 'expanded')) {
                expandEls[0] && expandEls[0].click()
              }
            })
          }
        }
        if (!parentId) {
          this.treeList = results
        } else { // 有parentid和callback才能进行数据更新
          if (callback) {
            callback(parentId, results)
          }
        }
        // 默认选中第一个
        this.selectId = this.treeList[0].id
        this.selectTree = this.treeList[0]
        // 更新数据时需要手动设置当前高亮选项
        if (this.selectId) {
          this.$nextTick(() => {
            this.$refs.treeRef.setCurrentKey(this.selectId)
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    getOrganizationList() {
      this.checkChannelLoading = true
      this.$apis.apiBackgroundAdminOrganizationListPost({
        page: 1,
        page_size: 9999,
        parent__is_null: '1',
        status__in: ['enable']
      }).then(res => {
        this.checkChannelLoading = false
        if (res.code === 0) {
          this.orgList = res.data.results.map(item => {
            let obj = {
              id: item.id,
              name: item.name
            }
            return obj
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 处理下没有children_list
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item[key]) {
            if (item[key].length > 0) {
              traversal(item[key])
            } else {
              _that.$delete(item, key)
            }
          } else {
            _that.$delete(item, key)
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // tab 点击事件
    clickTabHandle(tab) {
      if (!tab.disable) {
        let oldTab = this.tabList.filter(item => item.value === this.tabType)[0]
        this.slideTransition = oldTab.index < tab.index ? 'slide-left' : 'slide-right'
        this.tabType = tab.value
      }
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    // 点击tree node click
    async treeHandleNodeClick(e) {
      this.isLoading = true
      this.selectId = e.id
      this.selectTree = e
      // 更新数据时需要手动设置当前高亮选项
      if (this.selectId) {
        this.$nextTick(() => {
          this.$refs.treeRef.setCurrentKey(this.selectId)
        })
      }
      setTimeout(() => {
        this.refreshNum++
        this.isLoading = false
      }, 500)
    },
    // 添加root级
    async addRootTreeHandle(e, type) {
      this.type = ''
      this.tabType = 'channelInfo'
      // 延时300ms用于动画效果
      await this.$sleep(300);
      this.type = type
      this.selectId = e.id
    },
    showAddChannelDrawer(addType) {
      this.addType = addType
      this.addChannelDrawerShow = true
    },
    // 添加子层级
    async addChildTreeHandle(addType, data) {
      this.addType = ''
      this.tabType = 'channelInfo'
      this.isLoading = true
      // 延时300ms用于动画效果
      await this.$sleep(100);
      this.isLoading = false
      this.addType = addType
      this.parentTreeData = deepClone(data)
      this.addChannelDrawerShow = true
      console.log('this.parentTreeData', this.parentTreeData)
    },
    // tree 开启or关闭
    async changeStatus(data) {
      console.log('选中的data', data)
      let tipsText = ''
      let disable = true
      switch (data.status) {
        case 'enable':
          tipsText = `您正在关闭渠道：${data.name}。关闭后渠道账号将不可用，数据统计自动停止，请谨慎操作。确定要关闭该渠道？`
          break;
        default:
          tipsText = `您正在启用渠道：${data.name}。启用后渠道账号将恢复，数据统计重新计算。确定要启用该渠道？`
          disable = false
          break;
      }
      this.$confirm(tipsText, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: disable ? 'ps-warn' : 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.treeLoading = true
            // await this.$sleep(1000);
            const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionSupervisionChannelModifyPost({
              id: data.id,
              status: data.status === 'enable' ? 'disable' : 'enable',
              is_root: !data.level,
              name: data.name
            }))
            this.treeLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              // this.$set(data, 'status', data.status === 'enable' ? 'disable' : 'enable')
              this.getChannelList(this.treeFilterText, data.parent, this.updateTreeChildren)
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 更新tree数据，只能更新子级
    updateTreeChildren(parentId, childrens) {
      // updateKeyChildren
      let treeFRef = this.$refs.treeRef;
      treeFRef.updateKeyChildren(parentId, childrens)
    },
    // 抽屉的方法
    cancelHandle(type) {
      if (type === 'addChannelForm') {
        this.$refs.addChannelFormRef.resetFields()
        this.checkChannelTableData = []
        this.addChannelDrawerShow = false
      } else {
        this.$refs.checkChannelFormRef.resetFields()
        this.getChannelList()
        this.checkChannelTableData = []
        this.checkChannelDrawerShow = false
      }
    },
    saveHandle() {
      console.log('this.selectTree', this.selectTree, this.parentTreeData)
      let params = {
        is_root: this.addType === 'parent',
        parent: this.addType === 'parent' ? undefined : this.parentTreeData.id,
        name: this.addChannelForm.name,
        permission: this.addType === 'parent' ? undefined : this.parentTreeData.permission
      }
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelAddPost(params).then(res => {
        if (res.code === 0) {
          this.$refs.addChannelFormRef.resetFields()
          this.$message.success('创建成功')
          this.addChannelDrawerShow = false
          this.getChannelList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    untieHandle(data) {
      let params = {
        org_id: data.id,
        bind_type: !data.supervision_channel_bind
      }
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelOrgBingConfigPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success(!data.supervision_channel_bind ? '绑定成功' : '解绑成功')
          this.$refs.checkChannelFormRef.resetFields()
          this.checkChannelTableData = []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 组织查询
    checkOrg() {
      this.$refs.checkChannelFormRef.validate((valid) => {
        if (valid) {
          this.bindLoading = true
          let params = {
            org_id: this.checkChannelForm.name || undefined,
            district: this.checkChannelForm.address ? JSON.stringify(this.checkChannelForm.address) : undefined
          }
          this.$apis.apiBackgroundFundSupervisionSupervisionChannelTreeListPost(params).then(res => {
            this.bindLoading = false
            if (res.code === 0) {
              let arr = []
              if (res.data.length) {
                arr = res.data.map(item => {
                  return item[0]
                })
              } else {
                arr = []
              }
              // 先遍历逐级插入变量
              this.addStatus(arr)
              // 判断是否已绑
              this.setIsCanBind(arr)
              // 将数组转成map数据
              let treeMap = new Map()
              this.treeToMap(arr, treeMap)
              // 在整个map中找到已绑定的，往上找他的父级改变他的isCanBind
              treeMap.forEach(item => {
                if (item.supervision_channel_bind) {
                  // 如果当前这条数据是已绑的，用他的parent找到他的父级并调整他的状态
                  if (item.parent) {
                    this.findAndSet(item, treeMap)
                  }
                }
              })
              this.checkChannelTableData = deepClone(arr)
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.$message.error('查询条件不能为空')
        }
      })
    },
    load(tree, treeNode, resolve) {
      let params = {
        // supervision_channel_id: this.organizationData.id,
        parent__in: tree.tree_id
      }
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelBingOrgListPost(params).then(res => {
        if (res.code === 0) {
          resolve(res.data.results || [])
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 批量设置能否绑定
    setIsCanBind(arr) {
      arr.forEach(item => {
        if (item.supervision_channel_bind) {
          item.isCanBind = false
          if (item.children_list && item.children_list.length) {
            this.setFalseStatus(item.children_list)
          }
        } else {
          item.isCanBind = true
          if (item.children_list && item.children_list.length) {
            this.setIsCanBind(item.children_list)
          }
        }
      })
    },
    setFalseStatus(arr) {
      arr.forEach(item => {
        item.isCanBind = false
        item.supervision_channel_bind = false
        if (item.children_list && item.children_list.length) {
          this.setFalseStatus(item.children_list)
        }
      })
    },
    addStatus(targetArr) {
      if (targetArr.length) {
        targetArr.forEach(item => {
          Object.assign(item, { isCanBind: true })
          if (item.has_children && item.children_list.length) {
            this.addStatus(item.children_list)
          }
        })
      }
    },
    findAndSet(data, map) {
      let parentData = map.get(data.parent)
      parentData.isCanBind = false
      map.set(data.parent, { ...parentData })
      // 继续往上找
      if (parentData.parent) {
        this.findAndSet(parentData, map)
      }
    },
    // 将数组内的元素转成map数据
    treeToMap(treeArr, map) {
      treeArr.forEach(item => {
        map.set(item.id, item)
        if (item.children_list && item.children_list.length > 0) {
          this.treeToMap(item.children_list, map)
        }
      })
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.super-organization {
  display: flex;
  // height: 100%;
  .organization-r {
    position: relative;
    min-height: calc(100vh - 128px - 76px - 5px);
    // height: calc(100vh - 128px - 76px - 5px);
    // overflow-y: scroll;
    flex: 1;
    min-width: 0;
    background-color: #f8f9fa;
    box-shadow: 6px 6px 10px 0px rgba(202, 210, 221, 0.3),
      inset 2px 2px 0px 0px#ffffff;
    border-radius: 12px;
    padding: 20px;
    .organization-tab-group{
      .organization-tab{
        display: inline-block;
        padding: 5px 10px;
        margin: 5px 10px 5px 0;
        font-size: 13px;
        letter-spacing: 1px;
        color: #7b7c82;
        border: solid 1px #dae1ea;
        border-radius: 15px;
        cursor: pointer;
        &.is-checked{
          color: #fff;
          border: solid 1px #dae1ea;
          background-color: #fd953c;
        }
        .is-checked+.tab-label{
          color: #fff;
        }
        &.el-radio:last-child {
          margin-right: 0;
        }
        &.is-disable {
          cursor: not-allowed;
          opacity: .5;
        }
      }
    }
    .item-box{
      // display: flex;
      padding: 10px 0;
      .item-b-l{
        // display: flex;
        // justify-content: center;
        // align-items: center;
        float: left;
        width: 56px;
        height: 56px;
        line-height: 56px;
        text-align: center;
        vertical-align: middle;
        background-color: #ff9b45;
        border-radius: 8px;
        font-size: 30px;
        letter-spacing: 2px;
        color: #ffffff;
      }
      .item-b-r{
        margin-left: 76px;
      }
      .item-text-box{
        display: flex;
        padding: 5px 0;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 1px;
        color: #23282d;
        .item-label{
          opacity: 0.7;
        }
        .item-text{
          flex: 1;
        }
      }
    }
  }
  .organization-tree{
    width: auto;
    max-width: 320px;
    margin-right: 20px;
    border-radius: 12px !important;
    .el-tree-node__content{
      .is-leaf{ // 领导说要挪前点哦
        margin-left: -18px;
      }
    }
  }
  .custom-tree-node {
    width: 100%;
    min-width: 0;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #23282d;
    .el-icon-more{
      transform: rotate(90deg);
      color: #a1a1a1;
    }
    .tree-icon{
      width: 15px;
      text-align: center;
      &.el-icon-edit{
        color: #ff9b45;
      }
    }
    .stop-box{
      display: inline-block;
      text-align: center;
      color: red;
      border: 1px solid #ff5450;
      border-radius: 50%;
      font-size: 12px;
      padding: 2px 3px;
      transform: scale(0.7);
    }
  }
  .tree-flex{
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #23282d;
    .el-icon-more{
      transform: rotate(90deg);
      color: #a1a1a1;
    }
    .tree-icon{
      width: 15px;
      text-align: center;
    }
  }
}
.ps-dialog{
  .dialog-form{
    .flex{
      display: flex;
      align-items: center;
    }
    .form-img{
      display: inline-block;
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-left: 10px;
      cursor: pointer;
      opacity: .8;
      &:hover{
        opacity: 1;
      }
    }
  }
}
.custon-tree-popper{
  min-width: 50px;
  padding: 0;
  .popover-btn-box{
    display: flex;
    flex-direction: column;
    .el-button{
      display: block;
      margin: 0 !important;
      padding: 8px 15px;
      color: #23282d;
      &.popper-del{
        color: #ff5450;
      }
      &:hover{
        color: #fd953c;
        background-color: #edeff5;
      }
      &.is-disabled{
        opacity: .5;
        &:hover{
          color: #23282d;
          background-color: unset;
        }
      }
    }
  }
}
</style>
