// 车辆绑定表单表头
export const TABLE_HEAD_DATA_USER_BINDING = [
  { label: '水控账号', key: 'username' },
  { label: '人员编号', key: 'person_no' },
  { label: '姓名', key: 'name' },
  { label: '性别', key: 'gender_alias' },
  { label: '部门', key: 'card_department_group_alias', type: 'slot', slotName: 'cardDepartmentGroupAlias' },
  { label: '分组', key: 'card_user_group_alias', type: 'slot', slotName: 'cardUserGroupAlias' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "100" }
]

// 车辆绑定筛选设置
export const SEARCH_FORM_SET_DATA_USER_BINDING = {
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入',
    clearable: true
  },
  username: {
    type: 'input',
    value: '',
    label: '水控账号',
    placeholder: '请输入',
    clearable: true
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入',
    clearable: true
  },
  card_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    isLazy: false,
    checkStrictly: true,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    clearable: true
  },
  card_user_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}

// 跳转厂商链接
export const URL_TEMPLATE_MODEL = '/api/temporary/template_excel/水控账号导入.xlsx'

// 停车类型字典
export const TABLE_HEAD_DATA_IMPORT_USER_BINGING = [
  { label: '水控账号', key: 'username' },
  { label: '人员编号', key: 'person_no' }
]
