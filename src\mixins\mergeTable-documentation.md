# mergeTable.js 混入文档

## 概述

`mergeTable.js` 是一个功能强大的 Vue 混入，专门用于处理 Element UI 表格的单元格合并。它支持多种合并场景，包括行合并、列合并、小计行合并、汇总行合并等。

## 功能特性

- ✅ 行合并：根据相同值合并行
- ✅ 列合并：合并指定的列
- ✅ 小计行合并：自动处理小计行的合并显示
- ✅ 汇总行合并：处理汇总数据的合并
- ✅ 缓存机制：提高合并计算性能
- ✅ 自定义处理器：支持自定义特殊行合并逻辑

## 安装使用

### 1. 导入混入

```javascript
import mergeTableMixin from '@/mixins/mergeTable'

export default {
  name: 'YourComponent',
  mixins: [mergeTableMixin],
  // ...
}
```

### 2. 模板中使用

```html
<template>
  <el-table
    :data="tableData"
    :span-method="spanMethod"
    :cell-style="cellStyle"
    border
  >
    <el-table-column prop="name" label="姓名" />
    <el-table-column prop="department" label="部门" />
    <el-table-column prop="amount" label="金额" />
  </el-table>
</template>
```

## 配置选项

### mergeOpts 完整配置

```javascript
this.mergeOpts = {
  // 基础合并配置
  useKeyList: {
    // 根据固定key进行合并的配置
    // key: [需要合并的字段数组]
    person_id: ['name', 'department', 'phone']
  },
  
  mergeKeyList: [
    // 通用的合并字段配置
    'date', 'group_name'
  ],
  
  colMergeList: [
    // 列合并配置
    {
      labels: ['列1', '列2', '列3'], // 表头标签
      subtotals: ['总计', '小计'], // 需要合并的行标识值
      attr: 'type' // 判断字段
    }
  ],
  
  rowMergeList: [
    // 需要行合并的字段列表
    'category', 'group'
  ],
  
  // 特殊行配置
  subtotalConfig: {
    mergeColumns: 3, // 小计行合并前几列
    displayColumns: ['count', 'amount'] // 需要显示数据的列
  },
  
  summaryConfig: {
    mergeColumns: 8 // 汇总行合并列数
  },
  
  specialRowHandlers: [
    // 自定义特殊行处理器
    {
      condition: (row) => row.isSpecial,
      merge: (row, column, rowIndex, columnIndex) => {
        return columnIndex === 0 ? [1, 3] : [0, 0]
      }
    }
  ]
}
```

## 使用示例

### 示例1：基础行合并

```javascript
// 数据
data() {
  return {
    tableData: [
      { id: 1, name: '张三', department: '技术部', amount: 1000 },
      { id: 1, name: '张三', department: '技术部', amount: 2000 },
      { id: 2, name: '李四', department: '销售部', amount: 1500 }
    ],
    mergeOpts: {
      useKeyList: {
        id: ['name', 'department'] // 根据id合并姓名和部门列
      }
    }
  }
}

// 初始化
mounted() {
  this.initMergeData(this.tableData)
}
```

**效果：**
| 姓名 | 部门 | 金额 |
|------|------|------|
| 张三 (合并2行) | 技术部 (合并2行) | 1000 |
| | | 2000 |
| 李四 | 销售部 | 1500 |

### 示例2：列合并（分组标题行）

```javascript
// 数据
data() {
  return {
    tableData: [
      { type: '技术部', name: '', department: '', amount: 0, rowClassName: true },
      { type: '数据', name: '张三', department: '前端', amount: 1000 },
      { type: '数据', name: '李四', department: '后端', amount: 2000 },
      { type: '销售部', name: '', department: '', amount: 0, rowClassName: true },
      { type: '数据', name: '王五', department: '销售', amount: 1500 }
    ],
    mergeOpts: {
      colMergeList: [
        {
          labels: ['姓名', '部门', '金额'],
          subtotals: ['技术部', '销售部'],
          attr: 'type'
        }
      ]
    }
  }
}
```

**效果：**
| 姓名 | 部门 | 金额 |
|------|------|------|
| 技术部 (合并3列) | | |
| 张三 | 前端 | 1000 |
| 李四 | 后端 | 2000 |
| 销售部 (合并3列) | | |
| 王五 | 销售 | 1500 |

### 示例3：小计行合并

```javascript
// 数据
data() {
  return {
    tableData: [
      { name: '张三', department: '技术部', count: 5, amount: 1000 },
      { name: '李四', department: '技术部', count: 3, amount: 2000 },
      { name: '小计', department: '', count: 8, amount: 3000, subtotalClassName: true },
      { name: '王五', department: '销售部', count: 2, amount: 1500 }
    ],
    mergeOpts: {
      subtotalConfig: {
        mergeColumns: 2, // 合并前2列
        displayColumns: ['count', 'amount'] // 显示数量和金额
      }
    }
  }
}
```

**效果：**
| 姓名 | 部门 | 数量 | 金额 |
|------|------|------|------|
| 张三 | 技术部 | 5 | 1000 |
| 李四 | 技术部 | 3 | 2000 |
| 小计 (合并2列) | | 8 | 3000 |
| 王五 | 销售部 | 2 | 1500 |

### 示例4：复合合并（分组+小计）

```javascript
// 完整示例：分组数据 + 小计行
data() {
  return {
    tableData: [],
    mergeOpts: {
      colMergeList: [
        {
          labels: ['部门', '姓名', '职位', '数量', '金额'],
          subtotals: ['技术部', '销售部'],
          attr: 'department'
        }
      ],
      useKeyList: {
        person_id: ['name', 'position']
      },
      subtotalConfig: {
        mergeColumns: 3,
        displayColumns: ['count', 'amount']
      }
    }
  }
},

methods: {
  // 处理数据，添加分组行和小计行
  processData(rawData) {
    let processedData = []
    let groups = this.groupBy(rawData, 'department')
    
    Object.keys(groups).forEach(dept => {
      // 添加分组标题行
      processedData.push({
        department: dept,
        name: '',
        position: '',
        count: 0,
        amount: 0,
        rowClassName: true
      })
      
      // 添加分组数据
      processedData.push(...groups[dept])
      
      // 计算小计
      const totalCount = groups[dept].reduce((sum, item) => sum + item.count, 0)
      const totalAmount = groups[dept].reduce((sum, item) => sum + item.amount, 0)
      
      // 添加小计行
      processedData.push({
        department: '小计',
        name: '',
        position: '',
        count: totalCount,
        amount: totalAmount,
        subtotalClassName: true
      })
    })
    
    this.tableData = processedData
    this.initMergeData(this.tableData)
  }
}
```

### 示例5：自定义特殊行处理

```javascript
data() {
  return {
    mergeOpts: {
      specialRowHandlers: [
        {
          // 处理备注行
          condition: (row) => row.isRemark,
          merge: (row, column, rowIndex, columnIndex) => {
            // 备注行合并所有列
            return columnIndex === 0 ? [1, 5] : [0, 0]
          }
        },
        {
          // 处理分隔行
          condition: (row) => row.isDivider,
          merge: (row, column, rowIndex, columnIndex) => {
            // 分隔行只显示第一列
            return columnIndex === 0 ? [1, 1] : [0, 0]
          }
        }
      ]
    }
  }
}
```

## 方法说明

### 主要方法

- `spanMethod({ row, column, rowIndex, columnIndex })`: 主要的合并方法
- `initMergeData(data)`: 初始化合并数据
- `clearMergeCache()`: 清除合并缓存

### 辅助方法

- `handleSpecialRows()`: 处理特殊行
- `handleSubtotalRow()`: 处理小计行
- `handleSummaryRow()`: 处理汇总行
- `handleColumnMerge()`: 处理列合并
- `getRowMergeResult()`: 获取行合并结果

## 注意事项

1. **数据标识**：确保特殊行有正确的标识字段
   - 小计行：`subtotalClassName: true`
   - 汇总行：`summaryClassName: true`
   - 分组行：`rowClassName: true`

2. **初始化**：数据变化后需要调用 `initMergeData()`

3. **性能优化**：大数据量时建议使用分页

4. **样式配合**：配合 CSS 样式实现更好的视觉效果

## 常见问题

**Q: 为什么合并后显示 "--"？**
A: 检查数据结构和 `subtotalConfig` 配置，确保小计行的数据字段有值。

**Q: 如何自定义合并逻辑？**
A: 使用 `specialRowHandlers` 添加自定义处理器。

**Q: 合并后样式异常？**
A: 检查 `cellStyle` 方法和 CSS 样式配置。
