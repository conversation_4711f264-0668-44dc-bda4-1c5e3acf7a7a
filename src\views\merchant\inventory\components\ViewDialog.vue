<template>
  <!-- 添加/编辑 -->
  <dialog-message
    :show.sync="visible"
    :title="title"
    :showFooter="false"
    :loading.sync="isLoading"
    :width="width"
    top="200px"
    class="view-dialog"
    @close="handlerClose"
  >
    <!-- 给个插槽 -->
    <slot name="header"></slot>
    <div class="">
      <!-- table start -->
      <!-- <custom-table
        v-loading="isLoading"
        :table-data="viewTableData"
        :table-setting="tableSettings"
        ref="tableData"
        style="width: 100%"
        size="small"
        stripe
        max-height="600"
        header-row-class-name="ps-table-header-row"
      /> -->
      <el-table
        v-loading="isLoading"
        :data="viewTableData"
        ref="tableData"
        style="width: 100%"
        size="small"
        stripe
        header-row-class-name="ps-table-header-row"
        row-key="id"
        max-height="600"
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item">
          <template #operation="{ row }">
            <el-button v-for="btn in item.operationList" :key="btn.key" type="text" size="small" class="ps-text" @click="operationHandle(btn.key, row)">
              {{ btn.label }}
            </el-button>
          </template>
        </table-column>
      </el-table>
      <!-- table end -->
    </div>
    <!-- 分页 start -->
    <div v-if="total > pageSize" class="block" style="text-align: right">
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="total"
      ></pagination>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
// 查看
export default {
  name: 'ViewDialog',
  props: {
    showview: Boolean,
    loading: Boolean,
    // 类型
    type: {
      type: String,
      default: 'static' // static静态数据，xhr通过请求获取的数据
    },
    title: {
      type: String,
      default: '查看'
    },
    width: {
      type: String,
      default: '660px'
    },
    // 请求的接口
    api: {
      type: String,
      default: ''
    },
    // 接口请求的参数
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    tableSettings: {
      type: Array,
      default() {
        return [{ label: '名称', key: 'name' }]
      }
    },
    // 静态数据
    staticList: {
      type: Array,
      default() {
        return []
      }
    },
    // 格式化请求回来的数据
    formatResult: Function
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      dateRange: [],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  computed: {
    visible: {
      get() {
        return this.showview
      },
      set(val) {
        this.$emit('update:showview', val)
      }
    },
    // 当前table显示的数据
    viewTableData() {
      if (this.type === 'static') {
        return this.staticList.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
      } else {
        return this.tableData
      }
    },
    // 总条数
    total() {
      if (this.type === 'static') {
        return this.staticList.length
      } else {
        return this.totalCount
      }
    }
  },
  watch: {
    showview(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 初始化
    init() {
      // 每次打开时重置页码为第一页
      this.currentPage = 1
      this.getViewList()
    },
    // 获取草稿箱列表数据
    async getViewList() {
      if (this.type !== 'xhr') return
      if (!this.api) {
        return this.$message.error('获取接口地址失败！')
      }
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis[this.api]({
          ...this.params,
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '请求数据失败')
      }
      if (res.code === 0) {
        if (this.formatResult) {
          this.tableData = this.formatResult(res.data)
        } else {
          this.tableData = res.data
        }
      } else {
        this.$message.error(res.msg || '出错啦！')
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getViewList()
    },
    handlerClose(e) {
      console.log(11111, 'ViewDialog')
      this.isLoading = false
    },
    // 操作点击
    operationHandle(type, data) {
      this.$emit('operation', type, data)
    }
  }
}
</script>

<style lang="scss">
.view-dialog {
  .dialog-message {
    background-color: rgba(88, 88, 88, 0.3);
  }
  .el-dialog__body {
    padding-top: 10px;
  }
}
</style>
