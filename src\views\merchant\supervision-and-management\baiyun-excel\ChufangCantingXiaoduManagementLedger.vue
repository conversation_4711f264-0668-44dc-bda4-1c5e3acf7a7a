<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" label-width="105px" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="historyDrawerVisible = true">历史记录</button-icon>
          <button-icon color="origin" @click="openDrawer('configuration')">消毒剂配置</button-icon>
          <button-icon color="origin" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" type="export" @click="gotoExport">导出</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          ref="tableData"
          :data="tableData"
          v-loading="isLoading"
          stripe header-row-class-name="ps-table-header-row"
          row-key="id"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
          </table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'消毒剂配置'"
        :visible="drawerShow"
        :show-close="false"
        size="50%">
        <div class="p-20">
          <el-form ref="formRef" :model="formData" label-width="100px" label-position="right" class="tianjiaji-form">
            <div v-for="(item, index) in formData.infoData" :key="index" class="tianjiaji-form-wrap">
              <el-form-item :label="`消毒剂${index + 1}`" :prop="`infoData.${index}.name`" :rules="{ required: true, message: '请输入消毒剂名称', trigger: 'blur' }">
                <el-input v-model="item.name" class="w-300" placeholder="请输入添加剂名称" maxlength="20" :disabled="editMode"></el-input>
              </el-form-item>
              <div class="m-b-22 m-l-20">
                <img src="@/assets/img/plus.png" class="m-r-10" alt="" @click="addInfo()"  v-if="formData.infoData.length -1 === index">
                <img src="@/assets/img/reduce_red.png" alt="" @click="delInfo(index)" v-if="formData.infoData.length > 1">
              </div>
            </div>
          </el-form>
          <div class="ps-el-drawer-footer noprint">
            <el-button class="w-100" @click="cancelHandle">关闭</el-button>
            <el-button class="w-100 ps-btn" v-if="editMode" @click="editHandle">编辑</el-button>
            <el-button class="w-100 ps-btn" v-else @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- 历史记录抽屉 -->
    <HistoryRecordDrawer
      :visible.sync="historyDrawerVisible"
      title="历史记录"
      api="apiBackgroundFundSupervisionLedgerFoodSafetyOperateLogList"
      type="Sanitizer"
      @close="historyDrawerVisible = false"
    >
    </HistoryRecordDrawer>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { debounce, deepClone, getRequestParams } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import print from 'vue-print-nb'
import HistoryRecordDrawer from './compontents/HistoryRecordDrawer.vue'

export default {
  name: 'XuexiaoShipinAnquanManagementLedger',
  mixins: [exportExcel],
  components: {
    HistoryRecordDrawer
  },
  directives: {
    print
  },
  data() {
    return {
      isLoading: false,
      searchForm: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '日期筛选',
          labelWidth: '100px',
          clearable: false,
          value: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        },
        sanitizer: {
          type: 'input',
          value: '',
          label: '消毒剂名称',
          placeholder: '请输入'
        },
        operator: {
          type: 'input',
          value: '',
          label: '消毒人',
          placeholder: '请输入'
        },
        reviewer: {
          type: 'input',
          value: '',
          label: '复核人',
          placeholder: '请输入'
        }
      },
      tableData: [],
      tableSetting: [
        { label: '日期', key: 'create_time' },
        { label: '消毒剂名称', key: 'ledger_no' },
        { label: '用量', key: 'director' },
        { label: '用水量', key: 'examiner' },
        { label: '用途', key: 'submit_operator_name' },
        { label: '是否确认消毒', key: 'confirmer_name' },
        { label: '消毒人', key: '1' },
        { label: '复核人', key: '2' }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      drawerShow: false,
      formData: {
        infoData: [{ name: '' }]
      },
      editMode: true,
      historyDrawerVisible: false
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getData()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value || undefined
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    getData() {
      let params = {
        ...this.formatQueryParams(this.searchForm),
        page: this.currentPage,
        page_size: this.pageSize
      }
      this.$apis.apiBackgroundFundSupervisionLedgerKitchenHygieneKitchenCanteenDisinfectListPost(params).then(res => {
        if (res.code === 0) {
          this.tableData = res.data.results
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.pageSize = 10
      this.tableData = []
      this.initLoad()
    }, // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.pageSize = 10
        this.initLoad()
      }
    }, 300),
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.initLoad()
    },
    gotoExport() {
      const option = {
        url: "apiBackgroundFundSupervisionLedgerKitchenHygieneExportKitchenCanteenDisinfectListPost",
        params: {
          ids: this.selectedList
        }
      }
      this.$refs.tableData.clearSelection()
      this.exportHandle(option)
    },
    cancelHandle() {
      this.drawerShow = false
    },
    delInfo(index) {
      this.formData.infoData.splice(index, 1)
    },
    addInfo() {
      this.formData.infoData.push({
        name: ""
      })
    },
    openDrawer(type) {
      if (type === 'configuration') {
        this.$apis.apiBackgroundFundSupervisionLedgerKitchenHygieneSanitizerConfigPost().then(res => {
          if (res.code === 0) {
            let data = res.data[0].sanitizer || []
            if (data.length > 0) {
              let arr = []
              data.forEach(item => {
                let obj = {
                  name: item
                }
                arr.push(obj)
              })
              this.formData.infoData = deepClone(arr)
            }
            this.drawerShow = true
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    editHandle() {
      this.editMode = false
    },
    saveHandle() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.editForm()
        } else {
          this.$message.error('请检查消毒剂配置内容是否正确')
        }
      })
    },
    editForm() {
      let arr = this.formData.infoData.map(item => {
        return item.name
      })
      let params = {
        sanitizer: arr
      }
      this.$apis.apiBackgroundFundSupervisionLedgerKitchenHygieneModifySanitizerConfigPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('编辑成功')
          this.editMode = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 打印
    gotoPrint() {
      let params = {
        ...this.formatQueryParams(this.searchForm),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: '',
          print_title: '厨房餐厅消毒管理台账',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerKitchenHygieneKitchenCanteenDisinfectListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.tableSetting),
          collect: [],
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount ? this.totalCount : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.tianjiaji-form{
  // padding: 20px;
  .tianjiaji-form-wrap{
    display: flex;
    align-items: center;
  }
}
</style>
