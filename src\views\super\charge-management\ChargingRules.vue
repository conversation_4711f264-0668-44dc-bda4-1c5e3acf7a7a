<template>
  <div id="chargeRules">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addOrEditDialogShow('add')">新 建</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table ref="tableView" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" @click="addOrEditDialogShow('edit', row)">编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  :disabled="row.use_count > 0"
                  @click="deleteChargeRule(row)"
                >
                  删除
                </el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <AddOrEditDialog
        ref="addOrEditDialogRef"
        :isShow.sync="showAddOrEditDialog"
        :type="addOrEditDialogType"
        :rowData="addOrEditDialogData"
        :nameList="ruleListName"
        width="500px" />
    </div>
  </div>
</template>

<script>
import { chargeRulesTableSetting } from './constants'
import AddOrEditDialog from './components/AddOrEditDialog.vue'
import { debounce, to, divide } from '@/utils'
export default {
  name: 'ChargeRules',
  components: {
    AddOrEditDialog
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      currentTableSetting: chargeRulesTableSetting,
      showAddOrEditDialog: false,
      addOrEditDialogType: '',
      addOrEditDialogData: {},
      ruleListName: []
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    showAddOrEditDialog(newVal) {
      if (!newVal) {
        setTimeout(() => {
          this.initLoad()
        }, 500)
      }
    }
  },
  methods: {
    initLoad() {
      this.isLoading = true
      this.getChargeRulesList()
    },
    searchHandle: debounce(function() {
      this.isLoading = true
      this.page = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.isLoading = true
      this.page = 1
      this.initLoad()
    },
    // 获取后台收费规则列表
    async getChargeRulesList() {
      this.ruleListName = []
      const [err, res] = await to(this.$apis.apiBackgroundAdminBackgroundTollRuleListPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        // 校验规则 --- Name字段
        this.tableData.forEach(item => {
          this.ruleListName.push(item.name)
        })
      } else {
        this.$message.error(err.message)
      }
      this.isLoading = false
    },
    addOrEditDialogShow(type, data) {
      this.showAddOrEditDialog = true
      this.addOrEditDialogType = type
      this.addOrEditDialogData = {
        id: type === 'edit' ? data.id : undefined,
        name: type === 'edit' ? data.name : '',
        toll_version: type === 'edit' ? data.toll_version : '',
        fee: type === 'edit' ? parseInt(divide(data.fee)).toFixed(1) : '',
        first_discount: type === 'edit' ? data.first_discount : 100,
        second_discount: type === 'edit' ? data.second_discount : 95,
        third_discount: type === 'edit' ? data.third_discount : 90,
        fourth_discount: type === 'edit' ? data.fourth_discount : 65,
        fifth_discount: type === 'edit' ? data.fifth_discount : 55
      }
    },
    // 删除收费规则
    deleteChargeRule(e) {
      this.$confirm(`删除后数据将不可恢复，是否确认删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true
      })
        .then(async () => {
          const [err, res] = await to(
            this.$apis.apiBackgroundAdminBackgroundTollRuleDeletePost({
              ids: [e.id]
            })
          )
          if (err) {
            this.$message.error(err.msg)
            return
          }
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.getChargeRulesList()
          } else {
            return this.$message.error(err.msg)
          }
        })
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
