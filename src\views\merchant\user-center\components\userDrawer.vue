<template>
  <div class="drawer-box">
    <customDrawer
      :show.sync="visible"
      :loading="isLoading"
      :title="title"
      @confirm="saveSetting"
      :cancelClass="'ps-cancel-btn'"
      :cancelText="'取 消'"
      :size="600"
    >
      <div class="drawer-container">
        <el-form
          :model="userForm"
          @submit.native.prevent
          status-icon
          ref="userForm"
          :rules="userFormRules"
          label-width="120px"
          class="dialog-form"
        >
          <el-form-item label="姓名：" prop="name">
            <el-input v-model="userForm.name" placeholder="请输入" class="ps-input w-250"></el-input>
          </el-form-item>
          <el-form-item label="人员编号：" prop="personNo">
            <el-input
              v-model="userForm.personNo"
              placeholder="请输入"
              class="ps-input w-250"
              :disabled="type == 'add' ? false : true"
            ></el-input>
          </el-form-item>
          <el-form-item label="性别：" prop="gender">
            <el-select
              v-model="userForm.gender"
              placeholder="请下拉选择"
              class="ps-select w-250"
              popper-class="ps-popper-select"
            >
              <el-option
                v-for="item in genderList"
                :key="item.value"
                :label="item.gender"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="手机号码：" prop="phone">
            <el-input v-model="userForm.phone" placeholder="请输入8-11位数" class="ps-input w-250"></el-input>
          </el-form-item>
          <el-form-item label="卡号：" prop="cardNumber">
            <el-input
              :disabled="disabledCardNumber"
              v-model="userForm.cardNumber"
              placeholder="请输入"
              class="ps-input w-250"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="sensitiveSetting.id_number" label="身份证号：" prop="idNumber">
            <el-input
              v-model="userForm.idNumber"
              placeholder="请输入"
              class="ps-input w-250"
              @blur="blurIdNumber"
            ></el-input>
          </el-form-item>
          <el-form-item label="年龄：" prop="age">
            <el-input
              v-model="userForm.age"
              placeholder="请输入"
              class="ps-input w-250"
              :disabled="!!userForm.idNumber"
            ></el-input>
          </el-form-item>
          <el-form-item label="部门：" prop="department">
            <tree-select
              :multiple="false"
              :options="departmentList"
              :limit="1"
              :limitText="count => '+' + count"
              :default-expand-level="1"
              :normalizer="departmentNode"
              placeholder="请选择"
              v-model="userForm.department"
              class="w-250"
              v-if="treeSelectShow"
            ></tree-select>
            <el-input v-else :disabled="true" v-model="userForm.department" class="ps-input w-250"></el-input>
          </el-form-item>
          <!-- <el-form-item label="卡密码：" prop="cardPwd">
        <el-input
          v-model="userForm.cardPwd"
          placeholder="请输入"
          class="ps-input"
        ></el-input>
      </el-form-item> -->
          <el-form-item label="自动分组：">
            <template slot="label">
              <div class="ps-flex flex-align-c ps-float-r">
                <el-tooltip placement="top" class="item">
                  <div slot="content">
                    <div v-html="groupTip" style="white-space: pre-line"></div>
                  </div>
                  <i class="el-icon-question" style="font-size: 18px; color: #ff9b45"></i>
                </el-tooltip>
                <span>自动分组：</span>
              </div>
            </template>
            <el-switch v-model="userForm.isAutoGroup"  @change="changeIsAutoGroup" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="分组：" prop="group">
            <!-- <el-select
          v-model="userForm.group"
          placeholder="请下拉选择"
          class="ps-select w-250"
          popper-class="ps-popper-select"
          multiple
          collapse-tags
          clearable
        >
          <el-option
            v-for="item in groupList"
            :key="item.id"
            :label="item.group_name"
            :value="item.id"
          ></el-option>
        </el-select> -->
            <user-group-select
              class="search-item-w ps-input w-250"
              v-model="userForm.group"
              placeholder="请下拉选择"
              :disabled="userForm.isAutoGroup"
              :autoGroupType="true"
            ></user-group-select>
          </el-form-item>
          <el-form-item label="有效期" :prop="type == 'add' ? 'validityDate' : ''">
            <!-- :value-format="type == 'add' ? 'yyyy-MM-ddTHH:mm:ssZ' : 'yyyy-MM-ddTHH:mm:ss'" -->
            <el-date-picker
              v-model="userForm.validityDate"
              type="datetimerange"
              align="left"
              unlink-panels
              range-separator="至"
              start-placeholder="生效时间"
              end-placeholder="失效时间"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              clearable
              style="width: 390px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item :label="spare_field">
            <el-input
              v-model="userForm.spare_field"
              placeholder="请输入"
              maxlength="30"
              clearable
              class="w-250"
            ></el-input>
          </el-form-item>
          <el-form-item :label="spare_field2">
            <el-input
              v-model="userForm.spare_field2"
              placeholder="请输入"
              maxlength="30"
              clearable
              class="w-250"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </customDrawer>
  </div>
</template>

<script>
import { getAgeToIden } from '@/utils'
import UserGroupSelect from '@/components/UserGroupSelect'
import { decrypted, encrypted } from '@/utils/aesUtil'
export default {
  name: 'userDialog',
  components: {
    UserGroupSelect
  },
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '新增用户'
    },
    isshow: Boolean,
    userInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    groupList: {
      type: Array,
      default() {
        return []
      }
    },
    confirm: Function,
    sensitiveSetting: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    var validataLimitedDate = (rule, value, callback) => {
      if (this.type === 'add' && value != null && value.length && new Date(value[0]).getTime() < new Date().getTime()) {
        console.log(value)
        callback(new Error('生效时间不能早于当前时间'))
      } else {
        if (
          this.type === 'edit' &&
          value != null &&
          value.length &&
          new Date(this.userForm.create_time).getTime() > new Date(value[0]).getTime()
        ) {
          callback(new Error('生效时间不能早于创建时间'))
        } else {
          callback()
        }
      }
    }
    var cardNoValidate = (rule, value, callback) => {
      if (value && !/^[a-zA-Z0-9_]+$/i.test(value)) {
        callback(new Error('请输入正确的卡号'))
      } else {
        callback()
      }
    }
    let validatephone = (rule, value, callback) => {
      if (value) {
        let regTelphone = /^\d{8,11}$/
        if (!regTelphone.test(value)) {
          callback(new Error('请输入正确手机号'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    let validateIden = (rule, value, callback) => {
      if (value) {
        let regTelphone =
          /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/
        if (!regTelphone.test(value)) {
          callback(new Error('请输入正确身份证号码'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    let validateStock = (rule, value, callback) => {
      let number = /^\d+$/
      if (!value || (number.test(value) && value >= 1 && value <= 999)) {
        callback()
      } else {
        callback(new Error('请输入正确数字'))
      }
    }
    return {
      groupTip: '选中后，将根据自动分组规则\n进行分组，不允许手动修改',
      isLoading: false,
      spare_field: '备用字段1', // 备用字段1
      spare_field2: '备用字段2', // 备用字段2
      userForm: {
        id: '', // 姓名
        name: '', // 姓名
        gender: '', // 性别
        group: '', // 分组
        department: null, // 部门
        cardNumber: '', // 会员卡号
        cardPwd: '', // 会员卡密码
        phone: '', // 手机号
        personNo: '', // 人员编号
        validityDate: [], // 有效期
        create_time: '', // 有效期
        isAutoGroup: false, // 是否自定分组
        idNumber: '', // 身份证
        age: '', // 年龄
        spare_field: '', // 备用字段1
        spare_field2: '' // 备用字段2
      },
      userFormRules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        personNo: [{ required: true, message: '请输入人员编号', trigger: 'blur' }],
        cardNumber: [{ validator: cardNoValidate, trigger: 'blur' }],
        phone: [{ validator: validatephone, trigger: 'blur' }],
        gender: [{ required: true, message: '请选择性别', trigger: 'blur' }],
        validityDate: [{ validator: validataLimitedDate, trigger: 'change' }],
        idNumber: [{ validator: validateIden, trigger: 'change' }],
        age: [{ message: '请输入正确年龄', validator: validateStock, trigger: 'change' }]
      },
      props: {
        value: 'id',
        label: 'group_name',
        children: 'children_list',
        checkStrictly: true
      },
      genderList: [
        { gender: '男', value: 'MAN' },
        { gender: '女', value: 'WOMEN' },
        { gender: '其他', value: 'OTHER' }
      ],
      pickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now();
        // },
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      departmentIds: [],
      departmentList: [],
      isCurrent: false,
      treeSelectShow: true
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    },
    disabledCardNumber: function () {
      let status = false
      if (this.userInfo) {
        status =
          this.userInfo.card_status === 'LOSS' ||
          this.userInfo.card_status === 'UNUSED' ||
          this.userInfo.card_status === 'QUIT'
      }
      return status
    }
  },
  watch: {},
  created() {
    this.initLoad()
    this.getCardUserSpareFieldAlias()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.visible && this.type === 'edit') {
        this.userForm.id = this.userInfo.id
        this.userForm.name = this.userInfo.name
        this.userForm.gender = this.userInfo.gender
        // this.userForm.group = (this.userInfo.card_user_groups && this.userInfo.card_user_groups.length > 0) ? this.userInfo.card_user_groups[0] : ''
        let group = this.userInfo.card_user_group_objs.filter(v => {
          return v.organization === Number(sessionStorage.getItem('organization'))
        })
        this.userForm.group = group.length ? group[0].id : ''
        this.userForm.department = this.userInfo.card_department_group
        this.userForm.cardNumber = this.userInfo.card_no
        this.userForm.phone = this.userInfo.phone
        this.userForm.personNo = this.userInfo.person_no
        this.userForm.validityDate =
          this.userInfo.effective_time && this.userInfo.expiration_time
            ? [this.userInfo.effective_time, this.userInfo.expiration_time]
            : []
        this.userForm.isAutoGroup = this.userInfo.is_auto_group
        this.userForm.idNumber = this.userInfo.id_number ? decrypted(this.userInfo.id_number) : ''
        this.userForm.age = this.userInfo.age
        this.userForm.spare_field = this.userInfo.spare_field
        this.userForm.spare_field2 = this.userInfo.spare_field2
      } else {
        // this.userInfo = {}
        this.userForm = {
          name: '', // 姓名
          gender: '', // 性别
          group: '', // 分组
          department: null, // 部门
          cardNumber: '', // 会员卡号
          cardPwd: '', // 会员卡密码
          phone: '', // 手机号
          personNo: '', // 人员编号
          validityDate: [], // 有效期
          isAutoGroup: false, // 是否自定分组
          idNumber: '',
          age: '',
          spare_field: '',
          spare_field2: ''
        }
      }
      this.getDepartmentList()
    },
    saveSetting() {
      // this.userForm.group.map(item => Number(item))
      let params = {
        person_name: this.userForm.name,
        person_no: this.userForm.personNo,
        gender: this.userForm.gender,
        is_auto_group: this.userForm.isAutoGroup,
        id_number: this.userForm.idNumber ? encrypted(this.userForm.idNumber) : '',
        spare_field: this.userForm.spare_field,
        spare_field2: this.userForm.spare_field2
      }
      if (this.userForm.phone) {
        params.phone = this.userForm.phone
      }
      if (this.userForm.cardNumber) {
        params.card_no = this.userForm.cardNumber
      }
      if (this.userForm.group) {
        params.card_user_group_ids = [Number(this.userForm.group)]
      } else {
        params.card_user_group_ids = []
      }
      if (this.userForm.department) {
        params.card_department_group_id = Number(this.userForm.department)
      }
      // if (this.userForm.cardPwd) { params.card_pwd = this.userForm.cardPwd }
      if (this.userForm.validityDate) {
        params.effective_time = this.userForm.validityDate[0]
        params.expiration_time = this.userForm.validityDate[1]
      }
      if (this.userForm.age) {
        params.age = this.userForm.age
      }
      this.$refs.userForm.validate(valid => {
        if (valid) {
          if (this.type === 'add') {
            this.addCardUser(params)
          } else {
            params.card_user_id = this.userForm.id
            this.editCardUser(params)
          }
        } else {
        }
      })
    },
    async addCardUser(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserAddPost(params)
      this.isLoading = false
      if (res.code === 100016) {
        params.is_sync = true
        // this.visible = false
        this.$confirm(`该用户已存在，是否同步用户信息?`, '提示', {
          confirmButtonText: '同步',
          cancelButtonText: '不同步',
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              this.visible = false
              await this.addCardUser(params)
              instance.confirmButtonLoading = false
              done()
            } else {
              if (!instance.confirmButtonLoading) {
                done()
                this.visible = false
                this.$emit('confirm', 'search')
                // this.confirm()
              }
            }
          }
        })
          .then(e => {})
          .catch(e => {})
        return
      }
      if (res.code === 100020) {
        params.is_sync = true
        // this.visible = false
        this.$confirm(`存在相同用户（已退户），是否取消退户？`, '提示', {
          confirmButtonText: '取消退户',
          cancelButtonText: '否',
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              this.visible = false
              this.cancelQuit(res.data.card_user_id)
              instance.confirmButtonLoading = false
              done()
            } else {
              if (!instance.confirmButtonLoading) {
                done()
                this.visible = false
                this.$emit('confirm', 'search')
                // this.confirm()
              }
            }
          }
        })
          .then(e => {})
          .catch(e => {})
        return
      }
      if (res.code === 100017) {
        this.visible = false
        return this.$emit('showServiceDialog', res.msg)
      }
      if (res.code === 100018) {
        this.isLoading = false
        this.visible = false
        return this.$emit('showServiceDialog', res.msg)
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.isLoading = false
        this.$message.error(res.msg)
      }
    },
    async editCardUser(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserModifyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    async cancelQuit(id) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardOperateCancelPersonQuitPost({
        card_user_ids: [id]
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handleClose(e) {
      this.$refs.userForm.resetFields()
      this.isLoading = false
      this.visible = false
      // this.$emit('close')
    },
    normalizer(node) {
      return {
        id: node.id,
        label: node.group_name,
        children: node.children_list
      }
    },
    // 获取部门信息
    async getDepartmentList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardDepartmentGroupTreeListPost()
      this.isLoading = false
      if (res.code === 0) {
        this.departmentList = this.deleteEmptyGroup(res.data)
        this.treeSelectShow = this.showDepartmentSelect()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    departmentNode(node) {
      return {
        id: node.id,
        label: node.group_name,
        children: node.children_list
      }
    },
    isCurrentDepartment(data, isCurrent) {
      data.forEach(item => {
        if (item.id === this.userInfo.card_department_group) {
          this.isCurrent = true
        } else {
          if (item.children_list) {
            console.log(item.children_list)
            this.isCurrentDepartment(item.children_list, isCurrent)
          }
        }
      })
    },
    // 编辑的时候，判断该部门是否为当前组织可见
    showDepartmentSelect() {
      if (this.userInfo.card_department_group) {
        // 遍历一下该部门id是否存在
        this.isCurrentDepartment(this.departmentList, this.isCurrent)
        if (this.isCurrent) {
          return true
        } else {
          this.userForm.department = this.userInfo.card_department_group_alias
          return false
        }
      } else {
        return true
      }
    },
    getValue(value) {
      this.userForm.department = value
    },
    blurIdNumber() {
      if (this.userForm.idNumber) {
        if (getAgeToIden(this.userForm.idNumber)) {
          this.userForm.age = getAgeToIden(this.userForm.idNumber)
        }
      }
    },
    changeIsAutoGroup(e) {
      if (e) {
        this.userForm.group = ''
      }
    },
    async getCardUserSpareFieldAlias() {
      const res = await this.$apis.apiCardServiceCardOperateGetSpareFieldAliasPost()
      if (res.code === 0) {
        this.spare_field = res.data.spare_field
        this.spare_field2 = res.data.spare_field2
      } else {
        this.$message.error(res.msg)
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    padding: 30px;
  }
}
</style>
