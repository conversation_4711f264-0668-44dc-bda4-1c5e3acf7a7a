<template>
  <div class="withdraw-order container-wrapper">
    <banner :type="type" />
  </div>
</template>

<script>
import banner from '@/views/public/mobile-popup/list'
export default {
  name: 'MerchantPopup',
  components: {
    banner
  },
  data() {
    return {
      type: 'merchant'
    }
  },
  created() {
    // this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
      this.getReviewOrderList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
