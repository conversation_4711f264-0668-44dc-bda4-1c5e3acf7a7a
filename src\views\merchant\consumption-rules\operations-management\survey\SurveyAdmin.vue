<template>
  <div class="SurveyAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="origin" type="export" @click="handleExport()" >导出问卷</button-icon> -->
          <button-icon color="origin" v-permission="['background_marketing.survey_info.add']" type="add" @click="gotoAddOrEdit('add')" >新增问卷</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="name" label="问卷名称" align="center"></el-table-column>
          <el-table-column prop="end_date" label="截止时间" align="center"></el-table-column>
          <el-table-column prop="status_alias" label="状态" align="center" width="90"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-red"
                v-permission="['background_marketing.survey_info.modify_status']"
                v-if="scope.row.status==='enable' && scope.row.status!=='expire'"
                @click="mulOperation('status', scope.row)"
                >禁用</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                v-permission="['background_marketing.survey_info.modify_status']"
                v-if="scope.row.status!=='enable' && scope.row.status!=='expire'"
                @click="mulOperation('status', scope.row)"
                >启用</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="gotoDetail(scope.row)"
                >查看</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                v-permission="['background_marketing.survey_info.modify']"
                v-if="scope.row.status!=='enable' && scope.row.status!=='expire'"
                @click="gotoAddOrEdit('edit',  scope.row)"
                >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                v-permission="['background_marketing.survey_info.delete']"
                v-if="scope.row.status!=='enable'"
                @click="mulOperation('del', scope.row)"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'SurveyAdmin',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: {
        name: {
          type: 'input',
          label: '问卷名称',
          value: '',
          maxlength: 20,
          placeholder: '请输入问卷名称'
        },
        status: {
          type: 'select',
          label: '状态',
          value: '',
          clearable: true,
          placeholder: '请选择状态',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '启用',
            value: 'enable'
          }, {
            label: '未启用',
            value: 'disable'
          }, {
            label: '已结束',
            value: 'expire'
          }]
        }
      }
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getSurveyList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getSurveyList()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getSurveyList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundMarketingSurveyInfoListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results.map(item => {
          item.rules_status = !(item.status === 'disable')
          return item
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSurveyList()
    },
    // 操作提示
    mulOperation(type, data) {
      let title = '提示'
      let content = ''
      let status
      switch (type) {
        case 'del':
          content = '确定删除该调查问卷吗？'
          break;
        case 'status':
          if (data.status === 'disable') {
            content = '确定启用该调查问卷吗？'
            status = 'enable'
          } else {
            content = '确定停用该调查问卷吗？'
            status = 'disable'
          }
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {
              id: data.id
            }
            switch (type) {
              case 'del':
                params.ids = [data.id]
                this.delSurveyInfo(params)
                break;
              case 'status':
                params.status = status
                this.confirmStatus(params)
                break;
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            this.getSurveyList()
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    async confirmStatus(params) {
      const res = await this.$apis.apiBackgroundMarketingSurveyInfoModifyStatusPost(params)
      if (res.code === 0) {
        this.$message.success('成功')
      } else {
        this.$message.error(res.msg)
      }
      this.getSurveyList()
    },
    async delSurveyInfo(params) {
      const res = await this.$apis.apiBackgroundMarketingSurveyInfoDeletePost(params)
      if (res.code === 0) {
        this.$message.success('成功')
      } else {
        this.$message.error(res.msg)
      }
      this.getSurveyList()
    },
    gotoAddOrEdit(type, data) {
      let query = {}
      if (type === 'edit' || type === 'detail') {
        query = {
          data: JSON.stringify(data)
        }
      }
      this.$router.push({
        name: 'AddSurvey',
        params: {
          type
        },
        query: {
          ...query,
          type
        }
      })
    },
    gotoDetail(data) {
      this.$router.push({
        name: 'MerchantSurveyDetail',
        query: {
          id: data.id
        }
      })
    },
    // 导出报表
    handleExport() {
      let url = 'apiBackgroundApproveOrderApproveVisitorJfListExportPost'
      let params = this.formatQueryParams(this.searchFormSetting, this.page, this.pageSize)
      const option = {
        type: 'MealVisitorOrder',
        url,
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss">
.SurveyAdmin{
  .tips{
    color: #ff9b45;
    font-weight: bold;
    font-size: 16px;
    margin-top: 15px;
  }
  .el-dialog__footer{
    text-align: center;
  }
}
</style>
