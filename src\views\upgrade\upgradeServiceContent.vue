<template>
  <div class="upgrade-service-content">
    <div class="upgrade-service-content-header">
      <div class="header-left">
        <div class="w-350 ps-flex-align-c flex-align-c">
          <span class="m-r-100 f-w-700 font-size-24">{{ companyInfo.company_name }}</span>
          <span class="tips">{{ companyInfo.toll_rule_info ? companyInfo.toll_rule_info.toll_version_name : '' }}</span>
        </div>
        <span>到期时间：{{ timeFormat(companyInfo.service_end_time) }}</span>
      </div>
      <div class="header-right">
        <div>
          <el-statistic title="剩余服务天数" :class="[companyInfo.due_day_num <= 30 ? 'red' : '']" :value-style="companyInfo.due_day_num <= 30 ? fontStyle : {}">
            <template slot="formatter">
              {{ companyInfo.due_day_num }}天
            </template>
          </el-statistic>
        </div>
        <div v-if="serviceType === 1">
          <el-statistic title="用户规模使用情况">
            <template slot="formatter">
              {{ companyInfo.use_user_count }}人/{{ companyInfo.user_scale }}人
            </template>
          </el-statistic>
        </div>
      </div>
    </div>
    <div class="upgrade-service-content-content">
      <span class="font-size-14">如购买服务不满足需求，或需要系统升级，请联系客服：<span style="color: #2694F1;text-decoration: underline;">4008082098</span></span>
      <div class="userSize" v-if="serviceType === 1 && chargeTypeRadioNum === 'expansion'">
        <div class="userSize-header">
          <h3>用户规模</h3>
          <span class="m-l-20 font-size-14" v-if="chargeTypeRadioNum === 'expansion'">购买后，用户上限为{{ maximum || ' -- ' }}人</span>
        </div>
        <div>
          <span class="m-r-20 font-size-14">新增用户上限</span>
          <el-input-number v-model="userSize" :step="500" @change="handleChange" :min="1" :max="99999"></el-input-number>
        </div>
      </div>
      <div class="userSize" v-if="chargeTypeRadioNum === 'renew'">
        <div class="userSize-header">
          <h3>用户规模</h3>
        </div>
        <div>
          <span class="m-r-20 font-size-14">现有用户规模: {{ companyInfo.user_scale }} 人</span>
        </div>
      </div>
      <div class="renew" v-if="chargeTypeRadioNum === 'renew'">
        <div class="renew-header">
          <h3>续费</h3>
          <span class="m-l-20 font-size-14">服务时间：{{ timeFormat(serviceTime.service_start_time) }} 至 {{ timeFormat(serviceTime.service_end_time) }}</span>
        </div>
        <div class="renew-content">
          <div
          :class="['renew-content-item', chooseRenew === 1 ? 'isClick' : '']"
          @click="renew(1, serviceType === 1 ? price(companyInfo.toll_rule_info.fee) : price(companyInfo.renew_fee_list[0]))">
            <div>
              <span><span class="renew-content-item-title">1</span>年</span>
              <div class="m-t-5">
                <span class="renew-content-item-price">{{ serviceType === 1 ? '￥' + price(companyInfo.toll_rule_info.fee) : '￥' + price(companyInfo.renew_fee_list[0]) }}</span>
                <span v-if="serviceType === 1" style="font-size: 14px;">/人/年</span>
              </div>
            </div>
          </div>
          <div
            v-if="(companyInfo.toll_rule_info && companyInfo.toll_rule_info.second_discount) || serviceType === 2"
            :class="['renew-content-item', chooseRenew === 2 ? 'isClick' : '']"
            @click="renew(2, serviceType === 1 ? price(companyInfo.toll_rule_info.fee * price(companyInfo.toll_rule_info.second_discount)) : price(companyInfo.renew_fee_list[1]))">
            <div v-if="serviceType === 1" class="renew-content-item-tip">
              {{ discount(companyInfo.toll_rule_info.second_discount) }}折
            </div>
            <div>
              <span><span class="renew-content-item-title">2</span>年</span>
              <div class="m-t-5">
                <span
                  class="renew-content-item-price">
                  {{ serviceType === 1 ? '≈￥' + price(companyInfo.toll_rule_info.fee * price(companyInfo.toll_rule_info.second_discount)) : '￥' + price(companyInfo.renew_fee_list[1]) }}
                </span>
                <span v-if="serviceType === 1" style="font-size: 14px;">/人/年</span>
              </div>
            </div>
            <span v-if="serviceType === 1" class="renew-content-item-bottom">{{ companyInfo.toll_rule_info.alias }}</span>
          </div>
          <div
            v-if="(companyInfo.toll_rule_info && companyInfo.toll_rule_info.third_discount) || serviceType === 2"
            :class="['renew-content-item', chooseRenew === 3 ? 'isClick' : '']"
            @click="renew(3, serviceType === 1 ? price(companyInfo.toll_rule_info.fee * price(companyInfo.toll_rule_info.third_discount)) : price(companyInfo.renew_fee_list[2]))">
            <div
              v-if="serviceType === 1"
              class="renew-content-item-tip">
              {{ discount(companyInfo.toll_rule_info.third_discount) }}折
            </div>
            <div>
              <span><span class="renew-content-item-title">3</span>年</span>
              <div class="m-t-5">
                <span
                  class="renew-content-item-price">
                  {{ serviceType === 1 ? '≈￥' + price(companyInfo.toll_rule_info.fee * price(companyInfo.toll_rule_info.third_discount)) : '￥' + price(companyInfo.renew_fee_list[2]) }}
                </span>
                <span v-if="serviceType === 1" style="font-size: 14px;">/人/年</span>
              </div>
            </div>
            <span v-if="serviceType === 1" class="renew-content-item-bottom">{{ companyInfo.toll_rule_info.alias }}</span>
          </div>
          <div
            v-if="companyInfo.toll_rule_info && companyInfo.toll_rule_info.fourth_discount && serviceType !== 2"
            :class="['renew-content-item', chooseRenew === 4 ? 'isClick' : '']"
            @click="renew(4, price(companyInfo.toll_rule_info.fee * price(companyInfo.toll_rule_info.fourth_discount)))">
            <div
              v-if="serviceType === 1"
              class="renew-content-item-tip">
              {{ discount(companyInfo.toll_rule_info.fourth_discount) }}折
            </div>
            <div>
              <span><span class="renew-content-item-title">4</span>年</span>
              <div class="m-t-5">
                <span
                  class="renew-content-item-price">
                  {{ '≈￥' + price(companyInfo.toll_rule_info.fee * price(companyInfo.toll_rule_info.fourth_discount)) }}
                </span>
                <span v-if="serviceType === 1" style="font-size: 14px;">/人/年</span>
              </div>
            </div>
            <span v-if="serviceType === 1" class="renew-content-item-bottom">{{ companyInfo.toll_rule_info.alias }}</span>
          </div>
          <div
            v-if="companyInfo.toll_rule_info && companyInfo.toll_rule_info.fifth_discount && serviceType !== 2"
            :class="['renew-content-item', chooseRenew === 5 ? 'isClick' : '']"
            @click="renew(5, price(companyInfo.toll_rule_info.fee * price(companyInfo.toll_rule_info.fifth_discount)))">
            <div
              v-if="serviceType === 1"
              class="renew-content-item-tip">
              {{ discount(companyInfo.toll_rule_info.fifth_discount) }}折
            </div>
            <div>
              <span><span class="renew-content-item-title">5</span>年</span>
              <div class="m-t-5">
                <span
                  class="renew-content-item-price">
                  {{ '≈￥' + price(companyInfo.toll_rule_info.fee * price(companyInfo.toll_rule_info.fifth_discount)) }}
                </span>
                <span v-if="serviceType === 1" style="font-size: 14px;">/人/年</span>
              </div>
            </div>
            <span v-if="serviceType === 1" class="renew-content-item-bottom">{{ companyInfo.toll_rule_info.alias }}</span>
          </div>
        </div>
      </div>
      <div class="payWay">
        <div>
          <h3>支付方式</h3>
        </div>
        <div class="payWay-content">
          <div :class="['payWay-content-item', 'm-r-20', clickNum === index ? 'isClick' : '']" v-for="(item, index) of payWayList" :key="index" @click="selectThis(item)">
            <img :src="item.imgUrl">
            <span class="m-l-16">{{ item.text }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="upgrade-service-content-footer">
      <div class="footer-top">
        <div class="footer-top-left">
          <span class="font-size-16">实付金额：</span>
          <span class="m-l-10 font-size-28 priceShow">￥ {{ totalPrice }}</span>
        </div>
        <div class="footer-top-right">
          <span class="font-size-14">付款完成后可申请发票</span>
          <div class="buyNow m-l-20" @click="payNow">立即购买</div>
        </div>
      </div>
      <div class="m-t-10">
        <el-checkbox v-model="isRead"></el-checkbox>
        <span class="checkbox-label" :style="isRead ? {color: '#FF9B45'} : {}">
          我已认真阅读
        </span>
        <span v-for="(agreement, index) in agreementList" :key="index" style="color:#2694F1; text-decoration: underline; font-size: 14px;" @click="gotoTermsOfService(agreement)">
          《{{ agreement.agreement_type_alias }}》
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import * as dayjs from 'dayjs'
import { divide, times } from '@/utils/constants'
import NP from 'number-precision'
export default {
  name: 'upgradeServiceContent',
  props: {
    companyInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 商户自身选的服务
    serviceType: {
      type: [Number, String],
      default: ""
    },
    // 商户点击的服务按钮
    chargeTypeRadioNum: {
      type: String,
      default: ''
    }
  },
  computed: {
    timeFormat() {
      return d => {
        return dayjs(d).format('YYYY-MM-DD')
      }
    },
    discount() {
      return d => {
        if (d) {
          return NP.divide(d, 10).toFixed(1)
        }
      }
    },
    price() {
      return d => {
        if (d) {
          return divide(d)
        }
      }
    },
    maximum() {
      return this.companyInfo.user_scale + this.userSize
    }
  },
  watch: {
    chargeTypeRadioNum(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.isRead = false
      }
      if (newVal === 'renew') {
        this.isRead = true
      }
      this.totalPrice = 0
      this.clickNum = 4
    },
    companyInfo: {
      handler: function(newVal) {
        if (newVal) {
          this.serviceTime.service_start_time = newVal.service_start_time
          this.serviceTime.service_end_time = newVal.service_end_time
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      userSize: 100,
      payWayList: [
        {
          id: 0,
          imgUrl: require('@/assets/img/pay-wx.png'),
          value: 'wxpay',
          text: '微信支付'
        },
        {
          id: 1,
          imgUrl: require('@/assets/img/pay-zfb.png'),
          value: 'alipay',
          text: '支付宝'
        },
        {
          id: 2,
          imgUrl: require('@/assets/img/pay-zz.png'),
          value: 'transfer',
          text: '对公转账'
        }
      ],
      clickNum: 4,
      payMethod: '',
      chooseRenew: 0,
      renewForPersonAYear: 0,
      totalPrice: 0,
      agreementList: [
        {
          agreement_type_alias: "服务条款",
          agreement_type: "TS"
        }
      ],
      isRead: false,
      fontStyle: {
        color: '#fd594e'
      },
      serviceTime: {
        service_start_time: '',
        service_end_time: ''
      }
    }
  },
  methods: {
    handleChange() {
      if (!this.userSize) {
        return this.$message.error('用户上限不能为空')
      }
      if (this.companyInfo.due_day_num === 0 && this.serviceType === 2) return this.$message.error('目前服务天数为0，请先续费')
      let params = this.setParams()
      this.calculatePrice(params)
    },
    selectThis(e) {
      if (this.companyInfo.due_day_num === 0 && this.serviceType === 2) return this.$message.error('目前服务天数为0，请先续费')
      this.clickNum = e.id
      this.payMethod = e.value
      if (this.clickNum !== 4) {
        this.handleChange()
      }
    },
    renew(e, price) {
      this.chooseRenew = e
      this.renewForPersonAYear = price
      this.serviceTime.service_end_time = dayjs(this.companyInfo.service_end_time).add(e, 'year').format('YYYY-MM-DD')
      this.handleChange()
    },
    // 下单
    payNow() {
      // 检查服务条款是否已读
      if (!this.isRead) return this.$message.error('请阅读服务条款并勾选已读后重试')
      // 检查是否选了支付方式
      if (this.clickNum === 4) return this.$message.error('请选择支付方式')
      let params = this.setParams()
      Object.assign(params, { pay_method: this.payMethod })
      params.price = times(this.totalPrice) // 调整price
      this.$apis.apiBackgroundTollBackgroundTollOrderCreatePost(params)
        .then(res => {
          if (res.code === 0) {
            this.$emit('showQRCode', res.data, this.totalPrice)
            this.$emit('refresh')
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    // 设置params
    setParams() {
      let params = {
        transaction_type: this.chargeTypeRadioNum,
        price: this.serviceType === 1 ? this.companyInfo.toll_rule_info.fee : 0,
        user_scale: this.chargeTypeRadioNum === 'expansion' ? this.userSize : this.companyInfo.user_scale
      }
      if (this.chargeTypeRadioNum === 'renew') {
        Object.assign(params, { renew_year: this.chooseRenew })
        switch (this.serviceType) {
          case 1:
            params.price = times(this.renewForPersonAYear)
            break
          case 2:
            // 加多层判断，如果没有选择续费时长返回警告
            if (!this.chooseRenew) {
              return this.$message.error('请选择续费年限')
            } else {
              params.price = this.companyInfo.renew_fee_list[this.chooseRenew - 1]
            }
            break
        }
      }
      return params
    },
    // 计算价格
    calculatePrice(params) {
      this.$apis.apiBackgroundTollBackgroundTollOrderGetCalcFeePost(params)
        .then(res => {
          if (res.code === 0) {
            this.totalPrice = res.data.calc_fee ? divide(res.data.calc_fee) : 0
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    gotoTermsOfService(row) {
      let url = window.location.origin + '/#/agreement?type=' + row.agreement_type + '&key=AGREEMENTLIST'
      window.open(url, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.upgrade-service-content {
  width: 1000px;
  background-color: #fff;
  margin-top: 20px;
  border: 1px solid #DCDDDE;
  box-shadow: 3px 3px 5px #DCDDDE;
  border-radius: 10px;
  &-header {
    border-bottom: 1px solid #D7D7D7;
    display: flex;
    height: 130px;
    .header-left {
      border-radius: 10px 0px 0px 0px;
      flex-grow: 2;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: flex-start;
      padding: 20px;
      background-color: #101A3B;
      color: #fff;
      .tips {
        max-width: 7em;
        font-size: 14px;
        padding: 2px 8px;
        border: 1px solid #fff;
        border-radius: 4px;
        background-color: #fff;
        color: #101A3B
      }
    }
    .header-right {
      flex-grow: 6;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
  }
  &-content {
    padding: 20px;
    border-bottom: 1px solid #D7D7D7;
    .userSize {
      &-header {
        display: flex;
        align-items: center;
      }
    }
    .renew {
      &-header {
        display: flex;
        align-items: center;
      }
      &-content {
        display: flex;
        &-item {
          padding: 20px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;
          margin-right: 20px;
          width: 200px;
          height: 100px;
          border: 1px solid #DCDDDE;
          box-shadow: 3px 3px 5px #DCDDDE;
          border-radius: 6px;
          position: relative;
          &-title {
            font-weight: 600;
            font-size: 24px;
            margin-right: 5px;
          }
          &-bottom {
            text-decoration: line-through;
            font-size: 12px;
            color: #a19ca5;
            position: absolute;
            bottom: 5px;
          }
          &-tip {
            padding: 3px 5px;
            font-size: 12px;
            position: absolute;
            top: 0;
            right: 0;
            border-radius: 0px 6px;
            border: 1px solid #ff9b45;
            background-color: #ff9b45;
            color: #fff;
          }
          &-price{
            color: #fd594e;
            font-size: 20px;
          }
        }
      }
    }
    .payWay {
      &-content{
        display: flex;
        &-item{
          width: 200px;
          display: flex;
          padding: 20px;
          justify-content: center;
          align-items: center;
          border: 1px solid #DCDDDE;
          box-shadow: 3px 3px 5px #DCDDDE;
          border-radius: 10px;
        }
      }
    }
    .isClick{
      border: 2px solid #ff9b45;
    }
  }
  &-footer {
    padding: 20px;
    .footer-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-left {
        display: flex;
        align-items: center;
        .priceShow {
          color: #ff9b45;
        }
      }
      &-right {
        display: flex;
        align-items: center;
        .buyNow{
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 8px;
          width: 170px;
          height: 50px;
          background-color: #ff9b45;
          color: #fff;
        }
      }
    }
  }
}
.checkbox-label {
  margin-left: 10px;
  font-size: 14px;
}
::v-deep .red {
  .head {
    color: #fd594e;
  }
}
</style>
