<template>
  <div class="evaluate-setting-dialog">
    <CustomDrawer
      :show.sync="showDrawer"
      :size="size"
      :title="title"
      :cancelShow="false"
      :confirmShow="false"
      v-bind="$attrs"
      v-on="$listeners"
    >
    <el-form
      :model="dialogForm"
      @submit.native.prevent
      ref="dialogForm"
      :rules="dialogFormRules"
      label-width="100px"
      class="dialog-evaluate-form"
      v-loading="isLoading"
      size="mini"
    >
      <el-form-item label="菜品评价" class="m-t-6">
        <el-switch v-model="dialogForm.food_evaluation_status" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        <span class="switch-text origin-color m-l-20">关闭后用户只可对整笔订单进行评价</span>
        <div v-if="dialogForm.food_evaluation_status" class="rate-setting">
          <div class="origin-color">设置菜品星级评价内容，最多设置3个条件</div>
          <div class="">
            <el-form-item v-for="(item, index) in dialogForm.food_evaluation_field" :key="index" :prop="'food_evaluation_field.'+index+'.field_name'" :rules="dialogFormRules.food_evaluation_field" :show-message="false" label="" class="m-t-6">
              <el-input v-model="item.field_name" class="ps-input max-w-rate" :maxLength="5"></el-input>
              <el-rate class="rate inline-block m-l-10" v-model="item.score" disabled-void-color="#ff9b45" disabled></el-rate>
              <span class="square-icon-box vertical-middle m-l-20">
                <svg-icon v-if="dialogForm.food_evaluation_field.length < 3" icon-class="square_plus" class="pointer m-r-10" @click.native="addScoreHandle('food_evaluation_field', index)"/>
                <svg-icon v-if="dialogForm.food_evaluation_field.length !== 1" icon-class="square_minus" class="pointer" @click.native="deleteHandle('food_evaluation_field', index)" />
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="星级设置" class="m-t-6">
        <el-form-item v-for="(item, index) in dialogForm.start_score_field" :prop="'start_score_field.'+index+'.content'" :rules="dialogFormRules.start_score_field" :show-message="false" label="" label-width="0" class="rate-text-item">
          <span class="rate-text-albel vertical-middle m-r-20">{{ item.score }}星</span>
          <el-input v-model="item.content" class="ps-input max-w-text" :maxLength="5"></el-input>
        </el-form-item>
      </el-form-item>
      <el-form-item label="评价时间" class="m-t-6">
        <el-form-item label="" label-width="0" prop="on_scene" :show-message="false" class="rate-text-item">
          <span class="rate-text-albel vertical-middle">堂食订单：</span>
          <span class="inline-block vertical-middle max-s-w m-r-10">订单完成后</span>
          <el-input v-model="dialogForm.on_scene" class="ps-input max-s-w"></el-input>
          <span class="vertical-middle m-l-10">天内可评价</span>
        </el-form-item>
        <el-form-item label="" label-width="0" class="rate-text-item">
          <span class="rate-text-albel vertical-middle">预约订单：</span>
          <el-form-item label="" label-width="0" prop="reservation_order_evaluate_type" :show-message="false" class="rate-text-item inline-block">
            <el-select
              class="max-s-w ps-select m-r-10"
              v-model="dialogForm.reservation_order_evaluate_type"
            >
              <el-option label="核销后" value="release_end"></el-option>
              <el-option label="支付后" value="pay_end"></el-option>
              <el-option label="餐段结束后" value="meal_end"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="" label-width="0" prop="reservation_order" :show-message="false" class="rate-text-item inline-block">
            <el-input v-model="dialogForm.reservation_order" class="ps-input max-s-w"></el-input>
          </el-form-item>
          <span class="vertical-middle m-l-10">天内可评价</span>
        </el-form-item>
      </el-form-item>
      <el-form-item label="匿名设置" class="m-t-6">
        <el-switch v-model="dialogForm.anonymous" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        <span class="switch-text origin-color m-l-20">开启后支持用户进行匿名评价</span>
      </el-form-item>
      <el-form-item label="快捷回复" class="m-t-6">
        <el-input v-model="dialogForm.shortcut_reply_field" class="ps-input w-input" type="textarea" :rows="4" :maxlength="150" show-word-limit  placeholder="输入您常用的回复语，不超过150字"></el-input>
      </el-form-item>
      <el-form-item label-width="0">
        <div class="ps-drawer-footer">
          <!-- ps-cancel-btn -->
          <el-button size="medium" @click="closeHandle">取消</el-button>
          <el-button size="medium" class="ps-origin-btn" type="primary" @click="submitHandle">保存</el-button>
        </div>
      </el-form-item>
    </el-form>
    </CustomDrawer>
  </div>
</template>

<script>
import { t } from 'umy-ui/lib/locale';

export default {
  name: 'EvaluateSettingDialog',
  props: {
    isshow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '基础设置'
    },
    size: {
      type: String,
      default: '700px'
    },
    dialogInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    let validateNumber = (rule, value, callback) => {
      let reg = /^[0-9]$/
      if (!reg.test(value)) {
        callback(new Error('格式不正确'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      score: 0,
      dialogForm: {
        food_evaluation_status: true, // 菜品评价开关
        shortcut_reply_field: '', // 回复短语
        on_scene: 0, // 堂食订单可X天评价
        reservation_order: '', // 预约订单可X天评价
        reservation_order_evaluate_type: '', // 预约订单评价类型
        anonymous: false, // 是否允许匿名评价
        start_score_field: [
          { score: 5, content: '非常好' },
          { score: 4, content: '好' },
          { score: 3, content: '较好' },
          { score: 2, content: '一般' },
          { score: 1, content: '差' }
        ],
        food_evaluation_field: [{score: 5, field_name: ""}], // 评价类型，最大3个
        evaluation_score_field: [] // 旧的保留字段
      },
      dialogFormRules: {
        food_evaluation_field: [{ required: true, message: '请输入', trigger: 'blur' }],
        start_score_field: [{ required: true, message: '请输入', trigger: 'blur' }],
        reservation_order_evaluate_type: [{ required: true, message: '请选择', trigger: 'blur' }],
        on_scene: [
          { validator: validateNumber, trigger: "blur" }
        ],
        reservation_order: [
          { validator: validateNumber, trigger: "blur" }
        ],
      }
    }
  },
  computed: {
    showDrawer: {
      get() {
        if (this.isshow) {
          this.getOperationData()
        }
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {

  },
  created() {

  },
  mounted() {
  },
  methods: {
    async getOperationData() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOperationManagementEvaluationSettingListPost()
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let rateList = Object.keys(res.data.start_score_field).sort((a, b) => { return b - a })
        this.dialogForm.start_score_field = rateList.map((item, i) => {
          return {
            score: Number(item),
            content: res.data.start_score_field[item]
          }
        })
        this.dialogForm.food_evaluation_field = res.data.food_evaluation_field ? res.data.food_evaluation_field : []
        this.dialogForm.evaluation_score_field = res.data.evaluation_score_field ? res.data.evaluation_score_field : []
        this.dialogForm.evaluation_content_field = res.data.evaluation_content_field ? res.data.evaluation_content_field : []
        this.dialogForm.reservation_order_evaluate_type = res.data.reservation_order_evaluate_type
        this.dialogForm.reservation_order = res.data.reservation_order
        this.dialogForm.on_scene = res.data.on_scene
        this.dialogForm.anonymous = !!res.data.anonymous
        if (res.data.shortcut_reply_field && res.data.shortcut_reply_field.length > 0) {
          this.dialogForm.shortcut_reply_field = res.data.shortcut_reply_field[0]
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    closeHandle() {
      this.showDrawer = false
    },
    addScoreHandle(type, index) {
      this.dialogForm[type].splice(index + 1, 0, {score: 5, field_name: ""})
    },
    deleteHandle(type, index) {
      if (this.dialogForm[type].length > 1) {
        this.dialogForm[type].splice(index, 1)
      }
      console.log(this.dialogForm[type])
    },
    submitHandle() {
      this.$refs.dialogForm.validate(valid => {
        if (valid) {
          if (this.isLoading) return;
          this.getAppealPendList()
        }
      })
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key] !== '') {
          if (data[key] instanceof Array) {
            if (data[key].length > 0) {
              params[key] = data[key]
            }
          } else if (key === 'anonymous') {
            params[key] = data[key] ? 1 : 0
          } else if (key === 'shortcut_reply_field') {
            params[key] = [data[key]]
          } else {
            params[key] = data[key]
          }
        }
      }
      return params
    },
    async getAppealPendList() {
      console.log(this.formatQueryParams(this.dialogForm))
      // return
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundOperationManagementEvaluationSettingModifyPost(this.formatQueryParams(this.dialogForm)))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('成功')
        // this.getOperationData()
        this.showDrawer = false
        this.$emit('confirm')
      } else {
        this.$message.error(res.msg)
      }
    }
  }
};
</script>

<style scoped lang="scss">
.evaluate-setting-dialog{
  .w-input {
    width: 100%;
  }
  .max-w-rate {
    width: 70px;
  }
  .max-w-text {
    width: 90px;
  }
  .max-s-w {
    width: 80px;
  }
  .origin-color {
    color: var(--origin)
  }
  .vertical-middle {
    vertical-align: middle;
  } 
  .dialog-evaluate-form {
    margin-right: 14%;
    .switch-text {
      vertical-align: middle;
    }
    .rate {
      ::v-deep .el-rate__icon{
        margin-right: 0;
        font-size: 24px;
      }
    }
    .square-icon-box {
      .svg-icon {
        font-size: 15px;
      }
    }
    ::v-deep.el-input__inner {
      padding: 0 6px;
    }
    ::v-deep.el-form-item{
      margin-bottom: 10px;
    }
  }
}
</style>
