<template>
  <el-image class="column-image" :lazy="col.lazy" :src="row[prop]" :preview-src-list="srcList">
    <div slot="error" class="image-slot">
      <i class="el-icon-picture-outline"></i>
    </div>
  </el-image>
</template>
<script>
/**
 * @description 图片组件
 * @property {Object} row 组件数据
 * @property {Object} col 组件配置
 * @property {String} prop 组件类型
 */
export default {
  name: 'ColumnImage',
  props: {
    row: {
      type: Object
    },
    col: { // col.lazy 图片是否懒加载， col.preview 是否大图展示
      type: Object
    },
    prop: {
      type: String
    }
  },
  data() {
    return {}
  },
  computed: {
    srcList() {
      let src = []
      if (this.col.preview) {
        let value = this.row[this.prop]
        if (Array.isArray(value)) {
          src = value
        } else {
          src = [value]
        }
      }
      return src
    }
  },
  mounted () {
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
  .column-image{
    max-width: 60px;
    max-height: 80px;
  }
</style>
