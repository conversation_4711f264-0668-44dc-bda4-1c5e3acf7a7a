<template>
  <div>
    <div class="container-wrapper">
      <evaluate :form-setting="searchFormSetting" type="Merchant" :api-url="apiUrl" :autoSearch="false"/>
    </div>
  </div>
</template>

<script>
import { EVALUATE_LIST } from './constants'
import { deepClone } from '@/utils'
import Evaluate from "@/components/Evaluate"

export default {
  name: 'EvaluateList',
  components: { Evaluate },
  data() {
    return {
      searchFormSetting: deepClone(EVALUATE_LIST),
      apiUrl: 'apiBackgroundOperationManagementOrderEvaluationListPost'
    }
  },
  mounted() {
    // this.initLoad()
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
</style>
