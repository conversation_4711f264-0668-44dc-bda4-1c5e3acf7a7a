<template>
  <!-- 添加/编辑 -->
  <div>
    <custom-drawer
      :title="title"
      :show.sync="visible"
      :size="size"
      class="drawer-wrapper"
      v-bind="$attrs"
      v-on="$listeners"
      confirm-text="保存"
      @close="handlerClose"
      @cancel="clickCancleHandle"
      @confirm="clickConfirmHandle"
    >
      <el-form
        :model="drawerForm"
        ref="drawerFormRef"
        :rules="drawerFormRules"
        label-width="90px"
        class="dialog-form m-t-20"
        v-loading="isLoading"
        :status-icon="false"
      >
        <el-form-item label="负债名称" prop="name">
          <el-input v-model="drawerForm.name" class="w-300" placeholder="请输入负债名称" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item :label="'负债类别'" prop="liability_category">
          <el-select class="w-300" v-model="drawerForm.liability_category" filterable placeholder="请选择" clearable>
            <el-option
              v-for="(item, index) in debtCategoryList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="'负债类型'" prop="liability_type">
          <el-select class="w-300" v-model="drawerForm.liability_type" filterable placeholder="请选择" clearable>
            <el-option
              v-for="(item, index) in debtTypeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="债权人" prop="person">
          <el-input v-model="drawerForm.person" class="w-300" placeholder="请输入债权人" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="负债金额" prop="price">
          <el-input v-model="drawerForm.price" class="w-300" placeholder="请输入负债金额"></el-input>
        </el-form-item>
        <el-form-item :label="'附件'" prop="file">
          <div class="certification-info-show-tips">图片最大不超过5MB，仅支持jpg,png格式</div>
          <el-upload
            ref="uploadFoodImage"
            :class="{ 'upload-food': true, 'hide-upload': drawerForm.imageFileList.length >= 5 }"
            drag
            :data="uploadParams"
            :action="actionUrl"
            :multiple="false"
            :file-list="drawerForm.imageFileList"
            list-type="picture-card"
            :on-change="handelChange"
            :on-success="handleImgSuccess"
            :before-upload="beforeImgUpload"
            :limit="5"
            :headers="headersOpts"
          >
            <i v-if="drawerForm.imageFileList.length <= 5" class="el-icon-plus"></i>
            <div
              slot="file"
              slot-scope="{ file }"
              v-loading="file.status === 'uploading'"
              element-loading-text="上传中"
            >
              <div class="upload-food-img"><img :src="file.url" alt="" /></div>
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleImgRemove(file, 'imageFile')">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input
            type="textarea"
            maxlength="100"
            v-model="drawerForm.remark"
            :autosize="{ minRows: 4, maxRows: 6 }"
            placeholder="请输入备注"
            class="ps-input w-300"
          ></el-input>
        </el-form-item>
      </el-form>
    </custom-drawer>
    <!-- end -->
    <!-- 图片预览 -->
    <image-viewer v-model="dialogVisible" :z-index="3000" :on-close="closePreview" :preview-src-list="dialogImageUrl" />
  </div>
</template>

<script>
import { getToken, getSuffix, times } from '@/utils/index'
import { positiveMoney } from '@/utils/validata'
export default {
  name: 'DebtInfoAdd',
  props: {
    isShow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '新增负债'
    },
    size: {
      type: [String, Number],
      default: '40%'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  // mixins: [activatedLoadData],
  data() {
    let validataPrice = (rule, value, callback) => {
      if (value !== '') {
        if (!positiveMoney(value) || !Number(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    return {
      isLoading: false,
      drawerForm: {
        name: '',
        liability_category: '',
        liability_type: '',
        person: '',
        price: '',
        imageFile: [],
        imageFileList: [],
        remark: ''
      },
      drawerFormRules: {
        name: [{ required: true, message: '请输入负债名称', trigger: ['blur', 'change'] }],
        liability_category: [{ required: true, message: '请选择类别', trigger: ['blur', 'change'] }],
        liability_type: [{ required: true, message: '请选择类型', trigger: ['blur', 'change'] }],
        person: [{ required: true, message: '请输入债权人', trigger: ['blur', 'change'] }],
        price: [
          { required: true, message: '请输入金额', trigger: ['blur', 'change'] },
          { validator: validataPrice, trigger: ['blur', 'change'] }
        ],
        remark: [{ required: false, message: '请输入备注', trigger: ['blur', 'change'] }]
      },
      debtCategoryList: [
        {
          label: '非流动负债',
          value: 'non_flowing_liability'
        },
        {
          label: '流动负债',
          value: 'flowing_liability'
        }
      ],
      debtTypeList: [], // 负债类型
      headersOpts: {
        // 上传插入表头
        TOKEN: getToken()
      },
      uploading: false, // 上传加载中
      actionUrl: '/api/background/file/upload',
      uploadParams: {
        // 上传头
        prefix: 'DebtInfoAdd',
        key: 'DebtInfoAdd' + new Date().getTime() + Math.floor(Math.random() * 150)
      },
      dialogImageUrl: [],
      dialogVisible: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        this.initLoad()
      }
    },
    'drawerForm.liability_category'(val) {
      this.debtTypeList = []
      this.drawerForm.liability_type = ''

      if (val) {
        // 检查是否选择了全部的选项
        let hasFixedAsset = val.includes('non_flowing_liability')
        let hasCurrentAsset = val.includes('flowing_liability')
        if (hasFixedAsset) {
          // 只选择了非流动负债
          this.debtTypeList = [
            {
              label: '长期借款',
              value: 'long_borrowing'
            },
            {
              label: '长期应付款',
              value: 'long_payable'
            },
            {
              label: '其他负债',
              value: 'other_liability'
            }
          ]
        } else if (hasCurrentAsset) {
          // 只选择了流动负债
          this.debtTypeList = [
            {
              label: '短期借款',
              value: 'short_borrowing'
            },
            {
              label: '应付账款',
              value: 'payable'
            },
            {
              label: '预收账款',
              value: 'pre_harvest_payable'
            },
            {
              label: '其他负债',
              value: 'other_liability'
            }
          ]
        }
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    async initLoad() {},
    clickConfirmHandle() {
      let api
      let params = {
        name: this.drawerForm.name,
        liability_category: this.drawerForm.liability_category,
        liability_type: this.drawerForm.liability_type,
        liability_state: 'not_repayment', // 状态
        file: this.drawerForm.imageFile,
        person: this.drawerForm.person,
        price: times(this.drawerForm.price),
        remark: this.drawerForm.remark
      }
      this.$refs.drawerFormRef.validate(valid => {
        if (valid) {
          if (this.isLoading) return
          this.isLoading = true
          api = this.$apis.apiBackgroundFundSupervisionBusLiabilityV4LiabilityAddPost(params)
          this.senddrawerForm(api)
        } else {
        }
      })
    },
    async senddrawerForm(api) {
      const [err, res] = await this.$to(api)
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('成功')
        this.$emit('clickConfirm')
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = [file.url]
      this.dialogVisible = true
    },
    closePreview() {
      this.dialogVisible = false
    },
    handlerClose(e) {
      this.visible = false
    },
    // 设置文件名
    handelChange(file, fileList) {
      this.uploadParams.key =
        this.uploadParams.prefix + '_' + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    },
    // 上传成功数据处理
    handleImgSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.drawerForm.imageFileList = fileList
        this.drawerForm.imageFile.push(res.data.public_url)
        this.$nextTick(() => {
          this.$refs.drawerFormRef.clearValidate('imageFile')
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除图片
    handleImgRemove(file, type) {
      let index = this.drawerForm[type + 'List'].findIndex(item => item.url === file.url)
      this.drawerForm[type].splice(index, 1)
      this.drawerForm[type + 'List'].splice(index, 1)
    },
    // 上传图片前校验文件类型
    beforeImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.svg']
      const isLt5M = file.size / 1024 / 1024 > 5
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是PNG/JPG/SVG格式!')
        return false
      }

      if (isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
    }
  }
}
</script>

<style lang="scss" scope>
.drawer-wrapper {
  font-size: 14px;
  .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
  .upload-food {
    overflow: hidden;
    max-height: 830px;
    &.hide-upload {
      .el-upload--picture-card {
        display: none;
      }
    }
    .el-upload--picture-card {
      border: none;
    }
    .el-upload-dragger {
      width: 145px;
      height: 145px;
    }
    .upload-food-img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 145px;
      height: 145px;
      img {
        max-width: 145px;
        max-height: 145px;
      }
    }
  }
}
</style>
