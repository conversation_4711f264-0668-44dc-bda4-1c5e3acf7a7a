<template>
  <!-- 表21 从业人员食品安全培训记录表 -列表 -->
  <div class="assignment-ledger-permission container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandler"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="gotoExport" v-permission="['background_fund_supervision.ledger_food_safety.employees_food_safety_training_record_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content" style="padding-bottom: 0">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #showOverflowTooltip="{ row }">
              <el-tooltip class="item" effect="dark" :content="row[item.key]" placement="top-start">
                <div class="line-1">{{row[item.key]}}</div>
              </el-tooltip>
            </template>
            <template #record_img="{ row }">
              <el-button
                v-if="row.record_img"
                type="text"
                size="small"
                @click="previewImage(row.record_img)"
                class="view-btn"
              >
                查看
              </el-button>
              <span v-else class="no-image">暂无图片</span>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[5, 10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->

      <!-- 详情 -->
      <details-drawer
        :visible.sync="isShowDrawer"
        ledgerSerialNumber="24"
        :confirmShow="false"
        :showFooter="true"
        :printShow="true"
        cancelText="关 闭"
        @print="clickPrint"
      ></details-drawer>

      <!-- 图片预览 -->
      <el-dialog
        title="图片预览"
        :visible.sync="showImagePreview"
        width="80%"
        center
        append-to-body
        :close-on-click-modal="true"
      >
        <div class="image-preview-container">
          <el-image
            v-for="(image, index) in previewImages"
            :key="index"
            :src="image"
            :preview-src-list="previewImages"
            :initial-index="index"
            fit="contain"
            class="preview-image"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
              <div>加载失败</div>
            </div>
          </el-image>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN, TABLE_HEAD_DATA_EMPLOYEES_FOOD_SAFETY_TRAINING_RECORD_FORM } from './constants'
import detailsDrawer from '@/views/merchant/supervision-and-management/baiyun-excel/all-ledger-details-drawer/index.vue'
export default {
  name: 'XuexiaoHuiyiJiyaoManagementLedger',
  mixins: [exportExcel],
  data() {
    return {
      isShowDrawer: false, // 详情抽屉
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_EMPLOYEES_FOOD_SAFETY_TRAINING_RECORD_FORM), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_EMPLOYEES_FOOD_SAFETY_TRAINING_RECORD_FORM), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN), // 查询表单配置
      // 图片预览相关
      showImagePreview: false,
      previewImages: []
    }
  },
  created() {
    this.initLoad()
  },
  components: {
    detailsDrawer
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetEmployeeFoodSafetyTrainingRecordPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = results.map(item => {
          item.training_theme_alias = item.training_theme.join('，')
          item.training_method_alias = item.training_method.join('，')
          item.assessment_method_alias = item.assessment_method.join('，')
          item.evaluation_result_verbose = item.evaluation_result === 'qualified' ? "合格" : "不合格"
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 查看详情
    openDetail(data) {
      this.isShowDrawer = true
    },
    // 预览图片
    previewImage(imageUrl) {
      if (!imageUrl) {
        this.$message.warning('暂无图片')
        return
      }

      // 确保imageUrl是数组格式
      const imageList = Array.isArray(imageUrl) ? imageUrl : [imageUrl]

      // 设置预览数据并显示
      this.previewImages = imageList
      this.showImagePreview = true
    },
    // 获取提交操作员
    getOperatorUsername(row) {
      const operatorUsername = row.operator_username || ''
      const operatorMemberName = row.operator_member_name || ''
      return operatorMemberName + (operatorUsername ? `(${operatorUsername})` : '')
    },
    // 获取复核人一
    getConfirmerUsername(row) {
      const confirmerUsername = row.confirmer_username || ''
      const confirmerMemberName = row.confirmer_member_name || ''
      return confirmerMemberName + (confirmerUsername ? `(${confirmerUsername})` : '')
    },
    // 获取复核人二
    getReviewersList(reviewersList) {
      let list = []
      if (reviewersList && reviewersList.length > 0) {
        reviewersList.forEach(item => {
          list.push(item.member_name + '(' + item.username + ' )')
        })
      }
      return list.join(',')
    },
    // 打印
    clickPrint() {
      console.log('打印')
    },
    // word导出
    async gotoExport() {
      // 没有分页
      const option = {
        type: 'FoodSafetyGetCollectiveDiningDeliveryRecordExport',
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetEmployeeFoodSafetyTrainingRecordExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: 1,
          page_size: 9999999
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss" scoped>
// 查看按钮样式
.view-btn {
  color: #409eff;
  cursor: pointer;

  &:hover {
    color: #66b1ff;
  }
}

// 无图片提示样式
.no-image {
  color: #999;
  font-size: 12px;
}

// 图片预览容器样式
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;

  .preview-image {
    max-width: 100%;
    max-height: 500px;

    .image-slot {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 200px;
      background: #f5f7fa;
      color: #909399;
      font-size: 14px;

      i {
        font-size: 30px;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
