<template>
  <!-- dialog start -->
  <dialog-message :show.sync="visible" :loading="dialogLoading" :title="dialogTitle" width="460px" @close="closeDialog">
    <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogrules" :label-width="formLabelWidth" size="medium">
      <el-form-item label="运动类型" prop="sportType">
        <el-select v-model="dialogForm.sportType" clearable filterable reserve-keyword  class="ps-select w-auto" popper-class="ps-popper-select" placeholder="请选择">
          <el-option v-for="option in sportTypeList" :key="option.value" :label="option.label" :value="option.value" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="运动名称" class="m-b-0" prop="name">
        <el-input v-model="dialogForm.name" class="w-auto" :maxlength="10"></el-input>
      </el-form-item>
      <el-form-item label="强度/MET" class="m-b-0" prop="met">
        <el-input v-model="dialogForm.met" class="w-auto"></el-input>
      </el-form-item>
      <el-form-item label="能量消耗" class="m-b-0" prop="energyKcal">
        <div class="">
          <el-input v-model="dialogForm.energyKcal" class="w-140"></el-input>
          <span class="m-l-20">kcal/（kg·min）</span>
        </div>
      </el-form-item>
    </el-form>
    <div slot="tool" class="text-right m-t-40">
      <el-button :disabled="dialogLoading" class="ps-cancel-btn" @click="cancleDialog">取消</el-button>
      <el-button :disabled="dialogLoading" class="ps-btn" type="primary" @click="confirmDialog">确定</el-button>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { camelToUnderline } from '@/utils'
import { validateNotZeroOnePositive, validateNotZeroThreePositive } from '@/utils/form-validata'

export default {
  name: 'MotionDialog',
  // 重新定义下v-model的prop和触发的event
  // 为什么要重新定义的为了区别其它嵌套组件使用的v-model
  model: {
    prop: 'showDialog',
    event: 'changeShow'
  },
  props: {
    // 绑定的数据
    // visible: {
    //   required: true
    // },
    showDialog: {
      required: true
    },
    dialogTitle: {
      type: String,
      default: '添加'
    },
    // 类型
    type: {
      type: String,
      default: 'date'
    },
    // 弹窗数据源
    infoData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // label宽度
    formLabelWidth: {
      type: [String, Number],
      default: '90px'
    },
    // form size
    formSize: {
      type: String,
      default: 'medium'
    },
    // 关闭的回调
    closehandle: Function,
    // 确定回调
    confirmhandle: Function
  },
  data() {
    return {
      dialogForm: {
        sportType: '',
        name: '',
        met: '',
        energyKcal: ''
      }, // validator: validateNumber,
      dialogrules: {
        name: [{ required: true, message: '请输入运动名称', trigger: 'change' }],
        sportType: [{ required: true, message: '请选择运动类型', trigger: 'change' }],
        met: [
          { required: true, message: '请输入', trigger: 'change' },
          { validator: validateNotZeroOnePositive, trigger: 'change' }
        ],
        energyKcal: [
          { required: true, message: '请输入', trigger: 'change' },
          { validator: validateNotZeroThreePositive, trigger: 'change' }
        ]
      },
      // 运动类型
      sportTypeList: [
        { label: '运动', value: 'exercise' },
        { label: '休闲活动', value: 'leisure' },
        { label: '体力劳动', value: 'manual' },
        { label: '家务活动', value: 'labor' },
        { label: '演奏乐器', value: 'play' }
      ],
      dialogLoading: false
    }
  },
  computed: {
    visible: {
      get() {
        if (this.showDialog) {
          this.resetForm()
          if (this.type === 'modify') {
            this.init()
          }
        }
        // console.log(1111, this.showDialog)
        return this.showDialog
      },
      set(val) {
        this.$emit('changeShow', val)
      }
    }
  },
  watch: {
    // visible(val) {
    //   console.log(33, val)
    //   if (val) {
    //     console.log(2222, this.showDialog)
    //   }
    // }
  },
  created() {

  },
  mounted() {
  },
  methods: {
    init() {
      this.dialogForm = {
        sportType: this.infoData.sport_type,
        name: this.infoData.name,
        met: this.infoData.met,
        energyKcal: this.infoData.energy_kcal
      }
    },
    closeDialog() {
      this.resetForm()
      this.closehandle && this.closehandle()
    },
    cancleDialog() {
      this.closehandle && this.closehandle()
      this.visible = false
    },
    confirmDialog() {
      this.$refs.dialogFormRef.validate((valid) => {
        if (valid) {
          if (this.dialogLoading) return this.$message.error('请不要重复点击！')
          let data = {}
          for (let key in this.dialogForm) {
            data[camelToUnderline(key)] = this.dialogForm[key]
          }
          this.dialogLoading = true
          if (this.type === 'add') {
            this.addSportHandle(data)
          } else {
            data.id = this.infoData.id
            this.modifySportHandle(data)
          }
        }
      })
    },
    // 添加
    async addSportHandle(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminSportsAddPost(params))
      this.dialogLoading = false
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.visible = false
        this.confirmhandle && this.confirmhandle()
        this.$emit('confirmdialog')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑
    async modifySportHandle(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminSportsModifyPost(params))
      this.dialogLoading = false
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.visible = false
        this.confirmhandle && this.confirmhandle()
        this.$emit('confirmdialog')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重置表单
    resetForm() {
      this.dialogForm = {
        sportType: '',
        name: '',
        met: '',
        energyKcal: ''
      }
      const dialogFormRef = this.$refs.dialogFormRef
      // 重置表单数据
      if (dialogFormRef) {
        // 移除校验
        dialogFormRef.clearValidate()
      }
    }
  }
};
</script>

<style scoped lang="scss">
  .w-auto{
    width: 80%;
  }
  .w-140{
    width: 140px;
  }
  .min-w-100{
    min-width: 100px;
  }
  .flex{
    display: flex;
  }
  .text-right{
    text-align: right;
  }
</style>
