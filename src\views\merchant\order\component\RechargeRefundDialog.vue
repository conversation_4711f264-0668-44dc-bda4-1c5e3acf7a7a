<template>
  <div>
    <el-dialog :title="dialogTitle" :visible.sync="isShowDialog" width="1000px" custom-class="ps-dialog"
      :close-on-click-modal="false" :close-on-press-escape="false" show-close @close="handlerCancel">
      <div>
        <el-table :data="orderList" stripe header-row-class-name="ps-table-header-row" max-height="500px">
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
          </table-column>
        </el-table>
        <!--退款选择-->
        <el-radio-group v-model="radioType" class="m-t-20">
          <el-radio text-color="#FF9B45" label="all" v-if="companyPrice(dialogEditData.pay_fee, dialogEditData.net_fee)">全额退款</el-radio>
          <el-radio text-color="#FF9B45" label="part" class="ps-radio"
            v-if="dialogEditData.allow_refund_fee > 1">部分退款</el-radio>
        </el-radio-group>
        <!--全额退款-->
        <div v-if="radioType == 'all'" class="m-t-5">
          <div class="m-t-5">可退款余额：¥{{ dialogEditData.price }}</div>
          <div class="m-t-5">退款余额：¥{{ dialogEditData.price }}</div>
        </div>
        <!--部分退款-->
        <div v-if="radioType == 'part'">
          <el-form :model="dialogEditData" @submit.native.prevent status-icon ref="dialogFormRef" :rules="dialogFormRules"
            label-width="100px" class="attendance-form" inline>
            <div class="m-t-10">可退款额度：&lt; ￥{{ dialogEditData.refundMoney }}</div>
            <el-form-item label="储值钱包：" prop="refundWalletMoney" class="m-t-20">
              <el-input class="w-180 ps-input" :placeholder="'可退金额<' + dialogEditData.refundMoney"
                :disabled="!Number(dialogEditData.walletBalance)" v-model="dialogEditData.refundWalletMoney"></el-input>
              <div class="ps-inline m-l-20 ps-text-gray">钱包余额：￥{{ dialogEditData.walletBalance }}</div>
            </el-form-item>
          </el-form>
        </div>
        <!--备注-->
        <div class="ps-flex m-t-5">
          <div>备注：</div>
          <el-input type="textarea" :rows="3" v-model="dialogEditData.remark" placeholder="请输入备注" class="ps-input w-250"
            show-word-limit maxlength="100"></el-input>
        </div>
      </div>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer">
        <div class="dialog-footer" v-loading="isComfirmLoading">
          <el-button class="ps-cancel-btn w-100" @click="handlerCancel">取 消</el-button>
          <el-button class="ps-warn-btn w-100" type="primary" @click="handlerRefuse" v-permission="['background_order.order_charge.approval_charge_refund']">拒 绝</el-button>
          <el-button class="ps-btn w-100" type="primary" @click="handlerSumit" v-permission="['background_order.order_charge.approval_charge_refund']">同 意</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<script>
// import { to } from "@/utils"
import { divide, deepClone } from "@/utils"
export default {
  name: 'RechargeRefundDialog',
  props: {
    dialogTitle: { // 标题
      type: String,
      default: '提示'
    },
    show: {
      type: Boolean,
      default: false
    },
    dialogType: { // 弹窗类型
      type: String,
      default: ''
    }
  },
  data() {
    let validataRefundMoney = (rule, value, callback) => {
      let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      console.log(rule)
      let price = this.dialogEditData.refundMoney
      let balance = this.dialogEditData.walletBalance
      console.log("price", price, value);
      if (value) {
        if (Number(value) <= 0) {
          callback(new Error('金额不能小于等于0'))
        } else if (value >= Number(price)) {
          callback(new Error('金额不能大于等于可退金额'))
        } else if (value > Number(balance)) {
          callback(new Error('金额不能大于钱包余额'))
        } else if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入退款金额'))
      }
    }
    let validataPrice = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        // let number = /((^[1-9][0-9])|(([0]\.\d{1,2}|^[1-9][0-9]\.\d{1,2})))$/
        let number = /^[0-9]\d*\.?\d{0,2}$/
        if (!number.test(value) || value === '0.0' || value === '0.00') {
          callback(new Error('请输入大于零的数值，最多为两位小数'))
        } else {
          callback()
        }
      }
    };
    return {
      dialogEditData: {
        tradeNo: '',
        id: '',
        price: '',
        remark: '',
        refundMoney: '', // 部分退款金额
        refundWalletMoney: '', // 储值退款金额
        walletBalance: '' // 钱包余额
      },
      isComfirmLoading: false,
      dialogRules: {
        price: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { validator: validataPrice, trigger: 'blur' }
        ],
        remark: [
          { required: false, message: '请输入备注', trigger: 'blur' }
        ]
      },
      orderList: [], // 订单列表
      currentTableSetting: [
        {
          label: '充值订单号', key: 'charge_trade_no', width: "160"
        },
        {
          label: '充值时间', key: 'charge_create_time', width: "160"
        },
        {
          label: '充值金额', key: 'pay_fee', type: "money"
        },
        {
          label: '储值钱包到账', key: 'wallet_fee', type: "money"
        },
        {
          label: '赠送钱包余额', key: 'complimentary_fee', type: "money"
        },
        {
          label: '充值渠道', key: 'charge_payway_alias'
        }
      ],
      radioList: [
        {
          name: '全额退款',
          value: 'all'
        },
        {
          name: '部分退款',
          value: 'part'
        }
      ],
      radioType: 'all',
      dialogFormRules: {
        refundWalletMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }]
      }
    }
  },
  computed: {
    isShowDialog: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  watch: {
    show(newValue) {
      if (newValue) {
        this.$set(this.dialogRules.remark[0], 'required', this.dialogType === "refund")
      } else {
        this.isComfirmLoading = false
      }
      console.log("watch", newValue, this.dialogRules.remark);
    }
  },
  created() {
  },
  methods: {
    // 取消
    handlerCancel() {
      this.$emit("dialogClose", true)
    },
    // 确认
    handlerSumit() {
      if (this.radioType === 'part') {
        this.$set(this.dialogEditData, 'price', this.dialogEditData.refundWalletMoney)
      }
      this.$emit("dialogConfirm", this.dialogEditData)
    },
    // 设置订单列表
    setOrderList(list) {
      this.orderList = []
      if (Array.isArray(list)) {
        list.forEach(item => {
          this.orderList.push({ name: item })
        })
      }
    },
    // 拒绝
    handlerRefuse() {
      if (!this.dialogEditData.remark || this.dialogEditData.remark.length === 0) {
        return this.$message.error("拒绝备注必填")
      }
      this.$emit("dialogRefuse", this.dialogEditData)
    },
    // 设置按钮loading
    setBtnLoading(isFlag) {
      this.isComfirmLoading = isFlag
    },
    // 设置dialog数据
    setDialogData(data) {
      console.log("setDialogData", data);
      data.price = this.formatPrice(data.allow_refund_fee)
      data.remark = ''
      data.refundWalletMoney = ''
      data.refundMoney = this.formatPrice(data.net_fee - data.rate_fee)
      data.walletBalance = this.formatPrice(data.real_time_fee)
      data.tradeNo = data.charge_trade_no
      this.dialogEditData = deepClone(data)
      var list = [{
        charge_trade_no: data.charge_trade_no,
        charge_create_time: data.charge_create_time,
        pay_fee: data.pay_fee,
        wallet_fee: data.wallet_fee,
        complimentary_fee: data.complimentary_fee,
        charge_payway_alias: data.charge_payway_alias
      }]
      this.orderList = list
      this.radioType = this.companyPrice(data.pay_fee, data.net_fee) ? "all" : "part"
      console.log("dialogEditData", this.dialogEditData);
    },
    // 格式化价格
    formatPrice(price) {
      if (!price) {
        return 0
      }
      return divide(price)
    },
    // 对比价格
    companyPrice(price, price2) {
      console.log("price", price, price2);
      return price === price2
    }
  }
}
</script>

<style lang="scss" scope>
.w-330 {
  width: 330px;
}

.w-350 {
  width: 350px;
}
</style>
