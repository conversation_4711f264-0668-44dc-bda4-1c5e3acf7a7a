<template>
  <!-- 表13-食品留样记录表-详情 -->
  <div class="content">
    <div class="item-info">
      <div class="flex">
        <span class="color-999">留样日期：</span>
        <span>{{ formatTime(detailInfo.reserved_time)[0] }}</span>
      </div>
      <div class="flex">
        <span class="color-999">留样时间：</span>
        <span>{{ formatTime(detailInfo.reserved_time)[1] }}</span>
      </div>
    </div>
    <div class="item-info">
      <div class="flex">
        <span class="color-999">处理日期：</span>
        <span>{{ formatTime(detailInfo.operate_time)[0] }}</span>
      </div>
      <div class="flex">
        <span class="color-999">处理时间：</span>
        <span>{{ formatTime(detailInfo.operate_time)[1] }}</span>
      </div>
    </div>
    <div class="flex m-b-10">
      <span class="color-999">留样餐段：</span>
      <span>{{ detailInfo.meal_type_verbose }}</span>
    </div>
    <div class="flex flex-align-c">
      <span class="color-999">留样菜品：</span>
      ({{ detailInfo.data_list.length || 0 }})
    </div>
    <div v-for="(value, index) in detailInfo.data_list" :key="index" class="food-item">
      <div class="flex flex-align-c m-b-10">
        <span>样品名称：</span>
        <span>{{ value.food_name }}</span>
      </div>
      <div class="flex flex-align-c">
        <span>留样重量：</span>
        <span>{{ value.food_weight }}g</span>
      </div>
    </div>
    <div class="sign-content" v-if="detailInfo">
      <div class="sign-item m-r-40">
        <div class="m-b-10">
          <span class="color-999">留样人:</span>
          <span>{{ detailInfo.reserved_user }}</span>
        </div>
        <el-image
          style="width: 100px; height: 100px"
          :src="detailInfo.sign_info[0]"
          :preview-src-list="[detailInfo.sign_info[0]]"
        ></el-image>
      </div>
      <div class="sign-item">
        <div class="m-b-10">
          <span class="color-999">清倒人:</span>
          <span>{{ detailInfo.liquidator }}</span>
        </div>
        <el-image
          style="width: 100px; height: 100px"
          :src="detailInfo.sign_info[1]"
          :preview-src-list="[detailInfo.sign_info[1]]"
        ></el-image>
      </div>
    </div>
  </div>
</template>
<script>
import { to } from '@/utils'
export default {
  name: 'Table11ShipinLiuyangDetail',
  data() {
    return {
      isLoading: false,
      detailInfo: null
    }
  },
  props: {
    ledgerId: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    // 监听 ledgerId 变化
    ledgerId: {
      handler(newVal) {
        if (newVal) {
          this.getLedgerReviewDetail(newVal)
        }
      },
      immediate: true // 立即执行
    }
  },
  methods: {
    // 获取详情通用接口
    async getLedgerReviewDetail(id) {
      this.isLoading = true
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyLedgerReviewDetail({ id }))
      if (err) {
        this.$message.error(err.message)
        this.isLoading = false
        return
      }
      if (res.code === 0) {
        this.detailInfo = res.data || null
      } else {
        this.$message.error(res.msg)
      }
      this.isLoading = false
    },
    // 时间处理
    formatTime(timeStr) {
      // 分割日期和时间部分
      const [datePart, timePart] = timeStr.split(' ')
      // 分割日期的年、月、日
      const [year, month, day] = datePart.split('-')
      // 分割时间的时、分
      const [hour, minute] = timePart.split(':')

      // 格式化日期和时间
      const dateStr = `${year}年${month}月${day}日`
      const timeStrFormatted = `${hour}时${minute}分`

      return [dateStr, timeStrFormatted]
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 20px 0 0 3%;
}
.color-999 {
  color: #999;
}
.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-right: 30%;
  margin-bottom: 10px;
}
.food-item {
  padding: 10px;
  margin: 10px 0 10px 10%;
  background-color: #f2f2f2;
  border-radius: 10px;
  width: 80%;
}
.sign-content {
  margin-top: 20px;
  .sign-item {
    display: flex;
    flex-direction: column;
    .color-999 {
      color: #999;
    }
  }
}
</style>
