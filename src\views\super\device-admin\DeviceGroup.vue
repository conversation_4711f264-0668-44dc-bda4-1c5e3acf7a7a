<template>
  <div class="ThirdPartyEquipmentAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="openDialog('add')">添加</button-icon>
          <button-icon color="plain" @click="openImport">批量导入</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          size="small"
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="openDialog('modify', row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-red" @click="deleteHandler(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <device-group-drawer
      :isshow.sync="dialogVisible"
      :type="dialogType"
      :title="dialogTitle"
      :drawerData="drawerData"
      :companyList="searchFormSetting.company_id.dataList"
      @clickConfirm="confirmDrawer"
    />
    <import-dialog-drawer
      :templateUrl="templateUrl"
      :tableSetting="importTableSetting"
      :show.sync="importShowDialog"
      :title="importDialogTitle"
      :openExcelType="openExcelType"
    ></import-dialog-drawer>
  </div>
</template>

<script>
import DeviceGroupDrawer from './components/DeviceGroupDrawer.vue'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
export default {
  name: 'DeviceGroup',
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '所属组织', key: 'company_alias' },
        { label: '设备组名称', key: 'name' },
        { label: '当前设备数（台）', key: 'sub_count' }, // sub_count 目前是自定义的，需要后端返回
        { label: '最大设备数（台）', key: 'max_count' }, // max_count 目前是自定义的，需要后端返回
        { label: '组id', key: 'group_id' },
        { label: '状态', key: 'status_alias' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      searchFormSetting: {
        company_id: {
          type: 'select',
          filterable: true,
          multiple: false,
          collapseTags: false,
          clearable: true,
          label: '所属项目',
          value: '',
          placeholder: '请选择项目点',
          listNameKey: 'name',
          listValueKey: 'company',
          dataList: []
        },
        name: {
          type: 'input',
          label: '设备组名称',
          value: '',
          maxlength: '20',
          labelWidth: '100px',
          placeholder: '请输入'
        },
        // is_enable: {
        //   type: 'select',
        //   value: '',
        //   label: '状态',
        //   clearable: true,
        //   dataList: [
        //     { label: '全部', value: '' },
        //     { label: '启用', value: true },
        //     { label: '未启用', value: false }
        //   ]
        // },
        group_id: {
          type: 'input',
          label: '设备组ID',
          value: '',
          placeholder: '请输入'
        }
      },
      dialogTitle: '',
      dialogVisible: false,
      dialogType: '',
      drawerData: {},
      templateUrl: '',
      importTableSetting: [],
      importShowDialog: false,
      importDialogTitle: '',
      openExcelType: ''
    }
  },
  created() {
    this.getOrganizationList()
    this.initLoad()
  },
  components: {
    DeviceGroupDrawer
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDeviceGroupList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getDeviceGroupList()
    }, 300),
    confirmDrawer (type) {
      if (type === 'add') {
        this.currentPage = 1
      }
      this.dialogVisible = false
      this.getDeviceGroupList()
    },
    async getOrganizationList() {
      const res = await this.$apis.apiBackgroundAdminOrganizationListPost({
        parent__is_null: '1',
        status: 'enable',
        ...this.params,
        page: 1,
        page_size: 999999
      })
      if (res.code === 0) {
        if (this.companyKey === 'all') {
          this.searchFormSetting.company_id.dataList = [{ name: "全局用户", company: 1 }, ...res.data.results]
        } else {
          this.searchFormSetting.company_id.dataList = res.data.results
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    async getDeviceGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceListDeviceGroupPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.tableData = []
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDeviceGroupList()
    },
    deleteHandler(row) {
      this.$confirm(`确定删除当前选中的数据吗？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminDeviceDeleteDeviceGroupPost({
                id: row.id
              })
            )
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              if (this.currentPage > 1 && this.tableData.length === 1) {
                this.currentPage--
              }
              this.getDeviceGroupList()
              done()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    openDialog(type, data) {
      this.dialogType = type
      switch (type) {
        case 'add':
          this.dialogTitle = '新增设备组'
          break
        case 'modify':
          this.dialogTitle = '修改设备组'
          this.drawerData = data
          break
      }
      this.dialogVisible = true
    },
    openImport() {
      this.importShowDialog = true
      this.openExcelType = 'ImportDeviceGroup'
      this.importDialogTitle = '批量设备组'
      this.templateUrl = location.origin + '/api/temporary/template_excel/device_group_import.xlsx'
      this.importTableSetting = [
        { key: 'company_id', label: '*所属组织' },
        { key: 'name', label: '*设备组名' },
        { key: 'group_id', label: '*设备组ID' },
        { key: 'remark', label: '*备注' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped></style>
