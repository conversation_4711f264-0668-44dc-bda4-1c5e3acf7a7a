<template>
  <!-- dialog start -->
  <dialog-message :show.sync="showDialog" :loading="isLoading" :title="dialogTitle" width="435px" footer-center @close="closeDialog">
    <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogrules" label-position="left" label-width="80px" size="medium" class="add-warehouse">
      <el-form-item label="仓库名称" prop="name" label-width="80px">
        <el-input v-model="dialogForm.name" :maxlength="15" :disabled="isDisabled" class="ps-input"></el-input>
      </el-form-item>
      <!-- <el-form-item label="所属组织" label-width="80px" prop="organization_id">
        <organization-select
          v-model="dialogForm.organization_id"
          placeholder="请选择所属组织"
          :disabled="isDisabled"
          :isLazy="false"
          :multiple="false"
          :check-strictly="true"
          :append-to-body="true"
        ></organization-select>
      </el-form-item> -->
      <!-- <el-form-item label="是否允许其他组织可见" label-width="160px" prop="canSee">
        <el-switch v-model="dialogForm.is_show" active-color="#ff9b45"></el-switch>
      </el-form-item> -->
      <!-- <el-form-item label="" label-width="6px" prop="show_type">
        <el-radio-group v-model="dialogForm.show_type">
          <el-radio class="ps-radio" label="ONLY">仅本组织可见</el-radio>
          <el-radio class="ps-radio" label="ALL">全部可见</el-radio>
          <el-radio class="ps-radio" label="SOME">指定组织可见</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item v-if="dialogForm.show_type === 'SOME'" label="可见组织" label-width="80px" prop="org_ids">
        <!-- <organization-select
          v-model="dialogForm.org_ids"
          placeholder="请选择可见组织"
          :isLazy="false"
          :multiple="true"
          :check-strictly="true"
          :append-to-body="true"
      ></organization-select> -->
      <organization-select
        class="search-item-w ps-input"
        placeholder="请选择组织"
        v-model="dialogForm.org_ids"
        :isLazy="false"
        :multiple="true"
        :check-strictly="true"
        role="merchant"
        :append-to-body="true"
        :filterable="false"
        >
        <!-- :disabled-list="[deviceInfo.organization_id]"
        :org-id="deviceInfo.organization"
        disabled-org-level
        :only-child="true" -->
      </organization-select>
      </el-form-item>
      <!-- <el-form-item label="是否为该组织默认仓库" label-width="160px">
        <el-switch v-model="dialogForm.is_default" active-color="#ff9b45"></el-switch>
      </el-form-item> -->
      <el-form-item label="状态" label-width="60px" prop="status">
        <el-radio-group v-model="dialogForm.status" class="ps-radio">
          <el-radio label="enable">使用</el-radio>
          <el-radio label="disable">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="tool" class="footer-center m-t-20">
      <el-button :disabled="isLoading" class="ps-cancel-btn w-150" @click="cancleDialog">取消</el-button>
        <el-button :disabled="isLoading" class="ps-btn w-150" type="primary" @click="confirmDialog">确定</el-button>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
// import { to } from '@/utils'
// import OrganizationSelect from '@/components/OrganizationSelect'
import OrganizationSelect from '@/components/OrganizationSelect'
import { mapGetters } from 'vuex'

export default {
  name: '',
  components: {
    // OrganizationSelect,
    OrganizationSelect
  },
  props: {
    show: {
      type: Boolean
    },
    type: {
      type: String,
      default: 'add'
    },
    dialogLoading: Boolean,
    infoData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    closehandle: Function,
    confirmhandle: Function
  },
  data() {
    return {
      isLoading: false,
      dialogTitle: '新增仓库',
      showDialog: false,
      dialogContent: '',
      dialogForm: {
        name: '',
        organization_id: '',
        is_show: true,
        show_type: 'ONLY',
        org_ids: [],
        is_default: false,
        status: 'enable'
      },
      dialogrules: {
        name: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }],
        organization_id: [{ required: true, message: '请选择所属组织', trigger: 'blur' }],
        org_ids: [{ required: true, message: '请选择可见组织', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
        show_type: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    }
  },
  computed: {
    isDisabled (val) {
      return this.type === 'modify'
    },
    ...mapGetters(['organization'])
  },
  watch: {
    show(value) {
      this.dialogTitle = this.type === 'add' ? '新增仓库' : '编辑仓库'
      this.showDialog = value
      if (this.type === 'modify') {
        this.initData(this.infoData)
      }
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    initData(data) {
      this.dialogForm = {
        name: data.name,
        organization_id: data.organization_id,
        // is_show: data.show_type,
        // is_default: data.is_default,
        status: data.status,
        show_type: data.type,
        org_ids: data.type === 'SOME' ? [...data.organizations] : []
      }
    },
    clearHandle() {
      const domRef = this.$refs.dialogFormRef
      domRef && domRef.clearValidate()
      this.dialogForm = {
        name: '',
        organization_id: '',
        is_show: true,
        show_type: 'ONLY',
        org_ids: [],
        is_default: false,
        status: 'enable'
      }
    },
    closeDialog() {
      this.clearHandle()
      this.closehandle && this.closehandle()
    },
    cancleDialog() {
      // this.clearHandle()
      this.closehandle && this.closehandle()
    },
    confirmDialog() {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          if (this.type === 'add') {
            this.sendAddFormData()
          } else {
            this.sendModifyFormData()
          }
        }
      })
      // this.confirmhandle && this.confirmhandle()
    },
    // 格式化参数
    formatFormData() {
      let params = {
        name: this.dialogForm.name,
        organization_id: this.dialogForm.organization_id,
        // is_show: this.dialogForm.is_show,
        // is_default: this.dialogForm.is_default,
        status: this.dialogForm.status
      }
      params.show_type = this.dialogForm.show_type
      if (this.dialogForm.show_type === 'SOME') {
        params.org_ids = this.dialogForm.org_ids
      }
      if (this.type === 'modify') {
        params.id = this.infoData.id
      }
      console.log('params', params)
      return params
    },
    // 添加
    async sendAddFormData() {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpWarehouseAddPost(this.formatFormData()))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.clearHandle()
        this.confirmhandle && this.confirmhandle()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑
    async sendModifyFormData() {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpWarehouseModifyPost(this.formatFormData()))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.clearHandle()
        this.confirmhandle && this.confirmhandle()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
};
</script>

<style scoped lang="scss">
.add-warehouse{
  // .el-form-item{
  //   margin-bottom: 18px;
  // }
}
</style>
