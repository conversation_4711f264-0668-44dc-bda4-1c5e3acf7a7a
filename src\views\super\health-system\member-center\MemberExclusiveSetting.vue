<template>
  <div class="memberExclusiveSetting container-wrapper">
    <!--刷新页面-->
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper p-l-20 p-t-20">
      <!--菜单切换-->
      <div class="tab">
        <div :class="['tab-item', tabType === 'record' ? 'active' : 'no-active']" @click="tabClick('record')">
          短信配置
        </div>
      </div>
      <el-form :model="memberForm" :rules="memberFormRules" ref="memberFormRef" label-width="120px">
        <div class="l-title">
          通知类
        </div>
        <el-form-item label="线上消费">
          <el-switch v-model="memberForm.isOnline" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <el-form-item label="线下消费">
          <el-switch v-model="memberForm.isOffline" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <el-form-item label="退款通知">
          <el-switch v-model="memberForm.isRefund" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <el-form-item label="充值通知">
          <el-switch v-model="memberForm.isRecharge" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <el-form-item label="提现通知">
          <el-switch v-model="memberForm.isWithdrawal" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <el-form-item label="通行通知">
          <div class="ps-flex">
            <el-switch v-model="memberForm.isTransit" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            <div class="m-l-20">闸机为门禁模式</div>
          </div>
        </el-form-item>
        <div class="l-title">
          提醒类
        </div>
        <!-- <el-form-item label="预约提醒">
          <el-switch v-model="memberForm.isReservation" active-color="#ff9b45" inactive-color="#ffcda2"
            @change="hanlderSwitchChange($event, 'isReservation')"></el-switch>
          <div class="ps-flex" v-if="memberForm.isReservation">
            <div class="m-l-20 m-r-20">点餐截止前</div>
            <div><el-form-item prop="deadlineTimes"><el-input class="w-200"
                  v-model="memberForm.deadlineTimes"></el-input></el-form-item></div>
            <div class="m-l-20">小时</div>
          </div>
        </el-form-item>
        <el-form-item label="报餐提醒">
          <el-switch v-model="memberForm.isMealReporting" active-color="#ff9b45" inactive-color="#ffcda2" @change="hanlderSwitchChange($event, 'isMealReporting')"></el-switch>
          <div class="ps-flex" v-if="memberForm.isMealReporting">
            <div class="m-l-20 m-r-20">报餐截止前</div>
            <div><el-form-item prop="mealReportingHour"><el-input class="w-200"
                  v-model="memberForm.mealReportingHour"></el-input></el-form-item>
            </div>
            <div class="m-l-20">小时</div>
          </div>
        </el-form-item> -->
        <el-form-item label="余额提醒">
          <el-switch v-model="memberForm.isBalance" active-color="#ff9b45" inactive-color="#ffcda2"
            @change="hanlderSwitchChange($event, 'isBalance')" class="switch-tag"></el-switch>
          <div class="ps-flex" v-if="memberForm.isBalance">
            <div class="m-l-20 m-r-20">余额低于</div>
            <div><el-form-item prop="balanceNum"><el-input class="w-200"
                  v-model="memberForm.balanceNum"></el-input></el-form-item>
            </div>
            <div class="m-l-20">元</div>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button size="small" class="ps-origin-plain-btn w-100" @click="handlerCancel">取消</el-button>
          <el-button size="small" type="primary" class="ps-origin-btn w-100 m-l-40" @click="saveSetting"
            v-loading="isLoading">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { deepClone, to, times, divide } from '@/utils/index'

export default {
  name: 'MemberExclusiveSetting',
  components: {},
  props: {},
  data() {
    let validataHours = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        let number = /(^[1-9][0-9]*$)|((^[1-9]\d*)[\\.]([0-9]{1})$)|((^[0])[\\.]([1-9]{1})$)/;
        if (!number.test(value)) {
          callback(new Error('请输入正整数或者一位小数'))
        } else {
          callback()
        }
      }
    };
    let validataPrice = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        let number = /^[1-9][0-9]*$/;
        if (!number.test(value)) {
          callback(new Error('请输入正整数'))
        } else {
          callback()
        }
      }
    };
    return {
      tabType: 'record',
      isLoading: false, // 刷新数据
      type: '',
      memberForm: {
        isOnline: false,
        isOffline: false,
        isRefund: false,
        isRecharge: false,
        isWithdrawal: false,
        isTransit: false,
        isReservation: false,
        isMealReporting: false,
        mealReportingHour: 0,
        isBalance: false,
        deadlineTimes: 0,
        balanceNum: 0
      },
      memberFormRules: {
        deadlineTimes: [{ validator: validataHours, trigger: 'blur' }],
        mealReportingHour: [{ validator: validataHours, trigger: 'blur' }],
        balanceNum: [{ validator: validataPrice, trigger: 'blur' }]
      }
    }
  },
  created() {
    this.initData()
  },
  mounted() { },
  methods: {
    // 初始化数据
    initData() {
      this.getExclusiveSetting()
    },
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initLoad()
    },
    saveSetting() {
      this.$refs.memberFormRef.validate(valid => {
        if (valid) {
          console.log("valid");
          var params = {
            is_online: this.memberForm.isOnline,
            is_offline: this.memberForm.isOffline,
            is_refund: this.memberForm.isRefund,
            is_recharge: this.memberForm.isRecharge,
            is_withdraw: this.memberForm.isWithdrawal,
            is_pass: this.memberForm.isTransit,
            is_reservation: this.memberForm.isReservation,
            reservation_hours: this.memberForm.deadlineTimes ? parseFloat(this.memberForm.deadlineTimes) : 0,
            is_report_meal: this.memberForm.isMealReporting,
            report_meal_hours: this.memberForm.mealReportingHour ? parseFloat(this.memberForm.mealReportingHour) : 0,
            is_balance: this.memberForm.isBalance,
            balance_fee: this.memberForm.balanceNum ? times(parseFloat(this.memberForm.balanceNum)) : 0
          }
          var api = this.$apis.apiBackgroundMemberRightsSettingAddSmsSettingPost(params)
          this.confirmOperation(api)
        }
      })
    },
    // 保存配置
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(api)
      this.isLoading = false
      if (err) {
        return this.$message.error(err.message || '保存失败')
      }
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 会员等级
    async getMemberLevel() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundMemberMemberGradeListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.levelList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    tabClick() {
      console.log("tabClick");
    },
    // 切换开关
    hanlderSwitchChange(val, type) {
      console.log("hanlderSwitchChange", val, type);
      if (type === 'isReservation') {
        this.$set(this.memberForm, "deadlineTimes", val ? "1" : '')
      }
      if (type === 'isBalance') {
        this.$set(this.memberForm, "balanceNum", val ? "20" : '')
      }
      if (type === 'isMealReporting') {
        this.$set(this.memberForm, "mealReportingHour", val ? "1" : '')
      }
    },
    // 取消返回
    handlerCancel() {
      this.$router.back()
    },
    // 获取设置
    async getExclusiveSetting() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMemberRightsSettingGetSmsSettingPost())
      this.isLoading = false
      if (err) {
        return this.$message.error(err.message || '获取设置失败')
      }
      if (res && res.code === 0) {
        console.log("getExclusiveSetting", res);
        var settingData = res.data || {}
        if (settingData && typeof settingData === 'object' && Object.keys(settingData).length > 0) {
          var memberForm = deepClone(this.memberForm)
          memberForm.isOnline = settingData.is_online
          memberForm.isOffline = settingData.is_offline
          memberForm.isRefund = settingData.is_refund
          memberForm.isRecharge = settingData.is_recharge
          memberForm.isWithdrawal = settingData.is_withdraw
          memberForm.isTransit = settingData.is_pass
          memberForm.isReservation = settingData.is_reservation
          memberForm.isMealReporting = settingData.is_report_meal
          memberForm.mealReportingHour = settingData.report_meal_hours
          memberForm.isBalance = settingData.is_balance
          memberForm.balanceNum = settingData.balance_fee ? settingData.balance_fee / 100 : '0'
          memberForm.deadlineTimes = settingData.reservation_hours
          this.$set(this, 'memberForm', memberForm)
        }
      } else {
        this.$message.error(res.msg || '获取设置失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.memberExclusiveSetting {

  .tab {
    margin-bottom: 20px;

    .tab-item {
      display: inline-block;
      width: 90px;
      height: 28px;
      line-height: 28px;
      margin-right: 10px;
      text-align: center;
      border-radius: 5px;
      font-size: 16px;
      vertical-align: middle;
      cursor: pointer;

      &.active {
        color: #ffffff;
        background-color: #fd953c;
      }

      &.no-active {
        color: #fd953c;
        background-color: #ffffff;
      }
    }
  }

  .m-l-100 {
    margin-left: 100px;
  }

  .label-list {
    display: flex;
    flex-wrap: wrap;
    color: #fff;

    .label-list-item {
      margin-bottom: 10px;
      line-height: 30px;
      background-color: #ff9b45;
      padding: 0 10px;
      margin-right: 15px;
      border-radius: 5px;

      .del-icon {
        cursor: pointer;
      }
    }
  }

  .ps-flex {
    display: inline-flex;
    align-items: center;

    .switch-tag {
      align-self: center;
    }
  }
}
</style>
