<template>
  <div class="recharge-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" label-width="115px" @search="searchHandle" :autoSearch="false">
      <template #perv>
        <div class="searchref_top">
          <el-button :class="{ active: current === 0 }" @click="tabHandler(0)" v-permission="['background_order.order_charge.list']">充值订单</el-button>
          <el-button :class="{ active: current === 1 }" @click="tabHandler(1)" v-permission="['background_order.order_refund.charge_list']">
            充值退款订单
          </el-button>
        </div>
      </template>
    </search-form>
    <!-- 充值订单数据列表 -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" v-if="current === 0" @click="mulRefundHandle" v-permission="['background_order.order_charge.refund']">批量退款</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" v-if="current === 0" type="export" @click="handleExport" v-permission="['background_order.order_charge.list_export']">导出报表</button-icon>
          <button-icon color="plain" v-if="current === 1" type="export" @click="handleExport" v-permission="['background_order.order_refund.charge_list_export']">导出报表</button-icon>
          <button-icon color="plain" @click="openDialog">小票打印</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row"  @selection-change="handleOrderSelectionChange" :empty-text="isFirstSearch ? '暂无数据，请查询' : ''">
          <el-table-column type="selection" width="55" :selectable="selectableHandle"></el-table-column>
          <table-column :index="indexMethod" v-for="(item, i) in currentTableSetting" :key="item.key + i" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="rechargeFn(row)" :disabled="statusRefund(row)" v-permission="['background_order.order_charge.refund']">退款</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <ul class="total" v-if="current === 0">
        <li>
          合计充值笔数:
          <span>{{ total_count }}</span>
        </li>
        <li>
          合计充值金额:￥
          <span>{{ total_amount | formatMoney }}</span>
        </li>
        <li>
          合计到账金额:￥
          <span>{{ total_real_amount | formatMoney }}</span>
        </li>
        <li>
          合计赠送金额:￥
          <span>{{ total_complimentary_amount | formatMoney }}</span>
        </li>
        <li>
          手续费合计:￥
          <span>{{ total_rate_fee | formatMoney }}</span>
        </li>
      </ul>
      <ul class="total" v-else>
        <li>
          合计退款笔数:
          <span>{{ refund_total_count }}</span>
        </li>
        <li>
          合计退款金额:￥
          <span>{{ refund_total_amount | formatMoney }}</span>
        </li>
        <li>
          手续费合计:￥
          <span>{{ total_rate_fee | formatMoney }}</span>
        </li>
      </ul>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 充值订单对话框 -->
    <el-dialog :visible.sync="outerVisible" :show-close="false">
      <span style=" display: block; font-size:16px;font-weight: bold; margin-bottom: 10px;">
        退款
      </span>
      <el-table
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ textAlign: 'center' }"
        :data="refundTableData"
        v-loading="isLoading"
        border
        style="width: 100%;"
      >
        >
        <el-table-column prop="trade_no" label="充值订单号"></el-table-column>
        <el-table-column prop="create_time" label="充值时间"></el-table-column>
        <el-table-column prop="pay_fee" label="充值金额">
          <template slot-scope="scope">
            <span>￥{{ scope.row.pay_fee | formatMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="wallet_fee" label="储值钱包到账">
          <template slot-scope="scope">
            <span>￥{{ scope.row.wallet_fee | formatMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="subsidy_fee" label="赠送钱包到账">
          <template slot-scope="scope">
            <span>￥{{ scope.row.complimentary_fee | formatMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="payway" label="充值渠道">
          <template slot-scope="scope">{{ scope.row.payway_alias }}</template>
        </el-table-column>
      </el-table>
      <div class="radio" style="margin: 10px 0;">
        <el-radio text-color="#FF9B45" v-model="radio" label="1" @change="changeRefundType" v-if="companyPrice(refundDialogData.pay_fee, refundDialogData.net_fee)" >全额退款</el-radio>
        <el-radio text-color="#FF9B45" v-model="radio" @change="changeRefundType" label="part" class="ps-radio" v-if="isShowPartRefund && refundDialogData.allow_refund_fee > 1">部分退款</el-radio>
      </div>
      <ul class="refund_money" v-if="radio == '1'">
        <li>
          <span>可退款余额:￥</span>
          <span>{{ refundDialogData.allow_refund_fee | formatMoney }}</span>
        </li>
        <li>
          <span>退款金额:￥</span>
          <span>{{ refundDialogData.allow_refund_fee | formatMoney }}</span>
        </li>
      </ul>
      <div v-if="radio === 'part'">
        <el-form
          :model="dialogForm"
          @submit.native.prevent
          status-icon
          ref="dialogFormRef"
          :rules="dialogFormRules"
          label-width="100px"
          class="attendance-form"
          inline
        >
          <div>可退款额度：&lt; ￥{{ dialogForm.refundMoney }}</div>
            <el-form-item label="储值钱包：" prop="refundWalletMoney" class="m-t-20" >
              <el-input
                class="w-180 ps-input"
                :placeholder="'可退金额<' + dialogForm.refundMoney"
                :disabled="!Number(dialogForm.refundMoney)"
                v-model="dialogForm.refundWalletMoney"
              ></el-input>
            <div class="ps-inline m-l-20 ps-text-gray">钱包余额：￥{{dialogForm.walletBalance}}</div>
          </el-form-item>
        </el-form>
      </div>
      <div class="refund-remark">
        <el-form
          :model="refundFrom"
          @submit.native.prevent
          status-icon
          inline
        >
          <el-form-item label-width="140px" label="备注：" class="m-t-20">
            <el-input v-model="refundFrom.refundRemark" class="ps-input w-250" maxlength="30"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <el-dialog
        width="30%"
        class="el-dialog__body"
        title="温馨提示"
        :visible.sync="innerVisible"
        append-to-body
      >
        <p class="twoRefund" v-if="radio === '1'" style="font-size: 20px;text-align: center; ">
          确定要对该订单进行退款吗
        </p>
        <p class="twoRefund" v-if="radio === 'part'" style="font-size: 20px;text-align: center; ">
          确定要对该订单进行部分退款吗
        </p>
        <p class="twoRefund" style="color:#E0364C;text-align: center;">温馨提示: 确定后不可撤销</p>
        <div class="button" style="display: flex; justify-content: flex-end;">
          <el-button @click="innerVisible = false">取消</el-button>
          <el-button type="primary" @click="handleClose">确定</el-button>
        </div>
      </el-dialog>
      <div slot="footer" class="btm_bottom">
        <el-button size="small" type="info" @click="refundFirstFn">取 消</el-button>
        <el-button size="small" type="warning" @click="handlerRefundConfirm">
          确 定
        </el-button>
      </div>
    </el-dialog>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
    <print-ticket
      :isshow.sync="printTicketVisible"
      type="card"
      :cardType="current === 0?'CHARGE':'CHARGE_REFUND'"
      title="小票打印"
      :select-list-id="orderIds"
      @confirm="searchHandle"
    ></print-ticket>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone, divide, times } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import PrintTicket from '@/components/PrintTicket'
import {
  RECHARGE_TABLE,
  RECHARGE_REFUND_TABLE,
  RECHARGE_TYPEARR, // 充值类型
  RECHARGE_STATEARR, // 充值状态
  REFUND_TYPE,
  REFUND_TYPE_LIST,
  RECENTSEVEN,
  getRequestParams,
  REFUND_STATEARR // 退款状态
} from './constants'
export default {
  name: 'RechargeOrder',
  components: { PrintTicket },
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    let validataRefundMoney = (rule, value, callback) => {
      let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      console.log(rule)
      let price = this.dialogForm.refundMoney
      let balance = this.dialogForm.walletBalance
      console.log("price", price, value);
      if (value) {
        if (Number(value) <= 0) {
          callback(new Error('金额不能小于等于0'))
        } else if (value >= Number(price)) {
          callback(new Error('金额不能大于等于可退金额'))
        } else if (value > Number(balance)) {
          callback(new Error('金额不能大于钱包余额'))
        } else if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入退款金额'))
      }
    }
    return {
      allow_refund_fee: 0,
      current: 0,
      // 充值数据列表table
      tableData: [],
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      page: 1, // 第几页

      // 合计
      total_count: 0, // 合计充值笔数
      total_amount: 0, // 合计充值金额
      total_real_amount: 0, // 合计到账金额
      total_complimentary_amount: 0, // 合计赠送金额
      refund_total_count: '', // 合计退款笔数
      refund_total_amount: '', // 合计退款金额
      total_rate_fee: 0,
      // 报表设置相关
      tableSetting: [],
      rechargeTableSetting: deepClone(RECHARGE_TABLE),
      rechargeRefundTableSetting: deepClone(RECHARGE_REFUND_TABLE),
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'rechargeOrder',

      // 搜索筛选相关
      searchForm: {},
      rechargeSearchForm: {
        payway: {
          type: 'select',
          label: '充值渠道',
          value: '',
          placeholder: '请选择充值渠道',
          dataList: []
        },
        pay_scene: {
          type: 'select',
          label: '充值类型',
          value: '',
          placeholder: '请选择充值类型',
          dataList: RECHARGE_TYPEARR
        },
        order_status: {
          type: 'select',
          label: '充值状态',
          value: '',
          placeholder: '请选择充值状态',
          dataList: RECHARGE_STATEARR
        },
        phone: {
          type: 'input',
          value: '',
          label: '手机号',
          placeholder: '请输入手机号'
        },
        out_trade_no: {
          type: 'input',
          value: '',
          label: '第三方订单号',
          placeholder: '请输入第三方订单号'
        },
        provider_trade_no: {
          type: 'input',
          value: '',
          label: '交易流水号',
          placeholder: '请输入交易流水号'
        },
        person_no: {
          type: 'input',
          value: '',
          label: '人员编号',
          placeholder: '请输入人员编号'
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '充值订单号',
          placeholder: '请输入充值订单号'
        },
        name: {
          type: 'input',
          value: '',
          label: '用户姓名',
          placeholder: '请输入用户姓名'
        },
        pay_time: {
          type: 'daterange',
          label: '充值时间',
          clearable: false,
          value: RECENTSEVEN
        },
        finish_time: {
          type: 'daterange',
          label: '充值到账时间',
          clearable: false,
          value: RECENTSEVEN
        },
        refund_type_list: {
          type: 'select',
          label: '退款状态',
          value: '',
          multiple: true,
          collapseTags: true,
          placeholder: '请选择退款类型',
          dataList: REFUND_TYPE_LIST
        },
        wallet_org_list: {
          type: 'select',
          label: '动账钱包',
          value: [],
          multiple: true,
          collapseTags: true,
          filterable: true,
          placeholder: '请选择动账钱包',
          dataList: []
        },
        account_username: {
          type: 'input',
          value: '',
          label: '操作员',
          placeholder: '请输入操作员'
        },
        only_rate_fee: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看手续费',
          value: false
        }
      },
      rechargeRefundSearchForm: {
        payway: {
          type: 'select',
          label: '退款渠道',
          value: '',
          placeholder: '请选择退款渠道',
          dataList: []
        },
        pay_scene: {
          type: 'select',
          label: '退款类型',
          value: '',
          placeholder: '请选择退款类型',
          dataList: RECHARGE_TYPEARR
        },
        order_status: {
          type: 'select',
          label: '退款状态',
          value: '',
          placeholder: '请选择退款状态',
          dataList: REFUND_STATEARR
        },
        phone: {
          type: 'input',
          value: '',
          label: '手机号',
          placeholder: '请输入手机号'
        },
        person_no: {
          type: 'input',
          value: '',
          label: '人员编号',
          placeholder: '请输入人员编号'
        },
        refund_charge_trade_no: {
          type: 'input',
          value: '',
          label: '充值退款订单号',
          placeholder: '请输入充值退款订单号'
        },
        name: {
          type: 'input',
          value: '',
          label: '用户姓名',
          placeholder: '请输入用户姓名'
        },
        create_time: {
          type: 'daterange',
          label: '退款时间',
          clearable: false,
          value: RECENTSEVEN
        },
        account_username: {
          type: 'input',
          value: '',
          label: '操作员',
          placeholder: '请输入操作员'
        },
        refund_type: {
          type: 'select',
          label: '退款方式',
          value: '',
          placeholder: '请选择退款方式',
          dataList: REFUND_TYPE
        },
        wallet_org_list: {
          type: 'select',
          label: '动账钱包',
          value: [],
          multiple: true,
          collapseTags: true,
          filterable: true,
          placeholder: '请选择动账钱包',
          dataList: []
        },
        only_rate_fee: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看手续费',
          value: false
        },
        out_trade_no: {
          type: 'input',
          value: '',
          label: '第三方订单号',
          placeholder: '请输入订单号'
        }
      },

      // 控制退款弹窗框
      outerVisible: false,
      innerVisible: false,
      refundTableData: [], // 退款弹窗框列表数据
      refundDialogData: {},
      radio: '1', // 全额退款
      orderIds: [], // 多选功能
      printTicketVisible: false,
      refundFrom: {
        refundRemark: ''
      },
      dialogForm: {
        refundMoney: '', // 部分退款金额
        refundWalletMoney: '', // 储值退款金额
        walletBalance: '' // 钱包余额
      },
      dialogFormRules: {
        refundWalletMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }]
      },
      isShowPartRefund: true, // 是否显示部分退款功能
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
    this.getpayList()
    this.getOrderChargeWalletList()
  },
  mounted() {},
  // computed: {
  //   // 退款弹出框的金额退款
  //   refund_money() {
  //     return this.refundTableData.reduce((pre, cur) => {
  //       return pre + cur.pay_fee
  //     }, 0)
  //   }
  // },
  methods: {
    initLoad(isFirst) {
      console.log("isFirst", isFirst);
      // 初始化表格以及数据
      if (this.current === 0) {
        this.tableSetting = this.rechargeTableSetting
        this.searchForm = this.rechargeSearchForm
        if (!isFirst) {
          this.getRechargeOrderList()
        }
        // this.getRechargeOrderList()
      } else {
        this.tableSetting = this.rechargeRefundTableSetting
        this.searchForm = this.rechargeRefundSearchForm
        if (!isFirst) {
          this.getRefundOrderList()
        }
        // this.getRefundOrderList()
      }
      this.initPrintSetting()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.printTicketVisible = false
        // 搜索重置
        this.page = 1
        this.isFirstSearch = false
        this.$nextTick(() => {
          this.initLoad()
        })
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.page = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    tabHandler(type) {
      this.current = type
      this.page = 1
      this.isFirstSearch = true
      this.$nextTick(() => {
        if (type === 0) {
          this.printType = 'rechargeOrder'
          this.searchForm = this.rechargeSearchForm
          this.tableSetting = this.rechargeTableSetting
          this.initPrintSetting()
          this.tableData = []
          // this.getRechargeOrderList()
        } else {
          this.printType = 'rechargeRefundOrder'
          this.searchForm = this.rechargeRefundSearchForm
          this.tableSetting = this.rechargeRefundTableSetting
          this.initPrintSetting()
          this.tableData = []
          // this.getRefundOrderList()
        }
      })
    },
    // 充值信息请求
    async getRechargeOrderList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderChargeListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.total_amount = res.data.total_amount
        this.total_count = res.data.count
        this.total_real_amount = res.data.total_wallet_amount
        this.total_rate_fee = res.data.total_rate_fee
        this.total_complimentary_amount = res.data.total_complimentary_amount
      } else {
        this.$message.error(res.msg)
      }
    },
    // 充值退款信息请求
    async getRefundOrderList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderRefundChargeListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results.map(v => {
          v.complimentary_fee_2 = v.complimentary_fee
          return v
        })
        this.totalCount = res.data.count
        this.refund_total_count = res.data.count
        this.refund_total_amount = res.data.total_amount
        this.total_rate_fee = res.data.total_rate_fee
      } else {
        this.$message.error(res.msg)
      }
    },
    // 退款按钮禁用 20230608 梓健和罗杰确认增加退款笔数不是零的都不给退款
    statusRefund(e) {
      var netFee = e.net_fee || 0
      var rateFee = e.rate_fee || 0
      var canRefundPrice = divide(netFee - rateFee)
      let data
      if (e.net_fee === 0 || (e.refund_count !== 0 && canRefundPrice <= 0.01)) {
        data = true
      }
      return data
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.initLoad()
    },
    // 退款start
    rechargeFn(row) {
      this.refundTableData = []
      this.refundTableData.push(row)
      this.refundDialogData = row
      this.dialogForm.refundMoney = divide(row.net_fee - row.rate_fee)
      this.dialogForm.walletBalance = divide(row.real_time_fee)
      this.radio = this.companyPrice(this.refundDialogData.pay_fee, this.refundDialogData.net_fee) ? "1" : "part"
      this.outerVisible = true
      console.log("rechargeFn", this.refundTableData, row, row.pay_fee, this.dialogForm.refundMoney);
    },
    // (第二层)内层退款弹出框
    async handleClose() {
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderChargeRefundPost({
          trade_no: this.refundTableData[0].trade_no,
          refund_fee: this.radio === 'part' ? times(this.dialogForm.refundWalletMoney) : this.refundTableData[0].pay_fee,
          remark: this.refundFrom.refundRemark,
          // refund_fee: this.refundDialogData.allow_refund_fee,
          refund_reason: '退款理由',
          refund_type: this.radio === 'part' ? 'ORDER_PART_REFUND' : 'ORDER_REFUND'
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.refundFirstFn()
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
      this.outerVisible = false
      this.innerVisible = false
      this.refundTableData = []
    },
    // (第一层)内层退款弹出框取消键
    refundFirstFn() {
      this.outerVisible = false
      this.refundTableData = []
      this.radio = '1'
      this.dialogForm = {
        refundMoney: '', // 部分退款金额
        refundWalletMoney: '', // 储值退款金额
        wallet: '' // 钱包余额
      }
      this.refundFrom.refundRemark = ''
    },
    // 退款end

    // 导出 列表
    handleExport() {
      let type
      if (this.current === 0) {
        type = 'ExportChargeOrder'
      } else {
        type = 'ExportChargeRefundOrder'
      }
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const option = {
        type,
        params
      }
      this.exportHandle(option)
    },
    // 获取支付方式 / 支付类型
    async getpayList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost({
        type: 'charge' // 这是这个表特定的参数
      })
      if (res.code === 0) {
        const result = []
        res.data.result.payways.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.rechargeSearchForm.payway.dataList = [{ label: '全部', value: '' }, ...result] // 支付类型
        this.rechargeRefundSearchForm.payway.dataList = [{ label: '全部', value: '' }, ...result] // 支付类型
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取动账钱包数据
    async getOrderChargeWalletList() {
      const res = await this.$apis.apiBackgroundOrderOrderChargeWalletListPost()
      if (res.code === 0) {
        if (res.data.length) {
          let list = res.data.map(item => {
            // 使用Object.entries()进行键值对遍历，提高代码的易读性和简洁性
            let label = Object.entries(item).map(([key, value]) => {
              return { label: value, value: key };
            });
            // 取出第一个元素，假设每个item只有一个键值对
            return label[0];
          });
          this.rechargeSearchForm.wallet_org_list.dataList = list
          this.rechargeRefundSearchForm.wallet_org_list.dataList = list
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 多选禁用
    selectableHandle(row) {
      return row.net_fee !== 0
    },
    // 订单的多选
    handleOrderSelectionChange(val) {
      this.orderIds = val.map(item => {
        return item.id
      })
    },
    async mulRefundHandle() {
      if (this.dialogLoading) return
      if (!this.orderIds.length) return this.$message.error('请选择要退款的订单！')
      this.dialogLoading = true
      this.$confirm(`确定要将这些订单进行退款？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        // customClass: 'ps-confirm',
        // cancelButtonClass: 'ps-cancel-btn',
        // confirmButtonClass: 'ps-btn',
        closeOnClickModal: false,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {
              order_ids: this.orderIds
            }
            let res
            if (this.current === 0) {
              // 预约退款
              res = await this.$apis.apiBackgroundOrderOrderChargeRefundPost(params)
            }
            this.dialogLoading = false
            if (res.code === 0) {
              this.initLoad()
              this.$message.success(res.msg)
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              this.dialogLoading = false
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    openDialog() {
      if (!this.orderIds.length) {
        return this.$message.error('请先选择数据！')
      }
      this.printTicketVisible = true
    },
    // 退款类型监听
    changeRefundType() {
      if (this.radio === 'part') {
        // this.$refs.refundTable.clearSelection()
      }
    },
    // 退款按钮点击
    handlerRefundConfirm() {
      // 全额退款判断是否钱包余额不足
      if (this.isShowPartRefund && this.radio !== 'part' && (this.refundDialogData.allow_refund_fee > times(this.dialogForm.walletBalance, 100))) {
        return this.$message.error('钱包余额不满足退款')
      }
      if (this.radio === 'part') {
        // 部分退款 先校验
        this.$refs.dialogFormRef.validate((valid) => {
          if (valid) {
            this.innerVisible = true
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      } else {
        // 全部退款
        this.innerVisible = true
      }
    },
    // 对比价格
    companyPrice(price, price2) {
      console.log("price", price, price2);
      if (!this.isShowPartRefund) {
        return true
      }
      return price === price2
    }
  }
}
</script>

<style lang="scss" scoped>
.active {
  background-color: #ff9b45!important;
  color: #fff!important;
}
.el-button:focus.el-button--default:not(.is-plain):not(.el-button--primary), .el-button:hover.el-button--default:not(.is-plain):not(.el-button--primary) {
  background-color: #fff;
  color: #ff9b45;
}
// 日历
// .el-range-editor--mini.el-input__inner {
//   height: 32px !important;
//   width: 320px;
// }
.recharge-order {
  // font-size: 14px;
  .ps-small-box {
    // .el-range-editor--mini.el-input__inner {
    //   margin-right: 65px;
    // }
    .searchref_top {
      margin-bottom: 10px;
    }
    .block {
      display: inline-block;
    }
    .demo-input-suffix {
      display: inline-block;
    }

    .el-select {
      width: 180px !important;
    }
    .el-input {
      width: 180px !important;
    }
  }
  .el-table {
    font-size: 12px !important;
  }
  .total {
    padding: 0 20px;
    color: #606266;
    li {
      font-weight: bold;
      display: inline-block;
      margin-right: 20px;
      font-size: 14px;
      span {
        font-weight: normal;
      }
    }
  }
  .refund_money {
    li {
      margin: 0 20px 10px 0;
    }
  }
  .ps-text-gray {
    color : #a0a3a8;
  }

}
</style>
