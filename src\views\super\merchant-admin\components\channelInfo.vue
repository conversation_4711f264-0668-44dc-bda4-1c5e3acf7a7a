<template>
  <div class="channel-info-content">
    <el-form :label-position="'right'" label-width="120px" :model="formLabelAlign">
      <el-form-item label="所属上级：">
        <div>{{ formLabelAlign.father || '--' }}</div>
      </el-form-item>
      <el-form-item label="渠道名称：">
        <div class="ps-flex-align-c">
          <div class="m-r-20">{{ formLabelAlign.name || '--' }}</div>
          <el-button type="text" @click="showEditChannelDrawer(formLabelAlign.name)">修改</el-button>
        </div>
      </el-form-item>
      <el-form-item label="监管平台权限：">
        <el-button type="text" @click="showPermissionConfiguration">去配置（{{ channelSelectedPermissionList.length }}个）</el-button>
      </el-form-item>
      <!-- <el-form-item label="跳转链接：">
        <el-button type="text" :disabled="!formLabelAlign.token" @click="gotoPage(formLabelAlign.token)">登录</el-button>
      </el-form-item> -->
      <el-form-item label="绑定组织：">
        <div class="flex-b-c">
          <el-button type="text" class="m-r-20" @click="gotoBind">去绑定</el-button>
          <el-tooltip class="item" effect="dark" content="" placement="top-end">
            <div slot="content" class="text-center">
              进行绑定操作时，如上级/下级已被绑定，请先<br/>解绑后再进行操作，解绑前请自行记录当前绑<br/>定组织的数据权限。绑定后可根据数据需求进<br/>行组织树的权限控制。
            </div>
            <i class="el-icon-question font-size-24"></i>
          </el-tooltip>
        </div>
        <div class="table-style">
          <el-table :data="formLabelAlign.org" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #updateTime="{ row }">
                {{ computedTime(row.update_time) }}
              </template>
              <template #operation="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="setPermission(row)">数据权限</el-button>
                <el-button type="text" size="small" class="ps-warn-text" @click="removeHandle(row)">移除</el-button>
              </template>
            </table-column>
          </el-table>
        </div>
      </el-form-item>
    </el-form>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'修改渠道名称'"
        :visible="editChannelDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="editChannelFormRef" :model="editChannelForm" label-width="80px" label-position="left">
            <el-form-item :label="'渠道名称'" prop="name" :rules="[{ required: true, message: '请输入渠道名称', trigger: ['change', 'blur'] }]">
              <el-input v-model="editChannelForm.name" class="w-300" placeholder="请输入渠道名称，不超过30个字" maxlength="30"></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('edit')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('edit')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'绑定组织'"
        :visible="bindOrgDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20" v-loading="checkChannelLoading">
          <div class="red m-b-10 font-size-14">绑定需要保存后生效。本次新增绑定组织：{{ addCount }}个</div>
          <el-form ref="bindOrgFormRef" :model="bindOrgForm" label-width="80px" label-position="left">
            <el-form-item :label="'项目名称'" prop="name" :rules="{ required: bindOrgForm.address ? false : true, message: '请输入项目名称', trigger: ['change', 'blur']}">
              <el-select
                v-model="bindOrgForm.name"
                class="w-300"
                filterable
                clearable
                placeholder="输入项目点名称进行查询">
                <el-option
                  v-for="(item, index) in orgList"
                  :key="index"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="'项目地址'" prop="address" :rules="{ required: bindOrgForm.name ? false : true, message: '请输入项目地址', trigger: ['change', 'blur']}">
              <el-cascader
                ref="cascaderRef"
                class="w-300 m-r-20"
                placeholder="请选择项目点地址进行查询"
                :options="addrOptions"
                v-model="bindOrgForm.address"
                filterable
              ></el-cascader>
              <el-button class="ps-origin-btn" @click="getBingOrgList">查询</el-button>
            </el-form-item>
          </el-form>
          <el-table
            :data="bindOrgTableData"
            v-loading="bindLoading"
            stripe
            header-row-class-name="ps-table-header-row"
            row-key="id"
            default-expand-all
            :tree-props="{children: 'children_list', hasChildren: 'hasChildren'}">
            <table-column v-for="(item, i) in bindOrgTableSetting" :key="item.key + i" :col="item">
              <template #districtAlias="{ row }">
                <span>{{ computedAddress(row.district_alias) }}</span>
              </template>
              <template #operation="{ row }">
                <div v-if="row.supervision_channel_bind">
                  <el-popconfirm
                    title="解绑后项目数据将不再上传，确定要解绑？"
                    @confirm="bindHandle(row, 'unBind')">
                    <el-button type="text" size="small" class="ps-red" slot="reference">解绑</el-button>
                  </el-popconfirm>
                </div>
                <div v-else>
                  <el-button type="text" size="small" class="ps-origin-text" @click="bindHandle(row, 'unBind')" :disabled="!row.isCanBind">绑定</el-button>
                </div>
              </template>
            </table-column>
          </el-table>
          <div class="ps-el-drawer-footer">
            <el-button size="small" type="primary" class="w-100" @click="cancelHandle('bind')">关闭</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'数据权限'"
        :visible="dataPermissionDrawerShow"
        :show-close="false"
        size="100%">
        <div class="p-20" v-loading="dataPermissionLoading">
          <span>注：默认计算绑定组织数据包含其下级组织，禁用组织或禁用项将不统计数据；点击【禁用】按钮，禁用数据项/禁用组织；</span>
          <el-table
            :data="orgTree"
            v-loading="isLoading"
            row-key="id"
            border
            stripe
            default-expand-all
            header-row-class-name="ps-table-header-row"
            :tree-props="{children: 'children_list'}">
            <el-table-column :align="itemIn.align" v-for="(itemIn, indexIn) in permissionSetting" :key="indexIn" :prop="itemIn.key">
              <template #default="{ row }">
                <div v-if="itemIn.key === 'name'" class="ps-flex-align-c flex-right flex-align-c w-180">
                    <div class="m-r-10">{{ row.name }}</div>
                    <el-button size="mini" type="primary" @click="isOrgDataShow(row)">禁用</el-button>
                </div>
                <div v-else>
                  <div class="flex flex-col">
                    <el-switch v-model="row[itemIn.key]" @change="changeSelectThisRow(orgTree, itemIn.key)" />
                    <div>
                      <span class="m-r-10">仅当前</span>
                      <el-checkbox v-model="row[`${itemIn.key}_selectThisRow`]" @change="selectThisIsOpen(row[`${itemIn.key}_selectThisRow`], row, orgTree, itemIn.key)"></el-checkbox>
                    </div>
                  </div>
                </div>
              </template>
              <template #header>
                <!-- {{ itemIn.label }} -->
                <div v-if="itemIn.key === 'name'">
                  {{ itemIn.label }}
                </div>
                <div v-else class="flex-b-c w-180">
                  {{ itemIn.label }}
                  <el-button size="mini" type="primary" @click="isColumnDataShow(closeColumn[`${itemIn.key}`], itemIn.key, orgTree)">禁用</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('permission')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('permission')">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- 权限弹窗 -->
    <PermissionConfiguration
      :isShow.sync="permissionConfigurationShow"
      :id="organizationData.id"
      :name="organizationData.name"
      :channelSelectedPermissionList="channelSelectedPermissionList"
      :parentChannelSelectedPermissionList="parentChannelSelectedPermissionList"
      @refreshPermission="refreshPermission">
    </PermissionConfiguration>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import { DEFAULT_CHANNEL_TABLE_SETTING } from '../constants/bankMerchantConstants'
import { regionData } from 'element-china-area-data'
import PermissionConfiguration from './PermissionConfiguration.vue'
import dayjs from 'dayjs'
export default {
  components: {
    PermissionConfiguration
  },
  props: {
    organizationData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    tabType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      bindLoading: false,
      checkChannelLoading: false,
      formLabelAlign: {
        father: '',
        name: '',
        token: '',
        org: []
      },
      tableSetting: [
        { label: '绑定组织', key: 'org_name' },
        { label: '所属项目', key: 'company_name' },
        { label: '修改时间', key: 'update_time', type: 'slot', slotName: 'updateTime' },
        { label: '操作人', key: 'operator_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      editChannelDrawerShow: false,
      editChannelForm: {
        name: ''
      },
      bindOrgDrawerShow: false,
      bindOrgForm: {
        name: '',
        address: ''
      },
      orgList: [],
      addrOptions: regionData,
      bindOrgTableData: [],
      bindOrgTableSetting: DEFAULT_CHANNEL_TABLE_SETTING,
      permissionSetting: [
        { label: '组织', key: 'name', width: '300px', type: "slot", slotName: "name", align: 'right' },
        { label: '收入数据', key: 'revenue_data', type: "slot", slotName: "revenue_data", align: 'left' },
        { label: '支出数据', key: 'expenditure_data', type: "slot", slotName: "expenditure_data", align: 'left' },
        { label: '入账数据', key: 'recorded_data', type: "slot", slotName: "recorded_data", align: 'left' },
        { label: '供应商信息', key: 'supplier_information', type: "slot", slotName: "supplier_information", align: 'left' },
        { label: '采购数据', key: 'purchasing_data', type: "slot", slotName: "purchasing_data", align: 'left' },
        { label: '出入库数据', key: 'in_out_data', type: "slot", slotName: "in_out_data", align: 'left' },
        { label: '食谱数据', key: 'recipe_data', type: "slot", slotName: "recipe_data", align: 'left' },
        { label: '留样数据', key: 'sample_data', type: "slot", slotName: "sample_data", align: 'left' },
        { label: '监控数据', key: 'monitoring_data', type: "slot", slotName: "monitoring_data", align: 'left' },
        { label: '负债数据', key: 'liability_data', type: "slot", slotName: "liability_data", align: 'left' }
      ],
      permissionKeyList: [
        'revenue_data',
        'expenditure_data',
        'recorded_data',
        'supplier_information',
        'purchasing_data',
        'in_out_data',
        'recipe_data',
        'sample_data',
        'monitoring_data'
      ],
      closeColumn: {
        revenue_data: false,
        expenditure_data: false,
        recorded_data: false,
        supplier_information: false,
        purchasing_data: false,
        in_out_data: false,
        recipe_data: false,
        sample_data: false,
        monitoring_data: false
      },
      dataPermissionDrawerShow: false,
      dataPermissionLoading: false,
      orgTree: [],
      addCount: 0,
      selectRowData: {},
      permissionConfigurationShow: false,
      channelSelectedPermissionList: [],
      parentChannelSelectedPermissionList: []
    }
  },
  computed: {
    computedTime() {
      return d => {
        return dayjs(d).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    computedAddress() {
      return d => {
        let str = d.replace(/^\[|\]$/g, "")
        return str
      }
    }
  },
  watch: {
    organizationData: {
      handler: function(newVal, oldVal) {
        if (newVal) {
          this.channelSelectedPermissionList = this.organizationData.permission || []
          this.parentChannelSelectedPermissionList = this.organizationData.parent_permission || []
          this.formLabelAlign.father = newVal.parent_alias || '--'
          this.formLabelAlign.name = newVal.name || '--'
          this.formLabelAlign.token = newVal.get_login_token || ''
          this.formLabelAlign.org = deepClone(newVal.binded_org_info || [])
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getOrganizationList()
  },
  methods: {
    // 抽屉的方法
    cancelHandle(type) {
      switch (type) {
        case 'edit':
          this.$refs.editChannelFormRef.resetFields()
          this.editChannelDrawerShow = false
          break
        case 'bind':
          this.bindOrgDrawerShow = false
          this.$emit('refresh', this.organizationData)
          break
        case 'permission':
          this.dataPermissionDrawerShow = false
          break
      }
    },
    showEditChannelDrawer(name) {
      this.editChannelForm.name = name
      this.editChannelDrawerShow = true
    },
    saveHandle(type) {
      switch (type) {
        case 'edit':
          this.changeName()
          break
        case 'permission':
          this.savePermission()
          break
      }
    },
    gotoBind() {
      this.addCount = 0
      this.bindOrgDrawerShow = true
    },
    async bindHandle(data, type) {
      let params = {
        supervision_channel_id: this.organizationData.id,
        org_id: data.id,
        bind_type: !data.supervision_channel_bind
      }
      await this.$apis.apiBackgroundFundSupervisionSupervisionChannelOrgBingConfigPost(params).then(res => {
        if (res.code === 0) {
          if (!data.supervision_channel_bind) {
            this.addCount++
          }
          this.$message.success(!data.supervision_channel_bind ? '绑定成功' : '解绑成功')
          console.log('type', type)
          if (type === 'unBind') {
            // this.$refs.bindOrgFormRef.resetFields()
            this.bindOrgTableData = []
            this.getBingOrgList()
          } else if (type === 'remove') {
            this.bindOrgTableData = []
            this.$emit('refresh', this.organizationData)
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 修改渠道名
    changeName() {
      let params = {
        id: this.organizationData.id,
        name: this.editChannelForm.name
      }
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelModifyPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
        }
        this.editChannelDrawerShow = false
        this.$emit('refresh', this.organizationData)
      })
    },
    // 保存权限
    savePermission() {
      let config = []
      // 设置config
      this.setConfigArr(this.orgTree, config)
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelOrgPermissionConfigPost({
        supervision_channel_id: this.organizationData.id,
        config: config
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('保存成功')
        } else {
          this.$message.error(res.msg)
        }
        this.dataPermissionDrawerShow = false
        this.$emit('refresh', this.organizationData)
      })
    },
    // 遍历插入数据
    setConfigArr(targetArr, config) {
      if (targetArr.length) {
        targetArr.forEach(item => {
          let obj = {
            organization_id: item.id,
            revenue_data: item.revenue_data,
            expenditure_data: item.expenditure_data,
            recorded_data: item.recorded_data,
            purchasing_data: item.purchasing_data,
            supplier_information: item.supplier_information,
            in_out_data: item.in_out_data,
            recipe_data: item.recipe_data,
            sample_data: item.sample_data,
            monitoring_data: item.monitoring_data,
            liability_data: item.liability_data
          }
          config.push(obj)
          if (item.has_children && item.children_list.length) {
            this.setConfigArr(item.children_list, config)
          }
        })
      }
    },
    getOrganizationList() {
      this.checkChannelLoading = true
      this.$apis.apiBackgroundAdminOrganizationListPost({
        page: 1,
        page_size: 9999,
        parent__is_null: '1',
        status__in: ['enable']
      }).then(res => {
        this.checkChannelLoading = false
        if (res.code === 0) {
          this.orgList = res.data.results.map(item => {
            let obj = {
              id: item.id,
              name: item.name
            }
            return obj
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 跳转
    gotoPage() {
      window.open('https://baidu.com', '_blank')
    },
    // 获取用于绑定的数据
    getBingOrgList() {
      this.$refs.bindOrgFormRef.validate((valid) => {
        if (valid) {
          this.bindLoading = true
          this.$apis.apiBackgroundFundSupervisionSupervisionChannelTreeListPost({
            org_id: this.bindOrgForm.name || undefined,
            district: this.bindOrgForm.address ? JSON.stringify(this.bindOrgForm.address) : undefined
          }).then(res => {
            if (res.code === 0) {
              let arr = []
              if (res.data.length) {
                let treeItem = res.data.map(item => {
                  return item[0]
                })
                arr = deepClone(treeItem)
              } else {
                arr = []
              }
              // 先遍历逐级插入变量
              this.addStatus(arr)
              // 判断是否已绑
              this.setIsCanBind(arr)
              // 将数组转成map数据
              let treeMap = new Map()
              this.treeToMap(arr, treeMap)
              // 在整个map中找到已绑定的，往上找他的父级改变他的isCanBind
              treeMap.forEach(item => {
                if (item.supervision_channel_bind) {
                  // 如果当前这条数据是已绑的，用他的parent找到他的父级并调整他的状态
                  if (item.parent) {
                    this.findAndSet(item, treeMap)
                  }
                }
              })
              this.bindOrgTableData = deepClone(arr)
              this.bindLoading = false
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.$message.error('查询条件不能为空')
        }
      })
    },
    findAndSet(data, map) {
      let parentData = map.get(data.parent)
      parentData.isCanBind = false
      map.set(data.parent, { ...parentData })
      // 继续往上找
      if (parentData.parent) {
        this.findAndSet(parentData, map)
      }
    },
    // 将数组内的元素转成map数据
    treeToMap(treeArr, map) {
      treeArr.forEach(item => {
        map.set(item.id, item)
        if (item.children_list && item.children_list.length > 0) {
          this.treeToMap(item.children_list, map)
        }
      })
    },
    // 批量设置能否绑定
    setIsCanBind(arr) {
      arr.forEach(item => {
        if (item.supervision_channel_bind) {
          item.isCanBind = false
          if (item.children_list && item.children_list.length) {
            this.setFalseStatus(item.children_list)
          }
        } else {
          item.isCanBind = true
          if (item.children_list && item.children_list.length) {
            this.setIsCanBind(item.children_list)
          }
        }
      })
    },
    setFalseStatus(arr) {
      arr.forEach(item => {
        item.isCanBind = false
        item.supervision_channel_bind = false
        if (item.children_list && item.children_list.length) {
          this.setFalseStatus(item.children_list)
        }
      })
    },
    addStatus(targetArr) {
      if (targetArr.length) {
        targetArr.forEach(item => {
          Object.assign(item, { isCanBind: true })
          if (item.has_children && item.children_list.length) {
            this.addStatus(item.children_list)
          }
        })
      }
    },
    load(tree, treeNode, resolve) {
      let params = {
        supervision_channel_id: this.organizationData.id,
        parent__in: tree.tree_id
      }
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelBingOrgListPost(params).then(res => {
        if (res.code === 0) {
          resolve(res.data.results || [])
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取数据权限
    setPermission(data) {
      this.selectRowData = deepClone(data)
      this.dataPermissionDrawerShow = true
      this.$nextTick(() => {
        this.dataPermissionLoading = true
      })
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelGetOrgPermissionPost({
        org_id: data.org_id
      }).then(res => {
        this.dataPermissionLoading = false
        if (res.code === 0) {
          // 遍历插入需要的变量
          this.addIsOpen(res.data)
          console.log('res.data', res.data)
          this.orgTree = deepClone(res.data)
          console.log('this.orgTree', this.orgTree)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    addIsOpen(targetArr) {
      if (targetArr.length) {
        targetArr.forEach(item => {
          Object.assign(item, { isOpen: false })
          this.permissionKeyList.forEach(itemIn => {
            item[`${itemIn}_selectThisRow`] = false
          })
          if (item.has_children && item.children_list.length) {
            this.addIsOpen(item.children_list)
          }
        })
      }
    },
    // 选择仅当前
    selectThisIsOpen(status, row, list, key) {
      if (list.length && status) {
        console.log('打印一下', row, list, key)
        list.forEach(item => {
          if (item.id === row.id) {
            item[key] = true
          } else {
            item[key] = false
            item[`${key}_selectThisRow`] = false
          }
          if (item.has_children && item.children_list.length) {
            this.selectThisIsOpen(status, row, item.children_list, key)
          }
        })
      }
    },
    // 按钮切换时也要改变仅当前的状态
    changeSelectThisRow(list, key) {
      list.forEach(item => {
        item[`${key}_selectThisRow`] = false
        if (item.has_children && item.children_list.length) {
          this.changeSelectThisRow(item.children_list, key)
        }
      })
    },
    isColumnDataShow(status, key, list) {
      console.log('看看', status, key, list)
      let isShow = !status
      if (isShow) {
        list.forEach(item => {
          item[key] = false
          item[`${key}_selectThisRow`] = false
          if (item.has_children && item.children_list.length) {
            this.isColumnDataShow(status, key, item.children_list)
          }
        })
      }
    },
    isOrgDataShow(row) {
      this.permissionKeyList.forEach(item => {
        row[item] = false
        row[`${item}_selectThisRow`] = false
      })
    },
    removeHandle(row) {
      console.log('row', row)
      this.$confirm(`您正在移除组织：${row.company_name}。移除后组织数据将不再计入渠道，请谨慎操作。确定要移除该组织？`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        let obj = {
          id: row.org_id,
          supervision_channel_bind: true
        }
        this.bindHandle(obj, 'remove')
      }).catch(action => {
        console.log(action)
        this.$message('已取消')
      })
    },
    showPermissionConfiguration() {
      console.log('this.organizationData', this.organizationData)
      if (!this.organizationData.parent || this.parentChannelSelectedPermissionList.length) {
        this.permissionConfigurationShow = true
      } else {
        return this.$message.error('请先配置上级渠道权限')
      }
    },
    refreshPermission(e) {
      let params = {
        id: this.organizationData.id,
        name: this.organizationData.name,
        permission: e
      }
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelModifyPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
          this.permissionConfigurationShow = false
        } else {
          this.$message.error(res.msg)
        }
        this.$emit('refresh', this.organizationData)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.channel-info-content {
  padding: 10px 0px;
  &-item {
    margin-bottom: 10px;
    display: flex;
    &-label {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      min-width: 10%;
    }
    &-value {
      padding: 10px 0px;
      min-width: 90%;
      .table-style{
        width: 100%;
      }
    }
  }
}
::v-deep .cell {
  display: flex !important;
}
</style>
