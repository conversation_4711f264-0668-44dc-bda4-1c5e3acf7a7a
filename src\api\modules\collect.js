import http from '@/utils/request'

export default {
  // 餐补使用汇总
  apiBackgroundReportCenterDataReportMealSupplementSummaryList (params) {
    return http.post('/api/background_report_center/data_report/meal_supplement_summary_list', params)
  },
  // / 餐补使用汇总导出
  apiBackgroundReportCenterDataReportMealSupplementSummaryListExport (params) {
    return http.post('/api/background_report_center/data_report/meal_supplement_summary_list_export', params)
  },
  // 餐补使用明细列表
  apiBackgroundReportCenterDataReportMealSupplementDetailsList (params) {
    return http.post('/api/background_report_center/data_report/meal_supplement_details_list', params)
  },
  // 餐补使用明细导出
  apiBackgroundReportCenterDataReportMealSupplementDetailsListExport (params) {
    return http.post('/api/background_report_center/data_report/meal_supplement_details_list_export', params)
  },
  // 规则变更记录
  apiBackgroundMarketingConsumeGetConsumeRuleRecord (params) {
    return http.post('/api/background_marketing/consume/get_consume_rule_record', params)
  },
  // 规则变更记录导出
  apiBackgroundMarketingConsumeGetConsumeRuleRecordExport (params) {
    return http.post('/api/background_marketing/consume/get_consume_rule_record_export', params)
  },
  // 根据时间获取格则列表
  apiBackgroundMarketingConsumeGetConsumeRuleFilterName (params) {
    return http.post('/api/background_marketing/consume/get_consume_rule_filter_name', params)
  }
}
