<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <search-form
        ref="searchRef"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <!-- <button-icon color="plain" type="export" @click="gotoExport">导出Excel</button-icon> -->
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <el-table
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
            class="ps-table-tree"
            v-loading="isLoading"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item">
              <template #operation="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="clickOrderDetails(row)">
                  原订单
                </el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <order-details-drawer
      v-if="orderDetailsDrawerVisible"
      :isshow.sync="orderDetailsDrawerVisible"
      :tradeNo="drawerModifyData.origin_trade_no"
    ></order-details-drawer>
  </div>
</template>

<script>
import { to, deepClone, debounce } from '@/utils'
import { REFUND_ORDER } from './constantsConfig.js'
import OrderDetailsDrawer from './orderDetailsDrawer'

export default {
  name: 'RefundOrder',
  components: { OrderDetailsDrawer },
  data() {
    return {
      isLoading: false,
      tableSettings: [
        { label: '订单号', key: 'refund_no' },
        { label: '商品名称', key: 'commodity_name' },
        { label: '商品类型', key: 'commodity_type_alias' },
        { label: '积分回退', key: 'refund_points' },
        { label: '退款金额', key: 'refund_fee', type: 'money' },
        { label: '创建时间', key: 'create_time' },
        { label: '退款时间', key: 'finish_time' },
        { label: '用户名称', key: 'user_name' },
        { label: '手机号', key: 'user_phone' },
        { label: '原订单', key: 'origin_trade_no' },
        { label: '操作人', key: 'operator_name' },
        { label: '退款原因', key: 'refund_reason' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      total: 0,
      searchFormSetting: deepClone(REFUND_ORDER),
      orderDetailsDrawerVisible: false,
      drawerModifyData: {} // 编辑的数据
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getOrderRefundList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getOrderRefundList()
    }, 300),
    // 获取列表数据
    async getOrderRefundList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMemberPointsPointsOrderRefundListPost({
          page: this.currentPage,
          page_size: this.pageSize,
          ...this.formatQueryParams(this.searchFormSetting)
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.total = res.data.count
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    clickOrderDetails(row) {
      this.orderDetailsDrawerVisible = true
      this.drawerModifyData = deepClone(row)
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getOrderRefundList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getOrderRefundList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>
<style lang="scss" scoped></style>
