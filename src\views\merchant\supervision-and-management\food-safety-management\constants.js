import * as dayjs from 'dayjs'
import { MEAL_TYPES } from '@/utils/constants'

export const recentSevenDayTime = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD HH:mm:ss'),
  dayjs().format('YYYY-MM-DD HH:mm:ss')
]

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const recentThreeDay = [
  dayjs()
    .subtract(3, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const recentCurrentDay = [
  dayjs()
    .subtract(0, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]
// 晨检明细 筛选设置
export const SEARCH_SETTING_MORNING_INSPECTION_DETAILS = {
  select_time: {
    type: 'daterange',
    label: '晨检时间',
    value: recentSevenDay,
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '组织名称',
    listNameKey: 'name',
    listValueKey: 'id',
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  name: {
    type: 'input',
    value: '',
    label: '晨检人',
    dataList: [],
    placeholder: '请输入',
    maxlength: 20,
    clearable: true
  },
  check_result: {
    type: 'select',
    value: '全部',
    label: '晨检结果',
    dataList: [
      {
        label: '全部',
        value: '全部'
      },
      {
        label: '成功',
        value: '0'
      },
      {
        label: '失败',
        value: '1'
      }
    ],
    clearable: true
  }
}

// 晨检汇总 筛选设置
export const SEARCH_SETTING_MORNING_INSPECTION_SUMMARY = {
  select_time: {
    type: 'daterange',
    label: '晨检时间',
    value: recentCurrentDay,
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '组织名称',
    listNameKey: 'name',
    listValueKey: 'id',
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  name: {
    type: 'input',
    value: '',
    label: '晨检人',
    dataList: [],
    placeholder: '请输入',
    maxlength: 20,
    clearable: true
  },
  is_check: {
    type: 'select',
    value: '全部',
    label: '晨检状态',
    dataList: [
      {
        label: '全部',
        value: '全部'
      },
      {
        label: '已晨检',
        value: true
      },
      {
        label: '未晨检',
        value: false
      }
    ],
    clearable: true
  }
}

// 晨检明细 表格设置
export const TABLE_HEAD_DATA_MORNING_INSPECTION_DETAILS = [

  { label: '晨检时间', key: 'check_time' },
  { label: '组织名称', key: 'org_name' },
  { label: '晨检人姓名', key: 'name' },
  { label: '晨检结果', key: 'check_result_alias', type: "slot", slotName: 'checkResult' },
  { label: '不合格原因', key: 'remark' },
  { label: '健康证是否有效', key: 'health_certificate_status_alias' },
  { label: '体温', key: 'temperature', type: "slot", slotName: 'temperature' },
  { label: '手部识别结果', key: 'hand_result' },
  { label: '是否有腹泻和咽喉炎症', key: 'risk_type_one_alias' },
  {
    key: 'gerenweisheng',
    label: '个人卫生',
    children: [
      { key: '发帽', label: '发帽', type: "slot", slotName: 'extraField' },
      { key: '工服', label: '工服', type: "slot", slotName: 'extraField' },
      { key: '指甲', label: '指甲', type: "slot", slotName: 'extraField' },
      { key: '恶心', label: '恶心', type: "slot", slotName: 'extraField' },
      { key: '饰物', label: '饰物', type: "slot", slotName: 'extraField' }
    ]
  },
  {
    key: 'xiaohuidao',
    label: '消化道',
    children: [
      { key: '呕吐', label: '呕吐', type: "slot", slotName: 'extraField' },
      { key: '腹痛', label: '腹痛', type: "slot", slotName: 'extraField' },
      { key: '腹泻', label: '腹泻', type: "slot", slotName: 'extraField' }
    ]
  },
  {
    key: 'pifu',
    label: '皮肤',
    children: [
      { key: '外伤', label: '外伤', type: "slot", slotName: 'extraField' },
      { key: '烫伤', label: '烫伤', type: "slot", slotName: 'extraField' },
      { key: '疖肿', label: '疖肿', type: "slot", slotName: 'extraField' },
      { key: '湿疹', label: '湿疹', type: "slot", slotName: 'extraField' }
    ]
  },
  {
    key: 'huxidao',
    label: '呼吸道',
    children: [
      { key: '黄疸', label: '黄疸', type: "slot", slotName: 'extraField' },
      { key: '咽痛', label: '咽痛', type: "slot", slotName: 'extraField' },
      { key: '咳嗽', label: '咳嗽', type: "slot", slotName: 'extraField' },
      { key: '流涕', label: '流涕', type: "slot", slotName: 'extraField' }
    ]
  },
  { label: '图片', key: 'images', type: "slot", slotName: 'images' }
]
// 晨检汇总 表格设置
export const TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY = [
  { label: '晨检时间', key: 'check_time' },
  { label: '晨检人姓名', key: 'name' },
  { label: '晨检状态', key: 'check_status', type: "slot", slotName: 'checkStatus' },
  { label: '晨检次数', key: 'count' },
  { label: '合格数', key: 'qualified_count' },
  { label: '不合格数', key: 'unqualified_count' }
]

// 留样记录 筛选设置
export const SEARCH_SETTING_MORNING_SAMPLE_RECORD = {
  date_type: {
    type: 'select',
    value: 'reserved_time',
    label: '',
    width: '120px',
    dataList: [
      {
        label: '留样时间',
        value: 'reserved_time'
      },
      {
        label: '入柜时间',
        value: 'entry_date'
      },
      {
        label: '离柜时间',
        value: 'exit_time'
      },
      {
        label: '销样时间',
        value: 'destroy_time'
      }
    ],
    clearable: false
  },
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "",
    value: recentThreeDay,
    placeholder: "请选择",
    dataList: []
  },
  org_id: {
    type: 'organizationSelect',
    value: '',
    label: '组织名称',
    listNameKey: 'name',
    listValueKey: 'id',
    checkStrictly: true,
    multiple: false,
    collapseTags: true
  },
  meal_type: {
    type: 'select',
    value: [],
    clearable: true,
    label: '留样餐段',
    multiple: true,
    collapseTags: true,
    dataList: [
      ...MEAL_TYPES
    ]
  },
  menu_type: {
    type: 'select',
    value: '',
    label: '留样菜谱',
    placeholder: '请选择',
    dataList: [],
    clearable: true
  },
  food_name: {
    type: 'input',
    label: '菜品',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  reserved_user: {
    type: 'input',
    value: '',
    label: '留样员',
    placeholder: '请输入'
  },
  sample_exit_user: {
    type: 'input',
    value: '',
    label: '取样员',
    placeholder: '请输入'
  },
  sample_entry_user: {
    type: 'input',
    value: '',
    label: '入柜员',
    placeholder: '请输入'
  },
  reserved_status: {
    type: 'select',
    value: 'all',
    label: '留样状态',
    dataList: [
      {
        label: "全部",
        value: 'all'
      },
      {
        label: "已留样",
        value: "reserved"
      },
      {
        label: "未留样",
        value: "not_reserved"
      }
    ]
  },
  entry_cupboard: {
    type: 'select',
    value: 'all',
    label: '入柜状态',
    dataList: [
      {
        label: "全部",
        value: 'all'
      },
      {
        label: "是",
        value: true
      },
      {
        label: "否",
        value: false
      }
    ]
  },
  entry_device_ids: {
    type: 'select',
    value: [],
    label: '入柜设备',
    dataList: [],
    multiple: true,
    placeholder: '请选择',
    clearable: true,
    listNameKey: 'device_name',
    listValueKey: 'device_no'
  },
  category_ids: {
    type: 'cascader',
    label: '菜品分类',
    value: [],
    placeholder: '请选择分类',
    clearable: true,
    filterable: true,
    collapseTags: true,
    showAllLevels: false,
    width: '240px',
    dataList: [],
    props: {
      value: 'id',
      label: 'name',
      multiple: true,
      children: 'children',
      emitPath: false
    }
  }
}

// 留样记录 表格设置
export const TABLE_HEAD_DATA_SAMPLE_RECORD = [
  { label: '留样时间', key: 'reserved_time', width: '180px' },
  { label: '所属组织', key: 'org_name' },
  { label: '所属菜谱', key: 'menu_name' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '菜品', key: 'food_name', showTooltip: true, width: '130px' },
  { label: '菜品分类', key: 'category_name', showTooltip: true, width: '130px' },
  { label: '留样状态', key: 'reserved_status_alias' },
  { label: '留样数量', key: 'food_count' },
  { label: '留样重量', key: 'food_weight', type: 'slot', slotName: "foodWeight" },
  { label: '是否入柜', key: 'entry_cupboard', type: 'slot', slotName: "entryCupboard" },
  { label: '入柜时间', key: 'entry_cupboard_time', width: '180px' },
  { label: '存放时长', key: 'store_time', width: '180px' },
  { label: '离柜时间', key: 'exit_cupboard_time', width: '180px' },
  { label: '销样时间', key: 'destroy_time', width: '180px' },
  { label: '入柜设备', key: 'entry_device' },
  { label: '当前柜内温度', key: 'temperature', type: 'slot', slotName: "temperature" },
  { label: '留样员', key: 'reserved_user_name', type: 'slot', slotName: "reservedUserName", width: '180px', showTooltip: true },
  { label: '入柜员', key: 'sample_entry_user', type: 'slot', slotName: "sampleEntryUser", width: '180px', showTooltip: true },
  { label: '取样员', key: 'sample_exit_user', type: 'slot', slotName: "sampleExitUser", width: '180px', showTooltip: true },
  // { label: '未留样原因', key: 'not_reserved_reason', type: "slot", slotName: "notReservedReason", showTooltip: true, width: '180px' },
  { label: '未入柜原因', key: 'not_entry_reason', type: "slot", slotName: "notEntryReason", showTooltip: true, width: '180px' },
  { label: '留样照片', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: '140px' }
]

export const SEARCH_SETTING_DISHESROOTS = {
  select_time: {
    type: 'daterange',
    label: '日期筛选',
    value: recentSevenDay,
    clearable: false,
    labelWidth: '80px'
  },
  org_id: {
    type: 'organizationSelect',
    value: '',
    label: '组织名称',
    listNameKey: 'name',
    listValueKey: 'id',
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    labelWidth: '80px'
  },
  meal_type: {
    type: 'select',
    value: '',
    clearable: true,
    label: '餐段',
    multiple: false,
    collapseTags: true,
    labelWidth: '50px',
    dataList: [
      {
        label: "全部",
        value: ''
      },
      {
        label: "早餐",
        value: "breakfast"
      },
      {
        label: "午餐",
        value: "lunch"
      },
      {
        label: "下午茶",
        value: "afternoon"
      },
      {
        label: "晚餐",
        value: "dinner"
      },
      {
        label: "夜宵",
        value: "supper"
      },
      {
        value: "morning",
        label: "凌晨餐"
      }
    ]
  },
  food_name: {
    type: 'input',
    label: '菜品名称',
    clearable: true,
    value: '',
    placeholder: '请输入',
    labelWidth: '80px'
  },
  ingredient_name: {
    type: 'input',
    label: '使用食材',
    clearable: true,
    value: '',
    placeholder: '请输入',
    labelWidth: '80px'
  }
}

export const SEARCH_SETTING_REPASTROOTS = {
  select_time: {
    type: 'daterange',
    label: '日期筛选',
    value: recentSevenDay,
    clearable: false,
    labelWidth: '80px'
  },
  org_id: {
    type: 'organizationSelect',
    value: '',
    label: '组织名称',
    listNameKey: 'name',
    listValueKey: 'id',
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    labelWidth: '80px'
  },
  meal_type: {
    type: 'select',
    value: [],
    clearable: true,
    label: '餐段',
    multiple: false,
    labelWidth: '50px',
    collapseTags: true,
    dataList: [
      {
        label: "全部",
        value: ''
      },
      {
        label: "早餐",
        value: "breakfast"
      },
      {
        label: "午餐",
        value: "lunch"
      },
      {
        label: "下午茶",
        value: "afternoon"
      },
      {
        label: "晚餐",
        value: "dinner"
      },
      {
        label: "夜宵",
        value: "supper"
      },
      {
        value: "morning",
        label: "凌晨餐"
      }
    ]
  }
}

export const TABLE_HEAD_DATA_DISHESROOTS = [
  { label: '组织名称', key: 'org_name' },
  { label: '日期', key: 'pay_date' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '菜品名称', key: 'food_name', showTooltip: true },
  { label: '销售数量', key: 'total_count', type: "slot", slotName: 'totalCount' },
  { label: '使用食材', key: 'food_ingredient', type: "slot", slotName: 'foodIngredient' },
  { label: '操作', key: 'operation', type: "slot", slotName: 'operation' }
]

export const TABLE_HEAD_DATA_REPAST = [
  { label: '组织名称', key: 'organization_name' },
  { label: '日期', key: 'pay_date' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '就餐人数', key: 'count' },
  { label: '原始数据', key: 'download', type: "slot", slotName: 'download' }
]

export const CONTROL_MEASURE_LIST = [
  {
    label: '投饵',
    value: 'feeding'
  },
  {
    label: '电蚊器',
    value: 'electric_trap'
  },
  {
    label: '喷粉',
    value: 'dusting'
  },
  {
    label: '烟雾',
    value: 'smoke'
  },
  {
    label: '超低容量喷雾',
    value: 'low_spray'
  },
  {
    label: '滞留性喷洒',
    value: 'retention_spray'
  }
]

export const PEST_CLASS_LIST = [
  {
    label: '鼠',
    value: 'mouse'
  },
  {
    label: '蟑螂',
    value: 'cockroach'
  },
  {
    label: '蚊',
    value: 'mosquito'
  },
  {
    label: '苍蝇',
    value: 'fly'
  },
  {
    label: '蚂蚁',
    value: 'ant'
  },
  {
    label: '飞虫',
    value: 'flying_insect'
  },
  {
    label: '其他',
    value: 'other'
  }
]
