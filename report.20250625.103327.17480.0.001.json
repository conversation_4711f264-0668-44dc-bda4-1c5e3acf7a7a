{"header": {"reportVersion": 1, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20250625.103327.17480.0.001.json", "dumpEventTime": "2025-06-25T10:33:27Z", "dumpEventTimeStamp": "1750818807869", "processId": 17480, "cwd": "E:\\liangyizhen\\d代码\\shixiangSass\\background_v4", "commandLine": ["node", "E:\\liangyizhen\\d代码\\shixiangSass\\background_v4\\node_modules\\.bin\\\\..\\@vue\\cli-service\\bin\\vue-cli-service.js", "serve"], "nodejsVersion": "v12.16.1", "wordSize": 64, "arch": "x64", "platform": "win32", "componentVersions": {"node": "12.16.1", "v8": "7.8.279.23-node.31", "uv": "1.34.0", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "72", "nghttp2": "1.40.0", "napi": "5", "llhttp": "2.0.4", "http_parser": "2.9.3", "openssl": "1.1.1d", "cldr": "35.1", "icu": "64.2", "tz": "2019c", "unicode": "12.1"}, "release": {"name": "node", "lts": "Erbium", "headersUrl": "https://nodejs.org/download/release/v12.16.1/node-v12.16.1-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v12.16.1/node-v12.16.1.tar.gz", "libUrl": "https://nodejs.org/download/release/v12.16.1/win-x64/node.lib"}, "osName": "Windows_NT", "osRelease": "10.0.19041", "osVersion": "Windows 10 Pro", "osMachine": "x86_64", "cpus": [{"model": "Intel(R) Core(TM) i7-7700K CPU @ 4.20GHz", "speed": 4200, "user": 133028984, "nice": 0, "sys": 139597453, "idle": 1114531703, "irq": 51354671}, {"model": "Intel(R) Core(TM) i7-7700K CPU @ 4.20GHz", "speed": 4200, "user": 42757359, "nice": 0, "sys": 29547203, "idle": 1314853578, "irq": 611984}, {"model": "Intel(R) Core(TM) i7-7700K CPU @ 4.20GHz", "speed": 4200, "user": 111073453, "nice": 0, "sys": 75765359, "idle": 1200319328, "irq": 1634937}, {"model": "Intel(R) Core(TM) i7-7700K CPU @ 4.20GHz", "speed": 4200, "user": 49762093, "nice": 0, "sys": 29037421, "idle": 1308358421, "irq": 532046}, {"model": "Intel(R) Core(TM) i7-7700K CPU @ 4.20GHz", "speed": 4200, "user": 94728265, "nice": 0, "sys": 64098875, "idle": 1228330796, "irq": 1286468}, {"model": "Intel(R) Core(TM) i7-7700K CPU @ 4.20GHz", "speed": 4200, "user": 60136390, "nice": 0, "sys": 43706140, "idle": 1283315406, "irq": 737328}, {"model": "Intel(R) Core(TM) i7-7700K CPU @ 4.20GHz", "speed": 4200, "user": 89211390, "nice": 0, "sys": 55309031, "idle": 1242637515, "irq": 1052734}, {"model": "Intel(R) Core(TM) i7-7700K CPU @ 4.20GHz", "speed": 4200, "user": 111275656, "nice": 0, "sys": 63641312, "idle": 1212240968, "irq": 890828}], "networkInterfaces": [{"name": "以太网 2", "internal": false, "mac": "e0:d5:5e:05:b0:40", "address": "fe80::28ab:c975:a124:e3de", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 5}, {"name": "以太网 2", "internal": false, "mac": "e0:d5:5e:05:b0:40", "address": "*************", "netmask": "*************", "family": "IPv4"}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}], "host": "DESKTOP-OTHH"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x00007ff66ccb19f9", "symbol": ""}, {"pc": "0x00007ff66ccb5e1c", "symbol": ""}, {"pc": "0x00007ff66ccb4dd8", "symbol": ""}, {"pc": "0x00007ff66cdb34cb", "symbol": ""}, {"pc": "0x00007ff66d5c9f4e", "symbol": ""}, {"pc": "0x00007ff66d5b2021", "symbol": ""}, {"pc": "0x00007ff66d47e57c", "symbol": ""}, {"pc": "0x00007ff66d486521", "symbol": ""}, {"pc": "0x00007ff66d47bb13", "symbol": ""}, {"pc": "0x00007ff66d47a184", "symbol": ""}, {"pc": "0x00007ff66d49b36d", "symbol": ""}, {"pc": "0x00007ff66d200c51", "symbol": ""}, {"pc": "0x00007ff66da14ddd", "symbol": ""}, {"pc": "0x00007ff66d9e1526", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66da26fcc", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66da26fcc", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66da26fcc", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d9930bc", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d9930bc", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d999d8c", "symbol": ""}, {"pc": "0x00007ff66d9972c1", "symbol": ""}, {"pc": "0x00007ff66d996eac", "symbol": ""}, {"pc": "0x00007ff66d4d7c33", "symbol": ""}, {"pc": "0x00007ff66d4d75b6", "symbol": ""}, {"pc": "0x00007ff66d5a862d", "symbol": ""}, {"pc": "0x00007ff66ce0df1b", "symbol": ""}, {"pc": "0x00007ff66ce5ff44", "symbol": ""}, {"pc": "0x00007ff66ce5c99c", "symbol": ""}, {"pc": "0x00007ff66cd7ad43", "symbol": ""}, {"pc": "0x00007ff66cdda3d3", "symbol": ""}, {"pc": "0x00007ff66cc867ac", "symbol": ""}, {"pc": "0x00007ff66dacbe08", "symbol": ""}, {"pc": "0x00007ffef9a16fd4", "symbol": "BaseThreadInitThunk+20"}, {"pc": "0x00007ffef9f1cec1", "symbol": "RtlUserThreadStart+33"}], "javascriptHeap": {"totalMemory": 1507606528, "totalCommittedMemory": 1507606528, "usedMemory": 1261300464, "availableMemory": 932081128, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 262144, "capacity": 32808, "used": 32808, "available": 0}, "new_space": {"memorySize": 1048576, "committedMemory": 1048576, "capacity": 1047456, "used": 1047424, "available": 32}, "old_space": {"memorySize": 1083064320, "committedMemory": 1083064320, "capacity": 1080360888, "used": 845687752, "available": 234673136}, "code_space": {"memorySize": 9867264, "committedMemory": 9867264, "capacity": 9399968, "used": 8800480, "available": 599488}, "map_space": {"memorySize": 11538432, "committedMemory": 11538432, "capacity": 11525128, "used": 5972880, "available": 5552248}, "large_object_space": {"memorySize": 400621568, "committedMemory": 400621568, "capacity": 398664688, "used": 398664688, "available": 0}, "code_large_object_space": {"memorySize": 1204224, "committedMemory": 1204224, "capacity": 1094432, "used": 1094432, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 1047456, "used": 0, "available": 1047456}}}, "resourceUsage": {"userCpuSeconds": 121.109, "kernelCpuSeconds": 12.234, "cpuConsumptionPercent": 27.6072, "maxRss": 1961340928, "pageFaults": {"IORequired": 1052841, "IONotRequired": 0}, "fsActivity": {"reads": 13756, "writes": 1726}}, "libuv": [], "environmentVariables": {"=E:": "E:\\liangyizhen\\d代码\\shixiangSass\\background_v4", "ALLUSERSPROFILE": "C:\\ProgramData", "ANDROID_HOME": "D:\\SDK", "APPCODE_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\appcode.vmoptions", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "BABEL_ENV": "development", "BASE_URL": "/", "CHROME_CRASHPAD_PIPE_NAME": "\\\\.\\pipe\\crashpad_2644_VYMUPVGIYKOESWLQ", "CLASSPATH": ".;D:\\Java\\jdk1.8.0_201\\lib\\dt.jar;D:\\Java\\jdk1.8.0_201\\lib\\tools.jar", "CLION_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\clion.vmoptions", "COLORTERM": "truecolor", "CommonProgramFiles": "C:\\Program Files\\Common Files", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "CommonProgramW6432": "C:\\Program Files\\Common Files", "COMPUTERNAME": "DESKTOP-OTHH", "ComSpec": "C:\\Windows\\system32\\cmd.exe", "DATAGRIP_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\datagrip.vmoptions", "DATASPELL_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\dataspell.vmoptions", "DEVECOSTUDIO_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\devecostudio.vmoptions", "dp0": "E:\\liangyizhen\\d代码\\shixiangSass\\background_v4\\node_modules\\.bin\\", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "GATEWAY_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\gateway.vmoptions", "GENICAM_GENTL32_PATH": "C:\\Program Files (x86)\\Do3think\\DVP2\\", "GENICAM_GENTL64_PATH": "C:\\Program Files (x86)\\Do3think\\DVP2 x64\\", "GIT_ASKPASS": "e:\\software\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh", "GOLAND_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\goland.vmoptions", "GRADLE_USER_HOME": "D:\\gradle", "HOME": "C:\\Users\\<USER>", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\PUSHI", "IDEA_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\idea.vmoptions", "INIT_CWD": "E:\\liangyizhen\\d代码\\shixiangSass\\background_v4", "IntelliJ IDEA": "D:\\JetBrains\\IntelliJ IDEA 2018.3.5\\bin;", "JAVA_HOME": "D:\\Java\\jdk1.8.0_201", "JETBRAINSCLIENT_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\jetbrainsclient.vmoptions", "JETBRAINS_CLIENT_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\jetbrains_client.vmoptions", "LANG": "zh_CN.UTF-8", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\DESKTOP-OTHH", "NODE": "E:\\software\\nodejs\\node.exe", "NODE_ENV": "development", "NODE_EXE": "E:\\software\\nodejs\\\\node.exe", "NODE_PATH": "E:\\software\\nodejs\\node_modules", "NPM_CLI_JS": "E:\\software\\nodejs\\node_modules\\npm\\bin\\npm-cli.js", "npm_config_access": "", "npm_config_allow_same_version": "", "npm_config_also": "", "npm_config_always_auth": "", "npm_config_argv": "{\"remain\":[],\"cooked\":[\"run\",\"serve\"],\"original\":[\"run\",\"serve\"]}", "npm_config_audit": "true", "npm_config_audit_level": "low", "npm_config_auth_type": "legacy", "npm_config_before": "", "npm_config_bin_links": "true", "npm_config_browser": "", "npm_config_ca": "", "npm_config_cache": "C:\\Users\\<USER>\\AppData\\Roaming\\npm-cache", "npm_config_cache_lock_retries": "10", "npm_config_cache_lock_stale": "60000", "npm_config_cache_lock_wait": "10000", "npm_config_cache_max": "Infinity", "npm_config_cache_min": "10", "npm_config_cafile": "", "npm_config_cert": "", "npm_config_cidr": "", "npm_config_color": "true", "npm_config_commit_hooks": "true", "npm_config_config": "registry", "npm_config_depth": "Infinity", "npm_config_description": "true", "npm_config_dev": "", "npm_config_dry_run": "", "npm_config_editor": "notepad.exe", "npm_config_engine_strict": "", "npm_config_fetch_retries": "2", "npm_config_fetch_retry_factor": "10", "npm_config_fetch_retry_maxtimeout": "60000", "npm_config_fetch_retry_mintimeout": "10000", "npm_config_force": "", "npm_config_format_package_lock": "true", "npm_config_fund": "true", "npm_config_git": "git", "npm_config_git_tag_version": "true", "npm_config_global": "", "npm_config_globalconfig": "E:\\software\\nodejs\\etc\\npmrc", "npm_config_globalignorefile": "E:\\software\\nodejs\\etc\\npmignore", "npm_config_global_style": "", "npm_config_group": "", "npm_config_ham_it_up": "", "npm_config_heading": "npm", "npm_config_https_proxy": "", "npm_config_if_present": "", "npm_config_ignore_prepublish": "", "npm_config_ignore_scripts": "", "npm_config_init_author_email": "", "npm_config_init_author_name": "", "npm_config_init_author_url": "", "npm_config_init_license": "ISC", "npm_config_init_module": "C:\\Users\\<USER>\\.npm-init.js", "npm_config_init_version": "1.0.0", "npm_config_json": "", "npm_config_key": "", "npm_config_legacy_bundling": "", "npm_config_link": "", "npm_config_local_address": "", "npm_config_loglevel": "notice", "npm_config_logs_max": "10", "npm_config_long": "", "npm_config_maxsockets": "50", "npm_config_message": "%s", "npm_config_metrics_registry": "https://registry.npmmirror.com/", "npm_config_node_gyp": "E:\\software\\nvm\\v12.16.1\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js", "npm_config_node_options": "", "npm_config_node_version": "12.16.1", "npm_config_noproxy": "", "npm_config_offline": "", "npm_config_onload_script": "", "npm_config_only": "", "npm_config_optional": "true", "npm_config_otp": "", "npm_config_package_lock": "true", "npm_config_package_lock_only": "", "npm_config_parseable": "", "npm_config_prefer_offline": "", "npm_config_prefer_online": "", "npm_config_prefix": "E:\\software\\nodejs", "npm_config_preid": "", "npm_config_production": "", "npm_config_progress": "true", "npm_config_proxy": "", "npm_config_read_only": "", "npm_config_rebuild_bundle": "true", "npm_config_registry": "https://registry.npmmirror.com/", "npm_config_rollback": "true", "npm_config_save": "true", "npm_config_save_bundle": "", "npm_config_save_dev": "", "npm_config_save_exact": "", "npm_config_save_optional": "", "npm_config_save_prefix": "^", "npm_config_save_prod": "", "npm_config_scope": "", "npm_config_scripts_prepend_node_path": "warn-only", "npm_config_script_shell": "", "npm_config_searchexclude": "", "npm_config_searchlimit": "20", "npm_config_searchopts": "", "npm_config_searchstaleness": "900", "npm_config_send_metrics": "", "npm_config_shell": "C:\\Windows\\system32\\cmd.exe", "npm_config_shrinkwrap": "true", "npm_config_sign_git_commit": "", "npm_config_sign_git_tag": "", "npm_config_sso_poll_frequency": "500", "npm_config_sso_type": "o<PERSON>h", "npm_config_strict_ssl": "true", "npm_config_tag": "latest", "npm_config_tag_version_prefix": "v", "npm_config_timing": "", "npm_config_tmp": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "npm_config_umask": "0000", "npm_config_unicode": "", "npm_config_unsafe_perm": "true", "npm_config_update_notifier": "true", "npm_config_usage": "", "npm_config_user": "", "npm_config_userconfig": "C:\\Users\\<USER>\\.npmrc", "npm_config_user_agent": "npm/6.13.4 node/v12.16.1 win32 x64", "npm_config_version": "", "npm_config_versions": "", "npm_config_viewer": "browser", "npm_execpath": "E:\\software\\nvm\\v12.16.1\\node_modules\\npm\\bin\\npm-cli.js", "npm_lifecycle_event": "serve", "npm_lifecycle_script": "vue-cli-service serve && webpack-dev-server --open", "npm_node_execpath": "E:\\software\\nodejs\\node.exe", "npm_package_dependencies_axios": "^0.19.2", "npm_package_dependencies_clipboard": "^2.0.11", "npm_package_dependencies_core_js": "^3.6.5", "npm_package_dependencies_crypto_js": "^4.2.0", "npm_package_dependencies_dayjs": "^1.10.7", "npm_package_dependencies_echarts": "^4.9.0", "npm_package_dependencies_element_china_area_data": "^5.0.2", "npm_package_dependencies_element_ui": "^2.15.13", "npm_package_dependencies_file_loader": "^6.2.0", "npm_package_dependencies_file_saver": "^2.0.5", "npm_package_dependencies_html2canvas": "^1.4.1", "npm_package_dependencies_jspdf": "^2.5.1", "npm_package_dependencies_jszip": "^3.10.1", "npm_package_dependencies_js_cookie": "^3.0.1", "npm_package_dependencies_js_md5": "^0.7.3", "npm_package_dependencies_lodash": "^4.17.21", "npm_package_dependencies_nprogress": "^0.2.0", "npm_package_dependencies_number_precision": "^1.5.1", "npm_package_dependencies_qs": "^6.10.1", "npm_package_dependencies_save": "^2.9.0", "npm_package_dependencies_sortablejs": "^1.14.0", "npm_package_dependencies_tinymce": "^5.10.2", "npm_package_dependencies_umy_ui": "^1.1.6", "npm_package_dependencies_vconsole": "^3.9.1", "npm_package_dependencies_vue": "^2.6.11", "npm_package_dependencies_vuedraggable": "^2.24.3", "npm_package_dependencies_vuex": "^3.4.0", "npm_package_dependencies_vuex_persistedstate": "^4.1.0", "npm_package_dependencies_vue_barcode": "^1.3.0", "npm_package_dependencies_vue_demi": "^0.14.5", "npm_package_dependencies_vue_html2pdf": "^1.8.0", "npm_package_dependencies_vue_i18n": "^8.26.5", "npm_package_dependencies_vue_iframe_print": "^1.0.1", "npm_package_dependencies_vue_photo_preview": "^1.1.3", "npm_package_dependencies_vue_print_nb": "^1.7.5", "npm_package_dependencies_vue_router": "^3.2.0", "npm_package_dependencies_vue_virtual_scroll_list": "^2.3.4", "npm_package_dependencies_vue_wxlogin": "^1.0.4", "npm_package_dependencies_webpack_theme_color_replacer": "^1.4.7", "npm_package_dependencies_xlsx": "^0.17.4", "npm_package_dependencies__chenfengyuan_vue_qrcode": "^1.0.2", "npm_package_dependencies__packy_tang_vue_tinymce": "^1.1.2", "npm_package_dependencies__riophae_vue_treeselect": "^0.4.0", "npm_package_dependencies__vue_composition_api": "^1.7.2", "npm_package_dependencies__vue_office_docx": "^1.3.0", "npm_package_description": "相关依赖：\r [vue(2.x)](https://v2.cn.vuejs.org/v2/guide/) | \r [element-ui(2.x)](https://element.eleme.cn/#/zh-CN/component/installation) | \r [vue-router(3.x)](https://v3.router.vuejs.org/zh/) |\r [vuex(3.x)](https://vuex.vuejs.org/zh/)", "npm_package_devDependencies_babel_eslint": "^10.1.0", "npm_package_devDependencies_babel_plugin_component": "^1.1.1", "npm_package_devDependencies_babel_plugin_transform_remove_console": "^6.9.4", "npm_package_devDependencies_compression_webpack_plugin": "^4.0.0", "npm_package_devDependencies_eslint": "^6.7.2", "npm_package_devDependencies_eslint_plugin_import": "^2.20.2", "npm_package_devDependencies_eslint_plugin_node": "^11.1.0", "npm_package_devDependencies_eslint_plugin_promise": "^4.2.1", "npm_package_devDependencies_eslint_plugin_standard": "^4.0.0", "npm_package_devDependencies_eslint_plugin_vue": "^6.2.2", "npm_package_devDependencies_js_base64": "^3.7.7", "npm_package_devDependencies_mockjs": "^1.1.0", "npm_package_devDependencies_plop": "^2.7.3", "npm_package_devDependencies_postcss_pxtorem": "^5.1.1", "npm_package_devDependencies_postcss_px_to_viewport": "^1.1.1", "npm_package_devDependencies_prettier": "^2.8.4", "npm_package_devDependencies_sass": "^1.43.4", "npm_package_devDependencies_sass_loader": "^8.0.2", "npm_package_devDependencies_script_ext_html_webpack_plugin": "^2.1.5", "npm_package_devDependencies_shelljs": "^0.8.5", "npm_package_devDependencies_svg_sprite_loader": "^5.0.0", "npm_package_devDependencies_vue_template_compiler": "^2.6.11", "npm_package_devDependencies_webpack_bundle_analyzer": "^3.8.0", "npm_package_devDependencies_webpack_cli": "^4.9.1", "npm_package_devDependencies_webpack_dev_server": "^4.7.2", "npm_package_devDependencies_webpack_merge": "^5.8.0", "npm_package_devDependencies__vue_cli_plugin_babel": "~4.4.0", "npm_package_devDependencies__vue_cli_plugin_eslint": "~4.4.0", "npm_package_devDependencies__vue_cli_plugin_router": "~4.4.0", "npm_package_devDependencies__vue_cli_plugin_unit_jest": "~4.4.0", "npm_package_devDependencies__vue_cli_plugin_vuex": "~4.4.0", "npm_package_devDependencies__vue_cli_service": "~4.4.0", "npm_package_devDependencies__vue_eslint_config_standard": "^5.1.2", "npm_package_devDependencies__vue_test_utils": "^1.0.3", "npm_package_gitHead": "2cd6571d2c3ffd32762523c6321c3ffe71332083", "npm_package_name": "background_v4", "npm_package_private": "true", "npm_package_readmeFilename": "README.md", "npm_package_scripts_api": "node shell.config.js", "npm_package_scripts_build_prod": "vue-cli-service build --mode prod", "npm_package_scripts_build_staging": "vue-cli-service build --mode staging", "npm_package_scripts_build_test": "vue-cli-service build --mode test", "npm_package_scripts_clean": "node shell.clean.js", "npm_package_scripts_lint": "vue-cli-service lint", "npm_package_scripts_new": "plop", "npm_package_scripts_serve": "vue-cli-service serve && webpack-dev-server --open", "npm_package_scripts_svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "npm_package_scripts_test_unit": "vue-cli-service test:unit", "npm_package_version": "0.1.0", "NPM_PREFIX_NPM_CLI_JS": "E:\\software\\nodejs\\node_modules\\npm\\bin\\npm-cli.js", "NUMBER_OF_PROCESSORS": "8", "NVM_HOME": "E:\\software\\nvm", "NVM_SYMLINK": "E:\\software\\nodejs", "OneDrive": "C:\\Users\\<USER>\\OneDrive", "OneDriveConsumer": "C:\\Users\\<USER>\\OneDrive", "ORIGINAL_XDG_CURRENT_DESKTOP": "undefined", "OS": "Windows_NT", "Path": "E:\\software\\nvm\\v12.16.1\\node_modules\\npm\\node_modules\\npm-lifecycle\\node-gyp-bin;E:\\liangyizhen\\d代码\\shixiangSass\\background_v4\\node_modules\\.bin;D:\\Python\\Python38\\Scripts\\;D:\\Python\\Python38\\;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Program Files (x86)\\Do3think\\DVP2\\;C:\\Program Files (x86)\\Do3think\\DVP2 x64\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;E:\\software\\Git\\cmd;E:\\software\\Git\\bin;E:\\software\\nvm;E:\\software\\nodejs;E:\\software\\nodejs\\node_global;E:\\software\\nodejs\\node_modules;C:\\platform-tools;E:\\Python27;E:\\software\\mysql-5.7.19-winx64\\bin;E:\\software\\Xftp 7\\;D:\\Java\\jdk1.8.0_201\\bin;D:\\Java\\jdk1.8.0_201\\jre\\bin;E:\\software\\微信web开发者工具\\dll;e:\\software\\cursor\\resources\\app\\bin;D:\\SDK\\tools;D:\\SDK\\platform-tools;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;E:\\software\\Git\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;node_global;E:\\software\\nvm;E:\\software\\nodejs;E:\\software\\Microsoft VS Code\\bin;C:\\platform-tools;D:\\Fiddler;D:\\JetBrains\\IntelliJ IDEA 2018.3.5\\bin;;E:\\software\\cursor\\resources\\app\\bin", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL", "PHPSTORM_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\phpstorm.vmoptions", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 158 Stepping 9, GenuineIntel", "PROCESSOR_LEVEL": "6", "PROCESSOR_REVISION": "9e09", "ProgramData": "C:\\ProgramData", "ProgramFiles": "C:\\Program Files", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramW6432": "C:\\Program Files", "PROMPT": "$P$G", "PSModulePath": "C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules", "PUBLIC": "C:\\Users\\<USER>", "PYCHARM_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\pycharm.vmoptions", "RIDER_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\rider.vmoptions", "RUBYMINE_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\rubymine.vmoptions", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "STUDIO_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\studio.vmoptions", "SystemDrive": "C:", "SystemRoot": "C:\\Windows", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TERM_PROGRAM": "vscode", "TERM_PROGRAM_VERSION": "1.94.2", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "USERDOMAIN": "DESKTOP-OTHH", "USERDOMAIN_ROAMINGPROFILE": "DESKTOP-OTHH", "USERNAME": "PUSHI", "USERPROFILE": "C:\\Users\\<USER>", "VOLTA_FEATURE_PNPM": "1", "VSCODE_GIT_ASKPASS_EXTRA_ARGS": "", "VSCODE_GIT_ASKPASS_MAIN": "e:\\software\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js", "VSCODE_GIT_ASKPASS_NODE": "E:\\software\\Microsoft VS Code\\Code.exe", "VSCODE_GIT_IPC_HANDLE": "\\\\.\\pipe\\vscode-git-c9eede0f31-sock", "VSCODE_INJECTION": "1", "VUE_APP_BASE_URL": "/", "VUE_APP_DASHBOARD": "https://dashboard-v4.debug.packertec.com", "VUE_APP_HEALTHY": "http://*************:9527/#/order-management/consultation-order?companyId=1&&token=", "VUE_APP_MINAPP": "https://h5.debug.packertec.com", "VUE_CLI_ENTRY_FILES": "[\"E:\\\\liangyizhen\\\\d代码\\\\shixiangSass\\\\background_v4\\\\src\\\\main.js\"]", "VUE_CLI_TRANSPILE_BABEL_RUNTIME": "true", "WEBIDE_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\webide.vmoptions", "WEBPACK_DEV_SERVER": "true", "WEBSTORM_VM_OPTIONS": "C:\\Users\\<USER>\\Downloads\\jetbra\\jetbra\\vmoptions\\webstorm.vmoptions", "windir": "C:\\Windows", "_prog": "node", "_VOLTA_TOOL_RECURSION": "1"}, "sharedObjects": ["E:\\software\\nodejs\\node.exe", "C:\\Windows\\SYSTEM32\\ntdll.dll", "C:\\Windows\\System32\\KERNEL32.DLL", "C:\\Windows\\System32\\KERNELBASE.dll", "C:\\Windows\\System32\\WS2_32.dll", "C:\\Windows\\System32\\RPCRT4.dll", "C:\\Windows\\System32\\PSAPI.DLL", "C:\\Windows\\System32\\ADVAPI32.dll", "C:\\Windows\\SYSTEM32\\dbghelp.dll", "C:\\Windows\\System32\\msvcrt.dll", "C:\\Windows\\System32\\ucrtbase.dll", "C:\\Windows\\System32\\sechost.dll", "C:\\Windows\\System32\\USER32.dll", "C:\\Windows\\SYSTEM32\\IPHLPAPI.DLL", "C:\\Windows\\SYSTEM32\\USERENV.dll", "C:\\Windows\\System32\\win32u.dll", "C:\\Windows\\System32\\GDI32.dll", "C:\\Windows\\System32\\gdi32full.dll", "C:\\Windows\\System32\\msvcp_win.dll", "C:\\Windows\\System32\\CRYPT32.dll", "C:\\Windows\\System32\\bcrypt.dll", "C:\\Windows\\SYSTEM32\\WINMM.dll", "C:\\Windows\\System32\\IMM32.DLL", "C:\\Windows\\SYSTEM32\\powrprof.dll", "C:\\Windows\\SYSTEM32\\UMPDC.dll", "C:\\Windows\\SYSTEM32\\CRYPTBASE.DLL", "C:\\Windows\\system32\\uxtheme.dll", "C:\\Windows\\System32\\combase.dll", "C:\\Windows\\system32\\mswsock.dll", "C:\\Windows\\SYSTEM32\\kernel.appcore.dll", "C:\\Windows\\System32\\bcryptprimitives.dll", "C:\\Program Files (x86)\\Sangfor\\SSL\\ClientComponent\\SangforNspX64.dll", "C:\\Windows\\System32\\ole32.dll", "C:\\Windows\\System32\\OLEAUT32.dll", "C:\\Windows\\System32\\SHLWAPI.dll", "C:\\Windows\\system32\\napinsp.dll", "C:\\Windows\\system32\\pnrpnsp.dll", "C:\\Windows\\system32\\wshbth.dll", "C:\\Windows\\system32\\NLAapi.dll", "C:\\Windows\\SYSTEM32\\DNSAPI.dll", "C:\\Windows\\System32\\NSI.dll", "C:\\Windows\\System32\\winrnr.dll", "C:\\Windows\\SYSTEM32\\dhcpcsvc6.DLL", "C:\\Windows\\SYSTEM32\\dhcpcsvc.DLL", "C:\\Windows\\System32\\rasadhlp.dll", "C:\\Windows\\System32\\fwpuclnt.dll"]}