<template>
  <div class="PurchaseListDetails container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="handleExport">导出数据</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="id"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #count="{ row }">
              <span>{{ row.record_type === 'PURCHASE_ENTRY' ? row.expected_entry_count : row.count }}</span>
            </template>
            <template #fujian="{ row }">
              <el-button type="text" class="ps-text" size="small" @click="clickViewerHandler(row)" :disabled="!row.image_json || row.image_json.length === 0">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right; ">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
    </div>
    <!-- 预览 -->
    <image-viewer  v-model="showViewer" :initial-index="0" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce } from '@/utils'

export default {
  name: 'PurchaseListDetails',
  components: {
  },
  mixins: [exportExcel],
  data() {
    return {
      importLink: '',
      supplierId: '',
      tableData: [],
      isLoading: false, // 刷新数据
      searchFormSetting: {
        materials_name: {
          type: 'input',
          value: '',
          label: '物资名称',
          placeholder: '请输入物资名称',
          maxlength: 20
        }
      },
      tableSettings: [
        // { label: '所属组织', key: 'organization_name' },
        { label: '关联业务号', key: 'inventory_no' },
        // { label: '仓库名称', key: 'warehouse_name' },
        { label: '物资名称', key: 'materials_name' },
        { label: '物资分类', key: 'materail_classification_name' },
        { label: '数量', key: 'fixed_count' }, // ,  type: 'slot', slotName: 'count'
        { label: '单位', key: 'unit_management' },
        { label: '附件信息', key: 'fujian', type: 'slot', slotName: 'fujian' }
        // { label: '库存类型', key: 'record_type_alias' },
        // { label: '经手人', key: 'account_name' },
        // { label: '整单备注', key: 'tip' },
        // { label: '操作日期', key: 'update_time' }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      previewSrcList: [],
      showViewer: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getInboundAndOutboundReport()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getInboundAndOutboundReport()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getInboundAndOutboundReport() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDrpWarehouseInfoDetailPost({
        ...this.formatQueryParams(this.searchFormSetting),
        trade_no: this.$route.query.trade_no,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getInboundAndOutboundReport()
    },
    handleExport() {
      const option = {
        type: 'InboundAndOutboundReport',
        url: 'apiBackgroundDrpWarehouseInfoListExportPost',
        params: {
          trade_no: this.$route.query.trade_no,
          ...this.formatQueryParams(this.searchFormSetting)
        }
      }
      this.exportHandle(option)
    },
    // 查看预览图
    clickViewerHandler(row) {
      console.log("clickViewerHandler", row);
      // don't show viewer when preview is false
      let imgList = row.image_json || []
      if (imgList) {
        imgList = Array.isArray(imgList) ? imgList : JSON.parse(imgList)
      }
      this.previewSrcList = imgList
      if (!this.previewSrcList || this.previewSrcList.length === 0) {
        return this.$message.error('暂无图片');
      }
      this.showViewer = true;
    },
    // 关闭图片预览
    closeViewer() {
      this.showViewer = false;
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
