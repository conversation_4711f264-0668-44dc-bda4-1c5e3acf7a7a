<template>
  <!-- 添加/编辑 -->
  <custom-drawer
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :wrapperClosable="false"
    :fixedFooter="true"
    :size="760"
    destroy-on-close
    v-bind="$attrs"
    v-on="$listeners"
    :confirmShow="false"
    @close="handlerClose"
    @cancel="clickCancleHandle"
  >
    <div class="p-10">
      <el-table
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        size="small"
        stripe
        header-row-class-name="ps-table-header-row"
        row-key="id"
        border
        :max-height="tableHeight"
        :span-method="mergeSpanHandle"
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item">
          <template #count="{ row }">
            {{ `${row.count}*${row.limit_unit_name}*${row.net_content}${row.net_content_unit}` }}
          </template>
        </table-column>
      </el-table>
    </div>
  </custom-drawer>
  <!-- end -->
</template>

<script>
import { mergeHandle, mergeRowAction } from '@/utils/table'
export default {
  inheritAttrs: false,
  name: 'addMaterialWarehouseDialog',
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '关联物资'
    },
    width: {
      type: String,
      default: '460px'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    },
    confirm: Function
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSettings: [
        { label: '最小单位', key: 'limit_unit_name' },
        { label: '规格', key: 'unit_management_name' },
        { label: '数量', key: 'count', type: 'slot', slotName: 'count' },
        { label: '单价', key: 'unit_price', type: 'money', prefix: '￥' }
      ],
      tableHeight: 600,
      rowMergeArrs: [],
      mergeOpts: {
        useKeyList: {
          limit_unit_id: ['limit_unit_name']
        } // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        // mergeKeyList: ['limit_unit_name'] // 通用的合并字段，根據值合并
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        this.initLoad()
      }
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    async initLoad() {
      let dataList = this.infoData.material_specification || []
      if (dataList.length > 0) {
        // 规格要根据最小单位进行排序，不然无法合并单元格
        dataList.sort((a, b) => a.limit_unit_id > b.limit_unit_id ? 1 : -1)
      }
      this.tableData = dataList
      this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
      let bodyRects = document.body.getClientRects()
      this.tableHeight = bodyRects[0].height - 200
    },
    // 取消事件
    clickCancleHandle() {
      this.visible = false
      this.tableData = []
      this.rowMergeArrs = []
      // this.$emit('cancel')
    },
    // 关闭弹窗
    handlerClose(e) {
      this.tableData = []
      this.isLoading = false
      // this.visible = false
      // this.$emit('close')
    },
    // 合并单元格
    mergeSpanHandle({ row, column, rowIndex, columnIndex }) {
      if (this.mergeOpts.useKeyList && Object.keys(this.mergeOpts.useKeyList).length) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    }
  }
}
</script>

<style lang="scss">
.dialog-form {
  .m-l-4 {
    margin-left: 4px;
  }
  .m-r-4 {
    margin-right: 4px;
  }
  .w-220{
    width: 220px;
  }
  .w-260 {
    width: 260px;
  }
  .w-80 {
    width: 80px;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .el-upload__tip {
    color: red;
  }
  .ps-table-header-row {
    line-height: 31px;
  }
  .specifications {
    margin-right: 20px;
    margin-bottom: 22px;
    padding: 12px 14px;
    border-radius: 8px;
    background-color: #D7D7D7;
    .tool-icon {
      vertical-align: middle;
      cursor: pointer;
      font-size: 16px;
      &+.tool-icon {
        margin-left: 6px;
      }
      &:hover{
        color: #ff9b45;
      }
    }
    .specs-unit {
      display: inline-block;
      vertical-align: middle;
    }
    .el-form-item__error {
      top: 106%;
    }
    .fixed-tip {
      position: absolute;
      bottom: -28px;
      line-height: 1;
    }
  }
}
</style>
