<template>
  <div class="relationSupplierIngredient container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon v-permission="['background_drp.materials.import_materials']" color="origin"  @click="openImport">导入分类</button-icon>
          <button-icon v-permission="['background_drp.materials.add']" color="origin" type="add" @click="openDialogHandle('add')">新增分类</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #img="{ row }">
              <el-button v-if="row.material_feature_list && row.material_feature_list.length > 0" type="text" size="small" class="ps-text" @click="clickViewerHandler(row)">查看</el-button>
              <span v-else>--</span>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text"  @click="openDialogHandle('modify', row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-text" @click="mulOperation('del', row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right; ">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <add-material-category-dialog
      :showdialog.sync="dialogVisible"
      :type="dialogType"
      :title="dialogTitle"
      :width="dialogWidth"
      :info-data="dialogInfo"
      @clickConfirm="searchHandle"
    />
    <!-- dialog start -->
    <import-page-dialog
      v-if="showImportDialog"
      :show.sync="showImportDialog"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :url="importApi"
      isDeleteFirst
    ></import-page-dialog>
    <!-- dialog end -->
    <!-- 抄element的组件 -->
    <image-viewer v-model="showViewer" :initial-index="0" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import addMaterialCategoryDialog from './components/addMaterialCategoryDialog.vue'
import { debounce, getSevenDateRange } from '@/utils'

export default {
  name: 'MaterialCategory',
  components: {
    addMaterialCategoryDialog
  },
  mixins: [exportExcel],
  data() {
    return {
      importLink: '',
      supplierId: '',
      tableData: [],
      isLoading: false, // 刷新数据
      searchFormSetting: {
        name: {
          type: 'input',
          value: '',
          label: '分类名称',
          placeholder: '请输入分类名称',
          maxlength: 20
        }
      },
      tableSettings: [
        { label: '分类名称', key: 'name' },
        { label: '修改时间', key: 'update_time' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right" }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      dialogType: '', // 弹窗类型
      dialogVisible: false,
      dialogTitle: '新建分类',
      dialogWidth: '740px',
      dialogInfo: {},
      // 导入的弹窗数据
      importDialogTitle: '导入物资',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/导入分类.xlsx',
      importHeaderLen: 2,
      importApi: 'apiBackgroundDrpMaterailClassificationImportMaterailClassificationPost',
      previewSrcList: [],
      showViewer: false
    }
  },
  computed: {
    imageIndex() {
      let previewIndex = 0;
      const initialIndex = this.initialIndex;
      if (initialIndex >= 0) {
        previewIndex = initialIndex;
        return previewIndex;
      }
      const srcIndex = this.previewSrcList.indexOf(this.src);
      if (srcIndex >= 0) {
        previewIndex = srcIndex;
        return previewIndex;
      }
      return previewIndex;
    },
    preview() {
      const { previewSrcList } = this;
      return Array.isArray(previewSrcList) && previewSrcList.length > 0;
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMaterialWarehouseList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getMaterialWarehouseList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getMaterialWarehouseList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDrpMaterailClassificationListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        if (res.data.results) {
          this.tableData = res.data.results.map(v => {
            if (v.supplier_manage_name_list) {
              v.supplier_manage_name = v.supplier_manage_name_list.join('，')
            }
            return v
          })
        }
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    mulOperation(type, data) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const res = await this.$apis.apiBackgroundDrpMaterailClassificationDeletePost({
              id: data.id
            })
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getMaterialWarehouseList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    // 删除事件
    async deleteHandle(ids) {
      const res = await this.$apis.apiBackgroundDrpMaterialsDeletePost({
        ids
      })
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getMaterialWarehouseList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getMaterialWarehouseList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    openDialogHandle(type, data) {
      this.dialogType = type
      if (type === 'add') {
        this.dialogTitle = '新增分类'
        this.dialogInfo = {}
      } else {
        this.dialogTitle = '编辑分类'
        this.dialogInfo = data
      }
      this.dialogVisible = true
    },
    openImport(type) {
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    clickViewerHandler(row) {
      // don't show viewer when preview is false
      this.previewSrcList = row.material_feature_list || []
      this.showViewer = true;
    },
    closeViewer() {
      this.showViewer = false;
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
