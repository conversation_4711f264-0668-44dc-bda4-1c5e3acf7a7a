<template>
  <div class="AddOrEditJiaoFei container-wrapper">
    <div class="table-wrapper">
      <div class="table-header" style="display: flex; justify-content: space-between;">
        <div class="table-title">{{type==='add'?'新建':'编辑'}}缴费设置</div>
      </div>
      <el-form
        v-loading="isLoading"
        :model="jiaofeiInfo"
        ref="deviceForm"
        :rules="dejiaofeiFormRule"
        style="padding: 0 25px;"
        label-width="100px"
      >
        <el-form-item label="缴费名称" prop="jiaofeiName">
          <el-input v-model="jiaofeiInfo.jiaofeiName" max="20" :disabled="progress==='starting'" class="ps-input w-300"></el-input>
        </el-form-item>
        <el-form-item label="缴费类别" prop="jiaofeiType">
          <el-select v-model="jiaofeiInfo.jiaofeiType" :disabled="progress==='starting'" class="ps-select w-300">
            <el-option
              v-for="item in jiaofeiTypeList"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="缴费金额" prop="jiaofeiFee">
          <el-radio-group v-model="jiaofeiInfo.jiaofeiFeeType" @change="clearForm">
            <el-radio :label="'batch'" :disabled="progress==='starting'" class="ps-radio">批量金额</el-radio>
            <el-radio :label="'fixed'" :disabled="progress==='starting'" class="ps-radio">
              <span>固定金额</span>
              <el-input ref="jiaofeiFeeInput" v-if="jiaofeiInfo.jiaofeiFeeType==='fixed'" v-model="jiaofeiInfo.jiaofeiFee" :disabled="progress==='starting'" class="w-100 margin-l-r-10"></el-input>
              <span v-if="jiaofeiInfo.jiaofeiFeeType==='fixed'" class="fee-tips">单位上限为‘万’，限制到小数点后两位</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="flex">
          <el-form-item label="开始时间" prop="jiaofeiStartDate">
            <el-date-picker
              v-model="jiaofeiInfo.jiaofeiStartDate"
              :disabled="type==='edit'"
              type="datetime"
              :picker-options="pickerOptions"
              placeholder="选择开始时间"
              value-format="yyyy-MM-dd HH:mm:00"
              format="yyyy-MM-dd HH:mm"
              class="ps-picker w-300">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="截止时间" prop="jiaofeiEndDate">
            <el-date-picker
              v-model="jiaofeiInfo.jiaofeiEndDate"
              type="datetime"
              :picker-options="pickerOptions"
              placeholder="选择截止时间"
              value-format="yyyy-MM-dd HH:mm:59"
              format="yyyy-MM-dd HH:mm"
              class="ps-picker w-300">
            </el-date-picker>
          </el-form-item>
        </div>
        <el-form-item label="允许退款" prop="canRefund">
          <el-radio-group v-model="jiaofeiInfo.canRefund" @change="canRefundChange">
            <el-radio :label="true" class="ps-radio">是</el-radio>
            <el-radio :label="false" class="ps-radio">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <!--添加车辆管理自动退费-->
        <el-form-item label="自动退费" prop="autoRefund" v-if="jiaofeiInfo.canRefund">
          <el-switch
            v-model="jiaofeiInfo.autoRefund"
            active-color="#ff9b45"
            inactive-color="#ffcda2">
         </el-switch>
         <span class="fee-tips">开启自动退费功能后，用户缴费成功将自动发起缴费退款。</span>
        </el-form-item>
        <!-- 固定金额表格 -->
        <el-form-item label="缴费人员" prop="jiaofeiPersonList">
          <el-button v-if="jiaofeiInfo.jiaofeiFeeType==='fixed'" type="primary" size="mini" class="ps-origin-btn" :disabled="progress==='starting'" @click="openChooseDialog">去选择</el-button>
          <button-icon v-if="jiaofeiInfo.jiaofeiFeeType==='batch'" class="margin-l-r-5" color="plain" type="Import" @click="importSubsidy" :disabled="progress==='starting'">导入缴费</button-icon>
          <!-- <el-button type="primary" size="mini" class="ps-plain-btn" @click="delOperation('mul')">清空</el-button> -->
          <span class="margin-l-20">已选人数：{{jiaofeiPersonList.length}}人</span>
          <div>
            <el-table :data="jiaofeiPersonList" max-height="400" header-row-class-name="ps-table-header-row">
              <el-table-column prop="department_group_name" label="部门" align="center"></el-table-column>
              <el-table-column prop="card_user_group_alias" label="分组" align="center"></el-table-column>
              <el-table-column prop="name" label="姓名" align="center"></el-table-column>
              <el-table-column prop="gender_name" label="性别" align="center"></el-table-column>
              <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
              <el-table-column prop="card_no" label="卡号" align="center"></el-table-column>
              <el-table-column prop="money" label="缴费金额（元）" align="center" v-if="jiaofeiInfo.jiaofeiFeeType==='batch'" ></el-table-column>
              <el-table-column width="180" label="操作" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    class="ps-warn"
                    :disabled="progress==='starting'"
                    @click="delOperation('del', scope.$index)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" class="ps-origin-btn w-150" :disabled="isLoading" @click="checkForm">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
    <jiao-fei-dialog
      :isshow.sync="dialogVisible"
      :title="dialogTitle"
      :type="dialogType"
      :width="dialogWidth"
      :person-list="jiaofeiPersonList"
      @confirmPerson="confirmPerson"/>
      <!-- 导入数据的弹窗 start -->
      <import-dialog-drawer
        :templateUrl="templateUrl"
        :tableSetting="tableSetting"
        :show.sync="importShowDialog"
        :loading.sync="dialogLoading"
        :title="importDialogTitle"
        :openExcelType="openExcelType"
        importType=""
        @confirm="confirmImportData"
        :isShowCustomWarn="false"
        :warnTip="warnTip"
      ></import-dialog-drawer>
      <!-- 导入数据的弹窗 end -->
  </div>
</template>

<script>
import { divide, times } from '@/utils';
import JiaoFeiDialog from './components/JiaoFeiDialog.vue'
export default {
  name: 'AddOrEditJiaoFei',
  components: { JiaoFeiDialog },
  // mixins: [exportExcel],
  data() {
    let validataFee = (rule, value, callback) => {
      if (value) {
        let reg = /^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else if (Number(value) >= 99999.99) {
          callback(new Error('金额单位上限为‘万’'))
        } else if (Number(value) <= 0.00) {
          callback(new Error('缴费金额不可低于0.00元'))
        } else {
          callback()
        }
      } else if (this.jiaofeiFeeType === 'batch') {
        callback(new Error('请输入金额'))
      } else {
        callback()
      }
    }
    let validataStartDate = (rule, value, callback) => {
      if (value) {
        let now = new Date().getTime()
        let start = new Date(this.jiaofeiInfo.jiaofeiStartDate).getTime()
        if (this.type === 'add' && now > start) {
          callback(new Error('开始时间不能早于当前时间'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请选择开始时间'))
      }
    }
    let validataEndDate = (rule, value, callback) => {
      if (value) {
        let now = new Date().getTime()
        let start = new Date(this.jiaofeiInfo.jiaofeiStartDate).getTime()
        let end = new Date(this.jiaofeiInfo.jiaofeiEndDate).getTime()
        if (now > end) {
          callback(new Error('截止时间需大于当前时间'))
        } else if (start >= end) {
          callback(new Error('截止时间需大于开始时间'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请选择截止时间'))
      }
    }
    return {
      isLoading: false, // 刷新数据
      type: 'add',
      progress: '',
      jiaofeiId: '',
      jiaofeiInfo: {
        jiaofeiName: '',
        jiaofeiType: '',
        jiaofeiFee: '',
        jiaofeiStartDate: '',
        jiaofeiEndDate: '',
        canRefund: '',
        autoRefund: false,
        jiaofeiFeeType: 'fixed'
      },
      dejiaofeiFormRule: {
        jiaofeiName: [{ required: true, message: '请输入缴费名称', trigger: 'blur' }],
        jiaofeiType: [{ required: true, message: '请选择缴费类别', trigger: 'change' }],
        jiaofeiFee: [{ required: true, validator: validataFee, trigger: "blur" }],
        jiaofeiStartDate: [{ required: true, validator: validataStartDate, trigger: 'change' }],
        jiaofeiEndDate: [{ required: true, validator: validataEndDate, trigger: 'change' }],
        canRefund: [{ required: true, message: '请选择是否允许退款', trigger: 'change' }]
      },
      jiaofeiTypeList: [],
      jiaofeiPersonList: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < (Date.now() - 24 * 60 * 60 * 1000);
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      dialogWidth: '',
      // 控制输入框的显示
      inputShow: false,
      // 导入的弹窗数据
      importDialogTitle: '',
      importShowDialog: false,
      templateUrl: '',
      openExcelType: '',
      tableSetting: [],
      oldJiaofeiPersonList: [], // 暂存编辑页的jiaofeiPersonList
      dialogLoading: false,
      warnTip: '注意：需要按照平台提供的数据模板进行导入！导入前，请检查导入的格式和内容，重复人员默认取第一条数据进行导入。'
    }
  },
  created() {
    if (this.$route.query.id) {
      this.jiaofeiId = this.$route.query.id
      this.getJiaoFeiInfo()
    }
    if (this.$route.params.type) {
      this.type = this.$route.params.type
    }
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getJiaoFeiType()
    },
    checkForm() {
      this.$refs.deviceForm.validate(valid => {
        if (valid) {
          if (!this.jiaofeiPersonList.length) {
            return this.$message.error('请选择缴费人员')
          }
          let userList = []
          if (this.jiaofeiInfo.jiaofeiFeeType === 'batch') {
            this.jiaofeiPersonList.map(item => { userList.push({ person_no: item.person_no, money: times(item.money) }) })
          } else {
            this.jiaofeiPersonList.map(item => { userList.push({ person_no: item.person_no }) })
          }
          let params = {
            name: this.jiaofeiInfo.jiaofeiName,
            jiaofei_type: this.jiaofeiInfo.jiaofeiType,
            start_time: this.jiaofeiInfo.jiaofeiStartDate,
            end_time: this.jiaofeiInfo.jiaofeiEndDate,
            can_refund: this.jiaofeiInfo.canRefund,
            user_list: userList,
            auto_refund: this.jiaofeiInfo.autoRefund,
            jiaofei_fee_type: this.jiaofeiInfo.jiaofeiFeeType
          }
          if (this.jiaofeiInfo.jiaofeiFeeType === 'fixed') {
            params.jiaofei_fee = times(this.jiaofeiInfo.jiaofeiFee)
          }
          let api
          if (this.type === 'add') {
            api = this.$apis.apiBackgroundJiaofeiJiaofeiSettingAddPost
          } else {
            params.id = this.jiaofeiId
            api = this.$apis.apiBackgroundJiaofeiJiaofeiSettingModifyPost
          }
          this.saveSetting(params, api)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    async saveSetting(params, api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api(params)
      this.isLoading = false
      if (res.code === 0) {
        // 判断是否返回url
        if (res.data.url) {
          window.open(res.data.url)
          this.$message.error('导入的数据有误，请检查')
        } else {
          this.$message.success('保存成功')
          this.$closeCurrentTab(this.$route.path)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    openChooseDialog() {
      this.dialogVisible = true
      this.dialogTitle = '选择缴费人员'
      this.dialogType = 'choosePerson'
      this.dialogWidth = '850px'
    },
    delOperation(type, index) {
      if (type === 'del') {
        this.jiaofeiPersonList.splice(index, 1)
      }
    },
    confirmPerson(val) {
      val.map(item => {
        item.department_group_name = item.card_department_group_alias
        item.gender_name = item.gender_alias
      })
      this.jiaofeiPersonList = val
      // 暂存已选人员名单
      this.oldJiaofeiPersonList = val
    },
    async getJiaoFeiInfo() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundJiaofeiJiaofeiSettingGetJiaofeiSettingInfoPost({
        id: Number(this.jiaofeiId)
      })
      this.isLoading = false
      if (res.code === 0) {
        this.jiaofeiInfo.jiaofeiFeeType = res.data.jiaofei_fee_type
        this.jiaofeiInfo.jiaofeiName = res.data.name
        this.jiaofeiInfo.jiaofeiType = res.data.jiaofei_type
        this.jiaofeiInfo.jiaofeiFee = divide(res.data.jiaofei_fee)
        this.jiaofeiInfo.jiaofeiStartDate = res.data.start_time
        this.jiaofeiInfo.jiaofeiEndDate = res.data.end_time
        this.jiaofeiInfo.canRefund = res.data.can_refund
        this.jiaofeiInfo.autoRefund = res.data.auto_refund
        res.data.card_users.map(item => {
          item.money = divide(item.money)
        })
        this.jiaofeiPersonList = res.data.card_users
        // 存入编辑页已有的人员名单
        this.oldJiaofeiPersonList = res.data.card_users
        this.progress = res.data.jiaofei_progress
      } else {
        this.$message.error(res.msg)
      }
    },
    async getJiaoFeiType() {
      const res = await this.$apis.apiBackgroundBaseMenuGetJiaofeiTypePost()
      if (res.code === 0) {
        let jiaofeiTypeList = []
        for (let key in res.data) {
          jiaofeiTypeList.push({
            key,
            name: res.data[key]
          })
        }
        this.jiaofeiTypeList = jiaofeiTypeList
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 监听允许退款
     * @param {*} value
     */
    canRefundChange(value) {
      if (!value) {
        this.jiaofeiInfo.autoRefund = false
      }
    },
    // 点击切换后清空表单
    clearForm() {
      this.jiaofeiPersonList = []
      if (this.type === 'add' && this.jiaofeiInfo.jiaofeiFeeType === 'fixed') {
        this.$refs.jiaofeiFeeInput.clear()
        // 先获取焦点再失去焦点清除提示
        this.$refs.jiaofeiFeeInput.focus()
        this.$refs.jiaofeiFeeInput.blur()
        this.jiaofeiPersonList = this.oldJiaofeiPersonList
      } else {
        // 如果是编辑页，切换后需保存原有的数据
        switch (this.jiaofeiInfo.jiaofeiFeeType) {
          case 'fixed':
            this.jiaofeiPersonList = this.oldJiaofeiPersonList
            break
          case 'batch':
            break
        }
      }
    },
    importSubsidy() {
      this.importDialogTitle = '导入人员'
      this.templateUrl = location.origin + '/api/temporary/template_excel/缴费模板/缴费中心导入缴费表.xlsx'
      this.openExcelType = 'BatchAmount'
      this.tableSetting = [
        { key: 'department_group_name', label: '部门' },
        { key: 'card_user_group_alias', label: '分组' },
        { key: 'name', label: '姓名' },
        { key: 'gender_name', label: '性别' },
        { key: 'person_no', label: '人员编号' },
        { key: 'card_no', label: '卡号' },
        { key: 'money', label: '缴费金额（元）' }
      ]
      this.importShowDialog = true
    },
    confirmImportData(data) {
      this.importShowDialog = false
      let userList = data.allData
      let flag = false
      this.jiaofeiPersonList = userList.map(item => {
        if ('money' in item) {
          let obj = {
            department_group_name: item.department_group_name,
            gender_name: item.gender_name,
            person_no: item.person_no,
            name: item.name,
            card_no: item.card_no,
            money: item.money,
            card_user_group_alias: item.card_user_group_alias
          }
          return obj
        } else {
          this.$message.error('导入的表单有未填写金额的情况，请确认后重新导入')
          flag = true
          return {}
        }
      })
      if (flag) {
        this.jiaofeiPersonList = []
      }
      // this.jiaofeiPersonList = userList.map(item => {
      //   if ('money' in item) {
      //     this.jiaofeiPersonList.department_group_name = item.department_group_name
      //     this.jiaofeiPersonList.gender_name = item.gender_name
      //     this.jiaofeiPersonList.push({ person_no: item.person_no, money: times(item.money) })
      //   } else {
      //     this.$message.error('导入的表单有未填写金额的情况，请确认后重新导入')
      //     userList = []
      //   }
      // })
      // this.jiaofeiPersonList = userList
      // 做去重处理
      userList = userList.reduce((acc, item) => {
        const key = `${item.name}__${item.person_no}`;
        if (!acc.seen.has(key)) {
          acc.seen.add(key);
          acc.result.push(item);
        }
        return acc;
      }, { seen: new Set(), result: [] }).result;
      this.jiaofeiPersonList = userList
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
.AddOrEditJiaoFei{
  .flex{
    display: flex;
  }
  .fee-tips{
    margin-left: 10px;
    color: #a1a1a1;
  }
  .margin-l-20{
    margin-left: 20px;
  }
  .margin-l-r-10{
    margin: 0px 10px;
  }
  .margin-l-r-5{
    div{
      margin: 0px 5px !important;
    }
  }
}
</style>
