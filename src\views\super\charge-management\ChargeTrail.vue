<template>
  <div id="chargeTrail">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          <span class="m-r-20">数据列表</span>
          <el-checkbox v-model="selectAll" @change="selectAllList">全选</el-checkbox>
        </div>
        <div class="align-r">
          <button-icon color="origin" @click="goExport">批量导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
         <!-- :selectable="selectableHandle" -->
        <el-table ref="tableView" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row" @select="pushInSelectDataListOrNOt" @select-all="pushInSelectDataListOrNOt" :row-key="row => {return row.id}">
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #tollType="{ row }">
              <span>{{ row.toll_type === 1 ? '标准收费' : (row.toll_type === 2 ? '固定收费' : '一次性收费') }}</span>
            </template>
            <template #tollRule="{ row }">
              <span>{{  row.toll_rule_info ?  row.toll_rule_info.name : '--' }}</span>
            </template>
            <template #userScale="{ row }">
              <span>{{ row.use_user_count + '/' + row.user_scale }}</span>
            </template>
            <template #useUserRate="{ row }">
              <span>{{ row.use_user_rate }}%</span>
            </template>
            <template #dateRange="{ row }">
              <div>生效时间：{{ transformDate(row.service_start_time) }}</div>
              <div>失效时间：{{ row.service_end_time ? transformDate(row.service_end_time) : '--' }}</div>
            </template>
            <template #dueDayNum="{ row }">
              <span v-if="row.due_day_num !== -1">{{ row.due_day_num }}天</span>
              <span v-else>--</span>
            </template>
          </table-column>
        </el-table>
      </div>
    </div>
    <ul class="total m-t-10">
      <li>
        用户预警:
        <span>{{ userScaleWarningCount }}</span>
      </li>
      <li>
        到期预警:
        <span>{{ expireWarningCount }}</span>
      </li>
    </ul>
    <!-- 分页 start -->
    <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[10, 20, 50, 100, 500]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount"
        background
        class="ps-text"
        popper-class="ps-popper-select"
      ></el-pagination>
    </div>
    <!-- 分页 end -->
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { chargeTrailTableSetting } from './constants'
import * as dayjs from 'dayjs'
export default {
  name: 'ChargeTrail',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false,
      searchForm: {
        company_name: {
          label: '商户名称：',
          type: 'input',
          value: '',
          placeholder: '请输入',
          labelWidth: '100px'
        },

        project_status: {
          label: '项目状态：',
          type: 'select',
          value: '',
          labelWidth: '100px',
          placeholder: '全部',
          clearable: true,
          dataList: [
            {
              label: '待验收',
              value: 'accepting'
            },
            {
              label: '已验收',
              value: 'accepted'
            },
            {
              label: '维保中',
              value: 'maintenance'
            },
            {
              label: '其他',
              value: 'other'
            }
          ]

        },
        toll_type: {
          label: '收费模式：',
          type: 'select',
          value: '',
          labelWidth: '100px',
          placeholder: '全部',
          clearable: true,
          dataList: [
            {
              value: 1,
              label: '标准收费'
            },
            {
              value: 2,
              label: '固定收费'
            },
            {
              value: 3,
              label: '一次性收费'
            }
          ]
        },
        is_user_scale_warning: {
          label: '规模预警：',
          type: 'select',
          value: '',
          labelWidth: '100px',
          placeholder: '全部',
          clearable: true,
          dataList: [
            {
              label: '是',
              value: true
            },
            {
              label: '否',
              value: false
            }
          ]
        },
        is_expire_warning: {
          label: '到期预警：',
          type: 'select',
          value: '',
          labelWidth: '100px',
          placeholder: '全部',
          clearable: true,
          dataList: [
            {
              label: '是',
              value: true
            },
            {
              label: '否',
              value: false
            }
          ]
        }
      },
      tableData: [],
      currentTableSetting: chargeTrailTableSetting,
      page: 1,
      pageSize: 10,
      totalCount: 0,
      userScaleWarningCount: 0,
      expireWarningCount: 0,
      selectAll: false,
      selectDataList: []
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getChargeTrailList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.isLoading = true
      this.page = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.isLoading = true
      this.$refs.searchRef.resetForm()
      this.page = 1
      this.initLoad()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.isLoading = true
      this.pageSize = val
      this.page = 1
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.isLoading = true
      this.page = val
      this.initLoad()
      if (this.selectAll) {
        this.$refs.tableView.toggleAllSelection()
      }
    },
    // 格式化表单参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = dayjs(data[key].value[0]).format('YYYY-MM-DD HH:mm:ss')
            params.end_time = dayjs(data[key].value[1]).endOf('date').format('YYYY-MM-DD HH:mm:ss')
          }
        }
      }
      params.page = this.page
      params.page_size = this.pageSize
      console.log('params', params)
      return params
    },
    // 导出
    goExport() {
      let ids = []
      let option = {}
      if (this.selectAll) {
        option = {
          type: 'SuperChargeOrder',
          url: 'apiBackgroundAdminBackgroundTollListExportPost',
          params: this.formatQueryParams(this.searchForm)
        }
      } else {
        ids = this.getSelectDataId(this.selectDataList)
        option = {
          type: 'SuperChargeOrder',
          url: 'apiBackgroundAdminBackgroundTollListExportPost',
          params: {
            ids
          }
        }
      }
      this.exportHandle(option)
    },
    async getChargeTrailList() {
      this.loading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminBackgroundTollListPost(this.formatQueryParams(this.searchForm))
      )
      console.log('收费线索', res)
      if (err) {
        this.$message.error(res.msg)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.userScaleWarningCount = res.data.is_user_scale_warning_count
        this.expireWarningCount = res.data.is_expire_warning_count
        this.isLoading = false
      } else {
        this.$message.error(res.msg)
        return
      }
      this.loading = false
      this.$nextTick(() => {
        if (this.selectAll) {
          this.$refs.tableView.toggleAllSelection()
        }
      })
    },
    transformDate(date) {
      return dayjs(date).format('YYYY-MM-DD')
    },
    selectAllList() {
      // 判断当前页面是否已点击全选
      if (this.selectAll && this.selectDataList) {
        this.$refs.tableView.clearSelection()
      }
      // 勾选所有数据
      this.$refs.tableView.toggleAllSelection()
    },
    pushInSelectDataListOrNOt(selection) {
      this.selectDataList = selection
    },
    // 获取选中行的id
    getSelectDataId(dataList) {
      let idList = []
      dataList.forEach(item => {
        idList.push(item.id)
      })
      return idList
    }
  }
}
</script>

<style>
</style>
