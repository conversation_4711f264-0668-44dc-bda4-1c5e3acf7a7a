<template>
  <div class="AddMealApply container-wrapper">
    <div class="table-wrapper">
      <div class="table-header" style="display: flex; justify-content: space-between">
        <div class="table-title">{{ type === 'add' ? '新增' : '编辑' }}规则</div>
      </div>
      <el-form
        v-loading="isLoading"
        :model="mealApplyForm"
        ref="mealApplyFormRef"
        :rules="mealApplyFormRules"
        style="padding: 0 25px"
        label-width="110px"
      >
        <el-form-item label="规则名称" prop="mealApplyName">
          <el-input
            v-model="mealApplyForm.mealApplyName"
            max="20"
            placeholder="请输入规则名称"
            class="ps-input w-300"
            :disabled="type==='detail'"
          ></el-input>
        </el-form-item>
        <el-form-item size="small" label="适用消费点" prop="organizations">
          <organization-select
            class="ps-input w-300"
            placeholder="请选择适用消费点"
            :isLazy="false"
            :multiple="true"
            :check-strictly="true"
            v-model="mealApplyForm.organizations"
            :append-to-body="true"
            @change="organizationsChange"
            :disabled="type==='detail'"
          ></organization-select>
        </el-form-item>
        <el-form-item label="可申请餐段" prop="mealTypes">
          <el-checkbox-group v-model="mealApplyForm.mealTypes" @change="mealTypesChange">
            <el-checkbox
              v-for="mt in mealTypeList"
              :label="mt.value"
              :key="mt.value"
              name="meal_types"
              class="ps-checkbox"
              :disabled="type==='detail'"
            >
              {{ mt.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="可申请的天数" prop="dayCount">
          <el-input-number v-model="mealApplyForm.dayCount" :disabled="type==='detail'" :min="1"></el-input-number>
          <!-- <el-input v-model="mealApplyForm.dayCount" class="ps-input w-300"></el-input> -->
        </el-form-item>
        <el-form-item>
          <div class="ship-box clearfix">
            <div class="ship-box-item float-l m-r-30">
              跳过节假日：<el-switch v-model="mealApplyForm.skipHoliday" active-color="#ff9b45"></el-switch>
            </div>
            <div class="ship-box-item float-l m-r-30">
              跳过周末：<el-switch v-model="mealApplyForm.skipWeekends" active-color="#ff9b45"></el-switch>
            </div>
            <div class="ship-box-item float-l m-r-30">
              跳过工作日：<el-switch v-model="mealApplyForm.skipWorkDays" active-color="#ff9b45"></el-switch>
            </div>
          </div>
          <!-- 以下日期不跳过 -->
          <div class="ship-box ">
            <div>
              以下日期不跳过：
              <el-button type="text" class="hidden-picker">
                添加
                <el-date-picker
                  type="dates"
                  :clearable="false"
                  v-model="mealApplyForm.noSkipDays"
                  placeholder="选择一个或多个日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  popper-class="hidden-picker-year"
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </el-button>
              <el-button type="text" @click="clearNoShipDay">清空</el-button>
            </div>
          </div>
          <transition-group name="el-zoom-in-center" class="no-ship" tag="div">
            <div class="no-ship-time" v-for="(item, index) in mealApplyForm.noSkipDays" :key="item">
              {{ formatNoSkipTime(item) }}
              <div class="del-time" @click="delNoSkipTime(index)">
                <i class="el-icon-error"></i>
              </div>
            </div>
          </transition-group>
        </el-form-item>
        <el-form-item label="支付方式" prop="payMethod">
          <el-radio-group v-model="mealApplyForm.payMethod" :disabled="type==='detail'">
            <el-radio label="Accounting" class="ps-radio">记账</el-radio>
            <el-radio label="PayAtSight" class="ps-radio">即付</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="mealApplyForm.payMethod === 'Accounting'"
          label="记账方式"
          prop="accountingMethod"
        >
          <el-radio-group v-model="mealApplyForm.accountingMethod" :disabled="type==='detail'">
            <el-radio label="GD" class="ps-radio">固定金额</el-radio>
            <el-radio label="JZ" class="ps-radio">按实际记账</el-radio>
            <!-- <el-radio label="JC" class="ps-radio">记次</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="
            (mealApplyForm.payMethod === 'Accounting' && mealApplyForm.accountingMethod === 'GD') ||
            mealApplyForm.payMethod === 'PayAtSight'
          "
          prop="accountingMethod"
        >
          <div style="display: flex;">
            <el-table
              :data="mealApplyForm.mealTableData"
              ref="tableData"
              style="width: 100%"
              stripe
              header-row-class-name="ps-table-header-row"
            >
              <el-table-column
                v-for="item in mealApplyForm.mealTableSetting"
                :key="item.key"
                :prop="item.key"
                :label="item.label"
                align="center"
              >
                <template slot-scope="scope">
                  <el-form-item v-if="item.key !== 'org'" :prop="'mealTableData.'+scope.$index+'.'+item.key" :rules="mealApplyFormRules.mealPrice" class="table-form">
                    <el-input v-model="scope.row[item.key]" :disabled="type==='detail'" class="ps-input" size="mini"></el-input>
                  </el-form-item>
                  <span v-else>{{ scope.row.org }}</span>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 50px;">
              <div style="margin-top: 26px;" v-for="item in mealApplyForm.mealTableData.length" :key="item">人/次</div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-if="mealApplyForm.payMethod === 'Accounting' && mealApplyForm.accountingMethod === 'JZ'">
          <el-radio-group v-model="mealApplyForm.actualAccountingType" @change="changeActualAccountingType" :disabled="type==='detail'">
            <el-radio label="limitless" class="ps-radio radio-block">无额度限制</el-radio>
            <el-radio label="meal" class="ps-radio radio-block">
              <template>
                <span>单餐/人</span>
                <el-form-item class="form-content-inline" :prop="mealApplyForm.actualAccountingType === 'meal' ? 'actualOneMealPrice' : 'price'">
                  <el-input v-model="mealApplyForm.actualOneMealPrice" :disabled="type==='detail'" class="ps-input w-300"></el-input>
                </el-form-item>
                <span>元</span>
              </template>
            </el-radio>
            <el-radio label="day" class="ps-radio radio-block">
              <template>
                <span>每天/人</span>
                <el-form-item class="form-content-inline" :prop="mealApplyForm.actualAccountingType === 'day' ? 'actualAllDay' : 'price'">
                  <el-input v-model="mealApplyForm.actualAllDay" :disabled="type==='detail'" class="ps-input w-300"></el-input>
                </el-form-item>
                <span>元</span>
              </template>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- v-if="mealApplyForm.payMethod === 'Accounting' && (mealApplyForm.accountingMethod === 'GD' || mealApplyForm.accountingMethod === 'JZ' || mealApplyForm.accountingMethod === 'JC')" -->
        <el-form-item
          label="消费次数限制">
          <el-radio-group v-model="mealApplyForm.consumptionCountType" @change="changeConsumptionCountType" :disabled="type==='detail'">
            <el-radio label="limitless" class="ps-radio radio-block">无限制</el-radio>
            <el-radio label="meal" class="ps-radio radio-block">
              <template>
                <span>单餐/人</span>
                <el-form-item
                  class="form-content-inline"
                  :prop="mealApplyForm.consumptionCountType === 'meal'  ? 'consumptionOneMeal' : 'price'">
                  <el-input v-model="mealApplyForm.consumptionOneMeal" :disabled="type==='detail'" class="ps-input w-300"></el-input>
                </el-form-item>
                <span>次</span>
              </template>
            </el-radio>
            <el-radio label="day" class="ps-radio radio-block">
              <template>
                <span>每天/人</span>
                <el-form-item class="form-content-inline" :prop="mealApplyForm.consumptionCountType === 'day'  ? 'consumptionAllDay' : 'price'">
                  <el-input v-model="mealApplyForm.consumptionAllDay" :disabled="type==='detail'" class="ps-input w-300"></el-input>
                </el-form-item>
                <span>次</span>
              </template>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="申请类型" required>
          <el-form-item
            v-for="(item,index) in mealApplyForm.applyTypes"
            :key="'applyTypes'+index"
            :prop="`applyTypes[${index}]`"
            :rules="mealApplyFormRules.applyTypes"
            class="apply-types">
            <el-input v-model="mealApplyForm.applyTypes[index]" :disabled="!index || type==='detail'" max="20" class="ps-input w-300"></el-input>
            <img src="@/assets/img/plus.png" alt="" v-if="mealApplyForm.applyTypes.length < 30" @click="addApplyTypes">
            <img src="@/assets/img/reduce.png" alt="" v-if="index > 0" @click="delApplyTypes(index)">
          </el-form-item>
        </el-form-item>
        <el-form-item label="备注是否必填">
          <el-switch v-model="mealApplyForm.remarkRequired" :disabled="type==='detail'" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <el-form-item label="是否审批">
          <el-switch v-model="mealApplyForm.isApprove" :disabled="type==='detail'" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <div style="margin-bottom: 30px;" v-if="type!=='detail'">
          <el-button style="width: 120px;" :disabled="isLoading" @click="checkForm('disable')">保存</el-button>
          <el-button utton class="ps-origin-btn" style="width: 120px;" type="primary" :disabled="isLoading" @click="checkForm('enable')">保存并开启</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import OrganizationSelect from '@/components/OrganizationSelect'
import { MEAL_TYPES } from '@/utils/constants'
import { deepClone, times, getTreeDeepLabelList, divide } from '@/utils'
export default {
  name: 'AddApproveRules',
  components: { OrganizationSelect },
  // mixins: [activatedLoadData],
  data() {
    let validataPrice = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('金额不能为空'))
      } else {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      }
    };
    let validataNum = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('输入不能为空'))
      } else {
        let reg = /^[1-9][0-9]*$/
        if (!reg.test(value)) {
          callback(new Error('输入格式有误'))
        } else {
          callback()
        }
      }
    };
    let validataConsumptionOneMeal = (rule, value, callback) => {
      let reg
      if (this.mealApplyForm.accountingMethod === 'JZ') {
        reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      } else {
        reg = /^[1-9][0-9]*$/
      }
      if (!value) {
        return callback(new Error('输入不能为空'))
      } else {
        if (!reg.test(value)) {
          callback(new Error('输入格式有误'))
        } else {
          callback()
        }
      }
    };
    return {
      isLoading: false, // 刷新数据
      mealApplyData: '',
      userType: '',
      type: 'add',
      progress: '',
      mealApplyId: '',
      mealApplyForm: {
        mealApplyName: '',
        organizations: [],
        oldOrganizations: [],
        mealTypes: [],
        dayCount: '',
        skipHoliday: false, // 跳过节假日
        skipWeekends: false, // 跳过周末
        skipWorkDays: false, // 跳过工作日
        noSkipDays: [], // 不跳过的日期
        payMethod: 'Accounting',
        accountingMethod: 'GD',
        mealTableData: [],
        mealTableSetting: [{ label: '消费点', key: 'org' }],
        actualAccountingType: 'limitless',
        actualOneMealPrice: '',
        actualAllDay: '',
        consumptionCountType: 'limitless',
        consumptionOneMeal: '',
        consumptionAllDay: '',
        applyTypes: ['其他'],
        remarkRequired: false,
        isApprove: false
      },
      mealApplyFormRules: {
        mealApplyName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
        organizations: [{ required: true, message: '请选择适用消费点', trigger: 'blur' }],
        mealTypes: [{ required: true, message: '请选择可申请餐段', trigger: 'blur' }],
        dayCount: [{ required: true, message: '请输入可申请的天数', trigger: 'blur' }],
        payMethod: [{ required: true, message: '请选择支付方式', trigger: 'blur' }],
        accountingMethod: [{ required: true, message: '请选择支付方式', trigger: 'blur' }],
        mealPrice: [{ validator: validataPrice, trigger: 'blur' }],
        actualOneMealPrice: [{ validator: validataPrice, trigger: 'blur' }],
        actualAllDay: [{ validator: validataPrice, trigger: 'blur' }],
        consumptionOneMeal: [{ validator: validataNum, trigger: 'blur' }],
        consumptionAllDay: [{ validator: validataConsumptionOneMeal, trigger: 'blur' }],
        applyTypes: [{ required: true, message: '请选择申请类型', trigger: 'blur' }]
      },
      mealTypeList: MEAL_TYPES,
      organizationList: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
    }
  },
  created() {
    if (this.$route.query.data) {
      this.mealApplyData = JSON.parse(this.$route.query.data)
    }
    if (this.$route.query.userType) {
      this.userType = this.$route.query.userType
    }
    if (this.$route.query.type) {
      this.type = this.$route.query.type
    }
    this.initLoad()
  },
  mounted() {
    this.$eventBus.$on('form-data-save', val => {
      if (this.type === 'add' || this.type === 'edit') {
        console.log(666)
        this.$confirm(`信息未保存，是否关闭？`, {
          confirmButtonText: '保存并关闭',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              this.checkForm()
              done()
              instance.confirmButtonLoading = false
            } else {
              if (!instance.confirmButtonLoading) {
                this.$closeCurrentTab(this.$route.path)
              }
              done()
            }
          }
        })
          .then(e => {
          })
          .catch(e => {})
      }
    })
  },
  watch: {
    mealApplyForm: {
      handler(newValue) {
        console.log(6666)
        this.$store.dispatch('settings/setFormData', this.mealApplyForm)
      },
      deep: true
    }
  },
  methods: {
    initLoad() {
      if (this.type === 'edit' || this.type === 'detail') {
        this.initData()
      }
    },
    async initData() {
      await this.getOrganizationList()
      this.mealApplyForm.mealApplyName = this.mealApplyData.name
      this.mealApplyForm.organizations = deepClone(this.mealApplyData.rule.org_ids)
      this.mealApplyForm.oldOrganizations = deepClone(this.mealApplyData.rule.org_ids)
      this.mealApplyForm.mealTypes = this.mealApplyData.rule.meal_type_list
      this.mealApplyForm.dayCount = this.mealApplyData.can_approve_day
      this.mealApplyForm.skipHoliday = this.mealApplyData.skip_holiday ? true : false
      this.mealApplyForm.skipWeekends = this.mealApplyData.skip_weekends ? true : false
      this.mealApplyForm.skipWorkDays = this.mealApplyData.skip_work_days ? true : false
      this.mealApplyForm.noSkipDays = this.mealApplyData.no_skip_days
      this.mealApplyForm.payMethod = this.mealApplyData.rule.pay_method
      this.mealApplyForm.applyTypes = this.mealApplyData.rule.apply_type_list
      this.mealApplyForm.remarkRequired = !!this.mealApplyData.need_remark
      this.mealApplyForm.isApprove = !!this.mealApplyData.need_approve
      let keys = Object.keys(this.mealApplyData.rule)
      if (keys.indexOf('GD') !== -1) {}
      if (keys.indexOf('GD') !== -1) {
        this.mealApplyForm.accountingMethod = 'GD'
      }
      if (keys.indexOf('JZ') !== -1) {
        this.mealApplyForm.accountingMethod = 'JZ'
      }
      if (keys.indexOf('JC') !== -1) {
        this.mealApplyForm.accountingMethod = 'JC'
      }
      // 消费次数限制
      // if (this.mealApplyForm.accountingMethod === 'GD' || this.mealApplyForm.accountingMethod === 'JZ' || this.mealApplyForm.accountingMethod === 'JC') {
      this.mealApplyForm.consumptionCountType = this.mealApplyData.rule.limit?.type ? this.mealApplyData.rule.limit.type : 'limitless'
      if (this.mealApplyForm.consumptionCountType === 'meal') {
        this.mealApplyForm.consumptionOneMeal = this.mealApplyData.rule.limit.num
      } else if (this.mealApplyForm.consumptionCountType === 'day') {
        this.mealApplyForm.consumptionAllDay = this.mealApplyData.rule.limit.num
      }
      // }
      if (this.mealApplyForm.accountingMethod === 'JZ') {
        this.mealApplyForm.actualAccountingType = this.mealApplyData.rule.JZ.type ? this.mealApplyData.rule.JZ.type : 'limitless'
        if (this.mealApplyForm.actualAccountingType === 'meal') {
          this.mealApplyForm.actualOneMealPrice = divide(this.mealApplyData.rule.JZ.fee)
        } else if (this.mealApplyForm.actualAccountingType === 'day') {
          this.mealApplyForm.actualAllDay = divide(this.mealApplyData.rule.JZ.fee)
        }
      }
      // 初始化表格表头
      this.mealTypeList.map(meal => {
        if (this.mealApplyForm.mealTypes.indexOf(meal.value) !== -1) {
          this.mealApplyForm.mealTableSetting.push({
            label: meal.label,
            key: meal.value
          })
        }
      })
      // 初始化表格组织
      let infoList = keys.indexOf('GD') !== -1 ? this.mealApplyData.rule.GD : this.mealApplyForm.payMethod === 'PayAtSight' ? this.mealApplyData.rule.PayAtSight : []
      this.mealApplyData.rule.org_ids.map(item => {
        let info = {}
        if (infoList.length) {
          infoList.map(infoItem => {
            if (infoItem.org_id === item) {
              info = infoItem
            }
          })
        }
        let name = getTreeDeepLabelList(this.organizationList, [item], 'name', 'id', 'children_list')
        // this.mealApplyForm.mealTableData.push({
        //   orgId: item,
        //   org: name.length ? name[0] : '',
        //   breakfast: info.breakfast ? divide(info.breakfast) : '',
        //   lunch: info.lunch ? divide(info.lunch) : '',
        //   afternoon: info.afternoon ? divide(info.afternoon) : '',
        //   dinner: info.dinner ? divide(info.dinner) : '',
        //   supper: info.supper ? divide(info.supper) : '',
        //   morning: info.morning ? divide(info.morning) : ''
        // })
        this.mealApplyForm.mealTableData.push({
          orgId: item,
          org: name.length ? name[0] : '',
          breakfast: divide(info.breakfast),
          lunch: divide(info.lunch),
          afternoon: divide(info.afternoon),
          dinner: divide(info.dinner),
          supper: divide(info.supper),
          morning: divide(info.morning)
        })
      })
    },
    formateMealTable(key) {
      this.mealApplyData.rule[key].map(item => {
        let name = getTreeDeepLabelList(this.organizationList, [item.org_id], 'name', 'id', 'children_list')
        this.mealApplyForm.mealTableData.push({
          orgId: item.org_id,
          org: name[0],
          breakfast: divide(item.breakfast),
          lunch: divide(item.lunch),
          afternoon: divide(item.afternoon),
          dinner: divide(item.dinner),
          supper: divide(item.supper),
          morning: divide(item.morning)
        })
      })
    },
    formateParams() {
      let PayAtSight = []
      this.mealApplyForm.mealTableData.map(item => {
        let info = {
          org_id: item.orgId
        }
        this.mealApplyForm.mealTypes.map(meal => {
          info[meal] = times(Number(item[meal]))
        })
        PayAtSight.push(info)
      })
      return PayAtSight
    },
    checkForm(status) {
      this.$refs.mealApplyFormRef.validate(valid => {
        if (valid) {
          let params = {
            name: this.mealApplyForm.mealApplyName,
            user_type: this.userType,
            can_approve_day: this.mealApplyForm.dayCount,
            skip_holiday: this.mealApplyForm.skipHoliday ? 1: 0,
            skip_weekends: this.mealApplyForm.skipWeekends ? 1: 0,
            skip_work_days: this.mealApplyForm.skipWorkDays ? 1: 0,
            no_skip_days: this.mealApplyForm.noSkipDays,
            pay_method: this.mealApplyForm.payMethod,
            need_approve: this.mealApplyForm.isApprove ? 1 : 0,
            need_remark: this.mealApplyForm.remarkRequired ? 1 : 0,
            status,
            rule: {
              org_ids: this.mealApplyForm.organizations,
              meal_type_list: this.mealApplyForm.mealTypes,
              pay_method: this.mealApplyForm.payMethod,
              apply_type_list: this.mealApplyForm.applyTypes
            }
          }
          // 消费次数限制
          // if (this.mealApplyForm.accountingMethod === 'GD' || this.mealApplyForm.accountingMethod === 'JZ' || this.mealApplyForm.accountingMethod === 'JC') {
          let limit = {
            type: this.mealApplyForm.consumptionCountType
          }
          if (this.mealApplyForm.consumptionCountType === 'meal') {
            limit.num = this.mealApplyForm.consumptionOneMeal
          } else if (this.mealApplyForm.consumptionCountType === 'day') {
            limit.num = this.mealApplyForm.consumptionAllDay
          }
          if (this.mealApplyForm.accountingMethod === 'JC') {
            params.rule.JC = limit
          } else {
            params.rule.limit = limit
          }
          // }
          if (this.mealApplyForm.payMethod === 'PayAtSight') {
            params.rule.PayAtSight = this.formateParams()
          } else if (this.mealApplyForm.payMethod === 'Accounting') {
            params.rule.consume_type = this.mealApplyForm.accountingMethod
            if (this.mealApplyForm.accountingMethod === 'GD') {
              params.rule.GD = this.formateParams()
            }
            if (this.mealApplyForm.accountingMethod === 'JZ') {
              let limit = {
                type: this.mealApplyForm.actualAccountingType
              }
              if (this.mealApplyForm.actualAccountingType === 'meal') {
                limit.fee = times(this.mealApplyForm.actualOneMealPrice)
              } else if (this.mealApplyForm.actualAccountingType === 'day') {
                limit.fee = times(this.mealApplyForm.actualAllDay)
              }
              params.rule.JZ = limit
            }
          }
          let api
          if (this.type === 'add') {
            api = this.$apis.apiBackgroundApproveApproveOrderRuleAddPost
          } else if (this.type === 'edit') {
            params.id = this.mealApplyData.id
            api = this.$apis.apiBackgroundApproveApproveOrderRuleModifyPost
          }
          this.saveSetting(params, api)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    async saveSetting(params, api) {
      this.isLoading = true
      const res = await api(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    organizationsChange(e) {
      console.log('e', e, this.mealApplyForm.oldOrganizations, this.mealApplyForm.organizations)
      // 取消选择,一般一次一个
      let cancelArr = this.mealApplyForm.oldOrganizations.filter(
        v => !this.mealApplyForm.organizations.some(item => item === v)
      )
      if (cancelArr.length) {
        let cancelIndex = -1
        this.mealApplyForm.mealTableData.map((item, index) => {
          if (item.orgId === cancelArr[0]) cancelIndex = index;
        })
        this.mealApplyForm.mealTableData.splice(cancelIndex, 1)
      }
      console.log('cancelArr', cancelArr)
      // 勾选,一般一次一个
      let selectArr = this.mealApplyForm.organizations.filter(
        v => !this.mealApplyForm.oldOrganizations.some(item => item === v)
      )
      console.log('selectArr', selectArr)
      if (selectArr.length) {
        this.mealApplyForm.mealTableData.push({
          orgId: selectArr[0],
          org: e.find(item => item.id === selectArr[0]).name,
          breakfast: '',
          lunch: '',
          afternoon: '',
          dinner: '',
          supper: '',
          morning: ''
        })
      }
      console.log(this.mealApplyForm.mealTableData)
      this.mealApplyForm.oldOrganizations = deepClone(this.mealApplyForm.organizations)
    },
    mealTypesChange() {
      this.mealApplyForm.mealTableSetting = [{ label: '消费点', key: 'org' }]
      // 挨个餐段按顺序添加
      this.mealTypeList.map(meal => {
        if (this.mealApplyForm.mealTypes.indexOf(meal.value) !== -1) {
          this.mealApplyForm.mealTableSetting.push({
            label: meal.label + '/元',
            key: meal.value
          })
        }
      })
    },
    addApplyTypes() {
      this.mealApplyForm.applyTypes.push('')
    },
    delApplyTypes(index) {
      this.mealApplyForm.applyTypes.splice(index, 1);
    },
    changeActualAccountingType() {
      this.$nextTick(() => {
        this.$refs.mealApplyFormRef.clearValidate(['actualOneMealPrice', 'actualAllDay', 'price']);
      })
    },
    changeConsumptionCountType() {
      this.$nextTick(() => {
        this.$refs.mealApplyFormRef.clearValidate(['consumptionOneMeal', 'consumptionAllDay', 'price']);
      })
    },
    // 获取所有数据
    async getOrganizationList() {
      const res = await this.$apis.apiBackgroundOrganizationOrganizationTreeListPost()
      if (res.code === 0) {
        this.organizationList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳过时间格式化
    formatNoSkipTime(date) {
      // let text = date.split('-')[0] + '月' + date.split('-')[1] + '日'
      let result = date.replace(/-/g, (result, key) => {
        let text = ''
        switch (key) {
          case 4:
            text = '年'
            break
          case 7:
            text = '月'
            break
        }
        return text
      })
      return result + '日'
    },
    // 单个删除
    delNoSkipTime(index) {
      this.mealApplyForm.noSkipDays.splice(index, 1)
    },
    // 清除全部
    clearNoShipDay() {
      this.mealApplyForm.noSkipDays = []
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
.AddMealApply {
  .table-form{
    // height: 60px;
  }
  .form-content-inline{
    display: inline-block;
    .el-form-item__content{
      display: inline-block;
    }
  }
  .radio-block{
    display: block !important;
    margin-top: 20px;
  }
  .no-ship {
    padding: 10px 0;
    .no-ship-time {
      display: inline-block;
      margin-right: 30px;
      margin-bottom: 5px;
      font-size: 15px;
      position: relative;
      .del-time {
        position: absolute;
        top: -8px;
        right: -12px;
        cursor: pointer;
      }
    }
  }
  .hidden-picker {
    position: relative;
    overflow: hidden;
    .el-date-editor {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      .el-input__inner {
        padding: 0 !important;
      }
      opacity: 0;
    }
  }
  .form-btn {
    margin: 20px 0;
  }
  .apply-types{
    margin-bottom: 20px;
    .el-form-item__content{
      display: flex;
      align-items: center;
      img {
        margin-left: 10px;
      }
    }
  }
}
</style>
