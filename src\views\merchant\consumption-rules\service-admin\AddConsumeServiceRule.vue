<template>
  <div class="add-conrules-wrapper container-wrapper circular-bead">
    <el-form
      ref="consumptionFormRef"
      v-loading="isLoading"
      :rules="formDataRule"
      :model="formData"
      class="consumption-form-wrapper ps-small-box"
      size="small"
    >
      <el-form-item label="规则名称：" prop="name" class="name-b" label-width="130px">
        <el-input
          class="ps-input"
          v-model="formData.name"
          placeholder="请输入规则名称"
          style="width: 215px"
          maxlength="15"
        ></el-input>
      </el-form-item>
      <el-form-item class="min-label-w" label="适用消费点：" prop="orgNos" label-width="130px">
        <consume-select v-model="formData.orgNos" style="width: 215px" multiple collapse-tags />
      </el-form-item>
      <el-form-item
        class="min-label-w"
        label="适用分组："
        key="groupNos"
        :prop="formData.isTourist ? '' : 'groupNos'"
        label-width="130px"
      >
        <user-group-select
          :multiple="true"
          :collapse-tags="true"
          class="ps-input"
          style="width: 215px"
          v-model="formData.groupNos"
          placeholder="请选择分组"
        ></user-group-select>
        <!-- <el-checkbox
          class="ps-checkbox p-l-20"
          v-model="formData.isTourist"
          @change="checkboxIsTourist"
        >
          适用游客
        </el-checkbox> -->
      </el-form-item>
      <el-form-item class="min-label-w" label="适用就餐方式：" prop="takeType" label-width="130px">
        <el-checkbox-group class="ps-checkbox" v-model="formData.takeType">
          <el-checkbox v-for="mt in takeTypeList" :label="mt.value" :key="mt.value" name="takeType">
            {{ mt.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item class="min-label-w" label="适用餐段：" prop="mealTime" label-width="130px">
        <el-checkbox-group class="ps-checkbox" v-model="formData.mealTime">
          <el-checkbox v-for="mt in mealType" :label="mt.value" :key="mt.value" name="mealTime">
            {{ mt.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="选择需要收取手续费的扣款方式：" prop="selectListId" class="block-label">
        <!-- table start -->
        <div class="m-t-20">
          <el-table
            :data="historytableData"
            ref="historytableData"
            style="width: 800px"
            stripe
            height="300"
            header-row-class-name="ps-table-header-row"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" class-name="ps-checkbox" width="55"></el-table-column>
            <el-table-column prop="payway_alias" label="支付渠道" align="center"></el-table-column>
            <el-table-column
              prop="sub_payway_alias"
              label="扣款钱包"
              align="center"
            ></el-table-column>
            <el-table-column show-overflow-tooltip label="手续费" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  class="ps-text"
                  v-if="
                    !scope.row.service_fee_value &&
                    formData.selectListId.length &&
                    formData.selectListId.includes(scope.row.id)
                  "
                  @click="serviceSetting(scope.row)"
                >
                  设置
                </el-button>
                <span
                  v-else-if="
                    formData.selectListId.length &&
                    formData.selectListId.includes(scope.row.id) &&
                    scope.row.service_fee_type === 1 &&
                    scope.row.service_fee_value
                  "
                  @click="serviceSetting(scope.row)"
                  class="ps-origin"
                >
                  {{ scope.row.service_fee_value }}元
                </span>
                <span
                  v-else-if="
                    formData.selectListId.length &&
                    formData.selectListId.includes(scope.row.id) &&
                    scope.row.service_fee_type === 0 &&
                    scope.row.service_fee_value
                  "
                  @click="serviceSetting(scope.row)"
                  class="ps-origin"
                >
                  {{ scope.row.service_fee_value }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- table end -->
      </el-form-item>
      <div class="footer">
        <div style="color: red" class="m-b-20">注：禁用状态才支持修改规则信息</div>
        <el-button
          class="ps-origin-plain-btn"
          :disabled="modifyData.status === 'enable' ? true : false"
          type="primary"
          @click="saveConsumHandle('stop')"
        >
          保存
        </el-button>
        <el-button
          class="ps-origin-btn"
          type="primary"
          :disabled="modifyData.status === 'enable' ? true : false"
          @click="saveConsumHandle('enable')"
        >
          保存并启用
        </el-button>
      </div>
    </el-form>
    <el-dialog
      title="手续费设置"
      :visible.sync="serviceSettingDialog"
      width="400px"
      custom-class="ps-dialog"
    >
      <el-form
        ref="serviceSettingForm"
        :rules="serviceSettingDialogRuls"
        :model="serviceSettingDialogFormData"
      >
        <div class="ps-flex">
          <el-form-item class="p-r-20">
            <el-radio
              class="ps-radio"
              v-model="serviceSettingDialogFormData.service_fee_type"
              :label="1"
            >
              定额
            </el-radio>
          </el-form-item>
          <el-form-item prop="quota" v-if="serviceSettingDialogFormData.service_fee_type !== 0">
            <div class="ps-flex">
              <el-input
                class="ps-input w-150 p-r-10"
                size="small"
                v-model="serviceSettingDialogFormData.quota"
              ></el-input>
              <span>元</span>
            </div>
            <span>实收金额=订单金额+定额</span>
          </el-form-item>
        </div>
        <div class="ps-flex">
          <el-form-item label="" class="p-r-20">
            <el-radio
              class="ps-radio"
              v-model="serviceSettingDialogFormData.service_fee_type"
              :label="0"
            >
              百分比
            </el-radio>
          </el-form-item>
          <el-form-item prop="discount" v-if="serviceSettingDialogFormData.service_fee_type !== 1">
            <div class="ps-flex">
              <el-input
                class="ps-input w-150 p-r-10"
                size="small"
                v-model="serviceSettingDialogFormData.discount"
              ></el-input>
              <span>%</span>
            </div>
            <span>实收金额=订单金额+（订单金额*百分比）</span>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="serviceSettingDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="determineServiceSettingDialog">
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { to } from '@/utils'
import UserGroupSelect from '@/components/UserGroupSelect'
import ConsumeSelect from '@/components/ConsumeSelect'
import { MEAL_TYPES } from '@/utils/constants'
import { validataPrice, validateStock } from '@/assets/js/validata'
import NP from 'number-precision'
export default {
  components: {
    UserGroupSelect,
    ConsumeSelect
  },
  data() {
    return {
      type: '',
      isLoading: false,
      historytableData: [
        {
          coupon_no: 1
        }
      ],
      mealType: MEAL_TYPES,
      // 如果 有这个设备堂食 就用这个
      // takeTypeList: TAKE_TYPE_LIST,
      takeTypeList: [
        // {
        //   label: '设备堂食',
        //   value: 'instore'
        // },
        {
          label: '预约-堂食',
          value: 'on_scene'
        },
        {
          label: '预约-食堂自取',
          value: 'bale'
        },
        {
          label: '预约-取餐柜取餐',
          value: 'cupboard'
        },
        {
          label: '预约-外卖',
          value: 'waimai'
        },
        {
          label: '报餐',
          value: 'report'
        }
      ],
      formDataRule: {
        name: [{ required: true, message: '消费规则名称不能为空', trigger: 'blur' }],
        orgNos: [{ required: true, message: '消费点不能为空', trigger: 'change' }],
        groupNos: [{ required: true, message: '适用分组不能为空', trigger: 'change' }],
        takeType: [{ required: true, message: '适用就餐方式不能为空', trigger: 'change' }],
        mealTime: [{ required: true, message: '适用餐段不能为空', trigger: 'change' }],
        selectListId: [{ required: true, message: '扣款方式不能为空', trigger: 'change' }]
      },
      formData: {
        name: '',
        orgNos: [],
        groupNos: [],
        isTourist: false,
        takeType: [],
        mealTime: [],
        selectListId: [] // table 选择id
      },
      serviceSettingDialog: false,
      serviceSettingDialogFormData: {
        service_fee_type: 1,
        quota: '',
        discount: ''
      },
      serviceSettingDialogRuls: {
        quota: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { validator: validataPrice, trigger: 'blur' }
        ],
        discount: [
          { required: true, message: '请输入折扣', trigger: 'blur' },
          { validator: validateStock, trigger: 'blur' }
        ]
      },
      serviceSettingData: {}, // 手续费
      modifyData: {}
    }
  },
  computed: {
    ...mapGetters(['organization'])
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.type = this.$route.params.type
      if (this.type === 'modify') {
        this.modifyData = this.$decodeQuery(this.$route.query.data)
        this.formData = {
          name: this.modifyData.name,
          orgNos: this.modifyData.rule_info.org_names
            ? Object.values(this.modifyData.rule_info.org_names)
            : [],
          groupNos: this.modifyData.rule_info.group_names
            ? Object.values(this.modifyData.rule_info.group_names)
            : [],
          isTourist: this.modifyData.is_tourist,
          takeType: this.modifyData.take_type,
          mealTime: this.modifyData.meal_time,
          selectListId: [] // table 选择id
        }
        console.log(this.modifyData, 22)
      }
      this.getChargeGetPayinfoList()
    },
    checkboxIsTourist() {
      if (this.formData.isTourist) {
        this.$nextTick(() => {
          // this.$refs.consumptionFormRef.clearValidate(['groupNos'])
          this.$refs.consumptionFormRef.clearValidate()
        })
      }
    },
    // 获取扣款方式
    async getChargeGetPayinfoList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeGetPayinfoListPost({
          organizations: [this.organization]
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.historytableData = res.data.map(v => {
          v.service_fee_type = 1
          v.service_fee_value = ''
          // 编辑的时候需要返现
          if (this.type === 'modify') {
            this.modifyData.rule.forEach(ruleItem => {
              if (v.id === ruleItem.payinfo) {
                // 回显table
                this.$nextTick(() => {
                  this.$refs.historytableData.toggleRowSelection(v)
                })
                v.service_fee_value = ruleItem.charge_fixed
                  ? NP.divide(ruleItem.charge_fixed, 100)
                  : ruleItem.charge_percent
                v.service_fee_type = ruleItem.charge_fixed ? 1 : 0
                this.formData.selectListId.push(ruleItem.payinfo)
              }
            })
          }
          return v
        })
        console.log(this.formData.selectListId)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增手续费规则
    async getCommissionChargeConsumeAdd(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeConsumeAddPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑手续费规则
    async getConsumeModify(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeConsumeModifyPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 列表选择
    handleSelectionChange(val) {
      this.formData.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.formData.selectListId.push(item.id)
      })
    },
    // 手续费
    serviceSetting(data) {
      this.serviceSettingDialogFormData = {
        service_fee_type: 1,
        quota: '',
        discount: ''
      }
      this.serviceSettingData = data
      this.serviceSettingDialogFormData.service_fee_type = data.service_fee_type
      if (data.service_fee_type === 1) {
        this.serviceSettingDialogFormData.discount = ''
        this.serviceSettingDialogFormData.quota = data.service_fee_value
          ? data.service_fee_value
          : ''
      }
      if (data.service_fee_type === 0) {
        this.serviceSettingDialogFormData.discount = data.service_fee_value
        this.serviceSettingDialogFormData.quota = ''
      }
      this.serviceSettingDialog = true
    },
    determineServiceSettingDialog() {
      this.$refs.serviceSettingForm.validate(valid => {
        if (valid) {
          // 塞到列表里面
          this.serviceSettingData.service_fee_type =
            this.serviceSettingDialogFormData.service_fee_type
          this.serviceSettingData.service_fee_value =
            this.serviceSettingDialogFormData.service_fee_type === 1
              ? this.serviceSettingDialogFormData.quota
              : this.serviceSettingDialogFormData.discount
          this.serviceSettingDialog = false
        } else {
          console.log(valid)
        }
      })
    },
    saveConsumHandle(type) {
      this.$refs.consumptionFormRef.validate(valid => {
        if (valid) {
          let params = {
            name: this.formData.name,
            org_nos: this.formData.orgNos,
            group_nos: this.formData.groupNos,
            is_tourist: this.formData.isTourist,
            take_type: this.formData.takeType,
            meal_time: this.formData.mealTime,
            rules: [],
            status: type
          }
          let rulesFlag = false
          for (let index = 0; index < this.historytableData.length; index++) {
            let payway = this.historytableData[index]
            if (
              this.formData.selectListId.length &&
              this.formData.selectListId.includes(payway.id)
            ) {
              if (payway.service_fee_value) {
                params.rules.push({
                  payway_alias: payway.payway_alias,
                  sub_payway_alias: payway.sub_payway_alias,
                  pay_scene_alias: payway.pay_scene_alias,
                  payinfo_id: payway.id,
                  type: payway.service_fee_type,
                  numerical:
                    payway.service_fee_type === 1
                      ? NP.times(Number(payway.service_fee_value), 100)
                      : Number(payway.service_fee_value)
                })
              } else {
                rulesFlag = true
              }
            }
          }
          if (rulesFlag) return this.$message.error('请输入手续费')
          if (this.type === 'add') {
            this.getCommissionChargeConsumeAdd(params)
          } else if (this.type === 'modify') {
            this.getConsumeModify({ rule_no: this.modifyData.rule_no, ...params })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss">
.add-conrules-wrapper {
  margin-top: 20px;
  padding: 20px;
  .consumption-form-wrapper {
    // max-width: 1106px;
  }
}
.block-label {
  width: 100%;
  padding-left: 50px;
  .el-form-item__label {
    display: block;
    text-align: left;
    line-height: 1.5;
    float: none;
  }
}
.footer {
  margin-top: 30px;
  margin-left: 110px;
  .el-button {
    min-width: 180px;
  }
}
</style>
