<template>
  <!-- 添加/编辑 -->
  <custom-drawer
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :wrapperClosable="false"
    :size="760"
    destroy-on-close
    v-bind="$attrs"
    v-on="$listeners"
    :confirmShow="false"
    cancelText="关闭"
    @close="handlerClose"
    @cancel="clickCancleHandle"
  >
    <div class="p-10">
      <el-table
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        size="small"
        stripe
        header-row-class-name="ps-table-header-row"
        row-key="id"
        border
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item"></table-column>
      </el-table>
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :page-sizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
    </div>
  </custom-drawer>
  <!-- end -->
</template>

<script>
export default {
  inheritAttrs: false,
  name: 'PropertyDetailsHistoryDrawer',
  props: {
    isShow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '历史记录'
    },
    width: {
      type: String,
      default: '460px'
    },
    showFooter: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSettings: [
        { label: '创建时间', key: 'create_time' },
        { label: '负债名称', key: 'name' },
        { label: '操作内容', key: 'operate_content' },
        { label: '操作员', key: 'operate' }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getDataList()
    },
    async getDataList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundFundSupervisionBusLiabilityV4HistoryRecordListPost({
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        this.tableData = data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 取消事件
    clickCancleHandle() {
      this.visible = false
    },
    // 关闭弹窗
    handlerClose(e) {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-form {
}
</style>
