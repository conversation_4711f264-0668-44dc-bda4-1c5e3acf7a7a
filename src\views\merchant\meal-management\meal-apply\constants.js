import { getDateRang } from '@/utils'
import { MEAL_TYPES } from '@/utils/constants'

export const PAYMENTSTATE = [
  { label: '全部', value: '' },
  { label: '待支付', value: 'ORDER_PAYING' },
  { label: '支付成功', value: 'ORDER_SUCCESS' },
  { label: '支付失败', value: 'ORDER_FAILED' },
  { label: '交易冲正中', value: 'ORDER_REVERSALING' },
  { label: '交易冲正', value: 'ORDER_REVERSAL' },
  { label: '退款中', value: 'ORDER_REFUNDING' },
  { label: '已退款', value: 'ORDER_REFUND_SUCCESS' },
  { label: '关闭(用户未支付)', value: 'ORDER_CLOSE' },
  { label: '过期', value: 'ORDER_TIME_OUT' },
  { label: '未知', value: 'ORDER_UNKNOWN' }
]

export const PROCESSIMG_SEARCH = {
  create_time: {
    timeRange: true,
    type: 'daterange',
    label: '申请时间',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  approve_no: {
    type: 'input',
    label: '单号',
    value: '',
    placeholder: '请输入单号'
  },
  name: {
    type: 'input',
    label: '申请人',
    value: '',
    placeholder: '请输入申请人'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '所属分组',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择所属分组'
  },
  phone: {
    type: 'input',
    label: '手机号码',
    value: '',
    placeholder: '请输入手机号码'
  },
  pay_method: {
    type: 'select',
    label: '访客餐支付方式',
    labelWidth: '110px',
    value: '',
    placeholder: '请选择',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '访客记账',
        value: 'Accounting'
      },
      {
        label: '即付',
        value: 'PayAtSight'
      }
    ]
  },
  user_type: {
    type: 'select',
    label: '适用用户',
    value: '',
    placeholder: '请选择适用用户',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '内部用户',
        value: 'insider'
      },
      {
        label: '外部用户',
        value: 'outsiders'
      }
    ]
  },
  apply_type_list: {
    type: 'select',
    label: '申请类型',
    value: [],
    multiple: true,
    filterable: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: []
  }
}

export const PROCESSED_SEARCH = {
  deal_time: {
    timeRange: true,
    type: 'daterange',
    label: '审批时间',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  create_time: {
    timeRange: true,
    type: 'daterange',
    label: '申请时间',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  approve_no: {
    type: 'input',
    label: '单号',
    value: '',
    placeholder: '请输入单号'
  },
  name: {
    type: 'input',
    label: '申请人',
    value: '',
    placeholder: '请输入申请人'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '所属分组',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择所属分组'
  },
  phone: {
    type: 'input',
    label: '手机号码',
    value: '',
    placeholder: '请输入手机号码'
  },
  pay_method: {
    type: 'select',
    label: '访客餐支付方式',
    labelWidth: '110px',
    value: '',
    placeholder: '请选择',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '访客记账',
        value: 'Accounting'
      },
      {
        label: '即付',
        value: 'PayAtSight'
      }
    ]
  },
  user_type: {
    type: 'select',
    label: '适用用户',
    value: '',
    placeholder: '请选择适用用户',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '内部用户',
        value: 'insider'
      },
      {
        label: '外部用户',
        value: 'outsiders'
      }
    ]
  },
  apply_type_list: {
    type: 'select',
    label: '申请类型',
    value: [],
    multiple: true,
    filterable: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: []
  },
  approve_status_list: {
    type: 'select',
    label: '审批状态',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择审批状态',
    dataList: [
      {
        label: '审批中',
        value: 'PROCESSING'
      },
      {
        label: '审批通过',
        value: 'AGREE'
      },
      {
        label: '拒绝申请',
        value: 'REJECT'
      }
    ]
  }
}
export const REVOKE_SEARCH = {
  deal_time: {
    timeRange: true,
    type: 'daterange',
    label: '撤销时间',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  create_time: {
    timeRange: true,
    type: 'daterange',
    label: '申请时间',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  approve_no: {
    type: 'input',
    label: '单号',
    value: '',
    placeholder: '请输入单号'
  },
  name: {
    type: 'input',
    label: '申请人',
    value: '',
    placeholder: '请输入申请人'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '所属分组',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择所属分组'
  },
  phone: {
    type: 'input',
    label: '手机号码',
    value: '',
    placeholder: '请输入手机号码'
  }
}

export const ACCOUNTIMG_SEARCH = {
  date_type: {
    type: 'select',
    value: 'create_time',
    label: '时间',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      },
      {
        label: '就餐时间',
        value: 'date'
      }
    ]
  },
  create_time: {
    timeRange: true,
    type: 'daterange',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  // create_time: {
  //   timeRange: true,
  //   type: 'daterange',
  //   label: '创建时间',
  //   clearable: false,
  //   value: getDateRang(0, { format: '{y}-{m}-{d}' })
  // },
  // pay_time: {
  //   timeRange: true,
  //   type: 'daterange',
  //   label: '支付时间',
  //   clearable: false,
  //   value: getDateRang(0, { format: '{y}-{m}-{d}' })
  // },
  // koukuan_time: {
  //   timeRange: true,
  //   type: 'daterange',
  //   label: '扣款时间',
  //   clearable: true,
  //   value: getDateRang(0, { format: '{y}-{m}-{d}' })
  // },
  name: {
    type: 'input',
    label: '用户姓名',
    value: '',
    placeholder: '请输入申请人'
  },
  phone: {
    type: 'input',
    label: '手机号码',
    value: '',
    placeholder: '请输入手机号码'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择分组'
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  source_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  trade_no: {
    type: 'input',
    label: '订单编号',
    value: '',
    placeholder: '请输入手机号码'
  },
  consume_type: {
    type: 'select',
    label: '记账类型',
    value: '',
    placeholder: '请选择记账类型',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '固定金额',
        value: 'GD'
      },
      {
        label: '按实际记账',
        value: 'JZ'
      }
    ]
  },
  settle_status: {
    type: 'select',
    label: '对账状态',
    value: '',
    placeholder: '请选择对账状态',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '已对账',
        value: 1
      },
      {
        label: '未对账',
        value: 0
      }
    ]
  },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  }
}

export const PAYABLE_SEARCH = {
  date_type: {
    type: 'select',
    value: 'create_time',
    label: '时间',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      },
      {
        label: '就餐时间',
        value: 'date'
      }
    ]
  },
  create_time: {
    timeRange: true,
    type: 'daterange',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  // create_time: {
  //   timeRange: true,
  //   type: 'daterange',
  //   label: '创建时间',
  //   clearable: false,
  //   value: getDateRang(0, { format: '{y}-{m}-{d}' })
  // },
  // pay_time: {
  //   timeRange: true,
  //   type: 'daterange',
  //   label: '支付时间',
  //   clearable: false,
  //   value: getDateRang(0, { format: '{y}-{m}-{d}' })
  // },
  // koukuan_time: {
  //   timeRange: true,
  //   type: 'daterange',
  //   label: '扣款时间',
  //   clearable: false,
  //   value: getDateRang(0, { format: '{y}-{m}-{d}' })
  // },
  order_status: {
    type: 'select',
    label: '支付状态',
    value: '',
    placeholder: '请选择',
    dataList: PAYMENTSTATE
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择分组'
  },
  name: {
    type: 'input',
    label: '用户姓名',
    value: '',
    placeholder: '请输入申请人'
  },
  phone: {
    type: 'input',
    label: '手机号码',
    value: '',
    placeholder: '请输入手机号码'
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  source_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  trade_no: {
    type: 'input',
    label: '订单编号',
    value: '',
    placeholder: '请输入手机号码'
  },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  },
  is_use: {
    type: 'select',
    label: '状态',
    value: '',
    placeholder: '请选择对账状态',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '已就餐',
        value: 1
      },
      {
        label: '未使用',
        value: 0
      },
      {
        label: '已过期',
        value: 2
      }
    ]
  }
}

export const ORDER_TOTAL_SEARCH = {
  create_time: {
    timeRange: true,
    type: 'daterange',
    label: '就餐日期',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择分组'
  },
  is_visitor: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看游客',
    value: false
  }
}

export const ORDER_PERPARE_SEARCH = {
  create_time: {
    timeRange: true,
    type: 'daterange',
    label: '就餐日期',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择分组'
  },
  is_visitor: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看游客',
    value: false
  }
}

export const PROCESSIMG_TABLE = [
  { label: '单号', key: 'approve_no' },
  { label: '申请时间', key: 'create_time' },
  { label: '所属分组', key: 'payer_group_name' },
  { label: '消费点', key: 'org_name', type: 'slot', slotName: 'tooltip' },
  { label: '申请人', key: 'name' },
  { label: '手机号码', key: 'phone' },
  { label: '餐段', key: 'meal_type_list', type: 'slot', slotName: 'tooltip' },
  { label: '就餐日期', key: 'date_range_str' },
  { label: '就餐人数', key: 'meal_num' },
  { label: '访客餐支付方式', key: 'pay_method_alias' },
  { label: '记账方式', key: 'consume_type_alias' },
  { label: '次数限制', key: 'num_limit_alias' },
  { label: '申请类型', key: 'apply_type' },
  { label: '备注', key: 'remark', type: 'slot', slotName: 'tooltip' },
  {
    label: '操作',
    key: 'operation',
    type: 'slot',
    slotName: 'operation',
    fixed: 'right',
    width: '120'
  }
]

export const PROCESSED_TABLE = [
  { label: '单号', key: 'approve_no' },
  { label: '审批时间', key: 'approve_time' },
  { label: '审批状态', key: 'approve_status' },
  { label: '所属分组', key: 'payer_group_name' },
  { label: '消费点', key: 'org_name', type: 'slot', slotName: 'tooltip' },
  { label: '申请人', key: 'name' },
  { label: '手机号码', key: 'phone' },
  { label: '餐段', key: 'meal_type_list', type: 'slot', slotName: 'tooltip' },
  { label: '就餐日期', key: 'date_range_str' },
  { label: '访客餐支付方式', key: 'pay_method_alias' },
  { label: '记账方式', key: 'consume_type_alias' },
  { label: '次数限制', key: 'num_limit_alias' },
  { label: '就餐人数', key: 'meal_num' },
  { label: '申请类型', key: 'apply_type' },
  { label: '申请时间', key: 'create_time' },
  { label: '备注', key: 'remark', type: 'slot', slotName: 'tooltip' },
  {
    label: '操作',
    key: 'operation',
    type: 'slot',
    slotName: 'operation',
    fixed: 'right',
    width: '80'
  }
]

export const REVOKE_TABLE = [
  { label: '单号', key: 'approve_no' },
  { label: '撤销时间', key: 'revoke_time' },
  { label: '所属分组', key: 'payer_group_name' },
  { label: '消费点', key: 'org_name', type: 'slot', slotName: 'tooltip' },
  { label: '申请人', key: 'name' },
  { label: '手机号码', key: 'phone' },
  { label: '餐段', key: 'meal_type_list', type: 'slot', slotName: 'tooltip' },
  { label: '就餐日期', key: 'date_range_str' },
  { label: '就餐人数', key: 'meal_num' },
  { label: '访客餐支付方式', key: 'pay_method_alias' },
  { label: '记账方式', key: 'consume_type_alias' },
  { label: '次数限制', key: 'num_limit_alias' },
  { label: '申请类型', key: 'apply_type' },
  { label: '申请时间', key: 'create_time' },
  { label: '备注', key: 'remark', type: 'slot', slotName: 'tooltip' }
]

export const ACCOUNTIMG_TABLE = [
  { label: '审批单号', key: 'approve_no' },
  { label: '订单号', key: 'trade_no' },
  { label: '创建时间', key: 'create_time' },
  { label: '支付时间', key: 'pay_time' },
  { label: '扣款时间', key: 'deduction_time' },
  { label: '就餐时间', key: 'date' },
  { label: '订单金额', key: 'origin_fee', type: 'money' },
  { label: '优惠金额', key: 'discount_fee', type: 'money' },
  { label: '实付金额', key: 'pay_fee', type: 'money' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '记账类型', key: 'consume_type_alias' },
  { label: '所属组织', key: 'source_organization_name' },
  { label: '用户姓名', key: 'name' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' },
  // { label: '访客餐支付方式', key: 'payway_alias' },
  { label: '支付状态', key: 'order_status_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '消费点', key: 'stall' },
  {
    label: '操作',
    key: 'operation',
    type: 'slot',
    slotName: 'operation',
    fixed: 'right',
    width: '220'
  }
]

export const PAYABLE_TABLE = [
  { label: '审批单号', key: 'approve_no' },
  { label: '订单号', key: 'trade_no' },
  { label: '创建时间', key: 'create_time' },
  { label: '支付时间', key: 'pay_time' },
  { label: '扣款时间', key: 'deduction_time' },
  { label: '就餐时间', key: 'date' },
  { label: '订单金额', key: 'origin_fee', type: 'money' },
  { label: '优惠金额', key: 'discount_fee', type: 'money' },
  { label: '实付金额', key: 'pay_fee', type: 'money' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '所属组织', key: 'source_organization_name' },
  { label: '用户姓名', key: 'name' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' },
  { label: '访客餐支付方式', key: 'sub_payway_alias' },
  { label: '支付类型', key: 'payway_alias' },
  { label: '支付状态', key: 'order_status_alias' },
  { label: '状态', key: 'is_use_alias' },
  { label: '消费点', key: 'stall' },
  {
    label: '操作',
    key: 'operation',
    type: 'slot',
    slotName: 'operation',
    fixed: 'right',
    width: '220'
  }
]

export const ORDER_TOTAL_TABLE = [
  { label: '分组/游客', key: 'group_name' },
  { label: '访客就餐次数', key: 'meal_count' },
  { label: '即付金额', key: 'pay_at_sight_fee', type: 'money' },
  { label: '记账金额', key: 'accounting_fee', type: 'money' },
  // { label: '记账次数', key: 'accounting_count' },
  { label: '对账次数', key: 'settle_count' },
  { label: '已对账金额', key: 'settle_fee', type: 'money' },
  { label: '未对账金额', key: 'settle_fail_fee', type: 'money' }
]

export const ORDER_PERPARE_TABLE = [
  { label: '消费点', key: 'organization_name' },
  {
    label: '早餐',
    key: 'breakfast',
    children: [
      { label: '记账/单', key: 'breakfast_accounting_count' },
      { label: '即付/单', key: 'breakfast_pay_at_sight_count' }
    ]
  },
  {
    label: '午餐',
    key: 'lunch',
    children: [
      { label: '记账/单', key: 'lunch_accounting_count' },
      { label: '即付/单', key: 'lunch_pay_at_sight_count' }
    ]
  },
  {
    label: '下午茶',
    key: 'afternoon',
    children: [
      { label: '记账/单', key: 'afternoon_accounting_count' },
      { label: '即付/单', key: 'afternoon_pay_at_sight_count' }
    ]
  },
  {
    label: '晚餐',
    key: 'dinner',
    children: [
      { label: '记账/单', key: 'dinner_accounting_count' },
      { label: '即付/单', key: 'dinner_pay_at_sight_count' }
    ]
  },
  {
    label: '夜宵',
    key: 'supper',
    children: [
      { label: '记账/单', key: 'supper_accounting_count' },
      { label: '即付/单', key: 'supper_pay_at_sight_count' }
    ]
  },
  {
    label: '凌晨餐',
    key: 'morning',
    children: [
      { label: '记账/单', key: 'morning_accounting_count' },
      { label: '即付/单', key: 'morning_pay_at_sight_count' }
    ]
  }
]

export const ACCOUNTIMG_COLLECT = [
  {
    key: 'total_count',
    value: '',
    label: '合计笔数',
    class: 'origin'
  },
  {
    key: 'total_origin_fee',
    value: '',
    label: '合计订单金额',
    class: 'origin',
    type: 'money'
  },
  {
    key: 'total_pay_fee',
    value: '',
    label: '合计实收金额',
    class: 'origin',
    type: 'money'
  },
  {
    key: 'settle_origin_fee',
    value: '',
    label: '已对账金额（订单金额）',
    class: 'origin',
    type: 'money'
  },
  {
    key: 'settle_pay_fee',
    value: '',
    label: '已对账金额（实收）',
    class: 'origin',
    type: 'money'
  }
]
export const PAYABLE_COLLECT = [
  {
    key: 'total_count',
    value: '',
    label: '合计笔数',
    class: 'origin'
  },
  {
    key: 'total_origin_fee',
    value: '',
    label: '合计订单金额',
    class: 'origin',
    type: 'money'
  },
  {
    key: 'total_pay_fee',
    value: '',
    label: '合计实收金额',
    class: 'origin',
    type: 'money'
  }
]

export const getRequestParams = (searchFormSetting, page, pageSize) => {
  const searchData = {}
  Object.keys(searchFormSetting).forEach(key => {
    console.log(searchFormSetting.payer_group_ids.value?.length)
    if (key === 'create_time' && searchFormSetting[key].value?.length > 0) {
      searchData.start_date = searchFormSetting[key].value[0]
      searchData.end_date = searchFormSetting[key].value[1]
    } else if (key === 'deal_time' && searchFormSetting[key].value?.length > 0) {
      searchData.deal_start_date = searchFormSetting[key].value[0]
      searchData.deal_end_date = searchFormSetting[key].value[1]
    } else if (
      searchFormSetting[key].value !== '' &&
      searchFormSetting[key].value &&
      searchFormSetting[key].value?.length
    ) {
      searchData[key] = searchFormSetting[key].value
    }else if (typeof searchFormSetting[key].value === 'number') {
      searchData[key] = searchFormSetting[key].value
    } else if (typeof searchFormSetting[key].value === 'boolean') {
      searchData[key] = searchFormSetting[key].value
    }
  })
  const params = {
    page,
    page_size: pageSize,
    ...searchData
  }

  return params
}
