<template>
  <div class="NutritionOrders container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="nutrition-content table-wrapper">
      <div class="nutrition-order-left" v-loading="isLoading">
        <div class="">
          <el-button class="ps-btn" size="small" @click="showDrawer = true">选择项目</el-button>
          <span class="m-l-30">打标对象：{{ markingText ? markingText : '请选择项目及餐段' }}</span>
        </div>
        <div class="m-t-30 m-b-30 font-size-20">{{ markingFoodText }}</div>
        <div class="">
          <el-image :src="markNutritionOrder.source_image_key" fit="contain" lazy :preview-src-list="[markNutritionOrder.source_image_key]" class="food-img">
            <div slot="error" class="el-image__error">
              <!-- <img src="https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png" alt=""> -->
              <i class="el-icon-picture-outline" style="font-size: 40px"></i>
              <div class="error-message m-t-10">
                {{ markNutritionOrder.source_image_key ? '加载图片失败' : '暂无图片' }}
              </div>
            </div>
          </el-image>
        </div>
        <div class="m-t-30">本餐打标订单数：{{ markCount || 0 }}笔</div>
      </div>
      <div v-loading="loadingFood" class="nutrition-order-right m-l-20 flex-1">
        <div v-loading="loadingCurrentFood">
          <div class="m-t-20">
            <span class="m-r-30">本餐打标菜品：</span>
            <el-button class="ps-origin-btn" @click="submitHandle">提交</el-button>
            <span class="m-l-20 m-r-20">不选择菜品进行提交默认跳过该笔订单。</span>
            <div class="inline-block m-l-10 p-t-10 p-b-10">
              <el-button v-for="(lately, index) in latelyHistory" :key="index" size="mini" class="ps-origin-btn ellipsis history-btn" @click="clickHistory(lately)">{{ getHistoryText(lately) }}</el-button>
              <el-dropdown class="m-l-20" @command="clickHistory">
                <span class="el-dropdown-link pointer">
                  历史记录<i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="(item, index) in historyList" :command="item" :key="index">{{ getHistoryText(item) }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div class="food-wrapper">
            <!-- el-scrollbar 滚动的高度会有问题，待研究 -->
            <!-- <el-scrollbar> -->
              <ul v-if="currentShowMealFoods.length > 0" class="food-box">
                <!-- <li v-for="(food, index) in currentShowMealFoods" :key="food.id" :class="['food-item', selectCurrentFood.includes(food.food) ? 'active' : '']" @click="clickCurrentFood(food, index)">{{ food.food_name }}</li> -->
                <li
                  v-for="(food, index) in currentShowMealFoods"
                  :key="food.id"
                  :class="['food-item', selectCurrentFood.includes(food.food) ? 'active' : '']"
                  @click="clickCurrentFood(food, index)"
                >
                  <el-tooltip
                    :disabled="food.food_name.length < 8"
                    effect="dark"
                    :content="food.food_name"
                    placement="top-start"
                    :open-delay="260"
                  >
                    <span class="ellipsis" style="display: block">{{ food.food_name }}</span>
                  </el-tooltip>
                </li>
              </ul>
              <el-empty v-else description="暂无数据" :image-size="200">
                <div slot="image" class="custom-img"></div>
              </el-empty>
            <!-- </el-scrollbar> -->
          </div>
        </div>
        <div v-loading="loadingHistoryFood">
          <div class="m-t-20 m-b-20">
            <span class="m-r-30">历史餐段打标菜品：</span>
            <VirtualListSelect
              v-model="selectFood"
              collapse-tags
              clearable
              filterable
              :selectData="foodList"
              :selectOption="selectOption"
              :loading="loadingFoodSelect"
              class="m-r-20"
            />
            <el-button class="ps-origin-btn" @click="addNuteitionFood">添加菜品</el-button>
            <el-button class="ps-origin-btn" @click="openFoodDrawer">新建菜品</el-button>
          </div>
          <div class="m-b-20">
            
          </div>
          <div class="food-wrapper">
            <!-- <el-scrollbar> -->
              <ul v-if="historyMealFoods.length > 0" class="food-box">
                <!-- <li v-for="(food, index) in historyMealFoods" :key="food.id" class="food-item" @click="clickHistoryFood(food, index)">{{ food.food_name }}</li> -->
                <li
                  v-for="(food, index) in historyMealFoods"
                  :key="food.id"
                  class="food-item"
                  @click="clickHistoryFood(food, index)"
                >
                  <el-tooltip
                    :disabled="food.food_name.length < 8"
                    effect="dark"
                    :content="food.food_name"
                    placement="top-start"
                    :open-delay="230"
                  >
                    <span class="ellipsis" style="display: block">{{ food.food_name }}</span>
                  </el-tooltip>
                </li>
              </ul>
              <el-empty v-else description="暂无数据" :image-size="200">
                <div slot="image" class="custom-img"></div>
              </el-empty>
            <!-- </el-scrollbar> -->
          </div>
        </div>
      </div>
    </div>

    <!-- 抽屉弹窗 -->
    <addNutritionOrderProject
      :show.sync="showDrawer"
      :infoData="drawerInfoData"
      :size="560"
      @confirm="confirmHandle"
    />
    <AddFoodDrawer :show.sync="showFoodDrawer" :size="920" @confirmFood="confirmFoodHandle" />
  </div>
</template>

<script>
import addNutritionOrderProject from './components/addNutritionOrderProject'
import AddFoodDrawer from './components/AddFoodDrawer'
import VirtualListSelect from '@/components/VirtualListSelect'
import { debounce, uniqueArrKey } from '@/utils'
import { mapGetters } from "vuex"

export default {
  name: 'NutritionOrders',
  components: {
    addNutritionOrderProject,
    AddFoodDrawer,
    VirtualListSelect
  },
  props: {},
  data() {
    return {
      isLoading: false,
      // 抽屉弹窗
      showDrawer: false,
      // 选择项目点的数据
      drawerInfoData: {
        // companyName: '',
        // companyId: '',
        // date: '',
        // mealType: '',
        // mealName: ''
      },
      showFoodDrawer: false,
      markNutritionOrder: {}, // 当前打标的订单
      markCount: 0, // 已打标的数量
      loadingCurrentFood: false,
      selectCurrentFood: [], // 当前选中的打标菜品
      currentMealFoods: [], // 本餐打标菜品
      loadingHistoryFood: false,
      selectHistoryFood: [], // 历史打标的菜品选择
      historyMealFoods: [], // 本餐打标菜品
      loadingFood: false, // 右侧加载菜品loading
      loadingFoodSelect: false,
      selectFood: '', // 下拉框选中的菜品
      foodList: [], // 下拉框菜品列表
      selectOption: {
        // 下拉框配置
        label: 'name', // 下拉框需要显示的名称
        value: 'id' // 下拉框绑定的值
      },
      addToCurrentMealFoods: [], // 历史打标菜品里面添加进当前餐段打标菜品的数据
      historyList: [] // 保存历史记录
    }
  },
  computed: {
    // 打标对象
    markingText: function () {
      return `${this.drawerInfoData.companyName || ''} ${this.drawerInfoData.date || ''} ${
        this.drawerInfoData.mealName || ''
      }`
    },
    markingFoodText: function () {
      let foodText = ''
      if (this.markNutritionOrder.payment_food_list && this.markNutritionOrder.payment_food_list.length > 0) {
        foodText = this.markNutritionOrder.payment_food_list.join('，')
      }
      if (this.drawerInfoData.companyId) {
        if (!this.markNutritionOrder.id) {
          return '暂无需要打标的数据'
        } else if (!foodText && this.markNutritionOrder.id) {
          return '暂无打标菜品数据'
        }
      } else {
        return '暂无需要打标的数据'
      }
      return foodText
    },
    // 当前餐段展示的可打标菜品，是接口请求回来的数据和历史打标菜品里添加进来的数据的合集
    currentShowMealFoods: function () {
      let totalFoods = uniqueArrKey([].concat(this.currentMealFoods, this.addToCurrentMealFoods), 'food')
      // 排下序，不要了哦
      // this.sortSelectCurrentFood(totalFoods)
      return totalFoods
    },
    // 最近两条历史记录
    latelyHistory: function () {
      // 两个最新的
      return this.historyList.slice(0, 2)
    },
    ...mapGetters([
      'userInfo'
    ])
  },
  watch: {},
  created() {
    if (this.$route.query && Object.keys(this.$route.query).length > 0) {
      this.drawerInfoData = this.$route.query
      this.confirmHandle(this.drawerInfoData)
    }
    console.log(111, this.userInfo)
    let history = localStorage.getItem(`NutritionOrdersHistory${this.userInfo.username}`)
    try {
      if (history) {
        this.historyList = JSON.parse(history)
      }
    } catch (error) {
      
    }
    
  },
  mounted() {},
  methods: {
    refreshHandle() {
      if (
        this.drawerInfoData.companyId &&
        this.drawerInfoData.date &&
        this.drawerInfoData.mealType
      ) {
        this.selectCurrentFood = []
        this.getNuteitionOrderList()
        this.getMarkFoodList('current')
        this.getMarkFoodList()
        this.getFoodlist()
      } else {
        this.$message.error('请先选择打标对象！')
      }
    },
    // 获取营养订单数据
    async getNuteitionOrderList() {
      if (
        !(this.drawerInfoData.companyId && this.drawerInfoData.date && this.drawerInfoData.mealType)
      ) {
        this.$message.error('请先选择打标对象！')
        return
      }
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundAdminMarkNutritionOrderMarkNutritionOrderListPost({
          company_id: this.drawerInfoData.companyId,
          mark_date: this.drawerInfoData.date,
          meal_type: this.drawerInfoData.mealType,
          page: 1,
          page_size: 1
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 当前打标的订单
        this.markNutritionOrder = res.data?.results[0] || {}
        // 已打标的数量
        this.markCount = res.data.collect?.is_mark_count || 0
      } else {
        this.$message.error(res.msg)
      }
    },
    // 本餐打标菜品，历史餐段打标菜品
    // 两个菜品都用同一个接口，只是参数不同
    // 打标菜品只需要传公司id餐段，历史餐段打标菜品需要传公司id餐段时间
    async getMarkFoodList(type) {
      let params = {
        company_id: this.drawerInfoData.companyId,
        meal_type: this.drawerInfoData.mealType
      }
      if (type === 'current') {
        params.mark_date = this.drawerInfoData.date
        this.loadingCurrentFood = true
      } else {
        this.loadingHistoryFood = true
      }

      const [err, res] = await this.$to(
        this.$apis.apiBackgroundAdminMarkNutritionOrderMarkFoodListPost({
          ...params,
          page: 1,
          page_size: 999999
        })
      )
      if (type === 'current') {
        this.loadingCurrentFood = false
        this.currentMealFoods = []
      } else {
        this.loadingHistoryFood = false
        this.historyMealFoods = []
      }
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (type === 'current') {
          this.currentMealFoods = res.data.results
        } else {
          this.historyMealFoods = res.data.results
          this.setDisabledFood()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取系统菜品列表
    async getFoodlist() {
      if (this.loadingFoodSelect) return
      this.loadingFoodSelect = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundAdminFoodListPost({
          page: 1,
          page_size: 999999
        })
      )
      this.loadingFoodSelect = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodList = res.data.results
        this.setDisabledFood()
      } else {
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 添加菜品
    async addNuteitionFood() {
      if (!this.selectFood) {
        return this.$message.error('请先选择菜品！')
      }
      if (
        !(this.drawerInfoData.companyId && this.drawerInfoData.date && this.drawerInfoData.mealType)
      ) {
        this.$message.error('请先选择打标对象！')
        return
      }
      this.loadingFood = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundAdminMarkNutritionOrderMarkFoodAddPost({
          company: this.drawerInfoData.companyId,
          mark_date: this.drawerInfoData.date,
          meal_type: this.drawerInfoData.mealType,
          food: this.selectFood
        })
      )
      this.loadingFood = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 添加时需要选中
        // if (!this.selectCurrentFood.includes(this.selectFood)) {
        //   this.selectCurrentFood.push(this.selectFood)
        // }
        this.selectFood = ''
        this.getMarkFoodList('current')
        this.getMarkFoodList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 打开添加菜品弹窗
    openFoodDrawer() {
      if (
        !(this.drawerInfoData.companyId && this.drawerInfoData.date && this.drawerInfoData.mealType)
      ) {
        this.$message.error('请先选择打标对象！')
        return
      }
      this.showFoodDrawer = true
    },
    confirmHandle(result) {
      console.log(result)
      // 当公司不同/餐段不同时/日期当中有一个不同时都清空历史菜品选中过的数据
      if (
        result.mealType !== this.drawerInfoData.mealType ||
        result.companyId !== this.drawerInfoData.companyId ||
        result.date !== this.drawerInfoData.date
      ) {
        this.addToCurrentMealFoods = []
        this.selectCurrentFood = []
      }
      this.drawerInfoData = Object.assign({}, result)
      this.getNuteitionOrderList()
      if (this.foodList.length === 0) {
        this.getFoodlist()
      } else {
        // 当有数据是需要重置当中的紧选字段为false
        this.foodList = this.foodList.map(v => {
          v.disabled = false
          return v
        })
      }
      this.$changehash({
        name: 'NutritionOrders',
        query: {
          ...this.drawerInfoData
          // params: this.$encodeQuery(this.drawerInfoData)
        }
      })
      this.getMarkFoodList('current')
      this.getMarkFoodList()
    },
    // 当前餐段菜品点击
    clickCurrentFood: debounce(function (data, index) {
      let currentIndex = this.selectCurrentFood.indexOf(data.food)
      if (currentIndex > -1) {
        this.selectCurrentFood.splice(currentIndex, 1)
      } else {
        this.selectCurrentFood.push(data.food)
      }
    }, 100),
    // 历史菜品点击需将菜品添加到当前菜品列表并选中排序
    clickHistoryFood(data, index) {
      let hasFood = this.currentMealFoods.some(v => v.food === data.food)
      if (!hasFood) {
        this.selectCurrentFood.push(data.food)
        this.addToCurrentMealFoods.push(data)
      }
    },
    // 对当前菜单的菜品进行排序
    sortSelectCurrentFood(arr) {
      arr.sort((a, b) => {
        const prevIndex = this.selectCurrentFood.indexOf(a.food)
        const nextIndex = this.selectCurrentFood.indexOf(b.food)
        if (prevIndex > -1 && nextIndex > -1) {
          return 1
        }
        return prevIndex - nextIndex > 0 ? -1 : 1
      })
    },
    // 设置添加菜品的数据紧选
    setDisabledFood() {
      if (this.foodList.length > 0 && this.historyMealFoods.length > 0) {
        const historyMealFoodIds = this.historyMealFoods.map(v => v.food)
        this.foodList = this.foodList.map(v => {
          if (historyMealFoodIds.includes(v.id)) {
            v.disabled = true
          }
          return v
        })
      }
    },
    // 提交
    async submitHandle() {
      if (!this.markNutritionOrder.id) {
        this.$message.error('暂无需要打标订单数据，请检查！')
        return
      }
      let params = {
        id: this.markNutritionOrder.id,
        is_mark: true
      }
      if (this.selectCurrentFood.length > 0) {
        params.food_ids = this.selectCurrentFood
      } else {
        params.is_jump = true
      }
      this.isLoading = true
      this.loadingFood = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundAdminMarkNutritionOrderMarkNutritionOrderModifyPost(params)
      )
      // this.isLoading = false
      this.loadingFood = false
      if (err) {
        this.isLoading = false
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 添加历史记录, 打标时保存下历史记录，方便下次直接点历史记录直接选中
        if (params.food_ids) {
          // 历史记录最新的在前，刷新则消失
          const select = this.currentShowMealFoods.filter(v => params.food_ids.includes(v.food))
          // 在开头添加
          this.historyList.unshift(select)
          // 历史记录保存10条
          if (this.historyList.length > 10) {
            // 删掉最后一条
            this.historyList.pop()
          }
          // 保存到本地咯
          localStorage.setItem(`NutritionOrdersHistory${this.userInfo.username}`, JSON.stringify(this.historyList))
        }
        this.selectCurrentFood = []
        this.getNuteitionOrderList()
        this.getMarkFoodList('current')
        this.getMarkFoodList()
      } else {
        this.isLoading = false
        this.$message.error(res.msg)
      }
    },
    // 添加菜品回调，刷新下菜品列表数据
    confirmFoodHandle() {
      this.getFoodlist()
    },
    getHistoryText(data) {
      if (!data || data.length === 0) {
        return ''
      }
      return data.reduce((prev, next) => {
        if (prev) {
          return `${prev}+${next.food_name}`
        } else {
          return next.food_name
        }
      }, '')
    },
    // 点击历史，回填选中数据。回清空上次选中的
    clickHistory: debounce(function (data) {
      console.log(data)
      this.selectCurrentFood = data.map(v => v.food)
    }, 100)
  }
}
</script>

<style scoped lang="scss">
.NutritionOrders {
  .flex-1 {
    flex: 1;
  }
  .table-wrapper {
    margin-top: 0 !important;
  }
  .el-dropdown-link {
    color: var(--origin);
  }
  .history-btn {
    max-width: 160px;
  }
  .nutrition-content {
    display: flex;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    .food-img {
      display: block;
      width: 692px;
      height: calc(100vh - 100px - 320px);
      border-radius: 12px;
    }
    ::v-deep.el-image__error {
      flex-direction: column;
    }
    .food-wrapper {
      margin-top: 10px;
      min-height: calc(100vh - 100px - 620px);
      border-radius: 12px;
      background-color: #f5f7fa;
    }
    .food-box {
      display: flex;
      flex-wrap: wrap;
      max-height: calc(100vh - 100px - 570px);
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        background-color: transparent;
        cursor: pointer;
        margin-right: 5px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: rgba(144,147,153,.3) !important;
        border-radius: 3px !important;
      }
      &::-webkit-scrollbar-thumb:hover{
        background-color: rgba(144,147,153,.5) !important;
      }
      &::-webkit-scrollbar-track {
        background-color: transparent !important;
        border-radius: 3px !important;
        -webkit-box-shadow: none !important;
      }
      .food-item {
        width: 120px;
        height: 40px;
        padding: 10px;
        // line-height: 20px;
        margin: 0 8px 20px;
        color: #797979;
        border: 1px solid #797979;
        border-radius: 6px;
        text-align: center;
        font-size: 13px;
        vertical-align: middle;
        cursor: pointer;
        &:hover {
          color: #9d9d9d;
          border-color: #9d9d9d;
          opacity: 0.7;
        }
        &.active {
          color: #ff9b45;
          border-color: #ff9b45;
        }
        span {
          display: inline-block;
          line-height: 1.2;
        }
      }
    }
  }
}
</style>
