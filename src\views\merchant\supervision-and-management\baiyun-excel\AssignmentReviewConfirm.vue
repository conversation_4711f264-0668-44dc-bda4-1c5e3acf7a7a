<template>
  <div class="assignment-review-confirm container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="tab-box m-b-20">
      <el-radio-group v-model="tabType" class="ps-radio-btn" @change="changeTabType">
        <el-radio-button label="REVIEWING"
          v-permission="['background_marketing.discount_limit.list']">待复核</el-radio-button>
        <el-radio-button label="CONFIRMED"
          v-permission="['background_marketing.discount_limit.discount_limit_cardinfo_mealtype_list']">已确认</el-radio-button>
        <el-radio-button label="REJECTED"
          v-permission="['background_marketing.discount_limit.discount_limit_cardinfo_mealtype_list']">已驳回</el-radio-button>
        <el-radio-button label="REVOKED"
          v-permission="['background_marketing.discount_limit.discount_limit_cardinfo_mealtype_list']">已撤销</el-radio-button>
      </el-radio-group>
    </div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"
      :autoSearch="false" @reset="resetHandler"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe
          header-row-class-name="ps-table-header-row"
          height="calc(100vh - 640px)" :max-height="600">
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operator_username="{ row }">
              {{ getOperatorUsername(row) }}
            </template>
            <template #confirmer_username="{ row }">
              {{ getConfirmerUsername(row) }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-origin" @click="gotoDetail(row)">查看</el-button>
              <el-button type="text" size="small" class="ps-origin" @click="agree(row)"
                v-if="tabType === 'REVIEWING'">同意</el-button>
              <el-button type="text" size="small" class="ps-red" @click="reject(row)"
                v-if="tabType === 'REVIEWING'">拒绝</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount">
        </pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 同意拒绝弹窗-->
    <el-dialog :title="dialogType === 'agree' ? '同意' : '拒绝'" :visible.sync="reviewDialogVisible" width="400px"
      :close-on-click-modal="false" :close-on-press-escape="false" class="ps-dialog">
      <div class="dialog-content">
        <template v-if="dialogType === 'agree'">
          <div class="dialog-content-title">确认已核实提交内容，同意生成台账</div>
        </template>
        <template v-else>
          <div class="dialog-content-title">确认拒绝生成台账，请填写拒绝原因</div>
          <div class="reject-reason">
            <div class="label required">拒绝原因</div>
            <el-input type="textarea" v-model="rejectReason" :rows="5" placeholder="请填写拒绝原因" maxlength="50" show-word-limit></el-input>
          </div>
        </template>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="reviewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReview" v-loading="isBtnLoading" :disabled="isBtnLoading">确定</el-button>
      </span>
    </el-dialog>

    <!-- 查看详情 -->
    <details-drawer
      :visible.sync="isShowDetailDrawer"
      :ledgerSerialNumber="ledgerSerialNumber"
      :ledgerNo="ledgerNo"
      :ledgerId="ledgerId"
      :reviewPerson="reviewPerson"
      :operatorName="operatorName"
      :showFooter="false"
      :id="id"
      >
    <template slot="footer">
      <div class="dialog-footer m-t-20">
        <el-button class="ps-cancel-btn" @click="isShowDetailDrawer = false">关 闭</el-button>
        <el-button type="primary" @click="agree(currentRow)" v-if="tabType === 'REVIEWING'">同 意</el-button>
        <el-button type="primary" @click="reject(currentRow)" v-if="tabType === 'REVIEWING'">拒 绝</el-button>
      </div>
    </template>
  </details-drawer>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import { SEARCH_SETTING_MORNING_REVIEW_CONFIRM, TABLE_HEAD_DATA_REVIEW_CONFIRM_PENDING, TABLE_HEAD_DATA_REVIEW_CONFIRM_CONFIRMED, TABLE_HEAD_DATA_REVIEW_CONFIRM_REJECTED, TABLE_HEAD_DATA_REVIEW_CONFIRM_REVOKED } from './constants'
import detailsDrawer from '@/views/merchant/supervision-and-management/baiyun-excel/all-ledger-details-drawer/index.vue'
export default {
  name: 'AssignmentLedgerPermission',
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_PENDING), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_PENDING), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_MORNING_REVIEW_CONFIRM), // 查询表单配置
      printType: 'AssignmentLedgerPermission', // 类型
      isShowRecordDialog: false, // 历史记录
      isShowConfigurationPermissionDialog: false, // 配置权限
      dialogType: 'add', // 配置权限类型
      permissionData: {}, // 配置权限数据
      dialogTitle: '配置权限', // 配置权限标题
      chooseData: [], // 批量编辑数据
      selectedIds: [], // 批量编辑选中id
      tabType: 'REVIEWING', // 当前tab
      reviewDialogVisible: false, // 审核弹窗
      rejectReason: '', // 拒绝原因
      currentRow: null, // 当前操作的行数据
      isBtnLoading: false, // 按钮loading
      isShowDetailDrawer: false, // 台账详情抽屉
      ledgerSerialNumber: '', // 26张台账表的序号 定义组件时格式:Table + ledgerSerialNumber + 自定义组件名
      ledgerNo: '', // 台账编号
      ledgerId: '', // 台账ID
      reviewPerson: '', // 复核人
      operatorName: '', // 提交人
      id: '', // 台账id
      ledgerTypeList: [] // 管理台账列表
    }
  },
  created() {
    this.initLoad()
    this.getLedgerTypeList()
  },
  components: {
    detailsDrawer
  },
  mounted() { },
  methods: {
    initLoad() {
      this.getDataList()
      this.getLedgerTypeList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (key === 'ledger_type_list') {
              params.ledger_type_list = [data[key].value]
            } else if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize,
        review_status: this.tabType
      }
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerLedgerReviewListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    async getLedgerTypeList() {
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerLedgerTypeListPost({})
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || []
        this.searchFormSetting.ledger_type_list.dataList = deepClone(data)
        this.ledgerTypeList = deepClone(data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log('handleSelectionChange', val)
      this.selectedIds = val.map(item => item.id)
      this.chooseData = deepClone(val)
    },
    // 查看详情
    gotoDetail(data) {
      console.log('gotoDetail', data)
      this.currentRow = data
      // 根据后端返回的key值进行判断 待完善
      let key = data.ledger_type
      this.id = data.id
      this.ledgerId = data.id
      switch (key) {
        case "KITCHEN_RANGE_HOOD_CLEAN":
          this.ledgerSerialNumber = '4'
          this.ledgerNo = data.ledger_no
          this.reviewPerson = (this.tabType === 'CONFIRMED' || this.tabType === 'REJECTED') ? this.getConfirmerUsername(data) : ''
          this.operatorName = this.getOperatorUsername(data)
          break;
        case 'KITCHEN_RANGE_HOOD_CLEAN':
          this.ledgerSerialNumber = '24'
          this.ledgerNo = data.ledger_no
          break;
        case 'DAILY_FOOD_SAFETY':
          this.ledgerSerialNumber = '22'
          this.ledgerNo = data.ledger_no
          this.operatorName = this.getConfirmerUsername(data)
          break;
        case 'COLLECTIVE_DINING_DELIVERY_RECORD':
          this.ledgerSerialNumber = '26'
          break;
        case 'MORNING_CHECK':
          this.ledgerSerialNumber = '2'
          this.ledgerNo = data.ledger_no
          break;
        case 'INSPECTION_RECORD':
          this.ledgerSerialNumber = '11'
          break;
        case 'FOOD_ADDITIVE':
          this.ledgerSerialNumber = '8'
          this.ledgerNo = data.ledger_no
          this.reviewPerson = (this.tabType === 'CONFIRMED' || this.tabType === 'REJECTED') ? this.getConfirmerUsername(data) : ''
          this.operatorName = this.getOperatorUsername(data)
          console.log('this.reviewPerson', this.reviewPerson, this.operatorName)
          break;
        case 'ALLERGEN_USE_RECORD':
          this.ledgerSerialNumber = '9'
          this.ledgerNo = data.ledger_no
          this.reviewPerson = (this.tabType === 'CONFIRMED' || this.tabType === 'REJECTED') ? this.getConfirmerUsername(data) : ''
          this.operatorName = this.getOperatorUsername(data)
          break;
        case 'WEEKLY_FOOD_SAFETY_REPORT':
          this.ledgerSerialNumber = '23'
          break;
        case 'SCHOOL_FOOD_SAFETY_MANAGEMENT_CHECKLIST':
          this.ledgerSerialNumber = '25'
          break;
        case 'FOOD_RESERVED_SAMPLE':
          this.ledgerSerialNumber = '13'
          break;
        case 'EMPLOYEE_FOOD_SAFETY_TRAINING_RECORD':
          this.ledgerSerialNumber = '21'
          break;
        default:
          break;
      }
      this.isShowDetailDrawer = true
    },
    // 同意
    agree(data) {
      console.log('agree', data)
      this.currentRow = data
      this.dialogType = 'agree'
      this.reviewDialogVisible = true
    },
    // 拒绝
    reject(data) {
      console.log('reject', data)
      this.currentRow = data
      this.dialogType = 'reject'
      this.rejectReason = ''
      this.reviewDialogVisible = true
    },
    // 切换tab
    changeTabType(val) {
      this.tabType = val
      this.searchFormSetting = deepClone(SEARCH_SETTING_MORNING_REVIEW_CONFIRM)
      if (Reflect.has(this.searchFormSetting, 'ledger_type_list')) {
        this.searchFormSetting.ledger_type_list.dataList = deepClone(this.ledgerTypeList)
      }
      if (val === 'REVIEWING') {
        this.tableSetting = deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_PENDING)
        this.currentTableSetting = deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_PENDING)
      } else if (val === 'CONFIRMED') {
        this.tableSetting = deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_PENDING)
        this.currentTableSetting = deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_CONFIRMED)
      } else if (val === 'REJECTED') {
        this.tableSetting = deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_REJECTED)
        this.currentTableSetting = deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_REJECTED)
      } else if (val === 'REVOKED') {
        this.tableSetting = deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_REVOKED)
        this.currentTableSetting = deepClone(TABLE_HEAD_DATA_REVIEW_CONFIRM_REVOKED)
      }
      this.currentPage = 1
      this.getDataList()
    },
    // 确认审核
    confirmReview() {
      if (this.dialogType === 'reject') {
        if (!this.rejectReason || this.rejectReason.length < 0) {
          this.$message.warning('请至少填写拒绝原因')
          return
        }
      }
      if (this.dialogType === 'agree') {
        console.log('确认同意', this.currentRow)
        this.updateStatus(this.currentRow, '', 'CONFIRMED')
      } else {
        console.log('确认拒绝', this.currentRow, this.rejectReason)
        this.updateStatus(this.currentRow, this.rejectReason, 'REJECTED')
      }
      this.reviewDialogVisible = false
    },
    // 获取提交操作员
    getOperatorUsername(row) {
      const operatorUsername = row.operator_username || ''
      const operatorMemberName = row.operator_member_name || ''
      return operatorMemberName + (operatorUsername ? `(${operatorUsername})` : '')
    },
    // 获取复核人
    getConfirmerUsername(row) {
      const confirmerUsername = row.confirmer_username || ''
      const confirmerMemberName = row.confirmer_member_name || ''
      return confirmerMemberName + (confirmerUsername ? `(${confirmerUsername})` : '')
    },
    async updateStatus(data, reason, status) {
      console.log('updateStatus', data, reason, status)
      if (this.isBtnLoading) return
      let params = {
        id: data.id,
        review_status: status
      }
      if (status === 'REJECTED') {
        params.rejected_reason = reason
      }
      this.isBtnLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerLedgerReviewStatusUpdatePost(params)
      )
      this.isBtnLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.reviewDialogVisible = false
        this.currentPage = 1
        this.getDataList()
        this.isShowDetailDrawer = false
      } else {
        this.$message.error(res.msg)
      }
    }

    // 获取详情通用接口
    // async getLedgerReviewDetail(id) {
    //   let [err, res] = await to(
    //     this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyLedgerReviewDetail({ id })
    //   )
    //   if (err) {
    //     this.$message.error(err.message)
    //     return
    //   }
    //   if (res.code === 0) {
    //     // this.$message.success('操作成功')
    //     // this.reviewDialogVisible = false
    //     // this.currentPage = 1
    //     // this.getDataList()
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // }
  }
}
</script>
<style lang="scss" scoped>
.assignment-review-confirm {
  ::v-deep .el-dialog {
    .el-dialog__body {
      padding: 0 20px !important;
    }
  }

  .dialog-content {
    padding: 10px 0;
    text-align: left;

    .dialog-content-title {
      margin-bottom: 15px;
      font-size: 14px;
    }

    .reject-reason {
      text-align: left;

      .label {
        margin-bottom: 8px;
        font-size: 14px;

        &.required::before {
          content: '*';
          color: #F56C6C;
          margin-right: 4px;
        }
      }
    }
  }
}
</style>
