<template>
  <div class="add-inventory-stock-wrapper">
    <div class="m-t-20 clearfix">
      <span class="float-l">盘点管理/新增盘点</span>
      <div class="float-r">
        <el-button :disabled="isLoading" size="small" class="ps-cancel-btn w-130" @click="backPrevHandle">
          取消
        </el-button>
        <el-button :disabled="isLoading" size="small" class="ps-btn w-130" type="primary" @click="submitHandle">
          确定
        </el-button>
      </div>
    </div>
    <div class="container-wrapper">
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table"></div>
          <div class="align-r">
            <button-icon color="origin" @click="openImport">导入</button-icon>
            <button-icon color="origin" @click="showFormDialog('add')">新增物资</button-icon>
          </div>
        </div>
        <div class="table-content">
          <!-- content start -->
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            max-height="600px"
            stripe
            size="small"
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item">
              <template #resultCount="{ row }">
                <span :class="{ red: row.resultCount < 0 }">{{ row.resultCount }}</span>
              </template>
              <template #image="{ row }">
                <el-button
                  :disabled="imageJsonDisabled(row)"
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="handleClick(row)"
                >
                  查看
                </el-button>
              </template>
              <template #operation="{ row, index }">
                <el-button type="text" size="small" class="ps-text" @click="showFormDialog('modify', row)">
                  编辑
                </el-button>
                <el-button type="text" size="small" class="ps-text" @click="deleteHandle(row, index)">删除</el-button>
              </template>
            </table-column>
          </el-table>
          <!-- content end -->
        </div>
        <!-- 分页 start -->
        <div class="block ps-pagination">
          <pagination
            :onPaginationChange="onPaginationChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :layout="'total, prev, pager, next, jumper'"
            :total="totalCount"
          ></pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
    <!-- 添加弹窗 -->
    <dialog-message
      :show.sync="dialogVisible"
      :title="dialogTitle"
      :loading.sync="dialogFormLoading"
      @close="handleClose"
      customClass="ps-dialog dialog-stock"
      width="500px"
      top="200px"
    >
      <el-form
        :model="dialogFormData"
        @submit.native.prevent
        status-icon
        ref="dialogFormRef"
        :rules="dialogFormRules"
        label-width="100px"
        class="dialog-form"
      >
        <el-form-item label="物资名称" prop="stockId">
          <!-- :disabled="disabledList.includes(item.materials_id)" -->
          <el-select
            v-model="dialogFormData.stockId"
            placeholder="请选择"
            class="ps-select search-item-w"
            :disabled="dialogType === 'modify'"
            @change="changeIngredient"
          >
            <el-option
              v-for="item in inventoryList"
              :key="item.materials_id"
              :label="item.materials_name"
              :value="item.materials_id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="最小单位" prop="limitUnitId">
          <el-select v-model="dialogFormData.limitUnitId" :disabled="!dialogFormData.stockId" clearable filterable class="ps-select search-item-w" popper-class="ps-popper-select" placeholder="请选择" @change="changeLimitUnit">
            <el-option v-for="option in dialogFormData.limitUnitManagement" :key="option.id" :label="`${option.unit_management_name}*${option.net_content}${option.net_content_unit}`" :value="option.id" :disabled="disabledList.includes(option.key)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前库存" prop="stockCount">
          <el-input v-model="dialogFormData.stockCount" disabled class="search-item-w"></el-input>
        </el-form-item>
        <el-form-item label="盘点数量" prop="checkCount">
          <el-input v-model="dialogFormData.checkCount" class="search-item-w"></el-input>
        </el-form-item>
        <el-form-item label="盘点备注" prop="remark">
          <el-input
            v-model="dialogFormData.remark"
            class="search-item-w"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 10 }"
            placeholder="请输入内容"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="盘点图片" prop="fileLists">
          <file-upload
            ref="pictureRef"
            :fileList="dialogFormData.fileLists"
            type="enclosure"
            :before-upload="beforeUpload"
            @fileLists="getFileLists"
            class="avatar-uploader"
            :show-file-list="false"
          >
            <img
              v-if="dialogFormData.fileLists.length"
              :src="dialogFormData.fileLists[0].url"
              class="avatar"
              @click="clearFileHandle"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <!-- <template slot-scope="scope">
              <el-button :loading="scope.loading" class="ps-origin" size="small" type="text">上传{{ scope.loading ? '中' : '' }}</el-button>
            </template> -->
          </file-upload>
          <span class="tips m-l-20">支持png、jpg格式，图片大小不能超过2MB</span>
        </el-form-item>
      </el-form>
      <div slot="tool" class="footer-center m-t-60">
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right">
          <el-button :disabled="dialogFormLoading" class="ps-cancel-btn" @click="clickCancleHandle">取消</el-button>
          <el-button :disabled="dialogFormLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">
            确定
          </el-button>
        </div>
      </div>
    </dialog-message>
    <!-- end -->
    <!-- dialog start -->
    <import-page-dialog
      ref="importPageRef"
      title="导入物资"
      :show.sync="showImportDialog"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :isUpload="false"
      @confirm="confirmImportHandle"
    ></import-page-dialog>
    <!-- dialog end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSuffix } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import NP from 'number-precision'
import { downloadJsonExcel } from '@/utils/excel'
import { positiveMoney } from "@/utils/validata"
import { push } from 'shelljs/commands'

export default {
  name: 'InventoryStock',
  mixins: [exportExcel],
  components: {
    ElImageViewer
  },
  data() {
    const validateStock = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        if (!positiveMoney(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      }
    }
    return {
      id: '',
      isLoading: false, // 刷新数据
      tableData: [], // table数据
      tableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '当前库存', key: 'stockCount' },
        { label: '盘点数量', key: 'checkCount' },
        { label: '最小单位', key: 'limit_unit_name' },
        { label: '盘盈盘亏', key: 'resultCount' },
        { label: '盘点图片', key: 'image', type: 'slot', slotName: 'image' },
        { label: '备注', key: 'remark' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      dialogType: '', // 弹窗的状态，add/modify
      showImagePreview: false,
      previewList: [],
      dialogVisible: false, // 弹窗显示
      dialogTitle: '',
      dialogFormLoading: false,
      dialogFormData: {
        limitUnitId: '',
        limitUnitName: '',
        limitUnitManagement: [],
        stockId: '',
        stockCount: '',
        checkCount: '',
        remark: '', // 备注
        fileLists: []
      },
      dialogFormRules: {
        stockId: [{ required: true, message: '请选择物资', trigger: 'change' }],
        checkCount: [
          { required: true, message: '请输入盘点数量', trigger: 'change' },
          { validator: validateStock, trigger: 'change' }
        ]
      },
      inventoryList: [],
      query: {},
      // 导入的弹窗数据
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/导入盘点.xlsx',
      importHeaderLen: 2
    }
  },
  computed: {
    // 已添加的数据弹窗要禁用
    disabledList() {
      console.log('this.tableData.map(v => v.materials_id)', this.tableData.map(v => `${v.materials_id}_${v.limit_unit_id}`))
      return this.tableData.map(v => `${v.materials_id}_${v.limit_unit_id}`)
    },
  },
  created() {
    this.query = this.$route.query
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getInventoryManagementList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getInventoryManagementList() {
      if (!this.query.warehouse_id) return this.$message.error('出错啦,缺少参数!')
      let params = {
        warehouse_id: +this.query.warehouse_id,
        type: 'exit_info',
        page: 1,
        page_size: 999999
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpMaterialInventoryGetMaterialsListPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) {
          this.inventoryList = []
          return
        }
        this.inventoryList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
    },
    handleClick(row) {
      this.previewList = row.image_json
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    imageJsonDisabled(row) {
      let status = true
      if (row.image_json && row.image_json.length) {
        status = false
      }
      return status
    },
    // 导入弹窗
    openImport(type) {
      this.showImportDialog = true
    },
    // 导入物资确定
    confirmImportHandle(data) {
      let importData = data
      // 删除示例数据
      importData.splice(1, 1)
      console.log(importData)
      // importResult
      if (importData.length > 1) {
        // this.importLoading = true
        // 导入数据对应后端需要的字段
        const importObject = {
          '物资名称': 'materials_name',
          '盘点数量': 'inventory_num',
          '盘点备注': 'remark'
        }
        // 根据purchaseNameObject转换的index key
        let resultKey = {}
        importData[0].forEach((v, index) => {
          resultKey[index] = importObject[v]
        })
        let result = []
        importData.forEach((item, index) => {
          if (index > 0) {
            let current = {}
            item.forEach((v, k) => {
              current[resultKey[k]] = v
            })
            result.push(current)
          }
        })
        console.log(222, result)
        this.sendImportMaterials(result)
      } else {
        this.$message.error('导入物资为空！')
      }
    },
    // 发送导入物资数据给后端校验
    async sendImportMaterials(data) {
      if (this.importLoading) return
      this.importLoading = true
      let params = {
        data: data,
        warehouse_id: +this.query.warehouse_id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpMaterialInventoryImportMaterialsPost(params))
      // this.isLoading = false
      this.importLoading = false
      this.$refs.importPageRef.reset()
      this.showImportDialog = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 拿下当前列表的物资id判断导入是否重复
        let tableMaterialsIdObj = {}
        let resMaterialsIdObj = {}
        let result = []
        let failure = []
        const sortImportData = {} // 用来排序导入失败的数据，万一他们又要失败的数据按导入的表的顺序来呢，是不是
        data.map((materials, index) => {
          sortImportData[`${materials.materials_name}`] = index
        })
        if (res.data.success && res.data.success.length > 0) {
          this.tableData.map(v => {
            tableMaterialsIdObj[`${v.materials_id}`] = 1
          })
          res.data.success.map(v => {
            if (resMaterialsIdObj[`${v.materials_id}`]) {
              resMaterialsIdObj[`${v.materials_id}`] += 1
            } else {
              resMaterialsIdObj[`${v.materials_id}`] = 1
            }
            
          })
          res.data.success.forEach(v => {
            if (tableMaterialsIdObj[`${v.materials_id}`] || resMaterialsIdObj[`${v.materials_id}`] > 1) {
              failure.push({
                ...v,
                result: `${v.materials_name}物资重复`
              })
            } else {
              const resultCount = NP.minus(v.inventory_num, v.current_num)
              result.push({
                ...v,
                checkCount: v.inventory_num,
                stockCount: v.current_num,
                resultCount: resultCount,
                image_json: []
              })
            }
          })
          this.tableData.push(...result)
        }
        if (res.data.failure && res.data.failure.length > 0) {
          // 按导入的顺序排下序
          const allAailure = [...res.data.failure, ...failure].sort((a, b) => {
            return sortImportData[a.materials_name] > sortImportData[b.materials_name] ? 1 : -1
          })
          this.formatImportFailureResult(allAailure)
          this.$message.error('部分物资导入失败，请查看excel!')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化导入失败的数据，通过xlsx方式下载显示
    formatImportFailureResult(result) {
      const purchaseNameObject = {
        'materials_name': 0,
        'inventory_num': 1,
        'remark': 2,
        'result': 3
      }
      let failureJson = [
        ['物资名称', '盘点数量', '盘点备注', '导入结果']
      ]
      let json = result.map(v => {
        let current = []
        Object.keys(purchaseNameObject).forEach(k => {
          current[purchaseNameObject[k]] = v[k]
        })
        return current
      })
      failureJson = failureJson.concat(json)
      // 下载数据
      downloadJsonExcel(failureJson)
    },
    resetDialogForm() {
      this.dialogFormData = {
        stockId: '',
        stockCount: '',
        checkCount: '',
        fileLists: []
      }
      this.dialogFormLoading = false
    },
    showFormDialog(type, data) {
      this.dialogType = type
      if (type === 'add') {
        this.dialogFormData = {
          stockId: '',
          limitUnitId: '',
          stockCount: '',
          checkCount: '',
          fileLists: []
        }
      } else {
        let form = {
          stockId: data.materials_id,
          limitUnitId: data.limit_unit_id,
          limitUnitManagement: data.limit_unit_management,
          stockCount: data.stockCount,
          checkCount: data.checkCount,
          remark: data.remark,
          fileLists: []
        }
        if (data.image_json.length > 0) {
          let fileName = data.image_json[0].substring(data.image_json[0].lastIndexOf('/') + 1)
          // 后端加了tk校验文件，要把它截掉
          let isSplit = fileName.indexOf('?')
          if (isSplit > -1) {
            fileName = fileName.substring(0, isSplit)
          }
          form.fileLists = [
            {
              name: fileName,
              status: 'success',
              uid: new Date().getTime(),
              url: data.image_json[0]
            }
          ]
        }
        this.dialogFormData = form
      }
      this.dialogVisible = true
    },
    changeIngredient(e) {
      const current = this.inventoryList.find(v => v.materials_id === e)
      current.limit_unit_management.map(unit => {
        unit.key = `${current.materials_id}_${unit.id}`
      })
      this.$set(this.dialogFormData, 'limitUnitManagement', current.limit_unit_management)
    },
    changeLimitUnit(e) {
      const current = this.dialogFormData.limitUnitManagement.find(v => v.id === e)
      this.$set(this.dialogFormData, 'stockCount', current.current_num)
      this.$set(this.dialogFormData, 'limitUnitName', `${current.unit_management_name}*${current.net_content}${current.net_content_unit}`)
    },
    getFileLists(fileLists) {
      this.dialogFormData.fileLists = fileLists
      console.log(fileLists)
    },
    beforeUpload(file) {
      let unUploadType = ['.jpg', '.png']
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 jpg/png 格式')
        return false
      }
      const isLt10M = file.size / 1024 / 1024 < 2
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
    },
    clearFileHandle() {
      this.$refs.pictureRef.clearHandle()
      this.dialogFormData.fileLists = []
    },
    handleClose() {},
    clickCancleHandle() {
      this.$refs.dialogFormRef && this.$refs.dialogFormRef.resetFields()
      this.dialogVisible = false
    },
    clickConfirmHandle() {
      this.$refs.dialogFormRef.validate(async valid => {
        if (valid) {
          if (this.dialogFormLoading) return
          this.dialogFormLoading = true
          const resultCount = NP.minus(this.dialogFormData.checkCount, this.dialogFormData.stockCount)
          console.log(this.dialogFormData.checkCount, this.dialogFormData.stockCount, resultCount)
          const current = this.inventoryList.find(v => v.materials_id === this.dialogFormData.stockId)
          let result = {
            ...current,
            checkCount: this.dialogFormData.checkCount,
            stockCount: this.dialogFormData.stockCount,
            remark: this.dialogFormData.remark,
            resultCount: resultCount,
            image_json: this.dialogFormData.fileLists.map(v => v.url),
            limit_unit_name: this.dialogFormData.limitUnitName,
            limit_unit_id: this.dialogFormData.limitUnitId,
          }
          const index = this.tableData.findIndex(v => v.materials_id === this.dialogFormData.stockId && v.limit_unit_id === this.dialogFormData.limitUnitId)
          if (index > -1) {
            this.tableData.splice(index, 1, result)
          } else {
            this.tableData.push(result)
          }
          this.$refs.dialogFormRef && this.$refs.dialogFormRef.resetFields()
          await this.$sleep(200)
          this.dialogFormLoading = false
          this.dialogVisible = false
        }
      })
    },
    // 删除
    async deleteHandle(data, index) {
      this.$confirm('确定删除吗？', `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.tableData.splice(index, 1)
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            done()
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    backPrevHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'InventoryStock')
    },
    async submitHandle() {
      if (!this.tableData.length) {
        this.$message.error('请添加盘点数据！')
      }
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        name: this.$route.query.name,
        warehouse_id: +this.query.warehouse_id
      }
      params.inventory_json = this.tableData.map(v => {
        return {
          materials_id: v.materials_id,
          materials_name: v.materials_name,
          inventory_num: v.checkCount,
          current_num: v.stockCount,
          limit_unit_name: v.limit_unit_name,
          file_url: v.image_json[0],
          remark: v.remark
        }
      })
      console.log('this.tableData', this.tableData)
      const [err, res] = await to(this.$apis.apiBackgroundDrpMaterialInventoryAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.backPrevHandle()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-inventory-stock-wrapper {
  .ps-pagination {
    padding-top: 0px !important;
  }
  .search-item-w {
    width: 280px;
  }
  .w-130 {
    width: 130px;
  }
  .red {
    color: red;
  }
  .ps-text.is-disabled {
    color: #c0c4cc !important;
  }
}
</style>

<style lang="scss">
.dialog-stock {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .avatar {
    width: 80px;
    height: 80px;
    display: inline-block;
  }
  .el-upload {
    height: 80px;
  }
  .tips {
    line-height: 18px;
    font-size: 12px;
    color: #a2a2a2;
  }
}
</style>
