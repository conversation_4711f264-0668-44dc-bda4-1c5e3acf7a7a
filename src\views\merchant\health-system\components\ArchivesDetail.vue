<template>
  <div class="ps-el-drawer">
    <el-drawer
      :title="'档案详情'"
      :visible="visible"
      :show-close="false"
      size="70%">
      <div class="drawer-content p-20 flex-col" v-loading="isLoading">
        <div class="drawer-content-info m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">基本信息</div>
          <div class="bg-grey ps-flex-align-c flex-justify-c">
            <el-image
              style="width: 100px; height: 100px"
              :src="headImg"
              :fit="'contain'"></el-image>
            <div class="drawer-content-info-box m-l-20 w-100-p">
              <div v-for="(item, index) in infoData" :key="index" :class="[item.type === 'line' ? 'line' : '']">
                <div v-if="item.type !== 'line'" class="ps-flex-align-c flex-align-c">
                  <div class="m-r-20">
                    <span style="color: #939597">{{ item.label }}：</span>
                    <span>{{ item.value }}{{ item.unit }}</span>
                  </div>
                  <div v-if="item.key === 'bmi'" :class="['tips', selectClass(item.text)]" style="width: 60px;">{{ item.text }}</div>
                </div>
                <div v-else>
                  <div v-if="item.label === '饮食禁忌'">
                    <span style="color: #939597">{{ item.label }}：</span>
                    <span v-for="(item1, index1) in item.value" :key="index1"> {{ item1 }} {{ index1 < item.value.length - 1 ? '、' : '' }}</span>
                  </div>
                  <div v-else class="ps-flex-align-c flex-align-c">
                    <div class="m-r-20">
                      <span style="color: #939597">{{ item.label }}：</span>
                      <span>{{ item.value }}{{ item.unit }}</span>
                    </div>
                    <div v-if="item.key === 'job_alias'" :class="['tips', selectClass(computedPAL(item.Job_intensity))]" style="width: 80px;">{{ computedPAL(item.Job_intensity) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="drawer-content-physicalData m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">体检数据</div>
          <div class="bg-grey">
            <div class="drawer-content-physicalData-box" v-if="physicalData.length">
              <div v-for="(item, index) in physicalData" :key="index" class="ps-flex-align-c flex-align-c">
                <div class="m-r-10">
                  <span style="color: #939597">{{ item.name }}</span>： {{ item.value || '--' }} {{ item.unit }}
                </div>
                <!-- <div :class="['tips', item.index === 'normal' ? 'normal' : '', item.index === 'height' ? 'height' : '', item.index === 'low' ? 'low' : '', 'font-size-12']">
                  {{ tipName(item.index) }}
                </div> -->
              </div>
            </div>
            <div class="flex-center" v-else>
              <el-empty description="暂无数据" :image-size="80"></el-empty>
            </div>
          </div>
        </div>
        <div class="drawer-content-nutrient m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">营养素摄入推荐</div>
          <div class="bg-grey">
            <div class="drawer-content-nutrient-box" v-if="nutrientData.length">
              <div v-for="(item, index) in nutrientData" :key="index">
                <span style="color: #939597">{{ item.name }}</span>： {{ item.range[0] + '~' + item.range[1] }} {{ item.unit }}
              </div>
            </div>
            <div class="flex-center" v-else>
              <el-empty description="暂无数据" :image-size="80"></el-empty>
            </div>
          </div>
        </div>
        <div class="drawer-content-diet m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">饮食结构</div>
          <div class="bg-grey">
            <div class="drawer-content-diet-box" v-if="dietData.length">
              <div v-for="(item, index) in dietData" :key="index" class="drawer-content-diet-box-item">
                <span style="color: #939597">{{ item.name }}</span>： {{ item.range[0] + '~' + item.range[1] }} {{ item.unit }}
              </div>
            </div>
            <div class="flex-center" v-else>
              <el-empty description="暂无数据" :image-size="80"></el-empty>
            </div>
          </div>
        </div>
        <div class="drawer-content-forewarning m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">摄入预警</div>
          <div class="bg-grey">
            <div class="drawer-content-forewarning-box">
              <!-- 饮食数据 -->
              <diet :formInfoData="nutrientIntakeData" v-loading="isLoading" />
            </div>
          </div>
        </div>
        <div class="m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">运动数据</div>
          <div class="bg-grey">
            <el-table ref="tableView" :data="sportTableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
              <table-column  v-for="item in sportTableSetting" :key="item.key" :col="item">
              </table-column>
            </el-table>
          </div>
        </div>
        <div class="m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">习惯打卡</div>
          <div class="bg-grey">
            <el-table ref="tableView" :data="habitTableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
              <table-column  v-for="item in habitTableSetting" :key="item.key" :col="item">
              </table-column>
            </el-table>
          </div>
        </div>
        <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
          <div class="m-r-30">
            <el-button size="small" type="primary" class="w-100" @click="visible = false">关闭</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { deepClone } from '@/utils';
import Diet from './Diet'
export default {
  props: {
    isShow: Boolean,
    archivesDetailParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    Diet
  },
  data() {
    return {
      isLoading: false,
      infoData: [
        {
          key: 'name',
          label: '姓名',
          value: '',
          unit: '',
          type: ''
        },
        {
          key: 'person_no',
          label: '人员编号',
          value: '',
          unit: '',
          type: ''
        },
        {
          key: 'phone',
          label: '手机号',
          value: '',
          unit: '',
          type: ''
        },
        {
          key: 'gender',
          label: '性别',
          value: '',
          unit: '',
          type: ''
        },
        {
          key: 'age',
          label: '年龄',
          value: '',
          unit: '岁',
          type: ''
        },
        {
          key: 'height',
          label: '身高',
          value: '',
          unit: 'cm',
          type: ''
        },
        {
          key: 'weight',
          label: '体重',
          value: '',
          unit: 'kg',
          type: ''
        },
        {
          key: 'bmi',
          label: 'BMI',
          value: '19',
          text: '',
          unit: '',
          type: ''
        },
        {
          key: 'job_alias',
          label: '职业',
          value: '',
          pal: '',
          unit: '',
          type: 'line'
        },
        {
          key: 'disease_name',
          label: '个人特征',
          value: '',
          unit: '',
          type: 'line'
        },
        {
          key: 'ingredient_taboo',
          label: '饮食禁忌',
          value: '',
          unit: '',
          type: 'line'
        }
      ],
      headImg: '',
      physicalData: [],
      nutrientData: [],
      dietData: [],
      sportTableSetting: [
        { label: '运动名称', key: 'name' },
        { label: '运动强度', key: 'intensity_alias' },
        { label: '最近一次记录', key: 'last_update_time' },
        { label: '最近一次运动时长', key: 'last_length' },
        { label: '最近一次消耗能量', key: 'last_use_energy_kcal' },
        { label: '总记录次数', key: 'count' }
      ],
      habitTableSetting: [
        { label: '习惯名称', key: 'name' },
        { label: '最近一次打卡', key: 'update_time' },
        { label: '打卡次数', key: 'count' }
      ],
      nutrientIntakeData: {},
      sportTableData: [],
      habitTableData: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    },
    tipName() {
      return d => {
        let name = ''
        switch (d) {
          case 'low':
            name = '偏低'
            break
          case 'normal':
            name = '正常'
            break
          case 'height':
            name = '偏高'
            break
          default:
            break
        }
        return name
      }
    },
    computedPAL() {
      return d => {
        let str = ''
        switch (d) {
          case 'lv2':
            str = 'PAL：中'
            break
          case 'lv3':
            str = 'PAL：重'
            break
          default:
            str = 'PAL：轻'
            break
        }
        return str
      }
    }
  },
  watch: {
    visible(newVal, oldVal) {
      if (newVal) {
        this.setTableData()
      }
    }
  },
  methods: {
    setTableData() {
      this.isLoading = true
      this.$apis.apiBackgroundHealthyHealthyInfoHealthyInfoDetailsPost({
        ...this.archivesDetailParams
      }).then(res => {
        if (res.code === 0) {
          let archivesDetailData = res.data
          // 基本信息
          this.headImg = archivesDetailData.base_data.head_image || 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'
          this.infoData.forEach(item => {
            for (let key in archivesDetailData.base_data) {
              if (item.key === key) {
                item.value = archivesDetailData.base_data[key]
              }
              if (item.key === 'bmi' && key === 'bmi_text') {
                item.text = archivesDetailData.base_data[key]
              }
              if (item.key === 'job' && key === 'Job_intensity') {
                item.pal = archivesDetailData.base_data[key]
              }
            }
          })
          console.log('this.infoData', this.infoData)
          // 体检数据
          let obj = deepClone(archivesDetailData.physical_data) || {}
          if (obj.return_data && obj.return_data.length) {
            if (obj.data_source !== 'custom') {
              let bloodPressureL = obj.return_data.filter(item => item.key === 'blood_pressure_l')
              let newBloodPressureL = bloodPressureL[0].children.map(item => {
                item.name = item.name + '(左臂)'
                return item
              })
              let bloodPressureR = obj.return_data.filter(item => item.key === 'blood_pressure_r')
              let newBloodPressureR = bloodPressureR[0].children.map(item => {
                item.name = item.name + '(右臂)'
                return item
              })
              this.physicalData = [
                ...obj.return_data[0].children,
                ...obj.return_data[1].children,
                ...newBloodPressureL,
                ...newBloodPressureR
              ]
            } else {
              this.physicalData = [
                ...obj.return_data[0].children,
                ...obj.return_data[1].children,
                ...obj.return_data[2].children
              ]
            }
          }
          // 营养素摄入
          this.nutrientData = deepClone(archivesDetailData.nutrition_intake)
          // 饮食结构
          this.dietData = deepClone(archivesDetailData.food_category_nutrition)
          // 摄入预警
          this.nutrientIntakeData = deepClone(archivesDetailData.nutrient_intake_data)
          this.sportTableData = deepClone(archivesDetailData.sport_data.sport_list)
          this.habitTableData = deepClone(archivesDetailData.habit_data.habit_list)
          this.isLoading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    selectClass(str) {
      console.log('str', str)
      let className = ''
      switch (str) {
        case 'low':
          className = 'low'
          break
        case 'normal':
          className = 'normal'
          break
        case 'hight':
          className = 'height'
          break
        case 'PAL：轻':
          className = 'low'
          break
        case 'PAL：中':
          className = 'normal'
          break
        case 'PAL：重':
          className = 'height'
          break
        default:
          className = 'overWeight'
      }
      return className
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-content {
  &-info {
    &-box {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 10px;
      // grid-auto-flow: row;
      .line {
        grid-column-start: 1;
        grid-column-end: 5;
      }
    }
  }
  &-physicalData {
    &-box {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, 1fr);
      grid-auto-flow: row;
      grid-gap: 10px;
    }
  }
  &-nutrient {
    &-box {
      display: grid;
      grid-template-rows: repeat(auto, 1fr);
      grid-template-columns: repeat(5, 1fr);
      grid-gap: 10px;
    }
  }
  &-diet {
    &-box {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 10px 20px;
      &-item {
        padding: 10px 20px;
        background-color: #fff;
        border-radius: 4px;
      }
    }
  }
  .bg-grey {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
  }
  .tips {
    padding: 0px 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 4px;
  }
  .normal {
    background-color: #83E4BB;
  }
  .height {
    background-color: #F3AE8D;
  }
  .low {
    background-color: #A7D9FA;
  }
  .overWeight {
    background-color: #FF8F8F;
  }
}

</style>
