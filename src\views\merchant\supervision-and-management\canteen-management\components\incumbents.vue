<template>
  <div>
    <div class="table-header">
      <div class="header-left">
        <div class="header-left-title">人员配比</div>
      </div>
    </div>
    <div class="table-content disposition-content">
      <div v-for="(item, index) in countData" :key="index">
        <span>{{ item.job_title }}：</span>
        <span>{{ item.count }}人</span>
      </div>
    </div>
    <div class="table-header">
      <div class="header-left">
        <div class="header-left-title">在职人员（{{ totalCount }}人）</div>
        <div class="header-left-text">
          <div>在职：{{ resignData.in_num }}人，</div>
          <div>离职：{{ resignData.out_num }}人</div>
        </div>
      </div>
      <div class="align-r">
        <button-icon @click="showDrawer('add')" v-permission="['background_fund_supervision.job_person.add_job_person']">添加</button-icon>
      </div>
    </div>
    <div class="table-content">
      <!-- table start -->
      <el-table
        v-loading="isLoading"
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        stripe
        header-row-class-name="ps-table-header-row"
      >
        <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
          <template #phone="{ row }">
            <el-tooltip class="item" effect="dark" :content="row.phone" placement="top">
              <div>{{ computedPhone(row.phone) }}</div>
            </el-tooltip>
          </template>
          <template #idNumber="{ row }">
            <el-tooltip class="item" effect="dark" :content="row.id_number" placement="top">
              <div>{{ computedIdNumber(row.id_number) }}</div>
            </el-tooltip>
          </template>
          <template #recruitType="{ row }">
            {{ computedRecruitType(row.recruit_type) }}
          </template>
          <template #faceImage="{ row }">
            <el-button type="text" size="small" class="ps-text" :disabled="!row.face_image"  @click="handleClick('faceImg', row)">查看</el-button>
          </template>
          <template #healthImage="{ row }">
            <el-button type="text" size="small" class="ps-text" :disabled="!row.health_image"  @click="handleClick('healthImg', row)">查看</el-button>
          </template>
          <template #effectiveTime="{ row }">
            <div :class="computedTime(row.effective_time) ? 'red' : ''">{{ row.effective_time }}</div>
          </template>
          <template #aptitudeList="{ row }">
            <el-button type="text" size="small" class="ps-text" :disabled="!row.aptitude_list || !row.aptitude_list.length"  @click="handleClick('aptitudeList', row)">查看</el-button>
          </template>
          <template #inTime="{ row }">
            <div>{{ row.in_time || '--' }}</div>
          </template>
          <template #outTime="{ row }">
            <div>{{ row.out_time || '--' }}</div>
          </template>
          <template #leaveRemark="{ row }">
            <div v-if="row.leave_remark">
              <el-tooltip class="item" effect="dark" :content="row.leave_remark" placement="top">
                <div slot="content" style="width: 400px;">{{row.leave_remark}}</div>
                <div class="ellipsis" style="width: 80px;">{{ row.leave_remark }}</div>
              </el-tooltip>
            </div>
            <div v-else>--</div>
          </template>
          <template #appointmentState="{ row }">
            <div>{{ row.appointment_state === 'incumbency' ? '在职' : '离职' }}</div>
          </template>
          <template #operation="{ row }">
            <el-button v-show="row.appointment_state === 'incumbency'" type="text" size="small" class="ps-text" @click="showDrawer('resign', row)" v-permission="['background_fund_supervision.job_person.leave_job_person']">离职</el-button>
            <el-button v-show="row.appointment_state === 'incumbency'" type="text" size="small" class="ps-text" @click="showDrawer('edit', row)" v-permission="['background_fund_supervision.job_person.modify_job_person']">编辑</el-button>
            <el-button type="text" size="small" class="ps-warn-text" @click="deleteHandle(row)" v-permission="['background_fund_supervision.job_person.delete_job_person']">删除</el-button>
          </template>
        </table-column>
      </el-table>
      <!-- table end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="selectType === 'add' ? '添加人员' : '编辑人员'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="drawerFormRef" :model="drawerForm" label-width="100px" label-position="right">
            <el-form-item :label="'姓名'" prop="name" :rules="[{ required: true, message: '请输入姓名', trigger: ['change', 'blur'] }]">
              <el-input v-model="drawerForm.name" class="w-300" placeholder="请输入姓名，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item
              :label="'联系电话'"
              prop="phone"
              :rules="[
                { required: true, message: '请输入联系电话', trigger: ['change', 'blur'] },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: ['change', 'blur'] }
              ]">
              <el-input v-model="drawerForm.phone" class="w-300" placeholder="请输入联系电话，不超过11位" maxlength="11"></el-input>
            </el-form-item>
            <el-form-item
              :label="'身份证号'"
              prop="IDcard"
              :rules="[
                { required: true, message: '请输入身份证号', trigger: ['change', 'blur'] },
                { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入有效的身份证号', trigger: ['change', 'blur'] }
              ]">
              <el-input v-model="drawerForm.IDcard" class="w-300" placeholder="请输入身份证号" maxlength="18"></el-input>
            </el-form-item>
            <el-form-item :label="'所属岗位'" prop="post" :rules="[{ required: true, message: '请输入岗位信息', trigger: ['change', 'blur'] }]">
              <el-input v-model="drawerForm.post" class="w-300" placeholder="请输入岗位信息，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item :label="'用工形式'" prop="form" :rules="[{ required: true, message: '请选择用工形式', trigger: ['change', 'blur'] }]">
              <el-radio-group v-model="drawerForm.form">
                <el-radio :label="'zz'">直招</el-radio>
                <el-radio :label="'pq'">派遣</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="'入职时间'" prop="inTime" :rules="[{ required: drawerForm.form === 'zz', message: '请选择入职时间', trigger: ['change', 'blur'] }]">
              <el-date-picker
                v-model="drawerForm.inTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="'系统账号'" prop="systemAccount">
              <el-select v-model="drawerForm.systemAccount" filterable placeholder="请选择" clearable>
                <el-option
                  v-for="(item, index) in accountList"
                  :key="index"
                  :label="item.member_name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="'人脸照片'" prop="faceUrl" :rules="[{ required: true, message: '请上传有效的人脸照片', trigger: ['change', 'blur'] }]">
              <div class="certification-info-show-tips" style="color: #C0C4CC;">
                图片最大不超过2MB，仅支持jpg,png格式
              </div>
              <div class="flex-b-c">
                <el-upload v-loading="uploadingForFace" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                  :action="serverUrl" :file-list="fileListsForFace" :on-success="uploadSuccessForFace"
                  :before-upload="(file) => beforeFoodImgUpload(file, 2)" :limit="1" :multiple="false" :show-file-list="false"
                  :headers="headersOpts" :data="uploadParams" accept=".jpeg,.jpg,.png,.bmp">
                  <img v-if="drawerForm.faceUrl" :src="drawerForm.faceUrl" style="width: 150px; height: 150px; margin-right: 20px;">
                  <div v-else style="width: 150px; height: 150px; border: 1px dashed #C0C4CC; text-align: center; line-height: 150px; margin-right: 20px;">
                    <i class="el-icon-plus"></i>
                  </div>
                </el-upload>
                <div style="line-height: 16px; font-size: 14px; color: #C0C4CC;">
                  提示:<br/>
                  1、确保拍摄/上传的图片只存在一张人脸信息;<br/>
                  2、确保拍摄/上传的图片清晰，无模糊;<br/>
                  3、确保拍摄的图片，人脸位于图片正中央位置，且占据图片超过60%的区域;<br/>
                  4、确保拍摄时光线充足;<br/>
                  5、确保照片为正90度;<br/>
                  6、为避免影响人脸功能的正常使用，建议满6个月后更新一次人脸照片。<br/>
                </div>
              </div>
            </el-form-item>
            <el-form-item :label="'健康证件'" prop="imgUrl" :rules="[{ required: true, message: '请上传有效证件', trigger: ['change', 'blur'] }]">
              <div class="certification-info-show-tips" style="color: #C0C4CC;">
                图片最大不超过2MB，仅支持jpg,png格式
              </div>
              <el-upload v-loading="uploadingForImg" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                :action="serverUrl" :file-list="fileListsForImg" :on-success="uploadSuccessForImg"
                :before-upload="(file) => beforeFoodImgUpload(file, 2)" :limit="1" :multiple="false" :show-file-list="false"
                :headers="headersOpts" accept=".jpeg,.jpg,.png,.bmp">
                <img v-if="drawerForm.imgUrl" :src="drawerForm.imgUrl" style="width: 150px; height: 150px;">
                <div v-else style="width: 150px; height: 150px; border: 1px dashed #C0C4CC; text-align: center; line-height: 150px;">
                  <i class="el-icon-plus"></i>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item :label="'证件有效期'" prop="time" :rules="[{ required: true, message: '请选择证件的过期日期', trigger: ['change', 'blur'] }]">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="drawerForm.time"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="'背景资质'" prop="aptitudeList" :rules="[{ required: false, message: '请上传有效证件', trigger: ['change', 'blur'] }]">
              <div class="certification-info-show-tips" style="color: #C0C4CC;">
                图片最大不超过2MB，仅支持jpg,png格式
              </div>
              <el-upload v-loading="uploadingForAptitudeList" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                :action="serverUrl" :file-list="fileListsForAptitudeList" :on-success="uploadSuccessForAptitudeList"
                :before-upload="(file) => beforeFoodImgUpload(file, 5)" :limit="3" :multiple="false" :show-file-list="false"
                :headers="headersOpts" accept=".jpeg,.jpg,.png,.bmp">
                <div v-if="drawerForm.aptitudeList.length" class="flex">
                  <div style="position: relative;"  v-for="(item, index) in drawerForm.aptitudeList" :key="index">
                    <div class="delete m-r-10" @click.stop="deleteImgHandle(index)">
                      <i class="el-icon-delete" style="position: absolute; top: -14px; right: -14px; color: #fff;"></i>
                    </div>
                    <img :src="item" style="width: 150px; height: 150px; margin-right: 10px;" object-fit="fill">
                  </div>
                  <div v-if="drawerForm.aptitudeList.length < 3" style="width: 150px; height: 150px; border: 1px dashed #C0C4CC; text-align: center; line-height: 150px; margin-right: 10px;">
                    <i class="el-icon-plus"></i>
                  </div>
                </div>
                <div v-else style="width: 150px; height: 150px; border: 1px dashed #C0C4CC; text-align: center; line-height: 150px;">
                  <i class="el-icon-plus"></i>
                </div>
              </el-upload>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <el-dialog
      title="提示"
      :visible.sync="resignShow"
      width="25%"
      :before-close="resignDialogCancelHandle">
      <div class="resignShow">
        <span>确认将职工（</span>
        <span class="ps-text">{{ `${employeeName}` }}</span>
        <span>）的任职状态修改为离职？离职后职工信息将被禁用，关联账号解除并禁用。请谨慎操作。</span>
        <el-form class="m-t-20" :model="resignForm" :rules="resignRules" ref="resignFormRef" label-position="top">
          <el-form-item label="离职日期" prop="out_time">
            <el-date-picker
              class="w-100-p"
              v-model="resignForm.out_time"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions1"
              type="date"
              placeholder="选择离职日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="离职原因" prop="leave_remark">
            <el-input v-model="resignForm.leave_remark" placeholder="请输入离职原因" maxlength="200" type="textarea" :autosize="{ minRows: 3, maxRows: 6}" show-word-limit></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer">
        <el-button @click="resignDialogCancelHandle">取 消</el-button>
        <el-button type="primary" @click="resignDialogConfirmHandle">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { getToken, getSuffix, deepClone, to } from '@/utils/index'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import dayjs from 'dayjs'
export default {
  components: {
    ElImageViewer
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      countData: [],
      accountList: [],
      tableSetting: [
        { label: '姓名', key: 'name', showTooltip: true },
        { label: '联系电话', key: 'phone', type: 'slot', slotName: 'phone' },
        { label: '身份证号', key: 'id_number', type: 'slot', slotName: 'idNumber' },
        { label: '所属岗位', key: 'job_title', showTooltip: true },
        { label: '用工形式', key: 'recruit_type', type: "slot", slotName: 'recruitType' },
        { label: '系统账号', key: 'sys_account_alias' },
        { label: '人脸照片', key: 'face_image', type: "slot", slotName: "faceImage" },
        { label: '健康证', key: 'health_image', type: "slot", slotName: "healthImage" },
        { label: '证件有效期', key: 'effective_time', type: 'slot', slotName: 'effectiveTime' },
        { label: '背景资质', key: 'aptitude_list', type: 'slot', slotName: 'aptitudeList' },
        { label: '入职日期', key: 'in_time', type: 'slot', slotName: 'inTime' },
        { label: '离职日期', key: 'out_time', type: 'slot', slotName: 'outTime' },
        { label: '离职原因', key: 'leave_remark', type: 'slot', slotName: 'leaveRemark' },
        { label: '任职状态', key: 'appointment_state', type: 'slot', slotName: 'appointmentState' },
        { label: '修改时间', key: 'update_time' },
        { label: '操作人', key: 'operator' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "200" }
      ],
      page: 1,
      pageSize: 10,
      totalCount: 0,
      selectType: '',
      selectId: '',
      drawerShow: false,
      drawerForm: {
        name: '',
        phone: '',
        IDcard: '',
        post: '',
        form: 'zz',
        inTime: '',
        systemAccount: '',
        faceUrl: '',
        imgUrl: '',
        time: '',
        aptitudeList: []
      },
      uploadingForFace: false, // 上传加载中
      uploadingForImg: false, // 上传加载中
      uploadingForAptitudeList: false,
      serverUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'user_face',
        key: new Date().getTime() + Math.floor(Math.random() * 150)
      },
      fileListsForFace: [],
      fileListsForImg: [],
      fileListsForAptitudeList: [],
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      pickerOptions1: {
        disabledDate: (time) => this.setTimeLimit(time)
      },
      importDrawerShow: false,
      showImagePreview: false,
      previewList: [],
      resignData: {
        in_num: 0,
        out_num: 0
      },
      resignShow: false,
      employeeName: '',
      resignForm: {
        out_time: '',
        leave_remark: ''
      },
      resignRules: {
        out_time: [{ required: true, message: '请选择离职日期', trigger: ['change', 'blur'] }]
      }
    }
  },
  computed: {
    computedRecruitType() {
      return d => {
        let str = ''
        if (d === 'zz') {
          str = '直招'
        } else {
          str = '派遣'
        }
        return str
      }
    },
    computedTime() {
      return d => {
        console.log('d', d)
        let diff = dayjs().diff(dayjs(d), 'day')
        console.log('diff', diff)
        if (diff <= -30) {
          return false
        } else {
          return true
        }
      }
    },
    computedPhone() {
      return d => {
        return d.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
      }
    },
    computedIdNumber() {
      return d => {
        return d.slice(0, 6) + '********' + d.slice(-4);
      }
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getDataList()
      this.getCountData()
      this.getAccountList()
    },
    // 图片上传成功
    uploadSuccessForFace(res, file, fileList) {
      this.uploadingForFace = false
      if (res && res.code === 0) {
        this.fileListsForFace = []
        this.drawerForm.faceUrl = res.data.public_url
      } else {
        this.drawerForm.faceUrl = ''
        this.$message.error(res.msg)
      }
    },
    uploadSuccessForImg(res, file, fileList) {
      this.uploadingForImg = false
      if (res && res.code === 0) {
        this.fileListsForImg = []
        this.drawerForm.imgUrl = res.data.public_url
      } else {
        this.drawerForm.imgUrl = ''
        this.$message.error(res.msg)
      }
    },
    uploadSuccessForAptitudeList(res, file, fileList) {
      console.log('看看上传完的结果', res, file, fileList)
      this.uploadingForAptitudeList = false
      if (res && res.code === 0) {
        this.fileListsForAptitudeList = []
        this.drawerForm.aptitudeList = [...this.drawerForm.aptitudeList, res.data.public_url]
      } else {
        this.drawerForm.aptitudeList = [...this.drawerForm.aptitudeList]
        this.$message.error(res.msg)
      }
    },
    // 图片上传前检测
    beforeFoodImgUpload(file, num) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= num
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error(`上传图片大小不能超过 ${num}MB!`)
        return false
      }
      this.uploading = true
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
      this.getAccountList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.getDataList()
      this.getAccountList()
    },
    // 获取数据
    getDataList() {
      this.isLoading = true
      let params = {
        page: this.page,
        page_size: this.pageSize
      }
      this.$apis.apiBackgroundFundSupervisionJobPersonJobPersonListPost(params).then(res => {
        if (res.code === 0) {
          this.isLoading = false
          this.tableData = deepClone(res.data.results || [])
          this.totalCount = res.data.count
          this.getResignData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getCountData() {
      this.countData = []
      this.$apis.apiBackgroundFundSupervisionJobPersonCountJobPersonPost({
        page: 1,
        page_size: 9999
      }).then(res => {
        if (res.code === 0) {
          this.countData = deepClone(res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getAccountList() {
      this.$apis.apiBackgroundOrganizationAccountListPost({
        page: 1,
        page_size: 9999,
        status: 1
      }).then(res => {
        if (res.code === 0) {
          this.accountList = deepClone(res.data.results)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleClick(type, row) {
      switch (type) {
        case 'faceImg':
          this.previewList = [row.face_image]
          break
        case 'healthImg':
          this.previewList = [row.health_image]
          break
        case 'aptitudeList':
          this.previewList = deepClone(row.aptitude_list)
          break
      }
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    showDrawer(type, data) {
      this.selectType = type
      console.log('data', data)
      if (type === 'resign') {
        this.selectId = data.id
        this.employeeName = data.name
        this.pickerOptions1.disabledDate = (time) => this.setTimeLimit(time, data)
        console.log('this.pickerOptions1', this.pickerOptions1)
        this.resignShow = true
      } else {
        if (type === 'edit') {
          this.selectId = data.id
          this.drawerForm.phone = data.phone
          this.drawerForm.IDcard = data.id_number
          this.drawerForm.form = data.recruit_type
          this.drawerForm.faceUrl = data.face_image
          this.drawerForm.systemAccount = data.sys_account_id
          this.drawerForm.imgUrl = data.health_image
          this.drawerForm.name = data.name
          this.drawerForm.post = data.job_title
          this.drawerForm.time = data.effective_time
          this.drawerForm.aptitudeList = data.aptitude_list
          this.drawerForm.inTime = data.in_time
        } else {
          this.selectId = ''
          this.drawerForm.name = ''
          this.drawerForm.phone = ''
          this.drawerForm.IDcard = ''
          this.drawerForm.post = ''
          this.drawerForm.form = 'zz'
          this.drawerForm.systemAccount = ''
          this.drawerForm.faceUrl = ''
          this.drawerForm.imgUrl = ''
          this.drawerForm.time = ''
          this.drawerForm.aptitudeList = ''
          this.drawerForm.inTime = ''
        }
        this.drawerShow = true
        setTimeout(() => {
          this.$refs.drawerFormRef.clearValidate()
        }, 10)
      }
    },
    cancelHandle() {
      this.$refs.drawerFormRef.resetFields()
      this.drawerShow = false
    },
    saveHandle() {
      this.$refs.drawerFormRef.validate((valid) => {
        if (valid) {
          let params = {
            id: this.selectType === 'add' ? undefined : this.selectId,
            phone: this.drawerForm.phone ? this.drawerForm.phone : undefined,
            face_image: this.drawerForm.faceUrl,
            health_image: this.drawerForm.imgUrl,
            sys_account: this.drawerForm.systemAccount ? this.drawerForm.systemAccount : undefined,
            name: this.drawerForm.name,
            job_title: this.drawerForm.post,
            effective_time: this.drawerForm.time,
            id_number: this.drawerForm.IDcard,
            recruit_type: this.drawerForm.form ? this.drawerForm.form : undefined,
            in_time: this.drawerForm.inTime || undefined,
            aptitude_list: this.drawerForm.aptitudeList
          }
          if (this.selectType === 'add') {
            this.addJobPerson(params)
          } else {
            this.editJobPerson(params)
          }
        } else {
          this.$message.error('请确认表单内容填写是否正确')
        }
      })
    },
    addJobPerson(params) {
      this.$apis.apiBackgroundFundSupervisionJobPersonAddJobPersonPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('新增成功')
          this.$refs.drawerFormRef.resetFields()
          this.drawerShow = false
          this.initLoad()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    editJobPerson(params) {
      this.$apis.apiBackgroundFundSupervisionJobPersonModifyJobPersonPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('编辑成功')
          this.$refs.drawerFormRef.resetFields()
          this.drawerShow = false
          this.initLoad()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    deleteHandle(data) {
      this.$confirm(`确定要删除 ${data.name} 的人员信息？删除后关联账号将解除并禁用，删除数据不可恢复，请谨慎操作。`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$apis.apiBackgroundFundSupervisionJobPersonDeleteJobPersonPost({
          id: data.id
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
          } else {
            this.$message.error(res.msg)
          }
          this.initLoad()
        })
      }).catch(action => {
        this.$message('已取消删除')
      })
    },
    resignDialogCancelHandle() {
      this.selectId = ''
      this.$refs.resignFormRef.resetFields()
      this.resignShow = false
    },
    resignDialogConfirmHandle() {
      this.$refs.resignFormRef.validate(async (valid) => {
        if (valid) {
          const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionJobPersonLeaveJobPersonPost({
            ...this.resignForm,
            id: this.selectId
          }))
          if (err) {
            this.$message.error(err.msg)
          }
          if (res && res.code === 0) {
            this.$message.success('操作成功')
            this.resignDialogCancelHandle()
            this.getDataList()
          }
        } else {
          this.$message.error('请检查表单填写是否正确')
        }
      })
    },
    getResignData() {
      this.$apis.apiBackgroundFundSupervisionJobPersonJobPersonTotalPost().then(res => {
        if (res.code === 0) {
          this.resignData = deepClone(res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    deleteImgHandle(index) {
      this.drawerForm.aptitudeList.splice(index, 1)
    },
    setTimeLimit(time, data) {
      console.log('看看--', time, data)
      if (data) {
        return time.getTime() > Date.now() || time.getTime() < new Date(data.in_time + ' 00:00:00').getTime()
      } else {
        return time.getTime() > Date.now()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.disposition-content {
  display: grid;
  grid-auto-flow: row;
  grid-auto-columns: 150px;
  grid-template-columns: repeat(8, 1fr);
  grid-gap: 10px 40px;
  // display: flex;
}
.header-left {
  padding-left: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  &-title {
    height: 32px;
    line-height: 32px;
    margin-right: 10px;
    font-weight: 700;
  }
  &-text {
    height: 32px;
    line-height:32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-right: 20px;
  }
}
.delete {
  position: absolute;
  right: 0px;
  top: 0px;
  width: 0px;
  height: 0px;
  border-top: 18px solid #FF9B45;
  border-right: 18px solid #FF9B45;
  border-bottom: 18px solid transparent;
  border-left: 18px solid transparent;
}
::v-deep .el-dialog__body {
  padding: 0px 20px !important;
}
::v-deep .el-dialog__footer {
  padding: 0px 20px 20px !important;
}
.resignShow {
  ::v-deep .el-form-item__label {
    line-height: 0px;
  }
}

</style>
