<template>
  <customDrawer :show.sync="visible" cancelText="关闭" :confirmShow="false" :loading="isLoading" :title="'历史记录'"
    :size="size" :wrapperClosable="false" :showClose="false" @confirm="confirmDialog" @cancel="closeDialog">
    <div class="m-t-20 m-b-10 flex flex-align-c m-l-20">
      <div>时间：</div>
      <div class="m-l-10px">
        <el-date-picker v-model="searchDate" :default-value="searchDate" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
          :clearable="false"  @change="getSampleOperateLogList" :picker-options="pickerOptions" />
      </div>
    </div>

    <div class="table-content m-t-20">
      <!-- table start -->
      <el-table v-loading="isLoading" :data="historytableData" ref="historytableData" style="width: 100%" stripe
        header-row-class-name="ps-table-header-row" max-height="calc(100vh - 350px)" height="calc(100vh - 350px)">
        <el-table-column prop="operation_time" label="操作时间" align="center"></el-table-column>
        <el-table-column prop="ledger_data_type_alias" label="管理台账名称" align="center"></el-table-column>
        <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
        <el-table-column prop="context" label="操作内容" align="center">
          <template slot-scope="scope">
            <div v-for="(item,index) in getContentRecord(scope.row)" :key="index">{{ item }}</div>
          </template>
        </el-table-column>
      </el-table>
      <!-- table end -->
    </div>
    <!-- 分页 start -->
    <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100, 500]" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount" background class="ps-text" popper-class="ps-popper-select"></el-pagination>
    </div>
  </customDrawer>
  <!-- end -->
</template>

<script>
import { to } from '@/utils'
import dayjs from 'dayjs'

export default {
  name: 'LedgerPermissionRecordDrawer',
  props: {
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    isshow: Boolean
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      historytableData: [],
      searchDate: [],
      datePickerOptions: {
        // 禁用超过最近30天之前的日期
        disabledDate: time => {
          const today = dayjs()
          const thirtyDaysAgo = today.subtract(90, 'days')
          return time.getTime() < thirtyDaysAgo || time.getTime() > today.endOf('day')
        }
      },
      pickerOptions: {
        disabledDate: (time) => {
          const now = new Date()

          // 基本限制：不能选择未来时间
          if (time.getTime() > now.getTime()) {
            return true
          }

          // 如果已经选择了开始时间，限制结束时间
          if (this.tempStartDate) {
            const oneMonthLater = new Date(this.tempStartDate.getTime() + 30 * 24 * 60 * 60 * 1000)
            // 结束时间不能超过开始时间后30天，也不能早于开始时间
            return time.getTime() > Math.min(oneMonthLater.getTime(), now.getTime()) ||
                   time.getTime() < this.tempStartDate.getTime()
          }

          return false
        },
        onPick: (obj) => {
          // 当选择了开始时间后，限制结束时间最多为开始时间后一个月
          if (obj.minDate && !obj.maxDate) {
            // 临时存储选择的开始时间，用于限制结束时间
            this.tempStartDate = obj.minDate
          } else {
            // 清除临时存储
            this.tempStartDate = null
          }
        },
        shortcuts: [
          {
            text: '最近3天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3 * 24 * 60 * 60 * 1000)
              start.setHours(0, 0, 0, 0)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 7 * 24 * 60 * 60 * 1000)
              start.setHours(0, 0, 0, 0)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000)
              start.setHours(0, 0, 0, 0)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      size: '80%'
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    isshow(val) {
      if (val) {
        this.searchDate = [dayjs().subtract(3, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        this.getSampleOperateLogList()
      }
    }
  },
  created() {
  },
  mounted() { },
  methods: {
    confirmDialog(e) {
      this.visible = false
      this.$emit('confirm')
    },
    // 获取历史记录
    async getSampleOperateLogList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyOperateLogList({
          start_time: this.searchDate.length ? this.searchDate[0] + ' 00:00:00' : '',
          end_time: this.searchDate.length ? this.searchDate[1] + ' 23:59:59' : '',
          page: this.currentPage,
          page_size: this.pageSize,
          ledger_data_type: 'Permission'
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.historytableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getSampleOperateLogList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSampleOperateLogList()
    },
    closeDialog() {
      this.$emit('close')
    },
    // 获取操作内容
    getContentRecord(row) {
      if (typeof row === 'object') {
        let extra = row.extra || {}
        let afterContent = extra.after || {}
        if (afterContent && Array.isArray(afterContent)) {
          return afterContent
        }
      }
      return []
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
</style>
