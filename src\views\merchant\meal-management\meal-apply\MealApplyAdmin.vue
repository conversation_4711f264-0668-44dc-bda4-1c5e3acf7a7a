<template>
  <div class="MealApplyAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" :disabled="!!userInfo.level_tag" @click="gotoAddOrEdit('add', 'insider')" v-permission="['background_approve.approve_order_rule.add']">新增内部用户规则</button-icon>
          <button-icon color="origin" type="add" :disabled="!!userInfo.level_tag" @click="gotoAddOrEdit('add', 'outsiders')" v-permission="['background_approve.approve_order_rule.add']">新增外部用户规则</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          :key="tableKey"
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column prop="name" label="规则名称" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="user_type" label="适用用户" align="center">
            <template slot-scope="scope">
              <span>{{scope.row.user_type === 'insider' ? '内部用户' : '外部用户'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="pay_method_alias" label="支付方式" align="center"></el-table-column>
          <el-table-column prop="num_limit_alias" label="记账方式" align="center">consume_type
            <template slot-scope="scope">
              <span v-if="scope.row.rule.consume_type === 'JZ'">记账</span>
              <span v-if="scope.row.rule.consume_type === 'JC'">记次</span>
              <span v-if="scope.row.rule.consume_type === 'GD'">固定金额</span>
            </template>
          </el-table-column>
          <el-table-column prop="num_limit_alias" label="次数限制" align="center"></el-table-column>
          <el-table-column prop="need_approve" label="是否审批" align="center">
            <template slot-scope="scope">
              {{ scope.row.need_approve ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="status_alias" label="状态" align="center" width="90">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.rules_status"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
                @change="mulOperation('status', scope.row)"
                :disabled="!!userInfo.level_tag || !allPermissions.includes('background_approve.approve_order_rule.status_modify')"></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="申请码" align="center" width="150">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="openDialog('code', scope.row)"
                :disabled="!!userInfo.level_tag"
                >查看</el-button>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                :disabled="!!userInfo.level_tag"
                @click="gotoAddOrEdit('detail', scope.row.user_type, scope.row)"
                >查看</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                v-show="scope.row.status!=='enable'"
                :disabled="!!userInfo.level_tag"
                v-permission="['background_approve.approve_order_rule.modify']"
                @click="gotoAddOrEdit('edit', scope.row.user_type, scope.row)"
                >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                :disabled="!!userInfo.level_tag"
                @click="openDialog('history', scope.row)"
                >历史记录</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                v-show="scope.row.status!=='enable'"
                :disabled="!!userInfo.level_tag"
                @click="mulOperation('del', scope.row)"
                v-permission="['background_approve.approve_order_rule.delete']"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="tips">注：内部用户即项目点已注册的用户；外部用户则是未绑定项目点的用户（如游客）</div>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="400px"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false">
      <div v-if="dialogType === 'history'">
        <el-table
          :data="selectInfo.record_list"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
        <el-table-column prop="time" label="操作时间" align="center"></el-table-column>
        <el-table-column prop="content" label="内容" align="center"></el-table-column>
        <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
        </el-table>
      </div>
      <div v-if="dialogType==='code'" style="text-align: center;">
        <img :style="{width: '300px'}" :src="cameraImage" alt="">
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-btn" type="primary" @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import http from '@/utils/request'
import { debounce } from '@/utils'
import qrcode from '@chenfengyuan/vue-qrcode'
import { mapGetters } from 'vuex'
export default {
  name: 'MealApplyAdmin',
  components: { qrcode },
  data() {
    return {
      isLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: {
        user_type: {
          type: 'select',
          label: '适用用户',
          value: '',
          clearable: true,
          placeholder: '请选择适用用户',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '内部用户',
            value: 'insider'
          }, {
            label: '外部用户',
            value: 'outsiders'
          }]
        }
      },
      selectListId: [],
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      selectInfo: {},
      cameraImage: '',
      tableKey: 1
    }
  },
  mounted() {
    this.initLoad()
  },
  computed: {
    ...mapGetters(['userInfo', 'allPermissions'])
  },
  methods: {
    async initLoad() {
      this.getMealApplyList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getMealApplyList()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getMealApplyList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundApproveApproveOrderRuleListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results.map(item => {
          item.rules_status = !(item.status === 'disable')
          return item
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getMealApplyList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => { this.selectListId.push(item.id) })
    },
    // 操作提示
    mulOperation(type, data) {
      let title = '提示'
      let content = ''
      let status
      switch (type) {
        case 'del':
          content = '确定删除该就餐申请规则吗？'
          break;
        case 'status':
          if (data.status === 'disable') {
            content = '确定启用该就餐申请规则吗？'
            status = 'enable'
          } else {
            content = '确定停用该就餐申请规则吗？'
            status = 'disable'
          }
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {
              id: data.id
            }
            switch (type) {
              case 'del':
                params.status = 'delete'
                break;
              case 'status':
                params.status = status
                break;
            }
            this.confirmStatus(params)
            done()
            instance.confirmButtonLoading = false
          } else {
            this.getMealApplyList()
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    async confirmStatus(params) {
      const res = await this.$apis.apiBackgroundApproveApproveOrderRuleStatusModifyPost(params)
      if (res.code === 0) {
        this.$message.success('成功')
      } else {
        this.$message.error(res.msg)
      }
      this.getMealApplyList()
    },
    gotoAddOrEdit(type, userType, data) {
      let query = {}
      if (type === 'edit' || type === 'detail') {
        query = {
          data: JSON.stringify(data)
        }
      }
      this.$router.push({
        name: 'MerchantAddMealApply',
        params: {
          type
        },
        query: {
          ...query,
          type,
          userType
        }
      })
    },
    openDialog(type, data) {
      this.selectInfo = data
      this.dialogType = type
      if (this.dialogType === 'history') {
        this.dialogTitle = '历史记录'
        this.dialogVisible = true
      } else if (this.dialogType === 'code') {
        this.getCodeImg()
        this.dialogTitle = '查看申请码'
      }
    },
    async getCodeImg() {
      http
        .post(
          '/api/background_approve/approve_order_rule/get_qrcode_url',
          { id: this.selectInfo.id },
          { responseType: 'blob' }
        ) // arraybuffer, blob
        .then(async res => {
          if (res.type === 'application/json') {
            // 说明是普通对象数据，读取信息
            const fileReader = new FileReader()
            fileReader.onloadend = () => {
              const jsonData = JSON.parse(fileReader.result) // 后台信息
              this.$message.error(jsonData.msg)
            }
            fileReader.readAsText(res)
          } else {
            var blob = new Blob([res], { type: 'image/jpg' })
            this.cameraImage = window.URL.createObjectURL(blob)
            this.dialogVisible = true
          }
        })
        .catch(err => {
          this.isLoading = false
          console.log(err)
        })
    }
  }
}
</script>
<style lang="scss">
.MealApplyAdmin{
  .tips{
    color: #ff9b45;
    font-weight: bold;
    font-size: 16px;
    margin-top: 15px;
  }
  .el-dialog__footer{
    text-align: center;
  }
}
</style>
