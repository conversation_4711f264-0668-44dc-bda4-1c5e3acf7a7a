<template>
  <div class="nutritional-analysis" v-loading="isLoading">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="tab-box m-b-20">
      <el-radio-group v-model="tabType" class="ps-radio" @change="changeTabHandle">
      <el-radio-button label="meal">按餐段</el-radio-button>
      <el-radio-button label="day">按天</el-radio-button>
      <el-radio-button label="week">按自然周</el-radio-button>
    </el-radio-group>
    </div>
    <div class="nutritional-content">
      <div class="picker-panel">
        <div class="picker-date">
          <div class="picker-left">
            <el-popover
              placement="bottom-start"
              trigger="click"
              v-model="visible">
              <month-table
                @pick="handleMonthPick"
                :value="date"
                :default-value="defaultValue ? defaultValue : null"
                :show-date="date"
                :start="startDate"
                :end="endDate"
                :disabled-date="disabledMonthHandle">
              </month-table>
              <div slot="reference">
                <div class="el-date-picker__header-label">
                  {{ date | formatDate('YYYY年MM月') }}<i class="el-icon-caret-bottom"></i>
                </div>
              </div>
            </el-popover>
            <!-- <el-date-picker
              v-model="monthValue"
              type="month"
              :clearable="false"
              placeholder="选择月"
              :picker-options="pickerOptions"
              @change="changePickerMonth"
              class="no-show"
              >
            </el-date-picker> -->
          </div>
          <!-- v-if="tabType === 'week'" -->
          <div class="picker-right" v-if="tabType === 'week' && weekNumber">
            第{{ weekNumber }}周
            <!-- <i class="el-icon-caret-left pointer" @click="prevHandle"></i>&nbsp;&nbsp;&nbsp;&nbsp;
            <i class="el-icon-caret-right pointer" @click="nextHandle"></i> -->
          </div>
        </div>
        <date-table
          @pick="handleDatePick"
          :selection-mode="dateType"
          :first-day-of-week="firstDayOfWeek"
          :value="value"
          :default-value="defaultValue ? defaultValue : null"
          :date="date"
          :cell-class-name="cellClassName"
          :disabled-date="disabledDateHandle">
        </date-table>
        <div class="tip">
          <span class=""><span class="color-box blue"></span>已记录</span>
          <span class="m-l-10"><span class="color-box origin"></span>当前选择</span>
        </div>
      </div>
      <div class="content p-20">
        <div v-if="tabType === 'meal'" class="meal-box m-b-20 p-bg-radius">
          <el-radio-group v-model="mealType" class="ps-radio" size="small" @change="changeMealHandle">
            <el-radio-button label="breakfast">早餐</el-radio-button>
            <el-radio-button label="lunch">午餐</el-radio-button>
            <el-radio-button label="dinner">晚餐</el-radio-button>
          </el-radio-group>
        </div>
        <div v-if="tabType === 'meal'">
          <div class="analysis-title">
            菜品实况图（{{foodSceneImages.length}}）
          </div>
          <!-- 菜品实况图 start -->
          <div class="food-image p-bg-radius m-t-20 m-b-20">
            <div v-if="foodSceneImages.length">
              <el-image
                v-for="(img, index) in foodSceneImages"
                :key="img"
                style="width: 194px; height: 103px; vertical-align: middle;"
                :src="img"
                fit="cover"
                class="radius-6 m-r-16 m-b-10"
                :preview-src-list="foodSceneImages"
                :initial-index="index"
                >
              </el-image>
            </div>
            <el-empty v-else :image-size="60"></el-empty>
          </div>
        </div>
        <!-- 菜品图片 end -->
        <div class="food-data" v-if="tabType !== 'week'">
          <div class="analysis-title">
            食物清单（{{foodMenu.length}}）
          </div>
          <div class="m-t-20 m-b-20">
            <div class="food-list p-bg-radius" v-if="foodMenu.length">
              <div class="food-box radius-6 m-r-18" v-for="(food, index) in foodMenu" :key="index">
                <div class="food-name ellipsis">{{ food.name }}</div>
                <div class="food-weight">{{ food.weight }}g</div>
              </div>
            </div>
            <div v-else class="p-bg-radius p-t-10 p-b-10">
              <el-empty :image-size="60"></el-empty>
            </div>
          </div>
        </div>
        <div class="food-data" v-if="tabType !== 'meal'">
          <div class="analysis-title">
            饮食结构
          </div>
          <div class="m-t-20 m-b-20">
            <div class="food-list p-bg-radius" v-if="foodDiversity.length">
              <div class="food-diversity radius-6 m-r-18" v-for="(food, index) in foodDiversity" :key="index">
                <div class="food-name">
                  {{ food.text }}
                  <span>{{ food.value }}g</span>
                  <span>/</span>
                  <span v-if="food.range">
                    {{ food.range[0] + '~' + food.range[1] }}g
                  </span>
                  <i v-if="diffTopHandle(food)" class="el-icon-top m-l-20" style="color: #d9001b;"></i>
                  <i v-if="diffBootomHandle(food)" class="el-icon-bottom m-l-20" style="color: #f59a23;"></i>
                </div>
              </div>
            </div>
            <div v-else class="p-bg-radius p-t-10 p-b-10">
              <el-empty :image-size="60"></el-empty>
            </div>
          </div>
        </div>
        <div class="diversity-nutrition" v-if="tabType !== 'meal'">
          <div class="analysis-title">
            总能量分析
          </div>
          <div class="m-t-20 m-b-20">
            <div class="food-list p-bg-radius" v-if="energyKcalData.length">
              <div class="food-diversity radius-6 m-r-18" v-for="(food, index) in energyKcalData" :key="index">
                <div class="food-name">
                  {{ food.text }}：
                  <span>{{ food.value }}g</span>
                  <span>/</span>
                  <span v-if="food.range">
                    {{ food.range[0] + '~' + food.range[1] }}g
                  </span>
                  <i v-if="diffTopHandle(food)" class="el-icon-top m-l-20" style="color: #d9001b;"></i>
                  <i v-if="diffBootomHandle(food)" class="el-icon-bottom m-l-20" style="color: #f59a23;"></i>
                </div>
              </div>
            </div>
            <div v-else class="p-bg-radius p-t-10 p-b-10">
              <el-empty :image-size="60"></el-empty>
            </div>
          </div>
        </div>
        <div>
          <div class="analysis-title">
            能量来源
            <span v-if="tabType === 'meal' && Object.keys(energyKcal).length && energyKcal.value">（能量摄入：{{ `${energyKcal.value} / ${energyKcal.range[0]}~${energyKcal.range[1]} kcal` }}）</span>
          </div>
          <div class="analysis-kcal m-t-20 p-bg-radius">
            <el-table
              :data="threeNutrition"
              ref="tableData"
              style="width: 100%"
              stripe
              header-row-class-name="ps-table-header-row"
              row-key="id"
              border
            >
              <table-column v-for="item in tableSettings" :key="item.key" :col="item">
                <template #value="{ row }">
                  <span>{{ row.value }}g</span>
                  <i v-if="diffTopHandle(row)" class="el-icon-top" style="color: #d9001b;"></i>
                  <i v-if="diffBootomHandle(row)" class="el-icon-bottom" style="color: #f59a23;"></i>
                </template>
                <template #range="{ row }">
                  <span>{{ row.range[0] + '~' + row.range[1] + 'g' }}</span>
                </template>
              </table-column>
            </el-table>
          </div>
        </div>
        <div class="m-t-20" v-if="tabType !== 'meal'">
          <div class="analysis-title">
            营养素分析
          </div>
          <div class="analysis-kcal m-t-20 p-bg-radius">
            <el-table
              :data="totalNutrition"
              ref="tableData"
              style="width: 100%"
              stripe
              header-row-class-name="ps-table-header-row"
              row-key="id"
              border
            >
              <table-column v-for="item in totalNutritionTableSettings" :key="item.key" :col="item">
                <template #value="{ row }">
                  <span>{{ row.value ? row.value + row.unit : '--' }}</span>
                  <i v-if="diffTopHandle(row)" class="el-icon-top" style="color: #d9001b;"></i>
                  <i v-if="diffBootomHandle(row)" class="el-icon-bottom" style="color: #f59a23;"></i>
                </template>
                <template #value1="{ row }">
                  <span>{{ row.value_1 ? row.value_1 + row.unit_1 : '--' }}</span>
                  <i v-if="diffTopHandle(row, 'value_1')" class="el-icon-top" style="color: #d9001b;"></i>
                  <i v-if="diffBootomHandle(row, 'value_1')" class="el-icon-bottom" style="color: #f59a23;"></i>
                </template>
                <template #range="{ row }">
                  <span>{{ row.range[0] + '~' + row.range[1] + row.unit }}</span>
                </template>
              </table-column>
            </el-table>
          </div>
        </div>
        <div class="nutrition-analyze m-t-20" v-if="tabType !== 'meal'">
          <div class="analysis-title">
            饮食分析
          </div>
          <div class="m-t-20 m-b-20">
            <div class="padding-box p-bg-radius" v-if="nutritionAnalyze.length">
              <div class="nutrition-analyze-item radius-6 m-r-18 m-b-6" v-for="(analyze, index) in nutritionAnalyze" :key="index">
                {{ (index + 1) + '：' + analyze }}
              </div>
            </div>
            <div v-else class="p-bg-radius p-t-10 p-b-10">
              <el-empty :image-size="60"></el-empty>
            </div>
          </div>
        </div>
        <div class="nutrition-analyze m-t-20" v-if="tabType === 'week'">
          <div class="analysis-title">
            指导建议
          </div>
          <div class="m-t-20 m-b-20">
            <div class="padding-box p-bg-radius" v-if="nutritionGuid.length">
              <div class="nutrition-analyze-item radius-6 m-r-18 m-b-6" v-for="(analyze, index) in nutritionGuid" :key="index">
                {{ (index + 1) + '：' + analyze }}
              </div>
            </div>
            <div v-else class="p-bg-radius p-t-10 p-b-10">
              <el-empty :image-size="60"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DateTable from '../components/DateTable';
import MonthTable from '../components/MonthTable';
// import VirtualListSelect from '@/components/VirtualListSelect'
import { deepClone } from '@/utils'
import { getWeekNumber } from '@/utils/date'
import { prevMonth, nextMonth } from 'element-ui/src/utils/date-util'
import * as dayjs from 'dayjs'

export default {
  name: '',
  components: {
    DateTable,
    MonthTable
    // VirtualListSelect
  },
  props: {

  },
  data() {
    return {
      isLoading: false,
      id: '', // id
      tabType: 'meal', // 默认显示类型为餐段
      monthValue: '', // 年月选中的数据
      dateType: 'day', // 左侧日历的类型，日/周的模式
      firstDayOfWeek: 7, // 一周的以什么为开始
      value: new Date(), // 选中的日期
      defaultValue: [], // 默认高亮的日期
      date: new Date(), // 当前显示的日历日期年月
      cellClassName: '',
      selectOption: {
        label: 'name', // 下拉框需要显示的名称
        value: 'id', // 下拉框绑定的值
        isRight: false // 右侧是否显示
      },
      mealType: 'breakfast',
      tableSettings: [
        { label: '三大供能营养素', key: 'text' },
        { label: '摄入量', key: 'value', type: "slot", slotName: "value" },
        { label: '推荐量', key: 'range_text' },
        { label: '摄入供能比', key: 'value_rate_text' },
        { label: '推荐供能比', key: 'range_rate_text' }
        // { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      totalNutritionTableSettings: [
        { label: '营养素名称', key: 'text' },
        { label: '摄入量', key: 'value', type: "slot", slotName: "value" },
        { label: '推荐量', key: 'range_text' },
        { label: '营养素名称', key: 'text_1' },
        { label: '摄入量', key: 'value_1', type: "slot", slotName: "value1" },
        { label: '推荐量', key: 'range_text_1' }
        // { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      pickerOptions: {
        disabledDate(time) { // 时间从2023年开始
          return time.getTime() < new Date('2023/01/01').getTime();
        }
      },
      detailInfo: {}, // 餐段详情
      weekNumber: '',
      allowDate: [], // 允许点击的日期
      foodSceneImages: [], // 菜品实况图
      foodMenu: [], // 食物清单
      energyKcal: { // 能量摄入
        value: 0,
        range: [0, 0]
      },
      threeNutrition: [], // 三大营养
      foodDiversity: [], // 饮食结构
      totalNutrition: [], // 营养素分析
      nutritionAnalyze: [], // 饮食分析
      energyKcalData: [], // 总能量分析
      nutritionGuid: [], // 指导建议
      visible: false,
      startDate: '2023-01-01',
      endDate: new Date()
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    // 初始化
    async initLoad() {
      if (this.$route.query.id) {
        this.id = this.$route.query.id
      }
      if (this.$route.query.type) {
        this.tabType = this.$route.query.type
      }
      if (this.$route.query.date) {
        this.date = this.$route.query.date
      }
      if (this.id) {
        this.changeTabHandle(this.tabType)
      }
    },
    // 饮食分析类型
    async changeTabHandle() {
      this.clearData()
      switch (this.tabType) {
        case 'meal':
          this.dateType = 'day'
          await this.getDayCalendarData()
          this.getMealAnalysisData()
          break;
        case 'day':
          this.dateType = 'day'
          await this.getDayCalendarData()
          this.getadyAnalysisData()
          break;
        case 'week':
          this.dateType = 'week'
          await this.getWeekCalendarData()
          this.getWeekAnalysisData()
          break;
      }
    },
    // 年月change
    changePickerMonth(e) {
      this.date = e
      this.changeTabHandle(this.tabType)
    },
    refreshHandle() {
      this.changeTabHandle(this.tabType)
    },
    handleDatePick(e) {
      console.log(e)
      if (this.dateType === 'day') {
        this.value = e
      } else if (this.dateType === 'week') {
        // 周需要往前倒一天，因为控件默认从周3开始，但后台的数据默认周是从周日开始
        this.value = new Date(e.date.getTime() - 24 * 60 * 60 * 1000)
        this.weekNumber = e.week
      }
      this.clearData()
      this.isLoading = true
      switch (this.tabType) {
        case 'meal':
          this.dateType = 'day'
          this.getMealAnalysisData()
          break;
        case 'day':
          this.dateType = 'day'
          this.getadyAnalysisData()
          break;
        case 'week':
          this.dateType = 'week'
          this.getWeekAnalysisData()
          break;
      }
    },
    async handleMonthPick(e) {
      console.log(e)
      this.visible = false
      this.date = new Date(e.year + '-' + e.month + '-01')
      this.changeTabHandle()
    },
    changeMealHandle(e) {
      this.foodSceneImages = this.detailInfo[this.mealType].food_scene_images ? this.detailInfo[this.mealType].food_scene_images : []
      this.foodMenu = this.detailInfo[this.mealType].food_menu
      this.energyKcal = this.detailInfo[this.mealType].energy_kcal
      if (this.energyKcal.value) {
        this.threeNutrition = this.detailInfo[this.mealType].three_nutrition.map(v => {
          v.value_rate_text = `${v.value_rate}%`
          v.range_text = `${v.range[0]}~${v.range[1]}g`
          v.range_rate_text = `${v.range_rate[0]}~${v.range_rate[1]}%`
          return v
        })
      } else {
        this.threeNutrition = []
      }
    },
    // 获取周的数据
    async getMealAnalysisData() {
      if (!this.value) return
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminHealthyInfoMealTypeNutritionAnalyzePost({
        id: +this.id,
        date: dayjs(this.value).format('YYYY-MM-DD')
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data && Object.keys(res.data).length) {
          this.detailInfo = res.data
          this.foodSceneImages = this.detailInfo[this.mealType].food_scene_images ? this.detailInfo[this.mealType].food_scene_images : []
          this.foodMenu = this.detailInfo[this.mealType].food_menu
          this.energyKcal = this.detailInfo[this.mealType].energy_kcal
          if (this.energyKcal.value) {
            this.threeNutrition = this.detailInfo[this.mealType].three_nutrition.map(v => {
              v.value_rate_text = `${v.value_rate}%`
              v.range_text = `${v.range[0]}~${v.range[1]}g`
              v.range_rate_text = `${v.range_rate[0]}~${v.range_rate[1]}%`
              return v
            })
          } else {
            this.threeNutrition = []
          }
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取获取日的数据
    async getDayCalendarData() {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminHealthyInfoDayNutritionAnalyzeCalendarPost({
        id: +this.id,
        year: dayjs(this.date).year(),
        month: dayjs(this.date).month() + 1
      }))
      // this.isLoading = false
      if (err) {
        this.isLoading = false
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data) {
          // this.mealDetail = res.data
          this.allowDate = res.data
          this.defaultValue = res.data.filter(v => {
            let data = new Date(v).getTime()
            let now = new Date(dayjs(new Date()).format('YYYY-MM-DD')).getTime()
            return now >= data
          })
          this.defaultValue = []
          res.data.forEach(v => {
            let data = new Date(v).getTime()
            let now = new Date(dayjs(new Date()).format('YYYY-MM-DD')).getTime()
            if (now >= data) {
              this.defaultValue.push(new Date(v))
            }
          })
          if (this.defaultValue.length) {
            // 默认拿最新那条
            this.value = this.defaultValue[this.defaultValue.length - 1]
            this.date = new Date(this.value)
          } else {
            this.value = ''
            this.isLoading = false
          }
        }
      } else {
        this.isLoading = false
        this.$message.error(res.msg)
      }
    },
    disabledDateHandle(time) {
      return !this.allowDate.includes(dayjs(time).format('YYYY-MM-DD')) || time > new Date().getTime()
    },
    disabledMonthHandle(time) {
      return this.getYearMonthTime(this.startDate) > this.getYearMonthTime(time) || this.getYearMonthTime(this.endDate) < this.getYearMonthTime(time)
    },
    getYearMonthTime(date) {
      let time = new Date(date)
      return Number(`${time.getFullYear()}${time.getMonth() + 1}`)
    },
    // 获取获取日的数据
    async getadyAnalysisData() {
      if (!this.value) return
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminHealthyInfoDayNutritionAnalyzePost({
        id: +this.id,
        date: dayjs(this.value).format('YYYY-MM-DD')
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data && Object.keys(res.data).length) {
          this.detailInfo = res.data
          this.foodSceneImages = []
          this.foodMenu = this.detailInfo.food_data
          this.energyKcal = {}
          this.threeNutrition = this.detailInfo.three_nutrition.map(v => {
            v.value_rate_text = `${v.value_rate}%`
            v.range_text = v.range && v.range.length > 1 ? `${v.range[0]}~${v.range[1]}g` : ''
            v.range_rate_text = `${v.range_rate[0]}~${v.range_rate[1]}%`
            return v
          })
          if (this.detailInfo.nutrition_analyze_text) {
            this.nutritionAnalyze = []
            for (const key in this.detailInfo.nutrition_analyze_text) {
              const item = this.detailInfo.nutrition_analyze_text[key];
              if (item.length) {
                this.nutritionAnalyze.push(...item)
              }
            }
          }
          this.foodDiversity = this.detailInfo.food_diversity.data || []
          // 定义下初始index，用来排序
          let mealIndex = { all: 1, breakfast: 2, lunch: 3, dinner: 4 }
          this.energyKcalData = this.detailInfo.energy_kcal_data.sort(((a, b) => {
            return mealIndex[a.meal_type] - mealIndex[b.meal_type]
          }))

          this.totalNutrition = []
          let totalNutritionItem = {}
          // 一行显示两条数据
          this.detailInfo.total_nutrition.forEach((v, i) => {
            // 偶数添加一条数据
            if ((i + 1) % 2 === 0) {
              this.totalNutrition.push({
                ...deepClone(totalNutritionItem),
                text_1: v.text,
                value_1: v.value,
                unit_1: v.unit,
                range_1: v.range,
                range_text_1: `${v.range[0]}~${v.range[1]}${v.unit}`
              })
              totalNutritionItem = {}
            } else {
              totalNutritionItem = { ...v, range_text: `${v.range[0]}~${v.range[1]}${v.unit}` }
              // 边界处理，总数据为单数时
              if ((this.detailInfo.total_nutrition.length - 1) === i) {
                this.totalNutrition.push({
                  ...totalNutritionItem
                })
              }
            }
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取获取周的数据
    async getWeekCalendarData() {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminHealthyInfoWeeklyNutritionAnalyzeCalendarPost({
        id: +this.id,
        year: dayjs(this.date).year(),
        month: dayjs(this.date).month() + 1
      }))
      // this.isLoading = false
      if (err) {
        this.isLoading = false
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data) {
          // this.mealDetail = res.data
          this.allowDate = []
          let diff = 24 * 60 * 60 * 1000
          res.data.forEach((item, index) => {
            if (!index) this.date = new Date(item)
            this.allowDate.push(item)
            let time = new Date(item).getTime()
            for (let index = 1; index <= 6; index++) {
              const d = time + index * diff;
              this.allowDate.push(dayjs(d).format('YYYY-MM-DD'))
            }
          });
          if (!res.data.length) {
            this.value = ''
            this.isLoading = false
            this.weekNumber = ''
          } else {
            this.value = new Date(res.data[res.data.length - 1])
            this.date = this.value
            // 拿回来的数据是以周日为开始的要加一天计算是第几周，因为计算的方法是以周一为一周的开始
            let time = new Date(this.value).getTime() + 24 * 60 * 60 * 1000
            this.weekNumber = getWeekNumber(new Date(time))
            // if (res.data.length === 1) {
            //   this.date = new Date(res.data[0])
            // }
          }
          // this.defaultValue = res.data.map(v => new Date(v))
          this.defaultValue = []
        }
      } else {
        this.isLoading = false
        this.$message.error(res.msg)
      }
    },
    // 获取获取日的数据
    async getWeekAnalysisData() {
      if (!this.value) return
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminHealthyInfoWeeklyNutritionAnalyzePost({
        id: +this.id,
        date: dayjs(this.value).format('YYYY-MM-DD')
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodSceneImages = []
        this.foodMenu = []
        this.energyKcal = {}
        this.totalNutrition = []
        if (res.data && Object.keys(res.data).length) {
          // return
          this.detailInfo = res.data
          this.threeNutrition = this.detailInfo.three_nutrition.map(v => {
            v.value_rate_text = `${v.value_rate}%`
            v.range_text = v.range && v.range.length > 1 ? `${v.range[0]}~${v.range[1]}${v.unit}` : ''
            v.range_rate_text = `${v.range_rate[0]}~${v.range_rate[1]}%`
            return v
          })
          if (this.detailInfo.nutrition_analyze_text) {
            this.nutritionAnalyze = []
            for (const key in this.detailInfo.nutrition_analyze_text) {
              const item = this.detailInfo.nutrition_analyze_text[key];
              if (item.length) {
                this.nutritionAnalyze.push(...item)
              }
            }
          }
          this.foodDiversity = this.detailInfo.food_diversity ? this.detailInfo.food_diversity.data : []
          // 定义下初始index，用来排序
          let mealIndex = { all: 1, breakfast: 2, lunch: 3, dinner: 4 }
          this.energyKcalData = this.detailInfo.energy_kcal_data.sort(((a, b) => {
            return mealIndex[a.meal_type] - mealIndex[b.meal_type]
          }))

          let totalNutritionItem = {}
          // 一行显示两条数据
          this.detailInfo.total_nutrition.forEach((v, i) => {
            // 偶数添加一条数据
            if ((i + 1) % 2 === 0) {
              this.totalNutrition.push({
                ...deepClone(totalNutritionItem),
                text_1: v.text,
                value_1: v.value,
                unit_1: v.unit,
                range_1: v.range,
                range_text_1: `${v.range[0]}~${v.range[1]}${v.unit}`
              })
              totalNutritionItem = {}
            } else {
              totalNutritionItem = { ...v, range_text: `${v.range[0]}~${v.range[1]}${v.unit}` }
              // 边界处理，总数据为单数时
              if ((this.detailInfo.total_nutrition.length - 1) === i) {
                this.totalNutrition.push({
                  ...totalNutritionItem
                })
              }
            }
          })
          this.nutritionGuid = this.detailInfo.nutrition_guid_text
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    diffTopHandle(row, key) {
      if (!key) key = 'value'
      if (key === 'value_1') {
        if (!row.range_1) return false
        return row[key] > row.range_1[1]
      } else {
        if (!row.range) return false
        return row[key] > row.range[1]
      }
    },
    diffBootomHandle(row, key) {
      if (!key) key = 'value'
      if (key === 'value_1') {
        if (!row.range_1) return false
        return row[key] < row.range_1[0]
      } else {
        if (!row.range) return false
        return row[key] < row.range[0]
      }
    },
    // 上一个日期
    prevHandle() {
      this.date = prevMonth(this.date)
    },
    // 下一个日期
    nextHandle() {
      this.date = nextMonth(this.date)
    },
    changeHash(query) {
      this.$router.push({
        name: "SuperNutritionalAnalysis",
        query: {
          id: this.id,
          type: this.tabType, // tab类型
          date: this.showDate // 显示的日期
        }
      })
    },
    // 请除数据
    clearData() {
      this.detailInfo = {}
      this.foodMenu = []
      this.energyKcal = { // 能量摄入
        value: 0,
        range: [0, 0]
      }
      this.threeNutrition = []
      this.foodDiversity = []
      this.totalNutrition = []
      this.nutritionAnalyze = []
      this.energyKcalData = []
      this.nutritionGuid = []
    }
  }
};
</script>

<style scoped lang="scss">
.nutritional-analysis {
  .el-empty{
    padding: 0;
    margin-bottom: 10px;
  }
  .p-bg-radius{
    background-color: rgba(246, 247, 251, 1);
    border-radius: 6px;
  }
  .radius-6 {
    border-radius: 6px;
  }
  .tab-box {
    ::v-deep.el-radio-button__inner {
      display: inline-block;
      min-width: 100px;
    }
  }
  .picker-panel{
    width: 292px;
    padding: 20px;
    border-radius: 12px;
    background-color: #fff;
    .picker-date{
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .picker-left{
      position: relative;
    }
    .no-show{
      position: absolute;
      left: 0;
      top: 0;
      width: 100px;
      opacity: 0;
      cursor: pointer;
      ::v-deep.el-input__inner{
        cursor: pointer;
      }
    }
    .tip{
      margin-top: 20px;
      .color-box{
        display: inline-block;
        width: 14px;
        height: 14px;
        vertical-align: middle;
        margin-right: 4px;
        &.origin{
          background-color: #ff9b45;
        }
        &.blue{
          margin-left: 10px;
          background-color: #429cf5;
        }
      }
    }
  }
  .nutritional-content{
    display: flex;
    justify-content: space-between;
    .content{
      flex: 1;
      margin-left: 20px;
      border-radius: 12px;
      // overflow: hidden;
      background-color: #fff;
      max-height: calc(100vh - 270px);
      overflow-y: auto;
      .meal-box{
        display: inline-block;
        padding: 10px 20px;
        ::v-deep.el-radio-button__inner {
          display: inline-block;
          min-width: 80px;
        }
      }
      .food-image{
        padding: 20px 20px 10px;
      }
      .food-list{
        padding: 16px 20px 6px;
        display: flex;
        flex-wrap: wrap;
      }
      .padding-box{
        padding: 16px 20px 6px;
      }
      .food-box {
        width: 190px;
        padding: 6px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        font-size: 12px;
        .food-name{
          flex: 1;
          margin-right: 20px;
        }
      }
      .food-diversity{
        min-width: 190px;
        padding: 6px;
        margin-bottom: 10px;
        background-color: #fff;
        font-size: 12px;
      }
      .analysis-kcal{
        padding: 20px;
      }
      .el-icon-top, .el-icon-bottom{
        font-weight: 900;
      }
      .nutrition-analyze{
        font-size: 14px;
      }
    }
  }
}
</style>
