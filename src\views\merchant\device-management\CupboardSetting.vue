<template>
  <div class="RoutineSetting container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper" style="margin-top: 0px; margin-bottom: 50px">
      <div class="table-header" style="display: flex; justify-content: space-between">
        <div style="display: flex; align-items: center">
          <div class="table-title">适用组织：</div>
          <organization-select
            class="search-item-w ps-input w-250"
            placeholder="请选择所属组织"
            :isLazy="false"
            :multiple="false"
            :check-strictly="true"
            v-model="organizationId"
            @change="changeOrganization"
            :append-to-body="true"
          ></organization-select>
        </div>
        <div style="padding-right: 20px">
          <el-button size="small" type="primary" class="ps-origin-btn" @click="checkForm('self')">
            保存
          </el-button>
          <el-button size="small" type="primary" class="ps-plain-btn" @click="openOtherOrg">
            适用到其他组织
          </el-button>
        </div>
      </div>
      <div class="setting-wrap">
        <el-form :model="settingForm" :rules="settingFormRule" ref="settingForm">
          <el-form-item prop="ceilDealType" label="存餐规则" label-width="85px">
            <!-- {{ !autoSaveInCupboard }} -->
            <!-- {{ settingForm.canReuse  }} -->
            <!-- {{ !autoSaveInCupboard && settingForm.canReuse }} -->
            <el-radio-group class="ps-radio p-r-30" v-model="settingForm.ceilDealType">
              <el-radio label="random" :disabled="!autoSaveInCupboard || settingForm.canReuse">手动存餐</el-radio>
              <el-radio label="auto">自动存餐</el-radio>
            </el-radio-group>
            <el-form-item prop="cupboardSaveFoodType" label="" label-width="0">
              <el-radio-group
                v-if="settingForm.ceilDealType === 'auto'"
                class="ps-radio p-r-30"
                v-model="settingForm.cupboardSaveFoodType"
              >
              <!-- autoSaveInCupboard 是0的时候禁用 -->
                <el-radio label="all_save"  :disabled="!autoSaveInCupboard">下单即刻分柜</el-radio>
              <!-- autoSaveInCupboard 是1的时候禁用 -->
                <el-radio label="pay_time_save" :disabled="autoSaveInCupboard ? true : false">
                  按照下单时间分柜
                </el-radio>
                <el-radio label="auto_save_delay" :disabled="autoSaveInCupboard ? true : false">
                  依据菜品销量分柜-顺延
                </el-radio>
                <el-radio label="auto_save" :disabled="autoSaveInCupboard ? true : false">
                  依据菜品销量分柜-不顺延
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form-item>
          <div
            v-if="
              !settingForm.ceilDealType === 'random' ||
              (settingForm.ceilDealType === 'auto' &&
                settingForm.cupboardSaveFoodType !== 'all_save')
            "
          >
            <div class="title">自动存餐时间（餐段开始前）</div>
            <el-form-item label-width="40px">
              <div class="fake-table-wrapper">
                <div class="fake-table">
                  <div class="fake-col" v-for="(mt, key, index) in mealType" :key="index">
                    <span class="fake-col-title">{{ key }}</span>
                    <el-input-number
                      controls-position="right"
                      v-model="settingForm[`${mt}_ahead`]"
                      :min="1"
                      size="mini"
                      style="width: 80px; margin: 10px"
                    ></el-input-number>
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
          <el-form-item
            prop="canReuse"
            label="当前餐段重复使用餐柜"
            label-width="170px"
            v-if="settingForm.ceilDealType === 'auto'"
          >
            <el-switch
              v-model="settingForm.canReuse"
              active-color="#ff9b45"
              @change="changeCanReuse"
            ></el-switch>
            <span class="tips">注：关闭后，如用户退款或取餐，当前餐段的餐柜不可以重复使用。</span>
          </el-form-item>
          <el-form-item label="取餐柜自动释放" prop="isAutoRelease" label-width="130px">
            <el-switch v-model="settingForm.isAutoRelease" active-color="#ff9b45"></el-switch>
          </el-form-item>
          <!-- <div class="tips">注：开启后自动核销，允许餐段结束相差时间内释放所有餐格</div> -->
          <el-form-item label="餐段结束后" prop="autoReleaseHour" label-width="170px">
            <el-input-number
              class="ps-input-number"
              v-model="settingForm.autoReleaseHour"
              :min="0"
              :max="120"
              label=""
            ></el-input-number>
            <span>分钟释放所有格子，且未被取走的订单状态变更为“已过期”</span>
          </el-form-item>
          <!-- <el-form-item prop="ceilDealType" label="后台分配柜子" label-width="115px">
            <el-radio-group class="ps-radio" v-model="settingForm.ceilDealType">
              <el-radio :disabled="settingForm.canReuse" label="random">手动</el-radio>
              <el-radio label="auto">自动</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <div class="title">取餐柜餐格存餐数量：</div>
          <el-form-item label="每个小餐格(250*180*320mm)可存餐数量：" prop="littleCeilSize">
            <el-input-number
              class="ps-input-number"
              v-model="settingForm.littleCeilSize"
              :min="1"
              :max="999"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="每个大餐格(250*252*320mm)可存餐数量：" prop="bigCeilSize">
            <el-input-number
              class="ps-input-number"
              v-model="settingForm.bigCeilSize"
              :min="1"
              :max="999"
            ></el-input-number>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <setting-dialog
      :isshow.sync="dialogVisible"
      :type="dialogType"
      :title="dialogTitle"
      :confirm="dialogConfirm"
      @otherOrgConfirm="otherOrgConfirm"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import SettingDialog from './components/SettingDialog.vue'
import OrganizationSelect from '@/components/OrganizationSelect'
import { MEAT_TYPE_MAP } from '@/utils/constants'
export default {
  name: 'RoutineSetting',
  components: {
    OrganizationSelect,
    SettingDialog
  },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      organizationId: '',
      organizationIds: [],
      settingForm: {
        canReuse: false,
        ceilDealType: '',
        isAutoRelease: false,
        autoReleaseHour: 1,
        littleCeilSize: 1,
        bigCeilSize: 1,
        cupboardSaveFoodType: '',
        breakfast_ahead: 0, // 餐段结束前(早餐)
        lunch_ahead: 0, // 餐段结束前(午餐)
        afternoon_ahead: 0, // 餐段结束前(下午茶)
        dinner_ahead: 0, // 餐段结束前(晚餐)
        supper_ahead: 0, // 餐段结束前(夜宵)
        morning_ahead: 0 // 餐段结束前(凌晨餐)
      },
      settingFormRule: {
        ceilDealType: [{ required: true, message: '请先选择' }],
        cupboardSaveFoodType: [{ required: true, message: '请先选择' }],
        autoReleaseHour: [{ required: true, message: '请输入整数' }],
        littleCeilSize: [{ required: true, message: '请输入整数' }],
        bigCeilSize: [{ required: true, message: '请输入整数' }]
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      mealType: MEAT_TYPE_MAP,
      autoSaveInCupboard: 0
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.organizationId = Number(sessionStorage.getItem('organization'))
      this.getSettingDetail()
    },
    // 刷新页面
    refreshHandle() {
      this.initLoad()
    },
    checkForm(type) {
      this.$refs.settingForm.validate(valid => {
        if (valid) {
          let params = {
            can_reuse: this.settingForm.canReuse,
            is_auto_release: this.settingForm.isAutoRelease,
            auto_release_hour: this.settingForm.autoReleaseHour,
            ceil_deal_type: this.settingForm.ceilDealType,
            little_ceil_size: this.settingForm.littleCeilSize,
            big_ceil_size: this.settingForm.bigCeilSize,
            cupboard_save_food_type: this.settingForm.cupboardSaveFoodType,
            breakfast_ahead: this.settingForm.breakfast_ahead,
            lunch_ahead: this.settingForm.lunch_ahead,
            afternoon_ahead: this.settingForm.afternoon_ahead,
            dinner_ahead: this.settingForm.dinner_ahead,
            supper_ahead: this.settingForm.supper_ahead,
            morning_ahead: this.settingForm.morning_ahead
          }
          if (type === 'self') {
            if (!this.organizationId) {
              this.$message.error('请选择适用组织')
              return
            }
            params.org_nos = [this.organizationId]
          } else {
            params.org_nos = this.organizationIds
          }
          this.saveSetting(params)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    async saveSetting(params) {
      const res = await this.$apis.apiBackgroundDeviceCupboardConfAddPost(params)
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.getSettingDetail()
      } else {
        this.$message.error(res.msg)
      }
    },
    async getSettingDetail() {
      const res = await this.$apis.apiBackgroundDeviceCupboardConfDetailsPost({
        org_no: this.organizationId
      })
      if (res.code === 0) {
        if (Object.keys(res.data).length) {
          this.settingForm.canReuse = res.data.can_reuse
          this.settingForm.isAutoRelease = res.data.is_auto_release
          this.settingForm.autoReleaseHour = res.data.auto_release_hour
          this.settingForm.ceilDealType = res.data.ceil_deal_type
          this.settingForm.littleCeilSize = res.data.little_ceil_size
          this.settingForm.bigCeilSize = res.data.big_ceil_size
          this.settingForm.breakfast_ahead = res.data.breakfast_ahead
          this.settingForm.lunch_ahead = res.data.lunch_ahead
          this.settingForm.afternoon_ahead = res.data.afternoon_ahead
          this.settingForm.dinner_ahead = res.data.dinner_ahead
          this.settingForm.supper_ahead = res.data.supper_ahead
          this.settingForm.morning_ahead = res.data.morning_ahead
          this.settingForm.cupboardSaveFoodType = res.data.cupboard_save_food_type
        }
        //  else {
        //   this.resetForm()
        // }
        this.getCupboardOrgSettings()
      } else {
        this.$message.error(res.msg)
      }
    },

    async getCupboardOrgSettings() {
      const res = await this.$apis.apiBackgroundDeviceCupboardConfGetCupboardOrgSettingsPost({
        org_no: this.organizationId
      })
      if (res.code === 0) {
        // 系统管理-常规操作-是否需要用户选择取餐柜
        this.autoSaveInCupboard = res.data.auto_save_in_cupboard
        // if (this.settingForm.cupboardSaveFoodType === 'all_save' && !res.data.auto_save_in_cupboard) {
        //   this.settingForm.cupboardSaveFoodType = 'pay_time_save'
        // }
      } else {
        this.$message.error(res.msg)
      }
    },
    openOtherOrg() {
      this.dialogVisible = true
      this.dialogTitle = '请选择适用的组织'
      this.dialogType = 'other'
    },
    dialogConfirm() {
      this.dialogVisible = false
    },
    otherOrgConfirm(val) {
      this.dialogVisible = false
      this.organizationIds = val
      this.checkForm('other')
    },
    changeOrganization() {
      this.resetForm()
      if (this.organizationId) {
        this.getSettingDetail()
      }
    },
    resetForm() {
      this.settingForm = {
        canReuse: false,
        ceilDealType: '',
        isAutoRelease: false,
        autoReleaseHour: 1,
        littleCeilSize: 1,
        bigCeilSize: 1,
        cupboardSaveFoodType: '',
        breakfast_ahead: 0, // 餐段结束前(早餐)
        lunch_ahead: 0, // 餐段结束前(午餐)
        afternoon_ahead: 0, // 餐段结束前(下午茶)
        dinner_ahead: 0, // 餐段结束前(晚餐)
        supper_ahead: 0, // 餐段结束前(夜宵)
        morning_ahead: 0 // 餐段结束前(凌晨餐)
      }
    },
    // 开启了重复不能选手动
    changeCanReuse() {
      console.log(this.settingForm.canReuse)
      if (this.settingForm.canReuse) {
        this.settingForm.ceilDealType = 'auto'
      }
    }
  }
}
</script>

<style lang="scss">
.RoutineSetting {
  .setting-wrap {
    margin: 0 20px;
    .title {
      font-size: 14px;
      font-weight: bold;
      padding: 0 10px;
      margin: 15px 0;
    }
    .tips {
      font-size: 12px;
      color: #7e7e7e;
      margin-bottom: 25px;
      margin-left: 20px;
    }
  }
  .fake-table-wrapper {
    display: flex;
    align-items: flex-end;
    .fake-table {
      display: flex;
      // width: 600px;
      border: 1px solid #ddd;
      .fake-col {
        display: flex;
        align-items: center;
        flex-direction: column;
        // width: 130px;
        border-right: 1px solid #ddd;

        .fake-col-title {
          display: block;
          width: 100%;
          border-bottom: 1px solid #ddd;
          text-align: center;
        }
      }
    }
  }
}
</style>
