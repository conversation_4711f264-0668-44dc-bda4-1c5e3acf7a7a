<template>
  <div>
    <refresh-tool :title="title" :show-refresh="false" />
    <import-page class="importPage" :initial="initial" :url="url" :header-len="headerLen" :template-url="templateUrl" :subTips="'如导入的菜品没有编辑食材信息，系统将自动关联食材以便帮助您维护您的菜品营养和食材信息。'" :is-url-download-result="true"/>
  </div>
</template>

<script>
export default {
  name: 'ImportIngredients',
  data() {
    return {
      type: 'import',
      title: '批量导入菜品/商品',
      headerLen: 2,
      initial: true,
      url: 'apiBackgroundAdminIngredientTrayBatAddPost',
      templateUrl: '/api/temporary/template_excel/food_stock/shop_foods.xlsx'
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.$route.params.type) {
      this.type = this.$route.params.type
    }
    if (this.type === 'import') {
      this.title = '批量导入菜品/商品'
      this.url = 'apiBackgroundFoodFoodBatchAddPost'
      // this.templateUrl = 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/v4/bc45cd75e5d180e0cd8b35c1587de7971648891577188.xlsx'
    } else {
      this.title = '导入编辑'
      this.url = 'apiBackgroundFoodFoodBatchModifyPost'
      // this.templateUrl = 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/v4/80b8f41baaafc2786ded4b34c606bd021648891600359.xlsx'
    }
  },
  mounted() {
  },
  methods: {}
}
</script>

<style lang="scss">
.importPage {
  // max-width: 1160px;
}
</style>
