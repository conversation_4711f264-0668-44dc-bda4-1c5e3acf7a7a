<template>
  <!-- 添加/编辑 -->
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :width="width"
    top="200px"
    class="remark-dialog"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <!-- 给个插槽 -->
    <slot name="header"></slot>
    <div class="remark-content">
      <div class="ps-red m-b-10">只允许备注一次，后期无法进行修改</div>
      <el-input
        v-model="remark"
        type="textarea"
        :rows="5"
        placeholder="请输入内容"
        maxlength="100"
        show-word-limit
        class="remark-textarea"
      ></el-input>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { to } from '@/utils'
// 查看
export default {
  name: 'RemarkEditDialog',
  props: {
    show: Boolean,
    // 类型
    type: {
      type: String,
      default: 'add' // static静态数据，xhr通过请求获取的数据
    },
    title: {
      type: String,
      default: '异常备注'
    },
    width: {
      type: String,
      default: '500px'
    }
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      remark: '', // 备注
      id: '' // 订单id
    }
  },
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 初始化
    init() {
    },
    handlerClose(e) {
      console.log('关闭')
      this.isLoading = false
      this.$emit('closeDialog')
    },
    // 确认
    clickConfirmHandle() {
      if (this.remark.trim().length === 0) {
        this.$message.error('请输入备注')
        return
      }
      this.saveRemark()
    },
    // 取消
    clickCancleHandle() {
      console.log('取消')
      this.visible = false
      this.$emit('closeDialog')
    },
    // 设置备注
    setRemark(val, id) {
      this.remark = val || ''
      this.id = id
    },
    // 保存备注
    async saveRemark() {
      this.isLoading = true
      let params = {
        id: this.id,
        err_remark: this.remark
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpInventoryInfoRemarkAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        this.$message.success('保存成功！')
        this.$emit('confirmDialog')
      } else {
        this.$message.error(res.msg || '保存失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.remark-dialog {
  .dialog-message {
    background-color: rgba(88, 88, 88, 0.3);
  }
  .el-dialog__body {
    padding-top: 10px;
  }
}
</style>
