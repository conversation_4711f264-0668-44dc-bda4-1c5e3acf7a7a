<template>
  <div class="ApproveRules container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <el-radio-group v-model="headerType" size="small" @change="switchFn">
      <el-radio-button label="merchant_configs">商户后台</el-radio-button>
      <el-radio-button label="app_configs">用户移动端</el-radio-button>
      <el-radio-button label="device_configs">设备端</el-radio-button>
    </el-radio-group>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">满意度</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="onShowDrawer">不适用项目</button-icon>
        </div>
      </div>

      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="module_alias" label="功能名称" align="center"></el-table-column>

          <el-table-column prop="tips" label="提示内容" align="center">
            <template slot-scope="scope">
              <el-tooltip effect="dark" :content="scope.row.tips" placement="top-start">
                <div class="ellipsis">{{ scope.row.tips }}</div>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column v-if="headerType === 'appConfigs'" label="评价配置" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.is_every_day">每日弹出一次</div>
              <div v-if="scope.row.is_every_quarter">季度内只弹一次</div>
            </template>
          </el-table-column>

          <el-table-column prop="enable" label="使用状态" align="center" width="90">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.enable"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
                @change="mulOperation('status', scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="update_time" label="更新时间" align="center"></el-table-column>
          <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" class="ps-bule" @click="gotoAddOrEdit('edit', scope.row)">
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
    </div>

    <!-- 不适用项目弹窗 -->
    <customDrawer
      :show.sync="drawerVisible"
      :loading="isDrawerLoading"
      :fixedFooter="true"
      :title="'适用项目'"
      :cancelText="'取 消'"
      cancelClass="ps-btn"
      @confirm="onSetCompany"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form>
            <el-form-item label="注意：选中的项目点不适用该规则"></el-form-item>
            <el-form-item label="不适用项目">
              <company-select
                class="search-item-w ps-select"
                v-model="fitValue"
                :options="{
                  label: 'name',
                  value: 'company'
                }"
                clearable
                multiple
                collapse-tags
                filterable
                placeholder="请选择"
                @change="changeCompany"
              ></company-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </customDrawer>

    <!-- 编辑弹窗 -->
    <customDrawer
      :show.sync="drawerEditorVisible"
      :loading="isDrawerEditorLoading"
      :fixedFooter="true"
      :title="'编辑'"
      :cancelText="'取 消'"
      cancelClass="ps-btn"
      @confirm="confirmDialogHandle('drawerFormDataRef')"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form :model="drawerFormData" ref="drawerFormDataRef" :rules="rulesDrawerForm" label-width="100px">
            <el-form-item label="功能名称">{{ drawerFormData.module_alias }}</el-form-item>

            <el-form-item label="提示内容" prop="tips" required>
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入提示文案"
                show-word-limit
                maxlength="100"
                v-model="drawerFormData.tips"
              />
            </el-form-item>

            <el-form-item v-if="headerType === 'appConfigs'" prop="validityPeriod">
              <template slot="label">
                <span style="color: #f56c6c; margin-right: 4px">*</span>
                评价配置
              </template>
              <div>每季度评价后不再弹出</div>
              <el-radio-group v-model="validityPeriod">
                <el-radio class="ps-radio" label="is_every_day">每日弹出一次</el-radio>
                <el-radio class="ps-radio" label="is_every_quarter">季度内只弹一次</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="评价标签" prop="tags" required>
              <i
                v-if="!drawerFormData.tags.length"
                @click="clickAddSignature"
                class="init-add-icon el-icon-circle-plus"
              ></i>
              <template v-else>
                <div v-for="(item, index) in drawerFormData.tags" :key="index" class="m-b-10">
                  <el-input
                    class="ps-input"
                    v-model="drawerFormData.tags[index]"
                    placeholder="请输入标签内容"
                    maxlength="20"
                    style="width: 300px"
                  ></el-input>
                  <i
                    @click="clickAddSignature"
                    v-if="drawerFormData.tags.length && drawerFormData.tags.length < 10"
                    class="add-icon el-icon-circle-plus"
                  ></i>
                  <i
                    @click="deleteSignature(index)"
                    v-if="drawerFormData.tags.length"
                    class="remove-icon el-icon-remove"
                  ></i>
                </div>
              </template>
            </el-form-item>

            <el-form-item label="使用状态">
              <el-switch v-model="drawerFormData.enable" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </el-form-item>
            <el-form-item label="产品线负责人" prop="product_manager">
              <el-input
                class="ps-input w-300"
                placeholder="请输入产品线负责人"
                maxlength="20"
                v-model="drawerFormData.product_manager"
              />
            </el-form-item>
            <el-form-item label="产品线电话" prop="product_manager_phone">
              <el-input
                class="ps-input w-300"
                placeholder="请输入产品线电话"
                maxlength="11"
                v-model="drawerFormData.product_manager_phone"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
// import { debounce } from '@/utils'
import { mapGetters } from 'vuex'
import CompanySelect from '@/components/CompanySelect'
import { deepClone } from '@/utils/index'
export default {
  name: 'ApproveRulesList',
  components: {
    CompanySelect
  },
  data() {
    let validateSignatureList = (rule, value, callback) => {
      // 检查是否至少有一个标签
      if (!value || !value.length) {
        return callback(new Error('请至少添加一个标签'))
      }

      // 过滤掉空字符串和只包含空白字符的标签
      const validTags = value.filter(tag => tag && tag.trim())

      // 检查是否有空标签
      if (validTags.length !== value.length) {
        return callback(new Error('标签内容不能为空'))
      }

      // 使用正则检查标签内容规则
      const tagPattern = /^[^\s<>'"&]{1,20}$/
      for (let i = 0; i < validTags.length; i++) {
        const tag = validTags[i].trim()

        if (!tagPattern.test(tag)) {
          if (tag.includes(' ')) {
            return callback(new Error('标签内容不能包含空格'))
          }
          if (tag.length > 20) {
            return callback(new Error('标签内容不能超过20个字符'))
          }
          // if (/[<>'"&]/.test(tag)) {
          //   return callback(new Error('标签内容不能包含特殊字符'))
          // }
          return callback(new Error('标签格式不正确'))
        }
      }

      // 检查重复标签（使用 trim 后的值进行比较）
      const trimmedTags = validTags.map(tag => tag.trim())
      const uniqueTags = new Set(trimmedTags)
      if (uniqueTags.size !== trimmedTags.length) {
        return callback(new Error('标签内容不能重复'))
      }

      callback()
    }
    // let validateTelphone = (rule, value, callback) => {
    //   if (value) {
    //     this.sendCodeDisabled = true
    //     let regTelphone = /^1[3456789]\d{9}$/
    //     if (!regTelphone.test(value)) {
    //       this.sendCodeDisabled = true
    //       return callback(new Error('手机号格式错误'))
    //     } else {
    //       return callback()
    //     }
    //   } else {
    //     return callback()
    //   }
    // }
    return {
      headerType: 'merchant_configs',
      // 满意度 抽屉弹窗
      rulesDrawerForm: {
        tips: [{ required: true, message: '请输入提示文案', trigger: ['blur', 'change'] }],
        // validityPeriod: [{ required: true, message: '请选择评价配置', trigger: 'blur' }],
        tags: [{ required: true, validator: validateSignatureList, trigger: 'blur' }],
        // product_manager_phone: [{ validator: validateTelphone, trigger: 'change' }]
      },
      btnLoading: false,
      drawerVisible: false,
      drawerEditorVisible: false,
      isDrawerLoading: false,
      isDrawerEditorLoading: false,
      validityPeriod: 'is_every_day',
      drawerFormData: {
        module_alias: '',
        tips: '',
        enable: true,
        is_every_day: false,
        is_every_quarter: false,
        tags: [],
        product_manager: '',
        product_manager_phone: ''
      },
      fitValue: [],

      // 表单
      isLoading: false,
      tableData: []
    }
  },
  created() {},
  mounted() {
    this.initLoad()
  },
  computed: {
    ...mapGetters(['allPermissions', 'userInfo'])
  },
  methods: {
    switchFn() {
      this.refreshHandle()
    },
    clickAddSignature() {
      this.drawerFormData.tags.push('')
    },
    deleteSignature(index) {
      this.drawerFormData.tags.splice(index, 1)
      // 手动触发校验更新
      this.$refs.drawerFormDataRef.validateField('signatureList')
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    changeCompany(e) {
      this.fitValue = e
    },
    // 获取不适用项目列表
    onShowDrawer() {
      this.$apis.apiBackgroundAdminSatisfactionGetDisableCompanyPost().then(res => {
        if (res.code === 0) {
          this.fitValue = res.data.company_ids || []
          this.drawerVisible = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 设置不适用项目
    onSetCompany() {
      this.$apis.apiBackgroundAdminSatisfactionSetDisableCompanyPost({ company_ids: this.fitValue }).then(res => {
        if (res.code === 0) {
          this.$message.success('操作成功')
          this.drawerVisible = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 初始化
    initLoad() {
      this.getApproveRulesList()
    },
    // 刷新页面
    refreshHandle() {
      this.tableData = []
      this.initLoad()
    },
    // 获取列表数据
    async getApproveRulesList() {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminSatisfactionGetConfigListPost())
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data[this.headerType] && res.data[this.headerType].length) {
          this.tableData = res.data[this.headerType]
        }
      } else {
        this.$message.error(err.message)
      }
    },
    // 使用状态修改
    async mulOperation(type, data) {
      let params = {
        tips: data.tips,
        module_key: data.module_key,
        enable: data.enable,
        tags: data.tags
      }
      if (data.product_manager) {
        params.product_manager = data.product_manager
      }
      if (data.product_manager_phone) {
        params.product_manager_phone = data.product_manager_phone
      }
      if (this.headerType === 'app_configs') {
        params.is_every_day = this.validityPeriod === 'is_every_day'
        params.is_every_quarter = this.validityPeriod === 'is_every_quarter'
      }
      // 不知道为啥要删 我看都是必填的
      if (!params.tips) {
        delete params.tips
      }

      this.isDrawerEditorLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminSatisfactionSetAppConfigPost(params))
      this.isDrawerEditorLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('操作成功')
        const index = this.tableData.findIndex(item => item.module_key === data.module_key)
        if (index !== -1) {
          this.$set(this.tableData[index], 'enable', res.data.enable)
          this.$set(this.tableData[index], 'update_time', res.data.update_time)
          this.$set(this.tableData[index], 'operator', res.data.operator)
        }
        this.drawerEditorVisible = false
        if (type === 'confirm') {
          this.getApproveRulesList()
        }
      } else {
        this.$message.error(res.msg)
        if (type === 'status') {
          data.enable = !data.enable
        }
      }
    },
    gotoAddOrEdit(type, data) {
      let copyDrawerFormData = deepClone(data)
      if (!copyDrawerFormData.tags && !copyDrawerFormData.tags?.length) {
        copyDrawerFormData.tags = []
      }
      this.validityPeriod = copyDrawerFormData.is_every_day ? 'is_every_day' : 'is_every_quarter'
      this.drawerFormData = copyDrawerFormData
      this.drawerEditorVisible = true
    },
    confirmDialogHandle(formName) {
      this.drawerFormData.tips = this.drawerFormData.tips.trim() // 清除首尾空格;
      this.$refs[formName].validate(valid => {
        if (valid) {
          console.log(this.drawerFormData)
          // return
          this.mulOperation('confirm', this.drawerFormData)
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.init-add-icon {
  font-size: 20px;
  cursor: pointer;
  color: #fda04d;
  position: relative;
  top: 3px;
}
.add-icon {
  font-size: 20px;
  cursor: pointer;
  color: #fda04d;
  padding-left: 20px;
}
.remove-icon {
  font-size: 20px;
  cursor: pointer;
  color: red;
  padding-left: 20px;
}
</style>
