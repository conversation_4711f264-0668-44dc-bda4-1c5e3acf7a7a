<template>
  <div id="update-record-container" class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="update-record-list">
      <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon @click="showDialog('add', '')">新增</button-icon>
            <button-icon color="plain" @click="goExport">批量导出</button-icon>
            <button-icon color="plain" @click="labelManagementShow = true">标签管理</button-icon>
          </div>
        </div>
        <div class="table-content">
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            row-key="id"
            border
            :row-class-name="tableRowClassName"
            header-row-class-name="ps-table-header-row"
            class="ps-table"
          >
            <el-table-column
              prop="versio_no"
              label="更新版本"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="update_time"
              label="更新日期"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="type_list"
              label="更新类型"
              align="center"
            >
              <template slot-scope="scope">
                <span v-for="(item, index) in scope.row.type_list" :key="index">{{ item }}{{ index < scope.row.type_list.length - 1 ? '、' : '' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="tag_list"
              label="涉及标签"
              align="center"
            >
              <template slot-scope="scope">
                <div v-for="(item, index) in scope.row.tag_list" :key="index">
                  <span v-for="(itemr, indexr) in item" :key="indexr">{{ itemr }}{{ indexr < item.length - 1 ? '、' : '' }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="detail"
              label="更新内容"
              align="center"
            >
              <template slot-scope="scope">
                <div class="pointer origin" @click="showUpdateRecordDrawer(scope.row.detail)">{{ scope.row.detail ? '查看' : '' }}</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="update_time"
              label="发布状态"
              align="center"
            >
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.push_status" @change="changeStatus(scope.row)">
                </el-switch>
              </template>
            </el-table-column>
            <el-table-column
              prop="operator_name"
              label="操作员"
              align="center"
            >
              <template slot-scope="scope">
                <div>{{ scope.row.operator_name ? scope.row.operator_name : '--' }}</div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="showDialog('edit', scope.row)"
                  :disabled="Boolean(scope.row.push_status)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="deleteUpdateData(scope.row)"
                  :disabled="Boolean(scope.row.push_status)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- table end -->
        </div>
        <!-- 分页 -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 100, 200, 500, 1000, 2000]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        title="标签管理"
        :visible="labelManagementShow"
        :show-close="false"
        size="40%">
        <div class="drawer-content p-20">
          <el-form
            ref="tagManagementForm"
            :model="tagManagementForm"
            label-width="100px">
            <div class="drawer-content-item" v-for="(item, index) in updateType" :key="index">
              <el-form-item :label="item.label" :prop="item.value">
                <div class="m-b-10">
                  <el-input v-model="item.inputModel" placeholder="请输入，不超过10个字" :maxlength="10" class="w-180 m-r-10"></el-input>
                  <el-button class="ps-btn" @click="addTag(item)">添加</el-button>
                </div>
                <div class="show-tag" v-if="item.list.length">
                  <span class="show-tag-item m-r-10" v-for="(item1, index1) in item.list" :key="index1">
                    <span class="ellipsis" style="width: 5em;">{{ item1 }}</span>
                    <i class="el-icon-close m-l-10" style="cursor: pointer;" @click="deleteTag(item.value, index1)"></i>
                  </span>
                </div>
              </el-form-item>
            </div>
            <div class="ps-el-drawer-footer">
              <el-button size="small" class="w-100" @click="labelManagementShow = false">取消</el-button>
              <el-button size="small" type="primary" class="ps-origin-btn w-100" @click="saveTagList">保存</el-button>
            </div>
          </el-form>
        </div>
      </el-drawer>

      <el-drawer
        title="更新内容"
        :visible="updateRecordDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <div class="drawer-box m-b-20" v-for="(itemI, indexI) in updateDataList" :key="indexI">
            <div class="drawer-box-bg">
              <div class="p-t-10 p-b-10">
                <div class="font-size-20 f-w-700 m-b-10">{{ itemI.label }}:</div>
                <el-input
                  :disabled="true"
                  :resize="'none'"
                  type="textarea"
                  v-model="itemI.text"
                  :autosize="true">
                </el-input>
              </div>
            </div>
          </div>
          <div class="ps-el-drawer-footer">
            <el-button size="small" type="primary" class="w-100" @click="updateRecordDrawerShow = false">关闭</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="isEdit ? '编辑更新内容':'新增更新内容'"
        :visible="addUpdateDrawerShow"
        :show-close="false"
        size="40%">
        <div class="drawer-content p-20">
          <el-form
            ref="addUpdateForm"
            :model="addUpdateForm"
            :rules="addUpdateRules"
            label-width="90px">
            <el-form-item label="更新版本" prop="updateNo">
              <div class="flex-col">
                <el-input v-model="addUpdateForm.updateNo" class="w-250" placeholder="请输入本次更新的版本号"></el-input>
                <span class="font-size-12" style="color: #C0C4CC;">*格式：V大版本号.中版本号.小版本号.日期（年月日） 例如: V4.1.1.20240101</span>
              </div>
            </el-form-item>
            <el-form-item label="更新类型" prop="updateType">
              <button-icon :color="item.isSelected ? '' : 'plain'" v-for="(item, index) in updateType" :key="index" @click="selectedThis(item)">{{item.label}}</button-icon>
            </el-form-item>
            <div v-for="(item, index) in addUpdateForm.updateData" :key="index">
              <el-form-item
                :label="item.label"
                :prop="'updateData.'+ index + '.text'"
                :rules="{ required: true, message: '更新内容不能为空', trigger: ['change', 'blur'] }">
                <el-input
                  type="textarea"
                  :rows="6"
                  placeholder="请输入，不超过500字"
                  maxlength="500"
                  v-model="item.text"
                  class="m-b-10">
                </el-input>
              </el-form-item>
              <el-form-item
                class="p-l-80"
                label="更新标签:"
                :prop="'updateData.'+ index + '.updateTag'"
                :rules="{ required: true, message: '更新标签不能为空', trigger: ['change', 'blur'] }">
                <el-select v-model.trim="item.updateTag" placeholder="请选择" multiple collapse-tags class="w-150 m-r-5">
                  <el-option
                    v-for="(tagItem, tagIndex) in checkTagList(item.value)"
                    :key="tagIndex"
                    :label="tagItem"
                    :value="tagItem">
                  </el-option>
                </el-select>
                <span class="ps-origin-btn m-r-10 tag-style" v-for="(item1, index1) in item.updateTag" :key="index1">
                  <span class="ellipsis" style="width: 5em;">{{ item1 }}</span>
                  <i class="el-icon-close m-l-10" style="cursor: pointer;" @click="deleteTags(item1, item.updateTag)"></i>
                </span>
              </el-form-item>
            </div>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelDrawer">取消</el-button>
            <el-button size="small" type="primary" class="ps-origin-btn w-100" @click="saveUpdateDate">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { debounce, deepClone } from '@/utils'
import dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'SuperUpdateRecord',
  mixins: [exportExcel],
  data() {
    let checkUpdateType = (rule, value, callback) => {
      if (this.addUpdateForm.updateData.length === 0) {
        callback(new Error('请选择更新类型'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      tableData: [],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '更新日期',
          value: [
            dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD')
          ]
        },
        update_type: {
          type: 'select',
          label: '更新类型',
          value: '',
          dataList: []
        },
        push_status: {
          type: 'select',
          label: '发布状态',
          value: '',
          dataList: [
            { label: "全部", value: "" },
            { label: "已发布", value: 1 },
            { label: "未发布", value: 0 }
          ]
        },
        tags: {
          type: 'input',
          label: '涉及标签',
          value: '',
          placeholder: '输入标签进行搜索'
        }
      },
      currentPage: 1,
      pageSize: 10,
      totalCount: 1,
      labelManagementShow: false,
      tagManagementForm: {},
      updateType: [],
      addUpdateDrawerShow: false,
      addUpdateForm: {
        updateNo: '',
        updateData: []
      },
      addUpdateRules: {
        updateNo: [
          { required: true, message: '版本号不能为空', trigger: ['change', 'blur'] },
          { pattern: /^V\d+\.\d+\.\d+\.\d{8}$/, message: '请输入正确的版本号', trigger: ['change', 'blur'] }
        ],
        updateData: [{ required: true, validator: checkUpdateType, trigger: ['blur'] }]
      },
      tagList: [],
      selectedId: '',
      isEdit: false,
      updateRecordDrawerShow: false,
      updateDataList: []
    }
  },
  watch: {
    updateType(newVal, oldVal) {
      if (newVal.length !== oldVal.length) {
        this.getTagsList()
      }
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.isLoading = true
      this.getUpdateTypeList()
      // this.getTagsList()
      this.getUpdateDataList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.isLoading = true
      this.currentPage = 1
      this.getUpdateDataList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.isLoading = true
      this.currentPage = 1
      this.getUpdateDataList()
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.isLoading = true
      this.getUpdateDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.isLoading = true
      this.getUpdateDataList()
    },
    showDialog(type, row) {
      console.log('row', row)
      this.addUpdateForm.updateNo = ''
      this.addUpdateForm.updateData = []
      if (type === 'edit' && row) {
        this.isEdit = true
        this.selectedId = row.id
        this.addUpdateForm.updateNo = row.versio_no
        this.addUpdateForm.updateData = deepClone(row.detail)
        row.type_list.forEach(item => {
          this.updateType = this.updateType.map(item1 => {
            if (item1.label === item) {
              item1.isSelected = true
            }
            return item1
          })
        })
      }
      console.log('this.updateType', this.updateType)
      this.addUpdateDrawerShow = true
    },
    deleteTag(value, tagIndex) {
      let item = this.updateType.find((item) => item.value === value)
      let index = this.updateType.indexOf(item)
      if (item.list) {
        item.list.splice(tagIndex, 1)
        this.updateType[index].list = [...item.list]
      }
    },
    addTag(data) {
      if (!data.inputModel) {
        return this.$message.error('标签名不能为空')
      }
      data.list.push(data.inputModel)
      data.inputModel = ''
    },
    selectedThis(data) {
      if (data.isSelected) {
        this.addUpdateForm.updateData.forEach((item, index) => {
          if (item.value === data.value) {
            this.addUpdateForm.updateData.splice(index, 1)
          }
        })
      } else {
        let obj = {
          value: data.value,
          label: data.label,
          text: '',
          updateTag: []
        }
        this.addUpdateForm.updateData.push(obj)
      }
      data.isSelected = !data.isSelected
    },
    getUpdateTypeList() {
      this.$apis.apiBackgroundAdminUpdateTypeListPost().then(res => {
        if (res.code === 0) {
          this.searchFormSetting.update_type.dataList = deepClone(res.data)
          this.updateType = res.data.map(item => {
            let obj = {
              ...item,
              inputModel: '',
              isSelected: false,
              list: []
            }
            return obj
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getUpdateDataList() {
      let obj = this.formatQueryParams(this.searchFormSetting)
      let params = {
        page: this.currentPage,
        page_size: this.pageSize,
        ...obj
      }
      this.$apis.apiBackgroundAdminSysUpdateRecordListPost(params).then(res => {
        if (res.code === 0) {
          this.tableData = res.data.results.map(item => {
            item.push_status = Boolean(item.push_status)
            return item
          })
          this.isLoading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key.indexOf('time') < 0) {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_update_time = dayjs(data[key].value[0]).format('YYYY-MM-DD HH:mm:ss')
            params.end_update_time = dayjs(data[key].value[1]).hour(23).minute(59).second(59).format('YYYY-MM-DD HH:mm:ss')
          }
        }
      }
      return params
    },
    changeStatus(row) {
      this.isLoading = true
      let params = {
        id: row.id,
        push_status: Number(row.push_status)
      }
      this.$apis.apiBackgroundStoreSysUpdateRecordChangeStatusPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
          this.getUpdateDataList()
          this.isLoading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取已有的标签
    getTagsList() {
      this.$apis.apiBackgroundAdminTagManageListPost().then(res => {
        if (res.code === 0) {
          this.updateType = this.updateType.map(item => {
            let arr = res.data.filter(item1 => item1.key === item.value)
            item.list = deepClone(arr[0].tag_list)
            return item
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 一并保存
    saveTagList() {
      let params = this.updateType.map(item => {
        let data = {
          name: item.label,
          key: item.value,
          tag_list: deepClone(item.list)
        }
        return data
      })
      this.$apis.apiBackgroundAdminTagManageEditPost({ data: [...params] }).then(res => {
        if (res.code === 0) {
          this.$message.success('保存成功')
          this.labelManagementShow = false
        }
      })
    },
    // 根据选的类型确定标签
    checkTagList(key) {
      let arr = this.updateType.filter(item => item.value === key)
      console.log('list', arr[0].list)
      return arr[0].list
    },
    deleteTags(data, list) {
      let index = list.indexOf(data)
      list.splice(index, 1)
    },
    saveUpdateDate() {
      this.$refs.addUpdateForm.validate((valid) => {
        if (valid) {
          let typeList = this.addUpdateForm.updateData.map(item => {
            return item.label
          })
          let tagList = []
          this.addUpdateForm.updateData.forEach(item => {
            tagList.push(item.updateTag)
          })
          let params = {
            id: this.isEdit ? this.selectedId : undefined,
            versio_no: this.addUpdateForm.updateNo,
            type_list: typeList,
            tag_list: tagList,
            detail: this.addUpdateForm.updateData
          }
          console.log('this.addUpdateForm', this.addUpdateForm.updateData, this.updateType)
          if (this.isEdit) {
            this.editUpdateData(params)
          } else {
            this.addUpdateData(params)
          }
        } else {
          this.$message.error('校验不通过，请确认更新内容填写正确后重试')
        }
      })
    },
    cancelDrawer() {
      this.updateType = this.updateType.map(item => {
        item.isSelected = false
        return item
      })
      this.addUpdateDrawerShow = false
    },
    editUpdateData(params) {
      this.$apis.apiBackgroundAdminSysUpdateRecordModifyPost(params).then(res => {
        if (res.code === 0) {
          this.updateType.forEach(item => {
            item.isSelected = false
          })
          this.$message.success('修改成功')
          this.$refs.addUpdateForm.resetFields()
          this.addUpdateDrawerShow = false
          this.isLoading = true
          this.getUpdateDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    addUpdateData(params) {
      this.$apis.apiBackgroundAdminSysUpdateRecordAddPost(params).then(res => {
        if (res.code === 0) {
          this.updateType.forEach(item => {
            item.isSelected = false
          })
          this.$refs.addUpdateForm.resetFields()
          this.$message.success('新增成功')
          this.addUpdateDrawerShow = false
          this.isLoading = true
          this.getUpdateDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    deleteUpdateData(row) {
      this.$apis.apiBackgroundAdminSysUpdateRecordDeletePost({
        id: row.id
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('删除成功')
          this.isLoading = true
          this.getUpdateDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 导出
    goExport() {
      const option = {
        immediate: false,
        url: 'apiBackgroundAdminSysUpdateRecordListExportPost',
        params: this.formatQueryParams(this.searchForm)
      }
      this.exportHandle(option)
    },
    showUpdateRecordDrawer(data) {
      if (data.length) {
        console.log(data)
        this.updateDataList = data
        this.updateRecordDrawerShow = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-content {
  &-item {
    .show-tag {
      padding: 10px 10px 0px;
      display: flex;
      flex-wrap: wrap;
      border: 1px solid #f8f9fa;
      background-color: #f8f9fa;
      border-radius: 4px;
      &-item {
        padding: 5px 10px;
        width: 85px;
        height: 32px;
        margin-bottom: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #DCDFE6;
        background-color: #fff ;
        border-radius: 4px;
      }
    }
  }
  .tag-style {
    padding: 5px 10px;
    border-radius: 4px;
    margin: 0px 5px 5px;
  }
}
.drawer-box {
  &-bg {
    min-height: 100px;
    padding: 20px;
    border-radius: 4px;
    background-color: #f8f9fa;
  }
}
</style>
