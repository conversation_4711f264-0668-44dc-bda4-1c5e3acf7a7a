export const TABLE_SETTING_SHU_REPORT = [
  {
    label: '消费点',
    key: 'org_name'
  },
  {
    label: '区域',
    key: 'area_name'
  },
  {
    label: '一级',
    key: 'l1_addr'
  },
  {
    label: '二级',
    key: 'l2_addr'
  },
  {
    label: '三级',
    key: 'l3_addr'
  },
  {
    label: '四级',
    key: 'l4_addr'
  },
  {
    label: '五级',
    key: 'l5_addr'
  },
  {
    label: '取餐人',
    key: 'user_name'
  },
  {
    label: '手机号',
    key: 'phone'
  },
  {
    label: '备注',
    key: 'remark'
  },
  {
    label: '菜品',
    key: 'food_name'
  },
  {
    label: '数量',
    key: 'food_count'
  }
]

export const TABLE_SETTING_AREA_FOOD = [
  {
    label: '区域',
    key: 'name'
  },
  {
    label: '菜品',
    key: 'food_name'
  },
  {
    label: '数量',
    key: 'food_count'
  }
]

export const TABLE_SETTING_CONSUME = [
  {
    label: '消费点',
    key: 'org_name'
  },
  {
    label: '套餐',
    key: 'set_meal_name'
  },
  {
    label: '套餐数量',
    key: 'set_meal_count'
  },
  {
    label: '菜品',
    key: 'foods_names'
  },
  {
    label: '菜品数量',
    key: 'food_count'
  }
]

// Consume组件的搜索表单配置
export const SEARCH_FORM_SETTING_CONSUME = {
  select_date: {
    clearable: false,
    label: '取餐时间',
    type: 'daterange',
    value: null // 这里会在组件中动态设置
  },
  area_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '配送区域',
    dataList: []
  },
  l1_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '一级地址',
    dataList: []
  },
  l2_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '二级地址',
    dataList: []
  },
  l3_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '三级地址',
    dataList: []
  },
  l4_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '四级地址',
    dataList: []
  },
  l5_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '五级地址',
    dataList: []
  },
  take_meal_time: {
    type: 'select',
    value: '',
    clearable: true,
    label: '取餐餐段',
    dataList: null // 这里会在组件中动态设置
  },
  org_ids: {
    type: 'organizationSelect',
    value: null, // 这里会在组件中动态设置
    label: '消费点',
    clearable: true,
    checkStrictly: true,
    isLazy: false,
    multiple: true
  }
}

// ShuReport组件的搜索表单配置
export const SEARCH_FORM_SETTING_SHU_REPORT = {
  select_date: {
    clearable: false,
    label: '取餐时间',
    type: 'daterange',
    value: null // 这里会在组件中动态设置
  },
  area_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '配送区域',
    dataList: []
  },
  l1_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '一级地址',
    dataList: []
  },
  l2_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '二级地址',
    dataList: []
  },
  l3_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '三级地址',
    dataList: []
  },
  l4_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '四级地址',
    dataList: []
  },
  l5_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '五级地址',
    dataList: []
  },
  take_meal_time: {
    type: 'select',
    value: '',
    clearable: true,
    label: '取餐餐段',
    dataList: null // 这里会在组件中动态设置
  },
  name: {
    type: 'input',
    value: '',
    label: '取餐人',
    placeholder: '请输入'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号码',
    placeholder: '请输入手机号码'
  },
  org_ids: {
    type: 'organizationSelect',
    value: null, // 这里会在组件中动态设置
    label: '消费点',
    clearable: true,
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  is_visitor: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看游客',
    value: false
  }
}

// HengReport组件的搜索表单配置
export const SEARCH_FORM_SETTING_HENG_REPORT = {
  select_date: {
    clearable: false,
    label: '取餐时间',
    type: 'daterange',
    value: null // 这里会在组件中动态设置
  },
  area_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '配送区域',
    dataList: []
  },
  l1_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '一级地址',
    dataList: []
  },
  l2_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '二级地址',
    dataList: []
  },
  l3_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '三级地址',
    dataList: []
  },
  l4_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '四级地址',
    dataList: []
  },
  l5_addr: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '五级地址',
    dataList: []
  },
  take_meal_time: {
    type: 'select',
    value: '',
    clearable: true,
    label: '取餐餐段',
    dataList: null // 这里会在组件中动态设置
  },
  name: {
    type: 'input',
    value: '',
    label: '取餐人',
    placeholder: '请输入'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号码',
    placeholder: '请输入手机号码'
  },
  org_ids: {
    type: 'organizationSelect',
    value: null, // 这里会在组件中动态设置
    label: '消费点',
    clearable: true,
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  is_visitor: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看游客',
    value: false
  }
}

// AreaFood组件的搜索表单配置
export const SEARCH_FORM_SETTING_AREA_FOOD = {
  select_date: {
    clearable: false,
    label: '取餐时间',
    type: 'daterange',
    value: null // 这里会在组件中动态设置
  },
  area_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '配送区域',
    dataList: []
  },
  org_ids: {
    type: 'organizationSelect',
    value: null, // 这里会在组件中动态设置
    label: '消费点',
    clearable: true,
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  take_meal_time: {
    type: 'select',
    value: '',
    clearable: true,
    label: '取餐餐段',
    dataList: null // 这里会在组件中动态设置
  }
}
