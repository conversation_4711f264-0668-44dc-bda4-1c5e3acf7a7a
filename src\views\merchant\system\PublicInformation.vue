<template>
  <div class="public-information container-wrapper">
    <div class="table-type">
      <div :class="['table-type-btn', tableType==='InfoShow'?'active-btn':'']" @click="changeTableType('InfoShow')" v-permission="['background_fund_supervision.publicity_info.get_canteen_publicity']">食堂公示</div>
      <div :class="['table-type-btn', tableType==='Incumbents'?'active-btn':'']" @click="changeTableType('Incumbents')" v-permission="['background_fund_supervision.job_person.job_person_list']">在职人员</div>
    </div>
    <div class="table-wrapper">
      <div v-if="tableType === 'InfoShow'">
        <info-show />
      </div>
      <div v-else>
        <incumbents />
      </div>
    </div>
  </div>
</template>

<script>
import infoShow from './components/infoShow.vue'
import incumbents from './components/incumbents.vue'
export default {
  components: {
    infoShow,
    incumbents
  },
  data() {
    return {
      tableType: 'InfoShow'
    }
  },
  methods: {
    changeTableType(type) {
      this.tableType = type
    }
  }
}
</script>

<style lang="scss" scoped>
.public-information {
  .table-type{
    padding: 20px 0px 0px ;
    display: flex;
    font-size: 16px;
    .table-type-btn{
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      background-color: #E8F0F8;
      border-radius: 4px;
      margin-right: 20px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn{
      color: #FFF;
      background-color: #ff9b45;
      border: none;
    }
  }
}
</style>
