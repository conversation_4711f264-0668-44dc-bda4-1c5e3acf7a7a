<template>
  <div class="AttendanceSetting container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="gotoAddOrEdit('add')" v-permission="['background_attendance.access_control_settings.add']">新建</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          border
          header-row-class-name="ps-table-header-row"
          :span-method="objectSpanMethod"
        >
          <el-table-column
            v-for="col in columns"
            :prop="col.column"
            :label="col.label"
            align="center"
            :key="col.column"
          >
            <template slot-scope="scope">
              <div v-if="col.column!=='operation'">{{ scope.row[col.column] }}</div>
              <div v-else>
                <el-button
                  type="text"
                  size="small"
                  class="ps-bule"
                  @click="gotoAddOrEdit('edit', scope.row)"
                  v-permission="['background_attendance.access_control_settings.modify']"
                  >编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn"
                  @click="delPushSetting(scope.row.id)"
                  v-permission="['background_attendance.access_control_settings.delete']"
                  >删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import { weekList } from '../constantsConfig'
import { mergeHandle, mergeRowAction } from '@/utils/table'
export default {
  name: 'AttendanceSetting',
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      columns: [
        { label: '名称', column: 'name' },
        { label: '适用分组', column: 'user_groups' },
        { label: '晚归时间', column: 'sign_time' },
        { label: '适用日期', column: 'week_day_range_alias' },
        { label: '操作', column: 'operation' }
      ],
      mergeOpts: {
        useKeyList: {
          id: [
            'name',
            'user_groups',
            'operation'
          ]
        }, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        mergeKeyList: [] // 通用的合并字段，根據值合并
      },
      weekList
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getAccessControlSetting()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getAccessControlSetting()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async getAccessControlSetting() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAttendanceAccessControlSettingsListPost({
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        let list = res.data.results
        this.tableData = []
        list.map(item => {
          // 处理分组
          item.user_groups = []
          item.user_groups_ids = []
          item.card_user_groups_list.map(group => {
            item.user_groups.push(group.group_name)
            item.user_groups_ids.push(group.id)
          })
          item.user_groups = item.user_groups.join(",")
          item.access_control_time_settings.map(setting => {
            // 处理晚归时间
            if (setting.sign_in_time && setting.sign_out_time) {
              setting.sign_time = setting.sign_in_time + '-' + setting.sign_out_time
            }
            // 处理适用日期
            setting.week_day_range_alias = []
            setting.week_day_range.map(weekDay => {
              let weekIndex = this.weekList.findIndex(weekItem => weekItem.key === weekDay)
              setting.week_day_range_alias.push(this.weekList[weekIndex].name)
            })
            setting.week_day_range_alias = setting.week_day_range_alias.join(",")
            // 处理合并表格的数据
            this.tableData.push({
              ...item,
              sign_time: setting.sign_time,
              week_day_range_alias: setting.week_day_range_alias
            })
          })
        })
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getAccessControlSetting()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getAccessControlSetting()
    },
    async delPushSetting(id) {
      this.$confirm(`确定删除该考勤设置？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundAttendanceAccessControlSettingsDeletePost({
              ids: [id]
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getAccessControlSetting()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    gotoAddOrEdit(type, data) {
      let query = {}
      if (type === 'edit') {
        query = { data: encodeURIComponent(JSON.stringify(data)) }
      }
      this.$router.push({
        name: 'MerchantAddOrEditControlSetting',
        params: {
          type
        },
        query
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
