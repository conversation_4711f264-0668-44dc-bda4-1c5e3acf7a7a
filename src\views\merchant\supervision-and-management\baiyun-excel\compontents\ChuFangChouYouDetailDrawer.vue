<template>
  <customDrawer :show.sync="visible" cancelText="关闭" :confirmShow="false" :loading="isLoading" :title="title"
    :size="size" :wrapperClosable="false" :showClose="false" @cancel="closeDialog"
    :printShow="true" :printText="打印" @print="clickPrintHandle">
    <div class="table-content m-t-20">
      <div class="table-header-title">厨房油烟机清洗表</div>
      <!-- 表单包装表格 start -->
        <el-table v-loading="isLoading" :data="tableData" ref="historytableData" style="width: 100%" stripe
          header-row-class-name="ps-table-header-row">
        <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
        </table-column>
        </el-table>
      <!-- 表单包装表格 end -->
    </div>
    <!-- 分页 start -->
    <div class="block ps-pagination" style="text-align: right; padding-top: 20px; display: none">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100, 500]" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount" background class="ps-text" popper-class="ps-popper-select"></el-pagination>
    </div>
  </customDrawer>
  <!-- end -->
</template>

<script>
import { to, deepClone } from '@/utils'
import { TABLE_HEAD_DATA_CHUFANG_CHOUYOU_LEDGER_DETAIL } from '../constants'

export default {
  name: 'ChuFangChouYouDetailDrawer',
  props: {
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'detail'
    },
    size: {
      type: String,
      default: '40%'
    },
    isshow: Boolean
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableSetting: deepClone(TABLE_HEAD_DATA_CHUFANG_CHOUYOU_LEDGER_DETAIL),
      tableData: [] // 表格数据
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    isshow(val) {
      if (val) {
        // 初始化数据
        this.initData()
      }
    }
  },
  created() {
  },
  mounted() { },
  methods: {
    // 初始化数据
    initData() {
      // 使用默认数据初始化表格
      this.getDataList()
    },

    // 获取历史记录
    async getDataList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreRetentionRecordFoodReservedSampleOperateLogListPost({
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
    },
    closeDialog() {
      this.$emit('close')
    },
    // 打印事件
    clickPrintHandle() {
      console.log('clickPrintHandle')
      this.$print(this.$refs.historytableData)
    }
  }
}
</script>

<style lang="scss" scoped>
.table-header-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
  background-color: #fff;
}
</style>
