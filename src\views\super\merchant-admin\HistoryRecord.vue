<template>
  <div class="agreement-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="gotoExport">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item"></table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
export default {
  name: 'AgreementRecord',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '操作时间', key: 'create_time', align: 'left' },
        { label: '操作人', key: 'operator_name', align: 'left' },
        { label: '功能', key: 'full_module_verbose_name', align: 'left' },
        { label: '操作', key: 'child_operation_type_alias', align: 'left' },
        { label: '数据ID', key: 'instance_id', align: 'left' },
        { label: '操作详情', key: 'detail_text', align: 'left', width: '700px' }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '操作时间',
          clearable: true,
          format: 'yyyy-MM-dd',
          value: getSevenDateRange(3)
        },
        operator: {
          type: 'input',
          value: '',
          label: '操作人',
          placeholder: '请输入操作用户名称'
        },
        module_keys: {
          type: 'cascader',
          value: [],
          label: '功能',
          clearable: true,
          filterable: true,
          collapseTags: true,
          showAllLevels: false,
          width: '230px',
          dataList: [
            {
              value: 'zhinan',
              label: '指南',
              children: [
                {
                  value: 'shejiyuanze',
                  label: '设计原则',
                  children: [
                    {
                      value: 'yizhi',
                      label: '一致'
                    },
                    {
                      value: 'fankui',
                      label: '反馈'
                    },
                    {
                      value: 'xiaolv',
                      label: '效率'
                    },
                    {
                      value: 'kekong',
                      label: '可控'
                    }
                  ]
                }
              ]
            }
          ],
          props: {
            value: 'key',
            label: 'verbose_name',
            multiple: true,
            emitPath: false
          }
        },
        child_operation_type: {
          type: 'select',
          value: '',
          label: '操作',
          clearable: true,
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '新增',
              value: '1'
            },
            {
              label: '修改',
              value: '2'
            },
            {
              label: '删除',
              value: '3'
            },
            {
              label: '启用',
              value: '4'
            },
            {
              label: '禁用',
              value: '5'
            }
          ]
        },
        instance_id: {
          type: 'input',
          value: '',
          label: '数据ID',
          placeholder: '请输入数据ID'
        }
      }
    }
  },
  created() {
    this.getHistoryTreePost()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getHistoryList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getHistoryList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundLogOperationHistoryListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getHistoryTreePost() {
      const [err, res] = await to(this.$apis.apiBackgroundLogOperationHistoryTreePost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.module_keys.dataList = this.getTreeData(res.data)
        console.log(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 递归
    getTreeData(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined // children若为空数组，则将children设为undefined
        } else {
          this.getTreeData(data[i].children) // children若不为空数组，则继续 递归调用 本方法
        }
      }
      return data
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getHistoryList()
    },
    // 导出弹窗
    gotoExport() {
      const option = {
        type: 'HistoryRecord',
        url: 'apiBackgroundLogOperationHistoryListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.agreement-list {
}
</style>
