<template>
  <div class="dingTalk-wrapper container-wrapper circular-bead">
    <el-form
      ref="dingTalkMessageSeting"
      v-loading="isLoading"
      :rules="dingTalkFormRule"
      :model="dingTalkFormData"
      class="dingTalk-form-wrapper ps-small-box"
      size="small"
    >
      <div class="l-title clearfix">
        <span class="float-l min-title-h">钉钉消息提醒</span>
      </div>
      <div class="form-line margin-button"></div>
      <el-form-item class="min-label-w" label="钉钉提醒开启">
        <el-switch
          v-model="dingTalkFormData.status"
          active-color="#ff9b45"
          inactive-color="#ffcda2"
          active-value="enable"
          inactive-value="delete"
        ></el-switch>
      </el-form-item>
      <div v-if="dingTalkFormData.status === 'enable'">
        <el-form-item class="min-label-w" label="提醒用户分组：" prop="cardUserGroups">
          <user-group-select
            :multiple="true"
            :collapse-tags="true"
            class="ps-input"
            style="width: 215px;"
            v-model="dingTalkFormData.cardUserGroups"
            placeholder="请选择分组"
          ></user-group-select>
        </el-form-item>
        <el-form-item class="min-label-w" label="提醒时间：">
          <div>
            <el-checkbox-group class="ps-checkbox" v-model="dingTalkFormData.weekSetting">
              <el-checkbox
                v-for="week in weekList"
                :key="week.value"
                :label="week.value"
                :name="week.name"
              >
                {{ week.name }}
              </el-checkbox>
            </el-checkbox-group>
            <div class="time-button">
              <el-button class="ps-origin-btn fixed-btn-except" type="primary" icon="el-icon-date">
                自定义时间
              </el-button>
              <el-date-picker
                v-model="dingTalkFormData.pickerDate"
                style="opacity: 0; position: relative; z-index: 999"
                type="daterange"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
                range-separator="至"
                @change="changePackerDate"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </div>
            <div style="margin-top: 5px;">
              <el-tag
                v-for="(time, index) in dingTalkFormData.exceptDateRange"
                :key="time.join('-')"
                closable
                @close="closeExceptHandle(index)"
                style="margin-right: 10px; margin-bottom: 5px;"
                type="info"
              >
                {{ time.join('~') }}
              </el-tag>
            </div>
          </div>
        </el-form-item>
        <div class="content-box">
          <el-form-item class="min-label-w" label="提醒内容：" prop="reminderTime">
            <span>餐段开启前：</span>
            <el-input
              class="ps-input"
              v-model="dingTalkFormData.reminderTime"
              style="width: 100px;"
            ></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-select
              v-model="dingTalkFormData.reminderTimeUnit"
              placeholder="请选择"
              class="ps-select m-l-20 m-r-10"
              size="small"
              style="width: 100px;"
            >
              <el-option label="分钟" value="minute"></el-option>
              <el-option label="小时" value="hour"></el-option>
            </el-select>
            <span>提醒</span>
          </el-form-item>
        </div>

        <el-form-item class="min-label-w" label="" prop="content">
          <el-input
            type="textarea"
            class="ps-input"
            style="max-width: 500px;"
            placeholder="请输入提醒内容"
            :autosize="{ minRows: 4, maxRows: 8 }"
            v-model="dingTalkFormData.content"
          ></el-input>
        </el-form-item>
      </div>

      <!-- footer start -->
      <div class="footer" style="text-align: center;">
        <el-button class="ps-origin-btn" type="primary" @click="dingTalkHandle">保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import UserGroupSelect from '@/components/UserGroupSelect'
import { to, parseTime } from '@/utils'
export default {
  data() {
    let validateCount = (rule, value, callback) => {
      let reg = /^\d+$/
      if (!value) {
        return callback()
      } else {
        if (!reg.test(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      }
    }
    return {
      isLoading: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      dingTalkFormData: {
        status: false,
        cardUserGroups: [],
        weekSetting: [], // 星期限制
        pickerDate: [],
        exceptDateRange: [], // 日期限制
        reminderTime: '', // 提醒时间
        reminderTimeUnit: 'minute',
        content: ''
      },
      dingTalkFormRule: {
        cardUserGroups: [{ required: true, message: '用户分组不能为空', trigger: 'change' }],
        content: [{ required: true, message: '提醒内容不能为空', trigger: 'change' }],
        reminderTime: [
          { required: true, message: '请输入时间', trigger: 'change' },
          { validator: validateCount, trigger: 'change' }
        ]
      },
      weekList: [
        { name: '周一', value: 1 },
        { name: '周二', value: 2 },
        { name: '周三', value: 3 },
        { name: '周四', value: 4 },
        { name: '周五', value: 5 },
        { name: '周六', value: 6 },
        { name: '周日', value: 7 }
      ]
    }
  },
  components: {
    UserGroupSelect
  },
  created() {
    this.messageSettingList()
  },
  mounted() {},
  methods: {
    async messageSettingList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMessagesDingTalkMessageSettingListPost())
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let results = res.data.results
        if (results && Object.keys(results) && Object.keys(results).length) {
          this.dingTalkFormData = {
            status: results.status,
            cardUserGroups: results.card_user_groups,
            weekSetting: results.week_setting, // 星期限制
            reminderTime: results.reminder_time, // 提醒时间
            reminderTimeUnit: results.reminder_time_unit,
            content: results.content
          }
          if (results.date_setting) {
            this.dingTalkFormData.exceptDateRange = results.date_setting.map(time => {
              return [
                parseTime(time.start_date, '{y}-{m}-{d}'),
                parseTime(time.end_date, '{y}-{m}-{d}')
              ]
            })
          }
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 日期现在选择
    changePackerDate(e) {
      if (e && e.length) {
        if (!this.isRepeatDate(e)) {
          this.dingTalkFormData.exceptDateRange.push(e)
        } else {
          this.$message.error('时间重叠了！')
        }
        this.dingTalkFormData.pickerDate = []
      }
    },
    // 检查时间是否重叠
    isRepeatDate(arr) {
      let repeat = false
      const start = new Date(arr[0]).getTime()
      const end = new Date(arr[1]).getTime()
      this.dingTalkFormData.exceptDateRange.forEach(item => {
        const itemStart = new Date(item[0]).getTime()
        const itemEnd = new Date(item[1]).getTime()
        if (start >= itemStart && end <= itemEnd) {
          repeat = true
        }
      })
      return repeat
    },
    // 日期的删除
    closeExceptHandle(index) {
      // 页面调接口赋值后 splice删除后不知道为啥页面不更新
      this.dingTalkFormData.exceptDateRange.splice(index, 1)
      this.$forceUpdate()
    },
    formatData() {
      let params = {
        status: this.dingTalkFormData.status,
        card_user_groups: this.dingTalkFormData.cardUserGroups,
        reminder_time_unit: this.dingTalkFormData.reminderTimeUnit,
        reminder_time: this.dingTalkFormData.reminderTime,
        content: this.dingTalkFormData.content
      }
      // 周
      if (this.dingTalkFormData.weekSetting.length > 0) {
        params.week_setting = this.dingTalkFormData.weekSetting
      }
      // 自定义时间
      if (this.dingTalkFormData.exceptDateRange.length > 0) {
        params.date_setting = this.dingTalkFormData.exceptDateRange.map(v => {
          return {
            start_date: v[0],
            end_date: v[1]
          }
        })
      }
      return params
    },
    dingTalkHandle() {
      this.$confirm(`确定要保存吗?`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.$refs.dingTalkMessageSeting.validate(valid => {
              if (valid) {
                if (this.isLoading) return
                let params = this.formatData()
                this.messageSettingModify(params)
              } else {
              }
            })
            instance.confirmButtonLoading = false
            done()
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 修改提醒
    async messageSettingModify(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMessagesDingTalkMessageSettingModifyPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.messageSettingList()
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
.dingTalk-wrapper {
  margin-top: 20px;
  padding: 20px;
  .dingTalk-form-wrapper {
    max-width: 1106px;
  }
  .form-item {
    .form-label {
      display: inline-block;
      width: 180px;
      line-height: 32px;
      text-align: right;
    }
    .form-content {
      display: inline-block;
      line-height: 32px;
      vertical-align: top;
    }
  }
  .min-label-w {
    .el-form-item__label {
      min-width: 180px;
    }
    .el-form-item__content {
      margin-left: 180px;
    }
  }
  .time-button {
    position: relative;
    height: 36px;
    .fixed-btn-except {
      position: absolute;
      top: 0;
      left: 0;
      cursor: pointer;
      z-index: 1;
    }
  }
  .footer {
    margin-top: 30px;
    .el-button {
      min-width: 180px;
    }
  }
  .margin-button {
    margin-bottom: 15px;
  }
  .content-box {
    display: flex;
  }
}
</style>
