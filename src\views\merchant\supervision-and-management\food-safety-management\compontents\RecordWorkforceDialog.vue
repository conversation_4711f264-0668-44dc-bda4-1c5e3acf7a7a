<template>
  <el-drawer :visible.sync="visible" :title="title" :loading.sync="isLoading" @close="handleClose" size="878px"
    :wrapperClosable="false" class="ps-el-drawer">
    <div class="p-10 ps-flex flex-align-center">
      <div class="m-r-10 m-l-10">时间：</div>
      <el-date-picker v-model="chooseDate" type="daterange" :clearable="false" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" align="left" unlink-panels range-separator="⇀" start-placeholder="请选择起始日期"
        end-placeholder="请选择结束日期" :picker-options="pickerOptions" class="ps-picker m-t-10"
        popper-class="ps-poper-picker" @change="dateChange">
      </el-date-picker>
    </div>
    <div class="table-wrap m-t-10 m-l-10 m-r-10">
      <el-table ref="userListRef" :data="tableData" tooltip-effect="dark" header-row-class-name="table-header-row"
        v-loading="isLoading">
        <el-table-column prop="operate_time" label="操作时间" align="center"></el-table-column>
        <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
        <el-table-column prop="operate_type_alias" label="操作" align="center"></el-table-column>
        <el-table-column prop="detail" width="280px" label="操作内容" align="center"></el-table-column>
      </el-table>
    </div>
    <div class="block ps-pagination">
      <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'" :total="totalCount"></pagination>
    </div>
    <div class="ps-el-drawer-footer m-l-20">
      <el-button class="ps-btn" type="primary" @click="handleClose" >
        关闭
      </el-button>
    </div>
  </el-drawer>
  <!-- end -->
</template>

<script>
import { to, deepClone } from '@/utils'
import * as dayjs from 'dayjs'

export default {
  name: 'RecordWorkforceDialog',
  props: {
    loading: Boolean,
    isshow: Boolean,
    title: {
      type: String,
      default: '历史记录'
    },
    limitId: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: 'default'
    },
    useDate: {
      type: String,
      default: ''
    },
    confirm: Function
  },

  data() {
    return {
      isLoading: false,
      chooseDate: [],
      personList: [],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [] // 表格数据
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        const recentDay = [
          dayjs()
            .subtract(3, 'day')
            .format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD')
        ]
        this.chooseDate = recentDay
        console.log("this.visible", recentDay)
        this.getRecordList()
      }
    }
  },
  mounted() {
  },
  methods: {
    clickCancleHandle() {
      this.handleClose()
    },
    // 确认选择
    async getRecordList() {
      this.isBtnLoading = true
      this.isLoading = true
      let params = {
        page: this.currentPage,
        page_size: this.pageSize,
        start_date: this.chooseDate[0] || '',
        end_date: this.chooseDate[1] || ''
      }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionCanteenSafetyManagementGetScheduleHistoryPost(params))
      this.isBtnLoading = false
      this.isLoading = false
      if (err) {
        return this.$message.error('获取失败')
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        let results = data.results || []
        this.tableData = deepClone(results)
        this.totalCount = data.count
      } else {
        this.$message.error(res.msg)
      }
    },

    // 关闭
    handleClose(e) {
      this.isLoading = false
      this.chooseDate = []
      this.visible = false
      this.type = 'default'
      this.personList = []
      this.tableData = []
      this.currentPage = 1
      this.$emit('close', false)
    },
    // 设置人员列表
    setPersonList(list) {
      if (!list) {
        return
      }
      this.personList = deepClone(list)
    },
    // 日期选择
    dateChange(value) {
      console.log("dateChange", value);
      this.currentPage = 1
      this.getRecordList()
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getRecordList()
    }
  }
}
</script>

<style lang="scss" scoped>
.ps-picker {
  width: 400px !important;
}
.flex-align-center{
  align-items: center;
}
</style>
