<template>
  <div class="SupplierManagement container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="origin" type="add" @click="gotoAddHandle('add')">新建供应商</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text"  @click="modifyHandle(row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-text"  @click="showDetailHandle(row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right; ">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <SupplierManagementDetail :is-show.sync="showDetailDrawer" :info-data="dialogInfo" :confirmShow="false" :size="760" />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce } from '@/utils'
import SupplierManagementDetail from './SupplierManagementDetail'

export default {
  name: 'MaterialWarehouse',
  components: {
    SupplierManagementDetail
  },
  mixins: [exportExcel],
  data() {
    return {
      importLink: '',
      supplierId: '',
      tableData: [{}],
      isLoading: false, // 刷新数据
      searchFormSetting: {
        supper_manage_name: {
          type: 'input',
          value: '',
          label: '供应商名称',
          placeholder: '请输入供应商名称',
          maxlength: 20
        }
      },
      tableSettings: [
        { label: '供应商名称', key: 'name' },
        { label: '统一社会信用代码', key: 'credit_code' },
        { label: '创建组织', key: 'create_org_name' },
        { label: '创建时间', key: 'create_time' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right" }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      showDetailDrawer: false, // 详情弹窗
      dialogInfo: {}
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSupplierManagementList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getSupplierManagementList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getSupplierManagementList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminSupplierManageListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    deleteHandle(type, data) {
      this.$confirm('确定删除吗？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const res = await this.$apis.apiBackgroundDrpSupplierManageDeletePost({
              id: data.id
            })
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getSupplierManagementList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSupplierManagementList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    showDetailHandle(row) {
      this.dialogInfo = row
      this.showDetailDrawer = true
    },
    modifyHandle(data) {
      let query = {}
      if (data) {
        query.id = data.id
        query.type = 'modify'
      }
      this.$router.push({
        name: 'SuperModifySupplierManagement',
        query
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
