<template>
  <div class="driverInformation-box">
    <div v-for="(driver, index) in driverList" :key="index" class="driver-item">
      <div class="form-item">
        <span class="form-label">司机姓名：</span>{{ driver.name }}
      </div>
      <div class="form-item">
        <span class="form-label">联系方式：</span>{{ driver.phone }}
      </div>
      <div class="form-item">
        <span class="form-label">证件信息：</span>
        <div class="form-img-box">
          <div v-for="(img, k) in driver.health_certificate" :key="img" class="form-img">
            <el-image
              class="detault-img m-r-6 pointer"
              :src="img"
              fit="contain"
              @click="clickViewerHandler(driver.health_certificate, k)"
            ></el-image>
            <div class="text-center">健康证</div>
          </div>
          <div v-for="(img, k) in driver.driving_licence" :key="img" class="form-img">
            <el-image
              class="detault-img m-r-6 pointer"
              :src="img"
              fit="contain"
              @click="clickViewerHandler(driver.driving_licence, k)"
            ></el-image>
            <div class="text-center">驾驶证</div>
          </div>
        </div>
      </div>
      <el-divider v-if="showDivider"></el-divider>
    </div>
    <image-viewer v-model="showViewer" :initial-index="imgIndex" :z-index="3000" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
  </div>
</template>

<script>
// 司机信息
export default {
  name: 'DriverInformation',
  props: {
    driverList: {
      type: Array,
      default: () => []
    },
    showDivider: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      imgIndex: 0,
      previewSrcList: [],
      showViewer: false
    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {
    clickViewerHandler(row, index) {
      // don't show viewer when preview is false
      this.previewSrcList = row || []
      this.imgIndex = index
      this.showViewer = true;
    },
    closeViewer() {
      this.showViewer = false;
    }
  }
};
</script>

<style scoped lang="scss">
.driverInformation-box {
  .form-item {
    display: flex;
    font-size: 14px;
    line-height: 30px;
  }
  .form-img-box {
    flex: 1;
  }
  .form-img {
    display: inline-block;
  }
  .detault-img {
    width: 160px;
    height: 92px;
    border: 1px dashed #e1e1e1;
    border-radius: 2px;
  }
}
</style>
