// Sidebar menu ellipsis styles
.el-submenu__title,
.el-menu-item {
  padding-right: 20px !important;
  
  span {
    display: inline-block;
    width: calc(100% - 40px); // Account for icon and padding
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
    position: relative;
  }
}

// Add tooltip for overflowing text
.el-menu-item span[title],
.el-submenu__title span[title] {
  &:hover:after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: -5px;
    width: max-content;
    max-width: 200px;
    background-color: #303133;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 9999;
    opacity: 0.9;
    margin-left: 10px;
    white-space: normal;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    pointer-events: none;
  }
}

// For submenu titles, ensure the icon and the text are properly aligned
.el-submenu__title {
  display: flex !important;
  align-items: center;
  
  .svg-icon, .sub-el-icon, i {
    margin-right: 5px;
    flex-shrink: 0;
  }
  
  span {
    flex: 1;
    min-width: 0;
  }
}

// For menu items, ensure the icon and the text are properly aligned
.el-menu-item {
  display: flex !important;
  align-items: center;
  
  .svg-icon, .sub-el-icon, i {
    margin-right: 5px;
    flex-shrink: 0;
  }
  
  span {
    flex: 1;
    min-width: 0;
  }
} 