<template>
  <div class="container-wrapper thirdSetting" v-loading="isLoading">
    <!-- tab end -->
    <!-- <div class="l-title clearfix">
      <span class="float-l min-title-h">第三方对接</span>
      <el-button v-if="formOperate === 'detail'" @click="changeOperate" size="mini" class="float-r">编辑</el-button>
    </div> -->
    <div class="third-box" v-for="(third, key) in formThirdData" :key="'third'+key">
      <el-form
        :ref="`thirdRef${key}`"
        :model="formThirdData[key]"
        :rules="thirdFormRuls"
        size="small"
        label-width="130px"
      >
        <el-form-item  :label="third.name" prop="enable" class="third-item">
          <el-switch
            v-model="third.enable"
            active-color="#ff9b45"
            @change="changeEnableHandle($event, key)"
          ></el-switch>
          <!-- @change="saveAppidHandle(key)" -->
          <el-button type="primary" size="small" v-if="third.name == '车辆管理'"  class="ps-origin-btn m-l-140" @click="gotoThirdPath(third)">厂商管理</el-button>
        </el-form-item>
        <template v-if="third.enable">
          <el-form-item v-for="(item, index) in third.template" :key="'template'+index" :label="item.name" :prop="'data.' + item.key" :rules="thirdFormRuls[item.key] || []" class="third-item">
            <el-input size="small" v-if="!item.type || item.type==='input'" :disabled="item.disabled" v-model="formThirdData[key]['data'][item.key]"></el-input>
            <el-input size="small" v-if="item.type==='textarea'" type="textarea" :rows="3" :disabled="item.disabled" v-model="formThirdData[key]['data'][item.key]"></el-input>
            <el-select size="small" v-if="item.type==='select'" :disabled="item.disabled" v-model="formThirdData[key]['data'][item.key]" placeholder="">
              <el-option v-for="option in item.value" :key="option.value" :label="option.name" :value="option.value"></el-option>
            </el-select>
            <div v-if="item.type === 'org_select' && organizationData.level == 0">
              <!--组织选择-->
              <organization-select
                class="search-item-w ps-input w-250"
                v-model="formThirdData[key]['data'][item.key]"
                placeholder="请选择"
                :clearable="true"
                :multiple="false"
                :checkStrictly="true"
                :isLazy="false"
                :append-to-body="true"
                @change="searchHandle"
                role='super'
                :parentId="organizationData.id"
                >
            </organization-select>
            </div>
            <div class="" v-if="item.type === 'text'">{{ formThirdData[key]['data'][item.key] }}</div>
            <el-button type="primary" size="small" v-loading="isLoadingSchool" v-if="key == 'abc_school'&& index == third.template.length-1 "  class="ps-origin-btn m-l-20" @click="getSchoolInfo(third)">获取学校名称与ID</el-button>
            <!-- 勤网 -->
            <el-button type="primary" size="small" v-loading="isLoadingSchool" v-if="key == 'abc_diligent'&& index == third.template.length-1 "  class="ps-origin-btn m-l-20" @click="getAbcDiligentSchoolInfo(third)">获取学校名称与ID</el-button>
          </el-form-item>
          <el-form-item v-if="key == 'abc_diligent' && third.data.name" label="学校名称" class="third-item">{{ third.data.name }}</el-form-item>
          <div v-if="key == 'abc_school'">
            <div v-for="(schoolItem, schoolIndex) in schoolList" :key="schoolIndex" class="ps-flex align-center schoolTag">
              <el-checkbox class="ps-checkbox" v-model="schoolItem.enable" @change="schoolCheckChange($event, schoolIndex)"></el-checkbox>
              <div class="">
                <el-form-item label="学校名称：" class="third-item" >
                  {{ schoolItem.schoolName }}
                </el-form-item>
                <el-form-item label="学校ID：" class="third-item" v-if="key == 'abc_school'">
                {{ schoolItem.schoolKey }}
                </el-form-item>
              </div>
            </div>
          </div>
          <el-form-item>
            <div class="add-wrapper">
              <el-button type="primary" size="small" class="ps-origin-btn" @click="saveAppidHandle(key)">保存</el-button>
            </div>
          </el-form-item>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script>
import { to, debounce, deepClone } from '@/utils'
import { URL_MANUFACTURER, URL_MANUFACTURER_STAGING } from '@/views/merchant/user-center/constants/cardManageConstants'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import OrganizationSelect from "@/components/OrganizationSelect"

export default {
  name: 'ThirdSetting',
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: { // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    organizationData: Object,
    restoreHandle: Function
  },
  components: {
    OrganizationSelect
  },
  data() {
    return {
      formOperate: 'detail',
      isLoading: false,
      thirdSettingList: [], // 第三方设置
      formThirdData: {
        // aa: {
        //   third: '',
        //   secret_key: ''
        // }
      }, // 表单内容
      thirdTemplateList: {}, // 模板
      thirdData: {},
      thirdFormRuls: {
        third: [{ required: true, message: '请先输入third', trigger: "change" }]
      },
      schoolName: '', // 学校名称
      schoolID: '', // 学校Id
      isLoadingSchool: false, // 获取学校按钮loading
      schoolList: [
      ] // 学校list
    }
  },
  computed: {
    checkIsFormStatus: function() {
      let show = false
      switch (this.formOperate) {
        case 'detail':
          show = false
          break;
        case 'modify':
          show = true
          break;
      }
      return show
    }
  },
  watch: {
  },
  created() {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      console.log("organizationData", this.organizationData);
      await this.getThirdSettingTemplate()
      this.getThirdSetting()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
    }, 300),
    changeOperate() {
      if (this.formOperate !== 'modify') {
        this.formOperate = 'modify'
      } else {
        this.formOperate = 'detail'
      }
    },
    // 获取第三方设置
    async getThirdSetting() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationGetThirdSettingsPost({
        id: this.organizationData.id
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // if (res.data && res.data.length) {
        res.data.forEach(v => {
          // this.setFormKeyValueHandle(this.formThirdData, v)
          this.thirdData[v.third_name] = v
          // 如果是农行
          if (v.third_name === 'abc_school' && Reflect.has(v, 'extra')) {
            var schoolList = Reflect.has(v.extra, "school_list") ? v.extra.school_list : []
            if (schoolList) {
              this.schoolList = deepClone(schoolList)
            }
          }
        })
        Object.keys(this.thirdTemplateList).forEach(key => {
          this.setFormKeyValueHandle(this.formThirdData, (this.thirdData[key] ? this.thirdData[key] : {}), key)
          // 直接设置data的name
          if (key === 'abc_diligent' && this.thirdData[key] && this.thirdData[key].extra && this.thirdData[key].extra.name && this.formThirdData[key].data) {
            this.$set(this.formThirdData[key].data, 'name', this.thirdData[key].extra.name)
          }
          // set rules
          this.thirdTemplateList[key].keys.forEach(k => {
            if (k.required && !this.thirdFormRuls[k.key]) {
              let msg = ''
              switch (k.type) {
                case 'input':
                  msg = '输入'
                  break;
                case 'textarea':
                  msg = '输入'
                  break;
                case 'select':
                  msg = '选择'
                  break;
                default:
                  msg = '输入'
                  break;
              }
              // message: `请${msg}${k.name}`
              // const validatorFun = function (rule, value, callback) {
              //   console.log(11111, rule, value)
              // }
              // validator: validatorFun,
              this.$set(this.thirdFormRuls, k.key, [
                { required: true, message: `请${msg}${k.name}`, trigger: "change" }
              ])
            }
          })
        })
        // 因为不清楚要怎么在智慧校园里加，这里选择上面生成好了之后直接往里面塞
        let obj = {
          key: 'organization_id',
          name: '对接组织',
          required: true,
          type: 'org_select'
        }
        this.formThirdData.YDZHXY.template.splice(0, 0, obj)
        this.$set(this.formThirdData.YDZHXY.data, 'organization_id', this.thirdData.YDZHXY.extra.organization_id)
      } else {
        this.$message.error(res.msg)
      }
      console.log("formThirdData", this.formThirdData, this.thirdData);
    },
    // 设置表单字段
    setFormKeyValueHandle(list, data, type) {
      // let type = data.third_name
      if (!list[type]) {
        // 外层object
        this.$set(list, type, {})
      }
      // if (this.thirdTemplateList[type]) { // 判断是否有当前这个模板
      this.$set(list[type], 'template', this.thirdTemplateList[type].keys)
      this.$set(list[type], 'id', data.third_id ? data.third_id : '')
      this.$set(list[type], 'name', this.thirdTemplateList[type].name)
      this.$set(list[type], 'enable', data.enable ? data.enable : false)
      this.$set(list[type], 'data', {})
      this.thirdTemplateList[type].keys.forEach(v => {
        let value = data.extra && (data.extra[v.key] !== undefined) ? data.extra[v.key] : ''
        this.$set(list[type].data, v.key, value)
      })
      // }
    },
    // 获取第三方设置
    async getThirdSettingTemplate() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationThirdTemplateListPost({
        id: this.organizationData.id
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.thirdTemplateList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    changeEnableHandle(e, type) {
      if (!e) {
        this.modifyOrganization(type)
      }
      console.log(this.formThirdData)
    },
    // 发送请求
    async saveAppidHandle(type) {
      if (this.isLoading) {
        return
      }
      if (type && type === 'abc_school') {
        if (!this.schoolList && this.schoolList.length === 0) {
          this.$message.error("请先获取并选择学校！")
          return
        }
        var flag = false
        for (let i = 0; i < this.schoolList.length; i++) {
          if (this.schoolList[i].enable) {
            flag = true
            break
          }
        }
        if (!flag) {
          this.$message.error("请至少学则一个学校进行绑定")
          return
        }
      }
      console.log("saveAppidHandle", type);
      // this.modifyOrganization(type)
      this.$refs[`thirdRef${type}`][0].validate(valid => {
        if (valid) {
          this.modifyOrganization(type)
        }
      })
    },
    async modifyOrganization(type) {
      let params = {
        id: this.organizationData.id,
        // third_id: this.formThirdData[type].id,
        third_name: type,
        enable: this.formThirdData[type].enable,
        extra: this.formThirdData[type].data
      }
      if (this.formThirdData[type].id) {
        params.third_id = this.formThirdData[type].id
      }
      if (type === "abc_school") {
        params.extra.school_list = this.schoolList
      }
      if (type === 'abc_diligent') {
        if (!params.extra.schoolUuid) {
          delete params.extra.name
        }
      }
      this.isLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationModifyThirdSettingsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.formOperate = 'detail'
        this.restoreHandle(this.type, this.formOperate)
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 打开第三方
     */
    gotoThirdPath(data) {
      console.log("data", data);
      var thisData = data.data || ''
      var url = process.env.NODE_ENV === 'development' ? URL_MANUFACTURER : URL_MANUFACTURER_STAGING
      if (thisData && thisData.project_no.length > 0) {
        this.getCarToken(thisData, url)
      } else {
        window.open(url + 'login', '_blank')
      }
    },
    /***
     * 获取第三方车辆管理token
     */
    async getCarToken(data, urlPath) {
      var params = {
        project_no: data.project_no,
        app_secret: data.app_secret,
        appid: data.appid
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundCarTravelCarTravelInfoGenerateRedirectTokenPost(params))
      this.isLoading = false
      if (err) {
        window.open(urlPath + 'login', '_blank')
        return
      }
      if (res.code === 0) {
        var resultData = res.data || {}
        console.log("data", resultData);
        if (Reflect.has(resultData, "data") && Reflect.has(resultData.data, 'data') && Reflect.has(resultData.data.data, 'token')) {
          var newPath = urlPath + "parkingLot/homePage?token=" + resultData.data.data.token
          window.open(newPath, '_blank')
        } else {
          window.open(urlPath + 'login', '_blank')
        }
      } else {
        window.open(urlPath + 'login', '_blank')
      }
    },
    // 获取学校名称与ID
    async getSchoolInfo(itemData) {
      console.log("getSchoolInfo", itemData);
      this.isLoadingSchool = true
      var params = {
        id: this.organizationData.id,
        extra: itemData.data
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminAbcSchoolQuerySchoolPost(params))
      this.isLoadingSchool = false
      if (err) {
        return this.$message.error(err.message || '获取失败')
      }
      if (res && res.code === 0) {
        var data = res.data || []
        if (data) {
          this.schoolList = deepClone(data)
        }
      } else {
        this.$message.error(res.meg || '获取失败')
      }
    },
    // 获取勤网学校名称与ID
    async getAbcDiligentSchoolInfo(itemData) {
      console.log("getSchoolInfo", itemData);
      this.isLoadingSchool = true
      var params = {
        id: this.organizationData.id,
        extra: itemData.data
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminAbcSchoolSchoolMsgPost(params))
      this.isLoadingSchool = false
      if (err) {
        return this.$message.error(err.message || '获取失败')
      }
      if (res.code === 0) {
        if (res.data && res.data.name) {
          // 设置个name进去作为参数显示
          this.$set(itemData.data, 'name', res.data.name)
        }
      } else {
        this.$message.error(res.meg || '获取失败')
      }
    },
    // 选择学校改变监听
    schoolCheckChange(value, index) {
      console.log("schoolCheckChange", value, index);
      if (value) {
        var list = deepClone(this.schoolList)
        if (list) {
          list.forEach((item, itemIndex) => {
            if (index !== itemIndex) {
              item.enable = false
            }
          })
          this.schoolList = deepClone(list)
        }
      }
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.thirdSetting {
  position: relative;
  .third-box{
    margin-top: 10px;
    .third-item{
      .el-input{
        width: 260px;
      }
      .el-textarea{
        width: 320px;
      }
    }
  }
  .add-wrapper{
    margin: 20px 0 20px 60px;
    .el-button{
      width: 120px;
    }
  }
  .m-l-140{
    margin-left: 140px;
  }
  .align-center {
    align-items:  center;
  }
  .schoolTag {
    border-top:  1px solid sandybrown;
    width: 600px;
    padding-top: 15px;
  }

}
</style>
