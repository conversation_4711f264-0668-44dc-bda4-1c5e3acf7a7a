<template>
  <div>
    <div class="container-wrapper">
      <evaluate :form-setting="searchFormSetting" type="Super" :api-url="apiUrl" />
    </div>
  </div>
</template>

<script>
import { EVALUATE_LIST } from './constants'
// import { to } from '@/utils'
import Evaluate from "@/components/Evaluate"

export default {
  name: 'EvaluateList',
  components: { Evaluate },
  data() {
    return {
      searchFormSetting: EVALUATE_LIST,
      apiUrl: 'apiBackgroundOperationManagementAdminOrderEvaluationListPost'
    }
  },
  mounted() {
    // this.initLoad()
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
</style>
