<template>
  <div>
    <div class="table-wrapper p-20" style="background-color: #fff; margin-top: 0px;" v-loading="isLoading">
      <div class="risk-prediction">
        <div class="risk-prediction-box">
          <div class="risk-prediction-box-header" style="background-color: #fff;">
            <div class="risk-prediction-box-header-title">参考数据</div>
            <div class="align-r">
              <el-date-picker
                class="w-150"
                v-model="selectMonth"
                size="mini"
                type="month"
                placeholder="选择月"
                :clearable="false"
                :picker-options="pickerOptions"
                @change="getHistoricalData">
              </el-date-picker>
            </div>
          </div>
          <div class="risk-prediction-content" v-for="(item, index) in referenceDataList" :key="index">
            <reference-data-card
              :title="item.title"
              :referenceData="item.referenceData"
              :isRatio="item.isRatio"
              :showIncome="item.showIncome"
              :totalIncome="item.totalIncome"
              :totalTitle="item.totalTitle"
              class="m-b-10" />
          </div>
        </div>
        <div class="risk-prediction-box">
          <div class="risk-prediction-box-header1" style="background-color: #fff;">
            <div class="risk-prediction-box-header-title m-r-10">预测本月数据</div>
            <el-tooltip placement="top">
              <div slot="content">
                <div class="risk-prediction-box">
                  <div class="m-b-5">说明：</div>
                  <div class="m-b-5">1.风险预测仅供参考，预测数据根据《比例法》计算得出，为了规避真实风险，请及时调整经营情况。</div>
                  <div class="m-b-5">2.消费天数默认取上学日/教学日（周一到周五）为消费天数进行预测。可操作【消费天数】进行调整，调整数据下一个自然月后会自动清除。</div>
                  <div class="m-b-5">3.预测本月数据会根据已有数据进行计算，数据取（T-1）的模式。</div>
                  <div class="m-b-5">4.参考数据均为0时，不进行预测，可切换参考数据进行本月预测数据。</div>
                  <div class="m-b-5">5.本月数据均为0时，不进行预测。</div>
                </div>
              </div>
              <div style="font-size: 24px;">
                <i class="el-icon-question"></i>
              </div>
            </el-tooltip>
          </div>
          <div class="risk-prediction-content" v-for="(item, index) in predictionDataList" :key="index">
            <reference-data-card
              :title="item.title"
              :referenceData="item.referenceData"
              :isRatio="item.isRatio"
              :showIncome="item.showIncome"
              :totalIncome="item.totalIncome"
              @showDrawer="showDrawer"
              class="m-b-10" />
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'补充数据'"
        :visible="detailDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="detailDrawerFormRef" :model="detailDrawerForm" label-width="100px" label-position="right" :rules="rules">
            <el-form-item label="消费天数" prop="dayCount">
              <el-input v-model="detailDrawerForm.dayCount" class="w-300" resize="none" placeholder="请输入，不可为空不可为0"></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="cancelHandle">取消</el-button>
            <el-button class="w-100 ps-origin-btn" @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { divide } from '@/utils';
import ReferenceDataCard from './ReferenceDataCard.vue'
import dayjs from 'dayjs'

export default {
  components: { ReferenceDataCard },
  props: ['isShow'],
  data() {
    const checkCount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('预测天数不能为空'));
      } else if (value <= 0) {
        return callback(new Error('预测天数需不小于一天'));
      } else {
        return callback();
      }
    }
    return {
      isLoading: false,
      selectMonth: '',
      pickerOptions: {
        disabledDate: (time) => {
          // 获取当前月的起始时间（例如：2025年3月1日 00:00:00）
          const currentMonthStart = new Date(
            new Date().getFullYear(),
            new Date().getMonth(),
            1
          );
          // 禁用当前月及之后的月份（比较时间戳）
          return time >= currentMonthStart;
        }
      },
      referenceDataList: [
        {
          key: 'icp',
          title: '收支利润',
          referenceData: [
            {
              key: 'profit',
              label: '利润',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'profit_rate',
              label: '利润率',
              amount: '0',
              unit: '%',
              ratio: ''
            },
            {
              key: 'total_income',
              label: '总收入',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'avg_day_income',
              label: '平均日收入',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'total_cost',
              label: '总成本',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'avg_day_cost',
              label: '平均日成本',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'consume_day',
              label: '消费天数',
              amount: '0',
              unit: '天',
              ratio: ''
            }
          ],
          isRatio: false,
          showIncome: false,
          totalIncome: "0.00",
          totalTitle: ""
        },
        {
          key: 'operating_income',
          title: '营业收入-占比',
          referenceData: [
            {
              key: 'store_consume',
              label: '储值消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'teacher_store_consume',
              label: '教师储值消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'subsidy_consume',
              label: '补贴消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'teacher_subsidy_consume',
              label: '教师补贴消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'third_consume',
              label: '第三方消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'operating_other_income',
              label: '其他收入',
              amount: '0',
              unit: '元',
              ratio: '0'
            }
          ],
          isRatio: true,
          showIncome: true,
          totalIncome: "0.00",
          totalTitle: "合计收入"
        },
        {
          key: 'non_operating_income',
          title: '非营业收入-占比',
          referenceData: [
            {
              key: 'government_subsidy',
              label: '政府补助',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'public_welfare_donation',
              label: '公益捐赠',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'non_operating_other_income',
              label: '其他收入',
              amount: '0',
              unit: '元',
              ratio: '0'
            }
          ],
          isRatio: true,
          showIncome: true,
          totalIncome: "0.00",
          totalTitle: "合计收入"
        },
        {
          key: 'operating_cost',
          title: '营业成本-占比',
          referenceData: [
            {
              key: 'raw_material_cost',
              label: '原材料成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'utilities',
              label: '水电气成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'labor_cost',
              label: '人工成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'operating_other_costs',
              label: '其他成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            }
          ],
          isRatio: true,
          showIncome: true,
          totalIncome: "0.00",
          totalTitle: "合计成本"
        },
        {
          key: 'non_operating_cost',
          title: '非营业成本-占比',
          referenceData: [
            {
              key: 'non_operating_other_costs',
              label: '其他成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            }
          ],
          isRatio: true,
          showIncome: true,
          totalIncome: "0.00",
          totalTitle: "合计成本"
        }
      ], // 历史数据
      predictionDataList: [
        {
          key: 'icp',
          title: '收支利润',
          referenceData: [
            {
              key: 'profit',
              label: '利润',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'profit_rate',
              label: '利润率',
              amount: '0',
              unit: '%',
              ratio: ''
            },
            {
              key: 'total_income',
              label: '总收入',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'avg_day_income',
              label: '平均日收入',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'total_cost',
              label: '总成本',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'avg_day_cost',
              label: '平均日成本',
              amount: '0',
              unit: '元',
              ratio: ''
            },
            {
              key: 'consume_day',
              label: '消费天数',
              amount: '0',
              unit: '天',
              ratio: '',
              showButton: true
            },
            {
              key: 'actual_consume_day',
              label: '已消费天数',
              amount: '0',
              unit: '天',
              ratio: ''
            }
          ],
          isRatio: false,
          showIncome: false,
          totalIncome: "0.00",
          totalTitle: ""
        },
        {
          key: 'operating_income',
          title: '营业收入-占比',
          referenceData: [
            {
              key: 'store_consume',
              label: '储值消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'teacher_store_consume',
              label: '教师储值消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'subsidy_consume',
              label: '补贴消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'teacher_subsidy_consume',
              label: '教师补贴消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'third_consume',
              label: '第三方消费',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'operating_other_income',
              label: '其他收入',
              amount: '0',
              unit: '元',
              ratio: '0'
            }
          ],
          isRatio: true,
          showIncome: true,
          totalIncome: "0.00",
          totalTitle: "合计收入"
        },
        {
          key: 'non_operating_income',
          title: '非营业收入-占比',
          referenceData: [
            {
              key: 'government_subsidy',
              label: '政府补助',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'public_welfare_donation',
              label: '公益捐赠',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'non_operating_other_income',
              label: '其他收入',
              amount: '0',
              unit: '元',
              ratio: '0'
            }
          ],
          isRatio: true,
          showIncome: true,
          totalIncome: "0.00",
          totalTitle: "合计收入"
        },
        {
          key: 'operating_cost',
          title: '营业成本-占比',
          referenceData: [
            {
              key: 'raw_material_cost',
              label: '原材料成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'utilities',
              label: '水电气成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'labor_cost',
              label: '人工成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            },
            {
              key: 'operating_other_costs',
              label: '其他成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            }
          ],
          isRatio: true,
          showIncome: true,
          totalIncome: "0.00",
          totalTitle: "合计成本"
        },
        {
          key: 'non_operating_cost',
          title: '非营业成本-占比',
          referenceData: [
            {
              key: 'non_operating_other_costs',
              label: '其他成本',
              amount: '0',
              unit: '元',
              ratio: '0'
            }
          ],
          isRatio: true,
          showIncome: true,
          totalIncome: "0.00",
          totalTitle: "合计成本"
        }
      ], // 预测数据
      detailDrawerShow: false,
      detailDrawerForm: {
        dayCount: ''
      },
      rules: {
        dayCount: [
          { required: true, message: '消费天数不能为空', trigger: ['change', 'blur'] },
          { validator: checkCount, trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  created() {
    // this.getHistoricalData()
  },
  watch: {
    isShow: {
      handler(val) {
        if (val) {
          this.selectMonth = dayjs().subtract(1, 'month').format('YYYY-MM')
          this.detailDrawerForm.dayCount = this.getWorkDays()
          this.getHistoricalData()
        }
      },
      immediate: true
    }
  },
  methods: {
    getWorkDays() {
      let predictionStartDate = dayjs().startOf('month').format('YYYY-MM-DD')
      let predictionEndDate = dayjs().endOf('month').format('YYYY-MM-DD')
      let currentDay = dayjs(predictionStartDate)
      let workingDays = 0
      while (currentDay.isBefore(predictionEndDate) || currentDay.isSame(predictionEndDate)) {
        const weekday = currentDay.day(); // 获取星期（0为周日，6为周六）
        console.log('weekday', weekday)
        if (weekday !== 0 && weekday !== 6) {
          workingDays++;
        }
        currentDay = currentDay.add(1, 'day');
      }
      return workingDays
    },
    getHistoricalData() {
      this.isLoading = true
      let predictionStartDate = dayjs().startOf('month').format('YYYY-MM-DD')
      let predictionEndDate = dayjs().endOf('month').format('YYYY-MM-DD')
      this.$apis.apiBackgroundFundSupervisionEarlyWarningRiskPredictionPost({
        start_date: dayjs(this.selectMonth).format('YYYY-MM-DD'),
        end_date: dayjs(this.selectMonth).endOf('month').format('YYYY-MM-DD'),
        prediction_day: this.detailDrawerForm.dayCount,
        prediction_start_date: predictionStartDate,
        prediction_end_date: predictionEndDate
      }).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          if (!res.data.reference_consume_day) {
            this.$message.error('参考数据为0，请切换参考数据')
          } else if (!res.data.prediction_consume_day) {
            this.$message.error('本月数据为0，无法预测')
          }
          // 分别处理历史数据以及预测数据
          if (res.data.reference_data) {
            this.setData(this.referenceDataList, res.data.reference_data)
          }
          if (res.data.prediction_data) {
            this.setData(this.predictionDataList, res.data.prediction_data)
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    setData(arrList, dataObj) {
      let keyArr = Object.keys(dataObj)
      arrList.forEach(item => {
        if (keyArr.includes(item.key)) {
          item.referenceData.forEach(item2 => {
            if (item.key === 'icp') {
              if (item2.key === 'profit_rate') {
                item2.amount = dataObj[item.key][item2.key]
              } else {
                item2.amount = (item2.key === 'consume_day' || item2.key === 'actual_consume_day') ? dataObj[item.key][item2.key] : divide(dataObj[item.key][item2.key])
              }
            } else {
              item2.amount = divide(dataObj[item.key][item2.key].pay_fee)
              item2.ratio = dataObj[item.key][item2.key].percent
            }
          })
        }
        item.totalIncome = divide(dataObj[item.key].total_fee)
      })
    },
    showDrawer(count) {
      this.detailDrawerForm.dayCount = count
      this.detailDrawerShow = true
    },
    cancelHandle() {
      this.detailDrawerForm.count = 0
      this.detailDrawerShow = false
    },
    saveHandle() {
      this.getHistoricalData()
      this.cancelHandle()
    }
  }
}
</script>

<style lang="scss" scoped>
.content-title {
  font-size: 20px;
  font-weight: 900;
}
.risk-prediction {
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 20px;
  &-box {
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      &-title {
        border-left: 4px solid orange;
        padding-left: 10px;
        font-weight: 700;
        font-size: 18px;
      }
    }
    &-header1 {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 20px;
    }
  }
}
</style>
