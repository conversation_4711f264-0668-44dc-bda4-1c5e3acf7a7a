<template>
  <div class="ps-el-drawer">
    <el-drawer
      :title="drawerType === 'add' ? '创建档案' : '编辑档案'"
      :visible="visible"
      :show-close="false"
      size="30%">
      <div class="p-20 flex-col" v-loading="formLoading">
        <el-form
          :model="formData"
          @submit.native.prevent
          status-icon
          ref="formDataRef"
          :rules="formDataRules"
          label-width="90px"
          label-position="right"
        >
          <el-form-item label="选择用户" prop="name">
            <el-select
              v-model="formData.name"
              placeholder="输入用户姓名搜索"
              :filterable="true"
              :remote="true"
              :remote-method="getUserInfo"
              :loading="isLoading"
              @change="showInfo"
              :disabled="drawerType === 'edit'"
              class="w-220">
              <el-option
                v-for="(item, index) in userList"
                :key="index"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="人员编号">
            <el-input v-model="formData.person_no" class="w-220" placeholder="选择用户后回填" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="formData.phone" class="w-220" placeholder="选择用户后回填" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="gender">
            <el-radio v-model="formData.gender" label="MAN">男</el-radio>
            <el-radio v-model="formData.gender" label="WOMEN">女</el-radio>
          </el-form-item>
          <el-form-item label="身高" prop="height">
            <el-input v-model="formData.height" class="w-220 m-r-10" placeholder="请输入" type="number"></el-input>
            <span>cm</span>
          </el-form-item>
          <el-form-item label="体重" prop="weight">
            <el-input v-model="formData.weight" class="w-220 m-r-10" placeholder="请输入" type="number"></el-input>
            <span>kg</span>
          </el-form-item>
          <el-form-item label="目标体重" prop="targe_weight">
            <el-input v-model="formData.targe_weight" class="w-220 m-r-10" placeholder="请输入" type="number"></el-input>
            <span>kg</span>
          </el-form-item>
          <el-form-item label="出生日期" prop="birthday">
            <el-date-picker
              class="m-r-10"
              v-model="formData.birthday"
              type="date"
              placeholder="选择日期"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions">
            </el-date-picker>
            <span>{{ computedAge(formData.birthday) }}岁</span>
          </el-form-item>
          <el-form-item label="从事职业" prop="job">
            <el-select v-model="formData.job" placeholder="请选择" class="w-220">
              <el-option
                v-for="(item, index) in jobList"
                :key="index"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="个人特征" prop="disease">
            <el-select v-model="formData.disease"
              :multiple="true"
              :clearable="true"
              :filterable="true"
              :collapse-tags="true"
              placeholder="请选择"
              class="w-220">
              <el-option
                v-for="(item, index) in diseaseList"
                :key="index"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="饮食禁忌">
            <el-select v-model="formData.taboo_food"
              :collapse-tags="true"
              :multiple="true"
              :filterable="true"
              :clearable="true"
              @change="showSelectFood"
              placeholder="请选择"
              class="w-220">
              <el-option
                v-for="(item, index) in foodList"
                :key="index"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
            <div class="ps-flex-align-c flex-wrap m-t-5">
              <el-tag
                v-for="(item, index) in selectFoodList"
                :key="index"
                :size="'small'"
                type="info"
                closable
                @close="deleteHandle(index, item)"
                disable-transitions
                class="m-r-10 m-t-5 m-b-5">
                <span class="el-select__tags-text">{{ item.name }}</span>
              </el-tag>
            </div>
          </el-form-item>
          <el-form-item label="重点关注">
            <el-radio v-model="formData.is_follow" :label="true">是</el-radio>
            <el-radio v-model="formData.is_follow" :label="false">否</el-radio>
          </el-form-item>
        </el-form>
        <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
          <div class="m-r-30">
            <el-button size="small"  class="w-100" @click="cancel">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="save(drawerType)">保存</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { JOB_LIST } from '../constants';
import { deepClone } from '@/utils';
export default {
  props: {
    drawerType: String,
    isShow: Boolean,
    fileData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    foodList: {
      type: Array,
      default: () => {
        return []
      }
    },
    diseaseList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    const checkHeight = (rule, value, callback) => {
      if (value) {
        let reg = /^(\d+(\.\d{1})?)$/
        if (value > 300) {
          callback(new Error('请输入正确的身高'))
        } else if (!reg.test(value)) {
          console.log('没通过校验', value, typeof value)
          callback(new Error('至多一位小数'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入身高'))
      }
    }
    const checkWeight = (rule, value, callback) => {
      if (value) {
        let reg = /^(\d+(\.\d{1})?)$/
        if (value > 200) {
          callback(new Error('请输入正确的体重'))
        } else if (!reg.test(value)) {
          console.log('没通过校验', value, typeof value)
          callback(new Error('至多一位小数'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    return {
      formDataRules: {
        name: [{ required: true, message: '用户名不能为空', trigger: ['change', 'blur'] }],
        gender: [{ required: true, message: '请选择性别', trigger: ['change', 'blur'] }],
        height: [
          { required: true, message: '请输入身高', trigger: ['change', 'blur'] },
          { validator: checkHeight, trigger: ['change', 'blur'] }
        ],
        weight: [
          { required: true, message: '请输入体重', trigger: ['change', 'blur'] },
          { validator: checkWeight, trigger: ['change', 'blur'] }
        ],
        targe_weight: [
          { required: true, message: '请输入目标体重', trigger: ['change', 'blur'] },
          { validator: checkWeight, trigger: ['change', 'blur'] }
        ],
        birthday: [{ required: true, message: '请输入出生日期', trigger: ['change', 'blur'] }],
        job: [{ required: true, message: '请选择职业', trigger: ['change', 'blur'] }],
        disease: [{ required: true, message: '请选择个人特征', trigger: ['change', 'blur'] }]
      },
      formData: {
        name: '',
        person_no: '',
        phone: '',
        card_info_id: '',
        gender: '',
        height: '',
        weight: '',
        targe_weight: '',
        birthday: '',
        job: '',
        disease: '',
        taboo_food: '',
        is_follow: ''
      },
      formLoading: false, // 表单里Loading
      userList: [],
      jobList: JOB_LIST,
      selectFoodList: [],
      isLoading: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  computed: {
    computedAge() {
      return d => {
        let age = 18
        if (d) {
          const birthDay = dayjs(d)
          const today = dayjs()
          age = today.year() - birthDay.year()
          if (today.month() < birthDay.month() || (today.month() === birthDay.month() && today.date() < birthDay.date())) {
            age--
          }
        }
        return age
      }
    },
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        if (!val) {
          this.selectFoodList = []
        }
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.formLoading = true
        this.setFormData()
      }
    }
  },
  methods: {
    setFormData() {
      if (this.drawerType === 'edit') {
        console.log('this.fileData', this.fileData)
        this.formData.name = this.fileData.name
        this.formData.person_no = this.fileData.person_no
        this.formData.phone = this.fileData.phone
        this.formData.card_info_id = this.fileData.user_id
        this.formData.gender = this.fileData.gender === '男' ? 'MAN' : 'WOMEN'
        this.formData.height = this.fileData.height
        this.formData.weight = this.fileData.weight
        this.formData.targe_weight = this.fileData.weight_target
        this.formData.birthday = this.fileData.birthday
        this.formData.job = this.fileData.job
        this.formData.is_follow = this.fileData.is_follow
        this.formData.disease = this.fileData.disease_list.map(item => {
          return item.id
        })
        this.formData.taboo_food = this.fileData.ingredient_taboo.map(item => {
          return item.id
        })
        this.selectFoodList = deepClone(this.fileData.ingredient_taboo)
      } else {
        let date = dayjs().subtract(18, 'year').format('YYYY-MM-DD')
        this.formData.birthday = date
      }
      this.$nextTick(() => {
        this.$refs.formDataRef.clearValidate()
      })
      this.formLoading = false
    },
    deleteHandle(index, data) {
      let newArr1 = this.formData.taboo_food.toSpliced(index, 1)
      this.formData.taboo_food = [...new Set(newArr1)]
      let newArr2 = this.selectFoodList.filter(item => item.id !== data.id)
      this.selectFoodList = [...new Set(newArr2)]
    },
    // 删除元素
    deleteItem(oldArr, index) {
      let arr = []
      arr = oldArr.toSpliced(index, 1)
      oldArr = deepClone(arr)
    },
    // 获取人员信息
    async getUserInfo(query, id) {
      if (query) {
        this.isLoading = true
        let params = {
          name: query
        }
        await this.$apis.apiBackgroundHealthyHealthyInfoCardInfoListPost(params).then(res => {
          this.isLoading = false
          if (res.code === 0) {
            this.userList = deepClone(res.data)
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        this.userList = []
      }
    },
    showInfo(e) {
      let data = this.userList.filter(item => item.id === e)
      if (data && data.length) {
        this.formData.person_no = data[0].person_no || ''
        this.formData.phone = data[0].phone || ''
        this.formData.card_info_id = data[0].id || ''
        this.formData.gender = data[0].gender || ''
      }
    },
    showSelectFood(arr) {
      let newArr = []
      if (arr && arr.length) {
        arr.forEach(id => {
          let data = this.foodList.filter(item => item.id === id)
          newArr.push(data[0])
        })
      }
      this.selectFoodList = deepClone(newArr)
    },
    save(type) {
      this.$refs.formDataRef.validate((valid) => {
        if (valid) {
          let params = {
            id: type === 'edit' ? this.fileData.id : undefined,
            gender: this.formData.gender || '',
            height: this.formData.height || '',
            weight: parseFloat(this.formData.weight) || '',
            weight_target: parseFloat(this.formData.targe_weight) || '',
            birthday: this.formData.birthday || '',
            job: this.formData.job || '',
            disease_ids: this.formData.disease || [],
            ingredient_ids: this.formData.taboo_food || [],
            is_follow: this.formData.is_follow || false,
            card_info_id: this.formData.card_info_id || ''
          }
          if (type === 'add') {
            this.addNewHealthyInfo(params)
          } else {
            this.editHealthyInfo(params)
          }
        } else {
          this.$message.error('档案有未填写的地方，请检查后重试')
        }
      })
    },
    cancel() {
      let obj = {
        name: '',
        person_no: '',
        phone: '',
        card_info_id: '',
        gender: '',
        height: '',
        weight: '',
        targe_weight: '',
        birthday: '',
        job: '',
        disease: '',
        taboo_food: '',
        is_follow: ''
      }
      this.formData = { ...obj }
      this.visible = false
    },
    addNewHealthyInfo(params) {
      this.$apis.apiBackgroundHealthyHealthyInfoAddPost(params).then(res => {
        if (res.code === 0) {
          this.$refs.formDataRef.resetFields()
          this.$message.success('新增成功')
          this.visible = false
          this.$emit('refresh')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    editHealthyInfo(params) {
      this.$apis.apiBackgroundHealthyHealthyInfoModifyPost(params).then(res => {
        if (res.code === 0) {
          this.$refs.formDataRef.resetFields()
          this.$message.success('修改成功')
          this.visible = false
          this.$emit('refresh')
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tips {
  position: relative;
  height: 26px;
  padding: 0px 20px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  font-size: 12px;
  .delete {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
