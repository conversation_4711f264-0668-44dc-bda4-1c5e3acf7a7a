<template>
  <!-- 第三方对账异常订单 -->
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        label-width="100px"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            数据列表
            <span style="margin-left: 20px; font-size: 14px;">当前表格仅统计以下来源的交易对账数据：农行-缴费、微信-JSAPI支付</span>
          </div>
          <div class="align-r">
            <el-button size="mini" class="ps-origin-btn" @click="refundHandle('more', {})" v-permission="['background_order.compare_bill_order.bill_corder_refund']">批量退款</el-button>
            <el-button size="mini"  class="ps-origin-btn" @click="replenishmentHandle('more', {})" v-permission="['background_order.compare_bill_order.bill_order_charge']">批量补充值</el-button>
            <button-icon color="plain" @click="gotoExport">导出</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            ref="tableView"
            :data="tableData"
            v-loading="isLoading"
            stripe
            header-row-class-name="ps-table-header-row"
            @selection-change="handleOrderSelectionChange"
            :empty-text="isFirstSearch ? '暂无数据，请查询' : ''">
            <table-column  v-for="item in tableSetting" :key="item.key" :col="item">
              <template #operation="{ row }">
                <el-button v-show="row.out_trade_no && !row.trade_no" type="text" size="small" class="ps-text" @click="refundHandle('one', row)" v-permission="['background_order.compare_bill_order.bill_corder_refund']">退款</el-button>
                <el-button v-show="row.out_trade_no && !row.trade_no && row.only_refund !== 1 " type="text" size="small" class="ps-text" @click="replenishmentHandle('one', row)" v-permission="['background_order.compare_bill_order.bill_order_charge']">补充值</el-button>
                <el-button v-if="!row.out_trade_no && row.trade_no" type="text" size="small" class="ps-text" @click="withdrawHandle(row)">提现</el-button>
              </template>
            </table-column>
        </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
  </div>
</template>

<script>
import { recentSevenDay } from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'ThirdReconciliation',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '', type: 'selection' },
        { label: '第三方订单号', key: 'out_trade_no' },
        { label: '总单号', key: 'trade_no' },
        { label: '对账时间', key: 'create_time' },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        { label: '实际金额', key: 'real_fee', type: 'money' },
        { label: '第三方动账金额', key: 'third_fee', type: 'money' },
        { label: '第三方账单状态', key: 'settle_status_alias' },
        { label: '商户号', key: 'merchant_id' },
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '手机号码', key: 'phone' },
        { label: '操作员', key: 'account_name' },
        { label: '操作', type: 'slot', slotName: 'operation' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      refundOrderIds: [], // 可以退款的id
      selectListId: [], // 多选功能
      replenishmentIds: [], // 可以补充值的id
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '时间',
          value: recentSevenDay
        },
        out_trade_no: {
          type: 'input',
          value: '',
          label: '第三方订单号',
          placeholder: '请输入第三方订单号',
          clearable: true
        },
        merchant_id: {
          type: 'input',
          value: '',
          label: '商户号',
          placeholder: '请输入商户号',
          clearable: true
        }
      },
      printType: 'ThirdReconciliation',
      isFirstSearch: true
    }
  },
  created () {
    this.initLoad(true)
  },
  mounted() {
    // this.initPrintSetting()
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        // this.currentTableSetting = this.tableSetting
        this.getWithdrawList()
      }
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.getWithdrawList()
      this.isFirstSearch = true
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getWithdrawList()
        this.isFirstSearch = false
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getWithdrawList() {
      const params = this.formatQueryParams(this.searchFormSetting)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderCompareBillOrderCompareBillRecordPost({
        ...params,
        settle_status: 'FAILED',
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getWithdrawList()
    },
    // 导出
    gotoExport() {
      const option = {
        url: 'apiBackgroundOrderCompareBillOrderCompareBillRecordExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    refundHandle(type, data) {
      let param = {
        bill_record_ids: type === 'one' ? [data.id] : [...this.selectListId]
      }
      this.$apis.apiBackgroundOrderCompareBillOrderBillCorderRefundPost(param).then(res => {
        if (res.code === 0) {
          this.$message.success(`操作成功, ${res.msg}`)
        } else {
          this.$message.error(res.msg)
        }
        this.getWithdrawList()
      })
    },
    replenishmentHandle(type, data) {
      let param = {
        bill_record_ids: type === 'one' ? [data.id] : [...this.selectListId]
      }
      console.log('param', param)
      this.$apis.apiBackgroundOrderCompareBillOrderBillOrderChargePost(param).then(res => {
        if (res.code === 0) {
          this.$message.success(`操作成功, ${res.msg}`)
        } else {
          this.$message.error(res.msg)
        }
        this.getWithdrawList()
      })
    },
    withdrawHandle(data) {
      this.$apis.apiBackgroundOrderCompareBillOrderBillOrderChargePost({
        bill_record_id: data.id
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('提现成功')
        } else {
          this.$message.error(res.msg)
        }
      })
      this.getWithdrawList()
    },
    handleOrderSelectionChange(val) {
      console.log('val', val)
      this.refundOrderIds = []
      this.selectListId = val.map(item => {
        if (item.can_refund) {
          this.refundOrderIds.push(item.id)
        }
        return item.id
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
