<template>
  <div class="GoodsUnit container-wrapper">
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addOrEditGoodsUnit('add')" v-permission="['background_store.goods_unit.add']">
            新增
          </button-icon>
          <button-icon color="origin" type="add" @click="addOrEditGoodsUnit('batch')" v-permission="['background_store.goods_unit.add']">
            批量新增
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" @click="addOrEditGoodsUnit('modify', row)" v-permission="['background_store.goods_unit.modify']">
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                :disabled="row.goods_nums > 0"
                @click="clickDelete(row)"
                v-permission="['background_store.goods_unit.delete']"
              >
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <goods-unit-dialog
      v-if="goodsUnitDialogVisible"
      :isshow.sync="goodsUnitDialogVisible"
      :type="goodsUnitTypeDialog"
      :title="goodsUnitTitleDialog"
      @confirm="initLoad"
      :dialog-info="dialogInfo"
    />
  </div>
</template>
<script>
import { debounce, to } from '@/utils'
import GoodsUnitDialog from './GoodsUnitDialog.vue'
export default {
  name: 'GoodsUnit',
  components: { GoodsUnitDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      totalPageSize: 0,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '序号', type: 'index', width: '80' },
        { label: '单位名称', key: 'name' },
        { label: '商品数量', key: 'goods_nums' },
        { label: '创建时间', key: 'create_time' },
        {
          label: '操作',
          key: 'operation',
          type: 'slot',
          slotName: 'operation',
          fixed: 'right',
          width: '220'
        }
      ],
      goodsUnitDialogVisible: false, // 新增 批量新增 修改
      goodsUnitTypeDialog: 'add',
      goodsUnitTitleDialog: '',
      dialogInfo: {},
      searchFormSetting: {
        name: {
          type: 'input',
          label: '单位名称',
          value: '',
          placeholder: '请输入单位名称'
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.currentPage = 1
      this.getGoodsUnitList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getGoodsUnitList()
      }
    }, 300),
    async getGoodsUnitList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsUnitListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
      } else {
        this.$message.error(res.msg)
      }
    },
    clickDelete(row) {
      this.$confirm('确定删除该商品单位？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundStoreGoodsUnitDeletePost({
                id: row.id
              })
            )
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                }
              }
              this.getGoodsUnitList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 新增和批量新增
    addOrEditGoodsUnit(type, row) {
      let title = ''
      switch (type) {
        case 'add':
          title = '新增'
          break
        case 'batch':
          title = '批量新增'
          break
        case 'modify':
          title = '修改'
          this.dialogInfo = row
          break
        default:
          break
      }
      this.goodsUnitTitleDialog = title
      this.goodsUnitTypeDialog = type
      this.goodsUnitDialogVisible = true
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getGoodsUnitList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getGoodsUnitList()
    }
  }
}
</script>
<style lang="scss" scoped></style>
