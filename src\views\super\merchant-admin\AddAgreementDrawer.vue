<template>
  <div class="drawer-box">
    <customDrawer :show.sync="visible" :loading="isLoading" :title="drawerTitle" :size="800" @confirm="saveSetting">
      <div class="drawer-container">
        <div class="drawer-content">
          <div class="form-wrapper" v-loading="isLoading">
            <el-form :model="formData" label-width="80px" :rules="noticeInfoRules" ref="noticeInfoForm">
              <el-form-item label="协议类型" prop="agreement_type">
                <el-select
                  ref="companyRef"
                  v-model="formData.agreement_type"
                  clearable
                  filterable
                  class="ps-input"
                  style="width: 400px"
                  @change="changeAgreementType"
                >
                  <el-option
                    v-for="item in agreementTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="协议名称" prop="agreement_name">
                <el-input
                  v-model="formData.agreement_name"
                  placeholder="协议类型+年月日"
                  class="ps-input"
                  style="width: 400px"
                  maxlength="40"
                ></el-input>
              </el-form-item>
              <el-form-item label="协议内容" prop="content">
                <TinymceUeditor
                  v-model="formData.content"
                  listener="focus"
                  :custom-handle="blurSelsectHandle"
                ></TinymceUeditor>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
import { to, escapeHTML, unescapeHTML, parseTime } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import TinymceUeditor from '@/components/Tinymce/Tinymce.vue'
export default {
  props: {
    isshow: Boolean,
    drawerModifyData: {
      type: Object,
      default() {
        return {}
      }
    },
    drawerTitle: {
      type: String,
      default() {
        return ''
      }
    },
    type: {
      type: String,
      default() {
        return ''
      }
    }
  },
  components: {
    TinymceUeditor
  },
  data() {
    return {
      formData: {
        agreement_type: '',
        agreement_name: '',
        content: ''
      },
      agreementTypeList: [],
      noticeInfoRules: {
        agreement_type: [{ required: true, message: '请选择协议类型', trigger: 'blur' }],
        agreement_name: [{ required: true, message: '请输入协议名称', trigger: ['blur', 'change'] }],
        content: [{ required: true, message: '请输入协议内容', trigger: 'blur' }]
      },
      isLoading: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    this.getGreementType()
  },
  methods: {
    saveSetting() {
      this.submitForm()
    },
    async getGreementType() {
      const [err, res] = await to(this.$apis.apiBackgroundBaseMenuGetGreementTypePost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let arr = []
        for (const key in res.data) {
          arr.push({
            label: res.data[key],
            value: key
          })
        }
        this.agreementTypeList = arr
        if (this.type === 'modify') {
          this.getAgreementDetails()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增
    async addAgreemeentHandle(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminAgreementAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success(res.msg)
        this.$emit('clickSaveDrawer')
        // this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑
    async modifyAgreemeentHandle(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminAgreementModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success(res.msg)
        this.$emit('clickSaveDrawer')
        // this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑获取详情
    async getAgreementDetails() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminAgreementAgreementDetailPost({
          id: this.drawerModifyData.id
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.formData.agreement_name = res.data.agreement_name
        this.formData.agreement_type = res.data.agreement_type
        this.formData.content = unescapeHTML(res.data.content)
      } else {
        this.$message.error(res.msg)
      }
    },
    blurSelsectHandle(e) {
      this.$refs.companyRef.blur()
    },
    submitForm() {
      this.$refs.noticeInfoForm.validate(valid => {
        if (valid) {
          let params = {
            agreement_type: this.formData.agreement_type,
            agreement_name: this.formData.agreement_name,
            content: escapeHTML(this.formData.content) // 为了安全转下码
          }
          if (this.type === 'modify') {
            params.id = this.drawerModifyData.id
            this.modifyAgreemeentHandle(params)
          } else {
            this.addAgreemeentHandle(params)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 协议类型修改
    changeAgreementType(e) {
      let current = this.agreementTypeList.find(v => v.value === e)
      this.formData.agreement_name = current.label + parseTime(new Date(), '{y}-{m}-{d}')
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    padding: 20px;
  }
}
</style>
