// 分类出入库统计
export const STATISTICS_CLASSIFICATION_TABLE_SETTING_PENDING = [
  { label: '仓库名称', key: 'warehouse_name' },
  { label: '日期', key: 'date' },
  { label: '物资分类', key: 'materail_classification_name' },
  { label: '物资名称', key: 'materials_name' },
  { label: '最小单位', key: 'unit_name' },
  {
    label: "入库",
    key: "entry",
    children:
      [
        { label: '入库数量', key: 'entry_count' },
        { label: '入库合计金额', key: 'entry_total', type: 'money' }
      ]
  },
  {
    label: "出库",
    key: "exity",
    children:
      [
        { label: '出库数量', key: 'exit_count' },
        { label: '出库合计金额', key: 'exit_total', type: 'money' }
      ]
  },
  { label: '入库合计', key: 'entry_all_total', type: 'money' },
  { label: '出库合计', key: 'exit_all_total', type: 'money' }
]
