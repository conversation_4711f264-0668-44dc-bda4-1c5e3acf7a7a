<template>
  <div class="ChargeService container-wrapper">
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="80px"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="gotoAddActivityRecharge" v-permission="['background_marketing.commission_charge.charge_add']">新增</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :empty-text="isFirstSearch ? '暂无数据，请查询' : ''"
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="rule_id" label="规则编号" align="center"></el-table-column>
          <el-table-column prop="name" label="规则名称" align="center"></el-table-column>
          <el-table-column prop="" label="适用分组" align="center">
            <template slot-scope="scope">
              {{
                scope.row.rule_info && scope.row.rule_info.group_names
                  ? Object.keys(scope.row.rule_info.group_names).join(',')
                  : ''
              }}
            </template>
          </el-table-column>
          <el-table-column prop="end_time" label="充值渠道" align="center">
            <template slot-scope="scope">
              {{
                scope.row.paywayAliasName && scope.row.paywayAliasName.length
                  ? scope.row.paywayAliasName.join(',')
                  : ''
              }}
            </template>
          </el-table-column>
          <el-table-column prop="status_desc" label="状态" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-color="#ff9b45"
                active-value="enable"
                inactive-value="stop"
                @change="changeStatusModify(scope.row)"
                :disabled="!allPermissions.includes('background_marketing.commission_charge.charge_status_modify')"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-origin"
                @click="gotoModifyRandomReduce(scope.row)"
                v-permission="['background_marketing.commission_charge.charge_modify']"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                class="ps-green"
                size="small"
                @click="historyDialogClick(scope.row)"
              >
                历史记录
              </el-button>
              <el-button
                type="text"
                class="ps-warn-text"
                size="small"
                v-if="scope.row.status === 'stop'"
                @click="getChargeChargeDelete(scope.row)"
                v-permission="['background_marketing.commission_charge.charge_delete']"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <history-dialog
      v-if="historyDialogShow"
      :isshow.sync="historyDialogShow"
      :paramsInfo="paramsInfo"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone } from '@/utils'
import HistoryDialog from './HistoryDialog.vue'
import { RECHARGE_SERVICE } from '../../constants'
import { mapGetters } from 'vuex'
export default {
  components: { HistoryDialog },
  name: 'ChargeService',
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      totalPageSize: 0,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: deepClone(RECHARGE_SERVICE),
      historyDialogShow: false,
      paramsInfo: {},
      isFirstSearch: false
    }
  },
  created() {
    this.initLoad()
  },
  computed: {
    ...mapGetters(['allPermissions'])
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getChargeChargeList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.isFirstSearch = false
        this.getChargeChargeList()
      }
    }, 300),
    // 充值手续费
    async getChargeChargeList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeChargeListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          v.paywayAliasName = v.rule.map(ruleItem => {
            return ruleItem.payway_alias
          })
          return v
        })
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 扣款状态
    async gethargeStatusModifyModify(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeChargeStatusModifyPost({
          rule_no: params.rule_no,
          status: params.status
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.getChargeChargeList()
      } else {
        this.$message.error(res.msg)
      }
    },
    changeStatusModify(data) {
      this.gethargeStatusModifyModify(data)
    },
    historyDialogClick(data) {
      this.paramsInfo = {
        rule_id: data.rule_id,
        organization: data.org_no
      }
      this.historyDialogShow = true
    },
    async getChargeChargeDelete(row) {
      let params = {
        rule_no: row.rule_no
      }
      this.$confirm(`确定删除吗?`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundMarketingCommissionChargeChargeDeletePost(params)
            )
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                } else if (this.currentPage === this.totalPageSize) {
                  this.currentPage--
                }
              }
              this.getChargeChargeList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getChargeChargeList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getChargeChargeList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 添加充值活动
    gotoAddActivityRecharge() {
      this.$router.push({
        name: 'MerchantAddChargeServiceRule',
        params: { type: 'add' }
      })
    },
    gotoModifyRandomReduce(data) {
      this.$router.push({
        name: 'MerchantAddChargeServiceRule',
        params: {
          type: 'modify'
        },
        query: {
          data: this.$encodeQuery(data)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
