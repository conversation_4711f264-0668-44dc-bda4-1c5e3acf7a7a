import http from '@/utils/request'
// import api from '..'

export default {
  /**
   *
   * @typedef {Object} AddAddersAreaReq
   * @property {integer} organization - 指定组织
   * @property {string} name - 配送区域
   * @property {array} use_points -
   */

  /**
   *
   * @typedef {Object} AddersArea
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} organization_name - 组织名
   * @property {string} status - 状态
   * @property {string} name - 区域名
   * @property {integer} organization - Organization
   * @property {array} adders_centers -
   */

  /**
   *
   * @typedef {Object} Ids
   * @property {array} ids -
   */

  /**
   *
   * @typedef {Object} ListAddersAreaReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} AreaAddersCenter
   * @property {string} l0 - 一级
   * @property {string} l1 - 二级
   * @property {string} l2 - 三级
   * @property {string} l3 - 四级
   * @property {string} l4 - 五级
   */

  /**
   *
   * @typedef {Object} AreaResp
   * @property {integer} id - id
   * @property {string} organization_id - Organization id
   * @property {string} organization_name - 组织名
   * @property {string} name - 区域名
   * @property {array} adders_centers -
   */

  /**
   *
   * @typedef {Object} ModifyAddersAreaReq
   * @property {integer} id - ID
   * @property {string} name - 配送区域
   * @property {array} use_points -
   */

  /**
   *
   * @typedef {Object} AddAddersCenterReq
   * @property {integer} company - 指定公司ID
   * @property {string} data - 订餐点数据
   * @property {string} remark - 备注
   */

  /**
   *
   * @typedef {Object} AddersCenter
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} status - 状态
   * @property {string} name - 配送点名称
   * @property {string} remark - 地址说明
   * @property {string} parent_names - 父级组织名,按,号分割
   * @property {integer} lft - Lft
   * @property {integer} rght - Rght
   * @property {integer} tree_id - Tree id
   * @property {integer} level - Level
   * @property {integer} parent - 上级ID
   * @property {integer} company - 所属项目点公司
   */

  /**
   *
   * @typedef {Object} AddChildrenAddersCenterReq
   * @property {integer} company - 指定公司ID
   * @property {integer} parent - 上级配送点ID
   * @property {string} data - 订餐点数据
   * @property {string} remark - 备注
   */

  /**
   *
   * @typedef {Object} BatchImportAddersCenterReqSer
   * @property {string} url - 上传的xls地址
   */

  /**
   *
   * @typedef {Object} CeleryTaskResult
   * @property {object} query_id -
   */

  /**
   *
   * @typedef {Object} ListAddersCenterReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListAddersCenterRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyAddersCenterReq
   * @property {string} name - 配送点名字
   * @property {string} remark - 备注
   */

  /**
   *
   * @typedef {Object} Account
   * @property {integer} id - id
   * @property {integer} role - Role
   * @property {string} role_alias - 角色名
   * @property {array} organization_alias -
   * @property {string} username - 账号
   * @property {string} member_name - 用户名称
   * @property {integer} status - 用户状态
   * @property {string} status_alias - 状态
   * @property {string} password - 密码
   * @property {string} mobile - 手机号
   * @property {string} create_time - 创建时间
   */

  /**
   *
   * @typedef {Object} Id
   * @property {integer} id - ID
   */

  /**
   *
   * @typedef {Object} DeviceInfo
   * @property {integer} device_no - Device no
   * @property {string} canteen - Canteen
   * @property {string} stall - Stall
   * @property {array} group_name -
   * @property {string} device_type - 设备类型
   * @property {string} device_type_alias - Device type alias
   * @property {string} device_model - 设备型号
   * @property {string} device_model_alias - Device model alias
   * @property {string} device_name - 设备名
   * @property {string} serial_no - SN码(设备序列号)
   * @property {string} device_mac - 设备MAC地址
   * @property {boolean} activation_status - Activation status
   * @property {boolean} online - Online
   * @property {string} activate_time - Activate time
   * @property {string} can_refund - Can refund
   * @property {integer} refund_time - 退款时间配置(单位小时)
   * @property {string} device_number - 设备号
   * @property {string} device_settings_pwd - 开启设置密码
   * @property {string} device_refund_pwd - 退款密码
   */

  /**
   *
   * @typedef {Object} ListAccountReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} username - 账号
   * @property {string} member_name - 用户名称
   * @property {integer} status - 状态
   */

  /**
   *
   * @typedef {Object} ListAccountRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} Organization
   * @property {integer} id - id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {integer} parent - 上级ID
   * @property {integer} company - 所属项目点公司
   * @property {string} name - 组织名称
   * @property {integer} tree_id - Tree id
   * @property {integer} level - Level
   * @property {string} status_alias - Status alias
   * @property {string} parent_alias - 上级组织名
   * @property {string} has_children - Has children
   * @property {integer} account_num - 关联账号数
   * @property {integer} role_num - 关联角色数
   * @property {array} children_list -
   * @property {string} level_name - Level name
   * @property {integer} level_tag - 层级标签
   */

  /**
   *
   * @typedef {Object} ListOrganizationReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListOrganizationRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} Empty
   */

  /**
   *
   * @typedef {Object} AdminAddDeviceReqSer
   * @property {string} activation_code - 激活码
   * @property {string} device_name - 设备名
   * @property {integer} organization_id - 组织ID
   * @property {string} device_type - 设备类型
   * @property {string} device_model - 设备类型
   * @property {string} serial_no - SN码-出厂标识
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   */

  /**
   *
   * @typedef {Object} AdminDeviceInfo
   * @property {integer} device_no - Device no
   * @property {boolean} activation_status - Activation status
   * @property {string} device_name - 设备名
   * @property {string} device_mac - 设备MAC地址
   * @property {string} consumer_name - Consumer name
   * @property {string} canteen - Canteen
   * @property {string} device_type - 设备类型
   * @property {string} device_type_alias - Device type alias
   * @property {string} activation_status_alias - Activation status alias
   * @property {string} device_model - 设备型号
   * @property {string} device_model_alias - Device model alias
   * @property {string} serial_no - SN码(设备序列号)
   * @property {string} create_user - 创建人
   * @property {string} create_time - 创建时间
   * @property {boolean} using - Using
   * @property {string} activate_time - Activate time
   * @property {string} activation_code - 激活码
   * @property {string} device_number - 设备号
   */

  /**
   *
   * @typedef {Object} ModifyNameDeviceReqSer
   * @property {integer} device_no - 设备ID
   * @property {string} device_name - 设备名
   */

  /**
   *
   * @typedef {Object} BatchModifyDeviceReqSer
   * @property {integer} choices - 找回密码
   * @property {array} data -
   * @property {array} device_nos -
   */

  /**
   *
   * @typedef {Object} DeviceSettingReqSer
   * @property {integer} device_no - 设备ID
   * @property {integer} can_refund - Can refund
   * @property {string} device_settings_pwd - 设置密码
   * @property {string} device_refund_pwd - 退款密码
   * @property {integer} refund_time - 退款时间配置
   */

  /**
   *
   * @typedef {Object} DeviceModelReqSer
   * @property {string} device_type - 设备类型
   */

  /**
   *
   * @typedef {Object} AdminListDeviceReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {boolean} activation_status - 是否激活
   * @property {string} device_mac - MAC地址-设备唯一标识
   * @property {string} activation_code - 激活码
   * @property {boolean} using - 是否启用
   * @property {string} device_type - 设备类型
   * @property {string} device_name - 设备名
   * @property {integer} organization_id - 组织ID
   */

  /**
   *
   * @typedef {Object} DeviceTimeRangeSer
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   */

  /**
   *
   * @typedef {Object} AdminModifyDeviceReqSer
   * @property {integer} device_no - 设备ID
   * @property {string} device_name - 编辑设备名
   * @property {string} device_type - 编辑设备类型
   * @property {array} user_group_ids -
   * @property {boolean} using - 启用与禁用设备
   * @property {boolean} deactivate - 清除有效期 使设备失效
   * @property {} time_range -
   */

  /**
   *
   * @typedef {Object} ListLogReqSer
   * @property {integer} page_no - 当前页数1
   * @property {integer} page_size - 每页显示数量
   * @property {string} operator - 模糊搜索操作者
   * @property {string} module - 操作模块
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   */

  /**
   *
   * @typedef {Object} ListLogItemSer
   * @property {string} module_name - 操作模块
   * @property {string} module_operation - 操作人
   * @property {string} operator - 模糊搜索操作者
   * @property {string} detail - 操作内容
   * @property {string} create_time - 操作时间
   */

  /**
   *
   * @typedef {Object} ListLogRspSer
   * @property {integer} current_page_no - 当前页码
   * @property {integer} page_count - 页面数量
   * @property {integer} page_size - 每页显示数量
   * @property {array} log_list -
   */

  /**
   *
   * @typedef {Object} MsgAdminSerializerReq
   * @property {integer} msg_no - 公告ID
   * @property {array} company_ids -
   * @property {string} title - 标题
   * @property {string} msg_type - 公告类型
   * @property {string} content - 公告内容
   * @property {string} file_url - 附件链接
   * @property {string} post_time - 发送时间
   */

  /**
   *
   * @typedef {Object} MsgNo
   * @property {integer} msg_no - 公告ID
   * @property {integer} options - 操作选项
   */

  /**
   *
   * @typedef {Object} MsgNoBase
   * @property {integer} msg_no - 公告ID
   */

  /**
   *
   * @typedef {Object} MsgReceiveAdmin
   * @property {string} title - 主题
   * @property {string} msg_type - 公告类型
   * @property {string} post_time - 发布时间
   * @property {string} content - 通知文本
   * @property {string} file_url - 通知文本
   * @property {array} company_ids -
   */

  /**
   *
   * @typedef {Object} ListMessagesReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_time - 开始时间
   * @property {string} end_time - 结束时间
   * @property {string} sender - 创建人
   * @property {string} query - 类型或标题
   */

  /**
   *
   * @typedef {Object} AdminAddOrganizationInfoReqSer
   * @property {integer} id - Id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} name - 组织名
   * @property {integer} parent - 父ID
   * @property {string} status - 状态
   * @property {string} level_tag - 层级标签
   * @property {string} level_name - 层级组织名
   * @property {boolean} face_on - 人脸支付开关
   * @property {boolean} refund_on - 支持退款开关
   * @property {string} refund_password - 退款密码
   * @property {boolean} store_wallet_on - 朴食储值卡务钱包开关
   * @property {boolean} electronic_wallet_on - 电子账户钱包开关
   * @property {boolean} subsidy_wallet_on - 补贴钱包开关
   * @property {boolean} discount_wallet_on - 优惠钱包开关
   * @property {boolean} other_wallet_on - 第三方钱包开关
   * @property {integer} company - 指定公司ID
   * @property {string} url - 公司官网
   * @property {string} district - 所在地区
   * @property {string} contact - 联系人
   * @property {string} mobile - 手机号码
   * @property {string} mail_address - 邮箱
   * @property {string} tel - 公司联系电话
   * @property {integer} industry - 行业性质
   * @property {string} remark - 备注
   * @property {object} permission - 初始商户权限权限JSON列表
   * @property {string} appid - 微信公众号appid
   * @property {string} secret_key - 微信公众号secret_key
   * @property {string} auth_time - 认证时间
   * @property {boolean} is_third_interface - 是否开启第三方对接
   * @property {string} third_app_key - 应用
   * @property {string} third_secret_key - 第三方secret_key
   * @property {string} third_app_name - 第三方应用名称, 这个会放在点餐h5的应用名
   * @property {string} third_app_url - 第三方应用跳转的url
   * @property {string} third_app_callback_url - 第三方回调url
   * @property {string} sms_template_id - 短信模板ID
   */

  /**
   *
   * @typedef {Object} OrganizationInfoSettingsSer
   * @property {integer} id - ID
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} name - 组织名
   * @property {integer} parent - 父ID
   * @property {string} status - 状态
   * @property {string} level_tag - 层级标签
   * @property {string} level_name - 层级组织名
   * @property {array} permission -
   * @property {boolean} face_on - 人脸支付开关
   * @property {boolean} refund_on - 支持退款开关
   * @property {string} refund_password - 退款密码
   * @property {boolean} store_wallet_on - 朴食储值卡务钱包开关
   * @property {boolean} electronic_wallet_on - 电子账户钱包开关
   * @property {boolean} subsidy_wallet_on - 补贴钱包开关
   * @property {boolean} discount_wallet_on - 优惠钱包开关
   * @property {boolean} other_wallet_on - 第三方钱包开关
   * @property {string} url - 公司官网
   * @property {string} district - 所在地区
   * @property {string} contact - 联系人
   * @property {string} mobile - 手机号码
   * @property {string} mail_address - 邮箱
   * @property {string} tel - 公司联系电话
   * @property {integer} industry - 行业性质
   * @property {string} remark - 备注
   * @property {string} appid - 微信公众号appid
   * @property {string} secret_key - 微信公众号secret_key
   * @property {string} auth_time - 认证时间
   * @property {boolean} is_third_interface - 是否开启第三方对接
   * @property {string} third_app_key - 应用
   * @property {string} third_secret_key - 第三方secret_key
   * @property {string} third_app_name - 第三方应用名称, 这个会放在点餐h5的应用名
   * @property {string} third_app_url - 第三方应用跳转的url
   * @property {string} third_app_callback_url - 第三方回调url
   * @property {string} sms_template_id - 短信模板ID
   */

  /**
   *
   * @typedef {Object} AddRootOrganizationInfoReqSer
   * @property {integer} id - Id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} name - 组织名
   * @property {string} status - 状态
   * @property {string} level_tag - 层级标签
   * @property {string} level_name - 层级组织名
   * @property {array} permission -
   * @property {boolean} face_on - 人脸支付开关
   * @property {boolean} refund_on - 支持退款开关
   * @property {string} refund_password - 退款密码
   * @property {boolean} store_wallet_on - 朴食储值卡务钱包开关
   * @property {boolean} electronic_wallet_on - 电子账户钱包开关
   * @property {boolean} subsidy_wallet_on - 补贴钱包开关
   * @property {boolean} discount_wallet_on - 优惠钱包开关
   * @property {boolean} other_wallet_on - 第三方钱包开关
   * @property {integer} init_organization_level - 添加后初始化的层级数
   * @property {string} username - 新增组织经理账号
   * @property {string} password - 新增组织经理账号密码
   * @property {string} url - 公司官网
   * @property {string} district - 所在地区
   * @property {string} contact - 联系人
   * @property {string} mobile - 手机号码
   * @property {string} mail_address - 邮箱
   * @property {string} tel - 公司联系电话
   * @property {integer} industry - 行业性质
   * @property {string} remark - 备注
   * @property {string} appid - 微信公众号appid
   * @property {string} secret_key - 微信公众号secret_key
   * @property {string} auth_time - 认证时间
   * @property {boolean} is_third_interface - 是否开启第三方对接
   * @property {string} third_app_key - 应用
   * @property {string} third_secret_key - 第三方secret_key
   * @property {string} third_app_name - 第三方应用名称, 这个会放在点餐h5的应用名
   * @property {string} third_app_url - 第三方应用跳转的url
   * @property {string} third_app_callback_url - 第三方回调url
   * @property {string} sms_template_id - 短信模板ID
   */

  /**
   *
   * @typedef {Object} EnableOrganizationReq
   * @property {integer} id - ID
   * @property {boolean} enable - 是否开启
   */

  /**
   *
   * @typedef {Object} GetLevelNameMapReqSer
   * @property {integer} id - 组织ID
   */

  /**
   *
   * @typedef {Object} LevelNameSer
   * @property {integer} level - 层级
   * @property {string} name - 层级标签名
   */

  /**
   *
   * @typedef {Object} PermissionSer
   * @property {string} index - ID
   * @property {string} key - 权限Key
   * @property {string} verbose_name - 权限名
   * @property {boolean} is_menu - 是否菜单
   * @property {string} parent - 父ID
   * @property {integer} level - 深度
   * @property {integer} perm_type - 权限类型
   * @property {array} children -
   */

  /**
   *
   * @typedef {Object} MealTimeSettings
   * @property {integer} _version - 内置版本号
   * @property {boolean} enable_breakfast - 是否开启早餐
   * @property {string} breakfast_start - 早餐开始时间
   * @property {string} breakfast_end - 早餐结束时间
   * @property {boolean} enable_lunch - 是否开启午餐
   * @property {string} lunch_start - 午餐开始时间
   * @property {string} lunch_end - 午餐结束时间
   * @property {boolean} enable_afternoon - 是否开启下午茶
   * @property {string} afternoon_start - 下午茶开始时间
   * @property {string} afternoon_end - 下午茶结束时间
   * @property {boolean} enable_dinner - 是否开启晚餐
   * @property {string} dinner_start - 晚餐开始时间
   * @property {string} dinner_end - 晚餐结束时间
   * @property {boolean} enable_night - 是否开启夜宵
   * @property {string} midnight_start - 夜宵开始时间
   * @property {string} midnight_end - 夜宵结束时间
   * @property {boolean} enable_morning - 是否开启凌晨餐
   * @property {string} morning_start - 夜宵开始时间
   * @property {string} morning_end - 夜宵结束时间
   */

  /**
   *
   * @typedef {Object} OrganizationSettings
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {array} recharge_amount_list -
   * @property {} meal_time_settings -
   * @property {boolean} inherit_parent - 是否使用父级组织的设置
   * @property {boolean} allow_register - 允许自助注册账号
   * @property {boolean} facepay - 人脸支付
   * @property {boolean} baidu_is_active - 是否已存在百度人脸分组
   * @property {string} precharge_refund_type - 充值退款类型
   * @property {integer} precharge_refund_custom_day - 充值后自定义时间内退款
   * @property {boolean} consume_appeal_online_on - 线上申诉开关
   * @property {boolean} consume_appeal_offine_on - 线下申诉开关
   * @property {string} consume_appeal_type - 消费申诉类型
   * @property {integer} consume_appeal_custom_day - 自定义时间内申诉
   * @property {boolean} refund_on - 支持退款
   * @property {string} refund_password - 退款密码
   * @property {string} refund_type_online - 线上预约订单退款类型
   * @property {integer} refund_custom_time - 下单后可退款小时内
   * @property {integer} refund_meal_time - 充值后自定义时间内退款
   * @property {boolean} store_wallet_on - 朴食储值卡务钱包开关
   * @property {boolean} electronic_wallet_on - 电子账户钱包开关
   * @property {boolean} subsidy_wallet_on - 补贴钱包开关
   * @property {boolean} discount_wallet_on - 优惠钱包开关
   * @property {boolean} other_wallet_on - 第三方钱包开关
   * @property {boolean} allow_custom_amount - 可充值任意金额
   * @property {integer} minimum_recharge_amount - 最低充值金额
   * @property {string} allow_recharge_date - 指定可充值日期
   * @property {integer} limit_today_recharge_amount - 单日累积充值上限制
   * @property {integer} limit_today_consume_amount - 单日累积消费上限
   * @property {integer} limit_balance_amount - 钱包累计余额上限
   * @property {integer} is_duplicate_pay_limit - 储值支付-是否限制重复支付
   * @property {integer} duplicate_pay_second_limit - 储值支付-X秒内不能重复支付
   * @property {integer} eaccount_is_duplicate_pay_limit - 电子账户-是否限制重复支付
   * @property {integer} eaccount_duplicate_pay_second_limit - 电子账户-X秒内不能重复支付
   * @property {integer} limit_hour - 线上预约截止时间 截止小时,就是今天过了这个点后，不能再点菜, (0-23), 25代表不限制
   * @property {boolean} is_open_debt_money - 是否允许储值钱包透支
   * @property {integer} debt_money - 透支金额
   * @property {boolean} is_open_flat - 是否系统自动扣除工本费（押金）
   * @property {integer} flat_cost - 工本费
   * @property {integer} patch_cost - 补卡费
   * @property {boolean} is_flat_return - 工本费（开卡）退还
   * @property {integer} waimai_booking_ahead - 外卖预约时间需要提前几个小时(默认)
   * @property {integer} waimai_booking_breakfast_ahead - 外卖预约时间需要提前几个小时(早餐)
   * @property {integer} waimai_booking_lunch_ahead - 外卖预约时间需要提前几个小时(午餐)
   * @property {integer} waimai_booking_dinner_ahead - 外卖预约时间需要提前几个小时(晚餐)
   * @property {integer} waimai_booking_midnight_ahead - 外卖预约时间需要提前几个小时(夜宵)
   * @property {integer} waimai_waimai_fee - 外卖外卖费(分)
   * @property {integer} waimai_limit_hour - 截止小时,就是今天过了这个点后，不能再点菜, (0-23), 25代表不限制
   * @property {string} waimai_limit_type - 外卖的限制类型
   * @property {integer} use_group_address - 是否使用团餐地址
   * @property {integer} reservation_booking_ahead - 预约预约时间需要提前几个小时(默认)
   * @property {integer} reservation_booking_breakfast_ahead - 预约预约时间需要提前几个小时(早餐)
   * @property {integer} reservation_booking_lunch_ahead - 预约预约时间需要提前几个小时(午餐)
   * @property {integer} reservation_booking_dinner_ahead - 预约预约时间需要提前几个小时(晚餐)
   * @property {integer} reservation_booking_midnight_ahead - 预约预约时间需要提前几个小时(夜宵)
   * @property {integer} reservation_waimai_fee - 预约外卖费(分)
   * @property {integer} reservation_limit_hour - 截止小时,就是今天过了这个点后，不能再点菜, (0-23), 25代表不限制
   * @property {string} reservation_limit_type - 预约的限制类型
   * @property {integer} cupboard_booking_ahead - 取餐柜预约时间需要提前几个小时(默认)
   * @property {integer} cupboard_booking_breakfast_ahead - 取餐柜预约时间需要提前几个小时(早餐)
   * @property {integer} cupboard_booking_lunch_ahead - 取餐柜预约时间需要提前几个小时(午餐)
   * @property {integer} cupboard_booking_dinner_ahead - 取餐柜预约时间需要提前几个小时(晚餐)
   * @property {integer} cupboard_booking_midnight_ahead - 取餐柜预约时间需要提前几个小时(夜宵)
   * @property {integer} cupboard_waimai_fee - 取餐柜外卖费(分)
   * @property {integer} cupboard_limit_hour - 截止小时,就是今天过了这个点后，不能再点菜, (0-23), 25代表不限制
   * @property {string} cupboard_limit_type - 取餐柜的限制类型
   * @property {integer} booking_can_refund - 线上预约订单是否支持退款
   * @property {integer} cupboard_can_refund - 线上点餐柜订单是否支持退款
   * @property {integer} waimai_can_refund - 线上外卖订单是否支持退款
   * @property {array} multi_payinfo -
   */

  /**
   *
   * @typedef {Object} AdminModifyOrganizationInfoReqSer
   * @property {integer} id - ID
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} name - 组织名
   * @property {string} status - 状态
   * @property {string} level_tag - 层级标签
   * @property {string} level_name - 层级组织名
   * @property {array} permission -
   * @property {boolean} face_on - 人脸支付开关
   * @property {boolean} refund_on - 支持退款开关
   * @property {string} refund_password - 退款密码
   * @property {boolean} store_wallet_on - 朴食储值卡务钱包开关
   * @property {boolean} electronic_wallet_on - 电子账户钱包开关
   * @property {boolean} subsidy_wallet_on - 补贴钱包开关
   * @property {boolean} discount_wallet_on - 优惠钱包开关
   * @property {boolean} other_wallet_on - 第三方钱包开关
   * @property {integer} init_organization_level - 添加后初始化的层级数
   * @property {string} url - 公司官网
   * @property {string} district - 所在地区
   * @property {string} contact - 联系人
   * @property {string} mobile - 手机号码
   * @property {string} mail_address - 邮箱
   * @property {string} tel - 公司联系电话
   * @property {integer} industry - 行业性质
   * @property {string} remark - 备注
   * @property {string} appid - 微信公众号appid
   * @property {string} secret_key - 微信公众号secret_key
   * @property {string} auth_time - 认证时间
   * @property {boolean} is_third_interface - 是否开启第三方对接
   * @property {string} third_app_key - 应用
   * @property {string} third_secret_key - 第三方secret_key
   * @property {string} third_app_name - 第三方应用名称, 这个会放在点餐h5的应用名
   * @property {string} third_app_url - 第三方应用跳转的url
   * @property {string} third_app_callback_url - 第三方回调url
   * @property {string} sms_template_id - 短信模板ID
   */

  /**
   *
   * @typedef {Object} ModifyOrganizationLevelName
   * @property {integer} id - ID
   * @property {string} name - 层级标签名
   */

  /**
   *
   * @typedef {Object} TreeListReqOrganization
   * @property {integer} company_id - 公司ID
   */

  /**
   *
   * @typedef {Object} PayInfoReqSer
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {object} extra - 支付配置信息
   * @property {array} organizations -
   * @property {boolean} binded - 是否被绑定
   * @property {integer} weight - 权重(数值越高、权值越高)
   * @property {integer} organization - 组织id
   * @property {string} status - 状态
   * @property {string} merchant_id - 商户号
   * @property {string} merchant_name - 商户名称
   * @property {string} remark - 备注
   * @property {integer} enable - 开启状态
   * @property {string} pay_scene - 支付场景
   * @property {string} payway - 一级支付方式
   * @property {string} sub_payway - 二级支付方式
   * @property {integer} company - Company
   */

  /**
   *
   * @typedef {Object} SimpleOrganization
   * @property {integer} id - id
   * @property {string} name - 组织名称
   */

  /**
   *
   * @typedef {Object} PayInfo
   * @property {integer} id - id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} company - Company
   * @property {string} merchant_id - 商户号
   * @property {string} merchant_name - 商户名称
   * @property {string} remark - 备注
   * @property {integer} enable - 开启状态
   * @property {string} pay_scene - 支付场景
   * @property {string} payway - 一级支付方式
   * @property {string} sub_payway - 二级支付方式
   * @property {object} extra - 支付配置信息
   * @property {string} status_alias - Status alias
   * @property {string} enable_alias - Enable alias
   * @property {string} payway_alias - Payway alias
   * @property {string} sub_payway_alias - Sub payway alias
   * @property {string} pay_scene_alias - Pay scene alias
   * @property {array} organizations -
   * @property {boolean} binded - 是否被绑定
   * @property {integer} weight - 权重(数值越高、权值越高)
   * @property {string} callback_url - Callback url
   */

  /**
   *
   * @typedef {Object} OrgIdsReqSer
   * @property {array} ids -
   * @property {integer} organization - 组织id
   */

  /**
   *
   * @typedef {Object} BindPayInfoReqSer
   * @property {array} organizations -
   * @property {string} pay_scene - 支付场景
   * @property {array} pay_scenes -
   * @property {array} payinfo -
   */

  /**
   *
   * @typedef {Object} ListPayInfoReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {array} organizations -
   * @property {string} pay_scene - 支付场景
   * @property {array} pay_scenes -
   */

  /**
   *
   * @typedef {Object} ListPayInfoRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} MultiPayInfoSer
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status - 状态
   * @property {integer} weight - 权重，数值越低，就证明地位越高
   * @property {integer} payinfo - Payinfo
   * @property {integer} organization - Organization
   * @property {integer} organization_settings - Organization settings
   * @property {integer} company - 所属项目点公司
   */

  /**
   *
   * @typedef {Object} MultiPayInfoReqSer
   * @property {array} organizations -
   * @property {string} pay_scene - 支付场景
   * @property {array} pay_scenes -
   * @property {array} payinfos -
   */

  /**
   *
   * @typedef {Object} ListMultiPayInfoRespSer
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} PaySceneReqSer
   * @property {array} organizations -
   * @property {string} pay_scene - 支付场景
   * @property {array} pay_scenes -
   */

  /**
   *
   * @typedef {Object} PayInfoPayway
   * @property {string} key - 类型key
   * @property {string} name - 类型name
   * @property {string} sub_payway - 二级支付方式
   */

  /**
   *
   * @typedef {Object} PayInfoScene
   * @property {string} key - 场景key
   * @property {string} name - 场景name
   * @property {array} children -
   */

  /**
   *
   * @typedef {Object} TemplateSelect
   * @property {string} name - form表单name
   * @property {string} value - form表单value
   */

  /**
   *
   * @typedef {Object} TemplateDetail
   * @property {string} key - form表单key
   * @property {string} name - form表单name
   * @property {string} type - form表单类型, 默认不传
   * @property {string} default - form表单初始化默认值
   * @property {array} value -
   */

  /**
   *
   * @typedef {Object} PayInfoTemplate
   * @property {string} name - 支付方式名称
   * @property {string} pay_method - 支付渠道key
   * @property {array} template -
   */

  /**
   *
   * @typedef {Object} PayInfoTemplateGroup
   * @property {array} scene -
   * @property {string} pay_scene - 支付场景
   * @property {array} template -
   */

  /**
   *
   * @typedef {Object} AccountListRspSer
   * @property {string} username - 账号
   * @property {string} member_name - 用户名称
   * @property {string} create_time - 创建时间
   */

  /**
   *
   * @typedef {Object} Role
   * @property {integer} id - id
   * @property {string} name - 角色名称
   * @property {array} organization -
   * @property {array} permission -
   * @property {string} create_time - 创建时间
   * @property {integer} status - 状态
   * @property {string} account_type_alias - Account type alias
   * @property {integer} account_type - 用户类型
   * @property {string} status_alias - Status alias
   * @property {integer} account_num - 关联账号数
   * @property {array} organization_alias -
   * @property {boolean} can_delete - 允许删除
   * @property {integer} company - 所属公司
   */

  /**
   *
   * @typedef {Object} ListRoleReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListRoleRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} PermissionListRspSer
   * @property {array} permission_list -
   * @property {string} permission_keys - 角色拥有的权限key
   */

  /**
   *
   * @typedef {Object} SetPermissionKeyReq
   * @property {integer} id - 角色ID
   * @property {array} permission -
   */

  /**
   *
   * @typedef {Object} BindOrganizationReqSer
   * @property {integer} org_no - 组织ID
   */

  /**
   *
   * @typedef {Object} ResetPwdReqSer
   * @property {integer} choices - 找回密码
   * @property {string} new_password - MD5加密后的新登录密码
   * @property {string} sms_code - 手机登陆验证码
   */

  /**
   *
   * @typedef {Object} LoginReqSer
   * @property {string} mode - 登录方式
   * @property {string} token - 登录Token
   * @property {string} username - 登录名
   * @property {string} password - MD5加密后登录密码
   * @property {string} phone - 手机
   * @property {string} sms_code - 手机登陆验证码
   * @property {string} code - CODE
   */

  /**
   *
   * @typedef {Object} LoginRspSer
   * @property {integer} account_id - 账号ID
   * @property {string} username - 用户名
   * @property {string} token - Token
   * @property {integer} account_type - 用户类型
   * @property {boolean} company_auth - 公司是否认证
   * @property {integer} company_id - 公司ID
   * @property {string} company_name - 公司名
   * @property {integer} shop_id - 门店I
   * @property {string} shop_name - 门店名
   * @property {integer} area_id - 区域ID
   * @property {string} area_name - 区域名
   * @property {integer} stall_id - 摊档ID
   * @property {string} stall_name - 摊档名
   * @property {string} remark - 备注
   * @property {string} member_name - 成员名
   * @property {string} name - 姓名
   * @property {string} nickname - 第三方账号昵称
   * @property {string} mobile - 手机号
   * @property {string} has_bind_wechat - 是否已绑定微信号
   * @property {string} last_login_time - 最后登录时间
   * @property {integer} role_id - 角色ID
   * @property {string} role_name - 角色名
   * @property {array} role_permission -
   */

  /**
   *
   * @typedef {Object} UserInfoModifyRspSer
   * @property {integer} choices - 登录方式
   * @property {string} name - 姓名
   * @property {string} password - MD5加密后的旧登录密码
   * @property {string} new_password - MD5加密后的新登录密码
   * @property {string} code - CODE
   * @property {string} sms_code - CODE
   * @property {string} phone - 手机
   */

  /**
   *
   * @typedef {Object} LoginSMSReqSer
   * @property {string} phone - 手机
   * @property {integer} choices - 获取验证码
   */

  /**
   *
   * @typedef {Object} ExportQueryReqSer
   * @property {string} query_id - 查询ID
   */

  /**
   *
   * @typedef {Object} ExportQueryRsqSer
   * @property {string} status - 状态
   * @property {string} url - 下载地址
   */

  /**
   *
   * @typedef {Object} SetBuffetInfoRspSer
   * @property {array} stall_nos -
   * @property {integer} limit_fee - 限制额度
   * @property {integer} warn_weight - 余量提示重量(克)
   * @property {integer} out_weight - 菜品售罄重量(克)
   */

  /**
   *
   * @typedef {Object} StallNoReqSer
   * @property {integer} stall_no - 档口组织ID
   */

  /**
   *
   * @typedef {Object} DeviceActivateReqSer
   * @property {string} device_number - 设备号
   * @property {string} device_mac - 设备MAC
   * @property {string} activation_code - 激活码
   */

  /**
   *
   * @typedef {Object} DeviceLoginReqSer
   * @property {string} device_key - 设备号
   * @property {string} username - 用户名
   * @property {string} password - MD5加密后登录密码
   */

  /**
   *
   * @typedef {Object} DefineDeviceReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {integer} organization_id - 组织ID
   * @property {boolean} online - 是否在线
   * @property {string} device_type - 设备类型
   * @property {string} device_name - 设备名
   * @property {string} device_number - 设备号
   * @property {boolean} activation_status - 是否激活
   */

  /**
   *
   * @typedef {Object} ModifyDeviceReqSer
   * @property {integer} device_no - 设备ID
   * @property {string} device_name - 编辑设备名
   * @property {string} device_type - 编辑设备类型
   * @property {array} user_group_ids -
   */

  /**
   *
   * @typedef {Object} DevicePrintConfJSONReqSer
   * @property {object} data - 配置
   * @property {integer} conf_no - 配置ID
   * @property {integer} organization_id - 组织ID
   * @property {string} device_type - 设备类型
   */

  /**
   *
   * @typedef {Object} DevicePrintConfJSONRspSer
   * @property {integer} device_no - Device no
   * @property {string} device_type - 设备类型
   * @property {string} create_time - 创建时间
   * @property {string} consume_addr - Consume addr
   * @property {object} data - Data
   * @property {integer} organization_no - Organization no
   */

  /**
   *
   * @typedef {Object} DevicePrintConfNosSer
   * @property {array} device_nos -
   */

  /**
   *
   * @typedef {Object} DevicePrintConfNoSer
   * @property {integer} device_no - 设备号
   */

  /**
   *
   * @typedef {Object} ListDevicePrintConfReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {integer} organization_id - 组织ID
   * @property {string} device_type - 设备类型
   */

  /**
   *
   * @typedef {Object} DevicePrintConfRspSer
   * @property {integer} device_no - Device no
   * @property {string} device_type - 设备类型
   * @property {string} create_time - 创建时间
   * @property {string} device_type_alias - Device type alias
   * @property {string} consume_addr - Consume addr
   */

  /**
   *
   * @typedef {Object} TrayInfoReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {array} org_ids -
   * @property {string} tray_no - 编号
   * @property {string} rfid - RFID
   * @property {string} remark - 备注
   */

  /**
   *
   * @typedef {Object} TrayInfoUrlReqSer
   * @property {string} oss_url - 文件url路径
   */

  /**
   *
   * @typedef {Object} TrayExportReqSer
   * @property {array} ids -
   */

  /**
   *
   * @typedef {Object} TrayBatchReqSer
   * @property {array} rfids -
   */

  /**
   *
   * @typedef {Object} AddDietGroupReq
   * @property {integer} account - 创建用户ID
   * @property {string} name - 名称
   * @property {string} group - 人群
   * @property {string} gender - 性别
   * @property {integer} special_group - 特殊人群
   * @property {number} min_age - 最小年龄
   * @property {number} max_age - 最大年龄
   * @property {string} source - 来源
   * @property {number} energy_mj - 能量(兆焦耳:mj/kg)
   * @property {number} energy_kcal - 能量(千卡:kcal/kg)
   * @property {number} axunge - 脂肪(g)
   * @property {number} protein - 蛋白质(g)
   * @property {string} element - 微量和常量元素(mg)
   * @property {string} vitamin - 维生素
   */

  /**
   *
   * @typedef {Object} DietGroup
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} gender_alias - Gender alias
   * @property {string} special_group_name - Special group name
   * @property {string} account_username - 创建人
   * @property {string} status - 状态
   * @property {string} name - 名称
   * @property {string} group - 人群
   * @property {string} gender - 性别
   * @property {integer} special_group - 特殊人群
   * @property {number} min_age - 最小年龄
   * @property {number} max_age - 最大年龄
   * @property {string} source - 来源
   * @property {number} energy_mj - 能量(兆焦耳:mj/kg)
   * @property {number} energy_kcal - 能量(千卡:kcal/kg)
   * @property {number} axunge - 脂肪(g)
   * @property {number} protein - 蛋白质(g)
   * @property {string} element - 微量和常量元素(mg)
   * @property {string} vitamin - 维生素
   * @property {integer} account - 创建人
   */

  /**
   *
   * @typedef {Object} ListDietGroupReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListDietGroupRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyDietGroupReq
   * @property {integer} id - ID
   * @property {integer} account - 创建用户ID
   * @property {string} name - 名称
   * @property {string} group - 人群
   * @property {string} gender - 性别
   * @property {integer} special_group - 特殊人群
   * @property {number} min_age - 最小年龄
   * @property {number} max_age - 最大年龄
   * @property {string} source - 来源
   * @property {number} energy_mj - 能量(兆焦耳:mj/kg)
   * @property {number} energy_kcal - 能量(千卡:kcal/kg)
   * @property {number} axunge - 脂肪(g)
   * @property {number} protein - 蛋白质(g)
   * @property {string} element - 微量和常量元素(mg)
   * @property {string} vitamin - 维生素
   * @property {string} status - 状态
   */

  /**
   *
   * @typedef {Object} FoodIngredientSer
   * @property {integer} id - id
   * @property {string} name - 食材名字
   * @property {integer} weight - 称重重量(克)
   * @property {string} supplier_name - 供应商名字
   */

  /**
   *
   * @typedef {Object} AddFoodReq
   * @property {integer} id - Id
   * @property {integer} company - 指定公司ID
   * @property {integer} organization - 指定组织ID
   * @property {integer} account - 创建用户ID
   * @property {string} name - 分类名
   * @property {integer} category - 二级分类
   * @property {array} spec_list -
   * @property {array} taste_list -
   * @property {boolean} is_stock_limit - 是否限制数量限制
   * @property {integer} stock_num - 库存
   * @property {boolean} is_new_product - 是否新产品
   * @property {string} new_start_date - 上新开始日期
   * @property {string} new_end_date - 上新结束日期
   * @property {boolean} is_support_reservation - 是否支持预约
   * @property {integer} sale_status - 上下架状态
   * @property {string} image - 菜品图片
   * @property {string} count_type - 计费方式
   * @property {string} weight_type - 称重方式
   * @property {integer} food_price - 菜品价格
   * @property {integer} weight_price - 称重价格
   * @property {integer} weight - 称重重量(克)
   * @property {integer} start_gram - 起始计价重量(克)
   * @property {integer} fault_rate - 误差率
   * @property {integer} origin_price - 成本价格
   * @property {integer} pack_price - 打包费用
   * @property {array} ingredient_list -
   * @property {array} device_picture_list -
   */

  /**
   *
   * @typedef {Object} FoodNutritionRespSer
   * @property {integer} weight - 称重重量(克)
   * @property {number} energy_mj - 能量(兆焦耳:mj/kg)
   * @property {number} energy_kcal - 能量(千卡:kcal/kg)
   * @property {number} axunge - 脂肪(g)
   * @property {number} protein - 蛋白质(g)
   * @property {string} element - 微量和常量元素(mg)
   * @property {string} vitamin - 维生素
   */

  /**
   *
   * @typedef {Object} FoodIngredientRespSer
   * @property {integer} id - ID
   * @property {integer} ingredient_id - 食材ID
   * @property {integer} weight - 称重重量(克)
   * @property {string} name - 食材名
   * @property {string} supplier_name - 供应商
   */

  /**
   *
   * @typedef {Object} FoodTasteRespSer
   * @property {integer} id - ID
   * @property {string} name - 口味
   */

  /**
   *
   * @typedef {Object} FoodSpecRespSer
   * @property {integer} id - ID
   * @property {string} name - 规格
   */

  /**
   *
   * @typedef {Object} FoodPriceInfoRespSer
   * @property {integer} count_type - 计费方式
   * @property {integer} weight_type - 称重方式
   * @property {integer} food_price - 菜品价格
   * @property {integer} weight_price - 称重价格
   * @property {integer} weight - 称重重量(克)
   * @property {integer} start_gram - 起始计价重量(克)
   * @property {integer} fault_rate - 误差率
   * @property {integer} origin_price - 成本价格
   * @property {integer} pack_price - 打包费用
   */

  /**
   *
   * @typedef {Object} FoodRes
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} sale_status_alias - Sale status alias
   * @property {} nutrition -
   * @property {string} company_name - 公司名
   * @property {string} category_name - 分类名
   * @property {string} organization_name - 组织名
   * @property {array} ingredients -
   * @property {array} taste_list -
   * @property {array} spec_list -
   * @property {} price_info -
   * @property {string} account_username - 创建人
   * @property {string} status - 状态
   * @property {string} no - 菜品编号
   * @property {string} image - 菜品图片
   * @property {string} name - 菜品名字
   * @property {boolean} is_stock_limit - 是否限制数量限制
   * @property {integer} stock_num - 库存数量
   * @property {boolean} is_new_product - 是否新产品
   * @property {string} new_start_date - 起始上新日期
   * @property {string} new_end_date - 终止上新日期
   * @property {integer} sale_status - 上下架状态
   * @property {boolean} is_support_reservation - 是否支持预约
   * @property {boolean} is_origin - 是否为主菜品
   * @property {boolean} is_allow_modify - 是否允许修改
   * @property {integer} company - 所属项目点公司
   * @property {integer} organization - 创建组织
   * @property {integer} account - 创建人
   * @property {integer} category - 食品二级分类
   * @property {integer} origin - 主菜品
   * @property {array} use_organizations -
   */

  /**
   *
   * @typedef {Object} AddFoodIngredient
   * @property {integer} food_id - 菜品ID
   * @property {integer} ingredient_id - 食材ID
   * @property {integer} weight - 重量
   */

  /**
   *
   * @typedef {Object} AddFoodSpec
   * @property {integer} food_id - 菜品ID
   * @property {string} name - 规格
   */

  /**
   *
   * @typedef {Object} AddFoodTaste
   * @property {integer} food_id - 菜品ID
   * @property {string} name - 口味
   */

  /**
   *
   * @typedef {Object} AddFoodDiscount
   * @property {integer} discount_type - 优惠方式
   * @property {integer} discount_fee - 价格
   * @property {integer} count_type - 计费方式
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {array} meal_types -
   * @property {array} user_groups -
   * @property {integer} discount_mode - 优惠形式
   */

  /**
   *
   * @typedef {Object} BatchAddFoodDiscount
   * @property {array} food_ids -
   * @property {array} data -
   * @property {string} status - 优惠状态
   */

  /**
   *
   * @typedef {Object} BatchModifyFoodReq
   * @property {array} ids -
   * @property {array} spec_list -
   * @property {array} taste_list -
   * @property {boolean} is_support_reservation - 是否支持预约
   * @property {integer} sale_status - 计费方式
   * @property {string} image - 菜品图片
   * @property {integer} count_type - 计费方式
   * @property {integer} weight_type - 称重方式
   * @property {integer} food_price - 菜品价格
   * @property {integer} weight_price - 称重价格
   * @property {integer} weight - 称重重量(克)
   * @property {integer} start_gram - 起始计价重量(克)
   * @property {integer} fault_rate - 误差率
   * @property {integer} origin_price - 成本价格
   * @property {integer} pack_price - 打包费用
   * @property {boolean} is_stock_limit - 是否限制数量限制
   * @property {integer} stock_num - 库存
   */

  /**
   *
   * @typedef {Object} BatchOnSaleReq
   * @property {array} ids -
   * @property {integer} sale_status - 上下架状态
   */

  /**
   *
   * @typedef {Object} RemoveFoodIngredientIds
   * @property {integer} food_id - 菜品ID
   * @property {array} ids -
   */

  /**
   *
   * @typedef {Object} DistributeFoodReq
   * @property {integer} id - ID
   * @property {array} organizations -
   * @property {boolean} is_allow_modify - 是否允许修改
   */

  /**
   *
   * @typedef {Object} ListFoodReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} FoodDiscountReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {integer} food_id - 菜品ID
   */

  /**
   *
   * @typedef {Object} FoodDiscount
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} discount_type_alias - Discount type alias
   * @property {string} count_type_alias - Count type alias
   * @property {string} meal_type_alias - Meal type alias
   * @property {string} discount_mode_alias - Discount mode alias
   * @property {string} user_group_name - User group name
   * @property {integer} discount_type - 优惠方式
   * @property {integer} discount_fee - 价格
   * @property {integer} count_type - 计费方式
   * @property {string} start_date - 优惠开始日期
   * @property {string} end_date - 优惠结束日期
   * @property {object} meal_types - 餐段类型
   * @property {integer} discount_mode - 优惠形式
   * @property {string} status - 状态
   * @property {integer} food - 菜品
   * @property {array} user_groups -
   */

  /**
   *
   * @typedef {Object} ListFoodDiscountRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyFoodReq
   * @property {integer} id - ID
   * @property {string} no - 菜品编号
   * @property {string} name - 分类名
   * @property {integer} category - 二级分类
   * @property {boolean} is_new_product - 是否新产品
   * @property {string} new_start_date - 上新开始日期
   * @property {string} new_end_date - 上新结束日期
   * @property {string} image - 菜品图片
   * @property {integer} sale_status - 上下架状态
   * @property {string} count_type - 计费方式
   * @property {string} weight_type - 称重方式
   * @property {string} status - 状态
   * @property {integer} food_price - 菜品价格
   * @property {integer} weight_price - 称重价格
   * @property {integer} weight - 称重重量(克)
   * @property {integer} start_gram - 起始计价重量(克)
   * @property {integer} fault_rate - 误差率
   * @property {integer} origin_price - 成本价格
   * @property {integer} pack_price - 打包费用
   * @property {boolean} is_support_reservation - 是否支持预约
   * @property {boolean} is_stock_limit - 是否限制数量限制
   * @property {integer} stock_num - 库存
   */

  /**
   *
   * @typedef {Object} ModifyFoodDiscount
   * @property {integer} id - ID
   * @property {integer} discount_type - 优惠方式
   * @property {integer} discount_fee - 价格
   * @property {integer} count_type - 计费方式
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {array} meal_types -
   * @property {array} user_groups -
   * @property {integer} discount_mode - 优惠形式
   * @property {string} status - 状态
   */

  /**
   *
   * @typedef {Object} UserReservationFoodReq
   * @property {string} user_id - 用户ID
   * @property {string} date - 预约日期
   * @property {integer} shop_id - 食堂ID
   * @property {string} meal_type - 餐段
   */

  /**
   *
   * @typedef {Object} Params
   */

  /**
   *
   * @typedef {Object} AddFoodCategoryReq
   * @property {integer} id - Id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} company - 指定公司ID
   * @property {string} company_name - 公司名
   * @property {integer} organization - 指定组织ID
   * @property {string} organization_name - 组织名
   * @property {string} name - 分类名
   * @property {integer} sort - 一级分类
   * @property {string} sort_name - 分类名
   * @property {string} color - 颜色
   * @property {string} status_alias - Status alias
   * @property {string} color_alias - Color alias
   */

  /**
   *
   * @typedef {Object} FoodCategory
   * @property {integer} id - id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} company - 所属项目点公司
   * @property {string} company_name - 公司名
   * @property {integer} organization - 创建组织
   * @property {string} organization_name - 组织名
   * @property {string} name - 分类名
   * @property {integer} sort - 食品一级分类
   * @property {string} sort_name - 分类名
   * @property {string} color - 颜色
   * @property {string} status_alias - Status alias
   * @property {string} color_alias - Color alias
   */

  /**
   *
   * @typedef {Object} ListFoodCategoryReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListFoodCategoryRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyFoodCategoryReq
   * @property {integer} id - ID
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} company - 所属项目点公司
   * @property {string} company_name - 公司名
   * @property {integer} organization - 指定组织ID
   * @property {string} organization_name - 组织名
   * @property {string} name - 分类名
   * @property {integer} sort - 一级分类
   * @property {string} sort_name - 分类名
   * @property {string} color - 颜色
   * @property {string} status_alias - Status alias
   * @property {string} color_alias - Color alias
   */

  /**
   *
   * @typedef {Object} AddFoodSortReq
   * @property {integer} company - 指定公司ID
   * @property {string} name - 分类名
   */

  /**
   *
   * @typedef {Object} FoodSort
   * @property {integer} id - id
   * @property {integer} company - 所属项目点公司
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {string} name - 分类名
   * @property {string} status_alias - Status alias
   * @property {string} company_name - 公司名
   */

  /**
   *
   * @typedef {Object} ListFoodSortReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListFoodSortRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyFoodSortReq
   * @property {integer} id - ID
   * @property {integer} company - 指定公司ID
   * @property {string} name - 分类名
   */

  /**
   *
   * @typedef {Object} AddIngredientReq
   * @property {integer} id - id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} company - 指定公司ID
   * @property {string} name - 食材名
   * @property {integer} supplier - 供应商
   * @property {integer} sort - 食材分类
   * @property {integer} organization - 指定组织ID
   * @property {string} status_alias - Status alias
   * @property {number} energy_mj - 能量(兆焦耳:mj/kg)
   * @property {number} energy_kcal - 能量(千卡:kcal/kg)
   * @property {number} axunge - 脂肪(g)
   * @property {number} protein - 蛋白质(g)
   * @property {string} element - 微量和常量元素(mg)
   * @property {string} vitamin - 维生素
   */

  /**
   *
   * @typedef {Object} Ingredient
   * @property {integer} id - id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} company - 所属项目点公司
   * @property {string} name - 食材名
   * @property {integer} supplier - 供应商
   * @property {integer} sort - 食材分类
   * @property {integer} organization - 创建组织
   * @property {string} status_alias - Status alias
   * @property {number} energy_mj - 能量(兆焦耳:mj/kg)
   * @property {number} energy_kcal - 能量(千卡:kcal/kg)
   * @property {number} axunge - 脂肪(g)
   * @property {number} protein - 蛋白质(g)
   * @property {string} element - 微量和常量元素(mg)
   * @property {string} vitamin - 维生素
   */

  /**
   *
   * @typedef {Object} ListIngredientReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListIngredientRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyIngredientReq
   * @property {integer} id - ID
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} company - 指定公司ID
   * @property {string} name - 食材名
   * @property {integer} supplier - 供应商
   * @property {integer} sort - 食材分类
   * @property {integer} organization - 指定组织ID
   * @property {string} status_alias - Status alias
   * @property {number} energy_mj - 能量(兆焦耳:mj/kg)
   * @property {number} energy_kcal - 能量(千卡:kcal/kg)
   * @property {number} axunge - 脂肪(g)
   * @property {number} protein - 蛋白质(g)
   * @property {string} element - 微量和常量元素(mg)
   * @property {string} vitamin - 维生素
   */

  /**
   *
   * @typedef {Object} SyncIngredientReq
   * @property {array} ids -
   * @property {array} organizations -
   */

  /**
   *
   * @typedef {Object} AddIngredientSortReq
   * @property {integer} id - Id
   * @property {integer} company - 指定公司ID
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {string} name - 分类名
   * @property {string} status_alias - Status alias
   */

  /**
   *
   * @typedef {Object} IngredientSort
   * @property {integer} id - id
   * @property {integer} company - 所属项目点公司
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {string} name - 分类名
   * @property {string} status_alias - Status alias
   */

  /**
   *
   * @typedef {Object} ListIngredientSortReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListIngredientSortRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyIngredientSortReq
   * @property {integer} id - ID
   * @property {string} status - 状态
   * @property {string} name - 分类名
   */

  /**
   *
   * @typedef {Object} AddIngredientSupplierReq
   * @property {integer} id - Id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {string} no - 编号
   * @property {string} name - 分类名
   * @property {integer} sort - 食材分类
   * @property {string} sort_name - 分类名
   * @property {string} credit_code - 社会统一信用代码
   * @property {string} tax_registration_license - 税务登记证
   * @property {string} business_license - 经营许可证
   * @property {string} circulation_license - 流通许可证
   * @property {string} person_name - 负责人姓名
   * @property {string} phone - 联系电话
   * @property {string} address - 供应商地址
   * @property {string} status_alias - Status alias
   */

  /**
   *
   * @typedef {Object} IngredientSupplier
   * @property {integer} id - id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {string} no - 供应商编号
   * @property {string} name - 供应商名
   * @property {integer} sort - 食材分类
   * @property {string} sort_name - 分类名
   * @property {string} credit_code - 社会统一信用代码
   * @property {string} tax_registration_license - 税务登记证
   * @property {string} business_license - 经营许可证
   * @property {string} circulation_license - 流通许可证
   * @property {string} person_name - 负责人姓名
   * @property {string} phone - 联系电话
   * @property {string} address - 供应商地址
   * @property {string} status_alias - Status alias
   */

  /**
   *
   * @typedef {Object} ListIngredientSupplierReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListIngredientSupplierRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyIngredientSupplierReq
   * @property {integer} id - Id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {string} no - 编号
   * @property {string} name - 分类名
   * @property {integer} sort - 食材分类
   * @property {string} sort_name - 分类名
   * @property {string} credit_code - 社会统一信用代码
   * @property {string} tax_registration_license - 税务登记证
   * @property {string} business_license - 经营许可证
   * @property {string} circulation_license - 流通许可证
   * @property {string} person_name - 负责人姓名
   * @property {string} phone - 联系电话
   * @property {string} address - 供应商地址
   * @property {string} status_alias - Status alias
   */

  /**
   *
   * @typedef {Object} ListMenuDeviceReq
   * @property {array} device_types -
   */

  /**
   *
   * @typedef {Object} MenuDevice
   * @property {integer} id - id
   * @property {string} device_number - 设备号
   * @property {string} device_type - 设备类型
   */

  /**
   *
   * @typedef {Object} AddMenuMonthlyReq
   * @property {integer} company - 指定公司ID
   * @property {integer} organization - 指定组织ID
   * @property {integer} account - 创建用户ID
   * @property {string} month - 月份
   * @property {string} name - 菜谱名
   * @property {integer} apply_group - 适用人群ID
   * @property {array} device_types -
   * @property {array} devices -
   * @property {array} use_organizations -
   * @property {array} use_user_groups -
   */

  /**
   *
   * @typedef {Object} MenuDietGroup
   * @property {integer} id - ID
   * @property {string} name - 名称
   */

  /**
   *
   * @typedef {Object} MenuOrganization
   * @property {integer} id - ID
   * @property {string} name - 组织名
   */

  /**
   *
   * @typedef {Object} MenuUserGroup
   * @property {integer} id - ID
   * @property {string} name - 分组名
   */

  /**
   *
   * @typedef {Object} MenuMonthly
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} company_name - 公司名
   * @property {string} organization_name - 组织名
   * @property {string} account_username - 创建人
   * @property {} apply_group -
   * @property {array} devices -
   * @property {array} use_organizations -
   * @property {array} use_user_groups -
   * @property {string} status - 状态
   * @property {integer} month - 月
   * @property {string} name - 菜谱名
   * @property {object} device_types - 设备类型
   * @property {boolean} is_used - 是否使用
   * @property {integer} company - 所属项目点公司
   * @property {integer} organization - 创建组织
   * @property {integer} account - 创建人
   */

  /**
   *
   * @typedef {Object} BatchUsedMenuMonthlyReq
   * @property {array} ids -
   * @property {boolean} is_used - 是否使用
   */

  /**
   *
   * @typedef {Object} ListMenuMonthlyRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} MenuMonthlyFoodCopyDayReq
   * @property {integer} id - ID
   * @property {string} copy_date - 复制日期
   * @property {array} dates -
   */

  /**
   *
   * @typedef {Object} MonthlyTarget
   * @property {string} date - 日期
   * @property {string} meal_type - 餐段类型
   */

  /**
   *
   * @typedef {Object} MenuMonthlyFoodCopyMealTypeReq
   * @property {integer} id - ID
   * @property {array} target_data -
   */

  /**
   *
   * @typedef {Object} ListMenuMonthlyReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} MenuMonthlyFoodModifyReq
   * @property {integer} id - ID
   * @property {string} use_date - 使用日期
   * @property {string} setting_stock - 菜品库存
   * @property {string} meal_type - 餐段类型
   */

  /**
   *
   * @typedef {Object} ModifyMenuMonthlyReq
   * @property {integer} id - ID
   * @property {integer} company - 指定公司ID
   * @property {integer} organization - 指定组织ID
   * @property {integer} account - 创建用户ID
   * @property {string} month - 月份
   * @property {string} name - 菜谱名
   * @property {integer} apply_group - 适用人群ID
   * @property {array} device_types -
   * @property {array} devices -
   * @property {array} use_organizations -
   * @property {array} use_user_groups -
   * @property {boolean} is_used - 是否使用
   */

  /**
   *
   * @typedef {Object} CopyMenuMonthlyReq
   * @property {integer} id - ID
   * @property {array} months -
   */

  /**
   *
   * @typedef {Object} AddMenuWeeklyReq
   * @property {integer} company - 指定公司ID
   * @property {integer} organization - 指定组织ID
   * @property {integer} account - 创建用户ID
   * @property {string} start_date - 开始日期
   * @property {string} end_date - 结束日期
   * @property {string} name - 菜谱名
   * @property {integer} apply_group - 适用人群ID
   * @property {array} device_types -
   * @property {array} devices -
   * @property {array} use_organizations -
   * @property {array} use_user_groups -
   */

  /**
   *
   * @typedef {Object} MenuWeekly
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} company_name - 公司名
   * @property {string} organization_name - 组织名
   * @property {string} account_username - 创建人
   * @property {} apply_group -
   * @property {array} devices -
   * @property {array} use_organizations -
   * @property {array} use_user_groups -
   * @property {array} device_types -
   * @property {string} status - 状态
   * @property {integer} week - 周
   * @property {string} name - 菜谱名
   * @property {boolean} is_used - 是否使用
   * @property {integer} company - 所属项目点公司
   * @property {integer} organization - 创建组织
   * @property {integer} account - 创建人
   */

  /**
   *
   * @typedef {Object} BatchUsedMenuWeeklyReq
   * @property {array} ids -
   * @property {boolean} is_used - 是否使用
   */

  /**
   *
   * @typedef {Object} ListMenuWeeklyRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ListFoodRes
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} MenuFoodCopyDayReq
   * @property {integer} id - ID
   * @property {string} copy_date - 复制日期
   * @property {array} weekdays -
   */

  /**
   *
   * @typedef {Object} ListMenuFoodRes
   */

  /**
   *
   * @typedef {Object} Target
   * @property {integer} weekday - 周几
   * @property {string} meal_type - 餐段类型
   */

  /**
   *
   * @typedef {Object} MenuFoodCopyMealTypeReq
   * @property {integer} id - ID
   * @property {array} target_data -
   */

  /**
   *
   * @typedef {Object} MenuFoodFilter
   */

  /**
   *
   * @typedef {Object} ListMenuWeeklyReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} MenuFoodModifyReq
   * @property {integer} id - ID
   * @property {string} use_date - 使用日期
   * @property {string} setting_stock - 菜品库存
   * @property {string} meal_type - 餐段类型
   */

  /**
   *
   * @typedef {Object} ModifyMenuWeeklyReq
   * @property {integer} id - ID
   * @property {integer} company - 指定公司ID
   * @property {integer} organization - 指定组织ID
   * @property {integer} account - 创建用户ID
   * @property {string} start_date - 开始日期
   * @property {string} end_date - 结束日期
   * @property {string} name - 菜谱名
   * @property {integer} apply_group - 适用人群ID
   * @property {array} device_types -
   * @property {array} devices -
   * @property {array} use_organizations -
   * @property {array} use_user_groups -
   * @property {boolean} is_used - 是否使用
   */

  /**
   *
   * @typedef {Object} CopyMenuWeeklyReq
   * @property {integer} id - ID
   * @property {array} start_dates -
   */

  /**
   *
   * @typedef {Object} TimeRangeSer
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   */

  /**
   *
   * @typedef {Object} RuleInfoSer
   * @property {array} meal -
   * @property {object} rule - 配置
   * @property {string} rule_type - 消费类型
   */

  /**
   *
   * @typedef {Object} AddConsumeRuleReqSer
   * @property {string} name - 活动名称
   * @property {array} org_nos -
   * @property {array} group_nos -
   * @property {array} wallet_type -
   * @property {array} except_weekday -
   * @property {array} except_date_range -
   * @property {array} rules -
   * @property {string} tip - 说明
   * @property {integer} consume_type - 消费类型
   */

  /**
   *
   * @typedef {Object} ConsumeRuleNoReqSer
   * @property {integer} rule_no - 规则ID
   */

  /**
   *
   * @typedef {Object} ListConsumeRuleReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} name - 活动名称
   * @property {integer} group_no - 分组ID
   */

  /**
   *
   * @typedef {Object} ConsumeRuleSer
   * @property {integer} rule_no - 规则ID
   * @property {string} name - 活动名称
   * @property {string} org_name - 所属组织
   * @property {string} group_name - 所属分组
   * @property {string} tip - 说明
   * @property {string} consume_type_alias - 活动名称
   */

  /**
   *
   * @typedef {Object} ListConsumeRuleRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyConsumeRuleReqSer
   * @property {string} name - 活动名称
   * @property {array} org_nos -
   * @property {array} group_nos -
   * @property {array} wallet_type -
   * @property {array} except_weekday -
   * @property {array} except_date_range -
   * @property {array} rules -
   * @property {string} tip - 说明
   * @property {integer} consume_type - 消费类型
   * @property {integer} rule_no - 规则ID
   */

  /**
   *
   * @typedef {Object} DiscountListSer
   * @property {integer} limit_fee - 生效额度
   * @property {integer} coupon_fee - 优惠额度
   */

  /**
   *
   * @typedef {Object} AddRechargeCouponReqSer
   * @property {string} name - 活动名称
   * @property {array} user_group_nos -
   * @property {string} start_time - 开始时间
   * @property {string} end_time - 结束时间
   * @property {array} discount -
   * @property {integer} limit_type - 限制方式
   * @property {integer} upper_limit - 活动 支出上限/次数上限
   * @property {string} tip - 规则说明
   * @property {string} status - 活动状态
   */

  /**
   *
   * @typedef {Object} ChangeCouponStatusSer
   * @property {integer} coupon_no - 活动编号
   * @property {string} status - 活动状态
   */

  /**
   *
   * @typedef {Object} CouponNoReqSer
   * @property {integer} coupon_no - 活动编号
   */

  /**
   *
   * @typedef {Object} RechargeCouponRspSer
   * @property {integer} coupon_no - Coupon no
   * @property {string} name - 活动名称
   * @property {string} org_name - Org name
   * @property {string} user_groups_name - User groups name
   * @property {string} start_time - 开始时间
   * @property {string} end_time - 结束时间
   * @property {object} discount - Discount
   * @property {integer} limit_type - 限制方式
   * @property {integer} upper_limit - 活动金额支出上限/活动次数上限
   * @property {string} status - 状态
   * @property {string} tip - 说明
   */

  /**
   *
   * @typedef {Object} RechargeCouponReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} name - 活动名称
   * @property {string} status - 活动状态
   */

  /**
   *
   * @typedef {Object} RechargeCoupon
   * @property {integer} coupon_no - Coupon no
   * @property {string} org_name - Org name
   * @property {string} name - 活动名称
   * @property {string} start_time - 开始时间
   * @property {string} end_time - 结束时间
   * @property {string} limit_type_alias - Limit type alias
   * @property {string} status - 状态
   * @property {string} status_alias - Status alias
   */

  /**
   *
   * @typedef {Object} ListRechargeCouponRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyRechargeCouponReqSer
   * @property {integer} coupon_no - 活动编号
   * @property {string} name - 活动名称
   * @property {array} user_group_nos -
   * @property {string} start_time - 开始时间
   * @property {string} end_time - 结束时间
   * @property {array} discount -
   * @property {integer} limit_type - 限制方式
   * @property {integer} upper_limit - 活动 支出上限/次数上限
   * @property {string} tip - 规则说明
   */

  /**
   *
   * @typedef {Object} RechargeCouponReportReqSer
   * @property {string} start_time - 开始时间
   * @property {string} end_time - 结束时间
   * @property {string} name - 用户名
   * @property {string} person_no - 用户编号
   * @property {string} out_trade_no - 订单号
   * @property {string} coupon_name - 活动名
   * @property {integer} group_no - 分组ID
   * @property {boolean} is_export - 是否导出报表
   */

  /**
   *
   * @typedef {Object} RechargeCouponReportRspSer
   * @property {string} org_name - Org name
   * @property {string} coupon_name - Coupon name
   * @property {string} out_trade_no - Out trade no
   * @property {string} person_no - 到账人员编号
   * @property {string} name - 姓名
   * @property {string} payer_group - Payer group
   * @property {integer} wallet_fee - 储值钱包动账金额(分)
   * @property {string} tip - Tip
   * @property {string} pay_time - Pay time
   */

  /**
   *
   * @typedef {Object} MessagesSerializerReq
   * @property {integer} msg_no - 公告ID
   * @property {array} organization_ids -
   * @property {string} title - 标题
   * @property {string} msg_type - 公告类型
   * @property {string} content - 公告内容
   * @property {string} file_url - 附件链接
   * @property {string} post_time - 发送时间
   */

  /**
   *
   * @typedef {Object} MsgReceive
   * @property {string} title - 主题
   * @property {string} msg_type - 公告类型
   * @property {string} post_time - 发布时间
   * @property {string} content - 通知文本
   * @property {string} file_url - 通知文本
   * @property {array} organization_ids -
   */

  /**
   *
   * @typedef {Object} MsgList
   * @property {integer} id - id
   * @property {string} title - 主题
   * @property {string} msg_type - 公告类型
   * @property {string} sender - 发送者
   * @property {string} post_time - 发布时间
   * @property {string} status_alias - Status alias
   */

  /**
   *
   * @typedef {Object} MsgOptions
   * @property {array} msg_nos -
   * @property {integer} options - 操作选项
   */

  /**
   *
   * @typedef {Object} ListOrderChargeReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_pay_time - 开始充值时间
   * @property {string} end_pay_time - 结束充值时间
   * @property {string} start_finish_time - 开始充值到账时间
   * @property {string} end_finish_time - 结束充值到账时间
   * @property {string} payway - 充值渠道
   * @property {string} sub_payway - 充值类型
   * @property {string} order_status - 充值状态
   * @property {string} trade_no - 充值订单号
   * @property {string} out_trade_no - 第三方订单号
   * @property {string} provider_trade_no - 交易流水号
   * @property {string} person_no - 用户编号
   * @property {string} phone - 手机号
   * @property {string} name - 用户姓名
   */

  /**
   *
   * @typedef {Object} OrderCharge
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} wallet_name - 到账钱包名
   * @property {string} payway - 充值渠道
   * @property {string} payway_alias - 充值渠道别名
   * @property {string} sub_payway - 充值类型
   * @property {string} sub_payway_alias - 充值类型别名
   * @property {string} settle_status - 对账状态
   * @property {string} settle_status_alias - 对账状态别名
   * @property {string} order_status_alias - Order status alias
   * @property {string} status - 状态
   * @property {string} name - 姓名
   * @property {string} card_no - 到账卡编号
   * @property {string} person_no - 到账人员编号
   * @property {string} phone - 手机号
   * @property {integer} wallet_fee - 储值钱包动账金额(分)
   * @property {integer} subsidy_fee - 赠送钱包动账金额(分)
   * @property {integer} complimentary_fee - 赠送钱包动账金额(分)
   * @property {integer} wallet_balance - 钱包余额(分)
   * @property {integer} subsidy_balance - 补贴钱包余额
   * @property {integer} complimentary_balance - 赠送钱包余额(分)
   * @property {string} trade_no - 系统充值订单号
   * @property {string} out_trade_no - 接入方订单号,第三方系统订单号
   * @property {string} provider_trade_no - 提供方交易流水号
   * @property {integer} origin_fee - 充值原金额(分)
   * @property {integer} pay_fee - 支付金额(分)
   * @property {string} order_status - 状态
   * @property {string} finish_time - 充值到账时间
   * @property {string} user - 统一用户id
   * @property {integer} card_info - Card info
   * @property {integer} payer_group - 用户分组
   * @property {integer} payer_department_group - 用户部门
   * @property {integer} wallet - 到账钱包
   * @property {integer} order - Order
   * @property {integer} company - Company
   * @property {integer} organization - Organization
   * @property {integer} payinfo - Payinfo
   * @property {integer} coupon - Coupon
   */

  /**
   *
   * @typedef {Object} ListOrderChargeRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   * @property {integer} total_count - 合计充值笔数
   * @property {integer} total_amount - 合计充值金额
   * @property {integer} total_real_amount - 合计到账金额
   * @property {integer} total_complimentary_amount - 合计赠送金额
   */

  /**
   *
   * @typedef {Object} OrderChargeRefundReqSer
   * @property {string} trade_no - 订单号
   * @property {integer} refund_fee - 退款金额(分)
   * @property {string} refund_reason - 退款理由
   */

  /**
   *
   * @typedef {Object} ListOrderPaymentReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_pay_time - 开始充值时间
   * @property {string} end_pay_time - 结束充值时间
   * @property {string} start_finish_time - 开始充值到账时间
   * @property {string} end_finish_time - 结束充值到账时间
   * @property {string} payway - 充值渠道
   * @property {string} sub_payway - 充值类型
   * @property {string} order_status - 支付状态
   * @property {string} trade_no - 充值订单号
   * @property {string} out_trade_no - 第三方订单号
   * @property {string} provider_trade_no - 交易流水号
   * @property {string} person_no - 用户编号
   * @property {string} phone - 手机号
   * @property {string} name - 用户姓名
   * @property {integer} payer_group_id - 用户分组ID
   * @property {integer} payer_department_group_id - 用户部门ID
   * @property {integer} org_id - 消费点(组织)
   * @property {string} wallet_type - 动账钱包类型
   * @property {string} meal_type - 餐段
   */

  /**
   *
   * @typedef {Object} OrderPayment
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} status - 状态
   * @property {string} name - 姓名
   * @property {string} card_no - 到账卡编号
   * @property {string} person_no - 到账人员编号
   * @property {string} phone - 手机号
   * @property {integer} wallet_fee - 储值钱包动账金额(分)
   * @property {integer} subsidy_fee - 赠送钱包动账金额(分)
   * @property {integer} complimentary_fee - 赠送钱包动账金额(分)
   * @property {integer} wallet_balance - 钱包余额(分)
   * @property {integer} subsidy_balance - 补贴钱包余额
   * @property {integer} complimentary_balance - 赠送钱包余额(分)
   * @property {string} trade_no - 系统订单号
   * @property {string} out_trade_no - 接入方订单号,第三方系统订单号
   * @property {string} provider_trade_no - 提供方交易流水号
   * @property {string} device_number - 设备编码
   * @property {string} remark - 备注
   * @property {string} face_url - 人脸图片
   * @property {integer} origin_fee - 订单原始金额，单位为分
   * @property {integer} pay_fee - 订单支付金额，单位为分
   * @property {integer} discount_fee - 订单优惠金额，单位为分
   * @property {string} order_status - 状态
   * @property {string} meal_type - 餐段
   * @property {object} food_records - 识别时的食物列表或碗碟列表(原始数据)
   * @property {string} payment_order_type - 消费订单类型
   * @property {string} user - 统一用户id
   * @property {integer} card_info - Card info
   * @property {integer} payer_group - 用户分组
   * @property {integer} payer_department_group - 用户部门
   * @property {integer} order - Order
   * @property {integer} company - Company
   * @property {integer} organization - 消费点组织
   * @property {integer} device - 外键一下那个设备的id
   * @property {integer} wallet - Wallet
   * @property {integer} payinfo - Payinfo
   * @property {integer} consume_rule - 消费规则
   */

  /**
   *
   * @typedef {Object} ListOrderPaymentRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} OrderRefundChargeListReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_create_time - 开始退款时间
   * @property {string} end_create_time - 结束退款时间
   * @property {string} payway - 退款渠道
   * @property {string} sub_payway - 退款类型
   * @property {string} order_status - 退款状态
   * @property {string} refund_charge_trade_no - 退款充值订单号
   * @property {integer} order_type - 退款订单类型
   * @property {string} out_trade_no - 第三方订单号
   * @property {string} provider_trade_no - 交易流水号
   * @property {string} person_no - 用户编号
   * @property {string} phone - 手机号
   * @property {string} name - 用户姓名
   */

  /**
   *
   * @typedef {Object} RefundOrderChargeSer
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} wallet_name - 到账钱包名
   * @property {string} payway - 退款渠道
   * @property {string} payway_alias - 退款渠道别名
   * @property {string} sub_payway - 退款类型
   * @property {string} sub_payway_alias - 退款类型别名
   * @property {string} settle_status - 对账状态
   * @property {string} settle_status_alias - 对账状态别名
   * @property {string} origin_order_type_alias - 退款订单类型
   * @property {string} refund_type_alias - 退款类别别名
   * @property {string} order_status_alias - Order status alias
   * @property {string} status - 状态
   * @property {string} name - 姓名
   * @property {string} card_no - 到账卡编号
   * @property {string} person_no - 到账人员编号
   * @property {string} phone - 手机号
   * @property {integer} wallet_fee - 储值钱包动账金额(分)
   * @property {integer} subsidy_fee - 赠送钱包动账金额(分)
   * @property {integer} complimentary_fee - 赠送钱包动账金额(分)
   * @property {integer} wallet_balance - 钱包余额(分)
   * @property {integer} subsidy_balance - 补贴钱包余额
   * @property {integer} complimentary_balance - 赠送钱包余额(分)
   * @property {string} origin_trade_no - 原始系统订单号
   * @property {string} refund_no - 退款单号
   * @property {string} out_trade_no - 接入方订单号,第三方系统订单号
   * @property {string} provider_trade_no - 提供方交易流水号
   * @property {integer} refund_fee - 退款金额
   * @property {integer} origin_fee - 订单原金额
   * @property {string} refund_reason - 退款理由
   * @property {string} fail_reason - 失败原因
   * @property {string} deal_status - 处理状态
   * @property {string} order_status - 状态
   * @property {string} refund_type - 退款类型
   * @property {string} finish_time - 处理完成时间
   * @property {string} user - 统一用户id
   * @property {integer} card_info - Card info
   * @property {integer} payer_group - 用户分组
   * @property {integer} payer_department_group - 用户部门
   * @property {integer} wallet - 到账钱包
   * @property {integer} order - Order
   * @property {integer} company - Company
   * @property {integer} organization - Organization
   * @property {integer} origin_order - Origin order
   * @property {integer} payinfo - Payinfo
   */

  /**
   *
   * @typedef {Object} OrderRefundChargeListRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} OrderRefundListReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_create_time - 开始退款时间
   * @property {string} end_create_time - 结束退款时间
   * @property {string} payway - 退款渠道
   * @property {string} sub_payway - 退款类型
   * @property {string} order_status - 退款状态
   * @property {string} refund_charge_trade_no - 退款充值订单号
   * @property {integer} order_type - 退款订单类型
   * @property {string} out_trade_no - 第三方订单号
   * @property {string} provider_trade_no - 交易流水号
   * @property {string} person_no - 用户编号
   * @property {string} phone - 手机号
   * @property {string} name - 用户姓名
   */

  /**
   *
   * @typedef {Object} OrderRefund
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} wallet_name - 到账钱包名
   * @property {string} payway - 退款渠道
   * @property {string} payway_alias - 退款渠道别名
   * @property {string} sub_payway - 退款类型
   * @property {string} sub_payway_alias - 退款类型别名
   * @property {string} settle_status - 对账状态
   * @property {string} settle_status_alias - 对账状态别名
   * @property {string} origin_order_type_alias - 退款订单类型
   * @property {string} refund_type_alias - 退款类别别名
   * @property {string} order_status_alias - Order status alias
   * @property {string} status - 状态
   * @property {string} name - 姓名
   * @property {string} card_no - 到账卡编号
   * @property {string} person_no - 到账人员编号
   * @property {string} phone - 手机号
   * @property {integer} wallet_fee - 储值钱包动账金额(分)
   * @property {integer} subsidy_fee - 赠送钱包动账金额(分)
   * @property {integer} complimentary_fee - 赠送钱包动账金额(分)
   * @property {integer} wallet_balance - 钱包余额(分)
   * @property {integer} subsidy_balance - 补贴钱包余额
   * @property {integer} complimentary_balance - 赠送钱包余额(分)
   * @property {string} origin_trade_no - 原始系统订单号
   * @property {string} refund_no - 退款单号
   * @property {string} out_trade_no - 接入方订单号,第三方系统订单号
   * @property {string} provider_trade_no - 提供方交易流水号
   * @property {integer} refund_fee - 退款金额
   * @property {integer} origin_fee - 订单原金额
   * @property {string} refund_reason - 退款理由
   * @property {string} fail_reason - 失败原因
   * @property {string} deal_status - 处理状态
   * @property {string} order_status - 状态
   * @property {string} refund_type - 退款类型
   * @property {string} finish_time - 处理完成时间
   * @property {string} user - 统一用户id
   * @property {integer} card_info - Card info
   * @property {integer} payer_group - 用户分组
   * @property {integer} payer_department_group - 用户部门
   * @property {integer} wallet - 到账钱包
   * @property {integer} order - Order
   * @property {integer} company - Company
   * @property {integer} organization - Organization
   * @property {integer} origin_order - Origin order
   * @property {integer} payinfo - Payinfo
   */

  /**
   *
   * @typedef {Object} OrderRefundListRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} OrderReservation
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} consume_type_alias - Consume type alias
   * @property {string} take_meal_type_alias - Take meal type alias
   * @property {string} take_meal_status_alias - Take meal status alias
   * @property {string} meal_type_alias - Meal type alias
   * @property {string} unified_out_trade_no - Unified out trade no
   * @property {string} payer_name - Payer name
   * @property {string} payer_person_no - Payer person no
   * @property {string} payer_phone - Payer phone
   * @property {string} consumption_name - Consumption name
   * @property {string} payer_group_name - Payer group name
   * @property {string} payer_department_group_name - Payer department group name
   * @property {string} status - 状态
   * @property {string} name - 姓名
   * @property {string} card_no - 到账卡编号
   * @property {string} person_no - 到账人员编号
   * @property {string} phone - 手机号
   * @property {integer} wallet_fee - 储值钱包动账金额(分)
   * @property {integer} subsidy_fee - 赠送钱包动账金额(分)
   * @property {integer} complimentary_fee - 赠送钱包动账金额(分)
   * @property {integer} wallet_balance - 钱包余额(分)
   * @property {integer} subsidy_balance - 补贴钱包余额
   * @property {integer} complimentary_balance - 赠送钱包余额(分)
   * @property {string} trade_no - 系统订单号
   * @property {string} out_trade_no - 接入方订单号,第三方系统订单号
   * @property {string} provider_trade_no - 提供方交易流水号
   * @property {string} device_number - 设备编码
   * @property {string} remark - 备注
   * @property {string} face_url - 人脸图片
   * @property {integer} origin_fee - 订单原始金额，单位为分
   * @property {integer} pay_fee - 订单支付金额，单位为分
   * @property {integer} discount_fee - 订单优惠金额，单位为分
   * @property {string} order_status - 状态
   * @property {string} meal_type - 餐段
   * @property {object} food_records - 识别时的食物列表或碗碟列表(原始数据)
   * @property {string} payment_order_type - 消费订单类型
   * @property {string} user - 统一用户id
   * @property {integer} card_info - Card info
   * @property {integer} payer_group - 用户分组
   * @property {integer} payer_department_group - 用户部门
   * @property {integer} order - Order
   * @property {integer} company - Company
   * @property {integer} organization - 消费点组织
   * @property {integer} device - 外键一下那个设备的id
   * @property {integer} wallet - Wallet
   * @property {integer} payinfo - Payinfo
   * @property {integer} consume_rule - 消费规则
   */

  /**
   *
   * @typedef {Object} AddOrderUnifiedRes
   * @property {integer} id - Id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} order_status_alias - Order status alias
   * @property {string} pay_status_alias - Pay status alias
   * @property {string} flag_status_alias - Flag status alias
   * @property {string} settle_status_alias - Settle status alias
   * @property {string} order_type_alias - Order type alias
   * @property {string} pay_type_alias - Pay type alias
   * @property {string} out_trade_no - 接入方订单号
   * @property {string} trade_no - 系统订单号
   * @property {string} provider_trade_no - 第三方系统订单号
   * @property {integer} real_fee - 订单支付金额，单位为分
   * @property {integer} origin_fee - 订单总金额，单位为分
   * @property {string} order_type - 订单类型
   * @property {string} auth_code - 付款码/刷卡卡号
   * @property {integer} price - 支付金额
   * @property {integer} change_money - 找零金额
   * @property {integer} official_receipts - 找零金额
   * @property {array} order_payment -
   * @property {string} status - 状态
   * @property {string} name - 姓名
   * @property {string} card_no - 到账卡编号
   * @property {string} person_no - 到账人员编号
   * @property {string} phone - 手机号
   * @property {integer} discount_fee - 订单优惠金额，单位为分
   * @property {integer} rate_fee - 手续费金额，单位为分
   * @property {integer} net_fee - 剩余金额, 实收金额减退款金额
   * @property {integer} refund_count - 退款次数
   * @property {string} order_status - 订单状态
   * @property {integer} flag_status - 业务标志状态
   * @property {integer} settle_status - 对账状态
   * @property {integer} unified_order_type - 订单分类
   * @property {string} pay_method - 支付类型,可以理解为产品名称(如: 微信（反扫-刷卡支付）| 支付宝（反扫-刷卡支付） )
   * @property {integer} pay_source - 支付来源
   * @property {string} pay_time - 支付时间
   * @property {object} extra - 额外的不知名参数留底
   * @property {string} hook - 通知回调钩子
   * @property {object} attach - 回调附加信息
   * @property {boolean} receive_callback - 是否已收到回调
   * @property {string} callback_time - 回调时间
   * @property {string} remark - 备注
   * @property {string} user - 统一用户id
   * @property {integer} card_info - Card info
   * @property {integer} payer_group - 用户分组
   * @property {integer} payer_department_group - 用户部门
   * @property {integer} company - Company
   * @property {integer} organization - Organization
   * @property {integer} origin_order - Origin order
   * @property {integer} account - Account
   */

  /**
   *
   * @typedef {Object} ReservationListCollectOrderPaymentReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {integer} organization_id - 组织ID
   * @property {string} date_type - 时间类型
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} meal_type - 餐段
   */

  /**
   *
   * @typedef {Object} ReservationListFoodCollectOrderPaymentReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {integer} organization_id - 组织ID
   * @property {string} date_type - 时间类型
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} meal_type - 餐段
   * @property {string} food_name - 菜品名字
   */

  /**
   *
   * @typedef {Object} ReservationListGroupCollectOrderPaymentReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {integer} dep_id - 部门ID
   * @property {string} date_type - 时间类型
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} name - 姓名
   */

  /**
   *
   * @typedef {Object} ReservationListInfoOrderPaymentReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {integer} organization_id - 组织ID
   * @property {integer} user_group_id - 分组ID
   * @property {integer} consume_organization_id - 消费点ID
   * @property {string} consume_type - 支付方式
   * @property {string} meal_type - 餐段
   * @property {string} take_meal_status - 取餐状态
   * @property {string} name - 姓名
   * @property {string} person_no - 人员编号
   * @property {string} out_trade_no - 订单号
   * @property {string} unified_out_trade_no - 总单号
   * @property {string} date_type - 时间类型
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   */

  /**
   *
   * @typedef {Object} ReservationListInfoOrderPaymentRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ListOrderPaymentRspo
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} AddPaymentFoodRecordReq
   * @property {integer} id - ID
   * @property {integer} order_payment - Order payment
   * @property {integer} food_id - 菜品id
   * @property {string} name - 菜品名字
   * @property {string} spec - 规格
   * @property {string} taste - 口味
   * @property {integer} real_fee - 实际金额
   * @property {integer} raw_fee - 原金额
   * @property {integer} count - 份数
   * @property {string} food_status - 菜品交易状态
   * @property {object} food_extra - 菜品额外数据
   * @property {string} food_status_alias - Food status alias
   */

  /**
   *
   * @typedef {Object} PaymentFoodRecord
   * @property {integer} id - id
   * @property {integer} order_payment - Order payment
   * @property {integer} food_id - 菜品id
   * @property {string} name - 菜品名字
   * @property {string} spec - 规格
   * @property {string} taste - 口味
   * @property {integer} real_fee - 实际金额
   * @property {integer} raw_fee - 原金额
   * @property {integer} count - 份数
   * @property {string} food_status - 菜品交易状态
   * @property {object} food_extra - 菜品额外数据
   * @property {string} food_status_alias - Food status alias
   */

  /**
   *
   * @typedef {Object} ListPaymentFoodRecordReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListPaymentFoodRecordRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyPaymentFoodRecordReq
   * @property {integer} id - ID
   * @property {integer} order_payment - Order payment
   * @property {integer} food_id - 菜品id
   * @property {string} name - 菜品名字
   * @property {string} spec - 规格
   * @property {string} taste - 口味
   * @property {integer} real_fee - 实际金额
   * @property {integer} raw_fee - 原金额
   * @property {integer} count - 份数
   * @property {string} food_status - 菜品交易状态
   * @property {object} food_extra - 菜品额外数据
   * @property {string} food_status_alias - Food status alias
   */

  /**
   *
   * @typedef {Object} AddOrganizationInfoReqSer
   * @property {integer} id - Id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} name - 组织名
   * @property {integer} parent - 父ID
   * @property {string} status - 状态
   * @property {string} level_tag - 层级标签
   * @property {string} level_name - 层级组织名
   * @property {array} permission -
   * @property {boolean} face_on - 人脸支付开关
   * @property {boolean} refund_on - 支持退款开关
   * @property {string} refund_password - 退款密码
   * @property {boolean} store_wallet_on - 朴食储值卡务钱包开关
   * @property {boolean} electronic_wallet_on - 电子账户钱包开关
   * @property {boolean} subsidy_wallet_on - 补贴钱包开关
   * @property {boolean} discount_wallet_on - 优惠钱包开关
   * @property {boolean} other_wallet_on - 第三方钱包开关
   * @property {string} url - 公司官网
   * @property {string} district - 所在地区
   * @property {string} contact - 联系人
   * @property {string} mobile - 手机号码
   * @property {string} mail_address - 邮箱
   * @property {string} tel - 公司联系电话
   * @property {integer} industry - 行业性质
   * @property {string} remark - 备注
   * @property {string} appid - 微信公众号appid
   * @property {string} secret_key - 微信公众号secret_key
   * @property {string} auth_time - 认证时间
   * @property {boolean} is_third_interface - 是否开启第三方对接
   * @property {string} third_app_key - 应用
   * @property {string} third_secret_key - 第三方secret_key
   * @property {string} third_app_name - 第三方应用名称, 这个会放在点餐h5的应用名
   * @property {string} third_app_url - 第三方应用跳转的url
   * @property {string} third_app_callback_url - 第三方回调url
   * @property {string} sms_template_id - 短信模板ID
   */

  /**
   *
   * @typedef {Object} GetOrganizationCommonSettingsRsp
   * @property {integer} id - ID
   * @property {string} precharge_refund_type - 充值退款类型
   * @property {integer} precharge_refund_custom_day - 充值后自定义时间内退款
   * @property {boolean} consume_appeal_online_on - 线上申诉开关
   * @property {boolean} consume_appeal_offine_on - 线下申诉开关
   * @property {string} consume_appeal_type - 消费申诉类型
   * @property {integer} consume_appeal_custom_day - 自定义时间内申诉
   * @property {boolean} refund_on - 支持退款
   * @property {string} refund_password - 退款密码
   * @property {string} refund_type_online - 线上预约订单退款类型
   * @property {integer} refund_custom_time - 线上预约订单退款时间
   * @property {integer} refund_meal_time - 餐段时间内可退款
   * @property {} meal_time_settings -
   * @property {integer} limit_hour - Limit hour
   * @property {} parent_meal_time_settings -
   */

  /**
   *
   * @typedef {Object} ModifyOrganizationInfoReqSer
   * @property {integer} id - ID
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} name - 组织名
   * @property {integer} parent - 父ID
   * @property {string} status - 状态
   * @property {string} level_tag - 层级标签
   * @property {string} level_name - 层级组织名
   * @property {boolean} face_on - 人脸支付开关
   * @property {boolean} refund_on - 支持退款开关
   * @property {string} refund_password - 退款密码
   * @property {boolean} store_wallet_on - 朴食储值卡务钱包开关
   * @property {boolean} electronic_wallet_on - 电子账户钱包开关
   * @property {boolean} subsidy_wallet_on - 补贴钱包开关
   * @property {boolean} discount_wallet_on - 优惠钱包开关
   * @property {boolean} other_wallet_on - 第三方钱包开关
   * @property {string} url - 公司官网
   * @property {string} district - 所在地区
   * @property {string} contact - 联系人
   * @property {string} mobile - 手机号码
   * @property {string} mail_address - 邮箱
   * @property {string} tel - 公司联系电话
   * @property {integer} industry - 行业性质
   * @property {string} remark - 备注
   * @property {object} permission - 初始商户权限权限JSON列表
   * @property {string} appid - 微信公众号appid
   * @property {string} secret_key - 微信公众号secret_key
   * @property {string} auth_time - 认证时间
   * @property {boolean} is_third_interface - 是否开启第三方对接
   * @property {string} third_app_key - 应用
   * @property {string} third_secret_key - 第三方secret_key
   * @property {string} third_app_name - 第三方应用名称, 这个会放在点餐h5的应用名
   * @property {string} third_app_url - 第三方应用跳转的url
   * @property {string} third_app_callback_url - 第三方回调url
   * @property {string} sms_template_id - 短信模板ID
   */

  /**
   *
   * @typedef {Object} ModifyOrganizationCommonSettingsRsp
   * @property {integer} id - ID
   * @property {string} precharge_refund_type - 充值退款类型
   * @property {integer} precharge_refund_custom_day - 充值后自定义时间内退款
   * @property {boolean} consume_appeal_online_on - 线上申诉开关
   * @property {boolean} consume_appeal_offine_on - 线下申诉开关
   * @property {string} consume_appeal_type - 消费申诉类型
   * @property {integer} consume_appeal_custom_day - 自定义时间内申诉
   * @property {boolean} refund_on - 支持退款
   * @property {string} refund_password - 退款密码
   * @property {string} refund_type_online - 线上预约订单退款类型
   * @property {integer} refund_custom_time - 线上预约订单退款时间
   * @property {integer} refund_meal_time - 餐段时间内可退款
   * @property {} meal_time_settings -
   * @property {integer} limit_hour - Limit hour
   */

  /**
   *
   * @typedef {Object} OrganizationCommonSettings
   * @property {integer} id - ID
   * @property {string} precharge_refund_type - 充值退款类型
   * @property {integer} precharge_refund_custom_day - 充值后自定义时间内退款
   * @property {boolean} consume_appeal_online_on - 线上申诉开关
   * @property {boolean} consume_appeal_offine_on - 线下申诉开关
   * @property {string} consume_appeal_type - 消费申诉类型
   * @property {integer} consume_appeal_custom_day - 自定义时间内申诉
   * @property {boolean} refund_on - 支持退款
   * @property {string} refund_password - 退款密码
   * @property {string} refund_type_online - 线上预约订单退款类型
   * @property {integer} refund_custom_time - 线上预约订单退款时间
   * @property {integer} refund_meal_time - 餐段时间内可退款
   * @property {} meal_time_settings -
   * @property {integer} limit_hour - Limit hour
   */

  /**
   *
   * @typedef {Object} DeviceConsumeReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} device_name - 设备名
   * @property {string} device_number - 设备编号
   * @property {string} device_type - 设备类型
   * @property {array} org_ids -
   */

  /**
   *
   * @typedef {Object} DeviceConsume
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   * @property {string} device_id - 设备编号
   * @property {string} device_name - 设备名
   * @property {string} device_type - 设备类型
   * @property {string} device_total_fee - 设备流水总和
   * @property {string} device_consume_count - 设备消费笔数
   */

  /**
   *
   * @typedef {Object} DeviceConsumeRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ConsumeDetailReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} person_no - 人员编号
   * @property {string} name - 姓名
   * @property {string} phone - 手机号
   * @property {array} org_ids -
   * @property {string} trade_no - 订单号
   * @property {string} card_no - 卡号
   * @property {array} payer_group_ids -
   * @property {array} payer_department_group_ids -
   * @property {integer} operate_type - 操作类型
   * @property {string} payway - 支付方式
   * @property {string} sub_payway - 支付类型
   * @property {string} meal_type - 餐段
   * @property {string} device_number - 交易设备
   * @property {string} wallet - 扣款钱包
   * @property {integer} only_revenue - 是否只查看营收，1是，0不是
   */

  /**
   *
   * @typedef {Object} ConsumeDetail
   * @property {string} trade_no - 订单号
   * @property {string} out_trade_no - 第三方订单号
   * @property {string} create_time - 创建时间
   * @property {string} pay_time - 支付时间
   * @property {string} primary - 集团
   * @property {string} secondary - 地区
   * @property {string} company - 门店
   * @property {string} canteen - 食堂
   * @property {string} area - 食堂区域
   * @property {string} stall - 消费点
   * @property {string} payer_name - 姓名
   * @property {string} payer_phone - 手机号
   * @property {string} payer_person_no - 人员编号
   * @property {string} payer_card_no - 卡号
   * @property {string} payer_group - 分组
   * @property {string} payer_department_group - 部门
   * @property {string} wallet - 扣款钱包
   * @property {string} payway - 支付方式
   * @property {string} sub_payway - 支付类型
   * @property {integer} total_fee - 订单金额
   * @property {integer} settlement_fee - 动账金额
   * @property {integer} discount - 优惠
   * @property {integer} discount_fee - 优惠金额
   * @property {integer} discount_type - 优惠类型
   * @property {string} order_type - 操作类型
   * @property {string} wallet_money - 扣款钱包余额
   * @property {string} meal_type - 消费餐段
   * @property {string} device_id - 交易设备
   * @property {string} controller - 操作员
   */

  /**
   *
   * @typedef {Object} ConsumeDetailRspSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {array} result -
   */

  /**
   *
   * @typedef {Object} PechargeRecordReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} person_no - 人员编号
   * @property {string} name - 姓名
   * @property {string} phone - 手机号
   * @property {array} org_ids -
   * @property {array} pay_group_ids -
   * @property {array} pay_department_group_ids -
   * @property {string} recharge_method - 充值方式
   */

  /**
   *
   * @typedef {Object} PechargeRecord
   * @property {string} trade_no - 订单编号
   * @property {string} pay_time - 充值时间
   * @property {string} finish_time - 到账时间
   * @property {string} name - 姓名
   * @property {string} mobile - 手机号
   * @property {string} person_no - 人员编号
   * @property {string} payer_group - 分组名字
   * @property {string} payer_department_group - 部门名字
   * @property {string} recharge_type - 充值类型
   * @property {string} recharge_method - 充值方式
   * @property {integer} origin_fee - 原充值金额
   * @property {string} recharge_wallet - 充值钱包
   * @property {integer} fee - 到账金额
   * @property {string} presented_wallet - 赠送钱包
   * @property {integer} presented_fee - 赠送钱包到账金额
   * @property {string} attach - 备注
   * @property {string} order_source - 订单来源
   * @property {string} out_trade_no - 第三方订单号
   * @property {string} provider_trade_no - 订单流水号
   */

  /**
   *
   * @typedef {Object} ReconciliationReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {array} org_ids -
   */

  /**
   *
   * @typedef {Object} Reconciliation
   * @property {string} canteen - 食堂
   * @property {string} stall - 消费点
   * @property {integer} consume_total - 消费总额
   * @property {integer} consume_count - 消费笔数
   * @property {integer} pushi_wallet_pay - 朴食钱包消费
   * @property {integer} pushi_wallet_refund - 朴食钱包退款
   * @property {integer} electronic_wallet_pay - 电子账户消费
   * @property {integer} electronic_wallet_refund - 电子账户退款
   * @property {integer} subsidy_wallet_pay - 补贴钱包消费
   * @property {integer} subsidy_wallet_refund - 补贴钱包退款
   * @property {integer} third_wallet_pay - 第三方钱包消费
   * @property {integer} third_wallet_refund - 第三方钱包退款
   * @property {integer} complimentary_wallet_pay - 赠送钱包消费
   * @property {integer} complimentary_wallet_refund - 赠送钱包退款
   * @property {integer} qr_code_pay - 收款码消费
   * @property {integer} mobile_payment - 移动支付消费
   * @property {integer} discount_fee - 优惠金额
   * @property {integer} metering - 计次消费
   */

  /**
   *
   * @typedef {Object} ReconciliationRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} UnifiedReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} person_no - 人员编号
   * @property {string} name - 姓名
   * @property {string} phone - 手机号
   * @property {array} org_ids -
   * @property {string} trade_no - 订单号
   * @property {string} card_no - 卡号
   * @property {array} payer_group_ids -
   * @property {array} payer_department_group_ids -
   * @property {integer} operate_type - 操作类型
   * @property {string} payway - 支付方式
   * @property {string} sub_payway - 支付类型
   * @property {string} meal_type - 餐段
   * @property {string} device_number - 交易设备
   * @property {string} wallet - 扣款钱包
   */

  /**
   *
   * @typedef {Object} Unified
   * @property {string} trade_no - 订单号
   * @property {string} out_trade_no - 第三方订单号
   * @property {string} create_time - 创建时间
   * @property {string} pay_time - 支付时间
   * @property {string} primary - 集团
   * @property {string} secondary - 地区
   * @property {string} company - 门店
   * @property {string} canteen - 食堂
   * @property {string} area - 食堂区域
   * @property {string} stall - 消费点
   * @property {string} payer_name - 姓名
   * @property {string} payer_phone - 手机号
   * @property {string} payer_person_no - 人员编号
   * @property {string} payer_card_no - 卡号
   * @property {string} payer_group - 分组
   * @property {string} payer_department_group - 部门
   * @property {string} wallet - 扣款钱包
   * @property {string} payway - 支付方式
   * @property {string} sub_payway - 支付类型
   * @property {integer} total_fee - 订单金额
   * @property {integer} settlement_fee - 动账金额
   * @property {integer} discount - 优惠
   * @property {integer} discount_fee - 优惠金额
   * @property {integer} discount_type - 优惠类型
   * @property {string} order_type - 操作类型
   * @property {string} wallet_money - 扣款钱包余额
   * @property {string} meal_type - 消费餐段
   * @property {string} device_number - 交易设备
   * @property {string} controller - 操作员
   */

  /**
   *
   * @typedef {Object} UnifiedRspSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {array} result -
   */

  /**
   *
   * @typedef {Object} AddBackgroundReservationSettingsReq
   * @property {string} meal_type_detail - Meal type detail
   * @property {integer} id - Id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {array} consume_organizations_alias -
   * @property {array} card_user_groups_alias -
   * @property {string} organization_alias - Organization alias
   * @property {string} status_alias - Status alias
   * @property {string} menu_type_alias - Menu type alias
   * @property {string} consume_type_alias - Consume type alias
   * @property {string} refund_remit_type_alias - Refund remit type alias
   * @property {string} status - 状态
   * @property {integer} limit_count - 单餐限制份数
   * @property {string} menu_type - 菜单类型
   * @property {boolean} is_next - 是否允许预约下个月/周
   * @property {integer} can_reservation_days - 可预约天数
   * @property {boolean} is_open - 是否启用
   * @property {integer} breakfast_ahead - 餐段结束前(早餐)
   * @property {integer} lunch_ahead - 餐段结束前(午餐)
   * @property {integer} hit_tea_ahead - 餐段结束前(下午茶)
   * @property {integer} dinner_ahead - 餐段结束前(晚餐)
   * @property {integer} midnight_ahead - 餐段结束前(夜宵)
   * @property {integer} early_ahead - 餐段结束前(凌晨餐)
   * @property {string} consume_type - 扣费方式
   * @property {object} take_out_type - 取餐方式
   * @property {integer} waimai_fee - 外卖费
   * @property {integer} fuwu_fee - 服务费
   * @property {boolean} is_fixed_meal - 是否固定单餐金额
   * @property {integer} breakfast_fixed - 固定单餐金额(早餐)
   * @property {integer} lunch_fixed - 固定单餐金额(午餐)
   * @property {integer} hit_tea_fixed - 固定单餐金额(下午茶)
   * @property {integer} dinner_fixed - 固定单餐金额(晚餐)
   * @property {integer} midnight_fixed - 固定单餐金额(夜宵)
   * @property {integer} early_fixed - 固定单餐金额(凌晨餐)
   * @property {boolean} can_refund - 是否支持取消
   * @property {string} refund_remit_type - 取消类型
   * @property {integer} refund_remit_time - 取消(小时)
   * @property {integer} organization - Organization
   * @property {array} card_user_groups -
   * @property {array} consume_organizations -
   * @property {array} meal_types -
   */

  /**
   *
   * @typedef {Object} BackgroundReservationSettings
   * @property {string} meal_type_detail - Meal type detail
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {array} consume_organizations_alias -
   * @property {array} card_user_groups_alias -
   * @property {string} organization_alias - Organization alias
   * @property {string} status_alias - Status alias
   * @property {string} menu_type_alias - Menu type alias
   * @property {string} consume_type_alias - Consume type alias
   * @property {string} refund_remit_type_alias - Refund remit type alias
   * @property {string} status - 状态
   * @property {integer} limit_count - 单餐限制份数
   * @property {string} menu_type - 菜单类型
   * @property {boolean} is_next - 是否允许预约下个月/周
   * @property {integer} can_reservation_days - 可预约天数
   * @property {boolean} is_open - 是否启用
   * @property {integer} breakfast_ahead - 餐段结束前(早餐)
   * @property {integer} lunch_ahead - 餐段结束前(午餐)
   * @property {integer} hit_tea_ahead - 餐段结束前(下午茶)
   * @property {integer} dinner_ahead - 餐段结束前(晚餐)
   * @property {integer} midnight_ahead - 餐段结束前(夜宵)
   * @property {integer} early_ahead - 餐段结束前(凌晨餐)
   * @property {string} consume_type - 扣费方式
   * @property {object} take_out_type - 取餐方式
   * @property {integer} waimai_fee - 外卖费
   * @property {integer} fuwu_fee - 服务费
   * @property {boolean} is_fixed_meal - 是否固定单餐金额
   * @property {integer} breakfast_fixed - 固定单餐金额(早餐)
   * @property {integer} lunch_fixed - 固定单餐金额(午餐)
   * @property {integer} hit_tea_fixed - 固定单餐金额(下午茶)
   * @property {integer} dinner_fixed - 固定单餐金额(晚餐)
   * @property {integer} midnight_fixed - 固定单餐金额(夜宵)
   * @property {integer} early_fixed - 固定单餐金额(凌晨餐)
   * @property {boolean} can_refund - 是否支持取消
   * @property {string} refund_remit_type - 取消类型
   * @property {integer} refund_remit_time - 取消(小时)
   * @property {integer} organization - Organization
   * @property {array} card_user_groups -
   * @property {array} consume_organizations -
   * @property {array} meal_types -
   */

  /**
   *
   * @typedef {Object} FilterReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {array} consume_organizations -
   * @property {array} card_user_groups -
   */

  /**
   *
   * @typedef {Object} ListBackgroundReservationSettingsRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ListBackgroundReservationSettingsReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ModifyBackgroundReservationSettingsReq
   * @property {string} meal_type_detail - Meal type detail
   * @property {integer} id - ID
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {array} consume_organizations_alias -
   * @property {array} card_user_groups_alias -
   * @property {string} organization_alias - Organization alias
   * @property {string} status_alias - Status alias
   * @property {string} menu_type_alias - Menu type alias
   * @property {string} consume_type_alias - Consume type alias
   * @property {string} refund_remit_type_alias - Refund remit type alias
   * @property {string} status - 状态
   * @property {integer} limit_count - 单餐限制份数
   * @property {string} menu_type - 菜单类型
   * @property {boolean} is_next - 是否允许预约下个月/周
   * @property {integer} can_reservation_days - 可预约天数
   * @property {boolean} is_open - 是否启用
   * @property {integer} breakfast_ahead - 餐段结束前(早餐)
   * @property {integer} lunch_ahead - 餐段结束前(午餐)
   * @property {integer} hit_tea_ahead - 餐段结束前(下午茶)
   * @property {integer} dinner_ahead - 餐段结束前(晚餐)
   * @property {integer} midnight_ahead - 餐段结束前(夜宵)
   * @property {integer} early_ahead - 餐段结束前(凌晨餐)
   * @property {string} consume_type - 扣费方式
   * @property {object} take_out_type - 取餐方式
   * @property {integer} waimai_fee - 外卖费
   * @property {integer} fuwu_fee - 服务费
   * @property {boolean} is_fixed_meal - 是否固定单餐金额
   * @property {integer} breakfast_fixed - 固定单餐金额(早餐)
   * @property {integer} lunch_fixed - 固定单餐金额(午餐)
   * @property {integer} hit_tea_fixed - 固定单餐金额(下午茶)
   * @property {integer} dinner_fixed - 固定单餐金额(晚餐)
   * @property {integer} midnight_fixed - 固定单餐金额(夜宵)
   * @property {integer} early_fixed - 固定单餐金额(凌晨餐)
   * @property {boolean} can_refund - 是否支持取消
   * @property {string} refund_remit_type - 取消类型
   * @property {integer} refund_remit_time - 取消(小时)
   * @property {integer} organization - Organization
   * @property {array} card_user_groups -
   * @property {array} consume_organizations -
   * @property {array} meal_types -
   */

  /**
   *
   * @typedef {Object} ModifyBackgroundReservationSettingsOpenReq
   * @property {integer} id - ID
   * @property {boolean} is_open - Is open
   */

  /**
   *
   * @typedef {Object} H5Login
   * @property {string} appid - appid
   * @property {integer} company_id - 公司
   */

  /**
   *
   * @typedef {Object} PhoneLoginReqSer
   * @property {string} phone - 手机号
   * @property {string} code - 验证码
   */

  /**
   *
   * @typedef {Object} UserLoginRspSer
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} user_id - 用户唯一ID
   * @property {array} company_names -
   * @property {string} source_alias - 来源
   * @property {string} nickname - 微信昵称
   * @property {string} phone - 手机号码
   * @property {string} wechat_img_url - 微信默认头像url
   * @property {string} depart_name - 部门信息
   * @property {string} role_type - 角色类型
   * @property {integer} status - 状态
   * @property {string} source - 来源渠道
   * @property {integer} gender - 性别
   * @property {string} province - 所属地区
   * @property {string} city - 所属城市
   * @property {string} province_code - 所属地区（列表）
   * @property {string} appid - Appid
   * @property {string} openid - Openid
   * @property {string} unionid - 微信unionid
   * @property {object} extra_tags - 额外标签信息
   * @property {object} extra_user_detail - 额外用户信息
   * @property {string} headimgurl - 微信图片路径
   * @property {integer} role - 用户角色分组
   * @property {array} company -
   */

  /**
   *
   * @typedef {Object} ShopcardAdd
   * @property {string} payment_order_type - 消费订单类型
   * @property {string} take_meal_type - 取餐类型
   * @property {string} date - 选择日期
   * @property {string} meal_type - 餐段类型
   * @property {integer} organization_id - 消费点ID
   * @property {integer} organization_alias - 消费点名字
   * @property {integer} fuwu_fee - 服务费
   * @property {integer} waimai_fee - 外卖费
   * @property {integer} food_id - 菜品ID
   * @property {integer} spec_id - 规格ID
   * @property {integer} taste_id - 口味ID
   */

  /**
   *
   * @typedef {Object} Shopcard
   * @property {string} payment_order_type - 消费订单类型
   * @property {string} take_meal_type - 取餐类型
   */

  /**
   *
   * @typedef {Object} ShopcardClean
   * @property {boolean} clean_all - 清零
   */

  /**
   *
   * @typedef {Object} PhoneReqSer
   * @property {string} phone - 手机
   */

  /**
   *
   * @typedef {Object} UserBindORCreateReqSer
   * @property {integer} company_id - 所属食堂
   * @property {string} person_no - 用户编号
   * @property {string} name - 姓名
   * @property {string} face_img_url - 人脸
   */

  /**
   *
   * @typedef {Object} SaveCardInfoORWechatUserInfoReqSer
   * @property {integer} company_id - 所属食堂
   * @property {string} user_id - 用户id
   * @property {string} person_no - 用户编号
   * @property {string} gender - 性别
   * @property {object} extra_tags - 额外标签信息
   * @property {object} extra_user_detail - 额外用户信息
   */

  /**
   *
   * @typedef {Object} MonthCollectReservationPageReqSer
   * @property {integer} company_id - 所属食堂
   * @property {string} user_id - 用户id
   * @property {string} person_no - 用户编号
   * @property {integer} page - 查询的页码
   * @property {integer} page_size - 显示条目数,默认100
   */

  /**
   *
   * @typedef {Object} StallListReqSer
   * @property {integer} company_id - 所属食堂
   * @property {string} user_id - 用户id
   * @property {string} date - 选择日期
   * @property {string} person_no - 用户编号
   * @property {string} take_meal_type - 取餐类型
   * @property {integer} organization_id - 食堂ID
   * @property {string} meal_type - 餐段类型
   */

  /**
   *
   * @typedef {Object} MonthReservationInfoPageReqSer
   * @property {integer} company_id - 所属食堂
   * @property {string} user_id - 用户id
   * @property {string} person_no - 用户编号
   * @property {string} month - 月份
   * @property {string} unified_trade_no - 总订单号
   * @property {integer} page - 查询的页码
   * @property {integer} page_size - 显示条目数,默认100
   */

  /**
   *
   * @typedef {Object} ComapanyReqSer
   * @property {integer} company_id - 所属食堂
   * @property {string} user_id - 用户id
   */

  /**
   *
   * @typedef {Object} ReservationOrderAddReqSer
   * @property {integer} company_id - 所属食堂
   * @property {string} user_id - 用户id
   * @property {string} person_no - 用户编号
   * @property {string} name - 收货姓名
   * @property {string} phone - 收货电话
   * @property {string} adders - 收货地址
   * @property {string} take_meal_type - 取餐类型
   */

  /**
   *
   * @typedef {Object} WechatAuthReqSer
   * @property {string} code - 登录凭证
   */

  /**
   *
   * @typedef {Object} User
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} user_id - 用户唯一ID
   * @property {array} company_names -
   * @property {string} source_alias - 来源
   * @property {string} nickname - 微信昵称
   * @property {string} phone - 手机号码
   * @property {string} wechat_img_url - 微信默认头像url
   * @property {string} depart_name - 部门信息
   * @property {string} role_type - 角色类型
   * @property {integer} status - 状态
   * @property {string} source - 来源渠道
   * @property {integer} gender - 性别
   * @property {string} province - 所属地区
   * @property {string} city - 所属城市
   * @property {string} province_code - 所属地区（列表）
   * @property {string} appid - Appid
   * @property {string} openid - Openid
   * @property {string} unionid - 微信unionid
   * @property {object} extra_tags - 额外标签信息
   * @property {object} extra_user_detail - 额外用户信息
   * @property {string} headimgurl - 微信图片路径
   * @property {integer} role - 用户角色分组
   * @property {array} company -
   */

  /**
   *
   * @typedef {Object} AddCardDepartmentGroup
   * @property {integer} id - Id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} parent - 上级ID
   * @property {integer} company - 所属项目点公司
   * @property {string} group_id - 组ID
   * @property {string} group_name - 分组名称
   * @property {integer} level - Level
   * @property {string} status_alias - Status alias
   * @property {integer} card_counts - 用户人数
   * @property {string} has_children - Has children
   * @property {array} children_list -
   * @property {integer} organization - Organization
   */

  /**
   *
   * @typedef {Object} CardDepartmentGroup
   * @property {integer} id - id
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} parent - 上级ID
   * @property {integer} company - 所属项目点公司
   * @property {string} group_id - 部门编号
   * @property {string} group_name - 分组名称
   * @property {integer} level - Level
   * @property {string} status_alias - Status alias
   * @property {integer} card_counts - 用户人数
   * @property {string} has_children - Has children
   * @property {array} children_list -
   * @property {integer} organization - Organization
   */

  /**
   *
   * @typedef {Object} BatchImportGroupReqSer
   * @property {string} url - 上传的xls地址
   */

  /**
   *
   * @typedef {Object} ListCardDepartmentGroupReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   */

  /**
   *
   * @typedef {Object} ListCardDepartmentGroupRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyCardDepartmentGroup
   * @property {integer} id - ID
   * @property {string} status - 状态
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} parent - 上级ID
   * @property {integer} company - 所属项目点公司
   * @property {string} group_id - 部门编号
   * @property {string} group_name - 分组名称
   * @property {integer} level - Level
   * @property {string} status_alias - Status alias
   * @property {integer} card_counts - 用户人数
   * @property {string} has_children - Has children
   * @property {array} children_list -
   * @property {integer} organization - Organization
   */

  /**
   *
   * @typedef {Object} OperationCategoryTreeListSer
   * @property {string} key - 键
   * @property {string} name - 名称
   * @property {string} parent - 父级键
   * @property {array} children -
   */

  /**
   *
   * @typedef {Object} CardOperationLogListReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} operator - 操作人
   * @property {string} operation - 操作类型
   * @property {string} start_date - 开始日期
   * @property {string} end_date - 结束日期
   */

  /**
   *
   * @typedef {Object} CardOperationLogSer
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} operator_name - 操作人名
   * @property {string} role_name - 角色名
   * @property {string} category_alias - 操作菜单
   * @property {string} detail - 操作内容
   * @property {string} operation_alias - 操作类型别名
   * @property {string} operation_time - 操作时间
   * @property {string} category - 操作类别
   * @property {string} operation - 操作类型
   * @property {integer} company - Company
   * @property {integer} operator - Operator
   * @property {integer} organization - Organization
   */

  /**
   *
   * @typedef {Object} CardOperationLogListRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} AssignOrgReqSer
   * @property {array} org_ids -
   * @property {array} card_ids -
   */

  /**
   *
   * @typedef {Object} CardUserIdsReqSer
   * @property {array} card_user_ids -
   */

  /**
   *
   * @typedef {Object} ImportReqSer
   * @property {string} url - 导入文件地址
   */

  /**
   *
   * @typedef {Object} BatchRechargeReqSer
   * @property {array} card_user_ids -
   * @property {integer} money - 金额
   */

  /**
   *
   * @typedef {Object} CardUserIdReqSer
   * @property {integer} card_user_id - 卡用户ID
   */

  /**
   *
   * @typedef {Object} ChangeReqSer
   * @property {integer} card_user_id - 卡用户ID
   * @property {string} card_no - 卡号
   * @property {boolean} is_open_card - 是否开卡
   * @property {string} pay_method - 支付方式
   */

  /**
   *
   * @typedef {Object} ChargeReqSer
   * @property {integer} card_user_id - 卡用户ID
   * @property {integer} money - 金额
   * @property {integer} subsidy_balance - 补贴金额
   */

  /**
   *
   * @typedef {Object} PersonQuitReqSer
   * @property {integer} card_user_id - 卡用户ID
   * @property {boolean} is_withdrawal - 是否取款
   */

  /**
   *
   * @typedef {Object} PublishReqSer
   * @property {integer} card_user_id - 卡用户ID
   * @property {string} card_no - 卡号
   * @property {array} card_user_group_ids -
   * @property {string} pay_method - 支付方式
   * @property {integer} money - 开卡收取金额
   * @property {integer} change_money - 找零金额
   */

  /**
   *
   * @typedef {Object} RechargeReqSer
   * @property {integer} card_user_id - 卡用户ID
   * @property {integer} money - 挂失金额
   * @property {integer} wallet_id - 钱包id
   * @property {integer} pay_info_id - 支付信息ID
   */

  /**
   *
   * @typedef {Object} CardUserRefundReqSer
   * @property {integer} card_user_id - 卡用户ID
   * @property {string} order_id - 订单ID
   * @property {string} refund_type - 退款类型
   * @property {integer} refund_fee - 退款金额
   */

  /**
   *
   * @typedef {Object} WithDrawalReqSer
   * @property {integer} card_user_id - 卡用户ID
   * @property {integer} money - 挂失金额
   * @property {string} pay_method - 支付方式
   * @property {integer} wallet_id - 钱包id
   */

  /**
   *
   * @typedef {Object} CardUserSubsidySer
   * @property {string} person_no - 人员编号
   * @property {integer} subsidy_balance - 补贴余额
   */

  /**
   *
   * @typedef {Object} AddSubsidyReqSer
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} clear_time - 单次清除日期
   * @property {string} subsidy_type_alias - 补贴类型
   * @property {string} subsidy_status_alais - 补贴状态
   * @property {integer} card_counts - 补贴人数
   * @property {integer} total_subsidy_money - 补贴总金额
   * @property {string} clear_time_alias - 清零时间
   * @property {boolean} is_valid - 是否生效
   * @property {string} name - 补贴名称
   * @property {boolean} now_release - 是否立即发放
   * @property {array} user_list -
   * @property {string} status - 状态
   * @property {string} subsidy_id - 补贴编号
   * @property {string} subsidy_status - 补贴状态
   * @property {string} subsidy_type - 补贴类型
   * @property {integer} money - 补贴金额
   * @property {boolean} is_refresh - 是否清零
   * @property {integer} release_day - 发放日期
   * @property {integer} clear_day - 清除日期
   * @property {string} operate_name - 操作人员
   * @property {integer} total_money - 补贴发放总金额
   * @property {boolean} is_pass_saturday - 跳过星期六
   * @property {boolean} is_pass_sunday - 跳过星期日
   * @property {boolean} is_pass_holiday - 跳过节假日
   * @property {integer} organization - Organization
   * @property {integer} account - Account
   */

  /**
   *
   * @typedef {Object} ClearUserSubsidyReqSer
   * @property {array} person_no_list -
   */

  /**
   *
   * @typedef {Object} CardOrgRelatedSer
   * @property {integer} card_info - Card info
   * @property {integer} organization - Organization
   * @property {string} person_status - 会员状态
   * @property {string} person_quit_time - 退户时间
   */

  /**
   *
   * @typedef {Object} CardSubsidyUserInfo
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} company_name - 所属企业
   * @property {string} company_id - 所属企业ID
   * @property {string} gender_alias - 性别
   * @property {array} organization_alias -
   * @property {string} card_department_group_alias - 部门分组
   * @property {string} account_status_alias - 账户状态
   * @property {string} card_status_alias - 卡状态
   * @property {string} person_status_alias - 用户状态
   * @property {string} person_quit_time - 退户时间
   * @property {string} loss_time - 挂失时间
   * @property {string} delay_flat_cost_alias - 扣费状态别名
   * @property {string} card_source_alias - 发卡来源别名
   * @property {array} card_orgs -
   * @property {string} status - 状态
   * @property {string} person_no - 人员编号
   * @property {string} card_no - 卡号
   * @property {string} name - 会员名
   * @property {string} card_status - 卡状态
   * @property {integer} balance - 储值钱包余额
   * @property {integer} subsidy_balance - 补贴钱包余额
   * @property {integer} complimentary_balance - 赠送钱包余额
   * @property {integer} total_consume_fee - 总消费金额
   * @property {integer} total_rechange_fee - 总充值金额
   * @property {integer} total_draw_fee - 钱区取款
   * @property {integer} total_refund_fee - 钱区退款
   * @property {string} gender - 性别
   * @property {string} phone - 手机号
   * @property {string} person_status - 会员状态
   * @property {integer} delay_flat_cost - 是否待扣工本费
   * @property {integer} flat_cost_fee - 工本费/补卡费
   * @property {integer} card_source - 发卡来源
   * @property {string} face_url - 人脸url
   * @property {string} face_user_id - 人脸用户id
   * @property {boolean} facepay - 人脸支付开启
   * @property {string} publish_time - 发卡时间
   * @property {string} effective_time - 卡务生效时间
   * @property {string} expiration_time - 卡务失效时间
   * @property {boolean} allow_custom_wallet_order - 是否允许自定义钱包顺序
   * @property {object} extra_tags - 额外标签信息
   * @property {object} extra_user_detail - 额外用户信息
   * @property {integer} card_department_group - 部门分组
   */

  /**
   *
   * @typedef {Object} GetCardSubsidyRspSer
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} clear_time - 单次清除日期
   * @property {string} subsidy_type_alias - 补贴类型
   * @property {string} subsidy_status_alais - 补贴状态
   * @property {integer} card_counts - 补贴人数
   * @property {integer} total_subsidy_money - 补贴总金额
   * @property {string} clear_time_alias - 清零时间
   * @property {boolean} is_valid - 是否生效
   * @property {array} card_user_list -
   * @property {string} status - 状态
   * @property {string} subsidy_id - 补贴编号
   * @property {string} name - 补贴名
   * @property {string} subsidy_status - 补贴状态
   * @property {string} subsidy_type - 补贴类型
   * @property {integer} money - 补贴金额
   * @property {boolean} is_refresh - 是否清零
   * @property {integer} release_day - 发放日期
   * @property {integer} clear_day - 清除日期
   * @property {string} operate_name - 操作人员
   * @property {integer} total_money - 补贴发放总金额
   * @property {boolean} is_pass_saturday - 跳过星期六
   * @property {boolean} is_pass_sunday - 跳过星期日
   * @property {boolean} is_pass_holiday - 跳过节假日
   * @property {integer} organization - Organization
   * @property {integer} account - Account
   */

  /**
   *
   * @typedef {Object} CardSubsidyInfoListReqSer
   * @property {integer} id - ID
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {integer} year - 年
   * @property {integer} month - 月
   * @property {integer} card_department_group_id - 部门ID
   * @property {string} person_no - 人员编号
   */

  /**
   *
   * @typedef {Object} SubsidyRecord
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} release_status_alias -
   * @property {string} status - 状态
   * @property {string} operate_name - 发放员
   * @property {string} payer_name - 支付人
   * @property {string} payer_card_no - 支付人卡号
   * @property {string} payer_person_no - 支付人编号
   * @property {string} payer_group - 支付人部门
   * @property {integer} subsidy_money - 补贴金额
   * @property {string} release_status - 发放状态
   * @property {string} release_time - 清零时间
   * @property {string} remark - 备注
   * @property {integer} company - Company
   * @property {integer} card_subsidy - Card subsidy
   * @property {integer} card_record - Card record
   */

  /**
   *
   * @typedef {Object} CardSubsidyInfoListRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   * @property {integer} success_money - 合计发放补贴
   * @property {integer} fail_counts - 失败人数
   */

  /**
   *
   * @typedef {Object} CardSubsidy
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} clear_time - 单次清除日期
   * @property {string} subsidy_type_alias - 补贴类型
   * @property {string} subsidy_status_alais - 补贴状态
   * @property {integer} card_counts - 补贴人数
   * @property {integer} total_subsidy_money - 补贴总金额
   * @property {string} clear_time_alias - 清零时间
   * @property {boolean} is_valid - 是否生效
   * @property {string} status - 状态
   * @property {string} subsidy_id - 补贴编号
   * @property {string} name - 补贴名
   * @property {string} subsidy_status - 补贴状态
   * @property {string} subsidy_type - 补贴类型
   * @property {integer} money - 补贴金额
   * @property {boolean} is_refresh - 是否清零
   * @property {integer} release_day - 发放日期
   * @property {integer} clear_day - 清除日期
   * @property {string} operate_name - 操作人员
   * @property {integer} total_money - 补贴发放总金额
   * @property {boolean} is_pass_saturday - 跳过星期六
   * @property {boolean} is_pass_sunday - 跳过星期日
   * @property {boolean} is_pass_holiday - 跳过节假日
   * @property {integer} organization - Organization
   * @property {integer} account - Account
   */

  /**
   *
   * @typedef {Object} CardSubsidyListRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} CardSubsidyListReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {integer} year - 年
   * @property {integer} month - 月
   * @property {string} subsidy_status - 补贴状态
   * @property {string} name - 补贴名
   */

  /**
   *
   * @typedef {Object} ModifySubsidyReqSer
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} clear_time - 单次清除日期
   * @property {string} subsidy_type_alias - 补贴类型
   * @property {string} subsidy_status_alais - 补贴状态
   * @property {integer} card_counts - 补贴人数
   * @property {integer} total_subsidy_money - 补贴总金额
   * @property {string} clear_time_alias - 清零时间
   * @property {boolean} is_valid - 是否生效
   * @property {string} name - 补贴名称
   * @property {boolean} now_release - 是否立即发放
   * @property {array} user_list -
   * @property {string} status - 状态
   * @property {string} subsidy_id - 补贴编号
   * @property {string} subsidy_status - 补贴状态
   * @property {string} subsidy_type - 补贴类型
   * @property {integer} money - 补贴金额
   * @property {boolean} is_refresh - 是否清零
   * @property {integer} release_day - 发放日期
   * @property {integer} clear_day - 清除日期
   * @property {string} operate_name - 操作人员
   * @property {integer} total_money - 补贴发放总金额
   * @property {boolean} is_pass_saturday - 跳过星期六
   * @property {boolean} is_pass_sunday - 跳过星期日
   * @property {boolean} is_pass_holiday - 跳过节假日
   * @property {integer} organization - Organization
   * @property {integer} account - Account
   */

  /**
   *
   * @typedef {Object} CardSubsidyUserListRepSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} person_no - 人员编号
   * @property {string} person_name - 姓名
   * @property {integer} card_department_group_id - 部门ID列表
   * @property {array} card_user_group_ids -
   */

  /**
   *
   * @typedef {Object} CardWalletSer
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} source_organization - Source organization
   * @property {string} name - 钱包名称
   * @property {string} status - 状态
   * @property {string} user_id - 用户唯一ID
   * @property {string} type - 钱包类型
   * @property {integer} balance - 普通钱包余额
   * @property {integer} subsidy_balance - 补贴钱包余额
   * @property {integer} complimentary_balance - 赠送钱包余额
   * @property {integer} total_consume_fee - 总消费金额
   * @property {integer} total_rechange_fee - 总充值金额
   * @property {integer} total_draw_fee - 钱区取款
   * @property {integer} total_refund_fee - 钱区退款
   * @property {boolean} main - 是否作为主钱包,我们现在规定一级组织的钱包就是主钱包
   * @property {integer} company - 所属项目点公司
   * @property {integer} card_info - Card info
   * @property {integer} pay_info - 关联充值信息,如果是电子钱包类
   */

  /**
   *
   * @typedef {Object} CardUserInfo
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} company_name - 所属企业
   * @property {string} company_id - 所属企业ID
   * @property {string} gender_alias - 性别
   * @property {array} card_user_group_alias -
   * @property {array} organization_alias -
   * @property {string} card_department_group_alias - 部门分组
   * @property {string} account_status_alias - 账户状态
   * @property {string} card_status_alias - 卡状态
   * @property {string} person_status_alias - 用户状态
   * @property {string} person_quit_time - 退户时间
   * @property {string} loss_time - 挂失时间
   * @property {array} wallet -
   * @property {string} delay_flat_cost_alias - 扣费状态别名
   * @property {string} card_source_alias - 发卡来源别名
   * @property {array} card_orgs -
   * @property {string} status - 状态
   * @property {string} person_no - 人员编号
   * @property {string} card_no - 卡号
   * @property {string} name - 会员名
   * @property {string} card_status - 卡状态
   * @property {integer} balance - 储值钱包余额
   * @property {integer} subsidy_balance - 补贴钱包余额
   * @property {integer} complimentary_balance - 赠送钱包余额
   * @property {integer} total_consume_fee - 总消费金额
   * @property {integer} total_rechange_fee - 总充值金额
   * @property {integer} total_draw_fee - 钱区取款
   * @property {integer} total_refund_fee - 钱区退款
   * @property {string} gender - 性别
   * @property {string} phone - 手机号
   * @property {string} person_status - 会员状态
   * @property {integer} delay_flat_cost - 是否待扣工本费
   * @property {integer} flat_cost_fee - 工本费/补卡费
   * @property {integer} card_source - 发卡来源
   * @property {string} face_url - 人脸url
   * @property {string} face_token - 人脸token
   * @property {string} face_user_id - 人脸用户id
   * @property {boolean} facepay - 人脸支付开启
   * @property {string} publish_time - 发卡时间
   * @property {string} effective_time - 卡务生效时间
   * @property {string} expiration_time - 卡务失效时间
   * @property {boolean} allow_custom_wallet_order - 是否允许自定义钱包顺序
   * @property {object} extra_tags - 额外标签信息
   * @property {object} extra_user_detail - 额外用户信息
   * @property {integer} card_department_group - 部门分组
   * @property {array} card_user_groups -
   * @property {array} organization -
   */

  /**
   *
   * @typedef {Object} CardUserListRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} CardAccountListReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} person_no - 人员编号
   * @property {string} person_name - 姓名
   * @property {string} card_no - 卡号
   * @property {string} phone - 手机号
   * @property {integer} card_department_group_id - 部门ID列表
   * @property {array} card_user_group_ids -
   * @property {array} card_status -
   * @property {string} account_status - 账号状态
   * @property {string} person_status - 账号状态
   */

  /**
   *
   * @typedef {Object} CardAccountListRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} AddCardUserReqSer
   * @property {string} person_no - 人员编号
   * @property {string} person_name - 姓名
   * @property {string} card_no - 卡号
   * @property {string} phone - 手机号
   * @property {string} gender - 性别
   * @property {integer} card_department_group_id - 部门ID列表
   * @property {array} card_user_group_ids -
   * @property {string} effective_time - 有效时间
   * @property {string} expiration_time - 到期时间
   * @property {boolean} is_open_card - 是否开卡
   * @property {string} card_pwd - 卡密码
   * @property {boolean} is_sync - 是否同步组织用户信息
   */

  /**
   *
   * @typedef {Object} BatchFreezeCardUserReqSer
   * @property {array} card_user_ids -
   */

  /**
   *
   * @typedef {Object} BatchFreezeCardUserRspSer
   * @property {integer} card_user_id - 卡ID
   * @property {string} create_time - 创建时间
   * @property {string} effective_time - 有效时间
   * @property {string} expiration_time - 到期时间
   * @property {string} msg - 信息
   */

  /**
   *
   * @typedef {Object} ImportCardUserReqSer
   * @property {string} url - 导入文件地址
   * @property {boolean} is_open_card - 是否开卡
   */

  /**
   *
   * @typedef {Object} BatchSetCardUserGroupReqSer
   * @property {array} card_user_ids -
   * @property {array} card_user_group_ids -
   */

  /**
   *
   * @typedef {Object} BaseCardQueryParamsSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} person_no - 人员编号
   * @property {string} person_name - 姓名
   * @property {integer} card_department_group_id - 部门ID列表
   * @property {array} card_user_group_ids -
   * @property {array} card_sources -
   */

  /**
   *
   * @typedef {Object} CardRecord
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} account_name - 操作员
   * @property {string} consumption_name - 消费点
   * @property {string} payer_name - 支付人
   * @property {string} payer_card_no - 支付人卡号
   * @property {string} payer_person_no - 支付人编号
   * @property {string} payer_group - 支付人分组
   * @property {string} payer_mobile - 支付人手机
   * @property {integer} real_fee - 订单原金额
   * @property {integer} discount_fee - 优惠金额
   * @property {integer} discount_rate - 优惠
   * @property {string} out_trade_no - 订单号
   * @property {integer} subsidy_consume - 动账金额(补贴)
   * @property {integer} balance_consume - 动账金额(钱包)
   * @property {string} status - 状态
   * @property {string} operate_type - 操作类型
   * @property {string} consume_type - 消费类型
   * @property {integer} subsidy_money - 补贴余额
   * @property {integer} balance_money - 钱区余额
   * @property {integer} total_balance - 总余额
   * @property {string} remark - 备注
   * @property {string} face_user_id - 人脸用户id
   * @property {string} pay_time - 支付时间
   * @property {string} wallet_type - 钱包类型
   * @property {integer} order - Order
   * @property {integer} company - Company
   * @property {integer} organization - Organization
   * @property {integer} account - Account
   * @property {integer} payinfo - Payinfo
   * @property {integer} wallet - Wallet
   * @property {integer} card_info - Card info
   */

  /**
   *
   * @typedef {Object} CardChangeListRepSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} CardUserListReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} person_no - 人员编号
   * @property {string} person_name - 姓名
   * @property {string} card_no - 卡号
   * @property {string} phone - 手机号
   * @property {integer} card_department_group_id - 部门ID列表
   * @property {array} card_user_group_ids -
   * @property {array} card_status -
   * @property {string} person_status - 账号状态
   * @property {boolean} is_self_org - 是否本组织创建
   * @property {boolean} facepay - 人脸支付状态
   * @property {array} org_ids -
   */

  /**
   *
   * @typedef {Object} FlatCostListReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} person_no - 人员编号
   * @property {string} person_name - 姓名
   * @property {integer} card_department_group_id - 部门ID列表
   * @property {array} card_user_group_ids -
   * @property {array} card_sources -
   * @property {integer} delay_flat_cost_type - 扣费状态
   */

  /**
   *
   * @typedef {Object} FlatCostListRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyCardUserReqSer
   * @property {integer} card_user_id - 卡用户ID
   * @property {string} person_no - 人员编号
   * @property {string} person_name - 姓名
   * @property {string} card_no - 卡号
   * @property {string} phone - 手机号
   * @property {string} gender - 性别
   * @property {integer} card_department_group_id - 部门ID列表
   * @property {array} card_user_group_ids -
   * @property {string} effective_time - 有效时间
   * @property {string} expiration_time - 到期时间
   * @property {boolean} is_open_card - 是否开卡
   * @property {string} card_pwd - 卡密码
   * @property {boolean} is_sync - 是否同步组织用户信息
   */

  /**
   *
   * @typedef {Object} BatchDeleteCardUserFaceSer
   * @property {array} ids -
   */

  /**
   *
   * @typedef {Object} BatchImportFacesFromExcelReqSer
   * @property {string} face_zip_url - 压缩包地址
   */

  /**
   *
   * @typedef {Object} CardUserFacePaySer
   * @property {boolean} facepay - 人脸开关
   */

  /**
   *
   * @typedef {Object} ModifyCardUserFacePaySer
   * @property {array} ids -
   * @property {boolean} facepay - 人脸开关
   */

  /**
   *
   * @typedef {Object} AddCardUserGroupReq
   * @property {integer} id - Id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} type_alias - Type alias
   * @property {integer} card_counts - 用户人数
   * @property {string} status - 状态
   * @property {string} group_id - 分组编号
   * @property {string} group_name - 分组名
   * @property {string} remark - 分组说明
   * @property {integer} type - 分组类型
   * @property {boolean} is_open_debt_money - 是否允许储值钱包透支
   * @property {integer} debt_money - 透支金额
   * @property {boolean} is_open_flat - 是否系统自动扣除工本费（押金）
   * @property {integer} flat_cost - 工本费
   * @property {integer} patch_cost - 补卡费
   * @property {boolean} is_flat_return - 工本费（开卡）退还
   * @property {integer} company - 所属项目点公司
   * @property {integer} organization - Organization
   */

  /**
   *
   * @typedef {Object} CardUserGroup
   * @property {integer} id - id
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} type_alias - Type alias
   * @property {integer} card_counts - 用户人数
   * @property {string} status - 状态
   * @property {string} group_id - 分组编号
   * @property {string} group_name - 分组名
   * @property {string} remark - 分组说明
   * @property {integer} type - 分组类型
   * @property {boolean} is_open_debt_money - 是否允许储值钱包透支
   * @property {integer} debt_money - 透支金额
   * @property {boolean} is_open_flat - 是否系统自动扣除工本费（押金）
   * @property {integer} flat_cost - 工本费
   * @property {integer} patch_cost - 补卡费
   * @property {boolean} is_flat_return - 工本费（开卡）退还
   * @property {integer} company - 所属项目点公司
   * @property {integer} organization - Organization
   */

  /**
   *
   * @typedef {Object} BatchImportUserGroupReqSer
   * @property {string} url - 上传的xls地址
   */

  /**
   *
   * @typedef {Object} GetOrgDebtSettingsRspSer
   * @property {integer} debt_money - 透支额度
   * @property {boolean} is_open_debt_money - 是否允许储值钱包透支
   * @property {array} open_debt_group_list -
   */

  /**
   *
   * @typedef {Object} GetOrgFlatAndPatchCostSettingsRspSer
   * @property {integer} flat_cost - 工本费
   * @property {integer} patch_cost - 补卡费
   * @property {boolean} is_flat_return - 工本费（开卡）退还
   * @property {boolean} is_open_flat - 是否系统自动扣除工本费
   * @property {array} open_flat_group_list -
   */

  /**
   *
   * @typedef {Object} ListCardUserGroupReq
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {boolean} is_show_other - 是否显示其他
   */

  /**
   *
   * @typedef {Object} ListCardUserGroupRsp
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   *
   * @typedef {Object} ModifyCardUserGroupReq
   * @property {integer} id - ID
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} status_alias - Status alias
   * @property {string} type_alias - Type alias
   * @property {integer} card_counts - 用户人数
   * @property {string} status - 状态
   * @property {string} group_id - 分组编号
   * @property {string} group_name - 分组名
   * @property {string} remark - 分组说明
   * @property {integer} type - 分组类型
   * @property {boolean} is_open_debt_money - 是否允许储值钱包透支
   * @property {integer} debt_money - 透支金额
   * @property {boolean} is_open_flat - 是否系统自动扣除工本费（押金）
   * @property {integer} flat_cost - 工本费
   * @property {integer} patch_cost - 补卡费
   * @property {boolean} is_flat_return - 工本费（开卡）退还
   * @property {integer} company - 所属项目点公司
   * @property {integer} organization - Organization
   */

  /**
   *
   * @typedef {Object} ModifyFlatAndPatchCostReqSer
   * @property {integer} id - ID
   * @property {integer} flat_cost - 工本费
   * @property {integer} patch_cost - 补卡费
   * @property {boolean} is_flat_return - 工本费（开卡）退还
   * @property {boolean} is_open_flat - 是否系统自动扣除工本费
   */

  /**
   *
   * @typedef {Object} ModifyGroupDebtMoneyReqSer
   * @property {array} ids -
   * @property {integer} debt_money - 透支额度
   * @property {boolean} is_open_debt_money - 是否允许储值钱包透支
   */

  /**
   *
   * @typedef {Object} ModifyOrgDebtMoneyReqSer
   * @property {integer} debt_money - 透支额度
   * @property {boolean} is_open_debt_money - 是否允许储值钱包透支
   */

  /**
   *
   * @typedef {Object} ModifyOrgFlatAndPatchCostReqSer
   * @property {integer} flat_cost - 工本费
   * @property {integer} patch_cost - 补卡费
   * @property {boolean} is_flat_return - 工本费（开卡）退还
   * @property {boolean} is_open_flat - 是否系统自动扣除工本费
   */

  /**
   *
   * @typedef {Object} RspCodeChoicesSer
   * @property {integer} code - 所有 返回 code 的状态描述
   */

  /**
   *
   * @typedef {Object} SetLanguage
   * @property {string} next - 跳转页面
   * @property {string} language - 语言代码
   */

  /**
   *
   * @typedef {Object} UserCardInfo
   * @property {string} create_time - 创建时间
   * @property {string} update_time - 更新时间
   * @property {integer} _version - 内置版本号
   * @property {string} user_id - 用户唯一ID
   * @property {array} company_names -
   * @property {string} source_alias - 来源
   * @property {array} cardinfo_list -
   * @property {string} nickname - 微信昵称
   * @property {string} phone - 手机号码
   * @property {string} wechat_img_url - 微信默认头像url
   * @property {string} depart_name - 部门信息
   * @property {string} role_type - 角色类型
   * @property {integer} status - 状态
   * @property {string} source - 来源渠道
   * @property {integer} gender - 性别
   * @property {string} province - 所属地区
   * @property {string} city - 所属城市
   * @property {string} province_code - 所属地区（列表）
   * @property {string} appid - Appid
   * @property {string} openid - Openid
   * @property {string} unionid - 微信unionid
   * @property {object} extra_tags - 额外标签信息
   * @property {object} extra_user_detail - 额外用户信息
   * @property {string} headimgurl - 微信图片路径
   * @property {integer} role - 用户角色分组
   * @property {array} company -
   */

  /**
   *
   * @typedef {Object} AdminUserListReqSer
   * @property {integer} page - Page
   * @property {integer} page_size - Page size
   * @property {string} start_date - 开始时间
   * @property {string} end_date - 结束时间
   * @property {string} nickname - 姓名
   * @property {string} phone - 手机
   * @property {string} source - 来源渠道
   */

  /**
   *
   * @typedef {Object} AdminUserListRspSer
   * @property {integer} count - 总条目数
   * @property {integer} page - 当前页数
   * @property {integer} page_size - 每页显示数量
   * @property {object} filter - 查询条件
   * @property {array} results -
   */

  /**
   * ['商户端 地址管理 配送区域']
   *
   * @param {AddAddersAreaReq} param
   * @returns {Object}
   */
  apiAddressAddersAreaAddPost(param) {
    return http.post('/api/address/adders_area/add', param)
  },

  /**
   * ['商户端 地址管理 配送区域']
   *
   * @param {Ids} param
   * @returns {Object}
   */
  apiAddressAddersAreaDeletePost(param) {
    return http.post('/api/address/adders_area/delete', param)
  },

  /**
   * ['商户端 地址管理 配送区域']
   *
   * @param {ListAddersAreaReq} param
   * @returns {Object}
   */
  apiAddressAddersAreaListPost(param) {
    return http.post('/api/address/adders_area/list', param)
  },

  /**
   * ['商户端 地址管理 配送区域']
   *
   * @param {ModifyAddersAreaReq} param
   * @returns {Object}
   */
  apiAddressAddersAreaModifyPost(param) {
    return http.post('/api/address/adders_area/modify', param)
  },

  /**
   * ['商户端 地址管理 配送地址']
   * address.adders_center.add 添加配送地址
   * @param {AddAddersCenterReq} param
   * @returns {Object}
   */
  apiAddressAddersCenterAddPost(param) {
    return http.post('/api/address/adders_center/add', param)
  },

  /**
   * ['商户端 地址管理 配送地址']
   * address.adders_center.add_children 新增下级配送点
   * @param {AddChildrenAddersCenterReq} param
   * @returns {Object}
   */
  apiAddressAddersCenterAddChildrenPost(param) {
    return http.post('/api/address/adders_center/add_children', param)
  },

  /**
   * ['商户端 地址管理 配送地址']
   * address.adders_center.batch_import 批量导入配送地址
   * @param {BatchImportAddersCenterReqSer} param
   * @returns {Object}
   */
  apiAddressAddersCenterBatchImportPost(param) {
    return http.post('/api/address/adders_center/batch_import', param)
  },

  /**
   * ['商户端 地址管理 配送地址']
   * address.adders_center.delete 删除配送地址
   * @param {Ids} param
   * @returns {Object}
   */
  apiAddressAddersCenterDeletePost(param) {
    return http.post('/api/address/adders_center/delete', param)
  },

  /**
   * ['商户端 地址管理 配送地址']
   * address.adders_center.list 配送地址列表
   * @param {ListAddersCenterReq} param
   * @returns {Object}
   */
  apiAddressAddersCenterListPost(param) {
    return http.post('/api/address/adders_center/list', param)
  },

  /**
   * ['商户端 地址管理 配送地址']
   * address.adders_center.modify 编辑配送地址
   * @param {ModifyAddersCenterReq} param
   * @returns {Object}
   */
  apiAddressAddersCenterModifyPost(param) {
    return http.post('/api/address/adders_center/modify', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.account.add 账号新增
   * @param {Account} param
   * @returns {Object}
   */
  apiBackgroundAdminAccountAddPost(param) {
    return http.post('/api/background/admin/account/add', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.account.delete 账号删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundAdminAccountDeletePost(param) {
    return http.post('/api/background/admin/account/delete', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.account.get_login_device_list 获取该账号登录的设备
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundAdminAccountGetLoginDeviceListPost(param) {
    return http.post('/api/background/admin/account/get_login_device_list', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.account.list 账号列表 支持模糊、综合、精确搜索 状态默认显示请选择，下拉显示：全部、启用、停用
   * @param {ListAccountReq} param
   * @returns {Object}
   */
  apiBackgroundAdminAccountListPost(param) {
    return http.post('/api/background/admin/account/list', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.account.list_export 账号列表导出
   * @param {ListAccountReq} param
   * @returns {Object}
   */
  apiBackgroundAdminAccountListExportPost(param) {
    return http.post('/api/background/admin/account/list_export', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.account.modify 账号更改
   * @param {Account} param
   * @returns {Object}
   */
  apiBackgroundAdminAccountModifyPost(param) {
    return http.post('/api/background/admin/account/modify', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.department.add 部门新增 :添加组织：只能向下添加下级层级
   * @param {Organization} param
   * @returns {Object}
   */
  apiBackgroundAdminDepartmentAddPost(param) {
    return http.post('/api/background/admin/department/add', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.department.delete 部门删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundAdminDepartmentDeletePost(param) {
    return http.post('/api/background/admin/department/delete', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.department.list 部门列表
   * @param {ListOrganizationReq} param
   * @returns {Object}
   */
  apiBackgroundAdminDepartmentListPost(param) {
    return http.post('/api/background/admin/department/list', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.department.list_export 部门列表导出
   * @param {ListOrganizationReq} param
   * @returns {Object}
   */
  apiBackgroundAdminDepartmentListExportPost(param) {
    return http.post('/api/background/admin/department/list_export', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.department.modify 部门更改 :只能修改自己以及下级层级，平级以及上级层级无法编辑修改
   * @param {Organization} param
   * @returns {Object}
   */
  apiBackgroundAdminDepartmentModifyPost(param) {
    return http.post('/api/background/admin/department/modify', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.department.tree_list 管理员的部门组织 返回 包含 children_list 的组织列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundAdminDepartmentTreeListPost(param) {
    return http.post('/api/background/admin/department/tree_list', param)
  },

  /**
   * ['超管端 设备管理']
   * background.admin.device.add 添加设备
   * @param {AdminAddDeviceReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminDeviceAddPost(param) {
    return http.post('/api/background/admin/device/add', param)
  },

  /**
   * ['超管端 设备管理']
   * background.admin.device.batch_modify 批量编辑
   * @param {BatchModifyDeviceReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminDeviceBatchModifyPost(param) {
    return http.post('/api/background/admin/device/batch_modify', param)
  },

  /**
   * ['商户端 设备管理']
   * background.admin.device.config 设备设置
   * @param {DeviceSettingReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminDeviceConfigPost(param) {
    return http.post('/api/background/admin/device/config', param)
  },

  /**
   * ['商户端 设备管理']
   * background.admin.device.device_model 获取设备型号 下拉框显示的是 key
   * @param {DeviceModelReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminDeviceDeviceModelPost(param) {
    return http.post('/api/background/admin/device/device_model', param)
  },

  /**
   * ['商户端 设备管理']
   * background.admin.device.device_type 获取设备类型
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundAdminDeviceDeviceTypePost(param) {
    return http.post('/api/background/admin/device/device_type', param)
  },

  /**
   * ['商户端 设备管理']
   * background.admin.device.generate_activation 生成激活码
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundAdminDeviceGenerateActivationPost(param) {
    return http.post('/api/background/admin/device/generate_activation', param)
  },

  /**
   * ['超管端 设备管理']
   * background.admin.device.list 设备列表
   * @param {AdminListDeviceReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminDeviceListPost(param) {
    return http.post('/api/background/admin/device/list', param)
  },

  /**
   * ['商户端 设备管理']
   * background.admin.device.modify 超管设备编辑接口
   * @param {AdminModifyDeviceReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminDeviceModifyPost(param) {
    return http.post('/api/background/admin/device/modify', param)
  },

  /**
   * ['超管 系统管理']
   * background.admin.log.list_log 超管端日志记录
   * @param {ListLogReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminLogListLogPost(param) {
    return http.post('/api/background/admin/log/list_log', param)
  },

  /**
   * ['超管 公告模块']
   * background.admin.messages.add 添加公告
   * @param {MsgAdminSerializerReq} param
   * @returns {Object}
   */
  apiBackgroundAdminMessagesAddPost(param) {
    return http.post('/api/background/admin/messages/add', param)
  },

  /**
   * ['超管 公告模块']
   * background.admin.messages.delete 超管公告模块
   * @param {MsgNo} param
   * @returns {Object}
   */
  apiBackgroundAdminMessagesDeletePost(param) {
    return http.post('/api/background/admin/messages/delete', param)
  },

  /**
   * ['超管 公告模块']
   * background.admin.messages.details 公告详情
   * @param {MsgNoBase} param
   * @returns {Object}
   */
  apiBackgroundAdminMessagesDetailsPost(param) {
    return http.post('/api/background/admin/messages/details', param)
  },

  /**
   * ['超管 公告模块']
   * background.admin.messages.list 超管公告模块
   * @param {ListMessagesReq} param
   * @returns {Object}
   */
  apiBackgroundAdminMessagesListPost(param) {
    return http.post('/api/background/admin/messages/list', param)
  },

  /**
   * ['超管 公告模块']
   * background.admin.messages.modify 超管公告模块
   * @param {MsgAdminSerializerReq} param
   * @returns {Object}
   */
  apiBackgroundAdminMessagesModifyPost(param) {
    return http.post('/api/background/admin/messages/modify', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.add 组织新增 :添加组织：只能向下添加下级层级
   * @param {AdminAddOrganizationInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationAddPost(param) {
    return http.post('/api/background/admin/organization/add', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.add_root 新增项目点根组织
   * @param {AddRootOrganizationInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationAddRootPost(param) {
    return http.post('/api/background/admin/organization/add_root', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background.admin.organization.consume_list 消费点接口列表 background_organization.organization.consume_list
   * @param {ListOrganizationReq} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationConsumeListPost(param) {
    return http.post('/api/background/admin/organization/consume_list', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.delete 组织删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationDeletePost(param) {
    return http.post('/api/background/admin/organization/delete', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.enable 开启关闭组织
   * @param {EnableOrganizationReq} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationEnablePost(param) {
    return http.post('/api/background/admin/organization/enable', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.generate_third_appinfo 生成第三方调用的秘钥信息
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationGenerateThirdAppinfoPost(param) {
    return http.post('/api/background/admin/organization/generate_third_appinfo', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.get_appid_list 支持的公众号列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationGetAppidListPost(param) {
    return http.post('/api/background/admin/organization/get_appid_list', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.get_info 组织基本信息
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationGetInfoPost(param) {
    return http.post('/api/background/admin/organization/get_info', param)
  },

  /**
   * ['商户 系统管理 组织管理', '超管 商户管理']
   * background.admin.organization.get_level_name_map 获取指定公司的层级列表
   * @param {GetLevelNameMapReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationGetLevelNameMapPost(param) {
    return http.post('/api/background/admin/organization/get_level_name_map', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.get_merchant_permissions 获取商户的全部权限树列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationGetMerchantPermissionsPost(param) {
    return http.post('/api/background/admin/organization/get_merchant_permissions', param)
  },
  /**
   * ['超管 商户管理']
   * background.admin.organization.get_merchant_permissions 获取移動端商户的全部权限树列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationGetMerchantMobilePermissions(param) {
    return http.post('/api/background/admin/organization/get_merchant_mobile_permissions', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.get_org_appid 获取当前组织的公众号appid
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationGetOrgAppidPost(param) {
    return http.post('/api/background/admin/organization/get_org_appid', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.get_settings 获取组织高级设置-扣款设置
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationGetSettingsPost(param) {
    return http.post('/api/background/admin/organization/get_settings', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.list 组织列表
   * @param {ListOrganizationReq} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationListPost(param) {
    return http.post('/api/background/admin/organization/list', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.modify 组织信息更改 :只能修改自己以及下级层级，平级以及上级层级无法编辑修改
   * @param {AdminModifyOrganizationInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationModifyPost(param) {
    return http.post('/api/background/admin/organization/modify', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.modify_level_name 修改层级名称
   * @param {ModifyOrganizationLevelName} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationModifyLevelNamePost(param) {
    return http.post('/api/background/admin/organization/modify_level_name', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.modify_settings 修改组织高级设置-扣款设置
   * @param {OrganizationSettings} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationModifySettingsPost(param) {
    return http.post('/api/background/admin/organization/modify_settings', param)
  },

  /**
   * ['超管 商户管理']
   * background.admin.organization.tree_list 组织树 返回 包含 children_list 的组织列表
   * @param {TreeListReqOrganization} param
   * @returns {Object}
   */
  apiBackgroundAdminOrganizationTreeListPost(param) {
    return http.post('/api/background/admin/organization/tree_list', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.add 支付配置新增
   * @param {PayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoAddPost(param) {
    return http.post('/api/background/admin/pay_info/add', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.delete 支付配置删除
   * @param {OrgIdsReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoDeletePost(param) {
    return http.post('/api/background/admin/pay_info/delete', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.get_order_payinfos 扣款顺序设置-获取组织生效的扣款顺序配置列表 :param request: :return:
   * @param {BindPayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoGetOrderPayinfosPost(param) {
    return http.post('/api/background/admin/pay_info/get_order_payinfos', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.list 关于organizations的筛选有几种情况, 列表中只允许传单个组织id 1. organizations 为level_0的时候，可以查看所有支付列表 2. organizations 为非level_0的时候，只能选择当前他适用的支付列表
   * @param {ListPayInfoReq} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoListPost(param) {
    return http.post('/api/background/admin/pay_info/list', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.modify 支付配置修改
   * @param {PayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoModifyPost(param) {
    return http.post('/api/background/admin/pay_info/modify', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.set_order_payinfos (传参: pay_scene='online' 可以查询钱包的支付顺序列表) (传参: pay_scene='instore' 可以查询到店线下的支付顺序列表) (传参: pay_scene='charge' 可以查询到店充值顺序列表) :param request: :return:
   * @param {MultiPayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoSetOrderPayinfosPost(param) {
    return http.post('/api/background/admin/pay_info/set_order_payinfos', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.sub_orgs_all_list 支付配置-获取组织的全部支付配置列表
   * @param {BindPayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoSubOrgsAllListPost(param) {
    return http.post('/api/background/admin/pay_info/sub_orgs_all_list', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.sub_orgs_bind 支付设置-设置子组织绑定关联支付配置 :param request: :return:
   * @param {BindPayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoSubOrgsBindPost(param) {
    return http.post('/api/background/admin/pay_info/sub_orgs_bind', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.sub_orgs_bind_list 支付设置-只返回已关联的的支付配置列表
   * @param {MultiPayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoSubOrgsBindListPost(param) {
    return http.post('/api/background/admin/pay_info/sub_orgs_bind_list', param)
  },

  /**
   * ['超管 支付配置模块']
   * background.admin.pay_info.template_list 支付配置-模板列表
   * @param {PaySceneReqSer} param
   * @returns {Object}
   */
  apiBackgroundAdminPayInfoTemplateListPost(param) {
    return http.post('/api/background/admin/pay_info/template_list', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.role.account_list 查看角色账号列表
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundAdminRoleAccountListPost(param) {
    return http.post('/api/background/admin/role/account_list', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.role.add 角色新增 添加角色必填，可创建多个角色，但只能添加在一个层级上
   * @param {Role} param
   * @returns {Object}
   */
  apiBackgroundAdminRoleAddPost(param) {
    return http.post('/api/background/admin/role/add', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.role.delete 角色删除 删除：只能删除自己下级层级以及自己， 无法删除比自己更高层级以及同层级的组织， 但该层级如已有账号关联则无法删除，集团公司角色只有集团公司角色进行删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundAdminRoleDeletePost(param) {
    return http.post('/api/background/admin/role/delete', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.role.list 角色列表 1：显示已创建组织架构层级； 2：集团公司显示全部的组织架构层级；其余层级只显示自己以及自己下级层级的组织架构 3：默认显示全部角色，点击对应层级显示该层级所有角色
   * @param {ListRoleReq} param
   * @returns {Object}
   */
  apiBackgroundAdminRoleListPost(param) {
    return http.post('/api/background/admin/role/list', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.role.list_export 角色列表导出
   * @param {ListRoleReq} param
   * @returns {Object}
   */
  apiBackgroundAdminRoleListExportPost(param) {
    return http.post('/api/background/admin/role/list_export', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.role.modify 角色更改
   * @param {Role} param
   * @returns {Object}
   */
  apiBackgroundAdminRoleModifyPost(param) {
    return http.post('/api/background/admin/role/modify', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.role.permission_list 角色权限列表 集团公司级层级人员（超管）拥有所有权限 权限设置显示已开发功能菜单，只区分1级与2级菜单，需√上才能启用操作 筛选：页面默认显示全部功能，可下拉筛选功能选择进行对一级功能菜单进行筛选
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundAdminRolePermissionListPost(param) {
    return http.post('/api/background/admin/role/permission_list', param)
  },

  /**
   * ['超管 员工管理']
   * background.admin.role.set_permission 设置角色权限 只能设置比自己低层级的权限，不能设置自己、平级以及上级权限；
   * @param {SetPermissionKeyReq} param
   * @returns {Object}
   */
  apiBackgroundAdminRoleSetPermissionPost(param) {
    return http.post('/api/background/admin/role/set_permission', param)
  },

  /**
   * ['后台登录']
   * base.login.bind_org 切换当前账号组织
   * @param {BindOrganizationReqSer} param
   * @returns {Object}
   */
  apiBackgroundBindOrgPost(param) {
    return http.post('/api/background/bind_org', param)
  },

  /**
   * ['后台登录']
   * base.login.find_password 重置密码
   * @param {ResetPwdReqSer} param
   * @returns {Object}
   */
  apiBackgroundFindPasswordPost(param) {
    return http.post('/api/background/find_password', param)
  },

  /**
   * ['商户 系统管理']
   * base.log.list_log 操作日志
   * @param {ListLogReqSer} param
   * @returns {Object}
   */
  apiBackgroundLogListPost(param) {
    return http.post('/api/background/log/list', param)
  },

  /**
   * ['后台登录']
   * base.login.login 后台登录
   * @param {LoginReqSer} param
   * @returns {Object}
   */
  apiBackgroundLoginPost(param) {
    return http.post('/api/background/login', param)
  },

  /**
   * ['后台登录']
   * base.login.modify_userinfo 编辑个人信息
   * @param {UserInfoModifyRspSer} param
   * @returns {Object}
   */
  apiBackgroundModifyUserinfoPost(param) {
    return http.post('/api/background/modify_userinfo', param)
  },

  /**
   * ['后台登录']
   * base.login.verification_code 发送登陆验证码
   * @param {LoginSMSReqSer} param
   * @returns {Object}
   */
  apiBackgroundVerificationCodePost(param) {
    return http.post('/api/background/verification_code', param)
  },

  /**
   * ['后台登录']
   * base.login.verification_code_auto 发送验证码给已绑定的手机号
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundVerificationCodeAutoPost(param) {
    return http.post('/api/background/verification_code_auto', param)
  },

  /**
   * ['后台登录']
   * base.login.wechat_validate 微信扫码登陆内嵌二维码配置信息
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundWechatValidatePost(param) {
    return http.post('/api/background/wechat_validate', param)
  },

  /**
   * ['后台登录']
   * background_base.menu.get_list 菜单权限列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundBaseMenuGetListPost(param) {
    return http.post('/api/background_base/menu/get_list', param)
  },

  /**
   * ['通用接口 菜单']
   * background_base.menu.meal_type 餐段列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundBaseMenuMealTypePost(param) {
    return http.post('/api/background_base/menu/meal_type', param)
  },

  /**
   * ['通用接口 菜单']
   * background_base.menu.take_meal_status 取餐方式列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundBaseMenuTakeMealStatusPost(param) {
    return http.post('/api/background_base/menu/take_meal_status', param)
  },

  /**
   * ['通用接口 菜单']
   * background_base.menu.take_meal_type 取餐状态列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundBaseMenuTakeMealTypePost(param) {
    return http.post('/api/background_base/menu/take_meal_type', param)
  },

  /**
   * ['基础接口']
   * background_base.tasks.export_query 异步导出任务结果查询
   * @param {ExportQueryReqSer} param
   * @returns {Object}
   */
  apiBackgroundBaseTasksExportQueryPost(param) {
    return http.post('/api/background_base/tasks/export_query', param)
  },

  /**
   * ['商户端 设备管理 智能秤设置']
   * background_device.buffet.add 添加智能称配置
   * @param {SetBuffetInfoRspSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceBuffetAddPost(param) {
    return http.post('/api/background_device/buffet/add', param)
  },

  /**
   * ['api']
   * background_device.buffet.delete 删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundDeviceBuffetDeletePost(param) {
    return http.post('/api/background_device/buffet/delete', param)
  },

  /**
   * ['商户端 设备管理 智能秤设置']
   * background_device.buffet.details 智能称配置
   * @param {StallNoReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceBuffetDetailsPost(param) {
    return http.post('/api/background_device/buffet/details', param)
  },

  /**
   * ['设备端']
   * background_device.client.activate 设备端激活接口
   * @param {DeviceActivateReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceClientActivatePost(param) {
    return http.post('/api/background_device/client/activate', param)
  },

  /**
   * ['api']
   * background_device.client.delete 删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundDeviceClientDeletePost(param) {
    return http.post('/api/background_device/client/delete', param)
  },

  /**
   * ['设备端']
   * background_device.client.login 设备端登录接口
   * @param {DeviceLoginReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceClientLoginPost(param) {
    return http.post('/api/background_device/client/login', param)
  },

  /**
   * ['设备端']
   * background_device.client.logout 设备登出接口
   * @param {DeviceLoginReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceClientLogoutPost(param) {
    return http.post('/api/background_device/client/logout', param)
  },

  /**
   * ['商户端 设备管理']
   * background_device.device.batch_modify background_device设备相关信息
   * @param {BatchModifyDeviceReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceDeviceBatchModifyPost(param) {
    return http.post('/api/background_device/device/batch_modify', param)
  },

  /**
   * ['商户端 设备管理']
   * background_device.device.config 设备设置
   * @param {DeviceSettingReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceDeviceConfigPost(param) {
    return http.post('/api/background_device/device/config', param)
  },

  /**
   * ['商户端 设备管理']
   * background_device.device.device_model 获取设备型号 下拉框显示的是 key
   * @param {DeviceModelReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceDeviceDeviceModelPost(param) {
    return http.post('/api/background_device/device/device_model', param)
  },

  /**
   * ['商户端 设备管理']
   * background_device.device.device_type 获取设备类型
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundDeviceDeviceDeviceTypePost(param) {
    return http.post('/api/background_device/device/device_type', param)
  },

  /**
   * ['商户端 设备管理']
   * background_device.device.generate_activation 生成激活码
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundDeviceDeviceGenerateActivationPost(param) {
    return http.post('/api/background_device/device/generate_activation', param)
  },

  /**
   * ['商户端 设备管理']
   * background_device.device.list 设备相关信息列表
   * @param {DefineDeviceReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceDeviceListPost(param) {
    return http.post('/api/background_device/device/list', param)
  },

  /**
   * ['商户端 设备管理']
   * background_device.device.modify 设备相关信息更改
   * @param {ModifyDeviceReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceDeviceModifyPost(param) {
    return http.post('/api/background_device/device/modify', param)
  },

  /**
   * ['商户端 设备管理 打印配置']
   * background_device.device_print_conf.add 打印相关配置新增
   * @param {DevicePrintConfJSONReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceDevicePrintConfAddPost(param) {
    return http.post('/api/background_device/device_print_conf/add', param)
  },

  /**
   * ['商户端 设备管理 打印配置']
   * background_device.device_print_conf.batch_delete 批量删除打印配置
   * @param {DevicePrintConfNosSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceDevicePrintConfBatchDeletePost(param) {
    return http.post('/api/background_device/device_print_conf/batch_delete', param)
  },

  /**
   * ['商户端 设备管理 打印配置']
   * background_device.device_print_conf.conf_deatail 打印配置详情
   * @param {DevicePrintConfNoSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceDevicePrintConfConfDeatailPost(param) {
    return http.post('/api/background_device/device_print_conf/conf_deatail', param)
  },

  /**
   * ['商户端 设备管理 打印配置']
   * background_device.device_print_conf.list 打印相关配置列表
   * @param {ListDevicePrintConfReq} param
   * @returns {Object}
   */
  apiBackgroundDeviceDevicePrintConfListPost(param) {
    return http.post('/api/background_device/device_print_conf/list', param)
  },

  /**
   * ['商户端 设备管理 打印配置']
   * background_device.device_print_conf.modify 打印相关配置更改
   * @param {DevicePrintConfJSONReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceDevicePrintConfModifyPost(param) {
    return http.post('/api/background_device/device_print_conf/modify', param)
  },

  /**
   * ['api']
   * background_device.log.delete 删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundDeviceLogDeletePost(param) {
    return http.post('/api/background_device/log/delete', param)
  },

  /**
   * ['商户端 设备管理 日志相关']
   * background_device.log.list 日志相关
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundDeviceLogListPost(param) {
    return http.post('/api/background_device/log/list', param)
  },

  /**
   * ['api']
   * background_device.tray.delete 删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundDeviceTrayDeletePost(param) {
    return http.post('/api/background_device/tray/delete', param)
  },

  /**
   * ['商户端 设备管理 托盘二维码']
   * background_device.tray.tray_add 托盘二维码添加
   * @param {TrayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceTrayTrayAddPost(param) {
    return http.post('/api/background_device/tray/tray_add', param)
  },

  /**
   * ['商户端 设备管理 托盘二维码']
   * background_device.tray.tray_bat_add 托盘二维码批量添加
   * @param {TrayInfoUrlReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceTrayTrayBatAddPost(param) {
    return http.post('/api/background_device/tray/tray_bat_add', param)
  },

  /**
   * ['商户端 设备管理 托盘二维码']
   * background_device.tray.tray_bat_export 托盘二维码批量导出
   * @param {TrayExportReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceTrayTrayBatExportPost(param) {
    return http.post('/api/background_device/tray/tray_bat_export', param)
  },

  /**
   * ['商户端 设备管理 托盘二维码']
   * background_device.tray.tray_bat_modify 托盘二维码批量修改
   * @param {TrayInfoUrlReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceTrayTrayBatModifyPost(param) {
    return http.post('/api/background_device/tray/tray_bat_modify', param)
  },

  /**
   * ['商户端 设备管理 托盘二维码']
   * background_device.tray.tray_batch_delete 托盘批量删除
   * @param {TrayBatchReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceTrayTrayBatchDeletePost(param) {
    return http.post('/api/background_device/tray/tray_batch_delete', param)
  },

  /**
   * ['商户端 设备管理 托盘二维码']
   * background_device.tray.tray_delete 托盘二维码删除
   * @param {TrayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceTrayTrayDeletePost(param) {
    return http.post('/api/background_device/tray/tray_delete', param)
  },

  /**
   * ['商户端 设备管理 托盘二维码']
   * background_device.tray.tray_list 托盘二维码列表
   * @param {TrayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceTrayTrayListPost(param) {
    return http.post('/api/background_device/tray/tray_list', param)
  },

  /**
   * ['商户端 设备管理 托盘二维码']
   * background_device.tray.tray_modify 托盘二维码编辑
   * @param {TrayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundDeviceTrayTrayModifyPost(param) {
    return http.post('/api/background_device/tray/tray_modify', param)
  },

  /**
   * ['商户 菜单管理 膳食人群']
   * background_food.diet_group.add 膳食人群新增
   * @param {AddDietGroupReq} param
   * @returns {Object}
   */
  apiBackgroundFoodDietGroupAddPost(param) {
    return http.post('/api/background_food/diet_group/add', param)
  },

  /**
   * ['商户 菜单管理 膳食人群']
   * background_food.diet_group.delete 膳食人群删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodDietGroupDeletePost(param) {
    return http.post('/api/background_food/diet_group/delete', param)
  },

  /**
   * ['商户 菜单管理 膳食人群']
   * background_food.diet_group.list 膳食人群列表
   * @param {ListDietGroupReq} param
   * @returns {Object}
   */
  apiBackgroundFoodDietGroupListPost(param) {
    return http.post('/api/background_food/diet_group/list', param)
  },

  /**
   * ['商户 菜单管理 膳食人群']
   * background_food.diet_group.list_export 膳食人群列表导出
   * @param {ListDietGroupReq} param
   * @returns {Object}
   */
  apiBackgroundFoodDietGroupListExportPost(param) {
    return http.post('/api/background_food/diet_group/list_export', param)
  },

  /**
   * ['商户 菜单管理 膳食人群']
   * background_food.diet_group.modify 膳食人群更改
   * @param {ModifyDietGroupReq} param
   * @returns {Object}
   */
  apiBackgroundFoodDietGroupModifyPost(param) {
    return http.post('/api/background_food/diet_group/modify', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.add 菜品新增
   * @param {AddFoodReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodAddPost(param) {
    return http.post('/api/background_food/food/add', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.add_ingredient 添加食材
   * @param {AddFoodIngredient} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodAddIngredientPost(param) {
    return http.post('/api/background_food/food/add_ingredient', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.add_spec 添加规格
   * @param {AddFoodSpec} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodAddSpecPost(param) {
    return http.post('/api/background_food/food/add_spec', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.add_taste 添加口味
   * @param {AddFoodTaste} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodAddTastePost(param) {
    return http.post('/api/background_food/food/add_taste', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.batch_add_discount 批量创建优惠
   * @param {BatchAddFoodDiscount} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodBatchAddDiscountPost(param) {
    return http.post('/api/background_food/food/batch_add_discount', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.batch_modify 菜品批量编辑
   * @param {BatchModifyFoodReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodBatchModifyPost(param) {
    return http.post('/api/background_food/food/batch_modify', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.batch_on_sale 菜品批量修改上下架
   * @param {BatchOnSaleReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodBatchOnSalePost(param) {
    return http.post('/api/background_food/food/batch_on_sale', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.delete 菜品删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodDeletePost(param) {
    return http.post('/api/background_food/food/delete', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.delete_discount 删除优惠
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodDeleteDiscountPost(param) {
    return http.post('/api/background_food/food/delete_discount', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.delete_ingredient 删除食材
   * @param {RemoveFoodIngredientIds} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodDeleteIngredientPost(param) {
    return http.post('/api/background_food/food/delete_ingredient', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.delete_spec 删除规格
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodDeleteSpecPost(param) {
    return http.post('/api/background_food/food/delete_spec', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.delete_taste 删除口味
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodDeleteTastePost(param) {
    return http.post('/api/background_food/food/delete_taste', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.distribute 下发菜品
   * @param {DistributeFoodReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodDistributePost(param) {
    return http.post('/api/background_food/food/distribute', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.list 菜品列表
   * @param {ListFoodReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodListPost(param) {
    return http.post('/api/background_food/food/list', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.list_discount 菜品优惠列表
   * @param {FoodDiscountReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodListDiscountPost(param) {
    return http.post('/api/background_food/food/list_discount', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.list_export 菜品列表导出
   * @param {ListFoodReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodListExportPost(param) {
    return http.post('/api/background_food/food/list_export', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.modify 菜品更改
   * @param {ModifyFoodReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodModifyPost(param) {
    return http.post('/api/background_food/food/modify', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.modify_discount 编辑优惠
   * @param {ModifyFoodDiscount} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodModifyDiscountPost(param) {
    return http.post('/api/background_food/food/modify_discount', param)
  },

  /**
   * ['商户 菜单管理 菜品列表']
   * background_food.food.user_reservation_menu 用户点餐菜品
   * @param {UserReservationFoodReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodUserReservationMenuPost(param) {
    return http.post('/api/background_food/food/user_reservation_menu', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_category.add 食品二级分类新增
   * @param {AddFoodCategoryReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodCategoryAddPost(param) {
    return http.post('/api/background_food/food_category/add', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_category.delete 食品二级分类删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodCategoryDeletePost(param) {
    return http.post('/api/background_food/food_category/delete', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_category.list 食品二级分类列表
   * @param {ListFoodCategoryReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodCategoryListPost(param) {
    return http.post('/api/background_food/food_category/list', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_category.list_export 食品二级分类列表导出
   * @param {ListFoodCategoryReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodCategoryListExportPost(param) {
    return http.post('/api/background_food/food_category/list_export', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_category.modify 食品二级分类更改
   * @param {ModifyFoodCategoryReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodCategoryModifyPost(param) {
    return http.post('/api/background_food/food_category/modify', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_sort.add 食品一级分类新增
   * @param {AddFoodSortReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodSortAddPost(param) {
    return http.post('/api/background_food/food_sort/add', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_sort.delete 食品一级分类删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodSortDeletePost(param) {
    return http.post('/api/background_food/food_sort/delete', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_sort.list 食品一级分类列表
   * @param {ListFoodSortReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodSortListPost(param) {
    return http.post('/api/background_food/food_sort/list', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_sort.list_export 食品一级分类列表导出
   * @param {ListFoodSortReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodSortListExportPost(param) {
    return http.post('/api/background_food/food_sort/list_export', param)
  },

  /**
   * ['商户 菜单管理 菜品分类']
   * background_food.food_sort.modify 食品一级分类更改
   * @param {ModifyFoodSortReq} param
   * @returns {Object}
   */
  apiBackgroundFoodFoodSortModifyPost(param) {
    return http.post('/api/background_food/food_sort/modify', param)
  },

  /**
   * ['商户 菜单管理 食材管理']
   * background_food.ingredient.add 食材新增
   * @param {AddIngredientReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientAddPost(param) {
    return http.post('/api/background_food/ingredient/add', param)
  },

  /**
   * ['商户 菜单管理 食材管理']
   * background_food.ingredient.delete 食材删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientDeletePost(param) {
    return http.post('/api/background_food/ingredient/delete', param)
  },

  /**
   * ['商户 菜单管理 食材管理']
   * background_food.ingredient.list 食材管理
   * @param {ListIngredientReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientListPost(param) {
    return http.post('/api/background_food/ingredient/list', param)
  },

  /**
   * ['商户 菜单管理 食材管理']
   * background_food.ingredient.list_export 食材列表导出
   * @param {ListIngredientReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientListExportPost(param) {
    return http.post('/api/background_food/ingredient/list_export', param)
  },

  /**
   * ['商户 菜单管理 食材管理']
   * background_food.ingredient.modify 食材更改
   * @param {ModifyIngredientReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientModifyPost(param) {
    return http.post('/api/background_food/ingredient/modify', param)
  },

  /**
   * ['商户 菜单管理 食材管理']
   * background_food.ingredient.sync 食材同步
   * @param {SyncIngredientReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSyncPost(param) {
    return http.post('/api/background_food/ingredient/sync', param)
  },

  /**
   * ['商户 菜单管理 食材管理']
   * background_food.ingredient.warehouse 食材库
   * @param {ListIngredientReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientWarehousePost(param) {
    return http.post('/api/background_food/ingredient/warehouse', param)
  },

  /**
   * ['商户 菜单管理 食材分类']
   * background_food.ingredient_sort.add 食材分类新增
   * @param {AddIngredientSortReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSortAddPost(param) {
    return http.post('/api/background_food/ingredient_sort/add', param)
  },

  /**
   * ['商户 菜单管理 食材分类']
   * background_food.ingredient_sort.delete 食材分类删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSortDeletePost(param) {
    return http.post('/api/background_food/ingredient_sort/delete', param)
  },

  /**
   * ['商户 菜单管理 食材分类']
   * background_food.ingredient_sort.list 食材分类列表
   * @param {ListIngredientSortReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSortListPost(param) {
    return http.post('/api/background_food/ingredient_sort/list', param)
  },

  /**
   * ['商户 菜单管理 食材分类']
   * background_food.ingredient_sort.list_export 食材分类列表导出
   * @param {ListIngredientSortReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSortListExportPost(param) {
    return http.post('/api/background_food/ingredient_sort/list_export', param)
  },

  /**
   * ['商户 菜单管理 食材分类']
   * background_food.ingredient_sort.modify 食材分类更改
   * @param {ModifyIngredientSortReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSortModifyPost(param) {
    return http.post('/api/background_food/ingredient_sort/modify', param)
  },

  /**
   * ['商户 菜单管理 供应商管理']
   * background_food.ingredient_supplier.add 食材供应商新增
   * @param {AddIngredientSupplierReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSupplierAddPost(param) {
    return http.post('/api/background_food/ingredient_supplier/add', param)
  },

  /**
   * ['商户 菜单管理 供应商管理']
   * background_food.ingredient_supplier.delete 食材供应商删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSupplierDeletePost(param) {
    return http.post('/api/background_food/ingredient_supplier/delete', param)
  },

  /**
   * ['商户 菜单管理 供应商管理']
   * background_food.ingredient_supplier.list 食材供应商列表
   * @param {ListIngredientSupplierReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSupplierListPost(param) {
    return http.post('/api/background_food/ingredient_supplier/list', param)
  },

  /**
   * ['商户 菜单管理 供应商管理']
   * background_food.ingredient_supplier.list_export 食材供应商列表导出
   * @param {ListIngredientSupplierReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSupplierListExportPost(param) {
    return http.post('/api/background_food/ingredient_supplier/list_export', param)
  },

  /**
   * ['商户 菜单管理 供应商管理']
   * background_food.ingredient_supplier.modify 食材供应商更改
   * @param {ModifyIngredientSupplierReq} param
   * @returns {Object}
   */
  apiBackgroundFoodIngredientSupplierModifyPost(param) {
    return http.post('/api/background_food/ingredient_supplier/modify', param)
  },

  /**
   * ['api']
   * background_food.menu_device.delete 删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuDeviceDeletePost(param) {
    return http.post('/api/background_food/menu_device/delete', param)
  },

  /**
   * ['商户 菜单管理 菜谱设备']
   * background_food.menu_device.list 菜谱设备列表
   * @param {ListMenuDeviceReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuDeviceListPost(param) {
    return http.post('/api/background_food/menu_device/list', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.add 每月菜谱新增
   * @param {AddMenuMonthlyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyAddPost(param) {
    return http.post('/api/background_food/menu_monthly/add', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.batch_used 批量上下架
   * @param {BatchUsedMenuMonthlyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyBatchUsedPost(param) {
    return http.post('/api/background_food/menu_monthly/batch_used', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.copy_day_food 复制单日菜品
   * @param {MenuMonthlyFoodCopyDayReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyCopyDayFoodPost(param) {
    return http.post('/api/background_food/menu_monthly/copy_day_food', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.copy_meal_type_food 复制某个餐段菜品
   * @param {MenuMonthlyFoodCopyMealTypeReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyCopyMealTypeFoodPost(param) {
    return http.post('/api/background_food/menu_monthly/copy_meal_type_food', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.delete 每月菜谱删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyDeletePost(param) {
    return http.post('/api/background_food/menu_monthly/delete', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.list 每月菜谱列表
   * @param {ListMenuMonthlyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyListPost(param) {
    return http.post('/api/background_food/menu_monthly/list', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.list_export 每月菜谱列表导出
   * @param {ListMenuMonthlyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyListExportPost(param) {
    return http.post('/api/background_food/menu_monthly/list_export', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.menu_food_modify 编辑菜谱菜品
   * @param {MenuMonthlyFoodModifyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyMenuFoodModifyPost(param) {
    return http.post('/api/background_food/menu_monthly/menu_food_modify', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.modify 每月菜谱更改
   * @param {ModifyMenuMonthlyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyModifyPost(param) {
    return http.post('/api/background_food/menu_monthly/modify', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.monthly_copy 复制每月菜谱
   * @param {CopyMenuMonthlyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyMonthlyCopyPost(param) {
    return http.post('/api/background_food/menu_monthly/monthly_copy', param)
  },

  /**
   * ['商户 菜单管理 每月菜谱']
   * background_food.menu_monthly.monthly_detail 每月菜谱详情
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuMonthlyMonthlyDetailPost(param) {
    return http.post('/api/background_food/menu_monthly/monthly_detail', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.add 每周菜谱新增
   * @param {AddMenuWeeklyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyAddPost(param) {
    return http.post('/api/background_food/menu_weekly/add', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.batch_used 批量上下架
   * @param {BatchUsedMenuWeeklyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyBatchUsedPost(param) {
    return http.post('/api/background_food/menu_weekly/batch_used', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.category_list 分类列表
   * @param {Params} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyCategoryListPost(param) {
    return http.post('/api/background_food/menu_weekly/category_list', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.copy_day_food 复制单日菜品
   * @param {MenuFoodCopyDayReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyCopyDayFoodPost(param) {
    return http.post('/api/background_food/menu_weekly/copy_day_food', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.copy_meal_type_food 复制某个餐段菜品
   * @param {MenuFoodCopyMealTypeReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyCopyMealTypeFoodPost(param) {
    return http.post('/api/background_food/menu_weekly/copy_meal_type_food', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.delete 每周菜谱删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyDeletePost(param) {
    return http.post('/api/background_food/menu_weekly/delete', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.food_list 菜品列表
   * @param {MenuFoodFilter} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyFoodListPost(param) {
    return http.post('/api/background_food/menu_weekly/food_list', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.list 每周菜谱列表
   * @param {ListMenuWeeklyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyListPost(param) {
    return http.post('/api/background_food/menu_weekly/list', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.menu_food_modify 编辑菜谱菜品
   * @param {MenuFoodModifyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyMenuFoodModifyPost(param) {
    return http.post('/api/background_food/menu_weekly/menu_food_modify', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.modify 每周菜谱更改
   * @param {ModifyMenuWeeklyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyModifyPost(param) {
    return http.post('/api/background_food/menu_weekly/modify', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.weekly_copy 复制每周菜谱
   * @param {CopyMenuWeeklyReq} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyWeeklyCopyPost(param) {
    return http.post('/api/background_food/menu_weekly/weekly_copy', param)
  },

  /**
   * ['商户 菜单管理 每周菜谱']
   * background_food.menu_weekly.weekly_detail 每周菜谱详情
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundFoodMenuWeeklyWeeklyDetailPost(param) {
    return http.post('/api/background_food/menu_weekly/weekly_detail', param)
  },

  /**
   * ['营销模块 消费规则']
   * background_marketing.consume.add 新增消费规则
   * @param {AddConsumeRuleReqSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingConsumeAddPost(param) {
    return http.post('/api/background_marketing/consume/add', param)
  },

  /**
   * ['营销模块 消费规则']
   * background_marketing.consume.delete 删除消费规则
   * @param {ConsumeRuleNoReqSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingConsumeDeletePost(param) {
    return http.post('/api/background_marketing/consume/delete', param)
  },

  /**
   * ['营销模块 消费规则']
   * background_marketing.consume.list 消费规则列表
   * @param {ListConsumeRuleReqSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingConsumeListPost(param) {
    return http.post('/api/background_marketing/consume/list', param)
  },

  /**
   * ['营销模块 消费规则']
   * background_marketing.consume.modify 编辑消费规则
   * @param {ModifyConsumeRuleReqSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingConsumeModifyPost(param) {
    return http.post('/api/background_marketing/consume/modify', param)
  },

  /**
   * ['营销模块 充值活动']
   * background_marketing.recharge.add 新增充值活动
   * @param {AddRechargeCouponReqSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingRechargeAddPost(param) {
    return http.post('/api/background_marketing/recharge/add', param)
  },

  /**
   * ['营销模块 充值活动']
   * background_marketing.recharge.change 改变活动状态
   * @param {ChangeCouponStatusSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingRechargeChangePost(param) {
    return http.post('/api/background_marketing/recharge/change', param)
  },

  /**
   * ['营销模块 充值活动']
   * background_marketing.recharge.details 获取规则详情
   * @param {CouponNoReqSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingRechargeDetailsPost(param) {
    return http.post('/api/background_marketing/recharge/details', param)
  },

  /**
   * ['营销模块 充值活动']
   * background_marketing.recharge.list 充值活动列表
   * @param {RechargeCouponReqSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingRechargeListPost(param) {
    return http.post('/api/background_marketing/recharge/list', param)
  },

  /**
   * ['营销模块 充值活动']
   * background_marketing.recharge.modify 编辑充值活动
   * @param {ModifyRechargeCouponReqSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingRechargeModifyPost(param) {
    return http.post('/api/background_marketing/recharge/modify', param)
  },

  /**
   * ['营销模块 充值活动']
   * background_marketing.recharge.report 充值活动统计 OrderCharge 充值时间 用户名 用户编号 订单号 活动名 分组 OrderCharge 编号 所属组织 活动名 充值订单号 用户编号 用户名 分组 充值时间 充值金额 赠送内容
   * @param {RechargeCouponReportReqSer} param
   * @returns {Object}
   */
  apiBackgroundMarketingRechargeReportPost(param) {
    return http.post('/api/background_marketing/recharge/report', param)
  },

  /**
   * ['公告模块']
   * background_messages.messages.add 添加公告
   * @param {MessagesSerializerReq} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesAddPost(param) {
    return http.post('/api/background_messages/messages/add', param)
  },

  /**
   * ['公告模块']
   * background_messages.messages.delete 删除/撤销公告
   * @param {MsgNo} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesDeletePost(param) {
    return http.post('/api/background_messages/messages/delete', param)
  },

  /**
   * ['公告模块']
   * background_messages.messages.details 公告详情
   * @param {MsgNoBase} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesDetailsPost(param) {
    return http.post('/api/background_messages/messages/details', param)
  },

  /**
   * ['公告模块']
   * background_messages.messages.get_msg_list 个人公告列表
   * @param {ListMessagesReq} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesGetMsgListPost(param) {
    return http.post('/api/background_messages/messages/get_msg_list', param)
  },

  /**
   * ['公告模块']
   * background_messages.messages.get_msg_num 未读公告数量
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesGetMsgNumPost(param) {
    return http.post('/api/background_messages/messages/get_msg_num', param)
  },

  /**
   * ['公告模块']
   * background_messages.messages.get_msg_options 已读/未读/删除个人公告
   * @param {MsgOptions} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesGetMsgOptionsPost(param) {
    return http.post('/api/background_messages/messages/get_msg_options', param)
  },

  /**
   * ['公告模块']
   * background_messages.messages.get_msg_receive 个人公告详情
   * @param {MsgNoBase} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesGetMsgReceivePost(param) {
    return http.post('/api/background_messages/messages/get_msg_receive', param)
  },

  /**
   * ['公告模块']
   * background_messages.messages.list 公告列表
   * @param {ListMessagesReq} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesListPost(param) {
    return http.post('/api/background_messages/messages/list', param)
  },

  /**
   * ['公告模块']
   * background_messages.messages.modify 编辑公告
   * @param {MessagesSerializerReq} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesModifyPost(param) {
    return http.post('/api/background_messages/messages/modify', param)
  },

  /**
   * ['订单管理']
   * background_order.order_charge.list 充值订单
   * @param {ListOrderChargeReq} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderChargeListPost(param) {
    return http.post('/api/background_order/order_charge/list', param)
  },

  /**
   * ['订单管理']
   * background_order.order_charge.list_export 充值订单列表导出
   * @param {ListOrderChargeReq} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderChargeListExportPost(param) {
    return http.post('/api/background_order/order_charge/list_export', param)
  },

  /**
   * ['订单管理']
   * background_order.order_charge.refund 充值退款
   * @param {OrderChargeRefundReqSer} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderChargeRefundPost(param) {
    return http.post('/api/background_order/order_charge/refund', param)
  },

  /**
   * ['订单管理']
   * background_order.order_payment.list 消费订单列表
   * @param {ListOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderPaymentListPost(param) {
    return http.post('/api/background_order/order_payment/list', param)
  },

  /**
   * ['订单管理']
   * background_order.order_payment.list_export 消费订单表导出
   * @param {ListOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderPaymentListExportPost(param) {
    return http.post('/api/background_order/order_payment/list_export', param)
  },

  /**
   * ['订单管理']
   * background_order.order_refund.charge_list 充值退款列表
   * @param {OrderRefundChargeListReqSer} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderRefundChargeListPost(param) {
    return http.post('/api/background_order/order_refund/charge_list', param)
  },

  /**
   * ['订单管理']
   * background_order.order_refund.list 退款列表
   * @param {OrderRefundListReqSer} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderRefundListPost(param) {
    return http.post('/api/background_order/order_refund/list', param)
  },
  /**
   * ['订单管理']
   * background_order.order_refund.reservation_list 预约订单退款列表
   * @param {OrderRefundReservationListReqSer} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderRefundReservationListPost(param) {
    return http.post('/api/background_order/order_refund/reservation_list', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.order_reservation.add 创建消费订单
   * @param {AddOrderUnifiedRes} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderReservationAddPost(param) {
    return http.post('/api/background_order/order_reservation/add', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.order_reservation.collect_list 预约订单汇总
   * @param {ReservationListCollectOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderReservationCollectListPost(param) {
    return http.post('/api/background_order/order_reservation/collect_list', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.order_reservation.food_collect_list 预约订单菜品汇总
   * @param {ReservationListFoodCollectOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderReservationFoodCollectListPost(param) {
    return http.post('/api/background_order/order_reservation/food_collect_list', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.order_reservation.group_collect_list 部门点餐汇总
   * @param {ReservationListGroupCollectOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderReservationGroupCollectListPost(param) {
    return http.post('/api/background_order/order_reservation/group_collect_list', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.order_reservation.info_list 预约订单明细列表
   * @param {ReservationListInfoOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderReservationInfoListPost(param) {
    return http.post('/api/background_order/order_reservation/info_list', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.order_reservation.list_export 预约订单表导出
   * @param {ListOrderPaymentRspo} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderReservationListExportPost(param) {
    return http.post('/api/background_order/order_reservation/list_export', param)
  },

  /**
   * ['订单管理 菜品订单']
   * background_order.payment_food_record.add PaymentFoodRecord新增
   * @param {AddPaymentFoodRecordReq} param
   * @returns {Object}
   */
  apiBackgroundOrderPaymentFoodRecordAddPost(param) {
    return http.post('/api/background_order/payment_food_record/add', param)
  },

  /**
   * ['订单管理 菜品订单']
   * background_order.payment_food_record.delete PaymentFoodRecord删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundOrderPaymentFoodRecordDeletePost(param) {
    return http.post('/api/background_order/payment_food_record/delete', param)
  },

  /**
   * ['订单管理 菜品订单']
   * background_order.payment_food_record.list PaymentFoodRecord列表
   * @param {ListPaymentFoodRecordReq} param
   * @returns {Object}
   */
  apiBackgroundOrderPaymentFoodRecordListPost(param) {
    return http.post('/api/background_order/payment_food_record/list', param)
  },

  /**
   * ['订单管理 菜品订单']
   * background_order.payment_food_record.list_export PaymentFoodRecord列表导出
   * @param {ListPaymentFoodRecordReq} param
   * @returns {Object}
   */
  apiBackgroundOrderPaymentFoodRecordListExportPost(param) {
    return http.post('/api/background_order/payment_food_record/list_export', param)
  },

  /**
   * ['订单管理 菜品订单']
   * background_order.payment_food_record.modify PaymentFoodRecord更改
   * @param {ModifyPaymentFoodRecordReq} param
   * @returns {Object}
   */
  apiBackgroundOrderPaymentFoodRecordModifyPost(param) {
    return http.post('/api/background_order/payment_food_record/modify', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.reservation_order.add 创建消费订单
   * @param {AddOrderUnifiedRes} param
   * @returns {Object}
   */
  apiBackgroundOrderReservationOrderAddPost(param) {
    return http.post('/api/background_order/reservation_order/add', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.reservation_order.collect_list 预约订单汇总
   * @param {ReservationListCollectOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderReservationOrderCollectListPost(param) {
    return http.post('/api/background_order/reservation_order/collect_list', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.reservation_order.food_collect_list 预约订单菜品汇总
   * @param {ReservationListFoodCollectOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderReservationOrderFoodCollectListPost(param) {
    return http.post('/api/background_order/reservation_order/food_collect_list', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.reservation_order.group_collect_list 部门点餐汇总
   * @param {ReservationListGroupCollectOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderReservationOrderGroupCollectListPost(param) {
    return http.post('/api/background_order/reservation_order/group_collect_list', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.reservation_order.info_list 预约订单明细列表
   * @param {ReservationListInfoOrderPaymentReq} param
   * @returns {Object}
   */
  apiBackgroundOrderReservationOrderInfoListPost(param) {
    return http.post('/api/background_order/reservation_order/info_list', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.reservation_order.list_export 预约订单表导出
   * @param {ListOrderPaymentRspo} param
   * @returns {Object}
   */
  apiBackgroundOrderReservationOrderListExportPost(param) {
    return http.post('/api/background_order/reservation_order/list_export', param)
  },

  /**
   * ['订单管理 预约订单']
   * background_order.reservation_order.payment_food_record_list 菜品筛选
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundOrderOrderReservationPaymentFoodRecordList(param) {
    return http.post('/api/background_order/reservation_order/payment_food_record_list', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.account.add 账号新增
   * @param {Account} param
   * @returns {Object}
   */
  apiBackgroundOrganizationAccountAddPost(param) {
    return http.post('/api/background_organization/account/add', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.account.delete 账号删除 其实只是禁用
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundOrganizationAccountDeletePost(param) {
    return http.post('/api/background_organization/account/delete', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.account.list 账号列表  支持模糊、综合、精确搜索 状态默认显示请选择，下拉显示：全部、启用、停用
   * @param {ListAccountReq} param
   * @returns {Object}
   */
  apiBackgroundOrganizationAccountListPost(param) {
    return http.post('/api/background_organization/account/list', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.account.list_export 账号列表导出
   * @param {ListAccountReq} param
   * @returns {Object}
   */
  apiBackgroundOrganizationAccountListExportPost(param) {
    return http.post('/api/background_organization/account/list_export', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.account.modify 账号更改
   * @param {Account} param
   * @returns {Object}
   */
  apiBackgroundOrganizationAccountModifyPost(param) {
    return http.post('/api/background_organization/account/modify', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.add 组织新增 :添加组织：只能向下添加下级层级，比如：区域添加食堂，但无法添加与自己一样的区域；
   * @param {AddOrganizationInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationAddPost(param) {
    return http.post('/api/background_organization/organization/add', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.consume_list 消费点接口列表
   * @param {ListOrganizationReq} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationConsumeListPost(param) {
    return http.post('/api/background_organization/organization/consume_list', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.delete 组织删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationDeletePost(param) {
    return http.post('/api/background_organization/organization/delete', param)
  },

  /**
   * ['商户 系统管理 常规设置']
   * background_organization.organization.get_common_settings 获取常规设置
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationGetCommonSettingsPost(param) {
    return http.post('/api/background_organization/organization/get_common_settings', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.get_info 组织基本信息
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationGetInfoPost(param) {
    return http.post('/api/background_organization/organization/get_info', param)
  },

  /**
   * ['商户 系统管理 组织管理', '超管 商户管理']
   * background_organization.organization.get_level_name_map 获取指定公司的层级列表
   * @param {GetLevelNameMapReqSer} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationGetLevelNameMapPost(param) {
    return http.post('/api/background_organization/organization/get_level_name_map', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.get_settings 获取组织高级设置-扣款设置
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationGetSettingsPost(param) {
    return http.post('/api/background_organization/organization/get_settings', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.list 组织列表
   * @param {ListOrganizationReq} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationListPost(param) {
    return http.post('/api/background_organization/organization/list', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.modify 组织更改  :只能修改自己以及下级层级，平级以及上级层级无法编辑修改，集团公司层级角色可对所有层级进行编辑；
   * @param {ModifyOrganizationInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationModifyPost(param) {
    return http.post('/api/background_organization/organization/modify', param)
  },

  /**
   * ['商户 系统管理 常规设置']
   * background_organization.organization.modify_common_settings 修改常规设置
   * @param {ModifyOrganizationCommonSettingsRsp} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationModifyCommonSettingsPost(param) {
    return http.post('/api/background_organization/organization/modify_common_settings', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.modify_level_name 修改层级名称
   * @param {ModifyOrganizationLevelName} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationModifyLevelNamePost(param) {
    return http.post('/api/background_organization/organization/modify_level_name', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.modify_settings 修改组织高级设置-扣款设置
   * @param {OrganizationSettings} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationModifySettingsPost(param) {
    return http.post('/api/background_organization/organization/modify_settings', param)
  },

  /**
   * ['商户 系统管理 组织管理']
   * background_organization.organization.tree_list 组织树列表  返回 包含 children_list 的组织列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundOrganizationOrganizationTreeListPost(param) {
    return http.post('/api/background_organization/organization/tree_list', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.role.account_list 查看角色账号列表
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundOrganizationRoleAccountListPost(param) {
    return http.post('/api/background_organization/role/account_list', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.role.add 角色新增  添加角色必填，可创建多个角可创建多个角色色，但只能添加在一个层级上
   * @param {Role} param
   * @returns {Object}
   */
  apiBackgroundOrganizationRoleAddPost(param) {
    return http.post('/api/background_organization/role/add', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.role.delete 角色删除  删除：只能删除自己下级层级以及自己， 无法删除比自己更高层级以及同层级的组织， 但该层级如已有账号关联则无法删除，集团公司角色只有集团公司角色进行删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundOrganizationRoleDeletePost(param) {
    return http.post('/api/background_organization/role/delete', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.role.list 角色列表  1：显示已创建组织架构层级； 2：集团公司显示全部的组织架构层级；其余层级只显示自己以及自己下级层级的组织架构 3：默认显示全部角色，点击对应层级显示该层级所有角色
   * @param {ListRoleReq} param
   * @returns {Object}
   */
  apiBackgroundOrganizationRoleListPost(param) {
    return http.post('/api/background_organization/role/list', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.role.list_export 角色列表导出
   * @param {ListRoleReq} param
   * @returns {Object}
   */
  apiBackgroundOrganizationRoleListExportPost(param) {
    return http.post('/api/background_organization/role/list_export', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.role.modify 角色更改
   * @param {Role} param
   * @returns {Object}
   */
  apiBackgroundOrganizationRoleModifyPost(param) {
    return http.post('/api/background_organization/role/modify', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.role.permission_list 获取角色权限列表  集团公司级层级人员（超管）拥有所有权限 权限设置显示已开发功能菜单，只区分1级与2级菜单，需√上才能启用操作 筛选：页面默认显示全部功能，可下拉筛选功能选择进行对一级功能菜单进行筛选
   * @param {Id} param
   * @returns {Object}
   */
  apiBackgroundOrganizationRolePermissionListPost(param) {
    return http.post('/api/background_organization/role/permission_list', param)
  },

  /**
   * ['商户 系统管理']
   * background_organization.role.set_permission 设置角色权限  只能设置比自己低层级的权限，不能设置自己、平级以及上级权限；
   * @param {SetPermissionKeyReq} param
   * @returns {Object}
   */
  apiBackgroundOrganizationRoleSetPermissionPost(param) {
    return http.post('/api/background_organization/role/set_permission', param)
  },
  /**
   * ['商户 系统管理'] 移动端
   * background_organization.role.set_permission 设置角色权限  只能设置比自己低层级的权限，不能设置自己、平级以及上级权限；
   * @param {SetPermissionKeyReq} param
   * @returns {Object}
   */
  apiBackgroundOrganizationRoleSetPermissionMobilePost(param) {
    return http.post('/api/background_organization/role/set_merchant_mobile_permission', param)
  },

  /**
   * ['商户 系统管理 支付配置']
   * background_payment.pay_info.delete 支付配置删除-不支持
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundPaymentPayInfoDeletePost(param) {
    return http.post('/api/background_payment/pay_info/delete', param)
  },

  /**
   * ['商户 系统管理 支付配置']
   * background_payment.pay_info.get_order_payinfos (传参: pay_scene='online' 可以查询钱包的支付顺序列表) :param request: :return:
   * @param {BindPayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundPaymentPayInfoGetOrderPayinfosPost(param) {
    return http.post('/api/background_payment/pay_info/get_order_payinfos', param)
  },

  /**
   * ['商户 系统管理 支付配置']
   * background_payment.pay_info.set_order_payinfos (传参: pay_scene='online' 可以查询钱包的支付顺序列表) :param request: :return:
   * @param {MultiPayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundPaymentPayInfoSetOrderPayinfosPost(param) {
    return http.post('/api/background_payment/pay_info/set_order_payinfos', param)
  },

  /**
   * ['商户 系统管理 支付配置']
   * background_payment.pay_info.sub_orgs_all_list 支付配置-获取组织的全部支付配置列表
   * @param {BindPayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundPaymentPayInfoSubOrgsAllListPost(param) {
    return http.post('/api/background_payment/pay_info/sub_orgs_all_list', param)
  },

  /**
   * ['商户 系统管理 支付配置']
   * background_payment.pay_info.sub_orgs_bind 支付设置-设置子组织绑定关联支付配置 :param request: :return:
   * @param {BindPayInfoReqSer} param
   * @returns {Object}
   */
  apiBackgroundPaymentPayInfoSubOrgsBindPost(param) {
    return http.post('/api/background_payment/pay_info/sub_orgs_bind', param)
  },

  /**
   * ['商户 系统管理 支付配置']
   * background_payment.pay_info.template_list 支付配置-模板列表
   * @param {PaySceneReqSer} param
   * @returns {Object}
   */
  apiBackgroundPaymentPayInfoTemplateListPost(param) {
    return http.post('/api/background_payment/pay_info/template_list', param)
  },

  /**
   * ['报表中心 数据报表']
   * background_report_center.data_report.device_consume_list 设备消费明细表
   * @param {DeviceConsumeReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportDeviceConsumeListPost(param) {
    return http.post('/api/background_report_center/data_report/device_consume_list', param)
  },

  /**
   * ['报表中心 数据报表']
   * background_report_center.data_report.device_consume_list_export 导出设备消费明细表
   * @param {DeviceConsumeReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportDeviceConsumeListExportPost(param) {
    return http.post('/api/background_report_center/data_report/device_consume_list_export', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.get_level_name 获取指定公司的层级列表，根据层级获取
   * @param {GetLevelNameMapReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportGetLevelNamePost(param) {
    return http.post('/api/background_report_center/data_report/get_level_name', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.get_pay_info 根据项目点获取支付方式
   * @param {GetLevelNameMapReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportGetPayInfoPost(param) {
    return http.post('/api/background_report_center/data_report/get_pay_info', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.payment_order_detail_list 消费明细表
   * @param {ConsumeDetailReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportPaymentOrderDetailListPost(param) {
    return http.post('/api/background_report_center/data_report/payment_order_detail_list', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.payment_order_detail_list_export 消费明细表导出
   * @param {ConsumeDetailReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportPaymentOrderDetailListExportPost(param) {
    return http.post(
      '/api/background_report_center/data_report/payment_order_detail_list_export',
      param
    )
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.pecharge_order_list 充值记录明细
   * @param {PechargeRecordReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportPechargeOrderListPost(param) {
    return http.post('/api/background_report_center/data_report/pecharge_order_list', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.pecharge_order_list_export 充值记录明细导出
   * @param {PechargeRecordReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportPechargeOrderListExportPost(param) {
    return http.post('/api/background_report_center/data_report/pecharge_order_list_export', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.reconciliation_list_export 消费点对账表导出
   * @param {ReconciliationReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportReconciliationListExportPost(param) {
    return http.post('/api/background_report_center/data_report/reconciliation_list_export', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.reconciliation_statement 消费点对账表
   * @param {ReconciliationReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportReconciliationStatementPost(param) {
    return http.post('/api/background_report_center/data_report/reconciliation_statement', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.unified_order_list 明细总表
   * @param {UnifiedReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportUnifiedOrderListPost(param) {
    return http.post('/api/background_report_center/data_report/unified_order_list', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.unified_order_list_export 明细总表导出
   * @param {ConsumeDetailReqSer} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportUnifiedOrderListExportPost(param) {
    return http.post('/api/background_report_center/data_report/unified_order_list_export', param)
  },

  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.wallet_list 动账钱包筛选请求
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundReportCenterDataReportWalletListPost(param) {
    return http.post('/api/background_report_center/data_report/wallet_list', param)
  },
  /**
   * ['报表中心 财务报表']
   * background_report_center.data_report.wallet_list 列表组织显示请求
   * @param {Empty} param
   * @returns {Object}
   */
  apiBackgroundGetlevelNameListPost(param) {
    return http.post('/api/background_report_center/data_report/get_level_name', param)
  },

  /**
   * ['商户 系统管理 用餐管理']
   * background_reservation.background_reservation_settings.add 后台预约设置新增
   * @param {AddBackgroundReservationSettingsReq} param
   * @returns {Object}
   */
  apiBackgroundReservationBackgroundReservationSettingsAddPost(param) {
    return http.post('/api/background_reservation/background_reservation_settings/add', param)
  },

  /**
   * ['商户 系统管理 用餐管理']
   * background_reservation.background_reservation_settings.delete 后台预约设置删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiBackgroundReservationBackgroundReservationSettingsDeletePost(param) {
    return http.post('/api/background_reservation/background_reservation_settings/delete', param)
  },

  /**
   * ['商户 系统管理 用餐管理']
   * background_reservation.background_reservation_settings.list 后台预约设置列表
   * @param {FilterReq} param
   * @returns {Object}
   */
  apiBackgroundReservationBackgroundReservationSettingsListPost(param) {
    return http.post('/api/background_reservation/background_reservation_settings/list', param)
  },

  /**
   * ['商户 系统管理 用餐管理']
   * background_reservation.background_reservation_settings.list_export 后台预约设置列表导出
   * @param {ListBackgroundReservationSettingsReq} param
   * @returns {Object}
   */
  apiBackgroundReservationBackgroundReservationSettingsListExportPost(param) {
    return http.post(
      '/api/background_reservation/background_reservation_settings/list_export',
      param
    )
  },

  /**
   * ['商户 系统管理 用餐管理']
   * background_reservation.background_reservation_settings.modify 后台预约设置更改
   * @param {ModifyBackgroundReservationSettingsReq} param
   * @returns {Object}
   */
  apiBackgroundReservationBackgroundReservationSettingsModifyPost(param) {
    return http.post('/api/background_reservation/background_reservation_settings/modify', param)
  },

  /**
   * ['商户 系统管理 用餐管理']
   * background_reservation.background_reservation_settings.modify_open_status 后台预约设置修改开关状态
   * @param {ModifyBackgroundReservationSettingsOpenReq} param
   * @returns {Object}
   */
  apiBackgroundReservationBackgroundReservationSettingsModifyOpenStatusPost(param) {
    return http.post(
      '/api/background_reservation/background_reservation_settings/modify_open_status',
      param
    )
  },
  /**
   * ['商户 系统管理 用餐管理']
   * background_reservation.background_reservation_settings.reservation_organization_check 检查当前组织是否与后台设置的预约组织等级相同
   * @param {ModifyBackgroundReservationSettingsOpenReq} param
   * @returns {Object}
   */
  apiBackgroundReservationBackgroundReservationSettingsReservationOrganizationCheckPost(param) {
    return http.post(
      '/api/background_reservation/background_reservation_settings/reservation_organization_check',
      param
    )
  },

  /**
   * ['移动端 登录']
   * booking.login.h5_login H5登录接口
   * @param {H5Login} param
   * @returns {Object}
   */
  apiBookingLoginH5LoginPost(param) {
    return http.post('/api/booking/login/h5_login', param)
  },

  /**
   * ['移动端 登录']
   * booking.login.phone 小程序手机号登录接口
   * @param {PhoneLoginReqSer} param
   * @returns {Object}
   */
  apiBookingLoginPhonePost(param) {
    return http.post('/api/booking/login/phone', param)
  },

  /**
   * ['移动端 用户']
   * booking.login.query_userinfo 查询用户信息
   * @param {Empty} param
   * @returns {Object}
   */
  apiBookingLoginQueryUserinfoPost(param) {
    return http.post('/api/booking/login/query_userinfo', param)
  },

  /**
   * ['移动端 购物车']
   * booking.shopcard.add 添加购物车
   * @param {ShopcardAdd} param
   * @returns {Object}
   */
  apiBookingShopcardAddPost(param) {
    return http.post('/api/booking/shopcard/add', param)
  },

  /**
   * ['移动端 购物车']
   * booking.shopcard.clean 清空全部购物车
   * @param {Shopcard} param
   * @returns {Object}
   */
  apiBookingShopcardCleanPost(param) {
    return http.post('/api/booking/shopcard/clean', param)
  },

  /**
   * ['移动端 购物车']
   * booking.shopcard.delete 删除购物车菜品
   * @param {ShopcardClean} param
   * @returns {Object}
   */
  apiBookingShopcardDeletePost(param) {
    return http.post('/api/booking/shopcard/delete', param)
  },

  /**
   * ['移动端 购物车']
   * booking.shopcard.list 获取购物车
   * @param {Shopcard} param
   * @returns {Object}
   */
  apiBookingShopcardListPost(param) {
    return http.post('/api/booking/shopcard/list', param)
  },

  /**
   * ['移动端 短信']
   * booking.sms.send_validate_code 发送验证码
   * @param {PhoneReqSer} param
   * @returns {Object}
   */
  apiBookingSmsSendValidateCodePost(param) {
    return http.post('/api/booking/sms/send_validate_code', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.bind_project_point 绑定项目点
   * @param {UserBindORCreateReqSer} param
   * @returns {Object}
   */
  apiBookingUserBindProjectPointPost(param) {
    return http.post('/api/booking/user/bind_project_point', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.card_user_or_wechat_user_info_save 保存项目点个人信息或微信用户个人信息
   * @param {SaveCardInfoORWechatUserInfoReqSer} param
   * @returns {Object}
   */
  apiBookingUserCardUserOrWechatUserInfoSavePost(param) {
    return http.post('/api/booking/user/card_user_or_wechat_user_info_save', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.collection_reservation_order 查看汇总订单
   * @param {MonthCollectReservationPageReqSer} param
   * @returns {Object}
   */
  apiBookingUserCollectionReservationOrderPost(param) {
    return http.post('/api/booking/user/collection_reservation_order', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.create_card_info 移动端用户创建用户
   * @param {UserBindORCreateReqSer} param
   * @returns {Object}
   */
  apiBookingUserCreateCardInfoPost(param) {
    return http.post('/api/booking/user/create_card_info', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.generate_eaccount_code 获取项目点用户电子码
   * @param {Empty} param
   * @returns {Object}
   */
  apiBookingUserGenerateEaccountCodePost(param) {
    return http.post('/api/booking/user/generate_eaccount_code', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.get_reservation_stall_food 获取食堂下的档口
   * @param {StallListReqSer} param
   * @returns {Object}
   */
  apiBookingUserGetReservationStallFoodPost(param) {
    return http.post('/api/booking/user/get_reservation_stall_food', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.get_wechat_user_info 获取微信用户的基本信息
   * @param {Empty} param
   * @returns {Object}
   */
  apiBookingUserGetWechatUserInfoPost(param) {
    return http.post('/api/booking/user/get_wechat_user_info', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.logout 登出
   * @param {Empty} param
   * @returns {Object}
   */
  apiBookingUserLogoutPost(param) {
    return http.post('/api/booking/user/logout', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.month_reservation_order 查看用户一个月预约订单详情
   * @param {MonthReservationInfoPageReqSer} param
   * @returns {Object}
   */
  apiBookingUserMonthReservationOrderPost(param) {
    return http.post('/api/booking/user/month_reservation_order', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.project_list 获取项目点
   * @param {ComapanyReqSer} param
   * @returns {Object}
   */
  apiBookingUserProjectListPost(param) {
    return http.post('/api/booking/user/project_list', param)
  },

  /**
   * ['移动端 用户']
   * booking.user.reservation_order_add 预约下单接口
   * @param {ReservationOrderAddReqSer} param
   * @returns {Object}
   */
  apiBookingUserReservationOrderAddPost(param) {
    return http.post('/api/booking/user/reservation_order_add', param)
  },

  /**
   * ['移动端 微信小程序']
   * booking.wechat.miniapp_auth 微信验证接口 https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
   * @param {WechatAuthReqSer} param
   * @returns {Object}
   */
  apiBookingWechatMiniappAuthPost(param) {
    return http.post('/api/booking/wechat/miniapp_auth', param)
  },

  /**
   * ['移动端 微信小程序']
   * booking.wechat.miniapp_phone_login 小程序微信手机号一键登录https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/phonenumber/phonenumber.getPhoneNumber.html
   * @param {WechatAuthReqSer} param
   * @returns {Object}
   */
  apiBookingWechatMiniappPhoneLoginPost(param) {
    return http.post('/api/booking/wechat/miniapp_phone_login', param)
  },

  /**
   * ['商户 用户中心 部门']
   * card_service.card_department_group.add 部门分组新增
   * @param {AddCardDepartmentGroup} param
   * @returns {Object}
   */
  apiCardServiceCardDepartmentGroupAddPost(param) {
    return http.post('/api/card_service/card_department_group/add', param)
  },

  /**
   * ['商户 用户中心 部门']
   * card_service.card_department_group.batch_import 批量导入部门 只是单纯的导入分组
   * @param {BatchImportGroupReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardDepartmentGroupBatchImportPost(param) {
    return http.post('/api/card_service/card_department_group/batch_import', param)
  },

  /**
   * ['商户 用户中心 部门']
   * card_service.card_department_group.delete 部门分组删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiCardServiceCardDepartmentGroupDeletePost(param) {
    return http.post('/api/card_service/card_department_group/delete', param)
  },

  /**
   * ['商户 用户中心 部门']
   * card_service.card_department_group.list 部门分组列表
   * @param {ListCardDepartmentGroupReq} param
   * @returns {Object}
   */
  apiCardServiceCardDepartmentGroupListPost(param) {
    return http.post('/api/card_service/card_department_group/list', param)
  },

  /**
   * ['商户 用户中心 部门']
   * card_service.card_department_group.modify 部门分组编辑
   * @param {ModifyCardDepartmentGroup} param
   * @returns {Object}
   */
  apiCardServiceCardDepartmentGroupModifyPost(param) {
    return http.post('/api/card_service/card_department_group/modify', param)
  },

  /**
   * ['商户 用户中心 部门']
   * card_service.card_department_group.tree_list 部门树列表
   * @param {Empty} param
   * @returns {Object}
   */
  apiCardServiceCardDepartmentGroupTreeListPost(param) {
    return http.post('/api/card_service/card_department_group/tree_list', param)
  },

  /**
   * ['商户 用户中心']
   * card_service.card_log.get_card_operation_list 获取卡务操作类型树 :param request: :return:
   * @param {Empty} param
   * @returns {Object}
   */
  apiCardServiceCardLogGetCardOperationListPost(param) {
    return http.post('/api/card_service/card_log/get_card_operation_list', param)
  },

  /**
   * ['商户 用户中心']
   * card_service.card_log.get_card_operation_log_list 获取卡务操作日志信息列表 :param request: :return:
   * @param {CardOperationLogListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardLogGetCardOperationLogListPost(param) {
    return http.post('/api/card_service/card_log/get_card_operation_log_list', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_operate.assign_org 用户下发组织
   * @param {AssignOrgReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateAssignOrgPost(param) {
    return http.post('/api/card_service/card_operate/assign_org', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.batch_delay_flat_cost 批量扣费与扣工本费
   * @param {CardUserIdsReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateBatchDelayFlatCostPost(param) {
    return http.post('/api/card_service/card_operate/batch_delay_flat_cost', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.batch_import_publish_user_card 批量发卡
   * @param {ImportReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateBatchImportPublishUserCardPost(param) {
    return http.post('/api/card_service/card_operate/batch_import_publish_user_card', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.batch_import_user_consume 导入消费 从excel表中批量导入用户消费 :param request: :return:
   * @param {ImportReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateBatchImportUserConsumePost(param) {
    return http.post('/api/card_service/card_operate/batch_import_user_consume', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.batch_import_user_draw 导入取款 从excel表中批量导入用户取款 :param request: :return:
   * @param {ImportReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateBatchImportUserDrawPost(param) {
    return http.post('/api/card_service/card_operate/batch_import_user_draw', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.batch_import_user_recharge 导入充值 从excel表中批量导入用户充值 :param request: :return:
   * @param {ImportReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateBatchImportUserRechargePost(param) {
    return http.post('/api/card_service/card_operate/batch_import_user_recharge', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.batch_recharge 批量充值
   * @param {BatchRechargeReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateBatchRechargePost(param) {
    return http.post('/api/card_service/card_operate/batch_recharge', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_operate.cancel_loss 取消挂失
   * @param {CardUserIdReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateCancelLossPost(param) {
    return http.post('/api/card_service/card_operate/cancel_loss', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.cancel_person_quit 取消退户
   * @param {CardUserIdReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateCancelPersonQuitPost(param) {
    return http.post('/api/card_service/card_operate/cancel_person_quit', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_operate.card_quit 退卡 取款退卡的意思: 人还在,卡不在了，钱也被清了; 这种情况意思是人还能用，只是余额不足了 退卡的意思: 人还在,卡不在, 钱还在; 这种情况意思是人还能用，也有余额， 只是刷不卡
   * @param {CardUserIdReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateCardQuitPost(param) {
    return http.post('/api/card_service/card_operate/card_quit', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_operate.change 补卡 挂失的卡才能换卡(补卡)。支付补卡费。
   * @param {ChangeReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateChangePost(param) {
    return http.post('/api/card_service/card_operate/change', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.charge 冲销
   * @param {ChargeReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateChargePost(param) {
    return http.post('/api/card_service/card_operate/charge', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_operate.loss 挂失
   * @param {CardUserIdReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateLossPost(param) {
    return http.post('/api/card_service/card_operate/loss', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.person_quit 退户
   * @param {PersonQuitReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperatePersonQuitPost(param) {
    return http.post('/api/card_service/card_operate/person_quit', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_operate.publish 发卡 目前只有退卡了才能发卡（要扣开卡费）。 :param request: :return:
   * @param {PublishReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperatePublishPost(param) {
    return http.post('/api/card_service/card_operate/publish', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.recharge 充值
   * @param {RechargeReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateRechargePost(param) {
    return http.post('/api/card_service/card_operate/recharge', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.refund 退款
   * @param {CardUserRefundReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateRefundPost(param) {
    return http.post('/api/card_service/card_operate/refund', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_operate.withdrawal 取款/提现
   * @param {WithDrawalReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardOperateWithdrawalPost(param) {
    return http.post('/api/card_service/card_operate/withdrawal', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.add 增加补贴
   * @param {AddSubsidyReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyAddPost(param) {
    return http.post('/api/card_service/card_subsidy/add', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.bulk_clear_subsidy 批量清零补贴
   * @param {Ids} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyBulkClearSubsidyPost(param) {
    return http.post('/api/card_service/card_subsidy/bulk_clear_subsidy', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.clear_subsidy 补贴清零
   * @param {Id} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyClearSubsidyPost(param) {
    return http.post('/api/card_service/card_subsidy/clear_subsidy', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.clear_user_subsidy 批量清除用户的补贴 :param request: :return:
   * @param {ClearUserSubsidyReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyClearUserSubsidyPost(param) {
    return http.post('/api/card_service/card_subsidy/clear_user_subsidy', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.delete 删除补贴
   * @param {Ids} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyDeletePost(param) {
    return http.post('/api/card_service/card_subsidy/delete', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.get_card_subsidy 获取卡补贴信息
   * @param {Id} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyGetCardSubsidyPost(param) {
    return http.post('/api/card_service/card_subsidy/get_card_subsidy', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.info_list 补贴明细
   * @param {CardSubsidyInfoListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyInfoListPost(param) {
    return http.post('/api/card_service/card_subsidy/info_list', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.info_list_export 补贴明细详情导出
   * @param {CardSubsidyInfoListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyInfoListExportPost(param) {
    return http.post('/api/card_service/card_subsidy/info_list_export', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.list 补贴管理
   * @param {CardSubsidyListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyListPost(param) {
    return http.post('/api/card_service/card_subsidy/list', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.list_export 补贴列表导出
   * @param {CardSubsidyListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyListExportPost(param) {
    return http.post('/api/card_service/card_subsidy/list_export', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.modify 编辑补贴
   * @param {ModifySubsidyReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyModifyPost(param) {
    return http.post('/api/card_service/card_subsidy/modify', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.release 发放补贴
   * @param {Id} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyReleasePost(param) {
    return http.post('/api/card_service/card_subsidy/release', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.stop_release_subsidy 停止发放补贴
   * @param {Id} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyStopReleaseSubsidyPost(param) {
    return http.post('/api/card_service/card_subsidy/stop_release_subsidy', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.subsidy_status_list 获取补贴状态枚举 返回 Key Value Map
   * @param {Empty} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidySubsidyStatusListPost(param) {
    return http.post('/api/card_service/card_subsidy/subsidy_status_list', param)
  },

  /**
   * ['商户 用户中心 补贴管理']
   * card_service.card_subsidy.user_list 补贴用户列表 1.同一个用户，同一时间内只有一笔补贴时，可以统计已使用金额、未使用金额； 2.创建一笔补贴时，系统自动过滤掉当前已有在执行的同一个用户。
   * @param {CardSubsidyUserListRepSer} param
   * @returns {Object}
   */
  apiCardServiceCardSubsidyUserListPost(param) {
    return http.post('/api/card_service/card_subsidy/user_list', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_user.account_list 账户管理 用来看补贴跟金额的 :param request: :return:
   * @param {CardAccountListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserAccountListPost(param) {
    return http.post('/api/card_service/card_user/account_list', param)
  },

  /**
   * ['商户 用户中心 账户管理']
   * card_service.card_user.account_list_export 导出账户记录 用来看补贴跟金额的 :param request: :return:
   * @param {CardAccountListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserAccountListExportPost(param) {
    return http.post('/api/card_service/card_user/account_list_export', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.add 新增卡用户
   * @param {AddCardUserReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserAddPost(param) {
    return http.post('/api/card_service/card_user/add', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.batch_freeze_card_user 批量冻结用户
   * @param {BatchFreezeCardUserReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserBatchFreezeCardUserPost(param) {
    return http.post('/api/card_service/card_user/batch_freeze_card_user', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.batch_import 导入用户 从excel表中批量导入用户
   * @param {ImportCardUserReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserBatchImportPost(param) {
    return http.post('/api/card_service/card_user/batch_import', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.batch_set_card_user_group 批量设置用户分组
   * @param {BatchSetCardUserGroupReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserBatchSetCardUserGroupPost(param) {
    return http.post('/api/card_service/card_user/batch_set_card_user_group', param)
  },

  /**
   * ['商户 用户中心']
   * card_service.card_user.card_change_list 补卡费列表
   * @param {BaseCardQueryParamsSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserCardChangeListPost(param) {
    return http.post('/api/card_service/card_user/card_change_list', param)
  },

  /**
   * ['商户 用户中心']
   * card_service.card_user.card_loss_list 挂失列表
   * @param {CardUserListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserCardLossListPost(param) {
    return http.post('/api/card_service/card_user/card_loss_list', param)
  },

  /**
   * ['商户 用户中心']
   * card_service.card_user.card_status_list 卡状态列表
   * @param {CardUserListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserCardStatusListPost(param) {
    return http.post('/api/card_service/card_user/card_status_list', param)
  },

  /**
   * ['商户 用户中心 工本费管理']
   * card_service.card_user.flat_cost_list 工本费管理列表
   * @param {FlatCostListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserFlatCostListPost(param) {
    return http.post('/api/card_service/card_user/flat_cost_list', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.import_card_user_quit 导入退卡
   * @param {ImportReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserImportCardUserQuitPost(param) {
    return http.post('/api/card_service/card_user/import_card_user_quit', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.import_modify_card_user 导入编辑 批量编辑用户信息
   * @param {ImportReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserImportModifyCardUserPost(param) {
    return http.post('/api/card_service/card_user/import_modify_card_user', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.import_set_card_user_group 导入分组 导入设置用户分组
   * @param {ImportReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserImportSetCardUserGroupPost(param) {
    return http.post('/api/card_service/card_user/import_set_card_user_group', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.list 卡用户管理 用来看用户的基本信息，发卡挂失的操作列表 :param request: :return:
   * @param {CardUserListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserListPost(param) {
    return http.post('/api/card_service/card_user/list', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.list_export 导出用户
   * @param {CardUserListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserListExportPost(param) {
    return http.post('/api/card_service/card_user/list_export', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.modify 编辑卡用户
   * @param {ModifyCardUserReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserModifyPost(param) {
    return http.post('/api/card_service/card_user/modify', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user.open_wallet 开通用户钱包
   * @param {CardUserIdReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserOpenWalletPost(param) {
    return http.post('/api/card_service/card_user/open_wallet', param)
  },

  /**
   * ['商户 用户中心']
   * card_service.card_user.person_quit_list 退户列表 跟用户列表一样,只是筛选了 person_status 状态
   * @param {CardUserListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserPersonQuitListPost(param) {
    return http.post('/api/card_service/card_user/person_quit_list', param)
  },

  /**
   * ['商户 用户中心']
   * card_service.card_user.person_quit_list_export 导出退户列表
   * @param {CardUserListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserPersonQuitListExportPost(param) {
    return http.post('/api/card_service/card_user/person_quit_list_export', param)
  },

  /**
   * ['商户 用户中心']
   * card_service.card_user.preson_status_list 卡用户状态列表
   * @param {CardUserListReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserPresonStatusListPost(param) {
    return http.post('/api/card_service/card_user/preson_status_list', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user_face.batch_delete 批量删除人脸
   * @param {BatchDeleteCardUserFaceSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserFaceBatchDeletePost(param) {
    return http.post('/api/card_service/card_user_face/batch_delete', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user_face.batch_import 导入人脸 从excel表中批量导入人脸
   * @param {BatchImportFacesFromExcelReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserFaceBatchImportPost(param) {
    return http.post('/api/card_service/card_user_face/batch_import', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user_face.get_switch 获取人脸支付开关
   * @param {Empty} param
   * @returns {Object}
   */
  apiCardServiceCardUserFaceGetSwitchPost(param) {
    return http.post('/api/card_service/card_user_face/get_switch', param)
  },

  /**
   * ['商户 用户中心 用户管理']
   * card_service.card_user_face.switch 批量修改人脸开关
   * @param {ModifyCardUserFacePaySer} param
   * @returns {Object}
   */
  apiCardServiceCardUserFaceSwitchPost(param) {
    return http.post('/api/card_service/card_user_face/switch', param)
  },

  /**
   * ['商户 用户中心 用户分组']
   * card_service.card_user_group.add 用户分组新增
   * @param {AddCardUserGroupReq} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupAddPost(param) {
    return http.post('/api/card_service/card_user_group/add', param)
  },

  /**
   * ['商户 用户中心 用户分组']
   * card_service.card_user_group.batch_import 批量导入用户分组 只是单纯的导入分组
   * @param {BatchImportUserGroupReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupBatchImportPost(param) {
    return http.post('/api/card_service/card_user_group/batch_import', param)
  },

  /**
   * ['商户 用户中心 用户分组']
   * card_service.card_user_group.delete 用户分组删除
   * @param {Ids} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupDeletePost(param) {
    return http.post('/api/card_service/card_user_group/delete', param)
  },

  /**
   * ['商户 账户管理 账户设置']
   * card_service.card_user_group.get_org_debt_settings 获取组织的透支设置
   * @param {Empty} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupGetOrgDebtSettingsPost(param) {
    return http.post('/api/card_service/card_user_group/get_org_debt_settings', param)
  },

  /**
   * ['商户 账户管理 账户设置']
   * card_service.card_user_group.get_org_flat_and_patch_cost_settings 获取组织的开卡/补卡费
   * @param {Empty} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupGetOrgFlatAndPatchCostSettingsPost(param) {
    return http.post(
      '/api/card_service/card_user_group/get_org_flat_and_patch_cost_settings',
      param
    )
  },

  /**
   * ['商户 用户中心 用户分组']
   * card_service.card_user_group.list 用户分组列表 显示上下级,不显示同级
   * @param {ListCardUserGroupReq} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupListPost(param) {
    return http.post('/api/card_service/card_user_group/list', param)
  },

  /**
   * ['商户 用户中心 用户分组']
   * card_service.card_user_group.list_export 用户分组列表导出
   * @param {ListCardUserGroupReq} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupListExportPost(param) {
    return http.post('/api/card_service/card_user_group/list_export', param)
  },

  /**
   * ['商户 用户中心 用户分组']
   * card_service.card_user_group.modify 用户分组更改
   * @param {ModifyCardUserGroupReq} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupModifyPost(param) {
    return http.post('/api/card_service/card_user_group/modify', param)
  },

  /**
   * ['商户 用户中心 账户管理 账户列表']
   * card_service.card_user_group.modify 本组织剩余充值次数
   * @param {} param
   * @returns {Object}
   */
  apiCardServiceCardOperateGetChargeLimit(param) {
    return http.post('/api/card_service/card_operate/get_charge_limit', param)
  },

  /**
   * ['商户 账户管理 账户设置']
   * card_service.card_user_group.modify_flat_and_patch_cost 修改分组开卡/补卡费
   * @param {ModifyFlatAndPatchCostReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupModifyFlatAndPatchCostPost(param) {
    return http.post('/api/card_service/card_user_group/modify_flat_and_patch_cost', param)
  },

  /**
   * ['商户 账户管理 账户设置']
   * card_service.card_user_group.modify_group_debt_money 修改分组透支设置
   * @param {ModifyGroupDebtMoneyReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupModifyGroupDebtMoneyPost(param) {
    return http.post('/api/card_service/card_user_group/modify_group_debt_money', param)
  },

  /**
   * ['商户 账户管理 账户设置']
   * card_service.card_user_group.modify_org_debt_money 组织透支设置
   * @param {ModifyOrgDebtMoneyReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupModifyOrgDebtMoneyPost(param) {
    return http.post('/api/card_service/card_user_group/modify_org_debt_money', param)
  },

  /**
   * ['商户 账户管理 账户设置']
   * card_service.card_user_group.modify_org_flat_and_patch_cost 修改分组开卡/补卡费
   * @param {ModifyOrgFlatAndPatchCostReqSer} param
   * @returns {Object}
   */
  apiCardServiceCardUserGroupModifyOrgFlatAndPatchCostPost(param) {
    return http.post('/api/card_service/card_user_group/modify_org_flat_and_patch_cost', param)
  },

  /**
   * ['基础接口']
   * client_codegenerator.choices 所有枚举类的 只是告诉前端 枚举类含义
   * @param {RspCodeChoicesSer} param
   * @returns {Object}
   */
  apiClientCodegeneratorChoicesPost(param) {
    return http.post('/api/client_codegenerator/choices', param)
  },

  /**
   * ['多语言切换']
   * i18n.set_language 设置语言环境
   * @param {SetLanguage} param
   * @returns {Object}
   */
  apiI18NSetLanguagePost(param) {
    return http.post('/api/i18n/set_language', param)
  },

  /**
   * ['超管 用户管理']
   * user.admin.user_info 用户详情 账户信息 关联信息
   * @param {Id} param
   * @returns {Object}
   */
  apiUserAdminUserInfoPost(param) {
    return http.post('/api/user/admin/user_info', param)
  },

  /**
   * ['超管 用户管理']
   * user.admin.user_list 全局用户列表
   * @param {AdminUserListReqSer} param
   * @returns {Object}
   */
  apiUserAdminUserListPost(param) {
    return http.post('/api/user/admin/user_list', param)
  },

  /**
   * ['商户 获取公告弹窗消息']
   * user.admin.user_list 全局用户列表
   * @param {AdminUserListReqSer} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesGetPopMsg(param) {
    return http.post('/api/background_messages/messages/get_pop_msg', param)
  },

  /**
   * ['商户 关闭公告弹窗消息']
   * user.admin.user_list 全局用户列表
   * @param {AdminUserListReqSer} param
   * @returns {Object}
   */
  apiBackgroundMessagesMessagesBulkMsgCancelPop(param) {
    return http.post('/api/background_messages/messages/bulk_msg_cancel_pop', param)
  },

  /**
   * ['超管 用户管理']
   * user.admin.user_list_export 全局用户列表导出
   * @param {AdminUserListReqSer} param
   * @returns {Object}
   */
  apiUserAdminUserListExportPost(param) {
    return http.post('/api/user/admin/user_list_export', param)
  },
  apiBackgroundFoodMenuWeeklyCopyToOrganization(param) {
    return http.post('/api/background_food/menu_weekly/copy_to_organization', param)
  },
  apiBackgroundFoodMenuMonthlyCopyToOrganization(param) {
    return http.post('/api/background_food/menu_monthly/copy_to_organization', param)
  },
  apiBackgroundReportCenterDataReportPechargeMethod(param) {
    return http.post('/api/background_report_center/data_report/pecharge_method', param)
  },
  apiBackgroundReportCenterDataReportReconciliationStatementList(param) {
    return http.post(
      '/api/background_report_center/data_report/reconciliation_statement_list',
      param
    )
  },

  apiBackgroundDrpMaterailClassificationAddPost(param) {
    return http.post('/api/background_drp/materail_classification/add', param)
  },
  apiBackgroundDrpMaterailClassificationDeletePost(param) {
    return http.post('/api/background_drp/materail_classification/delete', param)
  },
  apiBackgroundDrpMaterailClassificationListPost(param) {
    return http.post('/api/background_drp/materail_classification/list', param)
  },
  apiBackgroundDrpMaterailClassificationListExportPost(param) {
    return http.post('/api/background_drp/materail_classification/list_export', param)
  },
  apiBackgroundDrpMaterailClassificationModifyPost(param) {
    return http.post('/api/background_drp/materail_classification/modify', param)
  },

  apiBackgroundAdminSupplierManageVendorContractListPost(param) {
    return http.post('/api/background/admin/supplier_manage/vendor_contract_list', param)
  },
  apiBackgroundAdminSupplierManageVendorVehicleInformationListPost(param) {
    return http.post('/api/background/admin/supplier_manage/vendor_vehicle_information_list', param)
  },
  apiBackgroundAdminSupplierManageVendorDriverInformationListPost(param) {
    return http.post('/api/background/admin/supplier_manage/vendor_driver_information_list', param)
  },
  apiBackgroundAdminSupplierManageVendorOrganizationInformationListPost(param) {
    return http.post('/api/background/admin/supplier_manage/vendor_organization_information_list', param)
  },
  apiBackgroundFundSupervisionPublicityInfoGetCanteenInfoPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/get_canteen_info', param)
  },
  apiBackgroundFundSupervisionPublicityInfoGetQualificationInfoPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/get_qualification_info', param)
  },
  apiBackgroundFundSupervisionPublicityInfoModifyCanteenPlanPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/modify_canteen_plan', param)
  },
  apiBackgroundFundSupervisionPublicityInfoDetailsQualificationPublicityPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/details_qualification_publicity', param)
  },
  apiBackgroundFundSupervisionAppropriationListPost(param) {
    return http.post('/api/background_fund_supervision/appropriation/list', param)
  },
  apiBackgroundFundSupervisionAppropriationSupplierManageListPost(param) {
    return http.post('/api/background_fund_supervision/appropriation/supplier_manage_list', param)
  },
  apiBackgroundFundSupervisionAppropriationAppropriationApplyPost(param) {
    return http.post('/api/background_fund_supervision/appropriation/appropriation_apply', param)
  },
  apiBackgroundFundSupervisionAppropriationAppropriationRevokePost(param) {
    return http.post('/api/background_fund_supervision/appropriation/appropriation_revoke', param)
  },
  apiBackgroundFundSupervisionAppropriationAppropriationSettlePost(param) {
    return http.post('/api/background_fund_supervision/appropriation/appropriation_settle', param)
  },

  apiBackgroundFundSupervisionFinanceApprovePendingListPost(param) {
    return http.post('/api/background_fund_supervision/finance_approve/pending_list', param)
  },
  apiBackgroundFundSupervisionFinanceApproveRejectListPost(param) {
    return http.post('/api/background_fund_supervision/finance_approve/reject_list', param)
  },
  apiBackgroundFundSupervisionFinanceApproveRevokeListPost(param) {
    return http.post('/api/background_fund_supervision/finance_approve/revoke_list', param)
  },
  apiBackgroundFundSupervisionFinanceApproveAgreeListPost(param) {
    return http.post('/api/background_fund_supervision/finance_approve/agree_list', param)
  },
  apiBackgroundFundSupervisionFinanceApproveGetApproveRulePost(param) {
    return http.post('/api/background_fund_supervision/finance_approve/get_approve_rule', param)
  },
  apiBackgroundFundSupervisionFinanceApproveAgreeApprovePost(param) {
    return http.post('/api/background_fund_supervision/finance_approve/agree_approve', param)
  },
  apiBackgroundFundSupervisionFinanceApproveRejectApprovePost(param) {
    return http.post('/api/background_fund_supervision/finance_approve/reject_approve', param)
  },

  apiBackgroundFundSupervisionFinanceApproveZjApproveAccountsPost(param) {
    return http.post('/api/background_fund_supervision/finance_approve/zj_approve_accounts', param)
  },

  apiBackgroundFundSupervisionFoodSafetySourceExportOrderSourcePost(param) {
    return http.post('/api/background_fund_supervision/food_safety_source/export_order_source', param)
  },
  apiBackgroundFundSupervisionFoodSafetySourceFoodSourceListPost(param) {
    return http.post('/api/background_fund_supervision/food_safety_source/food_source_list', param)
  },
  apiBackgroundFundSupervisionFoodSafetySourceGetOrderSourcePost(param) {
    return http.post('/api/background_fund_supervision/food_safety_source/get_order_source', param)
  },
  apiBackgroundFundSupervisionFoodSafetySourceGetReservedSampleInfoPost(param) {
    return http.post('/api/background_fund_supervision/food_safety_source/get_reserved_sample_info', param)
  },
  apiBackgroundFundSupervisionFoodSafetySourceGetSchedulePersonPost(param) {
    return http.post('/api/background_fund_supervision/food_safety_source/get_schedule_person', param)
  },
  apiBackgroundFundSupervisionPublicityInfoModifyComplaintInfoPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/modify_complaint_info', param)
  },
  apiBackgroundFundSupervisionEarlyWarningGetIsSuperviseOrgPost(param) {
    return http.post('/api/background_fund_supervision/early_warning/get_is_supervise_org', param)
  },
  apiBackgroundFundSupervisionEarlyWarningRiskPredictionPost(param) {
    return http.post('/api/background_fund_supervision/early_warning/risk_prediction', param)
  },
  apiBackgroundFundSupervisionEarlyWarningMaterialsRiskListPost(param) {
    return http.post('/api/background_fund_supervision/early_warning/materials_risk_list', param)
  },
  apiBackgroundFundSupervisionEarlyWarningDocContListPost(param) {
    return http.post('/api/background_fund_supervision/early_warning/doc_cont_list', param)
  },
  apiBackgroundFundSupervisionEarlyWarningGetSubordinateSupplierManagePost(param) {
    return http.post('/api/background_fund_supervision/early_warning/get_subordinate_supplier_manage', param)
  },

  // 民主监督 问卷
  apiBackgroundFundSupervisionPublicityInfoQuestionnaireListPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/questionnaire_list', param)
  },
  apiBackgroundFundSupervisionPublicityInfoQuestionnaireListExportPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/questionnaire_list_export', param)
  },
  apiBackgroundFundSupervisionPublicityInfoQuestionnaireAddPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/questionnaire_add', param)
  },
  apiBackgroundFundSupervisionPublicityInfoQuestionnaireModifyPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/questionnaire_modify', param)
  },
  apiBackgroundFundSupervisionPublicityInfoQuestionnaireDeletePost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/questionnaire_delete', param)
  },
  apiBackgroundFundSupervisionPublicityInfoQuestionnaireModifyStatusPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/questionnaire_modify_status', param)
  },
  apiBackgroundFundSupervisionPublicityInfoQuestionnaireDetailPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/questionnaire_detail', param)
  },
  apiBackgroundFundSupervisionPublicityInfoQuestionnaireAnswerDetailPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/questionnaire_answer_detail', param)
  },
  apiBackgroundFundSupervisionPublicityInfoQuestionnaireOperateLogListPost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/questionnaire_operate_log_list', param)
  },

  apiBackgroundFundSupervisionFoodSafetySourceDineSourceListPost(param) {
    return http.post('/api/background_fund_supervision/food_safety_source/dine_source_list', param)
  },
  apiBackgroundFundSupervisionFoodSafetySourceDownloadOrderPost(param) {
    return http.post('/api/background_fund_supervision/food_safety_source/download_order', param)
  },
  // 监管管理-食堂信息-托管管理
  // 列表
  apiBackgroundFundSupervisionCustodyManagementList(param) {
    return http.post('/api/background_fund_supervision/custody_management/list', param)
  },
  // 新增团餐公司
  apiBackgroundFundSupervisionCustodyManagementAdd(param) {
    return http.post('/api/background_fund_supervision/custody_management/add', param)
  },
  // 编辑团餐公司
  apiBackgroundFundSupervisionCustodyManagementModify(param) {
    return http.post('/api/background_fund_supervision/custody_management/modify', param)
  },
  // 删除团餐公司
  apiBackgroundFundSupervisionCustodyManagementDelete(param) {
    return http.post('/api/background_fund_supervision/custody_management/delete', param)
  },
  // 启用/关闭团餐公司
  apiBackgroundFundSupervisionCustodyManagementModifyStatus(param) {
    return http.post('/api/background_fund_supervision/custody_management/modify_status', param)
  },
  // 是否有开启的团餐公司
  apiBackgroundFundSupervisionCustodyManagementIsEnable(param) {
    return http.post('/api/background_fund_supervision/custody_management/is_enable', param)
  },
  // 获取团餐公司所有信用代码
  apiBackgroundFundSupervisionCustodyManagementGetUsccList(param) {
    return http.post('/api/background_fund_supervision/custody_management/get_uscc_list', param)
  },

  // 监管管理-食堂信息-配餐管理
  // 列表
  apiBackgroundFundSupervisionCateringCompanyList(param) {
    return http.post('/api/background_fund_supervision/catering_company/list', param)
  },
  // 新增配餐公司
  apiBackgroundFundSupervisionCateringCompanyAdd(param) {
    return http.post('/api/background_fund_supervision/catering_company/add', param)
  },
  // 编辑配餐公司
  apiBackgroundFundSupervisionCateringCompanyModify(param) {
    return http.post('/api/background_fund_supervision/catering_company/modify', param)
  },
  // 删除配餐公司
  apiBackgroundFundSupervisionCateringCompanyDelete(param) {
    return http.post('/api/background_fund_supervision/catering_company/delete', param)
  },
  // 启用/关闭配餐公司
  apiBackgroundFundSupervisionCateringCompanyModifyStatus(param) {
    return http.post('/api/background_fund_supervision/catering_company/modify_status', param)
  },
  // 是否有开启的配餐公司
  apiBackgroundFundSupervisionCateringCompanyIsEnable(param) {
    return http.post('/api/background_fund_supervision/catering_company/is_enable', param)
  },
  // 获取配餐公司所有信用代码
  apiBackgroundFundSupervisionCateringCompanyGetUsccList(param) {
    return http.post('/api/background_fund_supervision/catering_company/get_uscc_list', param)
  },
  apiBackgroundPrinterPrintTaskManagerGetMaxIndexNoPost(param) {
    return http.post('/api/background_printer/print_task_manager/get_max_index_no', param)
  },
  apiBackgroundFundSupervisionPublicityInfoModifySchoolTypePost(param) {
    return http.post('/api/background_fund_supervision/publicity_info/modify_school_type', param)
  },
  apiBackgroundFundSupervisionLedgerKitchenHygieneDailyFoodSafetyListPost(param) {
    return http.post('/api/background_fund_supervision/ledger_kitchen_hygiene/daily_food_safety_list', param)
  },
  apiBackgroundFundSupervisionLedgerKitchenHygieneDailyFoodSafetyDetailsPost(param) {
    return http.post('/api/background_fund_supervision/ledger_kitchen_hygiene/daily_food_safety_details', param)
  },
  apiBackgroundFundSupervisionLedgerKitchenHygieneBulkExportDailyFoodSafetyPost(param) {
    return http.post('/api/background_fund_supervision/ledger_kitchen_hygiene/bulk_export_daily_food_safety', param)
  },
  apiBackgroundFundSupervisionEarlyWarningOtherWarnMsgListPost(param) {
    return http.post('/api/background_fund_supervision/early_warning/other_warn_msg_list', param)
  },
  apiBackgroundMessagesWarnBoxBoxListPost(param) {
    return http.post('/api/background_messages/warn_box/box_list', param)
  },
  apiBackgroundMessagesWarnBoxBoxReadPost(param) {
    return http.post('/api/background_messages/warn_box/box_read', param)
  },
  apiBackgroundFundSupervisionJobPersonLeaveJobPersonPost(param) {
    return http.post('/api/background_fund_supervision/job_person/leave_job_person', param)
  },
  apiBackgroundFundSupervisionJobPersonJobPersonTotalPost(param) {
    return http.post('/api/background_fund_supervision/job_person/job_person_total', param)
  },
  // 用餐管理-地址管理-配送汇总表-消息点配餐
  apiBackgroundOrderOrderReservationGetDeliveryOrgCollect(param) {
    return http.post('/api/background_order/order_reservation/get_delivery_org_collect', param)
  },
  apiBackgroundPrinterPrintTaskManagerPrintForOrgOrder(param) {
    return http.post('/api/background_printer/print_task_manager/print_for_org_order', param)
  },
  apiBackgroundOrderOrderReservationGetDeliveryCollectByOrgIds(param) {
    return http.post('/api/background_order/order_reservation/get_delivery_collect_by_org_ids', param)
  },
  apiBackgroundStoreBackgroundAcceptanceAddPost(param) {
    return http.post('/api/background/store/background_acceptance/add', param)
  },
  apiBackgroundFundSupervisionLedgerKitchenHygieneKitchenCanteenDisinfectListPost(param) {
    return http.post('/api/background_fund_supervision/ledger_kitchen_hygiene/kitchen_canteen_disinfect_list', param)
  },
  apiBackgroundFundSupervisionLedgerKitchenHygieneExportKitchenCanteenDisinfectListPost(param) {
    return http.post('/api/background_fund_supervision/ledger_kitchen_hygiene/export_kitchen_canteen_disinfect_list', param)
  },
  apiBackgroundFundSupervisionLedgerKitchenHygieneModifySanitizerConfigPost(param) {
    return http.post('/api/background_fund_supervision/ledger_kitchen_hygiene/modify_sanitizer_config', param)
  },
  apiBackgroundFundSupervisionLedgerKitchenHygieneSanitizerConfigPost(param) {
    return http.post('/api/background_fund_supervision/ledger_kitchen_hygiene/sanitizer_config', param)
  }
}
