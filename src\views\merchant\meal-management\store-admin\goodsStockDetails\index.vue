<template>
  <div class="store-goods container-wrapper">
    <div class="top-btn">
      <el-radio-group v-model="tabType" class="ps-radio-btn" @change="changeTab">
        <el-radio-button v-for="item in tabTypeList" :key="item.value" :label="item.value" v-permission="[`${item.permission}`]">
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
      <!-- <img @click="refreshHandle" class="refresh" src="@/assets/img/refresh.png" alt="" /> -->
    </div>
    <add-stock-details ref="add_stock_details" v-if="tabType === 'add_stock_details'"></add-stock-details>
    <deduct-stock-details ref="deduct_stock_details" v-if="tabType === 'deduct_stock_details'"></deduct-stock-details>
  </div>
</template>

<script>
// import { debounce } from '@/utils'
import AddStockDetails from './components/AddStockDetails.vue'
import DeductStockDetails from './components/DeductStockDetails.vue'
export default {
  name: 'StoreStockAdmin',
  components: { AddStockDetails, DeductStockDetails },
  data() {
    return {
      tabType: 'add_stock_details',
      tabTypeList: [
        {
          value: 'add_stock_details',
          label: '入库详情',
          permission: 'background_store.goods.goods_stock_details_add'
        },
        {
          value: 'deduct_stock_details',
          label: '出库详情',
          permission: 'background_store.goods.goods_stock_details_deduct'
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    changeTab() {},
    refreshHandle() {
      // this.$refs.deliverChild.resetSearchHandle()
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.store-goods {
  .top-btn {
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
  }
  .refresh {
    cursor: pointer;
    padding: 14px 0;
  }
}
</style>
