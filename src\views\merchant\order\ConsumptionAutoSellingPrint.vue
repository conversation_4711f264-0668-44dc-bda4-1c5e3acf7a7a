<template>
  <!--这个页面由于需要打印合计，单独给售货柜订单销售统计作打印，页面修改来自print/index 公用打印页面-->
  <div id="print" v-loading="isLoading">
    <div class="print-title">{{ title }}</div>
    <!-- 有页头和页尾的 -->
    <div v-if="showPrintHeaderAndFooter" class="page-header-footer">
      <table class="wrapper-table">
        <thead>
          <tr>
            <td>
              <div :class="{ 'page-header-space': true }"></div>
            </td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <div class="print-table-wrapper">
                <!-- 目前所有打印报表都需要日期 ,如果不需要日期 print_date_state 传false-->
                <template v-if="printDateState">
                  <el-table v-loading="isLoading" :data="tableData" :show-summary="showSummary" :summary-method="getSummaries" border class="print-table" header-row-class-name="ps-table-header-row" style="width: 100%" >
                        <el-table-column prop="date" label="日期" :key="tableColumnKey">
                           <!-- eslint-disable-next-line -->
                            <template slot="header" slot-scope="scope">
                              <div class="clearfix" style="width:100%;text-align: center;">
                                <span>起止时间：{{ params.start_date + '至' + params.end_date }}</span>
                              </div>
                            </template>
                          <table-column v-for="item in currentTableSetting" :key="item.key"  :col="item"> </table-column>
                        </el-table-column>
                  </el-table>
                </template>
                <!-- <custom-table
                  v-else
                  class="print-table"
                  :tableSetting="currentTableSetting"
                  :tableData="tableData"
                  :show-summary="showSummary"
                  :summary-method="getSummaries"
                  header-row-class-name="ps-table-header-row"
                  border
                ></custom-table> -->
                <el-table
                  v-else
                  :data="tableData"
                  style="width: 100%"
                  :show-summary="showSummary"
                  header-row-class-name="ps-table-header-row"
                  border
                  class="print-table"
                  :span-method="collectSpanMethod"
                >
                  <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
                    <template #index="{row,index}">
                      {{ getPageIndex(index,row) }}
                    </template>
                    <template #dateStartFee="{ row }">
                      <span :class="{'danger bold': row.error_date_start_fee}">{{ row.date_start_fee | formatMoney }}</span>
                    </template>
                    <template #dateEndFee="{ row }">
                      <span :class="{'danger bold': row.error_date_end_fee}">{{ row.date_end_fee | formatMoney }}</span>
                    </template>
                  </table-column>
                </el-table>
              </div>
              <!-- 统计 start -->
              <table-statistics :statistics="collect" />
              <!-- end -->
            </td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td>
              <div class="page-footer-space"></div>
            </td>
          </tr>
        </tfoot>
      </table>
      <div class="page-header m-t-20">
        <div class="logo-wrapper">
          <img src="@/assets/img/logo-01.png" alt srcset />
        </div>
        <h3 style="text-align: center; margin: 10px 0; font-weight: 600;">{{ title }}</h3>
      </div>

      <div class="page-footer">
        <div class="autograph-wrapper">
          <span style="padding:0 30px 0 0px;">使用单位：{{ organizationName }}</span>
          <span  style="padding:0 30px 0 0px;">打印时间：{{ new Date() | formatDate }}</span>
          <span style="padding:0 130px 0 0px;">主管签字：</span>
          <span style="padding:0 130px 0 0px;">制表人签字：</span>
        </div>
      </div>
    </div>
    <!-- 无页头页尾 -->
    <div v-else class="print-table-wrapper">
      <!-- <custom-table
        class="print-table"
        :tableSetting="currentTableSetting"
        :tableData="tableData"
        :show-summary="showSummary"
        header-row-class-name="ps-table-header-row"
        border
      ></custom-table> -->
      <el-table
        :data="tableData"
        style="width: 100%"
        :show-summary="showSummary"
        header-row-class-name="ps-table-header-row"
        border
        class="print-table"
      >
        <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
          <template #dateStartFee="{ row }">
            <span :class="{'danger bold': row.error_date_start_fee}">{{ row.date_start_fee | formatMoney }}</span>
          </template>
          <template #dateEndFee="{ row }">
            <span :class="{'danger bold': row.error_date_end_fee}">{{ row.date_end_fee | formatMoney }}</span>
          </template>
        </table-column>
      </el-table>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogVisible"
      @confirm="confirmDialog"
    ></print-setting>
    <div class="print-tools noprint">
      <el-button
        class="print-btn"
        size="medium"
        circle
        @click="openSetting"
        icon="el-icon-setting"
      ></el-button>
      <el-button
        class="print-btn"
        size="medium"
        circle
        @click="toPrint"
        icon="el-icon-printer"
      ></el-button>
    </div>
  </div>
</template>

<script>
import { to, parseTime, deepClone } from '@/utils'
import * as dayjs from 'dayjs'
import { COLLECT_ORDER_AUTO_TABLE } from '@/views/merchant/order/constants'

export default {
  name: 'ConsumptionAutoSellingPrint',
  components: {
  },
  data() {
    return {
      isLoading: false,
      requestApi: '', // 请求的接口
      type: '',
      title: '', // 标题
      organizationName: '', // 当前组织名称
      printDateState: true, // 是否开启报表日期
      tableSetting: [],
      currentTableSetting: [],
      tableData: [], // table数据
      params: {}, // 请求数据的参数
      dialogVisible: false,
      showPrintHeaderAndFooter: false, // 是否显示打印头和尾
      resultKey: 'data', // 列表值的获取字段
      showSummary: false, // 是否显示合计 showSummary/pushSummary二选一
      pushSummary: false, // 合计添加到到table数据最后
      collect: [], // 统计
      tableColumnKey: 1, // 给有时间的表加个key
      isAutoCollect: false // 是否列表末尾增加合计,这个单独给售货柜订单进行统计，没有公用性
    }
  },
  created() {
    try {
      this.type = this.$route.query.print_type
      this.title = this.$route.query.print_title
      this.requestApi = this.$route.query.api
      this.showSummary = this.$route.query.show_summary === 'true'
      this.showPrintHeaderAndFooter = this.$route.query.show_print_header_and_footer === 'true'
      this.tableSetting = JSON.parse(this.$route.query.table_setting)
      var currentTableSetting = JSON.parse(this.$route.query.current_table_setting)
      if (currentTableSetting) {
        currentTableSetting.forEach(item => {
          item.sortable = false
        })
        this.currentTableSetting = currentTableSetting
      }
      console.log("this.currentTableSetting", this.currentTableSetting);
      this.printDateState = this.$route.query.print_date_state ? this.$route.query.print_date_state : false
      this.isAutoCollect = this.$route.query.isAutoCollect ? this.$route.query.isAutoCollect : false
      this.params = JSON.parse(this.$route.query.params)
      if (this.$route.query.collect) {
        this.collect = JSON.parse(this.$route.query.collect)
      }
      this.pushSummary = this.$route.query.push_summary === 'true'
      if (this.$route.query.result_key) {
        this.resultKey = this.$route.query.result_key
      }
      // 拿到当前组织名称 登陆的组织
      if (this.$store.getters.userInfo && !this.$store.getters.userInfo.organizationList) return
      this.$store.getters.userInfo.organizationList.forEach(item => {
        if (this.$store.getters.organization === item.id) {
          this.organizationName = item.name
        }
      })
    } catch (error) {
      console.log('get print query error', error)
    }
    this.initLoad()
  },
  methods: {
    parseTime,
    initLoad() {
      this.getTableData()
      this.printListener()
    },
    async getTableData() {
      if (!this.requestApi) return this.$message.error('请求失败，请检查参数！')
      this.isLoading = true
      const [err, res] = await to(this.$apis[this.requestApi](this.params))
      this.isLoading = false
      this.tableData = []
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let result = res.data
        if (this.type === 'AccountWalletDaily') {
          this.initAccountWalletDaily(result)
        }
        // 表末合计数据插入
        if (this.isAutoCollect) {
          this.tableData = this.setCollectTotal(res.data, res.data.summary_data)
        }
        if (this.resultKey) {
          this.tableData = result[this.resultKey]
        } else {
          this.tableData = result
        }
        // 统计
        if (this.collect && this.collect.length) {
          this.setCollectData(res)
        }
        // 合计
        if (this.pushSummary) {
          this.setSummaryData(res)
        }
        this.resetTableStyle()
      } else {
        this.$message.error(res.msg)
      }
    },
    initAccountWalletDaily(data) {
      let dateList = {} // 记录期初期末数据，用于分页的收尾数据
      let len = data.result.length
      let result = data.result
      result.map((item, index) => {
        // 手动存下当前分页首末的期初期末数据
        if (index === 0 || index === (len - 1)) {
          dateList[item.date] = {
            date_start_fee: item.date_start_fee,
            date_end_fee: item.date_end_fee
          }
        }
        // 上一条的日期，因为后台返回的数据是倒序
        let nextDate = dayjs(item.date).add(1, 'day').format('YYYY-MM-DD')
        // 计算当前期初的金额与上一天的期末金额是否相等，如果不相等则证明数据有误，需标红显示
        if (index > 0) {
          // 对比上一条的数据
          let nextItem = result[index - 1]
          // 当日期符合时进行金额的判断
          if (nextItem) {
            // 当上一条期末金额与当前的期初金额不同时
            if (nextItem.date === nextDate && nextItem.date_start_fee !== item.date_end_fee) {
              item.error_date_end_fee = true
              nextItem.error_date_start_fee = true
            }
          } else {
            // nextItem = dateList[nextDate]
            // if (nextItem && nextItem.date_start_fee !== item.date_end_fee) {
            //   item.error_date_end_fee = true
            //   nextItem.error_date_start_fee = true
            // }
          }
        } else { // 当index为0的时候，应该是分页的数据了
          let nextItem = dateList[nextDate]
          // 当日期符合时进行金额的判断
          if (nextItem) {
            // 当上一条期末金额与当前的期初金额不同时
            if (nextItem.date_start_fee !== item.date_end_fee) {
              item.error_date_end_fee = true
              // nextItem.error_date_start_fee = true
            }
          }
        }
        return item
      })
    },
    openSetting() {
      this.dialogVisible = true
    },
    confirmDialog(val) {
      this.currentTableSetting = val
      this.tableColumnKey++
      this.resetTableStyle()
    },
    // 设置统计的值
    setCollectData(res) {
      // 统计
      this.collect.forEach(item => {
        if (res.data.collect && res.data.collect[item.key] !== undefined) {
          this.$set(item, 'value', res.data.collect[item.key])
          // item.value = res.data.collect[item.key]
        }
      })
    },
    // 设置合计的值，严格依赖tableSetting，和数据的先后顺序
    setSummaryData(res) {
      let collect = res.data.collect
      collect[this.tableSetting[0].key] = '合计'
      this.tableData.push(collect)
    },
    // 表格合计
    getSummaries(param) {
      // const { columns, data } = param
      // const list = []
      // let sums = []
      // if (data.length) {
      //   columns.forEach((item, index) => {
      //     if (index === 0) {
      //       sums[index] = '合计'
      //     } else if (list.includes(item.property) && this.collectList) {
      //       if (item.property.indexOf('_count') > 0) {
      //         sums[index] = this.collectList[item.property] ? this.collectList[item.property] : 0
      //       } else {
      //         sums[index] = this.collectList[item.property] ? divide(this.collectList[item.property]) : 0
      //       }
      //     } else {
      //       sums[index] = ''
      //     }
      //   })
      // }
      // return sums
    },
    // 重置表格样式，importance
    resetTableStyle() {
      this.$nextTick(() => {
        let colgroupEl = document.querySelectorAll('colgroup')
        setTimeout(() => {
          for (let i = 0; i < colgroupEl.length; i++) {
            colgroupEl[i].remove()
          }
          // window.print()
        }, 50)
      })
    },
    // 打印窗口监听
    printListener() {
      // 貌似火狐不支持事件监听
      if (window.matchMedia) {
        var mediaQueryList = window.matchMedia('print')
        // mediaQueryList.onchange = function(ee) {
        //   console.log('ee',ee)
        // }
        mediaQueryList.addListener(function(mql) {
          if (mql.matches) {
          } else {
            window.close()
          }
        })
      }
    },
    toPrint() {
      window.print()
    },
    // 设置合并数据 只适用售货柜订单销售统计，没有公用性
    setCollectTotal(result, summaryData) {
      var dataList = this.resultKey ? result[this.resultKey] : result
      console.log("dataList", dataList);
      if (dataList && Array.isArray(dataList) && dataList.length > 0) {
        // 有数据在数据后面加上合计
        var listIndex = deepClone(COLLECT_ORDER_AUTO_TABLE)
        var itemDataList = [{}, {}]
        itemDataList.forEach((item, index) => {
          listIndex.forEach(listIndexItem => {
            // 当页统计
            if (index === 0) {
              item[listIndexItem.key] = this.getSumByKey(dataList, listIndexItem.key)
            }
            // 总数统计
            if (index === 1) {
              item[listIndexItem.key] = summaryData['total_' + listIndexItem.key] ? summaryData['total_' + listIndexItem.key] : 0
            }
            // 把倒数的index改一下名字
            if (listIndexItem.key === 'index') {
              item[listIndexItem.key] = index === 0 ? '当页合计' : '总合计'
            }
          }
          )
        })
        dataList = dataList.concat(itemDataList)
      }
      console.log("dataList newList", dataList);
      if (this.resultKey) {
        result[this.resultKey] = dataList
        return result
      } else {
        return dataList
      }
    },
    // 根据key值算总数
    getSumByKey(list, key) {
      var sum = 0
      if (list && Array.isArray(list)) {
        list.forEach(item => {
          sum = item[key] && key !== 'name' ? sum + item[key] : sum
        })
      }
      return sum
    },
    // 统计合并list
    collectSpanMethod({ row, column, rowIndex, columnIndex }) {
      var listLength = this.tableData.length
      // 最后两行做成合计
      if (rowIndex === listLength - 1 || rowIndex === listLength - 2) {
        // 第一，二，三，四行合并
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 3
          };
        } else if (columnIndex === 1 || columnIndex === 2) {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    // 获取数据下标
    getPageIndex(index, row) {
      console.log("getPageIndex", index, row.index);
      var thisIndex = row.index
      if (thisIndex && typeof thisIndex === 'string' && thisIndex.indexOf('合计') !== -1) {
        return thisIndex
      }
      return (index + 1) + (this.params.page - 1) * this.params.page_size
    }
  }
}
</script>
<style lang="scss">
@page {
    size: auto;
    margin: 0mm;
  }
#print {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .print-title {
    padding: 20px;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .print-table-wrapper {
    margin: 0 20px 20px;
    // width: 100%;
    .print-table {
      // width: 100%;
      margin: 0 -1px;
    }
  }
  .print-tools {
    position: fixed;
    right: 45px;
    bottom: 45px;
    z-index: 99;
    .el-button {
      display: block;
      margin: 15px 0;
      font-size: 26px;
    }
  }
  table {
    width: 100% !important;
    // min-width: 1400px;
    font-weight: 500;
    th {
      font-weight: 500;
    }
  }
  .page-header,
  .page-header-space {
    height: 120px;
  }

  .page-footer,
  .page-footer-space {
    height: 100px;
  }
  .page-header {
    position: fixed;
    top: 0mm;
    left: 20px;
    width: 100%;
    // height: 90px;
    background-color: #fff;
  }
  .page-footer {
    position: fixed;
    left: 20px;
    bottom: 0;
    width: 100%;
    // height: 90px;
    background-color: #fff;
    z-index: 1;
    .autograph-wrapper{
      display: flex;
      flex-wrap: wrap;
      padding: 20px 0;
    }
  }
  .el-table__empty-block {
    // width: 100% !important;
  }
}
// @media print {
//   @page {
//     margin: 0;
//     size: auto;
//   }
//   .noprint {
//     display: none;
//   }
// }
.bold{
  font-weight: bold;
}
.page {
  page-break-after: always;
}
@media print {
  thead {
    display: table-header-group;
  }
  tfoot {
    display: table-footer-group;
  }
  body {
    margin: 0;
  }
  @page {
    size: auto;
  }
  .page-header-footer{
    @page {
      size: auto;
      margin: 120px 0 80px;
    }
  }
  .noprint {
    display: none;
  }
}
</style>
