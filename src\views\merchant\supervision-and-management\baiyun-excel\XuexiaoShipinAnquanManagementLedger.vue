<template>
  <div class="container-wrapper">
    <div class="inspection-record">
      <h2 class="table-title">食品安全检查记录表</h2>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <el-table-column label="项目" align="center" >
          <template slot-scope="scope">
            <div>{{ scope.row.category }}</div>
          </template>
        </el-table-column>

        <el-table-column label="序号" align="center" width="60">
          <template slot-scope="scope">
            <div>{{ scope.$index + 1 }}</div>
          </template>
        </el-table-column>

        <el-table-column label="检查内容" align="center" min-width="200">
          <template slot-scope="scope">
            <div :class="scope.row.isImportant ? 'important-item' : ''">{{ scope.row.content }}</div>
          </template>
        </el-table-column>

        <el-table-column label="检查结果" align="center" width="130">
          <template slot-scope="scope">
            <el-select v-model="scope.row.result" placeholder="选择结果" size="mini">
              <el-option label="合格" value="合格"></el-option>
              <el-option label="不合格" value="不合格"></el-option>
              <el-option label="不适用" value="不适用"></el-option>
            </el-select>
          </template>
        </el-table-column>

        <el-table-column label="不符合项说明" align="center" width="180">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.nonCompliance"
              :disabled="scope.row.result !== '不合格'"
              type="text"
              :rows="2"
              placeholder=""
              size="mini">
            </el-input>
          </template>
        </el-table-column>

        <el-table-column label="不符合采取的防范措施" align="center" width="180">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.preventiveMeasures"
              :disabled="scope.row.result !== '不合格'"
              type="text"
              :rows="2"
              placeholder=""
              size="mini">
            </el-input>
          </template>
        </el-table-column>
      </el-table>

      <!-- 签名和日期部分 -->
      <div class="signature-area">
        <div class="signature-row">
          <span class="signature-label">检查人签名:</span>
          <el-input v-model="inspector" class="signature-input" size="mini"></el-input>
          <span class="date-wrapper">
            <el-date-picker
              v-model="inspectionDate"
              type="date"
              placeholder="选择日期"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy 年 MM 月 dd 日"
              size="mini"
              style="width: 180px;">
            </el-date-picker>
          </span>
        </div>
        <div class="signature-row">
          <span class="signature-label">负责人签名:</span>
          <el-input v-model="supervisor" class="signature-input" size="mini"></el-input>
          <span class="date-wrapper">
            <el-date-picker
              v-model="supervisorDate"
              type="date"
              placeholder="选择日期"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy 年 MM 月 dd 日"
              size="mini"
              style="width: 180px;">
            </el-date-picker>
          </span>
        </div>
      </div>

      <div class="button-row">
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'XuexiaoShipinAnquanManagementLedger',
  data() {
    return {
      inspector: '',
      supervisor: '',
      inspectionDate: '',
      supervisorDate: '',
      tableData: [
        {
          category: '加工制作过程',
          number: '18',
          content: '清洗、消毒水池不得与其他用途水池混用',
          isImportant: false,
          result: '',
          nonCompliance: '',
          preventiveMeasures: ''
        },
        {
          category: '加工制作过程',
          number: '19',
          content: '食品原料、半成品与成品在盛放、贮存时相互分开',
          isImportant: true,
          result: '',
          nonCompliance: '',
          preventiveMeasures: ''
        },
        {
          category: '加工制作过程',
          number: '20',
          content: '制作食品的设施设备及加工工具、容器等有显著标识',
          isImportant: false,
          result: '',
          nonCompliance: '',
          preventiveMeasures: ''
        },
        {
          category: '加工制作过程',
          number: '21',
          content: '食品煮熟煮透，食品成品存放的温度和时间符合要求',
          isImportant: true,
          result: '',
          nonCompliance: '',
          preventiveMeasures: ''
        },
        {
          category: '加工制作过程',
          number: '22',
          content: '食品留样符合规范',
          isImportant: true,
          result: '',
          nonCompliance: '',
          preventiveMeasures: ''
        },
        {
          category: '设施设备及维护',
          number: '23',
          content: '冷藏、冷冻、空调、空气消毒等设备设施完成维修保养校验，运转正常',
          isImportant: true,
          result: '',
          nonCompliance: '',
          preventiveMeasures: ''
        },
        {
          category: '设施设备及维护',
          number: '24',
          content: '食品加工、贮存、陈列及"三防"等设施设备运转正常，并保持清洁',
          isImportant: true,
          result: '',
          nonCompliance: '',
          preventiveMeasures: ''
        },
        {
          category: '设施设备及维护',
          number: '25',
          content: '具有餐具、饮具的清洗、消毒、保洁设备设施，并运转正常',
          isImportant: false,
          result: '',
          nonCompliance: '',
          preventiveMeasures: ''
        },
        {
          category: '清洗消毒',
          number: '26',
          content: '餐具、饮具和盛放直接入口食品的容器用后洗净、消毒，炊具、用具用后洗净，保持清洁',
          isImportant: false,
          result: '',
          nonCompliance: '',
          preventiveMeasures: ''
        }
      ]
    };
  },
  methods: {
    submitForm() {
      // 保存表单数据的逻辑
      console.log('表单数据:', {
        tableData: this.tableData,
        inspector: this.inspector,
        supervisor: this.supervisor,
        inspectionDate: this.inspectionDate,
        supervisorDate: this.supervisorDate
      });
      this.$message.success('保存成功');
    },
    resetForm() {
      // 重置表单数据
      this.tableData.forEach(item => {
        item.result = '';
        item.nonCompliance = '';
        item.preventiveMeasures = '';
      });
      this.inspector = '';
      this.supervisor = '';
      this.inspectionDate = '';
      this.supervisorDate = '';
      this.$message.info('已重置表单');
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.inspection-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  ::v-deep .el-table .cell {
    padding: 5px;
  }

  .important-item {
    position: relative;
    font-weight: bold;

    &::before {
      content: '*';
      color: #f56c6c;
      position: absolute;
      left: -8px;
    }
  }

  .signature-area {
    margin-top: 20px;
    padding: 10px 0;

    .signature-row {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .signature-label {
        width: 100px;
        text-align: right;
        margin-right: 10px;
      }

      .signature-input {
        width: 150px;
        margin-right: 30px;
      }

      .date-wrapper {
        margin-left: auto;
        padding-right: 50px;
      }
    }
  }

  .button-row {
    margin-top: 20px;
    text-align: center;
  }
}
</style>
