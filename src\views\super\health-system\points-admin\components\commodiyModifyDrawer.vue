<template>
  <div class="drawer-box">
    <customDrawer
      :show.sync="visible"
      :loading="isLoading"
      :title="drawerTitle"
      @confirm="saveSetting"
      :size="800"
    >
    <div class="drawer-container" >
        <div class="drawer-content">
          <el-form :model="drawerFormData" ref="drawerFormDataRef" :rules="drawerFormDataRuls">
            <el-form-item label="商品名称" prop="name" label-width="80px">
              <el-input
                v-model="drawerFormData.name"
                maxlength="20"
                placeholder="请输入商品名称"
                class="w-350 ps-input"
              ></el-input>
            </el-form-item>
            <el-form-item label="识别图片" label-width="80px" prop="imagesUrl">
              <div class="msg-tips">最多上传6张，格式支持jpg、png等，每张不超过5M</div>
              <el-upload
                ref="uploadExtraImage"
                class="upload-food"
                drag
                :data="uploadParams"
                :action="actionUrl"
                :multiple="true"
                :file-list="drawerFormData.imagesUrlList"
                list-type="picture-card"
                :on-change="handelChange"
                :on-success="handleImgSuccess"
                :before-upload="beforeFoodImgUpload"
                :limit="6"
                :headers="headersOpts"
              >
                <i v-if="drawerFormData.imagesUrl.length < 6" class="el-icon-plus"></i>
                <div
                  slot="file"
                  slot-scope="{ file }"
                  v-loading="file.status === 'uploading'"
                  element-loading-text="上传中"
                >
                  <div class="upload-food-img"><img :src="file.url" alt="" /></div>
                  <span class="el-upload-list__item-actions">
                    <span
                      class="el-upload-list__item-preview"
                      @click="handlePictureCardPreview(file)"
                    >
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span
                      class="el-upload-list__item-delete"
                      @click="handleFoodImgRemove(file, 'extraImages')"
                    >
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item label="上架时间" prop="listingTime" label-width="80px">
              <el-date-picker
                v-model="drawerFormData.listingTime"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :disabled="drawerFormData.isPermanent"
              ></el-date-picker>
              <el-checkbox
                v-model="drawerFormData.isPermanent"
                @change="changeIsPermanent"
                style="padding-left: 20px"
              >
                永久
              </el-checkbox>
            </el-form-item>
            <el-form-item label="商品类型" prop="commodityType" label-width="80px">
              <el-radio-group class="ps-radio" v-model="drawerFormData.commodityType">
                <el-radio label="virtual">虚拟商品</el-radio>
                <el-radio label="physical">实物商品</el-radio>
              </el-radio-group>
              <div style="display: flex" v-if="drawerFormData.commodityType === 'virtual'">
                <el-form-item prop="virtualCommodityType">
                  <el-select
                    v-model="drawerFormData.virtualCommodityType"
                    class="ps-select w-180"
                    placeholder="请选择类型"
                    clearable
                  >
                    <el-option
                      v-for="item in virtualCommodityTypeList"
                      :key="item.type"
                      :label="item.name"
                      :value="item.type"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  label-width="20px"
                  prop="virtualNum"
                  v-if="drawerFormData.virtualCommodityType"
                >
                  <el-input
                    v-model="drawerFormData.virtualNum"
                    placeholder="请输入"
                    class="w-150 ps-input"
                  ></el-input>
                  <span style="padding-left: 10px">
                    {{ drawerFormData.virtualCommodityType === 'member' ? '天' : '次' }}
                  </span>
                </el-form-item>
              </div>
              <div v-if="drawerFormData.commodityType === 'physical'">
                <el-form-item prop="physicalCode">
                  <el-input
                    v-model="drawerFormData.physicalCode"
                    placeholder="请输入商品编码"
                    class="w-150 ps-input"
                  ></el-input>
                </el-form-item>
              </div>
            </el-form-item>
            <el-form-item label="商品价格" prop="commodityPriceType" label-width="80px">
              <el-radio-group class="ps-radio" v-model="drawerFormData.commodityPriceType">
                <el-radio label="money_points">金额+积分</el-radio>
                <el-radio label="money">金额</el-radio>
                <el-radio label="points">积分</el-radio>
              </el-radio-group>
              <div style="display: flex">
                <el-form-item
                  prop="fee"
                  v-if="
                    drawerFormData.commodityPriceType === 'money' ||
                    drawerFormData.commodityPriceType === 'money_points'
                  "
                >
                  <el-input
                    v-model="drawerFormData.fee"
                    placeholder="请输入金额"
                    class="w-150 ps-input"
                  ></el-input>
                  <span style="padding-left: 10px">元</span>
                  <span
                    v-if="drawerFormData.commodityPriceType === 'money_points'"
                    style="padding-right: 10px"
                  >
                    +
                  </span>
                </el-form-item>
                <el-form-item
                  prop="points"
                  v-if="
                    drawerFormData.commodityPriceType === 'points' ||
                    drawerFormData.commodityPriceType === 'money_points'
                  "
                >
                  <el-input
                    v-model="drawerFormData.points"
                    placeholder="请输入积分"
                    class="w-150 ps-input"
                  ></el-input>
                  <span style="padding-left: 10px">积分</span>
                </el-form-item>
              </div>
            </el-form-item>
            <el-form-item label="库存数量" prop="buyStockNum" label-width="80px">
              <el-input
                v-model="drawerFormData.buyStockNum"
                placeholder="请输入库存数量"
                class="w-350 ps-input"
                :disabled="drawerFormData.buyStockNumType"
              ></el-input>
              <el-checkbox
                v-model="drawerFormData.buyStockNumType"
                @change="changeIsBuyStock"
                style="padding-left: 20px"
              >
                不限制
              </el-checkbox>
            </el-form-item>
            <el-form-item label="可兑换数" required>
              <div style="display: flex">
                <el-form-item prop="buyLimitType" lable-width="70px">
                  <el-select v-model="drawerFormData.buyLimitType" class="ps-select w-180">
                    <el-option
                      v-for="item in buyLimitTypeList"
                      :key="item.type"
                      :label="item.name"
                      :value="item.type"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  prop="buyLimitNum"
                  style="padding: 0 10px"
                  v-if="drawerFormData.buyLimitType !== 'non' && drawerFormData.buyLimitType"
                >
                  <el-input
                    v-model="drawerFormData.buyLimitNum"
                    placeholder="请输入次数"
                    class="ps-input"
                    style="width: 170px"
                  ></el-input>
                  <span style="padding-left: 10px">次</span>
                </el-form-item>
              </div>
            </el-form-item>
            <el-form-item label="图文详情" prop="details" label-width="80px">
              <TinymceUeditor
                :content="drawerFormData.details"
                v-model="drawerFormData.details"
                @message="messageTinymceUeditor"
              ></TinymceUeditor>
            </el-form-item>
            <el-form-item label="优先级" prop="priority" label-width="80px">
              <el-input
                v-model="drawerFormData.priority"
                maxlength="20"
                placeholder="请输入优先级"
                class="w-350 ps-input"
              ></el-input>
              <div>现有优先级：{{ priorityText() }}</div>
            </el-form-item>
          </el-form>
        </div>
      </div>
  </customDrawer>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import { getToken, getSuffix } from '@/utils'
import TinymceUeditor from '@/components/Tinymce/Tinymce.vue'
import { positiveMoney } from '@/utils/validata'
import NP from 'number-precision'
export default {
  props: {
    drawerType: {
      type: String,
      default() {
        return 'add'
      }
    },
    isshow: Boolean,
    drawerModifyData: {
      type: Object,
      default() {
        return {}
      }
    },
    collectData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {
    TinymceUeditor
  },
  data() {
    let validatorImage = (rule, value, callback) => {
      if (!this.drawerFormData.imagesUrl.length) {
        return callback(new Error('请上传商品图片'))
      } else {
        callback()
      }
    }
    let validatorMoney = (rule, value, callback) => {
      if (value) {
        if (positiveMoney(value) && Number(value)) {
          if (Number(value) > 9999.99) {
            return callback(new Error('最大值不能超过9999.99'))
          }
          callback()
        } else {
          callback(new Error('格式错误'))
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    let validateCount = (rule, value, callback) => {
      let reg = /^\d+$/
      if (!value) {
        return callback(new Error('格式错误'))
      } else {
        if (!reg.test(value) || !Number(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      }
    }
    let validateBuyStockNum = (rule, value, callback) => {
      let reg = /^\d+$/
      if (this.drawerFormData.buyStockNumType) {
        callback()
      } else if (!value) {
        return callback(new Error('格式错误'))
      } else {
        if (!reg.test(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      }
    }
    return {
      drawerTitle: '新建商品',
      isLoading: false,
      drawerFormData: {
        name: '',
        imagesUrlList: [],
        imagesUrl: [],
        listingTime: [],
        isPermanent: false,
        commodityType: '',
        virtualCommodityType: '',
        virtualNum: '',
        physicalCode: '',
        commodityPriceType: '',
        fee: '',
        points: '',
        buyStockNum: '',
        buyStockNumType: false,
        buyLimitType: '',
        buyLimitNum: '',
        details: '',
        priority: ''
      },
      drawerFormDataRuls: {
        name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        imagesUrl: [{ required: true, validator: validatorImage, trigger: 'blur' }],
        listingTime: [{ required: true, message: '请选择上架时间', trigger: ['change', 'blur'] }],
        commodityType: [{ required: true, message: '请选择商品类型', trigger: 'change' }],
        virtualCommodityType: [{ required: true, message: '请选择商品', trigger: 'change' }],
        virtualNum: [{ required: true, validator: validateCount, trigger: ['change', 'blur'] }],
        physicalCode: [{ required: true, message: '请输入商品编码', trigger: ['change', 'blur'] }],
        commodityPriceType: [
          { required: true, message: '请选择商品价格', trigger: ['change', 'blur'] }
        ],
        fee: [{ required: true, validator: validatorMoney, trigger: ['change', 'blur'] }],
        points: [
          {
            required: true,
            validator: validateCount,
            message: '请输入积分',
            trigger: ['change', 'blur']
          }
        ],
        buyStockNum: [
          {
            required: true,
            validator: validateBuyStockNum,
            message: '请输入库存数量',
            trigger: ['change', 'blur']
          }
        ],
        buyLimitType: [{ required: true, message: '请选择可兑换数', trigger: ['change', 'blur'] }],
        buyLimitNum: [
          {
            required: true,
            validator: validateCount,
            message: '请输入次数',
            trigger: ['change', 'blur']
          }
        ],
        details: [{ required: true, message: '请输入图文详情', trigger: ['change', 'blur'] }],
        priority: [
          {
            required: true,
            validator: validateCount,
            message: '请输入优先级',
            trigger: ['change', 'blur']
          }
        ]
      },
      actionUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      uploadParams: {
        prefix: 'commodityImage'
      },
      dialogImageUrl: '',
      dialogVisible: false,
      virtualCommodityTypeList: [
        {
          name: '食堂会员',
          type: 'member'
        },
        {
          name: 'AI营养师咨询',
          type: 'ai_nutritionist'
        }
      ],
      buyLimitTypeList: [
        {
          name: '不限制',
          type: 'non'
        },
        {
          name: '每天限制',
          type: 'day'
        },
        {
          name: '每周限制',
          type: 'week'
        },
        {
          name: '每月限制',
          type: 'month'
        },
        {
          name: '每人限制',
          type: 'person'
        }
      ]
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    this.initDrawerForm()
  },
  methods: {
    // 回填数据
    initDrawerForm() {
      if (this.drawerType === 'modify') {
        this.drawerTitle = '修改商品'
        this.drawerFormData.name = this.drawerModifyData.name
        this.drawerFormData.imagesUrlList = this.drawerModifyData.images_url.map(v => {
          let obj = {
            url: v,
            name: v,
            status: 'success',
            uid: v
          }
          return obj
        })
        this.drawerFormData.imagesUrl = this.drawerModifyData.images_url
        // 上架时间
        this.drawerFormData.isPermanent = this.drawerModifyData.is_permanent
        if (!this.drawerModifyData.is_permanent) {
          this.drawerFormData.listingTime = [
            this.drawerModifyData.start_date,
            this.drawerModifyData.end_date
          ]
        }
        // 是否要禁用上架时间
        this.changeIsPermanent()
        // 选择商品类型 虚拟商品
        this.drawerFormData.commodityType = this.drawerModifyData.commodity_type
        if (this.drawerModifyData.commodity_type === 'virtual') {
          this.drawerFormData.virtualCommodityType = this.drawerModifyData.virtual_commodity_type
          if (this.drawerModifyData.virtual_commodity_type === 'member') {
            this.drawerFormData.virtualNum = this.drawerModifyData.commodity_extra.day
          } else if (this.drawerFormData.virtualCommodityType === 'ai_nutritionist') {
            this.drawerFormData.virtualNum = this.drawerModifyData.commodity_extra.count
          }
        }
        // 选择了实物商品
        if (this.drawerModifyData.commodity_type === 'physical') {
          this.drawerFormData.physicalCode = this.drawerModifyData.physical_code
        }
        // 选择了商品价格
        this.drawerFormData.commodityPriceType = this.drawerModifyData.commodity_price_type
        if (this.drawerModifyData.commodity_price_type === 'money_points') {
          this.drawerFormData.fee = NP.divide(this.drawerModifyData.fee, 100)
          this.drawerFormData.points = this.drawerModifyData.points
        } else if (this.drawerModifyData.commodity_price_type === 'money') {
          this.drawerFormData.fee = NP.divide(this.drawerModifyData.fee, 100)
        } else if (this.drawerModifyData.commodity_price_type === 'points') {
          this.drawerFormData.points = this.drawerModifyData.points
        }
        // 库存数量
        if (this.drawerModifyData.buy_stock_num === -1) {
          this.drawerFormData.buyStockNumType = true
          // 是否要禁用上架时间
          this.changeIsBuyStock()
        } else {
          this.drawerFormData.buyStockNum = this.drawerModifyData.buy_stock_num
        }
        // 可兑换数
        this.drawerFormData.buyLimitType = this.drawerModifyData.buy_limit_type
        if (this.drawerModifyData.buy_limit_type !== 'non') {
          this.drawerFormData.buyLimitNum = this.drawerModifyData.buy_limit_num
        }
        this.drawerFormData.details = this.drawerModifyData.details
        this.drawerFormData.priority = this.drawerModifyData.priority
      } else {
        this.drawerTitle = '新建商品'
      }
    },
    closeClick() {
      this.visible = false
    },
    priorityText() {
      console.log(this.collectData, 7666)
      return this.collectData.priority_list && this.collectData.priority_list.length
        ? this.collectData.priority_list.join(',')
        : ''
    },
    // 传给后端格式化
    initParams() {
      let params = {
        name: this.drawerFormData.name,
        images_url: this.drawerFormData.imagesUrl,
        is_permanent: this.drawerFormData.isPermanent,
        commodity_type: this.drawerFormData.commodityType, // 商品类型
        commodity_price_type: this.drawerFormData.commodityPriceType, // 商品价格
        buy_limit_type: this.drawerFormData.buyLimitType, // 可兑换数
        details: this.drawerFormData.details, // 图文详情
        priority: this.drawerFormData.priority
      }
      // 要不要传上架时间
      if (!this.drawerFormData.isPermanent) {
        params.start_date = this.drawerFormData.listingTime[0]
        params.end_date = this.drawerFormData.listingTime[1]
      }
      // 选择商品类型 虚拟商品
      if (this.drawerFormData.commodityType === 'virtual') {
        params.virtual_commodity_type = this.drawerFormData.virtualCommodityType
        if (this.drawerFormData.virtualCommodityType === 'member') {
          params.commodity_extra = { day: this.drawerFormData.virtualNum }
        } else if (this.drawerFormData.virtualCommodityType === 'ai_nutritionist') {
          params.commodity_extra = { count: this.drawerFormData.virtualNum }
        }
      }
      // 选择了实物商品
      if (this.drawerFormData.commodityType === 'physical') {
        params.physical_code = this.drawerFormData.physicalCode
      }
      // 选择了商品价格
      if (this.drawerFormData.commodityPriceType === 'money_points') {
        params.fee = NP.times(this.drawerFormData.fee, 100)
        params.points = this.drawerFormData.points
      } else if (this.drawerFormData.commodityPriceType === 'money') {
        params.fee = NP.times(this.drawerFormData.fee, 100)
      } else if (this.drawerFormData.commodityPriceType === 'points') {
        params.points = this.drawerFormData.points
      }
      // 库存数量
      if (this.drawerFormData.buyStockNumType) {
        params.buy_stock_num = -1
      } else {
        params.buy_stock_num = this.drawerFormData.buyStockNum
      }
      // 可兑换数量
      if (this.drawerFormData.buyLimitType !== 'non') {
        params.buy_limit_num = this.drawerFormData.buyLimitNum
      }
      return params
    },
    saveSetting() {
      this.$refs.drawerFormDataRef.validate(valid => {
        if (valid) {
          if (this.drawerType === 'modify') {
            this.modifyDrawerForm()
          } else {
            this.addDrawerForm()
          }
        }
      })
    },
    // 添加
    async addDrawerForm() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundMemberPointsPointsCommodityAddPost(this.initParams())
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('修改成功')
        this.$emit('clickSaveDrawer')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改
    async modifyDrawerForm() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundMemberPointsPointsCommodityModifyPost({
          id: this.drawerModifyData.id,
          ...this.initParams()
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('修改成功')
        this.$emit('clickSaveDrawer')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 选择上架时间是否永久
    changeIsPermanent() {
      if (this.drawerFormData.isPermanent) {
        this.drawerFormDataRuls.listingTime[0].required = false
      } else {
        this.drawerFormDataRuls.listingTime[0].required = true
      }
    },
    // 选择库存数量限制
    changeIsBuyStock() {
      if (this.drawerFormData.buyStockNumType) {
        this.drawerFormDataRuls.buyStockNum[0].required = false
      } else {
        this.drawerFormDataRuls.buyStockNum[0].required = true
      }
    },
    messageTinymceUeditor(content) {
      this.drawerFormData.details = content
    },
    handelChange() {
      this.uploadParams.key =
        this.uploadParams.prefix + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    },
    handleImgSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.drawerFormData.imagesUrlList = fileList
        this.drawerFormData.imagesUrl.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleFoodImgRemove(file, type) {
      let index = this.drawerFormData.imagesUrlList.findIndex(item => item.url === file.url)
      this.drawerFormData.imagesUrl.splice(index, 1)
      this.drawerFormData.imagesUrlList.splice(index, 1)
    },
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是JPG/BMP/PNG格式!')
        return false
      }

      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    padding: 30px;
    .drawer-content {
      ::v-deep .upload-food {
        overflow: hidden;
        max-height: 830px;
        &.hide-upload {
          .el-upload--picture-card {
            display: none;
          }
        }
        .el-upload--picture-card {
          border: none;
        }
        .el-upload-dragger {
          width: 145px;
          height: 145px;
        }
        .upload-food-img {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 145px;
          height: 145px;
          img {
            max-width: 145px;
            max-height: 145px;
          }
        }
      }
    }
  }
}
</style>
