import http from '@/utils/request'
export default {
  /**
   * 线下核销订单列表
   * @param {*} params
   * @returns
   */
  apiBackgroundOrderOrderVerificationAbnormalListPost (params) {
    return http.post('/api/background_order/order_verification_abnormal/list', params)
  },
  /**
   * 线下核销订单合计
   * @param {*} params
   * @returns
   */
  apiBackgroundOrderOrderVerificationAbnormalListCollectPost (params) {
    return http.post('/api/background_order/order_verification_abnormal/list_collect', params)
  },
  /**
   * 线下核销订单列表导出
   * @param {*} params
   * @returns
   */
  apiBackgroundOrderOrderVerificationAbnormalListExportPost (params) {
    return http.post('/api/background_order/order_verification_abnormal/list_export', params)
  },
  /**
   * 离线订单关闭
   * @param {*} params
   * @returns
   */
  apiBackgroundOrderOrderVerificationAbnormalOrderClosePost (params) {
    return http.post('/api/background_order/order_verification_abnormal/order_close', params)
  },
  /**
  * ['订单审批']
  * background_order.order_review.order_payment_review_list 审核订单列表
  * @param {{page:number, page_size:number, update_cache:boolean, date_type:string, start_date:string, end_date:string, payer_group_ids:array, payer_department_group_ids:array, wallet_orgs:array, trade_no:string, review_no:string, person_no:string, phone:string, name:string, review_type:string, operator_name:string, meal_type:array, order_type:array, refund_type:array, history_review_status:array, only_rate_fee:boolean, account_review_status:string}} param page Page,page_size Page size,update_cache 刷新缓存,date_type 时间类型,start_date 开始时间,end_date 结束时间,payer_group_ids ,payer_department_group_ids ,wallet_orgs ,trade_no 订单号,review_no 审核编号,person_no 用户编号,phone 手机号,name 用户姓名,review_type 审核原因,operator_name 操作员,meal_type ,order_type ,refund_type ,history_review_status ,only_rate_fee 是否只查看手续费,account_review_status 当前账号审核状态
  * @returns {{code:number, msg:string, data:array}} - rsp
  */
  apiBackgroundOrderOrderReviewOrderPaymentReviewListPost(param) {
    return http.post('/api/background_order/order_review/order_payment_review_list', param)
  },
  /**
  * ['订单管理']
  * background_order.order_review.order_payment_review_success 审核操作同意
  * @param {{id:number, logistics_parent_id:number, reject_reason:string}} param id ID,logistics_parent_id ID,reject_reason 拒绝原因
  * @returns {{code:number, msg:string, data:framework_serializer_EmptySerializer}} - rsp
  */
  apiBackgroundOrderOrderReviewOrderPaymentReviewSuccessPost(param) {
    return http.post('/api/background_order/order_review/order_payment_review_success', param)
  },
  /**
  * ['订单管理']
  * background_order.order_review.order_payment_review_reject 审核操作拒绝
  * @param {{id:number, logistics_parent_id:number, reject_reason:string}} param id ID,logistics_parent_id ID,reject_reason 拒绝原因
  * @returns {{code:number, msg:string, data:framework_serializer_EmptySerializer}} - rsp
  */
  apiBackgroundOrderOrderReviewOrderPaymentReviewRejectPost(param) {
    return http.post('/api/background_order/order_review/order_payment_review_reject', param)
  },
  /**
  * ['订单管理']
  * background_order.order_review.bulk_order_payment_review_success 批量审核操作同意
  * @param {{ids:array, reject_reason:string}} param ids ,reject_reason 拒绝原因
  * @returns {{code:number, msg:string, data:framework_serializer_EmptySerializer}} - rsp
  */
  apiBackgroundOrderOrderReviewBulkOrderPaymentReviewSuccessPost(param) {
    return http.post('/api/background_order/order_review/bulk_order_payment_review_success', param)
  },
  /**
  * ['订单管理']
  * background_order.order_review.bulk_order_payment_review_reject 批量审核操作拒绝
  * @param {{ids:array, reject_reason:string}} param ids ,reject_reason 拒绝原因
  * @returns {{code:number, msg:string, data:framework_serializer_EmptySerializer}} - rsp
  */
  apiBackgroundOrderOrderReviewBulkOrderPaymentReviewRejectPost(param) {
    return http.post('/api/background_order/order_review/bulk_order_payment_review_reject', param)
  }
}
