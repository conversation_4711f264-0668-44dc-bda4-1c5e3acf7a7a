<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_order.finance_report.unified_order_list_export']">导出Excel</button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :index="indexMethod"
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row"/>
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText" :statistics="collect" />
        <!-- end -->
        <common-pagination
          ref="pagination"
          :total="total"
          :onPaginationChange="onPaginationChange"
        ></common-pagination>
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import CommonPagination from '../../meal-management/booking-setting/CommonPagination.vue'
import { DetailTotalSearchForm2 } from './constantsConfig'
import { getRequestParams, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'DetailTotalList',
  components: {
    CommonPagination
  },
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      isLoadingCollect: false,
      elementLoadingText: "数据正在加载，请耐心等待...",
      tableSetting: [
        { label: '序号', key: 'index', type: "index", width: '50px' },
        { label: '订单号', key: 'trade_no', width: '150px' },
        { label: '第三方订单号', key: 'out_trade_no', width: '150px' },
        { label: '创建时间', key: 'create_time', width: '160px' },
        { label: '支付时间', key: 'pay_time', width: '160px' },
        { label: '扣款时间', key: 'deduction_time', width: '160px' },
        { label: '动账钱包', key: 'wallet', width: '120px' },
        { label: '支付类型', key: 'payway_alias' },
        { label: '支付方式', key: 'sub_payway_alias' },
        { label: '访客餐支付方式', key: 'pay_method_alias' },
        { label: '订单金额', key: 'total_fee', type: 'money' },
        // { label: '动账金额', key: 'settlement_fee' },
        { label: '优惠', key: 'discount', type: 'percent' },
        { label: '优惠金额', key: 'discount_fee', type: 'money' },
        { label: '优惠类型', key: 'discount_type_alias' },
        { label: '优惠名称', key: 'discount_name' },
        { label: '券类型', key: 'coupon_type_alias' },
        { label: '抵扣金额', key: 'deduction_fee', type: 'money' },
        { label: '餐补', key: 'food_subsidy_fee', type: 'money' },
        { label: '手续费', key: 'rate_fee', type: 'money' },
        { label: '服务费', key: 'fuwu_fee', type: 'money' },
        { label: '动账金额', key: 'settlement_fee', type: 'money' },
        { label: '动账组织', key: 'wallet_org' },
        { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
        { label: '储值动账', key: 'wallet_fee', type: 'money' },
        { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
        { label: '补贴钱包余额', key: 'subsidy_balance', type: 'money' },
        { label: '储值钱包余额', key: 'wallet_balance', type: 'money' },
        { label: '赠送钱包余额', key: 'complimentary_balance', type: 'money' },
        { label: '操作类型', key: 'operate_type_alias' },
        // { label: '动账钱包余额', key: 'wallet_money', width: '110px', type: 'money' },
        { label: '餐段', key: 'meal_type' },
        { label: '姓名', key: 'payer_name' },
        { label: '手机号', key: 'payer_phone', width: '110px ' },
        { label: '人员编号', key: 'payer_person_no' },
        { label: '卡号', key: 'payer_card_no' },
        { label: '分组', key: 'payer_group' },
        { label: '部门', key: 'payer_department_group', width: '120px' },
        { label: '交易设备', key: 'device_number' },
        // { label: '交易组织', key: 'device_org' },
        { label: '操作员', key: 'controller', width: '110px' }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: deepClone(DetailTotalSearchForm2),
      collect: [ // 统计
        { key: 'consume', value: 0, label: '消费订单金额:￥', type: 'money' },
        { key: 'discounts', value: 0, label: '累计优惠金额:￥', type: 'money' },
        { key: 'balance_consume', value: 0, label: '累计动账金额:￥', type: 'money' },
        { key: 'recharge', value: 0, label: '累计充值:￥', type: 'money' },
        { key: 'draw', value: 0, label: '累计提现:￥', type: 'money' },
        { key: 'refund', value: 0, label: '累计退款:￥', type: 'money' },
        { key: 'subsidy_grant', value: 0, label: '补贴发放:￥', type: 'money' },
        { key: 'subsidy_reset', value: 0, label: '补贴清零:￥', type: 'money' },
        { key: 'total_rate_fee', value: 0, label: '手续费合计：¥', type: 'money' },
        { key: 'fuwu_fee', value: 0, label: '服务费（实收）合计：¥', type: 'money' }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'DetailTotalList',
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
  },
  mounted() {
    // this.searchFormSetting.device_org.value = [this.$store.getters.organization]
    // this.requestDeviceType()
    // this.userGroupList() // 分组
    this.getpayList() // 支付类型 /支付方式
    // this.getWalletList() // 动账钱包
    this.getLevelNameList() // 根据项目点获取公司的层级组织
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        this.requestPaymentOrderTotalList()
        this.requestPaymentOrderTotalCollect() // 获取合计
      }
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.tableData = []
      this.isFirstSearch = true
      // this.onPaginationChange({ current: 1, pageSize: 10 })
      // this.requestPaymentOrderTotalCollect() // 获取合计
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.$refs.pagination.handleCurrentChange(1, true)
        this.$refs.pagination.handleSizeChange(10, true)
        this.onPaginationChange({ current: 1, pageSize: 10 })
        this.requestPaymentOrderTotalCollect() // 获取合计
        this.isFirstSearch = false
      }
    },

    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 获取分组信息
    async userGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        page: 1,
        page_size: 9999999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.groupList = res.data.results
        this.searchFormSetting.payer_group_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },

    // 导出 列表
    handleExport() {
      this.$confirm(`确定导出？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true
      })
        .then(e => {
          const params = getRequestParams(this.searchFormSetting, 1, 9999999)
          this.$router.push({
            name: 'Excel',
            query: {
              type: 'PaymentOrderTotal',
              params: JSON.stringify(params)
            }
          })
        })
        .catch(e => {})
    },

    // 获取消费点信息
    async requestOrganizationConsumeList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrganizationOrganizationConsumeListPost({
        page: 1,
        pageSize: 9999999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.searchFormSetting.org_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },

    // 获取设备列表
    async requestDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost({})
      if (res.code === 0) {
        // this.searchFormSetting.device_name.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },

    // 获取列表数据
    async requestPaymentOrderTotalList() {
      const params = getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportUnifiedOrderListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表合计数据
    async requestPaymentOrderTotalCollect() {
      const params = getRequestParams(this.searchFormSetting)
      this.isLoadingCollect = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportUnifiedOrderListCollectPost(params)
      if (res.code === 0) {
        this.elementLoadingText = '数据正在加载，请耐心等待...'
        this.isLoadingCollect = false
        // 统计
        this.setCollectData(res)
      } else {
        this.elementLoadingText = '汇总数据加载失败，请重试。'
        this.$message.error('汇总数据加载失败，请重试。')
      }
    },
    // 获取动账钱包
    async getWalletList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportWalletListPost()
      if (res.code === 0) {
        const result = []
        res.data.result.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.wallet_org.dataList = result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetLevelNamePost()
      let arr = JSON.parse(JSON.stringify(res.data).replace(/name/g, 'label'))
      let arr2 = JSON.parse(JSON.stringify(arr).replace(/level/g, 'key'))
      this.tableSetting.splice(4, 0, ...arr2)
      // this.currentTableSetting = this.tableSetting
      this.initPrintSetting()
    },

    // 获取支付方式 / 支付类型
    async getpayList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost({
        type: 'all' // 这是这个表特定的参数
      })
      if (res.code === 0) {
        const result = []
        const result2 = []
        res.data.result.payways.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        res.data.result.sub_payways.forEach(d => {
          Object.keys(d).forEach(key => result2.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.payway_list.dataList = [...result] // 支付方式
        this.searchFormSetting.sub_payway_list.dataList = [...result2] // 支付类型
      } else {
        this.$message.error(res.msg)
      }
    },
    // 数组扁平化
    flatten(arr) {
      var res = []
      arr.map(item => {
        if (Array.isArray(item)) {
          res = res.concat(this.flatten(item))
        } else {
          res.push(item)
        }
      })
      return res
    },
    // 翻页
    onPaginationChange(data) {
      this.page = data.current
      this.pageSize = data.pageSize
      this.requestPaymentOrderTotalList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'DetailTotalList',
        params: getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = getRequestParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '明细总表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportUnifiedOrderListPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" >
.el-loading-wrapp{
  .el-loading-spinner{
    margin-top:0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
