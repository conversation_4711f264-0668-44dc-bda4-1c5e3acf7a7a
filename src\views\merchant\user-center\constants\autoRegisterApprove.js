import { getSevenDateRange } from "@/utils"

// 自注册审批-待审批
export const TABLE_SETTING_PENDING = [
  { label: '全选', key: 'selection', type: 'selection' },
  { label: '申请时间', key: 'create_time' },
  { label: '所属组织', key: 'org_name' },
  { label: '人员编号', key: 'person_no' },
  { label: '姓名', key: 'name' },
  { label: '手机号', key: 'phone' },
  { label: '申请备注', key: 'remark' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "100" }
]

// 自注册审批-已同意
export const TABLE_SETTING_FINISH = [
  { label: '审批时间', key: 'approve_time' },
  { label: '申请时间', key: 'create_time' },
  { label: '所属组织', key: 'org_name' },
  { label: '人员编号', key: 'person_no' },
  { label: '姓名', key: 'name' },
  { label: '手机号', key: 'phone' },
  { label: '申请备注', key: 'remark', showTooltip: true }
]

// 自注册审批-已拒绝
export const TABLE_SETTING_REJECT = [
  { label: '审批时间', key: 'approve_time' },
  { label: '申请时间', key: 'create_time' },
  { label: '所属组织', key: 'org_name' },
  { label: '人员编号', key: 'person_no' },
  { label: '姓名', key: 'name' },
  { label: '手机号', key: 'phone' },
  { label: '申请备注', key: 'remark', showTooltip: true },
  { label: '拒绝说明', key: 'reject_reason', showTooltip: true }
]

export const TABLE_SETTING_LIST = {
  pending: TABLE_SETTING_PENDING,
  finish: TABLE_SETTING_FINISH,
  reject: TABLE_SETTING_REJECT
}

// 自注册审批筛选设置-待审批
export const SEARCH_FORM_PENDING = {
  // date_type: {
  //   type: 'select',
  //   value: 'create_time',
  //   dataList: [
  //     {
  //       label: '创建时间',
  //       value: 'create_time'
  //     },
  //     {
  //       label: '支付时间',
  //       value: 'pay_time'
  //     }
  //   ]
  // },
  select_time: {
    label: '申请时间',
    type: 'daterange',
    value: getSevenDateRange(7),
    format: 'yyyy-MM-dd',
    clearable: false
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入',
    clearable: true
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入',
    clearable: true
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入',
    clearable: true
  },
  source_organization_id: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}


// 自注册审批筛选设置-
export const SEARCH_FORM_FINISH = {
  date_type: {
    type: 'select',
    value: 'approve_time',
    maxWidth: '100px',
    dataList: [
      {
        label: '审批时间',
        value: 'approve_time'
      },
      {
        label: '申请时间',
        value: 'create_time'
      }
    ]
  },
  select_time: {
    label: '',
    type: 'daterange',
    value: getSevenDateRange(7),
    format: 'yyyy-MM-dd',
    clearable: false
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入',
    clearable: true
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入',
    clearable: true
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入',
    clearable: true
  },
  source_organization_id: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}

export const SEARCH_FORM_LIST = {
  pending: SEARCH_FORM_PENDING,
  finish: SEARCH_FORM_FINISH,
  reject: SEARCH_FORM_FINISH
}