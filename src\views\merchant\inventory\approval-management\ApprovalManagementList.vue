<template>
  <div class="TransferOrder container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div style="margin-bottom: 20px;">
      <el-radio-group v-model="tabType" :disabled="isLoading" class="ps-radio-btn" @change="changeTabHandle">
        <el-radio-button :label="item.value" v-for="(item, index) in tabList" :key="index">{{ item.name}}</el-radio-button>
      </el-radio-group>
    </div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #record="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showDialogHandle('record', row)" >查看</el-button>
            </template>
            <template #operation="{ row }">
              <el-button v-show="tabType === 'pending'" v-permission="['background_drp.approve.agree_approve']" type="text" size="small" class="ps-text" @click="clickOperationHandle('agree', row)">同意</el-button>
              <el-button v-show="tabType === 'pending'" v-permission="['background_drp.approve.reject_approve']" type="text" size="small" class="ps-text" @click="clickOperationHandle('reject', row)" >驳回</el-button>
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)" >查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <dialog-message :show.sync="showDialog" :title="dialogTitle" :loading.sync="dialogLoading" width="435px" :showFooter="showDialogFooter" @close="closeDialogHandle"
      @cancel="clickCancleHandle" @confirm="clickConfirmHandle">
      <el-form ref="dialogFormRef" v-if="dialogType === 'reject'" :model="dialogForm" :rules="dialogrules" label-position="left" label-width="80px" size="medium">
        <el-form-item label="" label-width="0" prop="reason">
          <el-input v-model="dialogForm.reason" type="textarea" :rows="4" :maxlength="50" class="ps-input"></el-input>
        </el-form-item>
      </el-form>
      <timeline v-if="dialogType === 'record'" :timeline="timelineData" />
    </dialog-message>
    <!-- end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import Timeline from './Timeline'
import { APPROVAL_DAIBAN_SEARCHFORMSETTINGS, APPROVAL_DAIBAN_TABLESETTINGS, APPROVAL_YIBAN_SEARCHFORMSETTINGS, APPROVAL_YIBAN_TABLESETTINGS } from '../constants'
import { hasPermission } from '@/utils/permission'

export default {
  name: 'ApprovalManagementList',
  mixins: [exportExcel],
  components: { Timeline },
  data() {
    return {
      isLoading: false, // 刷新数据
      tabType: 'pending',
      tabList: [],
      xhrApi: 'apiBackgroundDrpApprovePendingListPost',
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: APPROVAL_DAIBAN_TABLESETTINGS,
      searchFormSetting: APPROVAL_DAIBAN_SEARCHFORMSETTINGS,
      // 弹窗
      showDialog: false,
      dialogLoading: false,
      dialogTitle: '驳回原因',
      dialogType: '',
      showDialogFooter: false,
      dialogForm: {
        id: '',
        reason: ''
      },
      dialogrules: {
        reason: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }]
      },
      timelineData: []
    }
  },
  created() {
    this.initTabList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.$route.query.currentPage) {
        this.currentPage = Number(this.$route.query.currentPage)
      }
      if (this.$route.query.tabType) {
        this.tabType = this.$route.query.tabType
        this.changeTabHandle(this.$route.query.tabType, false)
      } else {
        this.getApprovalManagementList()
      }
    },
    // 初始化下tab列表，根据权限走
    initTabList() {
      const defaultList = [
        {
          name: '待办事项',
          value: 'pending',
          permission: 'background_drp.approve.pending_list'
        },
        {
          name: '已办事项',
          value: 'finish',
          permission: 'background_drp.approve.finish_list'
        }
      ]
      this.tabList = []
      defaultList.forEach(v => {
        if (hasPermission(v.permission)) {
          this.tabList.push(v)
        }
      })
      if (this.tabList.length > 0) {
        this.tabType = this.tabList[0].value
      }
      this.initListSetting()
    },
    // 初始化搜索条件，接口，表格等
    initListSetting() {
      if (this.tabType === 'finish') {
        this.searchFormSetting = APPROVAL_YIBAN_SEARCHFORMSETTINGS
        this.tableSettings = APPROVAL_YIBAN_TABLESETTINGS
        this.xhrApi = 'apiBackgroundDrpApproveFinishListPost'
      } else {
        this.searchFormSetting = APPROVAL_DAIBAN_SEARCHFORMSETTINGS
        this.tableSettings = APPROVAL_DAIBAN_TABLESETTINGS
        this.xhrApi = 'apiBackgroundDrpApprovePendingListPost'
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getApprovalManagementList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key.indexOf('time') < 0) {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 切换tab
    changeTabHandle(e, changeHash = true) {
      this.initListSetting()
      this.searchHandle()
      if (changeHash) {
        this.replaceHash()
      }
    },
    // 保存下参数
    replaceHash() {
      this.$router.replace({
        name: "ApprovalManagementList",
        query: {
          currentPage: this.currentPage,
          tabType: this.tabType
        }
      })
    },
    // 获取列表数据
    async getApprovalManagementList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        data_type: this.tabType,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (this.tabType === 'pending') {
        params.date_type = 'create_time'
      }
      const [err, res] = await to(this.$apis[this.xhrApi](params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getApprovalManagementList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = ''
      switch (type) {
        case 'agree':
          title = '确定同意该审批申请？'
          apiUrl = 'apiBackgroundDrpApproveAgreeApprovePost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break;
        case 'delete':
          title = '确定删除吗？'
          apiUrl = 'apiBackgroundDrpInquiryDeletePost'
          this.showOperationDialog(title, apiUrl, { ids: [data.id] })
          break;
        case 'reject':
          this.dialogType = 'reject'
          this.dialogTitle = '驳回原因'
          this.showDialogFooter = true
          this.dialogForm.id = data.id
          this.dialogForm.reason = ''
          this.showDialog = true
          // title = '确定拒绝吗？'
          // apiUrl = 'apiBackgroundDrpApproveRejectApprovePost'
          // this.showOperationDialog(title, apiUrl, { id: data.id, reject_reason: this.dialogForm.reason })
          break;
      }
    },
    // 显示操作弹窗
    showOperationDialog(title, apiUrl, params) {
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis[apiUrl](params)
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getApprovalManagementList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    showDialogHandle(type, data) {
      this.dialogType = type
      if (type === 'record') {
        this.dialogTitle = '审批流程'
        // this.timelineData = data.record_list
        this.timelineData = data.all_process_record
      }
      this.showDialog = true
    },
    closeDialogHandle() {
      if (this.$refs.dialogFormRef) this.$refs.dialogFormRef.resetForm()
      this.timelineData = []
      this.showDialog = false
    },
    clickConfirmHandle(e) {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          this.sendRejectHandle()
        }
      })
    },
    clickCancleHandle() {},
    async sendRejectHandle() {
      if (this.dialogLoading) return
      this.dialogLoading = true
      let params = {
        id: this.dialogForm.id,
        reject_reason: this.dialogForm.reason
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpApproveRejectApprovePost(params))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialog = false
        this.getApprovalManagementList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳转
    gotoHandle(type, row) {
      // 新增
      if (type === 'detail') {
        this.$router.push({
          name: 'ApprovalManagementDetail',
          query: {
            id: row.id
          }
        })
        return
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.TransferOrder{
  .w-medium{
    width: 140px;
    height: 40px;
    &.m-r-20{
      margin-right: 20px;
    }
  }
}
</style>
