<template>
  <!--表26 集体用餐配送单位配送记录 -列表 -->
  <div class="assignment-ledger-permission container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandler"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="addDistributionUnit" v-permission="['background_fund_supervision.ledger_food_safety.delivery_unit_config_add']">配送单位管理</button-icon>
          <button-icon color="origin" @click="historyDialogVisible = true">历史记录</button-icon>
          <button-icon color="origin" @click="gotoPrint">打 印</button-icon>
          <button-icon color="origin" @click="handleExport" v-permission="['background_fund_supervision.ledger_food_safety.collective_dining_delivery_record_export']">导 出</button-icon>
        </div>
      </div>
      <div class="table-content" style="padding-bottom: 0">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          :max-height="600"
        >
          <table-column v-for="(item, index) in currentTableSetting" :key="index" :col="item">
            <template #delivery_unit="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showCheckRecord(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[5, 10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->

      <!-- 配送单位管理添加编辑 -->
      <delvery-unit-management-drawer
        :visible.sync="isShowDrawer"
        ledgerSerialNumber="24"
        :confirmShow="false"
        :showFooter="true"
        :printShow="true"
        cancelText="关闭"
      ></delvery-unit-management-drawer>

      <!-- 历史记录弹窗 -->
      <HistoryRecordDialog
        :visible.sync="historyDialogVisible"
        title="历史记录"
        type="DeliveryUnit"
        api="apiBackgroundFundSupervisionLedgerFoodSafetyOperateLogList"
        @close="historyDialogVisible = false"
      >
        <!-- 操作前内容插槽 -->
        <template #beforeContent="{ row }">
          <div>{{ row.extra && row.extra.before && row.extra.before.length ? row.extra.before.join('、') : '--' }}</div>
        </template>
        <!-- 操作后内容插槽 -->
        <template #afterContent="{ row }">
          <div>{{ row.extra && row.extra.after && row.extra.after.length ? row.extra.after.join('、') : '--' }}</div>
        </template>
      </HistoryRecordDialog>

      <!-- 查看配送单位小弹窗 -->
      <el-dialog :title="`${unitInfo.delivery_unit_name}信息`" :visible.sync="unitDialogVisible" width="40%" v-if="unitInfo">
        <el-descriptions :column="2">
          <el-descriptions-item label="配送单位名称">{{ unitInfo.delivery_unit_name }}</el-descriptions-item>
          <el-descriptions-item label="营业执照号码">{{ unitInfo.business_license }}</el-descriptions-item>
          <el-descriptions-item label="食品经营许可证">{{ unitInfo.aptitude_name }}</el-descriptions-item>
          <el-descriptions-item label="许可证有效期至">{{ unitInfo.end_date }}</el-descriptions-item>
          <el-descriptions-item label="配送合同起止时间">{{ unitInfo.delivery_contract_time[0] }} - {{ unitInfo.delivery_contract_time[1] }}</el-descriptions-item>
          <el-descriptions-item label="地址">{{ unitInfo.address }}</el-descriptions-item>
          <el-descriptions-item label="法定代表人">{{ unitInfo.contact_name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ unitInfo.contact_Phone }}</el-descriptions-item>
          <el-descriptions-item label="食品安全管理员">{{ unitInfo.food_safety_admin }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ unitInfo.food_safety_admin_Phone }}</el-descriptions-item>
          <el-descriptions-item label="配送负责人">{{ unitInfo.delivery_manager }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ unitInfo.delivery_manager_Phone }}</el-descriptions-item>
        </el-descriptions>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="unitDialogVisible = false">关 闭</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import {
  SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN,
  TABLE_HEAD_DATA_COLLECTIVE_MEAL_DELIVERY_UNIT_DELIVERY_RECORD
} from './constants'
import DelveryUnitManagementDrawer from './compontents/DelveryUnitManagementDrawer'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import HistoryRecordDialog from './compontents/HistoryRecordDrawer.vue'
export default {
  name: 'JitiYongcanManagementLedger',
  mixins: [exportExcel],
  data() {
    return {
      printType: 'JitiYongcanManagementLedger',
      historyDialogVisible: false,
      unitDialogVisible: false,
      unitInfo: null, // 配送单位信息
      isShowDrawer: false, // 配送单位管理抽屉
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_COLLECTIVE_MEAL_DELIVERY_UNIT_DELIVERY_RECORD), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_COLLECTIVE_MEAL_DELIVERY_UNIT_DELIVERY_RECORD), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN) // 查询表单配置
    }
  },
  created() {
    const date = this.$route.query.date
    if (date) {
      this.searchFormSetting.select_time.value = [date, date]
    }
    this.initLoad()
  },
  components: {
    DelveryUnitManagementDrawer,
    HistoryRecordDialog
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetCollectiveDiningDeliveryRecord(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = this.processedDataList(deepClone(results))
        // 动态处理表头数据
        this.setTableHead(deepClone(results))
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态设置表头数据
    setTableHead(dataList) {
      const data = dataList
      let maxFoodInfoObj = null
      let maxLength = 0
      // 重置表头到初始状态，避免重复添加
      this.currentTableSetting = deepClone(TABLE_HEAD_DATA_COLLECTIVE_MEAL_DELIVERY_UNIT_DELIVERY_RECORD)
      for (const item of data) {
        const currentLength = item.food_info.length
        if (currentLength > maxLength) {
          maxLength = currentLength
          maxFoodInfoObj = item.food_info
        }
      }
      // 如果存在food_info数据，则动态添加表头
      if (maxFoodInfoObj && maxFoodInfoObj.length > 0) {
        // 对于每个菜品，添加菜品名称和温度两列
        for (let index = 0; index < maxFoodInfoObj.length; index++) {
          // 添加菜品名称列
          this.currentTableSetting.push({
            label: `菜品名称${index + 1}`,
            key: `dishName${index + 1}`
          })
          // 添加到达中心温度列
          this.currentTableSetting.push({
            label: `到达中心温度`,
            key: `temperature${index + 1}`,
            width: '120'
          })
        }
      }
    },
    // 动态处理下表格数据
    processedDataList(list) {
      const processedArr = list.map(item => {
        // 复制原对象（浅拷贝）
        const newItem = { ...item }
        // 遍历 food_info 数组，动态添加序号属性
        item.food_info.forEach((food, index) => {
          const idx = index + 1 // 序号从1开始
          newItem[`dishName${idx}`] = food.dishName
          newItem[`temperature${idx}`] = food.temperature + '℃'
        })
        return newItem
      })
      return processedArr
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 查看配送单位
    showCheckRecord(row) {
      console.log(row);
      this.unitInfo = row.extra || null
      if (!this.unitInfo) {
        this.$message.info('暂无配送单位信息')
        return
      }
      this.unitDialogVisible = true
    },
    // 配送单位管理
    addDistributionUnit() {
      this.isShowDrawer = true
    },
    // 导出
    async handleExport() {
      const option = {
        // type: 'ExportCanteenMealReport',
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetCollectiveDiningDeliveryRecordExport',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.currentTableSetting)
      tableSetting[1].width = '300'
      // tableSetting = tableSetting.filter(item => item.key !== 'delivery_unit')
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '集体用餐配送单位配送记录',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetCollectiveDiningDeliveryRecord', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
.food-info-item {
  display: flex;
  align-items: center;
}
</style>
