<template>
  <!-- dialog start -->
  <dialog-message :show.sync="visible" :loading="dialogLoading" :title="dialogTitle" :width="dialogWidth" @close="closeDialog">
    <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogrules" :label-width="formLabelWidth" size="medium" class="RecipeTemplateDialog">
      <div class="form-block">
        <el-form-item label="模板名称" prop="name" class="inline-block">
          <el-input v-model="dialogForm.name" :maxlength="20" class="w-200"></el-input>
        </el-form-item>
        <el-form-item label="应季食材优先生成" label-width="140px" prop="seasonalPriority" class="inline-block">
          <el-switch v-model="dialogForm.seasonalPriority" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
      </div>
      <div class="meal-attr">
        <el-form-item v-for="(item, key) in formSetting" :key="key" label="" prop="" label-width="30px">
          <h3>{{ item.label }}</h3>
          <el-form-item v-for="child in item.children" :key="child.key" :label="child.label" :prop="'attrData.'+ item.key +'.'+ child.key" :rules="dialogrules.integerInput" class="inline-block m-b-20">
            <el-input v-model="dialogForm.attrData[item.key][child.key]" class="w-100"></el-input>
            <span class="m-l-10">道</span>
          </el-form-item>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="seasonalPriority">
        <el-input type="textarea" :rows="4" v-model="dialogForm.remark" placeholder="请输入备注" class="ps-input" style="width: 87%;"
        :maxlength='50' show-word-limit></el-input>
      </el-form-item>
    </el-form>
    <div slot="tool" class="text-right m-t-40">
      <el-button :disabled="dialogLoading" class="ps-cancel-btn" @click="cancleDialog">取消</el-button>
      <el-button :disabled="dialogLoading" class="ps-btn" type="primary" @click="submitHandler">确定</el-button>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { integer } from '@/utils/validata'
import { deepClone } from '@/utils'
import { MEAL_TYPES } from '@/utils/constants'

export default {
  name: 'RecipeTemplateDialog',
  // 重新定义下v-model的prop和触发的event
  // 为什么要重新定义的为了区别其它嵌套组件使用的v-model
  model: {
    prop: 'showDialog',
    event: 'changeShow'
  },
  props: {
    // 绑定的数据
    showDialog: {
      required: true
    },
    dialogTitle: {
      type: String,
      default: '新建模板'
    },
    // 类型
    type: {
      type: String,
      default: 'add'
    },
    // 弹窗数据源
    infoData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dialogWidth: {
      type: String,
      default: '800px'
    },
    // label宽度
    formLabelWidth: {
      type: [String, Number],
      default: '100px'
    },
    // form size
    formSize: {
      type: String,
      default: 'medium'
    },
    // 关闭的回调
    closehandle: Function,
    // 确定回调
    confirmhandle: Function
  },
  data() {
    let validataInput = (rule, value, callback) => {
      if (value) {
        if (!integer(value)) {
          callback(new Error('仅支持输入整数'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      dialogLoading: false, // loading
      dialogForm: {
        name: '', // 模板名称
        seasonalPriority: false, // 应季食材优先生成
        attrData: {},
        remark: '' // 备注
      },
      dialogrules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        integerInput: [{ validator: validataInput, trigger: 'change' }] // 校验是否为整数
      },
      formSetting: [],
      mealList: MEAL_TYPES,
      templateAttributeList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.showDialog
      },
      set(val) {
        this.$emit('changeShow', val)
      }
    }
  },
  watch: {
    showDialog(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {
    // for (const key in DIALOGfORMSETTING) {
    //   this.$set(this.dialogForm.attrData, key, DIALOGfORMSETTING[key].default)
    // }
    this.getFoodAttributeList()
  },
  mounted() {
  },
  methods: {
    init() {
      if (this.type === 'modify') {
        this.dialogForm.name = this.infoData.name
        this.dialogForm.seasonalPriority = this.infoData.seasonal_priority
        this.dialogForm.remark = this.infoData.remark
        if (this.infoData.category_data && this.infoData.category_data.length > 0) {
          this.infoData.category_data.forEach(meal => {
            const categoryJson = meal.category_json_data
            for (const key in this.dialogForm.attrData[meal.meal_type]) {
              if (categoryJson[key] !== '') {
                this.$set(this.dialogForm.attrData[meal.meal_type], key, categoryJson[key])
              }
            }
          });
        }
      }
    },
    // 获取菜品分类数据
    async getFoodAttributeList() {
      this.isLoading = true
      let [err, res] = await this.$to(
        this.$apis.apiBackgroundFoodFoodCategoryListPost(
          {
            page: 1,
            page_size: 99999
          }
        )
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.setDefaultAttrData(res.data.results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置餐段分类数据
    setDefaultAttrData(results) {
      let defaultAttr = {}
      if (results) {
        this.templateAttributeList = results.map(v => {
          let obj = {
            label: v.name,
            key: v.id
          }
          defaultAttr[v.id] = ''
          return obj
        })
        this.formSetting = this.mealList.map(v => {
          this.$set(this.dialogForm.attrData, v.value, deepClone(defaultAttr))
          return {
            label: v.label,
            key: v.value,
            children: deepClone(this.templateAttributeList)
          }
        })
      } else {
        this.templateAttributeList.map(v => {
          defaultAttr[v.key] = ''
        })
        this.mealList.map(v => {
          this.$set(this.dialogForm.attrData, v.value, deepClone(defaultAttr))
        })
      }
    },
    closeDialog() {
      this.resetForm()
      this.closehandle && this.closehandle()
    },
    cancleDialog() {
      // this.resetForm()
      this.closehandle && this.closehandle()
      this.visible = false
    },
    confirmDialog() {
      this.confirmhandle && this.confirmhandle()
      // this.$emit('confirmdialog')
    },
    // 提交按钮点击事件
    submitHandler() {
      if (this.isLoading) return this.$message.error('请不要重复点击提交！')
      this.$refs.dialogFormRef.validate(async valid => {
        if (valid) {
          let params = this.formatTemplateParams()
          if (this.type === 'modify') {
            this.modifyTemplate(params)
          } else {
            this.addTemplate(params)
          }
        }
      })
    },
    // 格式化模板参数
    formatTemplateParams() {
      let params = {
        name: this.dialogForm.name,
        seasonal_priority: this.dialogForm.seasonalPriority
      }
      if (this.dialogForm.remark) {
        params.remark = this.dialogForm.remark
      }
      let categoryData = []
      for (const meal in this.dialogForm.attrData) {
        const categoryObj = this.dialogForm.attrData[meal]
        let categoryJson = {}
        for (const key in categoryObj) {
          if (categoryObj[key] !== '') {
            categoryJson[key] = Number(categoryObj[key])
          } else {
            categoryJson[key] = 0
          }
        }
        categoryData.push({
          meal_type: meal,
          category_json_data: categoryJson
        })
      }
      params.category_data = categoryData
      if (this.type === 'modify') {
        params.id = this.infoData.id
      }
      return params
    },
    // 发送添加模板请求
    async addTemplate(params) {
      this.dialogLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundFoodMenuTemplateAddPost(params))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        // this.$closeCurrentTab(this.$route.path)
        this.confirmDialog()
        this.visible = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 发送编辑模板请求
    async modifyTemplate(params) {
      this.dialogLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundFoodMenuTemplateModifyPost(params))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        // this.$closeCurrentTab(this.$route.path)
        this.confirmDialog()
        this.visible = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重置表单
    resetForm() {
      const dialogFormRef = this.$refs.dialogFormRef
      if (dialogFormRef) {
        // 移除校验
        dialogFormRef.clearValidate()
      }
      // 重置表单数据
      this.dialogForm = {
        name: '', // 模板名称
        seasonalPriority: false, // 应季食材优先生成
        attrData: {},
        remark: '' // 备注
      }
      this.setDefaultAttrData()
    }
  }
};
</script>

<style scoped lang="scss">
.RecipeTemplateDialog {
  .meal-attr {
    max-height: 460px;
    overflow-y: auto;
  }
  .m-b-20 {
    margin-bottom: 20px;
  }
  .w-auto {
    width: 80%;
  }
  .w-100 {
    width: 100px;
  }
  .w-200 {
    width: 200px;
  }
}
</style>
