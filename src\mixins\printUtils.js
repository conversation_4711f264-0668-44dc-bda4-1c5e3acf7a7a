// 打印混入
export default {
  data() {
    return {
      hasResizeEvent: false
    }
  },
  beforeDestroy() {
    this.unbindEvents()
  },
  methods: {
    // 初始化窗口变化监听
    initResize() {
      this.hasResizeEvent = true
      window.addEventListener('resize', this.resizeTableHandler)
      this.resizeTableHandler()
    },
    // 移除监听事件
    unbindEvents() {
      if (this.hasResizeEvent) {
        window.removeEventListener('resize', this.resizeTableHandler)
      }
    },
    // 重置表格样式，importance
    resizeTableHandler() {
      this.$nextTick(() => {
        let colgroupEl = document.querySelectorAll('colgroup')
        let colTableEl = document.querySelectorAll('table')
        // 要给个延时，因为不知道element-ui的table设置table宽度是什么时候执行的
        setTimeout(() => {
          // 当存在节点时执行
          if (colgroupEl && colgroupEl.length > 0) {
            for (let i = 0; i < colgroupEl.length; i++) {
              colgroupEl[i].remove()
            }
          }
          // 当存在节点时执行
          if (colTableEl && colTableEl.length > 0) {
            for (let k = 0; k < colTableEl.length; k++) {
              // 为什么要设置table宽度100%呢，因为发现打印时行内样式优先级比其它的高，即使使用了important也无法覆盖行内的样式
              // 到底是不是这个原因有待研究
              colTableEl[k].setAttribute('style', 'width: 100%;')
            }
          }
          // window.print()
        }, 50)
      })
    }
  }
}
