<template>
  <div class="discount-limit">
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <div class="tab-box m-b-20">
        <el-radio-group v-model="tabType" class="ps-radio-btn">
          <el-radio-button label="rule" v-permission="['background_marketing.discount_limit.list']">规则配置</el-radio-button>
          <el-radio-button label="record" v-permission="['background_marketing.discount_limit.discount_limit_cardinfo_mealtype_list']">使用明细</el-radio-button>
        </el-radio-group>
      </div>
      <div v-if="tabType === 'rule'">
        <search-form ref="searchRef" :loading="isLoading" @search="searchHandle"
          :form-setting="searchFormSetting" @reset="handlerReset"></search-form>
        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-title">
              <span>数据列表</span>
              <!-- <span style="font-size: 14px;color: #ccc;">
                优惠限制仅一级组织可设置，且对所有组织生效
              </span> -->
            </div>
            <div class="align-r">
              <button-icon color="origin" type="add" @click="adddIscountLimitHandle('add')" v-permission="['background_marketing.discount_limit.add']">
                新建限制
              </button-icon>
            </div>
          </div>

          <!-- table-content start -->
          <div class="table-content">
            <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
              <table-column v-for="item in tableSetting" :key="item.key" :col="item">
                <template #status="{ row }">
                  <el-switch :value="row.is_enable" active-color="#ff9b45" inactive-color="#C0C4CC" v-permission="['background_marketing.discount_limit.enable']"
                    @change="changeStatusModify(row)"></el-switch>
                </template>
                <template #operation="{ row }">
                  <el-button type="text" size="small" class="ps-bule" @click="handlerShowChooseUser(row)">
                    查看人员
                  </el-button>
                  <el-button type="text" size="small" class="ps-origin" @click="adddIscountLimitHandle('modify', row)">
                    查看规则
                  </el-button>
                </template>
              </table-column>
            </el-table>
            <!-- table content end -->
          </div>
          <!-- 分页 start -->
          <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage"
            :page-size.sync="pageSize" :layout="'total, prev, pager, next, jumper'" :total="total"></pagination>
          <!-- 分页 end -->
        </div>
      </div>
      <rule-details v-if="tabType === 'record'" />
    </div>
    <!-- 弹窗 -->
    <discount-limit-form-dialog :show="showLimitFormDialog" @close="closeLimitFormDialog" :dialog-type="dialogType"
      :dialog-title="dialogTitle" ref="discountDialogRef"></discount-limit-form-dialog>
    <!--选择人员弹窗-->
    <choose-user-dialog ref="chooseMainUserDialog" :isshow.sync="isShowMainUserDialog" @confirm="confirmMainUserDialog"
      dialog-type="limit" :limit-id="itemlimitId" @close="closeMainUserDialog" />
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import DiscountLimitFormDialog from './components/DiscountLimitFormDialog'
import ChooseUserDialog from './components/ChooseUserDialog'
import RuleDetails from "./RuleDetails"
export default {
  name: 'discountLimit',
  components: {
    DiscountLimitFormDialog,
    RuleDetails,
    ChooseUserDialog
  },
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '名称', key: 'name' },
        { label: '限制类型', key: 'limit_type_alias' },
        { label: '限制周期', key: 'discount_cycle_alias' },
        { label: '备注', key: 'remark', showTooltip: true },
        { label: '状态', key: 'status', slotName: 'status', type: 'slot' },
        { label: '操作人', key: 'operator_name' },
        { label: '操作时间', key: 'update_time' },
        {
          label: '操作',
          key: 'operation',
          type: 'slot',
          slotName: 'operation',
          fixed: 'right',
          width: '200'
        }
      ],
      tableData: [],
      currentPage: 1,
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: {
        name: {
          type: 'input',
          label: '名称',
          value: '',
          placeholder: '请输入规则名称'
        }
        // groups: {
        //   type: 'groupSelect',
        //   label: '分组',
        //   value: [],
        //   placeholder: '请选择分组',
        //   multiple: true,
        //   collapseTags: true
        // }
      },
      showLimitFormDialog: false, // 是否显示弹窗
      tabType: 'rule',
      isShowMainUserDialog: false,
      itemlimitId: '', // 限制规则id
      dialogType: '', // 弹窗类型
      dialogTitle: '' // 弹窗标题
    }
  },
  created() {
    // 先获取权限判断
    let permissionList = this.$store.getters.allPermissions || []
    if (permissionList && Array.isArray(permissionList)) {
      this.tabType = permissionList.includes('background_marketing.discount_limit.list') ? 'rule' : permissionList.includes('background_marketing.discount_limit.discount_limit_cardinfo_mealtype_list') ? 'record' : ''
    }
    console.log("permissionList", this.tabType);
    this.getDiscountLimitList()
  },
  mounted() { },
  methods: {
    refreshHandle() {
      if (this.$refs.searchRef) {
        this.$refs.searchRef.resetForm()
      }
      this.currentPage = 1
      this.tabType = 'rule'
      this.getDiscountLimitList()
    },
    handlerReset() {
      this.searchHandle()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getDiscountLimitList()
    }, 300),
    // 获取列表数据
    async getDiscountLimitList() {
      this.isLoading = true
      // params
      const [err, res] = await to(this.$apis.apiBackgroundMarketingDiscountLimitListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        return this.$message.error("获取数据失败")
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        this.total = data.count || 0
        this.tableData = data.results || []
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除
    clickDel(data) {
      this.$confirm(`是否删除当前优惠限制，删除后将不再限制对应分组内用户的优惠额度。`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundMarketingDiscountLimitCardCancelPost({
              id: data.id
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                }
              }
              this.getDiscountLimitList()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => { })
        .catch(e => { })
    },
    // 添加规则弹窗
    adddIscountLimitHandle(type, row = {}) {
      this.dialogType = type
      this.dialogTitle = type === 'add' ? '新增规则' : '查看规则'
      if (type === 'modify' && this.$refs.discountDialogRef) {
        this.$refs.discountDialogRef.setFormData(row)
      }
      this.showLimitFormDialog = true
    },
    // 翻页
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDiscountLimitList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 关闭弹窗
    closeLimitFormDialog() {
      this.showLimitFormDialog = false
      this.getDiscountLimitList()
    },
    // 修改状态
    changeStatusModify(row) {
      console.log("row", row);
      this.$confirm(`是否修改当前状态？`, `提示`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(async () => {
          const [err, res] = await to(this.$apis.apiBackgroundMarketingDiscountLimitEnablePost({
            id: row.id,
            is_enable: !row.is_enable
          }))
          if (err) {
            return this.$message.error('修改状态失败')
          }
          if (res && res.code === 0) {
            row.is_enable = !row.is_enable
            this.$message.success('修改状态成功')
            this.getDiscountLimitList()
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    confirmMainUserDialog() {
      this.isShowMainUserDialog = false
    },
    closeMainUserDialog() {
      this.isShowMainUserDialog = false
    },
    handlerShowChooseUser(row) {
      console.log("handlerShowChooseUser", row);
      this.itemlimitId = row.id + ''
      this.isShowMainUserDialog = true
    }
  }
}
</script>
<style lang="scss" scoped></style>
