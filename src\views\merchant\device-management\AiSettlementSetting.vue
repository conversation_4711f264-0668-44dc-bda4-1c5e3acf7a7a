<template>
  <div class="AiSettlementSetting container-wrapper" v-loading="isLoading">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper" style="margin-top: 0px;margin-bottom: 50px;">
      <div class="table-header">
        <div>
          <div class="table-title"
            style="display: flex;align-items: center; justify-content: space-between;width: 380px;">适用组织：
            <organization-select class="search-item-w ps-input w-250" placeholder="请选择所属组织" :isLazy="false"
              :multiple="false" :check-strictly="true" v-model="organizationId" :append-to-body="true">
            </organization-select>
          </div>
        </div>
        <div style="padding-right:20px;">
          <el-button size="small" type="primary" class="ps-origin-btn" @click="checkForm('self')">保存</el-button>
          <el-button size="small" type="primary" class="ps-plain-btn" @click="openOtherOrg">适用到其他组织</el-button>
        </div>
      </div>
      <div class="setting-wrap">
        <el-form :model="settingForm" :rules="settingFormRules" ref="settingForm" label-width="150px"
          label-position="left">
          <div class="m-l-15">
            <el-form-item prop="foodDistinguishMode" label="自动同步留样仪菜谱">
              <el-radio-group class="ps-radio" v-model="settingForm.sync_reserved_sample">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="foodDistinguishMode" label="合并商品至菜谱">
              <el-radio-group class="ps-radio" v-model="settingForm.contain_goods">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="菜品相识度阈值" prop="">
              <el-slider class="detection-range-slider w-250" v-model="settingForm.food_similarity" :min="0" :max="1"
                :step="0.1"></el-slider>
            </el-form-item>
            <el-form-item label="饮料相识度阈值">
              <el-slider class="detection-range-slider w-250" v-model="settingForm.drink_similarity" :min="0" :max="1"
                :step="0.1"></el-slider>
            </el-form-item>
            <el-form-item label="水果相似度阈值">
              <el-slider class="detection-range-slider w-250" v-model="settingForm.fruit_similarity" :min="0" :max="1"
                :step="0.1"></el-slider>
            </el-form-item>
          </div>

        </el-form>
      </div>
    </div>
    <setting-dialog :isshow.sync="dialogVisible" :type="dialogType" :title="dialogTitle" :confirm="dialogConfirm"
      @otherOrgConfirm="otherOrgConfirm" />
  </div>
</template>

<script>
import SettingDialog from './components/SettingDialog.vue'
import OrganizationSelect from '@/components/OrganizationSelect'
import { to } from '@/utils'

export default {
  name: 'AiSettlementSetting',
  components: {
    OrganizationSelect,
    SettingDialog
  },
  props: {},
  data() {
    let validLiveScore = (rule, value, callback) => {
      if (value > 100) {
        callback(new Error('活体检测分数不能高于100'))
      } else if (value < 50) {
        callback(new Error('活体检测分数不能低于50'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      organizationId: '',
      organizationIds: [],
      settingForm: {
        sync_reserved_sample: true,
        contain_goods: true,
        food_similarity: 0.8,
        drink_similarity: 0.8,
        fruit_similarity: 0.8
      },
      settingFormRules: {
        mealFixedPrice: [{ required: true, validator: validLiveScore, trigger: 'blur' }]
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogType: ''
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    organizationId(newValue, oldValue) {
      console.log("organizationId", newValue, oldValue);
      if (!newValue) {
        this.resetForm()
      } else {
        this.getSettingDetail()
      }
    }
  },
  mounted() { },
  methods: {
    initLoad() {
      this.organizationId = Number(sessionStorage.getItem('organization'))
      // this.getSettingDetail()
    },
    // 刷新页面
    refreshHandle() {
      this.organizationId = Number(sessionStorage.getItem('organization'))
      this.getSettingDetail()
    },
    // 格式化参数
    formateParams() {
      let params = {
        sync_reserved_sample: this.settingForm.sync_reserved_sample,
        contain_goods: this.settingForm.contain_goods,
        food_similarity: this.settingForm.food_similarity,
        drink_similarity: this.settingForm.drink_similarity,
        fruit_similarity: this.settingForm.fruit_similarity
      }
      return params
    },
    // 检测提交
    checkForm(type) {
      this.$refs.settingForm.validate(valid => {
        if (valid) {
          let params = this.formateParams()
          if (type === 'self') {
            if (!this.organizationId) {
              this.$message.error('请选择适用组织')
              return
            }
            params.mode = 'self'
            params.org_no = this.organizationId
          } else {
            params.mode = 'other'
            params.org_nos = this.organizationIds
          }
          this.saveAiMachineSetting(params)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    // 保存设置
    async saveAiMachineSetting(params) {
      this.$confirm(`当前消费点可能正在就餐，是否保存？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            this.isLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundDeviceSettleInfoAddPost(params))
            this.isLoading = false
            if (err) {
              return this.$message.error(err.message || '保存失败')
            }
            if (res && res.code === 0) {
              this.$message.success('保存成功')
              this.getSettingDetail()
            } else {
              this.$message.error(res.msg || '保存失败')
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => { })
    },
    // 获取设置详情
    async getSettingDetail() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDeviceSettleInfoDetailsPost({
        org_no: this.organizationId
      }))
      this.isLoading = false
      if (err) {
        this.resetForm()
        return this.$message.error(err.message || '获取失败')
      }
      if (res && res.code === 0) {
        let resultData = res.data || {}
        if (resultData) {
          let data = resultData.setting || {}
          this.settingForm.sync_reserved_sample = data.sync_reserved_sample || false
          this.settingForm.contain_goods = data.contain_goods || false
          this.settingForm.food_similarity = data.food_similarity || 0
          this.settingForm.drink_similarity = data.drink_similarity || 0
          this.settingForm.fruit_similarity = data.fruit_similarity || 0
        } else {
          this.resetForm()
        }
      } else {
        this.$message.error(res.msg)
        this.resetForm()
      }
    },
    openOtherOrg() {
      this.dialogVisible = true
      this.dialogTitle = '请选择适用的组织'
      this.dialogType = 'other'
    },
    dialogConfirm() {
      this.dialogVisible = false
    },
    otherOrgConfirm(val) {
      this.dialogVisible = false
      this.organizationIds = val
      this.checkForm('other')
    },
    resetForm() {
      this.settingForm = {
        sync_reserved_sample: true,
        contain_goods: true,
        food_similarity: 0.8,
        drink_similarity: 0.8,
        fruit_similarity: 0.8
      }
    }
  }
}
</script>

<style lang="scss">
.AiSettlementSetting {

  .setting-wrap {
    margin: 0 20px;
  }

  .detection-range-slider {
    .el-slider__bar {
      background-color: #ff9b45;
    }

    .el-slider__button {
      border: 2px solid #ff9b45;
    }
  }
}
</style>
