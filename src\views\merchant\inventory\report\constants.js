import { getSevenDateRange } from '@/utils'
// 出入库类型
export const INVENTORY_TYPE = [
  {
    label: '采购入库',
    value: 'PURCHASE_ENTRY'
  },
  // {
  //   label: '赠予入库',
  //   value: 'BESTOW_ENTRY'
  // },
  {
    label: '调拨入库',
    value: 'BORROW_ENTRY'
  },
  {
    label: '调拨出库',
    value: 'BORROW_EXIT'
  },
  {
    label: '损耗出库',
    value: 'EXPEND_EXIT'
  },
  {
    label: '过期出库',
    value: 'OVERDUE_EXIT'
  },
  {
    label: '采购退货出库',
    value: 'REFUND_EXIT'
  },
  {
    label: '领料出库',
    value: 'RECEIVE_EXIT'
  },
  {
    label: '归还入库',
    value: 'RETURN_ENTRY'
  },
  {
    label: '归还出库',
    value: 'RETURN_EXIT'
  },
  {
    label: '其他入库',
    value: 'OTHER_ENTRY'
  },
  {
    label: '其他出库',
    value: 'OTHER_EXIT'
  }
]
// 物资重量修改统计 筛选设置
export const MATERIAL_WEIGHT_MODIFICATION_STATISTICS_SEARCH_SETTING = {
  select_time: {
    type: 'daterange',
    format: 'yyyy-MM-dd',
    label: '创建时间',
    labelWidth: '100px',
    clearable: false,
    value: getSevenDateRange(7)
  },
  warehouse_name: {
    type: 'input',
    value: '',
    label: '仓库名称',
    placeholder: '请输入仓库名称',
    maxlength: 20
  },
  materials_name: {
    type: 'input',
    value: '',
    label: '物资名称',
    placeholder: '请输入物资名称',
    maxlength: 20
  },
  // record_type_alias: {
  //   type: 'select',
  //   label: '库存类型',
  //   clearable: true,
  //   value: [],
  //   dataList: deepClone(INVENTORY_TYPE),
  //   multiple: true,
  //   collapseTags: true
  // },
  trade_no: {
    type: 'input',
    value: '',
    maxWidth: '350px',
    label: '单据编号',
    placeholder: '请输入单据编号'
  }
}

// 物资重量修改统计 表格设置
export const MATERIAL_WEIGHT_MODIFICATION_STATISTICS_TABLE_SETTING = [
  { label: '创建时间', key: 'create_time' },
  { label: '单据编号', key: 'trade_no' },
  { label: '仓库名称', key: 'warehouse_name' },
  { label: '库存类型', key: 'record_type_alias' },
  { label: '物资分类', key: 'materail_classification_name' },
  { label: '物资名称', key: 'materials_name' },
  { label: '价格', key: 'operate_price', type: 'moneyFloat', unit: '¥' },
  { label: '原数量', key: 'original_count' },
  { label: '修改后数量', key: 'total_variation_count' },
  { label: '差额', key: 'margin_count' },
  { label: '实际合计金额', key: 'original_total_price', type: 'moneyFloat', unit: '¥' },
  { label: '修改后合计金额', key: 'total_price', type: 'moneyFloat', unit: '¥' },
  { label: '差额合计', key: 'margin_price', type: 'moneyFloat', unit: '¥' },
  { label: '附件图片', key: 'img', type: 'slot', slotName: 'imgs' },
  { label: '操作人', key: 'account' }
]
