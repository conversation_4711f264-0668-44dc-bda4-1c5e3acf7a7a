<template>
  <div class="procure-order-list container-wrapper">
    <refresh-tool v-if="showRefresh" @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          数据列表
          <span class="inline-block m-l-20 font-size-16">
            当前仓库：
            <span style="color: 000; font-weight: 700">{{ $route.query.warehouse_name }}</span>
          </span>
        </div>
        <div class="align-r">
          <button-icon color="origin" @click="showDraftHandle">草稿箱</button-icon>
          <button-icon color="origin" @click="gotoHandle('add')">新增采购单</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #document="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickOpenDocument(row)"
              >
                关联单据
              </el-button>
            </template>
            <template #operation="{ row }">
              <el-button
                v-if="row.approve_status == 'PENDING'"
                type="text"
                size="small"
                class="ps-text"
                @click="clickOperationHandle('revocation', row)"
              >
                撤回
              </el-button>
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
              <el-button
                v-if="row.order_status === 'arrive'"
                type="text"
                size="small"
                class="ps-text"
                @click="clickOperationHandle('take_delivery', row)"
              >
                确认送达
              </el-button>
              <el-button
                v-if="row.order_status === 'supplier_refuse'"
                type="text"
                size="small"
                class="ps-text"
                @click="showRefuseResult('refuse', row)"
              >
                拒收原因
              </el-button>
              <el-button
                v-if="row.approve_status === 'REJECT' || row.order_status === 'supplier_refuse'"
                type="text"
                size="small"
                class="ps-warn"
                @click="clickOperationHandle('delete', row)"
              >
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- 草稿箱 star -->
    <draft-box-dialog
      :showdialog.sync="showDraft"
      :api="draftApi"
      :params="draftParams"
      :tableSettings="drafttableSettings"
      @recovery="recoveryHandle"
    />
    <!-- 草稿箱 end -->
    <dialog-message
      :show.sync="showDialogMessage"
      top="30vh"
      :title="dialogMessageTitle"
      :width="dialogMessageWidth"
      :message="dialogMessageContent"
      :showFooter="false"
      @close="dialogMessageClose"
    ></dialog-message>
    <purchase-related-document
      :isshow.sync="documentDrawer"
      :id="selectId"
    />
    <!-- 弹窗 -->
    <!-- <div class="ps-el-drawer">
      <el-drawer
        :title="drawerType === 'add' ? '创建链接' : '编辑链接'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-radio-group v-model="inTabType" @change="changeTabHandle" class="ps-radio-btn">
            <el-radio-button v-for="(tab, index) in inTabTypeList" :key="index">{{ tab.label }}</el-radio-button>
          </el-radio-group>

          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div> -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import DraftBoxDialog from '../../components/DraftBoxDialog'
import PurchaseRelatedDocument from '../../components/related-document/PurchaseRelatedDocument'

export default {
  name: 'ProcureOfferList',
  mixins: [exportExcel],
  components: { DraftBoxDialog, PurchaseRelatedDocument },
  props: {
    showRefresh: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      warehouseId: this.$route.query.warehouse_id,
      tabType: 1,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '创建时间', key: 'create_time' },
        { label: '采购时间', key: 'purchase_time', type: 'date', isComponents: true, format: 'YYYY-MM-DD' },
        { label: '单据编号', key: 'trade_no' },
        { label: '关联单据', key: 'document', type: 'slot', slotName: 'document' },
        { label: '经手人', key: 'account_name' }, // , isComponents: true, type: 'date', format: 'YYYY-MM-DD'
        { label: '审核状态', key: 'approve_status_alias' },
        { label: '供应商', key: 'supplier_manage_name' },
        { label: '订单状态', key: 'order_status_alias' },
        { label: '预计送达时间', key: 'expect_arrival_date' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '',
          value: 'create_time',
          maxWidth: '130px',
          placeholder: '请选择',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '采购时间',
              value: 'purchase_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        },
        order_status: {
          type: 'select',
          label: '订单状态',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '待接收',
              value: 'wait_receiving'
            },
            {
              label: '待配送',
              value: 'wait_delivery'
            },
            {
              label: '配送中',
              value: 'delivering'
            },
            {
              label: '货物送达待确认',
              value: 'arrive'
            },
            {
              label: '货物送达已确认',
              value: 'confirmed'
            },
            {
              label: '供应商拒收',
              value: 'supplier_refuse'
            }
          ]
        }
      },
      // 草稿弹窗
      showDraft: false,
      drafttableSettings: [
        { label: '采购单名称', key: 'name' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right' }
      ],
      draftApi: 'apiBackgroundDrpTemplateInfoTempListPost',
      draftParams: {
        warehouse_id: +this.$route.query.warehouse_id,
        inventory_info_type: 'purchase',
        temp_type: 'draft'
      },
      // 拒收原因弹窗
      showDialogMessage: false,
      dialogMessageTitle: '拒收原因',
      dialogMessageWidth: '460px',
      dialogMessageContent: '',
      inTabType: 'purchaseOrder',
      inTabTypeList: [
        {
          label: '配送单',
          value: '1'
        },
        {
          label: '收货单',
          value: '2'
        },
        {
          label: '结算单',
          value: '3'
        }
      ],
      // 关联单据
      selectId: '',
      documentDrawer: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getProcureOfferList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getProcureOfferList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: this.warehouseId,
        // audit_status: 'approve',
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpPurchaseInfoListPost(params))
      // this.tableData = [{
      //   '1': '查看'
      // }]
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getProcureOfferList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = 'apiBackgroundDrpPurchaseInfoStatusModifyPost'
      let params = {}
      switch (type) {
        case 'refuse':
          params = {
            id: data.id,
            supplier_manage_id: data.supplier_manage_id,
            purchase_operate: 'refuse'
          }
          title = '确定拒收订单吗？'
          break
        case 'take_delivery':
          params = {
            id: data.id,
            supplier_manage_id: data.supplier_manage_id,
            purchase_operate: 'confirm'
          }
          title = '确定订单已送达吗？'
          break
        case 'revocation':
          params = {
            id: data.id,
            supplier_manage_id: data.supplier_manage_id,
            purchase_operate: 'revoke'
          }
          title = '确定撤回订单吗？'
          break
        case 'delete':
          params = {
            id: data.id,
            supplier_manage_id: data.supplier_manage_id,
            purchase_operate: 'delete'
          }
          title = '确定删除订单吗？'
          break
      }
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg || '成功')
              this.getProcureOfferList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    showDraftHandle() {
      this.showDraft = true
    },
    gotoHandle(type, row) {
      let query = {
        ...this.$route.query
      }
      if (row) {
        query.id = row.id
      }
      this.$router.push({
        name: type === 'add' ? 'ModifyPurchaseOrder' : 'PurchaseDetail',
        query: query,
        params: {
          type
        }
      })
    },
    // 草稿恢复编辑
    recoveryHandle(data) {
      let query = {
        ...this.$route.query,
        id: data.id, // 草稿id
        type: 'recovery' // 类型
      }
      this.$router.push({
        name: 'ModifyPurchaseOrder',
        query: query,
        params: {
          type: 'add'
        }
      })
    },
    // 拒收原因
    showRefuseResult(type, data) {
      this.dialogMessageContent = data.reject_reason || '空'
      this.showDialogMessage = true
    },
    dialogMessageClose() {
      this.dialogMessageContent = ''
    },
    // handleExport(row) {
    //   const option = {
    //     type: 'InquiryList',
    //     url: 'apiBackgroundDrpInquiryExportInquiryPost',
    //     params: {
    //       id: row.id
    //     }
    //   }
    //   this.exportHandle(option)
    // }
    clickOpenDocument(data) {
      this.selectId = data.id
      this.documentDrawer = true
    }
  }
}
</script>

<style lang="scss" scoped>
.procure-order-list {
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
}
</style>
