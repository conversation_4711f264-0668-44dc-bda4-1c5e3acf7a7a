<template>
  <!-- 右上角通知列表 -->
  <div id="notice-list-container" class="container-wrapper">
    <refresh-tool v-if="isShowTop" @refreshPage="refreshHandle" />
    <div class="notice-list">
      <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon :buttonData="buttonData" @markUnread="Mulchange(1)" @markRead="Mulchange(0)" @mulDelete="Mulchange(2)"></button-icon>
          </div>
        </div>
        <div class="table-content">
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            row-key="id"
            border
            @selection-change="handleSelectionChange"
            header-row-class-name="ps-table-header-row"
            :row-class-name="setRowClassNameHandle"
            class="ps-table"
          >
            <el-table-column type="selection" width="40" class-name="ps-checkbox"></el-table-column>
            <el-table-column
              prop="title"
              align="center"
              label="公告标题"
            ></el-table-column>
            <el-table-column
              prop="status_alias"
              label="公告类型"
              align="center">
              <template slot-scope="scope">
                {{ ['公告','问卷','公告-监管'][scope.row.message_type] }}
              </template>
            </el-table-column>
            <el-table-column
              prop="status_alias"
              label="重要公告"
              align="center">
              <template slot-scope="scope">
                {{ scope.row.important_msg ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="sender_name"
              align="center"
              label="来源"
            ></el-table-column>
            <el-table-column prop="post_time" align="center" label="时间">
            </el-table-column>
            <el-table-column
              prop="read_flag"
              align="center"
              label="状态"
            ></el-table-column>
            <el-table-column fixed="right" label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="getMessagesDetails(scope.row)"
                  class="ps-text"
                >
                  查看
                </el-button>
                <!-- <el-button
                  type="text"
                  size="small"
                  @click="delNotice(scope.row.id, 1)"
                  class="ps-warn-text"
                >
                  删除
                </el-button> -->
              </template>
            </el-table-column>
          </el-table>
          <!-- table end -->
          <div style="margin-top: 10px;">
            <span style="margin-right: 10px; font-size: 12px;">未读通知：{{ unreadCount }}条</span>
          </div>
        </div>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
      <!-- 查看公告详情 start -->
      <el-dialog
        :title="$t('dialog.notice_dialog')"
        :visible.sync="dialogVisible"
        width="600px"
        top="15vh"
        custom-class="ps-dialog"
        :close-on-click-modal="false"
      >
        <div class="dialog-content">
          <div class="title">{{ MsgDetails.title }}</div>
          <div class="post-time">{{ MsgDetails.post_time }}</div>
          <div class="content" v-html="MsgDetails.content"></div>
          <div v-if="MsgDetails.file_url" @click="download" class="download">
            点击下载附件：{{ MsgDetails.file_url.name }}
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="ps-btn" type="primary" @click="dialogVisible = false">
            {{ $t('dialog.close_btn') }}
          </el-button>
        </span>
      </el-dialog>
      <!-- 弹窗 end -->
      <!-- 查看公告 start -->
      <NoticeDetail :show.sync="showDetailDrawer" :detailInfo="detailInfo" type="list" @closeDialogHandle="initLoad"></NoticeDetail>
      <!-- end -->
    </div>
  </div>
</template>

<script>
import { to, debounce, getSevenDateTimeRange, parseTime } from '@/utils'
import FileSaver from 'file-saver'
import NoticeDetail from './NoticeDetail'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'NoticeList',
  components: {
    NoticeDetail
  },
  props: {
    isShowTop: {
      type: Boolean,
      default: true
    }
  },
  // mixins: [activatedLoadData],
  data() {
    let time = getSevenDateTimeRange(90)
    const defaultdate = [parseTime(time[0], '{y}-{m}-{d}'), parseTime(time[1], '{y}-{m}-{d}')];
    return {
      searchForm: {
        noticeTypeTitle: '',
        creator: ''
      },
      creatorList: [],
      tableData: [], // 列表数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      isLoading: false,
      selectDate: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      MsgDetails: {
        title: '',
        content: '',
        post_time: '',
        file_url: ''
      },
      dialogVisible: false,
      selectionVal: [],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '搜索时间',
          value: [defaultdate[0], defaultdate[1]]
        },
        title: {
          type: 'input',
          label: '公告标题',
          value: '',
          placeholder: '请选择公告标题'
        },
        message_type: {
          type: 'select',
          label: '公告类型',
          value: '',
          maxWidth: '150px',
          placeholder: '请选择',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '问卷',
              value: 1
            },
            {
              label: '公告',
              value: 0
            },
            {
              label: '公告-监管',
              value: 2
            }
          ]
        },
        important_msg: {
          type: 'select',
          label: '重要公告',
          value: '',
          maxWidth: '150px',
          placeholder: '请选择',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '是',
              value: true
            },
            {
              label: '否',
              value: false
            }
          ]
        },
        read_flag: {
          type: 'select',
          label: '公告状态',
          value: '',
          placeholder: '请选择公告状态',
          maxWidth: '150px',
          dataList: [
            { label: "全部", value: "" },
            { label: "已读", value: true },
            { label: "未读", value: false }
          ]
        },
        sender: {
          type: 'input',
          label: '来源',
          value: '',
          placeholder: '请输入来源'
        }
      },
      buttonData: [
        // {
        //   name: "标记未读",
        //   click: 'markUnread',
        //   type: 'unread',
        //   color: 'blue'
        // },
        {
          name: "标记已读",
          click: 'markRead',
          type: 'read',
          color: 'origin'
        }
        // {
        //   name: "批量删除",
        //   click: 'mulDelete',
        //   type: 'del',
        //   color: 'plain'
        // }
      ],
      unreadCount: 0,
      showDetailDrawer: false,
      detailInfo: null
    }
  },
  created() {
    // 初始化有搜索条件
    let searchTitle = this.$route.query.searchTitle
    let searchType = this.$route.query.type
    if (searchTitle && !searchType) {
      this.searchFormSetting.title.value = searchTitle
    }
    this.initLoad()
  },
  watch: {
    '$route.query.searchTitle': {
      handler(val) {
        this.searchFormSetting.title.value = val
        this.initLoad()
      }
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMsgList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getMsgList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0] + ' 00:00:00'
            params.end_time = data[key].value[1] + ' 23:59:59'
          }
        }
      }
      return params
    },
    // 获取公告列表
    async getMsgList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesGetMsgListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        this.unreadCount = res.data.unread_count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取公告详情
    async getMessagesDetails(data) {
      console.log('data', data)
      if (data.message_type === 1) {
        let id = ''
        const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesGetMsgReceivePost({
          msg_no: data.msg_no
        }))
        if (err) {
          this.$message.error(err.message)
          return
        }
        if (res.code === 0) {
          id = res.data.content
        }
        const { href } = this.$router.resolve({
          name: 'QuestionnaireDetail',
          query: {
            id: parseInt(id)
          }
        })
        window.open(href, "_blank");
      } else {
        this.detailInfo = data
        this.showDetailDrawer = true
        // this.$router.push({
        //   name: 'MerchantNoticeDetail',
        //   query: {
        //     type: 'list',
        //     msg_no: data.msg_no
        //   }
        // })
      }
    },
    // 改变公告状态
    async changeMsgStatus(id, option, num) {
      if (num === 1) {
        id = [id]
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundMessagesMessagesBulkMsgReadPost({
          msg_nos: id
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // this.getMsgNum()
        this.$message.success('操作成功')
        this.getMsgList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 批量编辑公告
    Mulchange(option) {
      if (this.selectionVal.length) {
        if (option === 2) {
          this.delNotice(this.selectionVal)
        } else {
          this.changeMsgStatus(this.selectionVal, option)
        }
      } else {
        this.$message.error('请选择需要操作的公告')
      }
    },
    // 个人公告删除
    async delNotice(id, num) {
      let text
      if (num === 1) {
        id = [id]
        text = '是否删除该公告?'
      } else {
        text = '是否删除所选公告?'
      }
      this.$confirm(text, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const [err, res] = await to(
              this.$apis.apiBackgroundMessagesMessagesGetMsgOptionsPost({
                msg_nos: id
              })
            )
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success('删除成功')
              this.getMsgList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            done()
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    handleSelectionChange(selection) {
      this.selectionVal = []
      if (selection.length) {
        this.selectionVal = selection.map(v => {
          return v.msg_no
        })
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getMsgList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMsgList()
    },
    download() {
      FileSaver.saveAs(this.MsgDetails.file_url.url, this.MsgDetails.file_url.name)
    },
    async getMsgNum() {
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesGetMsgNumPost({}))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let unreadCount = 0
        if (res.data && res.data.unread_count) {
          unreadCount = res.data.unread_count > 99 ? '99+' : res.data.unread_count
        }
        this.$root.eventHub.$emit('updateNoticeCount', unreadCount)
      } else {
        this.$message.error(res.msg)
      }
    },
    setRowClassNameHandle({ row }) {
      if (row.status_verbose === "已读") {
        return 'ash-column'
      }
    }
  }
}
</script>

<style lang="scss">
#notice-list-container {
  .ash-column{
    .cell{
      color: #bbb !important;
    }
    // background-color: #f1f1f1;
  }
  .notice-list {
    min-width: 0;
    .search-form{
      padding: 20px 20px 0;
    }
    .dialog-content {
      .title {
        font-size: 26px;
        font-weight: bold;
      }
      .post-time {
        color: #a3a3a3;
        font-size: 16px;
        margin: 13px 0;
      }
      .content {
        max-height: 400px;
        overflow-y: auto;
      }
      .download {
        color: #ff9b45;
        text-decoration: underline;
        font-style: italic;
        cursor: pointer;
        margin-top: 20px;
      }
    }
  }
}
</style>
