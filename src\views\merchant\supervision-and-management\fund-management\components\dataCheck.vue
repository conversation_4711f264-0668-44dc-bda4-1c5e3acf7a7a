<template>
  <div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #applyFee="{ row }">
              {{ computedFee(row.apply_fee) }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="getDetail(row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'详情'"
        :visible="detailDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="detailDrawerFormRef" :model="detailDrawerForm" label-width="10px" label-position="top">
            <el-form-item label="申请信息">
              <table class="m-l-30">
                <tr>
                  <td style="width: 80px;">申请人：</td>
                  <td>{{ detailDrawerForm.applicationInfo.applicant }}</td>
                </tr>
                <tr>
                  <td style="width: 80px;">申请来源：</td>
                  <td>{{ detailDrawerForm.applicationInfo.applicationSource }}</td>
                </tr>
                <tr>
                  <td style="width: 80px;">申请内容：</td>
                  <td>{{ detailDrawerForm.applicationInfo.applicationContent }}</td>
                </tr>
                <tr>
                  <td style="width: 80px;">申请金额：</td>
                  <td>{{ computedFee(detailDrawerForm.applicationInfo.amountApplied) }}</td>
                </tr>
              </table>
            </el-form-item>
            <el-form-item label="单据信息">
              <div class="m-l-30" v-if="detailDrawerForm.documentInfo.length">
                <div v-for="(item, index) in detailDrawerForm.documentInfo" :key="index">{{ item }}</div>
              </div>
              <div v-else class="m-l-30">
                <!-- <el-empty description="暂无内容" :image-size="96"></el-empty> -->
                 <span>暂无内容</span>
              </div>
            </el-form-item>
            <el-form-item label="申请凭证/附件">
              <div class="m-l-30 flex-col" v-if="detailDrawerForm.applicationDocuments.length">
                <div v-for="(item, index) in detailDrawerForm.applicationDocuments" :key="index" class="w-350 flex-b-c m-r-10 m-b-10">
                    <div class="origin">{{ item.name }}</div>
                    <div class="flex">
                      <el-button type="text" size="small" class="ps-text" v-if="computedFileType(item.name)" @click="handleClick(item.url)">查看</el-button>
                      <el-button type="text" size="small" class="ps-text" @click="downloadFile(item.url)">下载</el-button>
                    </div>
                </div>
              </div>
              <div v-else class="m-l-30">
                <!-- <el-empty description="暂无凭证/附件" :image-size="96"></el-empty> -->
                 <span>暂无凭证/附件</span>
              </div>
            </el-form-item>
            <el-form-item label="收款信息">
              <table class="m-l-30">
                <tr>
                  <td>收款人：</td>
                  <td>{{ detailDrawerForm.collectionInfo.beneficiaryAccountName }}</td>
                </tr>
                <tr>
                  <td>收款账号：</td>
                  <td>{{ detailDrawerForm.collectionInfo.receivablesAccount }}</td>
                </tr>
                <tr>
                  <td>收款银行：</td>
                  <td>{{ detailDrawerForm.collectionInfo.receivingBank }}</td>
                </tr>
              </table>
            </el-form-item>
            <el-form-item :label="'审批状态'">
              <el-timeline class="m-l-35">
                <el-timeline-item
                  :icon="item.icon"
                  :color="item.color"
                  :size="'large'"
                  v-for="(item, index) in detailDrawerForm.approvalStatus"
                  :key="index"
                  :timestamp="item.status_alias"
                  :placement="'top'">
                  <div v-for="(itemIn, indexIn) in item.data" :key="indexIn" :class="[approveMethod === 'and_approve' && index !== 0 ? 'bg-grey' : '', 'm-b-10']">
                    <!--这里做个区别，会签和其他两个区别显示-->
                    <div v-if="approveMethod !== 'and_approve'" class="flex-col">
                      <div class="w-350 flex-b-c">
                        <div>{{ itemIn.operator }}</div>
                        <div class="w-150 flex-b-c" v-if="itemIn.status !== 'PENDING'">
                          <div v-if="itemIn.status !== 'PENDING'">{{ itemIn.timestamp }}</div>
                          <i :class="itemIn.icon" :style="{'color': itemIn.color, 'fontSize': '18px'}"></i>
                        </div>
                      </div>
                      <div v-if="index > 0 && item.status !== 'REVOKE' && itemIn.reason" style="color: #000">
                        审批意见：{{ itemIn.reason }}
                      </div>
                    </div>
                    <div v-else>
                      <div v-for="(childItem, childIndex) in itemIn" :key="childIndex" class="flex-col">
                        <div class="w-350 flex-b-c">
                          <div>{{ childItem.operator }}</div>
                          <div class="w-150 flex-b-c" v-if="childItem.status !== 'PENDING'">
                            <div v-if="childItem.status !== 'PENDING'">{{ childItem.timestamp }}</div>
                            <i :class="[childItem.icon, 'icon']" :style="{'color': childItem.color, 'fontSize': '18px'}"></i>
                          </div>
                        </div>
                        <div v-if="index > 0 && childItem.status !== 'REVOKE' && childItem.reason" style="color: #000">
                          审批意见：{{ childItem.reason }}
                        </div>
                      </div>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </el-form-item>
            <el-form-item label="申请备注">
              <div class="p-l-30">
                <el-input v-model="detailDrawerForm.applicationRemarks" type="textarea" :autosize="{ minRows: 4, maxRows: 6}" resize="none" show-word-limit maxlength="200" :disabled="true"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="" prop="uploadBillingVoucher" :rules="[{required: true, message: '请上传结算凭证', trigger: ['change', 'blur']}]">
              <div class="flex-start">
                <div class="f-w-700 m-r-10" style="color: #606266;"><span class="red">*</span>上传结算凭证</div>
                <el-upload
                  v-loading="uploading" :action="''" class="upload-w" :disabled="true" :file-list="fileListsForDetail" :limit="5" :show-file-list="true">
                  <div class="flex-center">
                    <el-button class="m-r-20" size="small" icon="el-icon-plus" :disabled="true">添加文件</el-button>
                    <div slot="tip" class="el-upload__tip">不超过20M</div>
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button class="w-100 ps-origin-btn" @click="cancelHandle">确认</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { debounce, getRequestParams, deepClone, divide, to, getUrlFilename } from '@/utils'
import dayjs from 'dayjs'
import FileSaver from 'file-saver'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  components: {
    ElImageViewer
  },
  props: {
    selectTab: {
      type: Number,
      default: 0
    }
  },
  watch: {
    selectTab: {
      handler: function(newVal, oldVal) {
        switch (newVal) {
          case 1:
            this.searchFormSetting.date_type.dataList[0].label = '审批时间'
            this.tableSetting[2].label = '审批时间'
            this.tableSetting[8].hidden = false
            this.tableSetting[9].hidden = true
            break
          case 2:
            this.searchFormSetting.date_type.dataList[0].label = '拒绝时间'
            this.tableSetting[2].label = '拒绝时间'
            this.tableSetting[8].hidden = true
            this.tableSetting[9].hidden = false
            break
          case 3:
            this.searchFormSetting.date_type.dataList[0].label = '撤销时间'
            this.tableSetting[2].label = '撤销时间'
            this.tableSetting[8].hidden = true
            this.tableSetting[9].hidden = true
            break
        }
        // 重置搜索条件
        this.searchFormSetting.date_type.value = 'apply'
        this.searchFormSetting.select_time.value = [
          dayjs()
            .subtract(7, 'day')
            .format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD')
        ]
        this.searchFormSetting.approve_no.value = ''
        this.searchFormSetting.apply_source.value = ''
        this.tableData = []
        this.getDataList()
      },
      immediate: true
    }
  },
  data() {
    const RECENTSEVEN = [
      dayjs()
        .subtract(7, 'day')
        .format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ]
    return {
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'apply',
          dataList: [
            {
              label: '',
              value: 'approve'
            },
            {
              label: '申请时间',
              value: 'apply'
            }
          ]
        },
        organization_ids: {
          type: 'organizationSelect',
          value: [],
          label: '所属组织',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: [],
          multiple: true,
          checkStrictly: true,
          collapseTags: true,
          clearable: true
        },
        select_time: {
          type: 'daterange',
          label: '日期筛选',
          clearable: false,
          value: RECENTSEVEN
        },
        approve_no: {
          type: 'input',
          label: '申请单号',
          value: '',
          placeholder: '请输入申请单号'
        },
        apply_source: {
          type: 'select',
          label: '申请来源',
          value: '',
          placeholder: '请选择申请来源',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '财务申请',
              value: 'cw'
            },
            {
              label: '采购单转化',
              value: 'cgd'
            }
          ]
        }
      },
      isLoading: false,
      tableData: [
        {
          key: 1
        }
      ],
      tableSetting: [
        { label: '所属组织', key: 'organization_name' },
        { label: '申请单号', key: 'approve_no' },
        { label: '申请时间', key: 'apply_time' },
        { label: '', key: 'apply_time' },
        { label: '申请金额', key: 'apply_fee', type: "slot", slotName: "applyFee" },
        { label: '申请人', key: 'operator' },
        { label: '申请来源', key: 'apply_source_alias' },
        { label: '申请内容', key: 'apply_content', showTooltip: true },
        { label: '申请备注', key: 'apply_remark', showTooltip: true },
        { label: '审批节点', key: 'approve_node', hidden: true },
        { label: '拒绝原因', key: 'reject_reason', hidden: true },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "200" }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      uploading: false, // 上传加载中
      fileListsForDetail: [],
      selectData: '',
      detailDrawerShow: false,
      detailDrawerForm: {
        applicationInfo: {
          applicant: '',
          applicationSource: '',
          applicationContent: '',
          amountApplied: ''
        },
        documentInfo: [],
        applicationDocuments: [],
        collectionInfo: {
          beneficiaryAccountName: '',
          receivablesAccount: '',
          receivingBank: ''
        },
        approvalStatus: [],
        applicationRemarks: '',
        uploadBillingVoucher: []
      },
      approveMethod: '',
      showImagePreview: false,
      previewList: []
    }
  },
  computed: {
    computedFee() {
      return d => {
        return '￥' + divide(d, 100)
      }
    },
    computedFileType() {
      return d => {
        const fileType = ['jpeg', 'jpg', 'png', 'tiff', "JPEG", "PNG", "BMP", "TIFF", "HEIF", "JPG"]
        let result = d.split(".")[1]
        if (!fileType.includes(result)) {
          return false
        } else {
          return true
        }
      }
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    getDataList() {
      this.isLoading = true
      let params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      params.approval_status = 'pending'
      let api = ''
      switch (this.selectTab) {
        case 1:
          api = 'apiBackgroundFundSupervisionFinanceApproveAgreeListPost'
          break
        case 2:
          api = 'apiBackgroundFundSupervisionFinanceApproveRejectListPost'
          break
        case 3:
          api = 'apiBackgroundFundSupervisionFinanceApproveRevokeListPost'
          break
      }
      this.$apis[api](params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.tableData = deepClone(res.data.results || [])
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList()
    },
    gotoExport() {
      let params = getRequestParams(this.searchFormSetting, this.currentPage, this.totalCount)
      params.approval_status = 'pending'
      const option = {
        url: 'apiBackgroundApproveApproveFundListExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    getDetail(data) {
      this.selectData = data
      this.approveMethod = data.approve_method
      let obj = {
        applicationInfo: {
          applicant: data.operator,
          applicationSource: data.apply_source_alias,
          applicationContent: data.apply_content,
          amountApplied: data.apply_fee
        },
        documentInfo: [],
        applicationDocuments: deepClone(data.image_json || []),
        collectionInfo: {
          beneficiaryAccountName: data.account_person,
          receivablesAccount: data.account_number,
          receivingBank: data.account_bank
        },
        approvalStatus: [
          {
            icon: 'el-icon-check',
            color: '#14ce84',
            status_alias: '提交申请',
            status: 'pending',
            data: [
              {
                icon: 'el-icon-success',
                color: '#14ce84',
                status_alias: '提交申请',
                status: 'pending',
                account_id: '',
                timestamp: data.apply_time,
                operator: `${data.operator}`
              }
            ]
          }
        ],
        applicationRemarks: data.apply_remark,
        uploadBillingVoucher: deepClone(data.settlement_json || [])
      }
      if (obj.applicationDocuments.length) {
        let arr = obj.applicationDocuments.map(item => {
          if (typeof item === 'string') {
            let objIn = {
              name: getUrlFilename(item).split('?')[0],
              url: item
            }
            return objIn
          } else {
            return item
          }
        })
        obj.applicationDocuments = deepClone(arr)
      }
      console.log('obj.applicationDocuments', obj.applicationDocuments)
      this.detailDrawerForm = deepClone(obj)
      this.fileListsForDetail = deepClone(obj.uploadBillingVoucher)
      this.getApprovalProcess()
      this.detailDrawerShow = true
    },
    async getApprovalProcess() {
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionFinanceApproveApproveAccountsPost({
        id: this.selectData.appropriation_id
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let result = res.data
        let newStatus = []
        // 先判断一下是不是或签，或签的话全部账号显示在一个流程里就可以了
        switch (this.approveMethod) {
          case 'one_by_one_approve': {
            // 依次审批还是拿回approve_account_info组成数组显示吧
            // 先循环res.data
            result.forEach(async item => {
              let obj = {
                icon: 'el-icon-check',
                color: '#ff9b45',
                status_alias: '待审批',
                status: 'pending',
                data: []
              }
              let statusList = []
              if (item.approve_account_info && item.approve_account_info.length) {
                // 将这个审批账号里的全部账号拉出来组成目标数据丢尽obj.data
                item.approve_account_info.forEach(itemIn => {
                  let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                  let child = {
                    icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                    color: this.switchColor(itemIn.approve_status),
                    status_alias: itemIn.approve_status_alias,
                    status: itemIn.approve_status,
                    account_id: itemIn.account_id,
                    timestamp: itemIn.approve_time,
                    operator: `${itemIn.account_name}`,
                    reason: itemIn.approve_reason
                  }
                  statusList.push(itemIn.approve_status)
                  obj.data.push(child)
                })
                let agreeFlag = statusList.some(item => item === 'AGREE')
                let rejectFlag = statusList.some(item => item === 'REJECT')
                // 把上传的obj根据里面的内容重新赋值一下
                obj.icon = agreeFlag ? 'el-icon-check' : (rejectFlag ? 'el-icon-close' : 'el-icon-more')
                obj.color = agreeFlag ? '#14ce84' : (rejectFlag ? '#fd594e' : '#ff9b45')
                obj.status_alias = agreeFlag ? '审批通过' : (rejectFlag ? '拒绝审批' : '待审批')
                obj.status = agreeFlag ? 'AGREE' : (rejectFlag ? 'REJECT' : 'PENDING')
              }
              newStatus.push(obj)
            })
            // 判断是否需要到资金平台
            if (result[0].approve_platform && result[0].approve_platform === 'zj') {
              let obj = {
                icon: 'el-icon-check',
                color: '#14ce84',
                status_alias: '待资金监管平台审批',
                status: 'pending',
                data: []
              }
              newStatus.push(obj)
              await this.getZJApprovalProcess(newStatus)
            }
            break
          }
          case 'and_approve': {
            // 如果是会签，将每个审批账号做成一个数组塞到data里面
            let obj = {
              icon: result[0].approve_status === 'agree' ? 'el-icon-check' : (result[0].approve_status === 'pending' ? 'el-icon-more' : 'el-icon-close'),
              color: this.switchColor(result[0].approve_status),
              status_alias: result[0].approve_status_alias,
              status: result[0].approve_status,
              data: []
            }
            if (result[0].approve_account_info && result[0].approve_account_info.length) {
              result[0].approve_account_info.forEach(item => {
                if (item.length) {
                  let arr = []
                  item.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name}`,
                      reason: itemIn.approve_reason
                    }
                    arr.push(child)
                  })
                  obj.data.push(arr)
                }
              })
              newStatus.push(obj)
            }
            // 判断是否需要到资金平台
            if (result[0].approve_platform && result[0].approve_platform === 'zj') {
              let obj = {
                icon: 'el-icon-check',
                color: '#14ce84',
                status_alias: '待资金监管平台审批',
                status: 'pending',
                data: []
              }
              newStatus.push(obj)
              await this.getZJApprovalProcess(newStatus)
            }
            break
          }
          case 'or_approve': {
            // 如果是或签，将所有账号放在一个流程内
            let obj = {
              icon: result[0].approve_status === 'agree' ? 'el-icon-check' : (result[0].approve_status === 'pending' ? 'el-icon-more' : 'el-icon-close'),
              color: this.switchColor(result[0].approve_status),
              status_alias: result[0].approve_status_alias,
              status: result[0].approve_status,
              data: []
            }
            if (result[0].approve_account_info && result[0].approve_account_info.length) {
              result[0].approve_account_info.forEach(item => {
                if (item.length) {
                  item.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name}`,
                      reason: itemIn.approve_reason
                    }
                    obj.data.push(child)
                  })
                }
              })
              newStatus.push(obj)
            }
            // 判断是否需要到资金平台
            if (result[0].approve_platform && result[0].approve_platform === 'zj') {
              let obj = {
                icon: 'el-icon-check',
                color: '#14ce84',
                status_alias: '待资金监管平台审批',
                status: 'pending',
                data: []
              }
              newStatus.push(obj)
              await this.getZJApprovalProcess(newStatus)
            }
            break
          }
        }
        this.addRejectStatus(this.selectData, newStatus)
        // 如果这时没撤回，再往里塞待拨款状态节点
        if (this.selectData.appropriation_status !== 'revoked' && this.selectData.appropriation_status !== 'pending') {
          this.setAppropriationProcess(newStatus)
        }
        if (this.approveMethod !== 'and_approve') {
          this.detailDrawerForm.approvalStatus.push(...newStatus)
        } else {
          let obj = deepClone(this.detailDrawerForm.approvalStatus[0])
          obj.data = [[obj.data[0]]]
          this.detailDrawerForm.approvalStatus = [obj, ...newStatus]
        }
        return
      }
    },
    // 获取资金平台审批流程
    async getZJApprovalProcess(arr) {
      console.log('arr', arr)
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionFinanceApproveZjApproveAccountsPost({
        id: this.selectData.id
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let result = res.data
        let newStatus = []
        console.log('看看资金流程的规则', this.zjApproveMethod)
        // 先判断一下是不是或签，或签的话全部账号显示在一个流程里就可以了
        switch (this.zjApproveMethod) {
          case 'one_by_one_approve': {
            // 依次审批还是拿回approve_account_info组成数组显示吧
            // 先循环res.data
            result.forEach(item => {
              let obj = {
                icon: 'el-icon-check',
                color: '#ff9b45',
                status_alias: '待审批',
                status: 'pending',
                data: []
              }
              let statusList = []
              if (item.approve_account_info && item.approve_account_info.length) {
                // 将这个审批账号里的全部账号拉出来组成目标数据丢尽obj.data
                item.approve_account_info.forEach(itemIn => {
                  let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                  let child = {
                    icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                    color: this.switchColor(itemIn.approve_status),
                    status_alias: itemIn.approve_status_alias,
                    status: itemIn.approve_status,
                    account_id: itemIn.account_id,
                    timestamp: itemIn.approve_time,
                    operator: `${itemIn.account_name}`,
                    reason: itemIn.approve_reason
                  }
                  statusList.push(itemIn.approve_status)
                  obj.data.push(child)
                })
                let agreeFlag = statusList.some(item => item === 'AGREE')
                let rejectFlag = statusList.some(item => item === 'REJECT')
                // 把上传的obj根据里面的内容重新赋值一下
                obj.icon = agreeFlag ? 'el-icon-check' : (rejectFlag ? 'el-icon-close' : 'el-icon-more')
                obj.color = agreeFlag ? '#14ce84' : (rejectFlag ? '#fd594e' : '#ff9b45')
                obj.status_alias = agreeFlag ? '审批通过' : (rejectFlag ? '拒绝审批' : '待审批')
                obj.status = agreeFlag ? 'AGREE' : (rejectFlag ? 'REJECT' : 'PENDING')
              }
              newStatus.push(obj)
            })

            // 判断是否需要到资金平台
            if (result[0].approve_platform && result[0].approve_platform === 'zj') {
              let obj = {
                icon: 'el-icon-check',
                color: '#14ce84',
                status_alias: '待资金监管平台审批',
                status: 'pending',
                data: []
              }
              newStatus.push(obj)
              await this.getZJApprovalProcess(newStatus)
            }
            break
          }
          case 'and_approve': {
            // 如果是会签，将每个审批账号做成一个数组塞到data里面
            let obj = {
              icon: result[0].approve_status === 'agree' ? 'el-icon-check' : (result[0].approve_status === 'pending' ? 'el-icon-more' : 'el-icon-close'),
              color: this.switchColor(result[0].approve_status),
              status_alias: result[0].approve_status_alias,
              status: result[0].approve_status,
              data: []
            }
            if (result[0].approve_account_info && result[0].approve_account_info.length) {
              result[0].approve_account_info.forEach(item => {
                if (item.length) {
                  let arr = []
                  item.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name}`,
                      reason: itemIn.approve_reason
                    }
                    arr.push(child)
                  })
                  obj.data.push(arr)
                }
              })
              newStatus.push(obj)
            }
            break
          }
          case 'or_approve': {
            // 如果是或签，将所有账号放在一个流程内
            let obj = {
              icon: result[0].approve_status === 'agree' ? 'el-icon-check' : (result[0].approve_status === 'pending' ? 'el-icon-more' : 'el-icon-close'),
              color: this.switchColor(result[0].approve_status),
              status_alias: result[0].approve_status_alias,
              status: result[0].approve_status,
              data: []
            }
            if (result[0].approve_account_info && result[0].approve_account_info.length) {
              result[0].approve_account_info.forEach(item => {
                if (item.length) {
                  item.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name}`,
                      reason: itemIn.approve_reason
                    }
                    obj.data.push(child)
                  })
                }
              })
              newStatus.push(obj)
            }
            break
          }
        }
        console.log(newStatus)
        newStatus.forEach(item => {
          arr.push(item)
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置待拨款流程
    setAppropriationProcess(arr) {
      console.log('看看待拨款的情况', arr, this.selectData)
      if (this.selectData.zj_approve_status === 'agree') {
        let obj = {
          icon: 'el-icon-check',
          color: '#14ce84',
          status_alias: '待拨款',
          status: 'agree',
          data: this.selectData.approve_method === 'and_approve' ? [
            [{
              icon: 'el-icon-success',
              color: '#14ce84',
              status_alias: '',
              status: 'agree',
              account_id: '',
              timestamp: this.toBeAllocated,
              operator: ``
            }]
          ] : [
            {
              icon: 'el-icon-success',
              color: '#14ce84',
              status_alias: '',
              status: 'agree',
              account_id: '',
              timestamp: this.toBeAllocated,
              operator: ``
            }
          ]
        }
        arr.push(obj)
        if (this.selectData.zj_appropriation_status === 'appropriated') {
          // 如果是已拨款，再插入已拨款的状态
          let obj = {
            icon: 'el-icon-check',
            color: '#14ce84',
            status_alias: '已拨款',
            status: 'agree',
            data: this.selectData.approve_method === 'and_approve' ? [
              [{
                icon: 'el-icon-success',
                color: '#14ce84',
                status_alias: '',
                status: 'agree',
                account_id: '',
                timestamp: this.selectData.appropriation_time
              }]
            ] : [
              {
                icon: 'el-icon-success',
                color: '#14ce84',
                status_alias: '',
                status: 'agree',
                account_id: '',
                timestamp: this.selectData.appropriation_time
              }
            ]
          }
          arr.push(obj)
        }
      }
    },
    addRejectStatus(data, statusArr) {
      // 处理状态
      if (this.selectTab === 3) {
        let obj = {
          icon: 'el-icon-error',
          color: '#909399',
          status_alias: '撤销申请',
          status: 'REVOKE',
          timestamp: data.approve_time,
          operator: `${data.operator}`
        }
        let status = {
          icon: 'el-icon-close',
          color: '#909399',
          status_alias: '撤销申请',
          status: 'REVOKE',
          data: []
        }
        // 用历史操作处理旧数据
        let record = []
        if (data.approve_record && data.approve_record.record && data.approve_record.record.length) {
          record = deepClone(data.approve_record.record)
        }
        // 如果是撤销的，直接塞
        switch (data.approve_method) {
          case 'one_by_one_approve': {
            // 先把最后一个干掉
            statusArr.pop()
            statusArr.forEach(item => {
              let approvalStatusArr = []
              item.data.forEach(itemIn => {
                let obj = record.filter(recordItem => recordItem.account_id === itemIn.account_id)
                if (obj.length) {
                  // 如果有就改
                  let childStatus = obj[0].status === 'PENDING' || obj[0].status === 'AGREE'
                  itemIn.icon = childStatus ? 'el-icon-success' : 'el-icon-error'
                  itemIn.color = this.switchColor(obj[0].status)
                  itemIn.status_alias = obj[0].content
                  itemIn.status = obj[0].status
                  itemIn.timestamp = obj[0].time
                } else {
                  // 没有就置空
                  itemIn.icon = ''
                  itemIn.timestamp = ''
                }
                approvalStatusArr.push(itemIn.status)
              })
              // 根据statusArr里的状态去判断
              let flag = approvalStatusArr.some(item => item === 'REJECT')
              // 审批账号里面的改好了，轮到该审批账号本身的状态了
              item.icon = flag ? 'el-icon-close' : 'el-icon-check'
              item.color = flag ? this.switchColor('') : this.switchColor('AGREE')
              item.status_alias = flag ? '' : '审批通过'
              item.status = flag ? '' : 'AGREE'
            })
            // 依次审批的话因为最后一个审批账号没审就撤销了，把最后那个干掉
            status.data = [{ ...obj }]
            statusArr.push(status)
            break
          }
          case 'and_approve': {
            statusArr[0].data.forEach(item => {
              item.forEach(itemIn => {
                let obj = record.filter(recordItem => recordItem.account_id === itemIn.account_id)
                if (obj.length) {
                  // 如果有就改
                  itemIn.icon = obj[0].status === 'AGREE' ? 'el-icon-success' : 'el-icon-error'
                  itemIn.color = this.switchColor(obj[0].status)
                  itemIn.status_alias = obj[0].content
                  itemIn.status = obj[0].status
                  itemIn.timestamp = obj[0].time
                } else {
                  // 没有就置空
                  itemIn.icon = ''
                  itemIn.timestamp = ''
                }
              })
            })
            // 审批账号里面的改好了，轮到该审批账号本身的状态了
            statusArr[0].icon = 'el-icon-more'
            statusArr[0].color = this.switchColor('PENDING')
            statusArr[0].status_alias = '待审批'
            statusArr[0].status = 'PENDING'
            status.data = [[{ ...obj }]]
            statusArr.push(status)
            break
          }
          case 'or_approve': {
            // 先把最后一个干掉
            statusArr.pop()
            status.data = [{ ...obj }]
            statusArr.push(status)
            break
          }
        }
      }
    },
    switchColor(status) {
      let color = ''
      switch (status) {
        case 'PENDING':
          color = '#ff9b45'
          break
        case 'AGREE':
          color = '#14ce84'
          break
        case 'REJECT':
          color = '#fd594e'
          break
        case 'pending':
          color = '#ff9b45'
          break
        case 'agree':
          color = '#14ce84'
          break
        case 'reject':
          color = '#fd594e'
          break
        default:
          color = '#909399'
      }
      return color
    },
    cancelHandle() {
      this.fileListsForDetail = []
      this.detailDrawerShow = false
    },
    handleClick(img) {
      this.previewList = [img]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    downloadFile(url) {
      this.step = 1
      // window.open(this.templateUrl);
      let spliturl = url.split('/')
      let filsename = spliturl[spliturl.length - 1]
      FileSaver.saveAs(url, filsename)
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-grey {
  padding: 10px 20px;
  border: 1px solid #E7E9EF;
  border-radius: 4px;
}
.icon {
  font-size: 18px;
}
::v-deep .el-timeline-item__node--large {
  left: -4px;
  width: 18px !important;
  height: 18px !important;
}
</style>
