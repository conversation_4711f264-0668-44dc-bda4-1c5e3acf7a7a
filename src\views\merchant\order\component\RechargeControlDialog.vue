<template>
  <div>
    <el-dialog :title="dialogTitle" :visible.sync="isShowDialog" width="600px" custom-class="ps-dialog"
      :close-on-click-modal="false" :close-on-press-escape="false" show-close @close="handlerCancel">
      <el-form ref="dialogForm" label-width="150px" :model="dialogEditData" :rules="dialogRules">
        <div v-if="dialogType == 'agree' || dialogType == 'refund'">
          <el-form-item label="申请提现金额：">
            <div class="w-350">{{ formatPrice(dialogEditData.applyWithdrawFee) }}</div>
          </el-form-item>
          <el-form-item label="储值钱包余额：">
            <div class="w-350">{{ formatPrice(dialogEditData.walletBalance) }}</div>
          </el-form-item>
          <el-form-item label="赠送钱包余额：">
            <div class="w-350">{{ formatPrice(dialogEditData.complimentaryBalance) }}</div>
          </el-form-item>
          <el-form-item label="实际提现余额：" prop='price' v-if="dialogType == 'agree'">
            <el-input v-model="dialogEditData.price" placeholder="请输入" clearable class="ps-input w-330">
            </el-input>
            <div class="ps-inline m-l-5">元</div>
          </el-form-item>
          <el-form-item label="备注：" prop="remark">
            <el-input type="textarea" :rows="6" v-model="dialogEditData.remark" placeholder="请输入备注" class="ps-input w-350"
              maxlength='100' show-word-limit></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div v-if="dialogType == 'detail'">
        <el-table :data="orderList" stripe header-row-class-name="ps-table-header-row" max-height="500px">
          <el-table-column label="订单号" prop="name" align="center">
          </el-table-column>
        </el-table>
      </div>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="handlerCancel" v-if="dialogType !== 'detail'" v-permission="['background_order.order_withdraw.approval_withdraw_1']">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="handlerSumit" v-loading="isComfirmLoading" v-permission="['background_order.order_withdraw.approval_withdraw']">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { to, divide } from "@/utils"
export default {
  name: 'rechargeControlDialog',
  props: {
    dialogTitle: { // 标题
      type: String,
      default: '提示'
    },
    show: {
      type: Boolean,
      default: false
    },
    dialogType: { // 弹窗类型
      type: String,
      default: ''
    }
  },
  data() {
    let validataPrice = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        // let number = /((^[1-9][0-9])|(([0]\.\d{1,2}|^[1-9][0-9]\.\d{1,2})))$/
        let number = /^[0-9]\d*\.?\d{0,2}$/
        let originFee = divide(this.dialogEditData.walletBalance)
        console.log("validataPrice", originFee, value);
        if (!number.test(value) || value === '0.0' || value === '0.00') {
          callback(new Error('请输入大于零的数值，最多为两位小数'))
        } else if (originFee < value) {
          callback(new Error('提现余额不能大于储值钱包余额'))
        } else {
          callback()
        }
      }
    };
    return {
      dialogEditData: {
        id: '',
        applyWithdrawFee: '',
        walletBalance: '',
        complimentaryBalance: '',
        price: '',
        remark: ''
      },
      isComfirmLoading: false,
      dialogRules: {
        price: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { validator: validataPrice, trigger: 'blur' }
        ],
        remark: [
          { required: false, message: '请输入备注', trigger: 'blur' }
        ]
      },
      orderList: [] // 订单列表
    }
  },
  computed: {
    isShowDialog: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:input', val)
      }
    }
  },
  watch: {
    show(newValue) {
      if (newValue) {
        this.$set(this.dialogRules.remark[0], 'required', this.dialogType === "refund")
      } else {
        this.dialogEditData = {
          id: '',
          applyWithdrawFee: '',
          walletBalance: '',
          complimentaryBalance: '',
          price: '',
          remark: ''
        }
      }
      console.log("watch", newValue, this.dialogRules.remark);
    }
  },
  created() {
  },
  methods: {
    // 动态加载远程数据
    async loadDepartmentList(tree, resolve) {
      console.log('load')
      let params = {
        status: 'enable',
        page: 1,
        page_size: 99999999
      }
      if (tree.level === 0) {
        params.level = 0
      } else {
        params.parent = tree.data.id
      }
      const [err, res] = await to(this.$apis.apiCardServiceCardDepartmentGroupListPost(params))
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(item => {
          if (item.has_children) {
            item.is_leaf = false
          } else {
            item.is_leaf = true
          }
        })
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    // 取消
    handlerCancel() {
      this.$emit("dialogClose", true)
    },
    // 确认
    handlerSumit() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          this.$emit("dialogConfirm", this.dialogType, this.dialogEditData)
        } else {
          return false;
        }
      });
    },
    // 设置订单列表
    setOrderList(list) {
      this.orderList = []
      if (Array.isArray(list)) {
        list.forEach(item => {
          this.orderList.push({ name: item })
        })
      }
    },
    // 设置
    setDialogData(data) {
      console.log("setDialogData", data);
      this.$set(this.dialogEditData, 'applyWithdrawFee', data.apply_withdraw_fee || 0)
      this.$set(this.dialogEditData, 'walletBalance', data.balance || 0)
      this.$set(this.dialogEditData, 'price', divide(data.balance) || 0)
      this.$set(this.dialogEditData, 'complimentaryBalance', data.complimentary_balance || 0)
      this.$set(this.dialogEditData, 'id', data.id || "")
    },
    // 设置按钮loading
    setBtnLoading(isFlag) {
      this.isComfirmLoading = isFlag
    },
    // 格式化价格
    formatPrice(price) {
      if (!price) {
        return 0
      }
      return divide(price)
    }

  }
}
</script>

<style lang="scss" scope>
.w-330 {
  width: 330px;
}

.w-350 {
  width: 350px;
}
</style>
