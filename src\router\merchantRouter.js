// import Layout from '@/layout'
// 注意layout一定要最后生成再替换成import的方式引入，不然会触发页面重复渲染
let Layout = 'Layout'

const merchant = [
  // 一级-首页
  {
    path: '/application',
    component: Layout,
    redirect: '/application/application_center',
    alwaysShow: false,
    name: 'MerchantApplication',
    meta: {
      title: 'home',
      permission: ['homepage_group']
    },
    children: [
      {
        path: 'application_center',
        component: () =>
          import(/* webpackChunkName: "home_page" */ '@/views/merchant/application/ApplicationCenter'),
        name: 'MerchantApplicationCenter',
        meta: {
          noCache: true,
          title: 'application_center',
          permission: ['app_center']
        }
      },
      {
        path: 'home_page',
        component: () =>
          import(/* webpackChunkName: "home_page" */ '@/views/merchant/home-page/HomePage'),
        name: 'MerchantHomePage',
        meta: {
          noCache: true,
          title: 'home_page',
          permission: ['business_data']
        }
      },
      {
        path: 'jiaofei_data_report',
        component: () =>
          import(
            /* webpackChunkName: "jiaofei_data_report" */ '@/views/merchant/jiaofei-center/JiaofeiDataReport'
          ),
        name: 'MerchantJiaofeiDataReport',
        meta: {
          noCache: true,
          title: 'jiaofei_data_report',
          permission: ['background_order.order_jiao.jiaofei_statistical']
        }
      }
    ]
  },
  // 一级-订单管理
  {
    path: '/order_management',
    component: Layout,
    redirect: '/order_management/consumption',
    alwaysShow: true,
    name: 'MerchantOrderManagement',
    meta: {
      title: 'order',
      permission: ['pay_order_management']
    },
    children: [
      {
        path: 'consumption',
        component: () =>
          import(/* webpackChunkName: "Consumption" */ '@/views/merchant/order/Consumption'),
        name: 'MerchantConsumption',
        meta: {
          noCache: true,
          title: 'consumption',
          permission: ['background_order.order_payment']
        }
      },
      {
        path: 'consumption_auto_selling',
        component: () =>
          import(/* webpackChunkName: "ConsumptionAutoSelling" */ '@/views/merchant/order/ConsumptionAutoSelling'),
        name: 'MerchantConsumptionAutoSelling',
        meta: {
          noCache: true,
          title: 'consumption_auto_selling',
          permission: ['background_order.vending_machine.order']
        }
      },
      {
        path: 'consumption_auto_selling_detail',
        component: () =>
          import(/* webpackChunkName: "orderDetail" */ '@/views/merchant/order/ConsumptionDetail'),
        name: 'MerchantConsumptionAutoDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'consumption_auto_selling_detail',
          permission: ['background_order.vending_machine.order']
        }
      },
      {
        path: 'refund_order',
        component: () =>
          import(/* webpackChunkName: "refundOrder" */ '@/views/merchant/order/RefundOrder'),
        name: 'MerchantRefundOrder',
        meta: {
          noCache: true,
          title: 'order_refund',
          permission: ['background_order.order_refund.list']
        }
      },
      {
        path: 'recharge_order',
        component: () =>
          import(/* webpackChunkName: "refundOrder" */ '@/views/merchant/order/RechargeOrder'),
        name: 'MerchantRechargeOrder',
        meta: {
          noCache: true,
          title: 'order_recharge',
          permission: ['background_order.order_charge']
        }
      },
      {
        path: 'consumption_failure',
        component: () =>
          import(/* webpackChunkName: "refundOrder" */ '@/views/merchant/order/ConsumptionFailure'),
        name: 'MerchantConsumptionFailure',
        meta: {
          // noCache: true,
          title: 'consumption_failure',
          permission: ['background_order.order_offline.list']
        }
      },
      {
        path: 'withdraw_order',
        component: () =>
          import(/* webpackChunkName: "withdraw_order" */ '@/views/merchant/order/WithdrawOrder'),
        name: 'MerchantWithdrawOrder',
        meta: {
          noCache: true,
          title: 'withdraw_order',
          permission: ['background_order.order_withdraw.list']
        }
      }, // withdraw
      {
        path: 'evaluate_list',
        component: () =>
          import(
            /* webpackChunkName: "evaluate_list" */ '@/views/merchant/order/operations-management/EvaluateList'
          ),
        name: 'MerchantOperationsManagementEvaluateList',
        meta: {
          noCache: true,
          title: 'evaluate_list',
          permission: ['background_operation_management.order_evaluation']
        }
      },
      {
        path: 'consumption_detail',
        component: () =>
          import(/* webpackChunkName: "orderDetail" */ '@/views/merchant/order/ConsumptionDetail'),
        name: 'MerchantConsumptionDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'consumption_detail',
          activeMenu: '/order/consumption',
          permission: ['background_order.order_payment']
        }
      },
      {
        path: 'consumption_failure_detail',
        component: () =>
          import(
            /* webpackChunkName: "consumption_failure_detail" */ '@/views/merchant/order/ConsumptionFailureDetail'
          ),
        name: 'MerchantConsumptionFailureDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'consumption_failure_detail',
          activeMenu: '/order/consumption_failure',
          permission: ['background_order.order_offline.list']
        }
      },
      // {
      //   path: 'setting',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "evaluate_list" */ '@/views/merchant/order/operations-management/setting'
      //     ),
      //   name: 'MerchantOperationsManagementSetting',
      //   meta: {
      //     noCache: true,
      //     title: 'operations_setting',
      //     permission: ['background_operation_management.evaluation_setting.list']
      //   }
      // },
      {
        path: 'abnormal_third_party_reconciliation',
        component: () =>
          import(/* webpackChunkName: "recharge_withdraw_order" */ '@/views/merchant/order/AbnormalThirdPartyReconciliation'),
        name: 'AbnormalThirdPartyReconciliation',
        meta: {
          noCache: true,
          title: 'abnormal_third_party_reconciliation',
          permission: ['background_order.compare_bill_order.compare_bill_record']
        }
      },
      {
        path: 'refund_pending_order',
        component: () =>
          import(/* webpackChunkName: "refund_pending_order" */ '@/views/merchant/order/RefundPendingOrder'),
        name: 'RefundPendingOrder',
        meta: {
          noCache: true,
          title: 'refund_pending_order',
          permission: ['background_order.order_refund.report_meal_pack_order_refund_list']
        }
      }
      // {
      //   path: 'setting',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "evaluate_list" */ '@/views/merchant/order/operations-management/setting'
      //     ),
      //   name: 'MerchantOperationsManagementSetting',
      //   meta: {
      //     noCache: true,
      //     title: 'operations_setting',
      //     permission: ['background_operation_management.evaluation_setting.list']
      //   }
      // }
    ]
  },
  {
    path: '/order_approval',
    component: Layout,
    redirect: '/order_approval/appeal_order',
    alwaysShow: true,
    name: 'MerchantOrderApproval',
    meta: {
      title: 'evaluate_admin',
      permission: ['order_approval']
    },
    children: [
      {
        path: 'appeal_order',
        component: () =>
          import(/* webpackChunkName: "appeal_order" */ '@/views/merchant/order/AppealOrder'),
        name: 'MerchantAppealOrder',
        meta: {
          noCache: true,
          title: 'appeal_order',
          permission: ['background_order.order_appeal.list']
        }
      },
      {
        path: 'appeal_order_detail',
        component: () =>
          import(
            /* webpackChunkName: "appeal_order_detail" */ '@/views/merchant/order/AppealOrderDetail'
          ),
        name: 'MerchantAppealOrderDetail',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/order/appeal_order',
          title: 'appeal_order_detail',
          permission: ['background_order.order_appeal.list']
        }
      },
      {
        path: 'approve_order',
        component: () =>
          import(/* webpackChunkName: "approve_order" */ '@/views/merchant/order/ApproveOrder'),
        name: 'MerchantApproveOrder',
        meta: {
          noCache: true,
          title: 'approve_order',
          permission: ['background_order.order_review.list']
        }
      },
      {
        path: 'approve_order_detail',
        component: () =>
          import(
            /* webpackChunkName: "approve_order_detail" */ '@/views/merchant/order/ApproveOrderDetail'
          ),
        name: 'MerchantApproveOrderDetail',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/order/approve_order',
          title: 'approve_order_detail',
          permission: ['background_order.order_review.list']
        }
      },
      {
        path: 'recharge_refund_order',
        component: () =>
          import(/* webpackChunkName: "recharge_refund_order" */ '@/views/merchant/order/RechargeRefundOrder'),
        name: 'MerchantRechargeRefundOrder',
        meta: {
          noCache: true,
          title: 'recharge_refund_order',
          permission: ['background_order.order_charge.charge_appeal_list']
        }
      },
      {
        path: 'recharge_withdraw_order',
        component: () =>
          import(/* webpackChunkName: "recharge_withdraw_order" */ '@/views/merchant/order/RechargeWithdrawOrder'),
        name: 'MerchantRechargeWithdrawOrder',
        meta: {
          noCache: true,
          title: 'recharge_withdraw_order',
          permission: ['background_order.order_withdraw.approval_list']
        }
      },
      {
        path: 'meal_approve_list',
        component: () =>
          import(
            /* webpackChunkName: "meal_approve_list" */ '@/views/merchant/meal-management/meal-apply/MealApproveList'
          ),
        name: 'MerchantMealApproveList',
        meta: {
          noCache: true,
          title: 'meal_approve_list',
          permission: ['background_approve.approve_order_visitor']
        }
      }
    ]
  },
  // 一级-用餐管理
  {
    path: '/food_admin',
    component: Layout,
    redirect: '/food_admin/recipes_manage',
    alwaysShow: true,
    name: 'MerchantIngredientsLibrary',
    meta: {
      noCache: true,
      title: 'food_admin',
      permission: ['background_food.list']
    },
    children: [
      {
        path: 'recipes_manage',
        component: () =>
          import(
            /* webpackChunkName: "recipes_manage" */ '@/views/merchant/meal-management/menu-admin/RecipesManage'
          ),
        name: 'MerchantRecipesManage',
        meta: {
          noCache: true,
          title: 'recipes_manage',
          permission: ['background_food.menu']
        }
      },
      // {
      //   path: 'ingredients_library',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "ingredients_library" */ '@/views/merchant/meal-management/food-admin/IngredientsLibrary'
      //     ),
      //   name: 'ingredientsLibrary',
      //   meta: {
      //     noCache: true,
      //     title: 'ingredients_library',
      //     permission: ['meal_management']
      //   }
      // },
      {
        path: 'ingredients_category',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "ingredients_category" */ '@/views/merchant/meal-management/food-admin/IngredientsCategory'
          ),
        name: 'MerchantIngredientsCategory',
        meta: {
          noCache: true,
          title: 'ingredients_category',
          permission: ['background_food.ingredient_sort.list']
        }
      },
      {
        path: 'ingredients_detail_:type',
        component: () =>
          import(
            /* webpackChunkName: "add_ingredients_admin" */ '@/views/merchant/meal-management/food-admin/AddIngredients'
          ),
        name: 'MerchantAddIngredients',
        hidden: true,
        meta: {
          noCache: true,
          title: 'ingredients',
          permission: ['background_food.ingredient.add', 'background_food.ingredient.modify']
        }
      },
      {
        path: 'Ingredients_admin',
        component: () =>
          import(
            /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/IngredientsAdmin'
          ),
        name: 'MerchantIngredientsAdmin',
        meta: {
          noCache: true,
          title: 'ingredients_library',
          permission: ['background_food.ingredient.list']
        }
      },
      {
        path: 'import_ingredients/:type',
        component: () =>
          import(
            /* webpackChunkName: "merchant_add_ingredients" */ '@/views/merchant/meal-management/food-admin/ImportIngredients'
          ),
        name: 'MerchantImportIngredients',
        hidden: true,
        meta: {
          noCache: true,
          title: 'import_ingredients',
          permission: ['background_food.ingredient.batch_add']
        }
      },
      {
        path: 'import_ingredient_image',
        component: () =>
          import(
            /* webpackChunkName: "import_ingredient_image" */ '@/views/merchant/meal-management/food-admin/ImportIngredientImage'
          ),
        name: 'MerchantImportIngredientImage',
        hidden: true,
        meta: {
          noCache: true,
          title: 'import_ingredient_image',
          permission: ['background_food.ingredient.ingredient_image_bat_add']
        }
      },
      {
        path: 'copy_ingredients',
        component: () =>
          import(
            /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/CopyIngredients'
          ),
        name: 'MerchantCopyIngredients',
        hidden: true,
        meta: {
          noCache: true,
          title: 'copy_ingredients',
          permission: ['background_food.ingredient.system_ingredient_copy']
        }
      },
      {
        path: 'import_commodity/:type',
        component: () =>
          import(
            /* webpackChunkName: "merchant_import_commodity" */ '@/views/merchant/meal-management/food-admin/ImportCommodity'
          ),
        name: 'MerchantImportCommodity',
        hidden: true,
        meta: {
          noCache: true,
          title: 'import_commodity',
          permission: ['background_food.food.batch_add', 'background_food.food.batch_modify']
        }
      },
      {
        path: 'import_commodity_image',
        component: () =>
          import(
            /* webpackChunkName: "import_commodity_image" */ '@/views/merchant/meal-management/food-admin/ImportCommodityImage'
          ),
        name: 'MerchantImportCommodityImage',
        hidden: true,
        meta: {
          noCache: true,
          title: 'import_commodity_image',
          permission: ['background_food.food.food_extra_image_bat_add']
        }
      },
      {
        path: 'meal_food_classification',
        component: () =>
          import(
            /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/MealFoodClassificationNew'
          ),
        name: 'MerchantMealFoodClassificationNew',
        meta: {
          noCache: true,
          title: 'meal_food_classification',
          permission: ['background_food.food_sort.list']
        }
      },
      {
        path: 'meal_food_list',
        component: () =>
          import(
            /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/MealFoodList'
          ),
        name: 'MerchantMealFoodList',
        meta: {
          noCache: true,
          title: 'meal_food_list',
          permission: ['background_food.food.list']
        }
      },
      {
        path: 'copy_foods',
        component: () =>
          import(
            /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/CopyFoods'
          ),
        name: 'MerchantCopyFoods',
        hidden: true,
        meta: {
          noCache: true,
          title: 'copy_foods',
          permission: ['background_food.list']
        }
      },
      // 暂时隐藏，不要删，这功能产品说等下次优化
      // {
      //   path: 'collective_admin',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/collective-admin/CollectiveAdmin'
      //     ),
      //   name: 'CollectiveAdmin',
      //   meta: {
      //     noCache: true,
      //     title: 'collective_admin',
      //     permission: ['background_food.collective.list']
      //   }
      // },
      {
        path: 'set_meal_admin',
        component: () =>
          import(
            /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/set-meal-admin/SetMealAdmin'
          ),
        name: 'MerchantSetMealAdmin',
        meta: {
          noCache: true,
          title: 'set_meal_admin',
          permission: ['background_food.set_meal.list']
        }
      },
      {
        path: 'add_edit_set_meal',
        component: () =>
          import(
            /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/set-meal-admin/AddEditSetMeal'
          ),
        name: 'MerchantAddEditSetMeal',
        hidden: true,
        meta: {
          noCache: true,
          title: 'add_edit_set_meal',
          permission: ['background_food.set_meal.add', 'background_food.set_meal.modify']
        }
      },
      {
        path: 'set_meal_classify',
        component: () =>
          import(
            /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/set-meal-admin/SetMealClassify'
          ),
        name: 'MerchantSetMealClassify',
        hidden: true,
        meta: {
          noCache: true,
          title: 'set_meal_classify',
          permission: ['background_food.set_meal.list']
        }
      },
      // 集体管理
      {
        path: 'collective_admin',
        component: () =>
          import(
            /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/collective-admin/CollectiveAdmin'
          ),
        name: 'MerchantCollectiveAdmin',
        meta: {
          noCache: true,
          title: 'collective_admin',
          permission: ['background_food.collective.list']
        }
      },
      // {
      //   path: 'diet_nutrition',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/DietNutrition'
      //     ),
      //   name: 'MerchantdietNutrition',
      //   meta: {
      //     noCache: true,
      //     title: 'diet_nutrition',
      //     permission: ['background_food.diet_group.list']
      //   }
      // },
      {
        path: 'diet_crowd',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "ingredients_category" */ '@/views/merchant/meal-management/food-admin/DietCrowd'
          ),
        name: 'MerchantDietCrowd',
        meta: {
          noCache: true,
          activeMenu: '/food_admin/diet_nutrition',
          title: 'diet_crowd'
        }
      },
      {
        path: 'add_week_recipes_separate',
        name: 'MerchantAddWeekRecipesSeparate',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_week_recipes" */ '@/views/merchant/meal-management/menu-admin/AddWeekRecipesSeparate'
          ),
        meta: {
          noCache: true,
          title: 'add_week_recipes',
          permission: ['background_food.menu_weekly.list', 'background_food.menu_weekly.menu_food_modify']
        }
      },
      {
        path: 'add_month_recipes_separate',
        name: 'MerchantAddMonthRecipesSeparate',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_month_recipes" */ '@/views/merchant/meal-management/menu-admin/AddMonthRecipesSeparate'
          ),
        meta: {
          noCache: true,
          title: 'add_month_recipes',
          permission: ['background_food.menu_monthly.menu_food_modify']
        }
      },
      {
        path: 'add_meal_month_recipes',
        name: 'MerchantAddMealMonthRecipes',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_meal_month_recipes" */ '@/views/merchant/meal-management/menu-admin/AddMealMonthRecipes'
          ),
        meta: {
          noCache: true,
          title: 'add_month_recipes',
          permission: ['background_food.menu_monthly.menu_food_modify']
        }
      },
      // 营养分析 单独一个页面
      {
        path: 'nutrition_analysis',
        name: 'MerchantNutritionAnalysis',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_meal_month_recipes" */ '@/views/merchant/meal-management/menu-admin/nutritionAnalysis'
          ),
        meta: {
          noCache: true,
          title: 'nutrition_analysis',
          permission: ['background_food.list']
        }
      },
      {
        path: 'menu_catering',
        name: 'MerchantMenuCatering',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_month_recipes" */ '@/views/merchant/meal-management/menu-admin/MenuCatering'
          ),
        meta: {
          noCache: true,
          title: 'menu_catering',
          permission: ['background_food.list']
        }
      },
      {
        path: 'intention_menu_rank',
        component: () =>
          import(
            /* webpackChunkName: "intention_menu_rank" */ '@/views/merchant/meal-management/intention-menu-rank/IntentionMenuRank'
          ),
        name: 'MerchantIntentionMenu',
        meta: {
          noCache: true,
          title: 'intention_menu_rank',
          permission: ['background_food.intent_food.list']
        }
      },
      {
        path: 'ai_retention_instrument',
        component: () =>
          import(
            /* webpackChunkName: "ai_retention_instrument" */ '@/views/merchant/meal-management/food-admin/AiRetentionInstrument'
          ),
        name: 'MerchantAiRetentionInstrument',
        meta: {
          noCache: true,
          title: 'ai_retention_instrument',
          permission: ['background_food.food_reserved.list']
        }
      },
      {
        path: 'ai_retention_instrument_detail',
        component: () =>
          import(
            /* webpackChunkName: "ai_retention_instrument_detail" */ '@/views/merchant/meal-management/food-admin/AiRetentionInstrumentDetail'
          ),
        name: 'MerchantAiRetentionInstrumentDetail',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/food_admin/ai_retention_instrument',
          title: 'ai_retention_instrument_detail',
          permission: ['background_food.food_reserved.list']
        }
      },
      {
        path: 'menu_rule_setting',
        component: () =>
          import(
            /* webpackChunkName: "intention_menu_rank" */ '@/views/merchant/meal-management/menu-rule-setting/MenuRuleSetting'
          ),
        name: 'MenuRuleSetting',
        meta: {
          noCache: true,
          title: 'menu_rule_setting',
          permission: ['background_food.menu_setting_info.list']
        }
      },
      {
        path: 'import_recipes_separate',
        name: 'MerchantImportRecipesSeparate',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "ImportRecipesSeparate" */ '@/views/merchant/meal-management/menu-admin/ImportRecipesSeparate'
          ),
        meta: {
          noCache: true,
          title: 'import_recipes_separate',
          permission: ['background_food.menu', 'background_food.menu_weekly.list', 'background_food.menu_monthly.list']
        }
      },
      {
        path: 'recipe_template_admin',
        name: 'MerchantRecipeTemplateAdmin',
        component: () =>
          import(
            /* webpackChunkName: "recipe-template-admin" */ '@/views/merchant/meal-management/recipe-template/RecipeTemplateAdmin'
          ),
        meta: {
          noCache: true,
          title: 'recipe_template_admin',
          permission: ['background_food.menu_template.list']
        }
      },
      {
        path: 'dish_ratings_list',
        name: 'MerchantDishRatingsList',
        component: () =>
          import(
            /* webpackChunkName: "dish-ratings-list" */ '@/views/merchant/meal-management/dish-ratings/DishRatingsList'
          ),
        meta: {
          noCache: true,
          title: 'dish_ratings_list',
          permission: ['background_food.food.food_evaluation_list']
        }
      },
      {
        path: 'ration_recipe',
        name: 'MerchantRationRecipe',
        component: () =>
          import(
            /* webpackChunkName: "recipe-template-admin" */ '@/views/merchant/meal-management/ration-recipe/RationRecipe'
          ),
        meta: {
          noCache: true,
          title: 'ration_recipe',
          permission: ['background_fund_supervision.ration_recipe.list']
        }
      }
    ]
  },
  {
    path: '/booking',
    component: Layout,
    redirect: '/booking/booking_meal',
    alwaysShow: true,
    name: 'MerchantBooking',
    meta: {
      title: 'booking',
      permission: ['reservation_settings']
    },
    children: [
      {
        path: 'booking_meal',
        component: () =>
          import(
            /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/booking-setting/Order'
          ),
        name: 'MerchantBookingMeal',
        meta: {
          noCache: true,
          title: 'booking_meal',
          permission: ['background_reservation.background_reservation_settings.list']
        }
      },
      {
        path: 'add_booking_order',
        component: () =>
          import(
            /* webpackChunkName: "add_booking_order" */ '@/views/merchant/meal-management/booking-setting/AddBookingOrder'
          ),
        name: 'MerchantAddBookingOrder',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/booking/booking_meal',
          title: 'add_booking_order',
          permission: ['background_reservation.background_reservation_settings.add', 'background_reservation.background_reservation_settings.modify']
        }
      },
      {
        path: 'booking_order_list',
        component: () =>
          import(
            /* webpackChunkName: "booking_order_list" */ '@/views/merchant/meal-management/booking-setting/OrderList'
          ),
        name: 'MerchantBookingOrderList',
        meta: {
          noCache: true,
          title: 'booking_order_list',
          permission: ['background_order.reservation_order.info_list']
        }
      },
      {
        path: 'canteen_booking',
        component: () =>
          import(
            /* webpackChunkName: "canteen_booking" */ '@/views/merchant/meal-management/booking-setting/CanteenBooking'
          ),
        name: 'MerchantCanteenBooking',
        meta: {
          noCache: true,
          title: 'canteen_booking',
          permission: ['background_order.reservation_order.collect_list']
        }
      },
      {
        path: 'group_order',
        component: () =>
          import(
            /* webpackChunkName: "group_order" */ '@/views/merchant/meal-management/booking-setting/GroupOrder'
          ),
        name: 'MerchantGroupOrder',
        meta: {
          noCache: true,
          title: 'group_order',
          permission: ['background_order.reservation_order.group_collect_list']
        }
      },
      {
        path: 'food_category',
        component: () =>
          import(
            /* webpackChunkName: "food_category" */ '@/views/merchant/meal-management/booking-setting/CategoryList'
          ),
        name: 'MerchantCategoryList',
        meta: {
          noCache: true,
          title: 'food_category',
          permission: ['background_order.reservation_order.food_collect_list']
        }
      },
      {
        path: 'set_meal_summary',
        component: () =>
          import(
            /* webpackChunkName: "set_meal_summary" */ '@/views/merchant/meal-management/booking-setting/SetMealSummary'
          ),
        name: 'MerchantSetMealSummary',
        meta: {
          noCache: true,
          title: 'set_meal_summary',
          permission: ['background_order.reservation_order.set_meal_collect_list']
        }
      },
      {
        path: 'cupboard_order',
        component: () =>
          import(
            /* webpackChunkName: "set_meal_summary" */ '@/views/merchant/meal-management/booking-setting/CupboardOrder'
          ),
        name: 'MerchantCupboardOrder',
        meta: {
          noCache: true,
          title: 'cupboard_order',
          permission: ['background_order.reservation_order.cupboard_order_list']
        }
      }
    ]
  },
  {
    path: '/address',
    component: Layout,
    redirect: '/address/deliver_report',
    name: 'MerchantAddress',
    alwaysShow: true,
    meta: {
      title: 'address',
      noCache: true,
      permission: ['address_management']
    },
    children: [
      {
        path: 'deliver_report',
        component: () =>
          import(
            /* webpackChunkName: "deliver_report" */ '@/views/merchant/meal-management/address-admin/deliver/DeliverReport.vue'
          ),
        name: 'MerchantDeliverReport',
        meta: {
          noCache: true,
          title: 'deliver_report',
          permission: ['background_order.reservation_order.delivery_list']
        }
      },
      {
        path: 'address_area_admin',
        component: () =>
          import(
            /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/address-admin/AddressAreaAdmin'
          ),
        name: 'MerchantAddressAreaAdmin',
        meta: {
          noCache: true,
          title: 'address_area_admin',
          permission: ['address.adders_area.list']
        }
      },
      {
        path: 'address_admin',
        component: () =>
          import(
            /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/address-admin/AddressAdmin'
          ),
        name: 'MerchantAddressAdmin',
        meta: {
          noCache: true,
          title: 'address_admin',
          permission: ['address.adders_center.list']
        }
      },
      {
        path: 'qrcode_template',
        component: () =>
          import(/* webpackChunkName: "qrcode_template" */ '@/views/merchant/system/QrcodeTemplate'),
        name: 'MerchantQrcodeTemplate',
        meta: {
          noCache: true,
          title: 'qrcode_template',
          permission: ['background_organization.qrcode_template']
        }
      },
      {
        path: 'download_center',
        component: () =>
          import(/* webpackChunkName: "download_center" */ '@/views/merchant/system/download/DownloadCenter'),
        name: 'MerchantDownloadCenter',
        meta: {
          noCache: true,
          title: 'download_center',
          permission: ['merchant_system']
        }
      },
      {
        path: 'download_center_detail',
        component: () =>
          import(/* webpackChunkName: "download_center_detail" */ '@/views/merchant/system/download/DownloadCenterDetail'),
        name: 'MerchantDownloadCenterDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'download_center_detail',
          activeMenu: '/organization/download_center',
          permission: ['merchant_system']
        }
      },
      {
        path: 'kitchen_info',
        component: () =>
          import(/* webpackChunkName: "kitchen_info" */ '@/views/merchant/consumption-rules/operations-management/KitchenInfo'),
        name: 'MerchantKitchenInfo',
        meta: {
          noCache: true,
          title: 'kitchen_info',
          permission: ['background_operation_management.kitchen_info.list']
        }
      }
    ]
  },
  {
    path: '/meal_report',
    component: Layout,
    redirect: '/meal_report/meal_report_admin',
    alwaysShow: true,
    name: 'MerchantMealReport',
    meta: {
      noCache: true,
      title: 'meal_report',
      permission: ['report_meal_settings']
    },
    children: [
      {
        path: 'meal_report_admin',
        name: 'MerchantMealReportAdmin',
        component: () =>
          import(
            /* webpackChunkName: "meal_report" */ '@/views/merchant/meal-management/meal-report/MealReportAdmin'
          ),
        meta: {
          noCache: true,
          title: 'meal_report_admin',
          permission: ['background_report_meal.report_meal_settings.list']
        }
      },
      // {
      //   hidden: true,
      //   path: 'meal_report_/:type',
      //   name: 'MerchantAddMealReport',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "add_meal_report" */ '@/views/merchant/meal-management/meal-report/AddMealReport'
      //     ),
      //   meta: {
      //     noCache: true,
      //     title: 'add_meal_report',
      //     permission: ['background_report_meal.report_meal_settings.add', 'background_report_meal.report_meal_settings.modify']
      //   }
      // },
      {
        path: 'head_count',
        component: () =>
          import(
            /* webpackChunkName: "meal_report" */ '@/views/merchant/meal-management/meal-report/HeadCount'
          ),
        name: 'MerchantHeadCount',
        meta: {
          noCache: true,
          title: 'head_count',
          permission: ['background_order.order_report_meal.not_report_list']
        }
      },
      {
        path: 'meal_report_detail',
        component: () =>
          import(
            /* webpackChunkName: "meal_details" */ '@/views/merchant/meal-management/meal-report/MealReportDetail'
          ),
        name: 'MerchantMealReportDetail',
        meta: {
          noCache: true,
          title: 'meal_report_detail',
          permission: ['background_order.order_report_meal.info_list']
        }
      },
      {
        path: 'meal_report_summary',
        component: () =>
          import(
            /* webpackChunkName: "meal_details" */ '@/views/merchant/meal-management/meal-report/MealReportSummary'
          ),
        name: 'MerchantMealReportSummary',
        meta: {
          noCache: true,
          title: 'meal_report_summary',
          permission: ['background_order.order_report_meal.department_group_summary_list', 'background_order.order_report_meal.group_summary_list']
        }
      },
      {
        path: 'meal_package_rule',
        component: () =>
          import(
            /* webpackChunkName: "meal_package_rule" */ '@/views/merchant/meal-management/meal-report/MealPackageRule'
          ),
        name: 'MerchantMealPackageRule',
        meta: {
          noCache: true,
          title: 'meal_package_rule',
          permission: ['background_report_meal.report_meal_pack_settings.list']
        }
      },
      {
        path: 'meal_package_detail',
        component: () =>
          import(
            /* webpackChunkName: "meal_package_rule" */ '@/views/merchant/meal-management/meal-report/MealPackageDetail'
          ),
        name: 'MerchantMealPackageDetail',
        meta: {
          noCache: true,
          title: 'meal_package_detail',
          permission: ['background_order.order_report_meal.order_report_meal_pack_list']
        }
      },
      {
        path: 'dep_meal_report',
        name: 'MerchantDepMealReport',
        component: () =>
          import(
            /* webpackChunkName: "dep_meal_report" */ '@/views/merchant/meal-management/meal-report/DepMealReport'
          ),
        meta: {
          noCache: true,
          title: 'dep_meal_report',
          permission: ['background_order.order_report_meal.group_collect_list']
        }
      },
      {
        path: 'canteen_meal_report',
        name: 'MerchantCanteenMealReport',
        component: () =>
          import(
            /* webpackChunkName: "canteen_meal_report" */ '@/views/merchant/meal-management/meal-report/CanteenMealReport'
          ),
        meta: {
          noCache: true,
          title: 'canteen_meal_report',
          permission: ['background_order.order_report_meal.collect_list']
        }
      }
    ]
  },
  {
    path: '/label_admin',
    component: Layout,
    redirect: '/label_admin/food_label',
    name: 'MerchantLabelAdmin',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'label_admin',
      permission: ['label_management']
    },
    children: [
      {
        path: 'food_label',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/merchant/meal-management/label-admin/FoodLabel'
          ),
        name: 'MerchantFoodLabel',
        meta: {
          noCache: true,
          title: 'food_label',
          permission: ['background_healthy.label_group.list']
        }
      },
      {
        path: 'ingredients_label',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/merchant/meal-management/label-admin/IngredientsLabel'
          ),
        name: 'MerchantIngredientsLabel',
        meta: {
          noCache: true,
          title: 'ingredients_label',
          permission: ['background_healthy.label_group.ingredient_list']
        }
      }
    ]
  },
  {
    path: '/supplier',
    component: Layout,
    redirect: '/supplier/supplier_admin',
    name: 'MerchantSupplier',
    alwaysShow: true,
    meta: {
      title: 'supplier_admin',
      noCache: true,
      permission: ['background_food.ingredient_supplier.list']
    },
    children: [
      {
        path: 'supplier_admin',
        component: () =>
          import(
            /* webpackChunkName: "meal_report" */ '@/views/merchant/meal-management/supplier-admin/SupplierAdmin'
          ),
        name: 'MerchantSupplierAdmin',
        meta: {
          noCache: true,
          title: 'supplier_admin',
          permission: ['background_food.ingredient_supplier.list']
        }
      },
      {
        path: 'add_edit_supplier',
        component: () =>
          import(
            /* webpackChunkName: "meal_report" */ '@/views/merchant/meal-management/supplier-admin/AddEditSupplier'
          ),
        name: 'MerchantAddEditSupplier',
        hidden: true,
        meta: {
          noCache: true,
          title: 'add_edit_supplier',
          permission: ['background_food.ingredient_supplier.add', 'background_food.ingredient_supplier.modify']
        }
      },

      {
        path: 'relation_supplier_ingredient',
        component: () =>
          import(
            /* webpackChunkName: "meal_report" */ '@/views/merchant/meal-management/supplier-admin/RelationSupplierIngredient'
          ),
        name: 'MerchantRelationSupplierIngredient',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/supplier/supplier_admin',
          title: 'relation_supplier_ingredient',
          permission: ['background_food.ingredient_supplier.add_supplier_ingredient']
        }
      }
    ]
  },
  {
    path: '/store_admin',
    component: Layout,
    redirect: '/reservation_management/store_goods_admin',
    name: 'MerchantStoreAdmin',
    alwaysShow: true,
    meta: {
      title: 'store_admin',
      noCache: true,
      permission: ['background_store']
    },
    children: [
      {
        path: 'store_goods_admin',
        component: () =>
          import(
            /* webpackChunkName: "store_goods_admin" */ '@/views/merchant/meal-management/store-admin/admin/index'
          ),
        name: 'MerchantStoreGoodsAdmin',
        meta: {
          noCache: true,
          title: 'store_goods_admin',
          permission: ['background_store.goods']
        }
      },
      {
        path: 'stock_goods_stock',
        component: () =>
          import(
            /* webpackChunkName: "stock_goods_stock" */ '@/views/merchant/meal-management/store-admin/stock/index'
          ),
        name: 'MerchantStockGoodsStock',
        meta: {
          noCache: true,
          title: 'stock_goods_stock',
          permission: ['background_store.goods_stock']
        }
      },
      {
        path: 'stock_goods_sales',
        component: () =>
          import(
            /* webpackChunkName: "stock_goods_sales" */ '@/views/merchant/meal-management/store-admin/sales/index'
          ),
        name: 'MerchantStockGoodsSales',
        meta: {
          noCache: true,
          title: 'stock_goods_sales',
          permission: ['background_store.goods.sales']
        }
      },
      {
        path: 'goods_stock_details',
        component: () =>
          import(
            /* webpackChunkName: "stock_goods_sales" */ '@/views/merchant/meal-management/store-admin/goodsStockDetails/index'
          ),
        name: 'MerchantGoodsStockDetails',
        meta: {
          noCache: true,
          title: 'goods_stock_details',
          permission: ['background_store.goods.goods_stock']
        }
      },
      {
        path: 'goods_stock_summary',
        component: () =>
          import(
            /* webpackChunkName: "goods_stock_summary" */ '@/views/merchant/meal-management/store-admin/goodsStockSummary/index'
          ),
        name: 'MerchantGoodsStockSummary',
        meta: {
          noCache: true,
          title: 'goods_stock_summary',
          // no_permission: true,
          permission: ['background_store.goods.goods_stock_summary']
        }
      }
    ]
  },
  // {
  //   path: '/reservation_management',
  //   component: Layout,
  //   redirect: '/reservation_management/reservation_report',
  //   name: 'ReservationManagement',
  //   alwaysShow: true,
  //   meta: {
  //     title: 'reservation_management',
  //     noCache: true,
  //     permission: ['meal_order_management']
  //   },
  //   children: [
  //     {
  //       path: 'reservation_report',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "reservation_report" */ '@/views/merchant/meal-management/reservation-management/ReservationReport'
  //         ),
  //       name: 'MerchantReservationReport',
  //       meta: {
  //         noCache: true,
  //         title: 'reservation_report',
  //         permission: ['background_order.ordering_food.list']
  //       }
  //     },
  //     {
  //       path: 'department_report_collect',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "department_report_collect" */ '@/views/merchant/meal-management/reservation-management/DepartmentReportCollect'
  //         ),
  //       name: 'MerchantDepartmentReportCollect',
  //       meta: {
  //         noCache: true,
  //         title: 'department_report_collect',
  //         permission: ['background_order.ordering_food.ordering_food_summary']
  //       }
  //     },
  //     {
  //       path: 'user_recharge_refund_summary',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "department_report_collect" */ '@/views/merchant/meal-management/reservation-management/UserRechargeRefundSummary'
  //         ),
  //       name: 'MerchantUserRechargeRefundSummary',
  //       meta: {
  //         noCache: true,
  //         title: 'user_recharge_refund_summary',
  //         permission: ['background_order.ordering_food.ordering_charge_list']
  //       }
  //     }
  //   ]
  // },
  {
    path: '/meal_apply',
    component: Layout,
    redirect: 'meal_management/meal_apply/meal_apply_admin',
    name: 'MerchantMealApply',
    alwaysShow: true,
    meta: {
      title: 'meal_apply',
      noCache: true,
      permission: ['background_approve']
    },
    children: [
      {
        path: 'meal_apply_admin',
        component: () =>
          import(
            /* webpackChunkName: "meal_apply_admin" */ '@/views/merchant/meal-management/meal-apply/MealApplyAdmin'
          ),
        name: 'MerchantMealApplyAdmin',
        meta: {
          noCache: true,
          title: 'meal_apply_admin',
          permission: ['background_approve.approve_order_rule.list']
        }
      },
      {
        path: 'meal_apply/:type',
        name: 'MerchantAddMealApply',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_meal_apply" */ '@/views/merchant/meal-management/meal-apply/AddMealApply'
          ),
        meta: {
          noCache: true,
          title: 'meal_apply',
          permission: ['background_approve.approve_order_rule.add', 'background_approve.approve_order_rule.modify']
        }
      },
      {
        path: 'meal_apply_order',
        component: () =>
          import(
            /* webpackChunkName: "meal_apply_order" */ '@/views/merchant/meal-management/meal-apply/MealApplyOrder'
          ),
        name: 'MerchantMealApplyOrder',
        meta: {
          noCache: true,
          title: 'meal_apply_order',
          permission: ['background_approve.order_approve_visitor.list']
        }
      },
      {
        path: 'meal_order_detail',
        component: () =>
          import(
            /* webpackChunkName: "meal_order_detail" */ '@/views/merchant/meal-management/meal-apply/MealOrderDetail'
          ),
        name: 'MerchantMealOrderDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'meal_order_detail',
          permission: ['background_approve.order_approve_visitor.jz_list', 'background_approve.order_approve_visitor.jf_list']
        }
      },
      {
        path: 'meal_apply_total',
        component: () =>
          import(
            /* webpackChunkName: "meal_apply_total" */ '@/views/merchant/meal-management/meal-apply/MealApplyTotal'
          ),
        name: 'MerchantMealApplyTotal',
        meta: {
          noCache: true,
          title: 'meal_apply_total',
          permission: ['background_approve.order_approve_visitor.collect_list']
        }
      },
      {
        path: 'meal_apply_prepare',
        component: () =>
          import(
            /* webpackChunkName: "meal_apply_prepare" */ '@/views/merchant/meal-management/meal-apply/MealApplyPrepare'
          ),
        name: 'MerchantMealApplyPrepare',
        meta: {
          noCache: true,
          title: 'meal_apply_prepare',
          permission: ['background_approve.order_approve_visitor.preparation_list']
        }
      }
    ]
  },
  // 一级-设备管理
  {
    path: '/device-management',
    component: Layout,
    redirect: '/device-management/device_list',
    alwaysShow: true,
    name: 'MerchantDeviceManagement',
    meta: {
      noCache: true,
      title: 'device_management',
      permission: ['device_management']
    },
    children: [
      {
        path: 'device_list',
        component: () =>
          import(
            /* webpackChunkName: "device_list" */ '@/views/merchant/device-management/DeviceList'
          ),
        name: 'MerchantDeviceList',
        meta: {
          noCache: true,
          title: 'device_list',
          permission: ['background_device.device_info.list']
        }
      },
      {
        path: 'abnormal_video_list',
        component: () =>
          import(
            /* webpackChunkName: "device_list" */ '@/views/merchant/device-management/AbnormalVideoList'
          ),
        name: 'MerchantAbnormalVideoList',
        hidden: true,
        meta: {
          noCache: true,
          title: 'abnormal_video_list',
          permission: ['background_device.abnormal_video.list']
        }
      },
      {
        path: 'cupboard_edit',
        component: () =>
          import(
            /* webpackChunkName: "cupboard_edit" */ '@/views/merchant/device-management/CupboardEdit'
          ),
        name: 'MerchantCupboardEdit',
        hidden: true,
        meta: {
          noCache: true,
          title: 'cupboard_edit',
          permission: ['background_device.device.device_info']
        }
      },
      {
        path: 'weight_food',
        component: () =>
          import(
            /* webpackChunkName: "weight_food" */ '@/views/merchant/device-management/WeightFood'
          ),
        name: 'MerchantWeightFood',
        hidden: true,
        meta: {
          noCache: true,
          title: 'weight_food',
          permission: ['background_device.device.device_food_bind.list']
        }
      },
      {
        path: 'device_admin_card',
        component: () =>
          import(
            /* webpackChunkName: "device_admin_card" */ '@/views/merchant/device-management/deviceAdminCard'
          ),
        name: 'MerchantDeviceAdminCard',
        meta: {
          noCache: true,
          title: 'device_admin_card',
          permission: ['background_organization.manage_card']
        }
      },
      {
        path: 'face_synchronization_history',
        component: () =>
          import(
            /* webpackChunkName: "device_list" */ '@/views/merchant/device-management/FaceSynchronizationHistory'
          ),
        name: 'MerchantFaceSynchronizationHistory',
        meta: {
          noCache: true,
          title: 'face_synchronization_history',
          permission: ['background_device.face_record.list']
        }
      },
      // {
      //   path: 'printSetting',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/device-management/PrintSetting'
      //     ),
      //   name: 'MerchantPrintSetting',
      //   meta: {
      //     noCache: true,
      //     title: 'print_setting',
      //     permission: ['background_device.device_print_conf.list']
      //   }
      // },
      {
        path: 'printDevice_:type',
        component: () =>
          import(
            /* webpackChunkName: "print_device" */ '@/views/merchant/device-management/AddPrintDevice'
          ),
        name: 'MerchantAddPrintDevice',
        hidden: true,
        meta: {
          noCache: true,
          title: 'print_device',
          activeMenu: '/device-management/printSetting',
          permission: ['device_management']
        }
      },
      {
        path: 'third_party_equipment_admin',
        component: () =>
          import(
            /* webpackChunkName: "third_party_equipment_admin" */ '@/views/merchant/device-management/ThirdPartyEquipmentAdmin'
          ),
        name: 'MerchantThirdPartyEquipmentAdmin',
        meta: {
          noCache: true,
          title: 'third_party_equipment_admin',
          permission: ['background_device.third_device.list']
        }
      }
    ]
  },
  {
    path: '/device-management/device_cloud_print_admin',
    component: Layout,
    name: 'MerchantDeviceCloudPrintAdmin',
    redirect: '/device-management/device_cloud_print_admin/print_admin',
    meta: {
      noCache: true,
      title: 'device_cloud_print_admin',
      permission: ['background_printer.printer']
    },
    children: [
      {
        path: 'print_admin',
        component: () =>
          import(
            /* webpackChunkName: "device_cloud_print" */ '@/views/merchant/device-management/device-cloud-print-admin/PrintAdmin'
          ),
        name: 'MerchantPrintAdmin',
        meta: {
          noCache: true,
          title: 'print_admin',
          permission: ['background_printer_management']
        }
      },
      {
        path: 'printer_settings',
        component: () =>
          import(
          /* webpackChunkName: "printer_settings" */ '@/views/merchant/device-management/device-cloud-print-admin/PrinterSettings'
          ),
        name: 'MerchantPrinterSettings',
        meta: {
          noCache: true,
          title: 'printer_settings',
          permission: ['background_printer.printer.add_printer_settings']
        }
      }
    ]
  },
  {
    path: '/device-management/weightSetting',
    component: Layout,
    redirect: '/device-management/weightSetting/routineSetting',
    name: 'WeightSetting',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'weight_setting',
      permission: ['background_device.device_buffet.details']
    },
    children: [
      {
        path: 'routineSetting',
        component: () =>
          import(
            /* webpackChunkName: "routine_setting" */ '@/views/merchant/device-management/RoutineSetting'
          ),
        name: 'RoutineSetting',
        meta: {
          noCache: true,
          title: 'routine_setting',
          permission: ['background_device.device_buffet']
        }
      },
      {
        path: 'trayQRcode',
        component: () =>
          import(
            /* webpackChunkName: "tray_qrcode" */ '@/views/merchant/device-management/TrayQRcode'
          ),
        name: 'TrayQRcode',
        meta: {
          noCache: true,
          title: 'tray_qrcode',
          permission: ['background_device.tray.tray_list']
        }
      }
    ]
  },
  {
    path: '/device-management/cupboard_setting',
    component: Layout,
    name: 'MerchantCupboardSetting',
    alwaysShow: false,
    redirect: '/device-management/cupboard_setting/cupboard_setting',
    meta: {
      noCache: true,
      title: 'cupboard_setting',
      permission: ['background_device.cupboard_conf.details']
    },
    children: [
      {
        path: 'cupboard_setting',
        component: () =>
          import(
            /* webpackChunkName: "cupboard_setting" */ '@/views/merchant/device-management/CupboardSetting'
          ),
        name: 'MerchantCupboardSetting',
        meta: {
          noCache: true,
          title: 'cupboard_setting',
          permission: ['background_device.cupboard_conf.details']
        }
      }
    ]
  },
  {
    path: '/device-management/d2_consumer_machine_seting',
    component: Layout,
    alwaysShow: false,
    name: 'MerchantD2ConsumerMachineSeting',
    redirect: '/device-management/d2_consumer_machine_seting/d2_consumer_machine_seting',
    meta: {
      noCache: true,
      title: 'd2_consumer_machine_seting',
      permission: ['background_device.d2_consume.details']
    },
    children: [
      {
        path: 'd2_consumer_machine_seting',
        component: () =>
          import(
            /* webpackChunkName: "d2_consumer_machine_seting" */ '@/views/merchant/device-management/D2ConsumerMachineSeting'
          ),
        name: 'MerchantD2ConsumerMachineSeting',
        meta: {
          noCache: true,
          title: 'd2_consumer_machine_seting',
          permission: ['background_device.d2_consume.details']
        }
      }
    ]
  },
  {
    path: '/device-management/consume_machine_setting',
    component: Layout,
    alwaysShow: false,
    name: 'MerchantConsumeMachineSetting',
    redirect: '/device-management/consume_machine_setting/consume_machine_setting',
    meta: {
      noCache: true,
      title: 'consume_machine_setting',
      permission: ['background_device.consume_info.details']
    },
    children: [
      {
        path: 'consume_machine_setting',
        component: () =>
          import(
            /* webpackChunkName: "consume_machine_setting" */ '@/views/merchant/device-management/ConsumeMachineSetting'
          ),
        name: 'MerchantConsumeMachineSetting',
        meta: {
          noCache: true,
          title: 'consume_machine_setting',
          permission: ['background_device.consume_info.details']
        }
      }
    ]
  },
  {
    path: '/device-management/ai_settlement_setting',
    component: Layout,
    alwaysShow: false,
    name: 'MerchantAiSettlementSetting',
    redirect: '/device-management/ai_settlement_setting/ai_settlement_setting',
    meta: {
      noCache: true,
      title: 'ai_settlement_setting',
      permission: ['background_device.settle_info.details']
    },
    children: [
      {
        path: 'ai_settlement_setting',
        component: () =>
          import(
            /* webpackChunkName: "ai_settlement_setting" */ '@/views/merchant/device-management/AiSettlementSetting'
          ),
        name: 'MerchantAiSettlementSetting',
        meta: {
          noCache: true,
          title: 'ai_settlement_setting',
          permission: ['background_device.settle_info.details']
        }
      }
    ]
  },
  // 一级-营销活动
  {
    path: '/consumption-rules',
    component: Layout,
    redirect: '/consumption-rules/list',
    alwaysShow: true,
    name: 'MerchantConsumptionRules',
    meta: {
      noCache: true,
      title: 'consumption_rules',
      permission: ['background_marketing.consume']
    },
    children: [
      {
        path: 'list',
        name: 'MerchantConsumptionRulesList',
        component: () =>
          import(
            /* webpackChunkName: "consumption_rules_list" */ '@/views/merchant/consumption-rules/ConsumptionRules'
          ),
        meta: {
          noCache: true,
          title: 'consumption_rules_list',
          permission: ['background_marketing.consume.list']
        }
      },
      {
        path: 'form/:type',
        name: 'MerchantConsumptionRulesForm',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "consumption_rules_form" */ '@/views/merchant/consumption-rules/AddConsumptionRules'
          ),
        meta: {
          noCache: true,
          title: 'consumption_rules_form',
          permission: ['background_marketing.consume.add', 'background_marketing.consume.modify']
        }
      },
      {
        path: 'discountLimit',
        name: 'MerchantDiscountLimit',
        component: () =>
          import(
            /* webpackChunkName: "consumption_rules_list" */ '@/views/merchant/consumption-rules/discount-limit/index'
          ),
        meta: {
          noCache: true,
          title: 'discount_limit',
          permission: ['background_marketing.discount_limit']
        }
      },
      // 消费规则操作记录表
      {
        path: 'operation_record_list',
        name: 'ConsumptionRuleOperationRecordList',
        component: () =>
          import(
            /* webpackChunkName: "operation_record_list" */ '@/views/merchant/consumption-rules/ConsumptionRuleOperationRecordList'
          ),
        meta: {
          noCache: true,
          title: 'consumption_rule_operation_record_list',
          permission: ['background_marketing.consume.get_consume_rule_record']
        }
      }
    ]
  },
  // {
  //   path: '/consumption-rules/discount-limit',
  //   component: Layout,
  //   redirect: '/consumption-rules/discount-limit',
  //   alwaysShow: false,
  //   name: 'merchant_system',
  //   meta: {
  //     title: 'discount_limit',
  //     // icon: 'rule',
  //     permission: ['background_marketing.discount_limit']
  //   },
  //   children: [
  //     {
  //       path: 'discountLimit',
  //       name: 'discountLimit',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "consumption_rules_list" */ '@/views/merchant/consumption-rules/discount-limit/index'
  //         ),
  //       meta: {
  //         noCache: true,
  //         title: 'discount_limit',
  //         permission: ['background_marketing.discount_limit']
  //       }
  //     }
  //   ]
  // },
  {
    path: '/consumption-rules/activity',
    component: Layout,
    redirect: '/consumption-rules/activity/recharges',
    alwaysShow: true,
    name: 'MerchantMerchantSystem',
    meta: {
      title: 'activity_recharges',
      permission: ['background_marketing.recharge']
    },
    children: [
      {
        path: 'recharges',
        name: 'MerchantActivityRecharges',
        component: () =>
          import(
            /* webpackChunkName: "activity_recharges" */ '@/views/merchant/consumption-rules/ActicityRecharges'
          ),
        meta: {
          noCache: true,
          title: 'activity_recharges_list',
          permission: ['background_marketing.recharge.list']
        }
      },
      {
        path: 'activity/:type',
        name: 'MerchantAddActivityRecharge',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_activity_recharges" */ '@/views/merchant/consumption-rules/AddActivityRecharge'
          ),
        meta: {
          noCache: true,
          title: 'activity_recharges',
          permission: ['background_marketing.recharge.add', 'background_marketing.recharge.modify']
        }
      },
      {
        path: 'statistics',
        name: 'MerchantActivityRechargeStatistics',
        component: () =>
          import(
            /* webpackChunkName: "activity_recharges_statistics" */ '@/views/merchant/consumption-rules/ActivityRechargeStatistics'
          ),
        meta: {
          noCache: true,
          title: 'activity_recharges_statistics',
          permission: ['background_marketing.recharge.report']
        }
      }
    ]
  },
  {
    path: '/consumption-rules/coupon',
    component: Layout,
    redirect: '/consumption-rules/coupon/coupon_admin',
    alwaysShow: true,
    name: 'MerchantCoupon',
    meta: {
      noCache: true,
      title: 'coupon',
      permission: ['background_coupon.coupon_manage']
    },
    children: [
      {
        path: 'coupon_admin',
        name: 'MerchantCouponAdmin',
        component: () =>
          import(
            /* webpackChunkName: "coupon_admin" */ '@/views/merchant/consumption-rules/coupon-admin/CouponAdmin'
          ),
        meta: {
          noCache: true,
          title: 'coupon_admin',
          permission: ['background_coupon.coupon_manage.list']
        }
      },
      {
        path: 'coupon_admin/:type',
        name: 'MerchantAddCoupon',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_discount_coupon" */ '@/views/merchant/consumption-rules/coupon-admin/AddCoupon'
          ),
        meta: {
          noCache: true,
          title: 'coupon',
          permission: ['background_coupon.coupon_manage.add', 'background_coupon.coupon_manage.modify']
        }
      },
      {
        path: 'coupon_admin_detail',
        name: 'MerchantCouponAdminDetail',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_discount_coupon" */ '@/views/merchant/consumption-rules/coupon-admin/DetailCoupon'
          ),
        meta: {
          noCache: true,
          title: 'coupon_detail',
          permission: ['background_coupon.coupon_manage.list']
        }
      }
      // {
      //   path: 'discount_coupon_statistics',
      //   name: 'MerchantDiscountCouponStatistics',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "discount_coupon_statistics" */ '@/views/merchant/consumption-rules/coupon/DiscountCouponStatistics'
      //     ),
      //   meta: {
      //     noCache: true,
      //     title: 'discount_coupon_statistics',
      //     permission: ['background_coupon.coupon_manage']
      //   }
      // }
    ]
  },
  {
    path: '/consumption-rules/operations_management',
    redirect: '/consumption-rules/operations_management/banner',
    component: Layout,
    name: 'MerchantOperationsManagement',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'operations_management',
      permission: ['marketing_activities_operation']
    },
    children: [
      {
        path: 'banner',
        component: () =>
          import(/* webpackChunkName: "Merchant_banner" */ '@/views/merchant/consumption-rules/operations-management/banner/banner'),
        name: 'MerchantBanner',
        meta: {
          noCache: true,
          title: 'banner_setting',
          permission: ['background_marketing.marketing_banner']
        }
      },
      {
        path: 'add_banner/:type',
        component: () =>
          import(/* webpackChunkName: "Merchant_add_banner" */ '@/views/merchant/consumption-rules/operations-management/banner/AddBanner'),
        name: 'MerchantAddBanner',
        hidden: true,
        meta: {
          noCache: true,
          title: 'banner',
          permission: ['background_marketing.marketing_banner.add', 'background_marketing.marketing_banner.mpdify']
        }
      },
      {
        path: 'mobile_popup',
        component: () =>
          import(/* webpackChunkName: "Merchant_mobile_popup" */ '@/views/merchant/consumption-rules/operations-management/popup/MobilePopup'),
        name: 'MerchantMobilePopup',
        meta: {
          noCache: true,
          title: 'mobile_popup',
          permission: ['background_marketing.marketing_popup']
        }
      },
      {
        path: 'add_mobile_popup/:type',
        component: () =>
          import(/* webpackChunkName: "Merchant_add_mobile_popup" */ '@/views/merchant/consumption-rules/operations-management/popup/AddMobilePopup'),
        name: 'MerchantAddMobilePopup',
        hidden: true,
        meta: {
          noCache: true,
          title: 'mobile_popup',
          permission: ['background_marketing.marketing_popup.add', 'background_marketing.marketing_popup.mpdify']
        }
      },
      {
        path: 'survey_admin',
        component: () =>
          import(/* webpackChunkName: "survey_admin" */ '@/views/merchant/consumption-rules/operations-management/survey/SurveyAdmin'),
        name: 'MerchantSurveyAdmin',
        meta: {
          noCache: true,
          title: 'survey_admin',
          permission: ['background_marketing.survey_info.list']
        }
      },
      {
        path: 'add_survey/:type',
        component: () =>
          import(/* webpackChunkName: "add_survey" */ '@/views/merchant/consumption-rules/operations-management/survey/AddSurvey'),
        name: 'AddSurvey',
        hidden: true,
        meta: {
          noCache: true,
          title: 'survey_admin',
          activeMenu: '/consumption-rules/operations_management/survey_admin',
          permission: ['background_marketing.survey_info.add', 'background_marketing.survey_info.modify']
        }
      },
      {
        path: 'survey_detail',
        component: () =>
          import(/* webpackChunkName: "survey_admin" */ '@/views/merchant/consumption-rules/operations-management/survey/SurveyDetail'),
        name: 'MerchantSurveyDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'survey_detail',
          activeMenu: '/consumption-rules/operations_management/survey_admin',
          permission: ['background_marketing.survey_info.list']
        }
      },
      {
        path: 'survey_data_total',
        component: () =>
          import(/* webpackChunkName: "survey_admin" */ '@/views/merchant/consumption-rules/operations-management/survey/SurveyDataTotal'),
        name: 'MerchantSurveyDataTotal',
        hidden: true,
        meta: {
          noCache: true,
          title: 'survey_data_total',
          activeMenu: '/consumption-rules/operations_management/survey_admin',
          permission: ['background_marketing.survey_info.list']
        }
      }
    ]
  },
  {
    path: '/consumption-rules/service_admin',
    redirect: '/consumption-rules/service_admin/charge_consume_service',
    component: Layout,
    name: 'MerchantServiceAdmin',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'service_admin',
      permission: ['marketing_activities_rate']
    },
    children: [
      {
        path: 'charge_consume_service',
        component: () =>
          import(/* webpackChunkName: "MerchantRechargeDeductionService" */ '@/views/merchant/consumption-rules/service-admin/ChargeConsumeService'),
        name: 'MerchantRechargeDeductionService',
        meta: {
          noCache: true,
          title: 'recharge_deduction_service',
          permission: ['marketing_activities_rate_settings']
        }
      },
      {
        path: 'service_rule_consume/:type',
        component: () =>
          import(/* webpackChunkName: "MerchantAddServiceRule" */ '@/views/merchant/consumption-rules/service-admin/AddConsumeServiceRule'),
        name: 'MerchantAddConsumeServiceRule',
        hidden: true,
        meta: {
          noCache: true,
          title: 'add_service_rule_consume',
          permission: ['background_marketing.commission_charge.consume_add', 'background_marketing.commission_charge.consume_modify']
        }
      },
      {
        path: 'service_rule_charge/:type',
        component: () =>
          import(/* webpackChunkName: "MerchantAddServiceRule" */ '@/views/merchant/consumption-rules/service-admin/AddChargeServiceRule'),
        name: 'MerchantAddChargeServiceRule',
        hidden: true,
        meta: {
          noCache: true,
          title: 'add_service_rule_charge',
          permission: ['background_marketing.commission_charge.charge_add', 'background_marketing.commission_charge.charge_modify']
        }
      }
    ]
  },
  {
    path: '/consumption-rules/messages_admin',
    redirect: '/consumption-rules/messages_admin/messages_setting',
    component: Layout,
    name: 'MerchantMessagesAdmin',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'messages_admin',
      permission: ['background_messages.third_messages_settings.list']
    },
    children: [
      {
        path: 'messages_setting',
        component: () =>
          import(/* webpackChunkName: "messages_setting" */ '@/views/merchant/consumption-rules/messages-admin/MessagesSetting'),
        name: 'MerchantMessagesSetting',
        meta: {
          noCache: true,
          title: 'messages_setting',
          permission: ['background_messages.third_messages_settings.modify']
        }
      }
    ]
  },
  {
    path: '/consumption-rules/qycode',
    redirect: '/consumption-rules/qycode/alipay_enterprise_code_rules',
    component: Layout,
    name: 'MerchantAlipayEnterpriseCode',
    alwaysShow: false,
    meta: {
      noCache: true,
      title: 'alipay_enterprise_code_rles',
      permission: ['background_marketing.alipay_qycode_rule']
    },
    children: [
      {
        path: 'alipay_enterprise_code_rules',
        component: () =>
          import(/* webpackChunkName: "alipay_enterprise_code_rles" */ '@/views/merchant/consumption-rules/alipay-enterprise-code/AlipayEnterpriseCodeRules'),
        name: 'MerchantAlipayEnterpriseCodeRules',
        meta: {
          noCache: true,
          title: 'alipay_enterprise_code_rles',
          permission: ['background_marketing.alipay_qycode_rule.list']
        }
      },
      {
        path: 'add_alipay_enterprise_code_rules/:type',
        component: () =>
          import(/* webpackChunkName: "add_alipay_enterprise_code_rules" */ '@/views/merchant/consumption-rules/alipay-enterprise-code/AddAlipayEnterpriseCodeRules'),
        name: 'MerchantAddAlipayEnterpriseCodeRules',
        hidden: true,
        meta: {
          noCache: true,
          title: 'add_alipay_enterprise_code_rules',
          activeMenu: '/consumption-rules/qycode/alipay_enterprise_code_rules',
          permission: ['background_marketing.alipay_qycode_rule.modify', 'background_marketing.alipay_qycode.add']
        }
      }
    ]
  },
  {
    path: '/consumption-rules/feedback',
    redirect: '/consumption-rules/feedback/feedback',
    component: Layout,
    name: 'MerchantFeedback',
    alwaysShow: false,
    meta: {
      noCache: true,
      title: 'canteen_feedback',
      permission: ['operation_management.feedbackrecord']
    },
    children: [
      {
        path: 'canteen_feedback',
        component: () =>
          import(
            /* webpackChunkName: "canteen_feedback" */ '@/views/merchant/order/operations-management/feedback'
          ),
        name: 'MerchantCanteenFeedback',
        meta: {
          noCache: true,
          title: 'canteen_feedback',
          permission: ['background_feedback.feedback_record.list']
        }
      }
    ]
  },
  // 一级-报表中心
  {
    path: '/data_report',
    component: Layout,
    redirect: '/data_report/detail_total_list',
    alwaysShow: true,
    name: 'MerchantDataReport',
    meta: {
      noCache: true,
      title: 'dataReport',
      permission: ['data_report']
    },
    children: [
      // 明细总表
      {
        path: 'detail_total_list',
        component: () =>
          import(
            /* webpackChunkName: "detail_total_list" */ '@/views/merchant/report/financial-statements/DetailTotalList'
          ),
        name: 'MerchantDetailTotalList',
        meta: {
          noCache: true,
          title: 'detail_total_list',
          permission: ['background_order.finance_report.unified_order_list']
        }
      },
      // 消费明细表
      {
        path: 'consume_detail_list',
        component: () =>
          import(
            /* webpackChunkName: "consume_detail_list" */ '@/views/merchant/report/financial-statements/ConsumeDetailList'
          ),
        name: 'MerchantConsumeDetailList',
        meta: {
          noCache: true,
          title: 'consume_detail_list',
          permission: ['background_order.finance_report.payment_order_detail_list']
        }
      },
      // 充值明细表
      {
        path: 'top_up_detail',
        component: () =>
          import(
            /* webpackChunkName: "top_up_detail" */ '@/views/merchant/report/financial-statements/TopUpDetail'
          ),
        name: 'MerchantTopUpDetail',
        meta: {
          noCache: true,
          title: 'top_up_detail',
          permission: ['background_order.finance_report.pecharge_order_list']
        }
      },
      // 提现明细表
      {
        path: 'withdraw_list',
        component: () =>
          import(
            /* webpackChunkName: "business_list" */ '@/views/merchant/report/financial-statements/WithdrawList'
          ),
        name: 'MerchantWithdrawList',
        meta: {
          noCache: true,
          title: 'withdraw_list',
          permission: ['background_report_center.data_report.order_withdraw_details_list']
        }
      },
      // 冲销明细表
      {
        path: 'write_off_detail',
        component: () =>
          import(
            /* webpackChunkName: "write_off_detail" */ '@/views/merchant/report/financial-statements/WriteOffDetail'
          ),
        name: 'MerchantWriteOffDetail',
        meta: {
          noCache: true,
          title: 'write_off_detail',
          permission: ['background_report_center.finance_report.charge_off_details_list']
        }
      },
      // 补贴清零明细表
      {
        path: 'subsidy_clear_detail',
        component: () =>
          import(
            /* webpackChunkName: "subsidy_clear_detail" */ '@/views/merchant/report/financial-statements/SubsidyClearDetail'
          ),
        name: 'MerchantSubsidyClearDetail',
        meta: {
          noCache: true,
          title: 'subsidy_clear_detail',
          permission: ['background_report_center.finance_report.clear_subsidy_details_list']
        }
      },
      // 工本费收款明细
      {
        path: 'flat_cost_report',
        component: () =>
          import(
            /* webpackChunkName: "flat_cost_report" */ '@/views/merchant/report/financial-statements/FlatCostReport'
          ),
        name: 'MerchantFlatCostReport',
        meta: {
          noCache: true,
          title: 'flat_cost_report',
          permission: ['background_report_center.data_report.flat_cost_list']
        }
      },
      // 工本费退款明细
      {
        path: 'flat_cost_refund',
        component: () =>
          import(
            /* webpackChunkName: "flat_cost_refund" */ '@/views/merchant/report/financial-statements/FlatCostRefund'
          ),
        name: 'MerchantFlatCostRefund',
        meta: {
          noCache: true,
          title: 'flat_cost_refund',
          permission: ['background_report_center.data_report.flat_cost_refund_list']
        }
      },
      // 设备消费明细
      {
        path: 'device_cost',
        component: () =>
          import(
            /* webpackChunkName: "device_cost" */ '@/views/merchant/report/data_reporting/DeviceCost'
          ),
        name: 'MerchantDeviceCost',
        meta: {
          noCache: true,
          title: 'device_cost',
          permission: ['background_order.finance_report.device_consume_list']
        }
      },
      // 设备汇总表
      {
        path: 'device_cost_summary',
        component: () =>
          import(
            /* webpackChunkName: "device_cost_summary" */ '@/views/merchant/report/data_reporting/DeviceCostSummary'
          ),
        name: 'MerchantDeviceCostSummary',
        meta: {
          noCache: true,
          title: 'device_cost_summary',
          permission: ['background_order.finance_report.device_consume_summary_list']
        }
      },
      // 优惠券明细表
      {
        path: 'coupon_detail_list',
        component: () =>
          import(
            /* webpackChunkName: "person_meal_report" */ '@/views/merchant/report/financial-statements/CouponDetailList'
          ),
        name: 'MerchantCouponDetailList',
        meta: {
          noCache: true,
          title: 'coupon_detail_list',
          permission: ['background_report_center.data_report.coupon_order_list']
        }
      },
      // 餐补使用明细表
      {
        path: 'meal_used_detail_list',
        component: () =>
          import(
            /* webpackChunkName: "meal_used_detail_list" */ '@/views/merchant/report/financial-statements/MealAllowanceUsedDetailsList'
          ),
        name: 'MealAllowanceUsedDetailsList',
        meta: {
          noCache: true,
          title: 'meal_allowance_used_details_list',
          permission: ['background_report_center.data_report.meal_supplement_details_list']
        }
      },
      // 餐补使用汇总表
      {
        path: 'meal_used_summary_list',
        component: () =>
          import(
            /* webpackChunkName: "meal_used_summary_list" */ '@/views/merchant/report/financial-statements/MealAllowanceUsedSummaryList'
          ),
        name: 'MealAllowanceUsedSummaryList',
        meta: {
          noCache: true,
          title: 'meal_allowance_used_summary_list',
          permission: ['background_report_center.data_report.meal_supplement_summary_list']
        }
      }
    ]
  },
  {
    path: '/report_center',
    component: Layout,
    redirect: '/report_center/consume_reconciliation',
    alwaysShow: true,
    name: 'MerchantReportCenter',
    meta: {
      title: 'financialStatement',
      permission: ['financial_report']
    },
    children: [
      // 消费点对账表
      {
        path: 'consume_reconciliation',
        component: () =>
          import(
            /* webpackChunkName: "consume_reconciliation" */ '@/views/merchant/report/financial-statements/ConsumeReconciliation'
          ),
        name: 'MerchantConsumeReconciliation',
        meta: {
          noCache: true,
          title: 'consume_reconciliation',
          permission: ['background_order.finance_report.reconciliation_statement_list']
        }
      },
      // 营业额日报表
      {
        path: 'business_list',
        component: () =>
          import(
            /* webpackChunkName: "business_list" */ '@/views/merchant/report/financial-statements/BusinessList'
          ),
        name: 'MerchantReconciliationStatement',
        meta: {
          noCache: true,
          title: 'business_list',
          permission: ['background_order.finance_report.order_business_list']
        }
      },
      // 个人充值汇总
      {
        path: 'personal_recharge_summary',
        component: () =>
          import(
            /* webpackChunkName: "personal_recharge_summary" */ '@/views/merchant/report/financial-statements/PersonalRechargeSummary'
          ),
        name: 'MerchantPersonalRechargeSummary',
        meta: {
          noCache: true,
          title: 'personal_recharge_summary',
          permission: ['background_order.finance_report.person_charge_list']
        }
      },
      // 部门消费汇总
      {
        path: 'departmental_consumption_summary',
        component: () =>
          import(
            /* webpackChunkName: "departmental_consumption_summary" */ '@/views/merchant/report/financial-statements/DepartmentalConsumptionSummary'
          ),
        name: 'MerchantDepartmentalConsumptionSummary',
        meta: {
          noCache: true,
          title: 'departmental_consumption_summary',
          permission: ['background_order.finance_report.department_payment_collect_list']
        }
      },
      // 个人消费汇总
      {
        path: 'personal_consumption_summary',
        component: () =>
          import(
            /* webpackChunkName: "personal_consumption_summary" */ '@/views/merchant/report/financial-statements/PersonalConsumptionSummary'
          ),
        name: 'MerchantPersonalConsumptionSummary',
        meta: {
          noCache: true,
          title: 'personal_consumption_summary',
          permission: ['background_order.finance_report.person_payment_collect_list']
        }
      },
      // 账户钱包汇总
      {
        path: 'account_wallet_daily',
        component: () =>
          import(
            /* webpackChunkName: "account_wallet_daily" */ '@/views/merchant/report/financial-statements/AccountWalletDaily'
          ),
        name: 'MerchantAccountWalletDaily',
        meta: {
          noCache: true,
          title: 'account_wallet_daily',
          permission: ['background_order.finance_report.wallet_daily_list']
        }
      },
      // 个人钱包日报表
      {
        path: 'personal_wallet_daily',
        component: () =>
          import(
            /* webpackChunkName: "account_wallet_daily" */ '@/views/merchant/report/financial-statements/PersonalWalletDaily'
          ),
        name: 'MerchantPersonalWalletDaily',
        meta: {
          noCache: true,
          title: 'personal_wallet_daily',
          permission: ['background_order.finance_report.person_wallet_daily_list']
        }
      },
      // 收款码明细表
      {
        path: 'collectionl_code_report',
        component: () =>
          import(
            /* webpackChunkName: "account_wallet_daily" */ '@/views/merchant/report/financial-statements/CollectionlCodeReport'
          ),
        name: 'MerchantCollectionlCodeReport',
        meta: {
          noCache: true,
          title: 'collectionl_code_report',
          permission: ['background_order.finance_report.instore_payment_detail_list']
        }
      },
      // 手续费总表
      {
        path: 'service_charge_report',
        component: () =>
          import(
            /* webpackChunkName: "service_charge_report" */ '@/views/merchant/report/financial-statements/ServiceChargeReport'
          ),
        name: 'MerchantServiceChargeReport',
        meta: {
          noCache: true,
          title: 'service_charge_report',
          permission: ['background_order.finance_report.commission_consume_list', 'background_order.finance_report.commission_charge_list']
        }
      },
      // 第三方对账表
      {
        path: 'third_reconciliation',
        component: () =>
          import(
            /* webpackChunkName: "third_reconciliation" */ '@/views/merchant/report/financial-statements/ThirdReconciliation'
          ),
        name: 'MerchantThirdReconciliation',
        meta: {
          noCache: true,
          title: 'third_reconciliation',
          permission: ['background_report_center.data_report.third_order_list']
        }
      },
      // 分组储值汇总表
      {
        path: 'group_wallet_daily',
        component: () =>
          import(
            /* webpackChunkName: "group_wallet_daily" */ '@/views/merchant/report/financial-statements/GroupWalletDaily'
          ),
        name: 'MerchantGroupWalletDaily',
        meta: {
          noCache: true,
          title: 'group_wallet_daily',
          permission: ['background_report_center.data_report.group_wallet_daily_list']
        }
      },
      // 消费点汇总表
      {
        path: 'consumption_summary_report',
        component: () =>
          import(
            /* webpackChunkName: "consumption_summary_report" */ '@/views/merchant/report/financial-statements/ConsumptionSummaryReport'
          ),
        name: 'MerchantConsumptionSummaryReport',
        meta: {
          noCache: true,
          title: 'consumption_summary_report',
          permission: ['background_report_center.finance_report.consumption_summary_list']
        }
      },
      {
        path: 'third_party_reconciliation',
        component: () =>
          import(
            /* webpackChunkName: "person_meal_report" */ '@/views/merchant/report/financial-statements/ThirdPartyReconciliation'
          ),
        name: 'ThirdPartyReconciliation',
        meta: {
          noCache: true,
          title: 'third_party_reconciliation',
          // no_permission: true,
          permission: ['background_report_center.data_report.compare_bill_record']
        }
      },
      // 消费汇总表
      {
        path: 'consumption_summary',
        component: () =>
          import(
            /* webpackChunkName: "consumption_summary" */ '@/views/merchant/report/financial-statements/ConsumptionSummary'
          ),
        name: 'MerchantConsumptionsSummary',
        meta: {
          noCache: true,
          title: 'consumption_summary',
          permission: ['background_report_center.data_report.consume_summary_list']
        }
      },
      // 充值汇总表
      {
        path: 'recharge_summary',
        component: () =>
          import(
            /* webpackChunkName: "recharge_summary" */ '@/views/merchant/report/financial-statements/RechargeSummary'
          ),
        name: 'MerchantRechargeSummary',
        meta: {
          noCache: true,
          title: 'recharge_summary',
          permission: ['background_report_center.data_report.recharge_summary_list']
        }
      }
    ]
  },
  {
    path: '/report_management',
    component: Layout,
    redirect: '/report_management/food_sale_ranking',
    alwaysShow: true,
    name: 'MerchantReportManagement',
    meta: {
      noCache: true,
      title: 'report_management',
      permission: ['business_report_management']
    },
    children: [
      {
        path: 'food_sale_ranking',
        component: () =>
          import(
            /* webpackChunkName: "detail_total_list" */ '@/views/merchant/report/report-management/FoodSaleRanking'
          ),
        name: 'MerchantFoodSaleRanking',
        meta: {
          noCache: true,
          title: 'food_sale_ranking',
          permission: ['background_order.manage_report.food_payment_ranking_list']
        }
      },
      {
        path: 'summary_of_sales',
        component: () =>
          import(
            /* webpackChunkName: "detail_total_list" */ '@/views/merchant/report/report-management/SummaryOfSales'
          ),
        name: 'MerchantSummaryOfSales',
        meta: {
          noCache: true,
          title: 'summary_of_sales',
          permission: ['background_order.manage_report.food_sort_payment_ranking_list']
        }
      },
      {
        path: 'dishes_taken_ranking',
        component: () =>
          import(
            /* webpackChunkName: "detail_total_list" */ '@/views/merchant/report/report-management/DishesTakenRanking'
          ),
        name: 'MerchantDishesTakenRanking',
        meta: {
          noCache: true,
          title: 'dishes_taken_ranking',
          permission: ['background_order.manage_report.food_payment_weight_ranking_list']
        }
      },
      {
        path: 'third_party_reconciliation_summary',
        component: () =>
          import(
            /* webpackChunkName: "third_reconciliation" */ '@/views/merchant/report/financial-statements/ThirdPartyReconciliationSummary'
          ),
        name: 'ThirdPartyReconciliationSummary',
        meta: {
          noCache: true,
          title: 'third_party_reconciliation_summary',
          permission: ['background_order.data_report.compare_bill_collect_list'] // 权限有问题
        }
      }
    ]
  },
  {
    path: '/customized_report',
    component: Layout,
    redirect: '/customized_report/customReport0',
    alwaysShow: true,
    name: 'MerchantCustomizedReport',
    meta: {
      noCache: true,
      title: 'customized_report',
      permission: ['background_report_center.jmreport.jmreport_menu_list']
    },
    children: [
      // {
      //   path: `customReport0`,
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "custom_report" */ '@/views/merchant/report/custom-report/CustomReport'
      //     ),
      //   name: `MerchantCustomReport0`,
      //   meta: {
      //     noCache: true,
      //     title: '111',
      //     permission: ['bank_financial_report']
      //   }
      // }
    ]
  },
  {
    path: '/report_center/bank',
    component: Layout,
    redirect: '/report_center/bank/settlement_details',
    alwaysShow: true,
    name: 'MerchantReportCenter',
    meta: {
      noCache: true,
      title: 'bankfinancialStatement',
      permission: ['bank_financial_report']
    },
    children: [
      {
        path: 'settlement_details',
        component: () =>
          import(
            /* webpackChunkName: "settlement_details" */ '@/views/merchant/report/bank-financial-statements/SettlementDetails'
          ),
        name: 'MerchantSettlementDetails',
        meta: {
          noCache: true,
          title: 'settlement_details',
          permission: ['background_order.finance_report.out_put_list']
        }
      },
      {
        path: 'account_billing_details',
        component: () =>
          import(
            /* webpackChunkName: "account_billing_details" */ '@/views/merchant/report/bank-financial-statements/AccountBillingDetails'
          ),
        name: 'MerchantAccountBillingDetails',
        meta: {
          noCache: true,
          title: 'account_billing_details',
          permission: ['background_order.finance_report.sub_mch_order_list']
        }
      },
      {
        path: 'bank_flow_details',
        component: () =>
          import(
            /* webpackChunkName: "bank_flow_details" */ '@/views/merchant/report/bank-financial-statements/BankFlowDetails'
          ),
        name: 'MerchantBankFlowDetails',
        meta: {
          noCache: true,
          title: 'bank_flow_details',
          permission: ['background_order.finance_report.sub_mch_order_detail_list', 'background_report_center.data_report.sub_mch_order_detail_refund_list']
        }
      }
    ]
  },
  // 支付宝报表
  {
    path: '/report_center/alipay',
    component: Layout,
    redirect: 'report_center/alipay/alipay_enterprise_code_order',
    alwaysShow: true,
    name: 'MerchantReportCenterAlipay',
    meta: {
      noCache: true,
      title: 'alipay_report',
      permission: ['alipay_report']
    },
    children: [
      {
        path: 'alipay_enterprise_code_order',
        component: () =>
          import(
            /* webpackChunkName: "alipay_enterprise_code_order" */ '@/views/merchant/report/alipay/AlipayEnterpriseCodeOrder'
          ),
        name: 'MerchantAlipayEnterpriseCodeOrder',
        meta: {
          noCache: true,
          title: 'alipay_enterprise_code_order',
          // no_permission: true,
          permission: ['background_report_center.alipay_qycode.alipay_order_list']
        }
      }
    ]
  },
  // // 开票记录
  // {
  //   path: '/receipt_list',
  //   component: Layout,
  //   redirect: '/receipt_list/record',
  //   alwaysShow: false,
  //   name: 'ReceiptList',
  //   meta: {
  //     noCache: true,
  //     title: 'receipt_list',
  //     permission: ['background_invoice.invoice_record']
  //   },
  //   children: [
  //     {
  //       path: 'record',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "receipt_list" */ '@/views/merchant/report/financial-statements/ReceiptList'
  //         ),
  //       name: 'MerchantReceiptList',
  //       meta: {
  //         noCache: true,
  //         title: 'receipt_list',
  //         permission: ['background_invoice.invoice_record.invoice_record_list']
  //       }
  //     }
  //   ]
  // },
  // 人员就餐统计报表
  {
    path: '/person_meal_report',
    component: Layout,
    redirect: '/person_meal_report/list',
    alwaysShow: false,
    name: 'PersonMealReport',
    meta: {
      noCache: true,
      title: 'person_meal_report',
      permission: ['background_order.finance_report']
    },
    children: [
      {
        path: 'list',
        component: () =>
          import(
            /* webpackChunkName: "person_meal_report" */ '@/views/merchant/report/financial-statements/PersonMealReport'
          ),
        name: 'MerchantPersonMealReport',
        meta: {
          noCache: true,
          title: 'person_meal_report',
          permission: ['background_order.finance_report.person_meal_list']
        }
      }
    ]
  },
  // 一级-健康系统
  {
    path: '/health_system',
    component: Layout,
    redirect: '/health_system/health_report',
    alwaysShow: true,
    name: 'MerchantHealthSystem',
    meta: {
      title: 'health_system',
      permission: ['health_management']
    },
    children: [
      {
        path: 'health_report',
        component: () =>
          import(/* webpackChunkName: "merchant_health_report" */ '@/views/merchant/health-system/HealthSystem'),
        name: 'MerchantHealthSystem',
        meta: {
          noCache: true,
          title: 'user_health_system',
          permission: ['background_healthy.healthy_info.list']
        }
      },
      {
        path: 'physical_examination_report',
        component: () =>
          import(/* webpackChunkName: "merchant_health_report" */ '@/views/merchant/health-system/PhysicalExaminationReport'),
        name: 'MerchantPhysicalExaminationReport',
        meta: {
          noCache: true,
          title: 'user_physical_examination_report',
          permission: ['background.admin.healthy_info.check_data_list']
        }
      },
      {
        path: 'disease_management',
        component: () =>
          import(/* webpackChunkName: "merchant_health_report" */ '@/views/merchant/health-system/DiseaseManagement'),
        name: 'MerchantDiseaseManagement',
        meta: {
          noCache: true,
          title: 'user_disease_management',
          permission: ['background_healthy.disease.list']
        }
      }
    ]
  },
  // 一级-缴费中心
  {
    path: '/jiaofei',
    component: Layout,
    redirect: '/jiaofei/jiaofei_admin',
    alwaysShow: true,
    name: 'MerchantJiaoFei',
    meta: {
      noCache: true,
      title: 'jiaofei_center',
      permission: ['background_jiaofei']
    },
    children: [
      {
        path: 'jiaofei_admin',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiAdmin" */ '@/views/merchant/jiaofei-center/JiaoFeiAdmin'
          ),
        name: 'MerchantJiaoFeiAdmin',
        meta: {
          noCache: true,
          title: 'jiaofei_admin',
          permission: ['background_jiaofei.list']
        }
      },
      {
        path: 'jiaofei/:type',
        component: () =>
          import(
            /* webpackChunkName: "AddOrEditJiaoFei" */ '@/views/merchant/jiaofei-center/AddOrEditJiaoFei'
          ),
        name: 'MerchantAddOrEditJiaoFei',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/jiaofei/jiaofei_admin',
          title: 'jiaofei_setting',
          permission: ['background_jiaofei.list']
        }
      },
      {
        path: 'jiaofei_detail',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiDetail" */ '@/views/merchant/jiaofei-center/JiaoFeiDetail'
          ),
        name: 'MerchantJiaoFeiDetail',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/jiaofei/jiaofei_admin',
          title: 'jiaofei_detail',
          permission: ['background_jiaofei.list']
        }
      },
      {
        path: 'jiaofei_order',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiOrder" */ '@/views/merchant/jiaofei-center/JiaoFeiOrder'
          ),
        name: 'MerchantJiaoFeiOrder',
        meta: {
          noCache: true,
          title: 'jiaofei_order',
          permission: ['background_order.order_jiao.list']
        }
      },
      {
        path: 'jiaofei_refund_order',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiRefundOrder" */ '@/views/merchant/jiaofei-center/JiaoFeiRefundOrder'
          ),
        name: 'MerchantJiaoFeiRefundOrder',
        meta: {
          noCache: true,
          title: 'jiaofei_refund_order',
          permission: ['background_order.order_jiao.refund_list']
        }
      },
      {
        path: 'jiaofei_refund_apply',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiRefundApply" */ '@/views/merchant/jiaofei-center/JiaoFeiRefundApply'
          ),
        name: 'MerchantJiaoFeiRefundApply',
        meta: {
          noCache: true,
          title: 'jiaofei_refund_apply',
          permission: ['background_order.order_jiao.approval_list']
        }
      }
    ]
  },
  // 一级-用户中心
  {
    path: '/user-center',
    component: Layout,
    redirect: '/user-center/list',
    alwaysShow: true,
    name: 'MerchantUserCenter',
    meta: {
      title: 'user_admin',
      permission: ['card_service.card_user']
    },
    children: [
      {
        path: 'list',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UseCenter'
          ),
        name: 'MerchantUseCenter',
        meta: {
          noCache: true,
          title: 'user_admin_list',
          permission: ['card_service.card_user.list']
        }
      },
      {
        path: 'mul_import_face',
        component: () =>
          import(
            /* webpackChunkName: "merchant-mul_import_face" */ '@/views/merchant/user-center/MulImportFace'
          ),
        name: 'MerchantMulImportFace',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/user-center/list',
          title: 'mul_import_face',
          permission: ['card_service.card_face.batch_import']
        }
      },
      {
        path: 'userDepartment',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UserDepartment'
          ),
        name: 'MerchantUserDepartment',
        meta: {
          noCache: true,
          title: 'user_department',
          permission: ['card_service.card_department_group']
        }
      },
      {
        path: 'userGroup',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UserGroup'
          ),
        name: 'MerchantUserGroup',
        meta: {
          noCache: true,
          title: 'user_group',
          permission: ['card_service.card_user_group.list']
        }
      },
      {
        path: 'cardLossAdmin',
        component: () =>
          import(
            /* webpackChunkName: "merchant-mul_import_face" */ '@/views/merchant/user-center/CardLossAdmin'
          ),
        name: 'MerchantCardLossAdmin',
        meta: {
          noCache: true,
          title: 'card_loss_list',
          permission: ['card_service.card_user.card_loss_list']
        }
      },
      {
        path: 'information_collection',
        name: 'MerchantInformationCollection',
        component: () =>
          import(
            /* webpackChunkName: "approve_rules" */ '@/views/merchant/system/InformationCollection'
          ),
        meta: {
          title: 'information_collection',
          permission: ['card_service.user_collect.list']
        }
      },
      {
        path: 'auto_register_approve',
        component: () =>
          import(
            /* webpackChunkName: "merchant_AutoRegisterApprove" */ '@/views/merchant/user-center/AutoRegisterApprove'
          ),
        name: 'MerchantAutoRegisterApprove',
        meta: {
          title: 'auto_register_approve',
          permission: ['approve_register']
        }
      }
    ]
  },
  {
    path: '/user-center/userAccountAdmin',
    component: Layout,
    redirect: '/user-center/userAccountAdmin/userAccountList',
    name: 'MerchantUserAccountAdmin',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'user_account_admin',
      permission: ['card_service.card_user.account']
    },
    children: [
      {
        path: 'userAccountList',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UserAccountList'
          ),
        name: 'MerchantUserAccountList',
        meta: {
          noCache: true,
          title: 'user_account_list',
          permission: ['card_service.card_user.account_list']
        }
      },
      {
        path: 'userAccountSetting',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UserAccountSetting'
          ),
        name: 'MerchantUserAccountSetting',
        meta: {
          noCache: true,
          title: 'user_account_setting',
          permission: ['card_service.card_user_group.get_org_flat_and_patch_cost_settings']
        }
      },
      // 透支设置后续做成抽屉
      {
        path: 'overdraftSetting',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/OverdraftSetting'
          ),
        name: 'MerchantOverdraftSetting',
        meta: {
          noCache: true,
          title: 'overdraft_setting',
          permission: ['card_service.card_user_group.get_org_debt_settings']
        }
      },
      {
        path: 'overdraftDetail',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/OverdraftDetail'
          ),
        name: 'MerchantOverdraftDetail',
        meta: {
          noCache: true,
          title: 'Overdraft_detail',
          permission: ['card_service.card_user_group.debt_order_report']
        }
      },
      {
        path: 'withdrawalAdmin',
        component: () =>
          import(
            /* webpackChunkName: "merchant-mul_import_face" */ '@/views/merchant/user-center/WithdrawalAdmin'
          ),
        name: 'MerchantWithdrawalAdmin',
        meta: {
          noCache: true,
          title: 'withdrawal_admin',
          permission: ['card_service.card_user.person_quit_list']
        }
      }
    ]
  },
  {
    path: '/user-center/flatCostListAdmin',
    component: Layout,
    redirect: '/user-center/flatCostListAdmin/flatCostList',
    name: 'MerchantFlatCostListAdmin',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'user_flat_cost',
      permission: ['card_service.flat_cost']
    },
    children: [
      {
        path: 'flatCostList',
        component: () =>
          import(
            /* webpackChunkName: "merchant-flatCostList" */ '@/views/merchant/user-center/FlatCostList'
          ),
        name: 'MerchantFlatCostList',
        meta: {
          noCache: true,
          title: 'user_flat_cost_list',
          permission: ['card_service.flat_cost.list']
        }
      },
      {
        path: 'repairCardList',
        component: () =>
          import(
            /* webpackChunkName: "merchant-repairCardList" */ '@/views/merchant/user-center/RepairCardList'
          ),
        name: 'MerchantRepairCardList',
        meta: {
          noCache: true,
          title: 'user_repair_card_list',
          permission: ['card_service.flat_cost.supplementary_list']
        }
      },
      {
        path: 'CardOperationHistoryList',
        component: () =>
          import(
            /* webpackChunkName: "merchant-flatCostList" */ '@/views/merchant/user-center/CardOperationHistoryList'
          ),
        name: 'MerchantCardOperationHistoryList',
        meta: {
          noCache: true,
          title: 'user_card_operation_history',
          permission: ['card_service.flat_cost.card_operation_list']
        }
      }
    ]
  },
  {
    path: '/user-center/subsidy-rules',
    component: Layout,
    redirect: '/user-center/subsidy-rules/cardSubsidy',
    name: 'MerchantSubsidyRules',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'user_card_subsidy',
      permission: ['card_service.card_subsidy']
    },
    children: [
      {
        path: 'cardSubsidy',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/CardSubsidy'
          ),
        name: 'MerchantCardSubsidy',
        meta: {
          noCache: true,
          title: 'subsidy_rules',
          permission: ['card_service.card_subsidy.list']
        }
      },
      {
        path: 'subsidy_receive_detail',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/SubsidyReceiveDetail'
          ),
        name: 'MerchantSubsidyReceiveDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'subsidy_receive_detail',
          activeMenu: '/user-center/cardSubsidy',
          permission: ['card_service.card_subsidy.info_list']
        }
      },
      {
        path: 'cardSubsidy_:type',
        component: () =>
          import(/* 2: "merchant-usecenter" */ '@/views/merchant/user-center/AddCardSubsidy'),
        name: 'MerchantAddCardSubsidy',
        hidden: true,
        meta: {
          noCache: true,
          title: 'user_card_subsidy_add',
          activeMenu: '/user-center/cardSubsidy',
          permission: ['card_service.card_subsidy.add', 'card_service.card_subsidy.modify']
        }
      },
      {
        path: 'cardSubsidyDetail',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/CardSubsidyDetail'
          ),
        name: 'MerchantCardSubsidyDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'user_card_subsidy_detail',
          permission: ['card_service.card_subsidy.info_list']
        }
      }
    ]
  },
  {
    path: '/user-center/mobile-notice',
    component: Layout,
    redirect: '/user-center/mobile-notice/mobile-notice',
    name: 'MerchantMobileNotice',
    alwaysShow: false,
    meta: {
      noCache: true,
      title: 'notice',
      permission: ['background_marketing.marketing_notice.list']
    },
    children: [
      {
        path: 'mobile_notice',
        component: () =>
          import(/* webpackChunkName: "Merchant_mobile_notice" */ '@/views/merchant/consumption-rules/operations-management/notice/NoticeAdmin'),
        name: 'MerchantMobileNotice',
        meta: {
          noCache: true,
          title: 'notice',
          // no_permission: true,
          permission: ['background_marketing.marketing_notice.list']
        }
      },
      {
        path: 'add_mobile_notice/:type',
        component: () =>
          import(/* webpackChunkName: "Merchant_add_mobile_notice" */ '@/views/merchant/consumption-rules/operations-management/notice/NoticeAdd'),
        name: 'MerchantAddMobileNotice',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice',
          // no_permission: true,
          activeMenu: '/consumption-rules/operations_management/mobile_notice',
          permission: ['background_marketing.marketing_notice.list']
        }
      },
      {
        path: 'have_viewed',
        component: () =>
          import(/* webpackChunkName: "Merchant_add_mobile_notice" */ '@/views/merchant/consumption-rules/operations-management/notice/HaveViewed'),
        name: 'MerchantHaveViewed',
        hidden: true,
        meta: {
          noCache: true,
          title: 'haveViewed',
          // no_permission: true,
          permission: ['background_marketing.marketing_notice.list']
        }
      }
    ]
  },
  {
    path: '/userBandingWater',
    component: Layout,
    redirect: '/userBandingWater/banding',
    name: 'UserBandingWater',
    alwaysShow: true,
    meta: {
      // noCache: true,
      title: 'user_banding_water',
      permission: ['card_service.third_card_user.sk_list']
    },
    children: [
      {
        path: 'banding',
        component: () => import('@/views/merchant/user-center/UserBandingWaterList'),
        name: 'UserBandingWaterList',
        meta: {
          noCache: true,
          title: 'user_banding_water',
          permission: ['card_service.third_card_user.sk_list']
        }
      }
    ]
  },
  {
    path: '/bind_customer',
    component: Layout,
    redirect: '/bind_customer/binding',
    alwaysShow: true,
    name: 'BindCustomerMain',
    meta: {
      title: 'customer_bind_user',
      permission: ['card_service.third_card_user.client_number_list']
    },
    children: [
      {
        path: 'binding',
        component: () => import('@/views/merchant/user-center/BindCustomer'),
        name: 'BindCustomer',
        meta: {
          noCache: true,
          title: 'customer_bind_user',
          permission: ['card_service.third_card_user.client_number_list']
        }
      }
    ]
  },
  {
    path: '/user-center/card-log',
    component: Layout,
    redirect: '/user-center/card-log/cardLog',
    name: 'MerchantCardLog',
    alwaysShow: false,
    meta: {
      // noCache: true,
      title: 'user_card_log',
      permission: ['card_service.card_log.get_card_operation_log_user']
    },
    children: [
      {
        path: 'cardLog',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/CardLog'
          ),
        name: 'MerchantCardLogList',
        meta: {
          noCache: true,
          title: 'user_card_log',
          permission: ['card_service.card_log.get_card_operation_log_list']
        }
      }
    ]
  },
  {
    // 农行校园用户
    path: '/bank_school_manager',
    component: Layout,
    redirect: '@/views/merchant/user-center/BankSchoolUser',
    name: 'BankSchoolManager',
    alwaysShow: true,
    meta: {
      // noCache: true,
      title: 'bank_school_manager',
      permission: ['background_ebank.abc_school_user.list']
    },
    children: [
      {
        path: 'bank_school_user',
        component: () =>
          import(
            /* webpackChunkName: "BankSchoolUser" */ '@/views/merchant/user-center/BankSchoolUser'
          ),
        name: 'BankSchoolUser',
        meta: {
          noCache: true,
          title: 'bank_school_user',
          permission: ['background_ebank.abc_school_user.list']
        }
      }
    ]
  },
  {
    // 农行校园用户
    path: '/campus_management',
    component: Layout,
    redirect: '@/views/merchant/user-center/CampusManagement',
    name: 'MerchantCampusManagement',
    alwaysShow: false,
    meta: {
      // noCache: true,
      title: 'campus_management',
      permission: ['background_ebank.abc_school_user.list']
    },
    children: [
      {
        path: 'list',
        component: () =>
          import(
            /* webpackChunkName: "BankSchoolUser" */ '@/views/merchant/user-center/CampusManagement'
          ),
        name: 'MerchantCampusManagementList',
        meta: {
          noCache: true,
          title: 'campus_management',
          permission: ['background_ebank.abc_school_user.list']
        }
      },
      {
        path: 'architecture_list',
        component: () =>
          import(
            /* webpackChunkName: "BankSchoolUser" */ '@/views/merchant/user-center/SchoolArchitecture'
          ),
        name: 'MerchantArchitectureList',
        hidden: true,
        meta: {
          noCache: true,
          title: 'architecture',
          permission: ['background_ebank.abc_school_user.list']
        }
      }
    ]
  },
  // 一级-系统管理
  {
    path: '/organization',
    component: Layout,
    redirect: '/organization/list',
    alwaysShow: true,
    name: 'MerchantSystem',
    meta: {
      noCache: true,
      title: 'merchant_system',
      permission: ['merchant_system_management']
    },
    children: [
      {
        path: 'list',

        component: () =>
          import(/* webpackChunkName: "organization" */ '@/views/merchant/system/Organization'),
        name: 'MerchantOrganizationList',
        meta: {
          noCache: true,
          title: 'organization_list',
          permission: ['background_organization.organization.list']
        }
      },
      {
        path: 'role',
        component: () => import(/* webpackChunkName: "role" */ '@/views/merchant/system/RoleList'),
        name: 'MerchantRoleList',
        meta: {
          noCache: true,
          title: 'role_management',
          permission: ['background_organization.role.list']
        }
      },
      {
        path: 'role/setting',
        component: () =>
          import(/* webpackChunkName: "RoleSetting" */ '@/views/merchant/system/RoleSetting'),
        name: 'MerchantRoleSetting',
        hidden: true,
        meta: {
          noCache: true,
          title: 'role_setting',
          activeMenu: '/organization/role',
          permission: ['background_organization.role.list']
        }
      },
      {
        path: 'account',
        component: () =>
          import(/* webpackChunkName: "account" */ '@/views/merchant/system/AccountList'),
        name: 'MerchantAccountList',
        meta: {
          noCache: true,
          title: 'role_account_management',
          permission: ['background_organization.account.list']
        }
      },
      {
        path: 'mul_import_face',
        component: () =>
          import(
            /* webpackChunkName: "account-mul_import_face" */ '@/views/merchant/system/MulImportFace'
          ),
        name: 'MerchantAccountMulImportFace',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/organization/account',
          title: 'mul_import_face',
          permission: ['background_organization.account.batch_import_user_faces']
        }
      },
      {
        path: 'general_settings',
        component: () =>
          import(
            /* webpackChunkName: "general_settings" */ '@/views/merchant/system/GeneralSettings'
          ),
        name: 'MerchantGeneralSettings',
        meta: {
          noCache: true,
          title: 'generalSettings',
          permission: ['background_organization.organization.get_common_settings']
        }
      },
      {
        path: 'rule-list',
        name: 'MerchantApproveRulesList',
        component: () =>
          import(
            /* webpackChunkName: "approve-rules_list" */ '@/views/merchant/system/approve-rules/ApproveRules'
          ),
        meta: {
          noCache: true,
          title: 'approve_rules_list',
          permission: ['background_approve.approve_rule.list']
        }
      },
      {
        path: 'approve_rules/:type',
        name: 'MerchantAddApproveRules',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "approve_rules" */ '@/views/merchant/system/approve-rules/AddApproveRules'
          ),
        meta: {
          noCache: true,
          activeMenu: '/approve-rules/list',
          title: 'approve_rules',
          permission: ['background_approve.approve_rule.add', 'background_approve.approve_rule.modify']
        }
      },
      {
        path: 'module_admin',
        component: () =>
          import(
            /* webpackChunkName: "module_admin" */ '@/views/merchant/system/ModuleAdmin'
          ),
        name: 'MerchantModuleAdmin',
        meta: {
          noCache: true,
          title: 'module_admin',
          permission: ['background_organization.app_permission']
        }
      }
      // 旧的食安公示
      // {
      //   path: 'public_information',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "module_admin" */ '@/views/merchant/system/PublicInformation'
      //     ),
      //   name: 'MerchantPublicInformation',
      //   meta: {
      //     noCache: true,
      //     title: 'public_information',
      //     permission: ['publicity_information']
      //   }
      // },
      // 待办事项
      // {
      //   path: 'to_do_list',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "module_admin" */ '@/views/merchant/system/ToDoList'
      //     ),
      //   name: 'MerchantToDoList',
      //   meta: {
      //     noCache: true,
      //     title: 'to_do_list',
      //     permission: ['merchant_system_management']
      //   }
      // }
    ]
  },
  {
    path: '/notice-and-announcement',
    component: Layout,
    redirect: '/notice-and-announcement/notice_admin',
    alwaysShow: false,
    name: 'MerchantNoticeAdmin1',
    meta: {
      noCache: true,
      title: 'notice_admin',
      permission: ['background_messages.messages.list']
    },
    children: [
      {
        path: 'notice_admin',
        component: () =>
          import(
            /* webpackChunkName: "noticeadmin" */ '@/views/merchant/system-settings/NoticeAdmin'
          ),
        name: 'MerchantNoticeAdmin',
        meta: {
          noCache: true,
          title: 'notice_admin',
          permission: ['background_messages.messages.list'] // 假权限
        }
      },
      {
        path: 'notice-:type',
        component: () =>
          import(/* webpackChunkName: "notice_add" */ '@/views/merchant/system-settings/NoticeAdd'),
        name: 'MerchantNoticeAdd',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice',
          permission: ['background_messages.messages.add', 'background_messages.messages.mpdify']
        }
      },
      {
        path: 'notice_list',
        component: () =>
          import(
            /* webpackChunkName: "notice_list" */ '@/views/merchant/system-settings/NoticeList'
          ),
        name: 'MerchantNoticeList',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice_list',
          // activeMenu: '/organization/notice_admin',
          no_permission: true
          // permission: ['background_messages.messages.get_msg_list']
        }
      },
      {
        path: 'notice_detail',
        component: () =>
          import(
            /* webpackChunkName: "notice_detail" */ '@/views/merchant/system-settings/NoticeDetail'
          ),
        name: 'MerchantNoticeDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice_detail',
          permission: ['background_messages.messages.list']
        }
      }
    ]
  },
  {
    path: '/DingTalk-message-configuration',
    component: Layout,
    redirect: '/DingTalk-message-configuration/dingtalk_message_seting',
    alwaysShow: false,
    name: 'DingTalkMessageSetting1',
    meta: {
      noCache: true,
      title: 'dingTalkMessageSetting',
      permission: ['background_messages.dingtalk_message_setting']
    },
    children: [
      {
        path: 'dingtalk_message_seting',
        component: () =>
          import(
            /* webpackChunkName: "noticeadmin" */ '@/views/merchant/system/DingTalkMessageSeting'
          ),
        name: 'DingTalkMessageSeting',
        meta: {
          noCache: true,
          title: 'dingTalkMessageSeting',
          permission: ['background_messages.dingtalk_message_setting'] // 假权限
        }
      }
    ]
  },
  // 操作日志
  {
    path: '/log_list',
    component: Layout,
    redirect: '/log_list/list',
    alwaysShow: false,
    name: 'MerchantLogList',
    meta: {
      noCache: true,
      title: 'notice_admin',
      permission: ['background.log']
    },
    children: [
      {
        path: 'list',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/public/log/OperatingLogList'),
        name: 'MerchantList',
        meta: {
          noCache: true,
          title: 'list_log',
          permission: ['background.log.list']
        }
      }
    ]
  },
  // 一级-监管管理
  {
    path: '/Fund-supervision',
    component: Layout,
    redirect: '/Fund-supervision/Fund-upload',
    alwaysShow: true,
    name: 'MerchantFundSuperVision',
    meta: {
      noCache: true,
      title: 'fund_supervision',
      permission: ['fund_supervision']
    },
    children: [
      {
        path: 'Fund-upload',
        component: () =>
          import(/* webpackChunkName: "organization" */ '@/views/merchant/system/fund-supervision/FundUpload'),
        name: 'MerchantFundUpload',
        meta: {
          noCache: true,
          title: 'fund_upload',
          permission: ['background_fund_supervision.fund_upload.fund_upload_list']
        }
      },
      {
        path: 'Fund-approval',
        component: () =>
          import(/* webpackChunkName: "organization" */ '@/views/merchant/system/fund-supervision/FundApproval'),
        name: 'MerchantFundApproval',
        meta: {
          noCache: true,
          title: 'fund_approval',
          permission: ['background_approve.approve_fund.list']
        }
      },
      {
        path: 'Income-and-expenditure-detail',
        component: () =>
          import(/* webpackChunkName: "organization" */ '@/views/merchant/system/fund-supervision/IncomeAndExpenditureDetail'),
        name: 'MerchantIncomeAndExpenditureDetail',
        meta: {
          noCache: true,
          title: 'income_and_expenditure_detail',
          permission: ['background_fund_supervision.details_report.details_list']
        }
      }
    ]
  },
  {
    path: '/AI_forewarning',
    component: Layout,
    redirect: '/AI_forewarning/operation_warning',
    alwaysShow: true,
    name: 'MerchantAIForewarning',
    meta: {
      noCache: true,
      title: 'AI_forewarning',
      permission: ['ai_early_manage']
    },
    children: [
      {
        path: 'operation_warning',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/AI-forewarning/BusinessWarning'),
        name: 'MerchantOperationWarning',
        meta: {
          noCache: true,
          title: 'operation_warning',
          permission: ['early_warning']
        }
      }
    ]
  },
  {
    path: '/canteen_management',
    component: Layout,
    redirect: '/canteen_management/canteen_info',
    alwaysShow: true,
    name: 'MerchantCanteenManagement',
    meta: {
      noCache: true,
      title: 'canteen_management',
      permission: ['merchant_supervision']
    },
    children: [
      {
        path: 'canteen_info',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/canteen-management/CanteenInfo'),
        name: 'MerchantCanteenInfo',
        meta: {
          noCache: true,
          title: 'canteen_info',
          permission: ['merchant_supervision_canteen']
        }
      },
      {
        path: 'democratic_feedback',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/canteen-management/democratic-feedback/DemocraticFeedback'),
        name: 'MerchantDemocraticFeedback',
        meta: {
          noCache: true,
          title: 'democratic_feedback',
          permission: ['merchant_supervision_canteen']
        }
      },
      {
        path: 'survey_detail',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/canteen-management/democratic-feedback/SurveyDetailData'),
        name: 'MerchantSurveyDetailData',
        hidden: true,
        meta: {
          noCache: true,
          title: 'survey_detail_data',
          permission: ['merchant_supervision_canteen']
        }
      }
    ]
  },
  {
    path: '/property_admin',
    component: Layout,
    redirect: '/property_admin/property_info',
    alwaysShow: true,
    name: 'MerchantPropertyAdmin',
    meta: {
      noCache: true,
      title: 'property_admin',
      permission: ['asset']
    },
    children: [
      {
        path: 'property_info',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/property-admin/PropertyInfo'),
        name: 'MerchantPropertyInfo',
        meta: {
          noCache: true,
          title: 'property_info',
          permission: ['background_fund_supervision.asset.asset_info_list', 'background_fund_supervision.asset.asset_info_details_summary_list', 'background_fund_supervision.asset.asset_info_statistics_list']
        }
      },
      {
        path: 'debt_info',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/property-admin/DebtInfo'),
        name: 'MerchantDebtInfo',
        meta: {
          noCache: true,
          title: 'debt_info',
          permission: ['background_fund_supervision.asset.liability_info_list', 'background_fund_supervision.asset.repayment_info_list', 'background_fund_supervision.asset.liability_info_statistics_list']
        }
      }
    ]
  },
  {
    path: '/fund_management',
    component: Layout,
    redirect: '/fund_management/financial_application',
    alwaysShow: true,
    name: 'MerchantFundManagement',
    meta: {
      noCache: true,
      title: 'fund_management',
      permission: ['finance_supervision']
    },
    children: [
      {
        path: 'financial_application',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/fund-management/FinanicalApplication'),
        name: 'MerchantFinancialApplication',
        meta: {
          noCache: true,
          title: 'financial_application',
          permission: ['background_fund_supervision.finance_approve.list']
        }
      },
      {
        path: 'financial_approval',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/fund-management/FinancialApproval.vue'),
        name: 'MerchantFinancialApproval',
        meta: {
          noCache: true,
          title: 'financial_approval',
          permission: ['background_fund_supervision.appropriation.list']
        }
      }
    ]
  },
  {
    path: '/food_safety_management',
    component: Layout,
    redirect: '/food_safety_management/scheduling_management',
    alwaysShow: true,
    name: 'MerchantFoodSafetyManagement',
    meta: {
      noCache: true,
      title: 'food_safety_management',
      permission: ['merchant_supervision']
    },
    children: [
      {
        path: 'management_ledger',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/index.vue'),
        name: 'ManagementLedger',
        meta: {
          noCache: true,
          title: 'management_ledger',
          no_permission: true
        },
        children: [
          {
            path: 'fengxian_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/FengXianManagementLedger.vue'),
            name: 'ManagementLedger1',
            meta: {
              noCache: true,
              title: 'fengxian_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'congye_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/CongYeManagementLedger.vue'),
            name: 'ManagementLedger2',
            meta: {
              noCache: true,
              title: 'congye_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'shitang_shebei_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/ShitangShebeiManagementLedger.vue'),
            name: 'ShitangShebeiManagementLedger',
            meta: {
              noCache: true,
              title: 'shitang_shebei_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'chufang_chouyou_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/ChufangChouyouManagementLedger.vue'),
            name: 'ChufangChouyouManagementLedger',
            meta: {
              noCache: true,
              title: 'chufang_chouyou_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'shitang_daoju_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/ShitangDaojuManagementLedger.vue'),
            name: 'ShitangDaojuManagementLedger',
            meta: {
              noCache: true,
              title: 'shitang_daoju_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'shitang_caigou_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/ShitangCaigouManagementLedger.vue'),
            name: 'ShitangCaigouManagementLedger',
            meta: {
              noCache: true,
              title: 'shitang_caigou_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'danlei_qingxi_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/DanleiQingxiManagementLedger.vue'),
            name: 'DanleiQingxiManagementLedger',
            meta: {
              noCache: true,
              title: 'danlei_qingxi_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'shipin_tianjiaji_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/ShipinTianjiajiManagementLedger.vue'),
            name: 'ShipinTianjiajiManagementLedger',
            meta: {
              noCache: true,
              title: 'shipin_tianjiaji_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'guominyuan_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/GuominyuanManagementLedger.vue'),
            name: 'GuominyuanManagementLedger',
            meta: {
              noCache: true,
              title: 'guominyuan_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'bianzhi_shipin_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/BianzhiShipinManagementLedger.vue'),
            name: 'BianzhiShipinManagementLedger',
            meta: {
              noCache: true,
              title: 'bianzhi_shipin_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'xuncha_jilu_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/XunchaJiluManagementLedger.vue'),
            name: 'XunchaJiluManagementLedger',
            meta: {
              noCache: true,
              title: 'xuncha_jilu_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'shipin_zhongxin_wendu_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/ShipinZhongxinWenduManagementLedger.vue'),
            name: 'ShipinZhongxinWenduManagementLedger',
            meta: {
              noCache: true,
              title: 'shipin_zhongxin_wendu_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'shipin_liuyang_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/ShipinLiuyangManagementLedger.vue'),
            name: 'ShipinLiuyangManagementLedger',
            meta: {
              noCache: true,
              title: 'shipin_liuyang_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'peicanjian_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/PeicanjianManagementLedger.vue'),
            name: 'PeicanjianManagementLedger',
            meta: {
              noCache: true,
              title: 'peicanjian_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'chufang_canting_xiaodu_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/ChufangCantingXiaoduManagementLedger.vue'),
            name: 'ChufangCantingXiaoduManagementLedger',
            meta: {
              noCache: true,
              title: 'chufang_canting_xiaodu_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'chufang_gongju_xiaodu_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/ChufangGongjuXiaoduManagementLedger.vue'),
            name: 'ChufangGongjuXiaoduManagementLedger',
            meta: {
              noCache: true,
              title: 'chufang_gongju_xiaodu_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'canyinju_xiaodu_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/CanyinjuXiaoduManagementLedger.vue'),
            name: 'CanyinjuXiaoduManagementLedger',
            meta: {
              noCache: true,
              title: 'canyinju_xiaodu_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'xuexiao_shitang_peican_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/XuexiaoShitangPeicanManagementLedger.vue'),
            name: 'XuexiaoShitangPeicanManagementLedger',
            meta: {
              noCache: true,
              title: 'xuexiao_shitang_peican_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'canshu_laji_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/CanshuLajiManagementLedger.vue'),
            name: 'CanshuLajiManagementLedger',
            meta: {
              noCache: true,
              title: 'canshu_laji_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'youhang_shengwu_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/YouhangShengwuManagementLedger.vue'),
            name: 'YouhangShengwuManagementLedger',
            meta: {
              noCache: true,
              title: 'youhang_shengwu_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'congye_renyuan_peixun_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/CongyeRenyuanPeixunManagementLedger.vue'),
            name: 'CongyeRenyuanPeixunManagementLedger',
            meta: {
              noCache: true,
              title: 'congye_renyuan_peixun_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'xuexiao_shipin_anquan_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/XuexiaoShipinAnquanManagementLedger.vue'),
            name: 'XuexiaoShipinAnquanManagementLedger',
            meta: {
              noCache: true,
              title: 'xuexiao_shipin_anquan_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'xuexiao_shitang_pingjia_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/XuexiaoShitangPingjiaManagementLedger.vue'),
            name: 'XuexiaoShitangPingjiaManagementLedger',
            meta: {
              noCache: true,
              title: 'xuexiao_shitang_pingjia_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'xuexiao_huiyi_jiyao_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/XuexiaoHuiyiJiyaoManagementLedger.vue'),
            name: 'XuexiaoHuiyiJiyaoManagementLedger',
            meta: {
              noCache: true,
              title: 'xuexiao_huiyi_jiyao_management_ledger',
              no_permission: true
            }
          },
          {
            path: 'jiti_yongcan_management_ledger',
            component: () =>
              import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/baiyun-excel/JitiYongcanManagementLedger.vue'),
            name: 'JitiYongcanManagementLedger',
            meta: {
              noCache: true,
              title: 'jiti_yongcan_management_ledger',
              no_permission: true
            }
          }
        ]
      },
      {
        path: 'sample_record',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/food-safety-management/SampleRecord.vue'),
        name: 'MerchantSampleRecord',
        meta: {
          noCache: true,
          title: 'sample_record',
          permission: ['background_store.retention_record.food_reserved_sample_record']
        }
      },
      {
        path: 'scheduling_management',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/food-safety-management/WorkforceManagement.vue'),
        name: 'MerchantSchedulingManagement',
        meta: {
          noCache: true,
          title: 'scheduling_management',
          permission: ['background_fund_supervision.canteen_safety_management.get_person_schedule']
        }
      },
      {
        path: 'morning_check_log',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/food-safety-management/MorningInspectionRecord.vue'),
        name: 'MerchantMorningCheckLog',
        meta: {
          noCache: true,
          title: 'morning_check_log',
          permission: ['background_fund_supervision.canteen_safety_management.morning_check_record']
        }
      },
      {
        path: 'pest_control',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/food-safety-management/PestControl.vue'),
        name: 'MerchantPestControl',
        meta: {
          noCache: true,
          title: 'pest_control',
          permission: ['background_fund_supervision.canteen_safety_management.pest_control_record']
        }
      },
      {
        path: 'food_safety_roots',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/food-safety-management/FoodSafetyTraceability.vue'),
        name: 'MerchantFoodSafetyRoots',
        meta: {
          noCache: true,
          title: 'food_safety_roots',
          permission: ['background_fund_supervision.food_safety_source']
        }
      },
      {
        path: 'accompanying_meal_record',
        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/merchant/supervision-and-management/meal-management/AccompanyingMealRecord.vue'),
        name: 'AccompanyingMealRecord',
        meta: {
          noCache: true,
          title: 'accompanying_meal_record',
          permission: ['background_fund_supervision.channel_canteen_management.meal_accompanying_list']
        }
      },
      {
        path: 'daily_inspection',
        component: () =>
          import(/* webpackChunkName: "DailyInspection" */ '@/views/merchant/supervision-and-management/daily-inspection/DailyInspection.vue'),
        name: 'DailyInspection',
        meta: {
          noCache: true,
          title: 'daily_inspection',
          permission: ['background_fund_supervision.daily_patrol.list']
        }
      }
    ]
  },
  // 一级-其他
  {
    path: '/carManagement',
    component: Layout,
    redirect: '@/views/merchant/user-center/CarBinding',
    name: 'MerchantCarManagement',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'car_management',
      permission: ['background_car_travel']
    },
    children: [
      {
        path: 'carBinding',
        component: () =>
          import(
            /* webpackChunkName: "car_binding" */ '@/views/merchant/user-center/CarBinding'
          ),
        name: 'MerchantCarBinding',
        meta: {
          noCache: true,
          title: 'car_binding',
          permission: ['background_car_travel.car_travel_info.list']
        }
      },
      {
        path: 'trafficRecords',
        component: () =>
          import(
            /* webpackChunkName: "traffic_records" */ '@/views/merchant/user-center/TrafficRecords'
          ),
        name: 'MerchantTrafficRecords',
        meta: {
          noCache: true,
          title: 'traffic_records',
          permission: ['background_car_travel.car_pass_record.list']
        }
      },
      {
        path: 'trafficOrders',
        component: () =>
          import(
            /* webpackChunkName: "traffic_orders" */ '@/views/merchant/user-center/TrafficOrders'
          ),
        name: 'MerchantTrafficOrders',
        meta: {
          noCache: true,
          title: 'traffic_orders',
          permission: ['background_car_travel.order_pass_record.list']
        }
      }
    ]
  },
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/board_admin',
    alwaysShow: true,
    name: 'MerchantDashboard',
    meta: {
      noCache: true,
      title: 'board_admin',
      permission: ['background_datascreen.kanban_temp']
    },
    children: [
      {
        path: 'board_admin',
        component: () =>
          import(/* webpackChunkName: "board_admin" */ '@/views/merchant/dashboard/DataBoardAdmin'),
        name: 'MerchantDataBoardAdmin',
        meta: {
          noCache: true,
          title: 'large_data_screen',
          permission: ['background_datascreen.kanban_temp.list']
        }
      },
      {
        path: 'board_admin/:type',
        component: () =>
          import(/* webpackChunkName: "board_admin" */ '@/views/merchant/dashboard/AddDataBoard'),
        name: 'MerchantAddDataBoard',
        hidden: true,
        meta: {
          noCache: true,
          title: 'large_data_screen',
          activeMenu: '/dashboard/board_admin',
          permission: ['others']
        }
      }
    ]
  },
  {
    path: '/attendance',
    component: Layout,
    redirect: '/attendance/attendance_group_admin',
    alwaysShow: true,
    name: 'MerchantAttendance',
    meta: {
      noCache: true,
      title: 'attendance',
      permission: ['attendance_record.list']
    },
    children: [
      // {
      //   path: 'push_setting_admin',
      //   component: () =>
      //     import(/* webpackChunkName: "push_setting_admin" */ '@/views/merchant/accessControl-attendance/attendance/PushSettingAdmin'),
      //   name: 'PushSettingAdmin',
      //   meta: {
      //     noCache: true,
      //     title: 'push_setting_admin',
      //     no_permission: true
      //   }
      // },
      // {
      //   path: 'push_setting/:type',
      //   component: () =>
      //     import(/* webpackChunkName: "push_setting" */ '@/views/merchant/accessControl-attendance/attendance/AddOrEditPushSetting'),
      //   name: 'AddOrEditPushSetting',
      //   hidden: true,
      //   meta: {
      //     noCache: true,
      //     title: 'push_setting',
      //     activeMenu: '/attendance/push_setting_admin',
      //     no_permission: true
      //   }
      // },
      // 考情组管理
      {
        path: 'attendance_group_admin',
        component: () =>
          import(
            /* webpackChunkName: "attendance_group_admin" */ '@/views/merchant/accessControl-attendance/attendance/AttendanceGroupManage'
          ),
        name: 'MerchantAttendanceGroupManage',
        meta: {
          noCache: true,
          title: 'attendance_group_admin',
          permission: ['background_attendance.group', 'background_attendance.group.list', 'background_attendance.attendance_group_admin.list']
        }
      },
      {
        path: 'attendance_setting',
        component: () =>
          import(
            /* webpackChunkName: "attendance_setting" */ '@/views/merchant/accessControl-attendance/attendance/AttendanceSetting'
          ),
        name: 'MerchantAttendanceSetting',
        meta: {
          noCache: true,
          title: 'attendance_setting',
          permission: ['background_attendance.settings.list']
        }
      },
      {
        path: 'attendance_setting/:type',
        component: () =>
          import(
            /* webpackChunkName: "attendance_setting" */ '@/views/merchant/accessControl-attendance/attendance/AddOrEditAttendanceSetting'
          ),
        name: 'MerchantAddOrEditAttendanceSetting',
        hidden: true,
        meta: {
          noCache: true,
          title: 'attendance_setting',
          activeMenu: '/attendance/attendance_setting',
          permission: ['background_attendance.settings.add', 'background_attendance.settings.modify']
        }
      },
      {
        path: 'person_attendance_report',
        component: () =>
          import(
            /* webpackChunkName: "person_attendance_report" */ '@/views/merchant/accessControl-attendance/attendance/PersonAttendanceReport'
          ),
        name: 'MerchantPersonAttendanceReport',
        meta: {
          noCache: true,
          title: 'person_attendance_report',
          permission: ['background_attendance.attendance_record_details.card_user_punch_status_count']
        }
      },
      {
        path: 'attendance_record_detail',
        component: () =>
          import(
            /* webpackChunkName: "attendance_record_detail" */ '@/views/merchant/accessControl-attendance/attendance/AttendanceRecordDetail'
          ),
        name: 'MerchantAttendanceRecordDetail',
        meta: {
          noCache: true,
          title: 'attendance_record_detail',
          permission: ['background_attendance.record_details.list']
        }
      },
      {
        path: 'attendance_record',
        component: () =>
          import(
            /* webpackChunkName: "attendance_record" */ '@/views/merchant/accessControl-attendance/attendance/AttendanceRecord'
          ),
        name: 'MerchantAttendanceRecord',
        meta: {
          noCache: true,
          title: 'attendance_record',
          permission: ['background_attendance.attendance_record.list']
        }
      },
      {
        path: 'miss_card_record',
        component: () =>
          import(
            /* webpackChunkName: "miss_card_record" */ '@/views/merchant/accessControl-attendance/attendance/MissCardRecord'
          ),
        name: 'MerchantMissCardRecord',
        meta: {
          noCache: true,
          title: 'miss_card_record',
          permission: ['background_attendance.absence_work_record_details.list']
        }
      }
    ]
  },
  {
    path: '/access_control',
    component: Layout,
    redirect: '/access_control/access_control_setting',
    alwaysShow: true,
    name: 'MerchantAccessControl',
    meta: {
      noCache: true,
      title: 'access_control',
      permission: ['background_attendance.access_control_settings']
    },
    children: [
      {
        path: 'access_control_setting',
        component: () =>
          import(/* webpackChunkName: "access_control_setting" */ '@/views/merchant/accessControl-attendance/access-control/AccessControlSetting'),
        name: 'MerchantAccessControlSetting',
        meta: {
          noCache: true,
          title: 'access_control_setting',
          permission: ['background_attendance.access_control_settings.list']
        }
      },
      {
        path: 'access_control_setting/:type',
        component: () =>
          import(/* webpackChunkName: "access_control_setting" */ '@/views/merchant/accessControl-attendance/access-control/AddOrEditControlSetting'),
        name: 'MerchantAddOrEditControlSetting',
        hidden: true,
        meta: {
          noCache: true,
          title: 'access_control_setting',
          activeMenu: '/access_control/access_control_setting',
          permission: ['background_attendance.access_control_settings.add', 'background_attendance.access_control_settings.modify']
        }
      },
      {
        path: 'through_statistics',
        component: () =>
          import(/* webpackChunkName: "through_statistics" */ '@/views/merchant/accessControl-attendance/access-control/ThroughStatistics'),
        name: 'MerchantThroughStatistics',
        meta: {
          noCache: true,
          title: 'through_statistics',
          permission: ['background_attendance.access_control_record.org_punch_status_count']
        }
      },
      {
        path: 'through_record',
        component: () =>
          import(/* webpackChunkName: "through_record" */ '@/views/merchant/accessControl-attendance/access-control/ThroughRecord'),
        name: 'MerchantThroughRecord',
        meta: {
          noCache: true,
          title: 'through_record',
          permission: ['background_attendance.access_control_record.list']
        }
      }
    ]
  },
  // {
  //   path: '/consumption-rules/coupon',
  //   component: Layout,
  //   alwaysShow: true,
  //   name: 'merchant_system',
  //   meta: {
  //     title: 'discount_coupon',
  //     permission: ['marketing_activities']
  //   },
  //   children: [
  //     {
  //       path: '',
  //       name: 'MerchantDiscountCoupon',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "discount_coupon" */ '@/views/merchant/consumption-rules/coupon/DiscountCoupon'
  //         ),
  //       meta: {
  //         noCache: true,
  //         title: 'discount_coupon',
  //         permission: ['marketing_activities']
  //       }
  //     },
  //     {
  //       path: 'discount/:type',
  //       name: 'MerchantAddDiscountCoupon',
  //       hidden: true,
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "add_discount_coupon" */ '@/views/merchant/consumption-rules/coupon/AddDiscountCoupon'
  //         ),
  //       meta: {
  //         noCache: true,
  //         title: 'discount_coupon',
  //         activeMenu: '/consumption-rules/coupon',
  //         permission: ['marketing_activities']
  //       }
  //     },
  //     {
  //       path: 'discount_coupon_statistics',
  //       name: 'MerchantDiscountCouponStatistics',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "discount_coupon_statistics" */ '@/views/merchant/consumption-rules/coupon/DiscountCouponStatistics'
  //         ),
  //       meta: {
  //         noCache: true,
  //         title: 'discount_coupon_statistics',
  //         permission: ['marketing_activities']
  //       }
  //     }
  //   ]
  // },
  // 杂
  {
    path: '/homechannel',
    component: Layout,
    redirect: '/homechannel/home',
    alwaysShow: false,
    name: 'home',
    meta: {
      title: 'home',
      permission: ['homechannel']
    },
    children: [
      {
        path: 'home',
        component: () =>
          import(/* webpackChunkName: "homechannel" */ '@/views/merchant/channel-manage/ChannelHomePage'),
        name: 'ChannelHomePage',
        meta: {
          noCache: true,
          title: 'home',
          permission: ['background_channel.data_statistics.homepage_data']
        }
      }
    ]
  },
  {
    path: '/channel-manage',
    component: Layout,
    redirect: '/channel-manage/statistics',
    alwaysShow: true,
    name: 'channelManage',
    meta: {
      title: 'super_merchant_channel',
      permission: ['channel-manage']
    },
    children: [
      {
        path: 'statistics',
        component: () =>
          import(/* webpackChunkName: "homechannel" */ '@/views/merchant/channel-manage/ChannelStatistics'),
        name: 'ChannelStatistics',
        meta: {
          noCache: true,
          title: 'channel_statistics',
          permission: ['background_channel.data_statistics.list']
        }
      }
    ]
  },
  {
    path: '/consumption_auto_selling_print',
    component: () =>
      import(/* webpackChunkName: "ConsumptionAutoSelling" */ '@/views/merchant/order/ConsumptionAutoSellingPrint'),
    name: 'ConsumptionAutoSellingPrint',
    hidden: true,
    meta: {
      noCache: true,
      title: 'consumption_auto_selling',
      no_permission: true
    }
  }
  // {
  //   path: 'distribution',
  //   name: 'Distribution',
  //   alwaysShow: true,
  //   meta: {
  //     title: 'distribution',
  //     noCache: true,
  //     permission: ['meal_management']
  //   },
  //   children: [
  //     {
  //       path: 'getMealReport',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/distribution/GetMealReport'
  //         ),
  //       name: 'GetMealReport',
  //       meta: {
  //         noCache: true,
  //         title: 'get_meal_report',
  //         permission: ['meal_management']
  //       }
  //     },
  //     {
  //       path: 'transverseReport',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/distribution/TransverseReport'
  //         ),
  //       name: 'TransverseReport',
  //       meta: {
  //         noCache: true,
  //         title: 'transverse_report',
  //         permission: ['meal_management']
  //       }
  //     },
  //     {
  //       path: 'verticalReport',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/distribution/VerticalReport'
  //         ),
  //       name: 'VerticalReport',
  //       meta: {
  //         noCache: true,
  //         title: 'vertical_report',
  //         permission: ['meal_management']
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: 'on_scene',
  //   name: 'OnScene',
  //   alwaysShow: true,
  //   meta: {
  //     title: 'on_scene',
  //     noCache: true,
  //     permission: ['meal_management']
  //   },
  //   children: [
  //     {
  //       path: 'on_scene_admin',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "on_scene" */ '@/views/merchant/meal-management/on-scene-setting/OnSceneAdmin'
  //         ),
  //       name: 'OnSceneAdmin',
  //       meta: {
  //         noCache: true,
  //         title: 'on_scene',
  //         permission: ['meal_management']
  //       }
  //     },
  //     {
  //       path: 'on_scene/:type',
  //       name: 'AddOrEditOnScene',
  //       hidden: true,
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "add_or_edit_on_scene" */ '@/views/merchant/meal-management/on-scene-setting/AddOrEditOnScene'
  //         ),
  //       meta: {
  //         activeMenu: '/meal_management/on_scene/on_scene_admin',
  //         title: 'on_scene',
  //         permission: ['meal_management']
  //       }
  //     },
  //     {
  //       path: 'tableAdmin',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/on-scene-setting/TableAdmin'
  //         ),
  //       name: 'TableAdmin',
  //       meta: {
  //         noCache: true,
  //         title: 'table_admin',
  //         permission: ['meal_management']
  //       }
  //     }
  //   ]
  // }
]
export default merchant
