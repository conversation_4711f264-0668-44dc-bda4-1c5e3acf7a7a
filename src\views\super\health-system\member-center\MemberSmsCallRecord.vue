<template>
  <div class="sms-manager container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!--搜索层-->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler"
      @reset="resetHandler"></search-form>
    <!--表格-->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="showRecordDialog('send', row)"
            v-if="tabType === 'record'">手动发放</button-icon>
          <el-button class="ps-origin-plain-btn h-26" @click="goToMemberSetting()"
            v-if="tabType === 'setting'">功能配置</el-button>
          <button-icon color="origin" @click="showRecordDialog('add')" v-if="tabType === 'setting'">新增</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" height="460" stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #name="{ row }">
              {{ row.name || '--' }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text-blue"
                @click="showRecordDialog('edit', row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-text-blue" @click="handlerDeleteRecord(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- table content end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination @size-change="handlerSizeChange" @current-change="handlerPageChange" :current-page.sync="currentPage"
          :page-size.sync="pageSize" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount"></pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>
<script>
import { SEARCH_FORM_SMS_MANAGER_DATA, TABLE_HEAD_SEND_DATA } from './constants.js'
import { debounce, deepClone, to } from '@/utils'

export default {
  name: 'MemberSmsCallRecord',
  data() {
    return {
      searchFormSetting: deepClone(SEARCH_FORM_SMS_MANAGER_DATA),
      tableSettings: deepClone(TABLE_HEAD_SEND_DATA),
      collect: [
        { key: 'all', value: 0, label: '总发送条数：', unit: '条' },
        { key: 'success_count', value: 0, label: '成功数：', unit: '条' },
        { key: 'not_success_count', value: 0, label: '失败数：', unit: '条' }
      ],
      dialogVisible: false, // 弹窗是否可见
      isLoading: false, // 表格是否加载
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页数量
      totalCount: 0,
      tableData: [], // 表单数据
      orgsList: [] // 项目点列表
    }
  },
  created() {
    this.initData();
  },
  methods: {
    // 初始化数据
    initData() {
      this.getOrgList()
      this.getDataList()
    },
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initData()
    },
    // 节下流咯
    searchHandler: debounce(function () {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getDataList()
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.currentPage = 1;
      this.getDataList()
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    handlerPageChange(val) {
      console.log("handlerPageChange", val);
      this.currentPage = val
      this.getDataList()
    },
    /**
     * 页面条数改变监听
     */
    handlerSizeChange(val) {
      console.log("handlerSizeChange", val);
      this.pageSize = val
      this.getDataList()
    },
    /**
     * 获取二级商户列表
     */
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundMemberSmsPushReceiveListPost(params))
      console.log("apiBackgroundSubMerchantInfoList", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        var resultList = res.data.results || []
        if (Array.isArray(resultList) && resultList.length > 0) {
          // 给列表增加序号
          resultList.map((item, index) => {
            item.index = index + 1
            return item
          })
        }
        this.tableData = deepClone(resultList)
        var summaryData = res.data.summary_data || {}
        if (summaryData) {
          var collect = deepClone(this.collect)
          collect.forEach(item => {
            if (Reflect.has(summaryData, item.key)) {
              item.value = summaryData[item.key]
            }
            if (item.key === 'all') {
              item.value = (summaryData.success_count ? summaryData.success_count : 0) + (summaryData.not_success_count ? summaryData.not_success_count : 0)
            }
          })
        }
        this.collect = deepClone(collect)
        this.totalCount = res.data.count || -1
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'select_time') {
            params.start_date = data[key].value[0] || ''
            params.end_date = data[key].value[1] || ''
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    // 获取项目点
    async getOrgList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminMarketingPopupGetOrgsPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log(res)
      if (res && res.code === 0) {
        this.orgsList = res.data || []
        if (this.orgsList) {
          this.searchFormSetting.organization_id.dataList = deepClone(this.orgsList)
        }
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.sms-manager {}
</style>
