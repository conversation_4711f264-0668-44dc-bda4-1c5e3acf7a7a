<template>
  <div class="channel-info-content">
    <el-form :label-position="'left'" label-width="100px">
      <el-form-item label="数据大屏：">
        <div class="table-style">
          <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #status="{ row }">
                <el-switch v-model="row.isUsing" @change="changeStatus(row)" />
              </template>
            </table-column>
          </el-table>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
export default {
  props: {
    organizationData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    tabType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '大屏名称', key: 'name' },
        { label: '地址', key: 'link_address' },
        { label: '备注', key: 'remark' },
        { label: '操作时间', key: 'update_time' },
        { label: '操作人', key: 'operator' },
        { label: '使用状态（启用/禁用）', key: 'status', type: "slot", slotName: "status" }
      ],
      drawerType: ''
    }
  },
  watch: {
    tabType: {
      handler: function(newVal, oldVal) {
        if (newVal === 'functionConfiguration') {
          console.log('切换了')
          this.getDataList()
        }
      },
      immediate: true
    }
  },
  methods: {
    getDataList() {
      this.isLoading = true
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelScreenListPost({
        supervision_channel_id: this.organizationData.id
      }).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          let arr = []
          if (res.data.results && res.data.results.length) {
            arr = res.data.results.map(item => {
              Object.assign(item, { isUsing: false })
              if (item.status === 'enable') {
                item.isUsing = true
              } else {
                item.isUsing = false
              }
              return item
            })
          }
          this.tableData = deepClone(arr || [])
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    changeStatus(data) {
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelScreenModifyPost({
        id: data.id,
        status: data.isUsing ? 'enable' : 'disable'
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
        }
        this.getDataList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.channel-info-content {
  padding: 10px;
}
</style>
