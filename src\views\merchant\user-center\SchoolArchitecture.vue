<template>
  <div class="container-wrapper p-t-20">
    <div class="table-type">
      <el-button :class="[type === index ? 'ps-origin-btn' : '']" v-for="(item, index) in buttonList" :key="index" @click="changeType(index)">{{ item.name }}</el-button>
    </div>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="getList">刷新数据</button-icon>
          <button-icon color="origin" @click="synchronizationHandle">同步架构</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="type === 0 ? studentArchitectureData : teacherArchitectureData"
          style="width: 100%"
          header-row-class-name="ps-table-header-row"
          :row-key="getRowKey"
          stripe
          lazy
          :load="type === 0 ? loadStudentArchitecture : loadTeacherArchitecture"
          :tree-props="{children: 'childrenList', hasChildren: 'has_children'}"
        >
          <table-column v-for="(item, index) in type === 0 ? studentArchitectureTableSetting : teacherArchitectureTableSetting" :key="index" :col="item">
          </table-column>
        </el-table>
      </div>
    </div>

  </div>
</template>

<script>
import { deepClone, to } from '@/utils';

export default {
  data() {
    return {
      type: 0,
      buttonList: [
        {
          name: '学生架构'
        },
        {
          name: '教职工架构'
        }
      ],
      isLoading: false,
      studentArchitectureData: [],
      teacherArchitectureData: [],
      studentArchitectureTableSetting: [
        {
          label: '架构名称',
          key: 'org_name',
          align: 'left'
        },
        {
          label: '架构ID',
          key: 'org_id',
          align: 'left'
        }
      ],
      teacherArchitectureTableSetting: [
        {
          label: '架构名称',
          key: 'dept_name',
          align: 'left'
        },
        {
          label: '架构ID',
          key: 'dept_id',
          align: 'left'
        }
      ]
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.studentArchitectureData = []
      this.teacherArchitectureData = []
      this.page = 1
      this.$nextTick(() => {
        switch (this.type) {
          case 0:
            this.getStudentArchitecture(true, undefined)
            break;
          case 1:
            this.getTeacherArchitecture(true, undefined)
            break;
        }
      })
    },
    getStudentArchitecture(isRoot, tree) {
      this.isLoading = true
      let params = {
        page: 1,
        page_size: 99999999
      }
      if (isRoot) {
        params.level = 0
        params.parent_is_null = true
      } else {
        params.parent_is_null = false
        params.parent_id = tree.data.id
      }
      this.$apis.apiCardServiceThirdCardUserYideStudentOrgListPost(params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          let obj = res.data.results.map(item => {
            item.childrenList = []
            return item
          })
          this.studentArchitectureData = deepClone(obj)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getTeacherArchitecture(isRoot, tree) {
      this.isLoading = true
      let params = {
        page: 1,
        page_size: 99999999
      }
      if (isRoot) {
        params.level = 0
        params.parent_is_null = true
      } else {
        params.parent_is_null = false
        params.parent_id = tree.data.id
      }
      this.$apis.apiCardServiceThirdCardUserYideSchoolDeptListPost(params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.teacherArchitectureData = deepClone(res.data.results)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    changeType(index) {
      this.type = index
      this.getList()
    },
    async loadStudentArchitecture(tree, treeNode, resolve) {
      let params = {
        page: 1,
        page_size: 99999999,
        parent_is_null: false,
        parent_id: tree.id
      }
      const [err, res] = await to(this.$apis.apiCardServiceThirdCardUserYideStudentOrgListPost(params));
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(item => {
          if (item.has_children) {
            item.is_leaf = false
          } else {
            item.is_leaf = true
          }
        })
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    async loadTeacherArchitecture(tree, treeNode, resolve) {
      let params = {
        page: 1,
        page_size: 99999999,
        parent_is_null: false,
        parent_id: tree.id
      }
      const [err, res] = await to(this.$apis.apiCardServiceThirdCardUserYideSchoolDeptListPost(params));
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(item => {
          if (item.has_children) {
            item.is_leaf = false
          } else {
            item.is_leaf = true
          }
        })
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    getRowKey(row) {
      // 组合多个字段生成唯一键值
      return `${this.type}-${row.id}`
    },
    synchronizationHandle() {
      switch (this.type) {
        case 0:
          this.refreshStudentArchitecture()
          break;
        case 1:
          this.refreshTeacherArchitecture()
          break;
      }
    },
    refreshStudentArchitecture() {
      this.$apis.apiCardServiceThirdCardUserYideStudentOrgCreatePost().then(res => {
        if (res.code === 0) {
          this.$message.success('同步成功')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    refreshTeacherArchitecture() {
      this.$apis.apiCardServiceThirdCardUserYideSchoolDeptCreatePost().then(res => {
        if (res.code === 0) {
          this.$message.success('同步成功')
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
