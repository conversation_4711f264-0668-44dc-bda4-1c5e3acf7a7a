<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="dialogForm"
      ref="dialogForm"
      :rules="dialogFormRules"
      label-width="120px"
    >
      <div v-if="type==='add' || type==='mulAdd' || type==='edit'">
        <el-form-item :label="type==='mulAdd'?'名称前缀：':'名称：'" prop="name">
          <el-input v-model="dialogForm.name" placeholder="请输入名称" class="ps-input w-250"></el-input>
        </el-form-item>

        <el-form-item v-if="type==='mulAdd'" label="起始序号：" prop="number">
          <el-form-item class="inline-box" prop="numberStart">
            <el-input v-model="dialogForm.numberStart" placeholder="开始序号" class="ps-input w-100"></el-input>
          </el-form-item>
          <span style="margin: 0 18px;">至</span>
          <el-form-item class="inline-box" prop="numberEnd">
            <el-input v-model="dialogForm.numberEnd" placeholder="结束序号" class="ps-input w-100"></el-input>
          </el-form-item>
        </el-form-item>

        <el-form-item label="标准就餐人数：" prop="count">
          <el-input v-model="dialogForm.count" placeholder="请输入标准就餐人数" class="ps-input w-250"></el-input>
        </el-form-item>

        <el-form-item label="适用消费点：" prop="remark">
          <organization-select
            class="ps-input w-250"
            placeholder="请选择适用消费点："
            :isLazy="false"
            :multiple="true"
            :check-strictly="true"
            v-model="dialogForm.org"
            :append-to-body="true"
            >
          </organization-select>
        </el-form-item>
        <div v-if="type==='mulAdd'">例：桌台名称前缀为小桌，序号为1-4，可用餐人数为4，将批量添加4个4人桌，桌台名称分别为：小桌1、小桌2、小桌3、小桌4</div>
      </div>
      <div class="preview-code" v-if="type === 'code'">
        <img src="https://gdghospital.packertec.com/api/temporary/2021072623064225017910001.jpg" alt="">
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">取消</el-button>
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">确定</el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import OrganizationSelect from '@/components/OrganizationSelect'
export default {
  name: 'accountDialog',
  components: {
    OrganizationSelect
  },
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '500px'
    },
    isshow: Boolean,
    confirm: Function
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      time: new Date().getTime(),
      dialogForm: {
        org: [],
        name: '',
        count: '',
        numberStart: '',
        numberEnd: ''
      },
      dialogFormRules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
    },
    clickConfirmHandle(isWithdrawal) {
      console.log(this.dialogForm.treeData)
      this.$refs.dialogForm.validate(valid => {
        if (valid) {
          // let params = {}
          // let api
          switch (this.type) {
            case 'add':
              // api = this.$apis.apiCardServiceCardOperateRechargePost(params)
              break;
          }
          // this.confirmOperate(params, api)
        } else {
        }
      })
    },
    async confirmOperate(params, api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.dialogForm.resetFields()
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
.inline-box{
  display: inline-block;
}
.preview-code{
  width: 360px;
  margin: auto;
  text-align: center;
  img{
    width: 360px;
  }
}
</style>
