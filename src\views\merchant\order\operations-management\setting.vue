<template>
  <div class="operation-setting m-t-20">
    <div class="setting-title blob">评价内容设置</div>
    <div class="setting-content" v-loading="isLoading">
      <div class="m-t-10 m-b-10">
        <span class="before-line">星级设置</span>
        <div class="rate-item-box m-l-20">
          <template v-for="(rate, index) in formData.start_score_field">
            <div class="rate-item m-t-20 m-r-60 inline" :key="index">
              <el-rate class="rate" v-model="rate.score" disabled></el-rate>
              <span class="rate-label">{{ rate.content }}<i class="el-icon-edit pointer" @click="openDialog('start_score_field', index)"></i></span>
            </div>
            <br v-if="index > 0 && (index/2) === 0" :key="index" />
          </template>
        </div>
      </div>
      <div class="m-t-30 m-b-10">
        <span class="before-line">评价字段设置</span>
        <div class="evaluate-box m-l-20 m-t-10">
          <div class="evaluate-text-item inline m-r-20 " v-for="(score, index) in formData.evaluation_score_field" :key="index">
            {{ score }}
            <i class="el-icon-edit pointer" @click="openDialog('evaluation_score_field', index)"></i>
            <i class="el-icon-error fix-error pointer" @click="deleteHandle('evaluation_score_field', index)"></i>
          </div>
          <i class="el-icon-circle-plus-outline pointer" @click="addScoreHandle('evaluation_score_field', 'add')"></i>
        </div>
      </div>
      <div class="m-t-30 m-b-10">
        <span class="before-line">便捷标签设置</span>
        <div class="evaluate-box m-l-20 m-t-10">
          <div class="evaluate-text-item inline m-r-20" v-for="(score, index) in formData.evaluation_content_field" :key="index">
            {{ score }}
            <i class="el-icon-edit pointer" @click="openDialog('evaluation_content_field', index)"></i>
            <i class="el-icon-error fix-error pointer" @click="deleteHandle('evaluation_content_field', index)"></i>
          </div>
          <i class="el-icon-circle-plus-outline pointer" @click="addScoreHandle('evaluation_content_field', 'add')"></i>
        </div>
      </div>
      <el-divider class="line"></el-divider>
      <div class="">
        <div class="setting-title blob">评价时间设置</div>
        <el-form
          ref="settingFormRef"
          :rules="formDataRuls"
          :model="formData"
          label-width="0px"
          size="small"
          class="m-l-30 m-t-20"
        >
          <el-form-item prop="on_scene" label="">
            <span class="m-r-10">堂食订单：订单支付完成后</span>
            <el-input class="ps-input" style="width: 230px;" v-model="formData.on_scene" placeholder=""></el-input>
            <span class="m-l-10">天内</span>
          </el-form-item>
          <el-form-item prop="reservation_order" label="">
            <span class="m-r-10">预约订单评价时间：</span>
            <el-select
              class="w-150 ps-select"
              v-model="formData.reservation_order_evaluate_type"
            >
              <el-option label="核销后" value="release_end"></el-option>
              <el-option label="支付后" value="pay_end"></el-option>
              <el-option label="餐段结束后" value="meal_end"></el-option>
            </el-select>
            <el-input class="ps-input" style="width: 230px;" v-model="formData.reservation_order" placeholder=""></el-input>
            <span class="m-l-10">天内</span>
          </el-form-item>
        </el-form>
      </div>
      <el-divider class="line"></el-divider>
      <div class="">
        <div class="setting-title blob">匿名设置</div>
        <div class="evaluate-box m-l-20 m-t-10">
          <span class="m-l-10 m-r-10">匿名</span>
          <el-switch class="ps-switch" active-color="#ff9b45" v-model="formData.anonymous"></el-switch>
          <div class="m-l-10 m-t-10" style="font-size: 14px;">开启时，用户在对订单、食堂建议、食堂投诉时可选择匿名提交</div>
        </div>
        <div class="m-t-20">
          <el-button class="ps-origin-btn" style="width: 150px;" @click="validateSetting">保存</el-button>
        </div>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="showDialog"
      width="500px"
      custom-class="ps-dialog"
      @closed="closeDialog"
    >
      <div class="dialog-box">
        <el-form
          ref="dialogFormRef"
          :rules="dialogFormRuls"
          :model="dialogForm"
          label-width="0px"
          size="small"
          class="m-l-30 m-t-20"
        >
          <el-form-item prop="text" label="">
            <span v-if="dialogType === 'start_score_field'" class="m-r-10">星级名称：</span>
            <el-input class="ps-input" style="width: 260px;" maxlength="5" v-model="dialogForm.text" placeholder=""></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="closeDialog">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="submitHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { to } from '@/utils'

export default {
  name: '',
  components: {

  },
  props: {

  },
  data() {
    let validateNumber = (rule, value, callback) => {
      let reg = /^[0-9]$/
      if (!reg.test(value)) {
        callback(new Error('格式不正确'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      formData: {
        start_score_field: [
          { score: 5, content: '非常好' },
          { score: 4, content: '好' },
          { score: 3, content: '较好' },
          { score: 2, content: '一般' },
          { score: 1, content: '差' }
        ],
        evaluation_score_field: [],
        evaluation_content_field: [],
        on_scene: '',
        reservation_order_evaluate_type: '',
        reservation_order: '',
        anonymous: false
      },
      formDataRuls: {
        on_scene: [
          { validator: validateNumber, trigger: "blur" }
        ],
        reservation_order: [
          { validator: validateNumber, trigger: "blur" }
        ]
      },
      dialogTitle: '', // 弹窗title
      showDialog: false,
      dialogType: '', // 弹窗类型
      dialogOperate: '', // 弹窗操作类型， 默认是修改modify，add添加
      dialogForm: { // 弹窗form
        text: ''
      },
      dialogFormRuls: { // 校验规则
        text: [
          { required: true, message: '不能为空', trigger: "blur" }
        ]
      },
      dialogData: '' // 弹窗内容，当前主要用于保存index
    }
  },
  computed: {
  },
  watch: {

  },
  created() {
    this.getOperationData()
  },
  mounted() {

  },
  methods: {
    async getOperationData() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOperationManagementEvaluationSettingListPost()
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let rateList = Object.keys(res.data.start_score_field).sort((a, b) => { return b - a })
        this.formData.start_score_field = rateList.map((item, i) => {
          return {
            score: Number(item),
            content: res.data.start_score_field[item]
          }
        })
        this.formData.evaluation_score_field = res.data.evaluation_score_field ? res.data.evaluation_score_field : []
        this.formData.evaluation_content_field = res.data.evaluation_content_field ? res.data.evaluation_content_field : []
        this.formData.reservation_order_evaluate_type = res.data.reservation_order_evaluate_type
        this.formData.reservation_order = res.data.reservation_order
        this.formData.on_scene = res.data.on_scene
        this.formData.anonymous = !!res.data.anonymous
      } else {
        this.$message.error(res.msg)
      }
    },
    openDialog(type, index) {
      this.dialogType = type
      this.dialogData = index
      this.dialogTitle = '编辑'
      switch (type) {
        case 'start_score_field':
          this.dialogForm.text = this.formData[type][index].content
          break;
        default:
          this.dialogForm.text = this.formData[type][index]
          break;
      }
      this.showDialog = true
    },
    closeDialog() {
      this.dialogOperate = ''
      this.dialogForm.text = ''
      this.showDialog = false
    },
    // dialog 确定
    submitHandle() {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          if (this.dialogOperate !== 'add') { // 修改赋值
            switch (this.dialogType) {
              case 'start_score_field':
                this.formData[this.dialogType][this.dialogData].content = this.dialogForm.text
                break;
              default:
                this.formData[this.dialogType][this.dialogData] = this.dialogForm.text
                break;
            }
          } else {
            this.formData[this.dialogType].push(this.dialogForm.text)
          }
          this.showDialog = false
        }
      })
    },
    // 添加
    addScoreHandle(type, operate) {
      this.dialogType = type
      this.dialogOperate = operate
      switch (type) {
        case 'evaluation_score_field':
          this.dialogTitle = '添加评价字段'
          break;
        case 'evaluation_content_field':
          this.dialogTitle = '添加便捷标签'
          break;
      }
      this.showDialog = true
    },
    // 删除操作
    deleteHandle(type, index) {
      this.formData[type].splice(index, 1)
    },
    // 评价设置
    validateSetting() {
      this.$refs.settingFormRef.validate(valid => {
        if (valid) {
          if (this.isLoading) return;
          this.getAppealPendList()
        }
      })
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key] !== '') {
          if (data[key] instanceof Array) {
            if (data[key].length > 0) {
              params[key] = data[key]
            }
          } else if (key === 'anonymous') {
            params[key] = data[key] ? 1 : 0
          } else {
            params[key] = data[key]
          }
        }
      }
      return params
    },
    async getAppealPendList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOperationManagementEvaluationSettingModifyPost(this.formatQueryParams(this.formData)))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('成功')
        this.getOperationData()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
};
</script>

<style scoped lang="scss">
.operation-setting{
  padding: 20px;
  box-shadow: 6px 6px 10px 0px rgb(202 210 221 / 30%), inset 2px 2px 0px 0px #ffffff;
  border-radius: 12px;
  overflow: hidden;
  background-color: #f8f9fa;
  .setting-title{
    font-size: 20px;
    font-weight: 600;
  }
  .inline{
    display: inline-block;
  }
  .line{
    margin: 35px 0;
  }
  .rate-item-box{
    .rate-label{
      vertical-align: middle;
    }
    .rate-item{
      width: 380px;
    }
    .rate{
      display: inline-block;
      ::v-deep .el-rate__icon{
        font-size: 46px;
      }
    }
  }
  .el-icon-circle-plus-outline{
    font-size: 25px;
    vertical-align: middle;
    color: #ff9b45;
  }
  .el-icon-edit{
    cursor: pointer;
  }
  .evaluate-box{
    .evaluate-text-item{
      position: relative;
      padding: 5px 10px;
      border: 1px solid #d7d7d7;
      border-radius: 10px;
      .fix-error{
        position: absolute;
        top: -5px;
        right: -5px;
        color: red;
      }
    }
  }
}
</style>
