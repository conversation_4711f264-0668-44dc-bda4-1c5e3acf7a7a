<template>
  <div class="face-synchronization-history container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="update_time" min-width="85px" label="同步时间" align="center" ></el-table-column>
          <el-table-column prop="org_name" label="所属组织" align="center" ></el-table-column>
          <el-table-column prop="device_name" label="设备名" align="center" width="120"></el-table-column>
          <el-table-column prop="device_type_alias" label="设备型号" align="center" ></el-table-column>
          <el-table-column prop="device_mac" label="设备地址" align="center" ></el-table-column>
          <el-table-column prop="count" label="本日同步次数" align="center" ></el-table-column>
          <el-table-column prop="update_time" label="最新一次同步时间" align="center" ></el-table-column>
          <el-table-column prop="mqtt_status" label="最新一次同步状态" align="center" ></el-table-column>
          <el-table-column prop="sync_type_alias" label="类型" align="center" ></el-table-column>
          <el-table-column prop="operator" label="操作人" align="center" ></el-table-column>
        </el-table>
      </div>
      <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import dayjs from 'dayjs'

export default {
  name: 'FaceSynchronizationHistory',
  mixins: [exportExcel, report], // activatedLoadData
  data() {
    return {
      isLoading: false, // 请求数据loading
      // 搜索
      searchFormSetting: {
        select_date: {
          timeRange: true,
          type: 'daterange',
          label: '同步时间',
          clearable: true,
          value: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        },
        org_ids: {
          type: 'organizationSelect',
          value: [],
          label: '所属组织',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        device_type: {
          type: 'select',
          label: '设备类型',
          value: '',
          placeholder: '请选择设备类型',
          multiple: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        },
        mqtt_status: {
          type: 'select',
          label: '状态',
          value: [],
          placeholder: '请选择状态',
          multiple: false,
          collapseTags: false,
          clearable: true,
          dataList: [
            // 放入数据
            {
              label: '成功',
              value: 'success'
            },
            {
              label: '失败',
              value: 'failed'
            }
            // ,{
            //   label: '同步中',
            //   value: 'running'
            // }
          ]
        },
        device_name: {
          type: 'input',
          label: '设备名',
          value: '',
          placeholder: '请输入设备名',
          clearable: true
        },
        operator: {
          type: 'input',
          label: '操作人',
          value: '',
          placeholder: '请输入操作人',
          clearable: true
        },
        sync_type: {
          type: 'select',
          label: '类型',
          value: '',
          placeholder: '请选择类型',
          dataList: [
            {
              label: '人员信息同步',
              value: 'card_info'
            },
            {
              label: '人脸同步',
              value: 'face'
            }
          ]
        }
      },
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // table数据
      collect: [ // 统计
        // { key: 'total_charge_off_money', value: 0, label: '合计冲销金额:￥', type: 'money' }
      ],
      printType: 'FaceSynchronizationHistory' // 打印type
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    // 初始化
    initLoad() {
      this.getDeviceType()
      this.getData()
    },
    // 防抖优化下
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getData()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.searchHandle()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getData()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取数据
    async getData() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceFaceRecordListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.tableData.map(item => {
          switch (item.mqtt_status) {
            case 'success':
              item.mqtt_status = '成功'
              break
            case 'failed':
              item.mqtt_status = '失败'
              break
            case 'running':
              item.mqtt_status = '同步中'
              break
          }
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        this.searchFormSetting.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.face-synchronization-history {}
</style>
