<template>
  <div ref="app" id="app" class="origin-wrapper">
    <router-view />
    <!-- setting btn -->
    <div v-if="showSettings" class="setting-tools noprint">
      <el-button type="primary" id="setting-btn" class="ps-origin-btn" icon="el-icon-setting" @click="showSettingDrowHandle"></el-button>
      <el-drawer
        title="设置"
        :visible.sync="settingVisible"
        direction="rtl"
        custom-class="setting-drawer"
        ref="settingDrawer"
        >
        <div class="setting-drawer__content">
          <el-form :model="settingToolForm">
            <el-form-item label="请求类型：" >
              <el-select v-model="settingToolForm.urlType" placeholder="请选择" @change="changeUrlTypeHandle">
                <el-option v-for="item in requestTypeList" :key="item.key" :label="item.label" :value="item.key"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="请求地址：" >
              <el-input v-model="settingToolForm.url" :disabled="settingToolForm.urlType !== 'custom'" type="textarea" :rows="3" style="max-width: 250px;" autocomplete="off"></el-input>
            </el-form-item>
          </el-form>
          <div class="setting-drawer__footer">
            <el-button class="ps-cancel-btn" @click="cancelDrawerHandle">取 消</el-button>
            <el-button type="primary" class="ps-origin-btn" @click="saveDrawerHandle">保 存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { getSessionStorage, setSessionStorage, jsonp, debounce } from '@/utils'
import { mapActions, mapGetters } from "vuex";
// import DevicePixelRatio from '@/utils/DevicePixelRatio.js';

export default {
  data() {
    return {
      settingVisible: false,
      settingToolForm: {
        urlType: '',
        url: ''
      },
      requestTypeList: [
        { label: '测试', key: 'test', value: 'https://cashier-v4.debug.packertec.com/' },
        // { label: '正式', key: 'prod', value: 'https://cashier-v4.packertec.com/' },
        { label: '自定义', key: 'custom', value: '' }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'showSettings'
    ])
  },
  mounted() {
    if (this.showSettings) {
      this.getJsonpData()
    }
  },
  methods: {
    ...mapActions('settings', {
      _setBaseUrl: "setBaseUrl"
    }),
    showSettingDrowHandle() {
      this.settingVisible = !this.settingVisible
      if (this.settingVisible) {
        this.settingToolForm.urlType = getSessionStorage('urlType') || ''
        this.settingToolForm.url = getSessionStorage('baseUrl') || ''
        if (!this.settingToolForm.url) {
          this.requestTypeList.forEach(item => {
            if (item.key === this.settingToolForm.urlType) {
              this.settingToolForm.url = item.value
            }
          })
        }
      }
    },
    changeUrlTypeHandle(e) {
      this.requestTypeList.forEach(item => {
        if (item.key === e) {
          this.settingToolForm.url = item.value
        }
      })
    },
    cancelDrawerHandle() {
      this.settingVisible = false
    },
    saveDrawerHandle() {
      // this.$refs.settingDrawer.closeDrawer()
      setSessionStorage('urlType', this.settingToolForm.urlType)
      setSessionStorage('baseUrl', this.settingToolForm.url)
      this._setBaseUrl(this.settingToolForm.url)
      this.settingVisible = false
    },
    // 发个jsonp请求拿去本地配置的数据
    async getJsonpData() {
      const [err, res] = await this.$to(jsonp('http://localhost:9999/jsonp', {
        test: 'jsonp1'
      }))
      if (err) {
        console.log(err)
        return
      }
      let result = JSON.parse(res)
      console.log('jsonp', result)
      if (result.code === 0) {
        this.requestTypeList = result.data
      }
    }
  }
}
</script>
<style lang="scss">
@import '@/styles/root.scss';
@import '@/styles/origin-theme.scss';
#setting-btn{
  position: fixed;
  bottom: 25px;
  right: 25px;
  z-index: 9;
}

.setting-drawer{
  .setting-drawer__content{
    // min-height: 100%;
    position: relative;
    padding: 0 30px;
  }
  .setting-drawer__footer{
    margin-top: 200px;
    text-align: center;
  }
}
</style>
