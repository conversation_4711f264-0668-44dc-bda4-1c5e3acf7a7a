import * as dayjs from 'dayjs'
import { ALL_MEAL_LIST } from '@/views/merchant/consumption-rules/constants'
import { deepClone } from '@/utils'
export const recentSevenDayTime = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]
// 当前天的前一个月
export const fullMonthRange = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
// 当前月份
export const currentMonth = dayjs().format('YYYY-MM')

// 台账权限分配 筛选设置
export const SEARCH_SETTING_MORNING_LEDGER_PERMISSION = {

  ledger_type_list: {
    type: 'select',
    label: '台账名称',
    clearable: true,
    value: '',
    placeholder: '请选择',
    dataList: [],
    multiple: false,
    filterable: true,
    collapseTags: true,
    listNameKey: 'value',
    listValueKey: 'name'
  }
}

// 台账权限分配 表格设置
export const TABLE_HEAD_DATA_LEDGER_PERMISSION = [
  { label: '管理台账名称', key: 'ledger_type_alias_list', width: '250px', type: 'slot', slotName: "ledger_type_alias_list" },
  { label: '指定操作员', key: 'operators_list', type: 'slot', slotName: "operators_list" },
  { label: '复核人', key: 'reviewers_list', type: 'slot', slotName: "reviewers_list" },
  { label: '最近更新时间', key: 'update_time' },
  { label: '操作', key: 'operation', type: 'slot', slotName: "operation" }
]
// 复核确认表筛选设置
export const SEARCH_SETTING_MORNING_REVIEW_CONFIRM = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: recentSevenDayTime,
    placeholder: "请选择",
    dataList: []
  },
  ledger_type_list: {
    type: 'select',
    label: '台账名称',
    clearable: true,
    value: '',
    placeholder: '请选择',
    dataList: [],
    multiple: false,
    filterable: true,
    collapseTags: true,
    listNameKey: 'value',
    listValueKey: 'name'
  },
  username: {
    type: 'input',
    label: '操作员',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}

// 复核确认表 待复核 表格设置
export const TABLE_HEAD_DATA_REVIEW_CONFIRM_PENDING = [
  { label: '创建时间', key: 'create_time' },
  { label: '管理台账名称', key: 'ledger_type_alias' },
  { label: '提交操作员', key: 'operator_username', type: 'slot', slotName: "operator_username" },
  { label: '复核状态', key: 'review_status_alias' },
  { label: '操作', key: 'operation', type: 'slot', slotName: "operation" }
]
// 复核确认表 已确认 表格设置
export const TABLE_HEAD_DATA_REVIEW_CONFIRM_CONFIRMED = [
  { label: '创建时间', key: 'create_time' },
  { label: '管理台账名称', key: 'ledger_type_alias' },
  { label: '提交操作员', key: 'operator_username', type: 'slot', slotName: "operator_username" },
  { label: '复核状态', key: 'review_status_alias' },
  { label: '复核时间', key: 'review_time' },
  { label: '复核人', key: 'confirmer_username', type: 'slot', slotName: "confirmer_username" },
  { label: '操作', key: 'operation', type: 'slot', slotName: "operation" }
]
// 复核确认表 已驳回 表格设置
export const TABLE_HEAD_DATA_REVIEW_CONFIRM_REJECTED = [
  { label: '提交时间', key: 'create_time' },
  { label: '管理台账名称', key: 'ledger_type_alias' },
  { label: '提交操作员', key: 'operator_username', type: 'slot', slotName: "operator_username" },
  { label: '复核状态', key: 'review_status_alias' },
  { label: '复核人', key: 'confirmer_username', type: 'slot', slotName: "confirmer_username" },
  { label: '复核时间', key: 'review_time' },
  { label: '拒绝原因', key: 'rejected_reason', showTooltip: true },
  { label: '操作', key: 'operation', type: 'slot', slotName: "operation" }
]
// 复核确认表 已撤销 表格设置
export const TABLE_HEAD_DATA_REVIEW_CONFIRM_REVOKED = [
  { label: '提交时间', key: 'create_time' },
  { label: '管理台账名称', key: 'ledger_type_alias' },
  { label: '提交操作员', key: 'operator_username', type: 'slot', slotName: "operator_username" },
  { label: '复核状态', key: 'review_status_alias' },
  { label: '撤销时间', key: 'revoked_time' }
]

// 提交记录表筛选设置
export const SEARCH_SETTING_MORNING_SUMIT_RECORD_LIST = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: recentSevenDayTime,
    placeholder: "请选择",
    dataList: []
  },
  ledger_type_list: {
    type: 'select',
    label: '台账名称',
    clearable: true,
    value: '',
    placeholder: '请选择',
    dataList: [],
    multiple: false,
    filterable: true,
    collapseTags: true,
    listNameKey: 'value',
    listValueKey: 'name'
  },
  ledger_no: {
    type: 'input',
    label: '台账编号',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 30
  },
  username: {
    type: 'input',
    label: '操作员',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  confirmer_username: {
    type: 'input',
    label: '复核人',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}
// 提交记录表 表格设置
export const TABLE_HEAD_DATA_SUMIT_RECORD_LIST = [
  { label: '创建时间', key: 'create_time' },
  { label: '台账名称', key: 'ledger_type_alias' },
  { label: '台账编号', key: 'ledger_no' },
  { label: '提交操作员', key: 'operator_username', type: 'slot', slotName: "operator_username" },
  { label: '复核人', key: 'confirmer_username', type: 'slot', slotName: "confirmer_username" },
  { label: '操作', key: 'operation', type: 'slot', slotName: "operation" }
]
// 食品安全风险管控清单筛选设置
export const SEARCH_SETTING_MORNING_FENG_XIAN_LEDGER = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: recentSevenDayTime,
    placeholder: "请选择",
    dataList: []
  },
  username: {
    type: 'input',
    label: '操作员',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}
// 食品安全风险管控清单 表格设置
export const TABLE_HEAD_DATA_FENG_XIAN_LEDGER = [
  { label: '创建时间', key: 'create_time' },
  { label: '台账编号', key: 'ledger_no', width: '250px' },
  { label: '操作员', key: 'operator_username', type: 'slot', slotName: "operatorUsername" },
  { label: '状态', key: 'state_alias' },
  { label: '操作', key: 'operation', type: 'slot', slotName: "operation" }
]
// 食品安全风险管控清单 表格设置
export const TABLE_HEAD_DATA_FENG_XIAN_LEDGER_DETAIL = [
  { label: '序号', key: 'index', width: '60px' },
  { label: '食品类别', key: 'food_category' },
  { label: '类别名称', key: 'category_name' },
  { label: '风险类型', key: 'risk_type' },
  { label: '过程控制环节', key: 'process_control' },
  { label: '风险点', key: 'risk_point' },
  { label: '风险描述', key: 'risk_description', width: '200px' },
  { label: '管控措施', key: 'control_measures', width: '300px', type: 'slot', slotName: "control_measures" },
  { label: '管控频次', key: 'control_frequency' },
  { label: '管控目标', key: 'control_target', width: '250px' }
]

// 厨房抽油烟机管理台账筛选设置
export const SEARCH_SETTING_MORNING_CHUFANG_CHOUYOU_LEDGER = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: recentSevenDayTime,
    placeholder: "请选择",
    dataList: []
  },
  username: {
    type: 'input',
    label: '操作员',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  confirmer_username: {
    type: 'input',
    label: '复核人',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}

// 厨房抽油烟机管理台账 表格设置
export const TABLE_HEAD_DATA_CHUFANG_CHOUYOU_LEDGER = [
  { label: '创建时间', key: 'create_time' },
  { label: '台账编号', key: 'ledger_no', width: '250px' },
  { label: '提交操作员', key: 'operator_username', type: 'slot', slotName: "operatorUsername" },
  { label: '复核人', key: 'confirmer_username', type: 'slot', slotName: "confirmerUsername" },
  { label: '操作', key: 'operation', type: 'slot', slotName: "operation" }
]

// 厨房抽油烟机管理台账详情 表格设置
export const TABLE_HEAD_DATA_CHUFANG_CHOUYOU_LEDGER_DETAIL = [
  { label: '清洗内容', key: 'clean_content', width: '250px' },
  { label: '清洗情况', key: 'clean_situation' },
  { label: '清洗人员', key: 'clean_person' },
  { label: '复核人', key: 'review_name' }
]

// 表13食品留样记录表  筛选设置
export const SEARCH_SETTING_FOOD_SAMPLE_RETENT_RECORD_BOOK = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: fullMonthRange,
    placeholder: "请选择",
    dataList: []
  },
  select_time_operate: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "样品处理日期",
    value: fullMonthRange,
    placeholder: "请选择",
    dataList: []
  },
  meal_type: {
    type: 'select',
    label: '餐次',
    clearable: true,
    value: [],
    placeholder: '请选择',
    dataList: deepClone(ALL_MEAL_LIST),
    multiple: true,
    filterable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'value'
  },
  food_name: {
    type: 'input',
    label: '菜品',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  reserved_user: {
    type: 'input',
    label: '留样人',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}

// 表13食品留样记录表 表格设置
export const TABLE_HEAD_DATA_FOOD_SAMPLE_RETENT_RECORD_BOOK = [
  { label: '留样日期', key: 'reserved_date' },
  { label: '餐次', key: 'meal_type_alias' },
  { label: '样品名称', key: 'food_name' },
  { label: '留样重量（g）', key: 'food_weight', showTooltip: true },
  { label: '留样时间', key: 'reserved_time' },
  { label: '留样人', key: 'reserved_user' },
  { label: '留样人签名', key: 'sign_info_reserved', type: 'slot', slotName: "reserved_sign" },
  { label: '样品处理日期', key: 'operate_date' },
  { label: '处理时间', key: 'operate_time' },
  { label: '清倒人', key: 'liquidator' },
  { label: '清倒人签名', key: 'sign_info_liquidator', type: 'slot', slotName: "liquidator_sign" }
]

// 表19/21/23/24/25  筛选设置
export const SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: fullMonthRange,
    placeholder: "请选择",
    dataList: []
  },
  confirmer: {
    type: 'input',
    label: '复核人',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}
// 表格设置
// 表23-学校每周食品安全排查治理报告
export const TABLE_HEAD_DATA_SCHOOL_WEEKLY_FOOD_SAFETY = [
  { label: '日期', key: 'operate_date' },
  { label: '报告周期(开始)', key: 'start_date' },
  { label: '报告周期(结束)', key: 'end_date' },
  { label: '本周排查发现问题情况', key: 'problem', showTooltip: true },
  { label: '周排查问题整改情况和措施', key: 'measure', showTooltip: true },
  { label: '本周食品安全管理情况评价', key: 'evaluate_alias', showTooltip: true },
  { label: '食品安全总监(1)', key: 'first_director' },
  { label: '食品安全总监(1)签名', key: 'sign_info_first', type: 'slot', slotName: "first_director_sign" },
  { label: '食品安全总监(2)', key: 'second_director' },
  { label: '食品安全总监(2)签名', key: 'sign_info_second', type: 'slot', slotName: "second_director_sign" },
  { label: '操作人', key: 'username' },
  { label: '复核人', key: 'confirmer', showTooltip: true }
]
// 表格设置
// 表24-学校食堂每月食品安全调度会议纪要
// 表25-学校校园食品安全工作管理清单
export const TABLE_HEAD_DATA_XUE_XIAO_HUI_YI_JI_YAO_GUAN = [
  { label: '创建时间', key: 'create_time' },
  { label: '台账编号', key: 'ledger_no' },
  { label: '提交操作员', key: 'username' },
  { label: '复核人', key: 'confirmer' },
  // { label: '提交操作员', key: 'operator_username', type: 'slot', slotName: "operator_username" },
  // { label: '复核人', key: 'reviewers_list', type: 'slot', slotName: "reviewers_list", showTooltip: true },
  { label: '操作', key: 'operation', type: 'slot', slotName: "operation" }
]

// 表21-从业人员食品安全培训记录表  表格设置
export const TABLE_HEAD_DATA_EMPLOYEES_FOOD_SAFETY_TRAINING_RECORD_FORM = [
  { label: '主讲人', key: 'speaker' },
  { label: '培训日期', key: 'operate_date' },
  { label: '培训时长(min)', key: 'duration', width: "120px" },
  { label: '培训主题', key: 'training_theme_alias', type: 'slot', slotName: "showOverflowTooltip" },
  { label: '培训方式', key: 'training_method_alias', type: 'slot', slotName: "showOverflowTooltip" },
  { label: '培训内容', key: 'training_content', type: 'slot', slotName: "showOverflowTooltip" },
  { label: '应到人员', key: 'expected_person', type: 'slot', slotName: "showOverflowTooltip" },
  { label: '实到人员', key: 'actual_person', type: 'slot', slotName: "showOverflowTooltip" },
  { label: '考核方式', key: 'assessment_method_alias' },
  { label: '培训记录照片', key: 'record_img', type: 'slot', slotName: "record_img" },
  { label: '记录人', key: 'username' },
  { label: '评估结果', key: 'evaluation_result_verbose' },
  { label: '复核人', key: 'confirmer' }
]

// 表26-集体用餐配送单位配送记录  表格设置
export const TABLE_HEAD_DATA_COLLECTIVE_MEAL_DELIVERY_UNIT_DELIVERY_RECORD = [
  { label: '日期', key: 'operate_date' },
  { label: '配送单位', key: 'delivery_unit', type: 'slot', slotName: "delivery_unit" },
  { label: '配送人', key: 'delivery_person' },
  { label: '签收人', key: 'receiver' },
  { label: '餐次', key: 'meal_type_alias' },
  { label: '配送数量', key: 'delivery_count' },
  { label: '出发时间', key: 'start_time' },
  { label: '到达时间', key: 'end_time' }
]

// 表11巡检记录表筛选设置
export const SEARCH_SETTING_XUNCHAJILUMANAGEMENTLEDGER = {
  month: {
    type: "month",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "月份",
    value: dayjs().format("YYYY-MM"),
    placeholder: "请选择",
    dataList: []
  },
  inspection_item: {
    type: 'input',
    label: '项目名称',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  confirmer: {
    type: 'input',
    label: '复核人',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}

// 过敏原使用管理台账筛选设置
export const SEARCH_SETTING_GUO_MIN_YUAN_LEDGER = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: recentSevenDayTime,
    placeholder: "请选择",
    dataList: []
  },
  meal_type: {
    type: 'select',
    label: '餐次',
    clearable: true,
    value: 'all',
    placeholder: '请选择',
    dataList: deepClone(ALL_MEAL_LIST),
    multiple: false,
    filterable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'value'
  },
  allergen: {
    type: 'input',
    label: '过敏原食材',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  food_name: {
    type: 'input',
    label: '菜品名称',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  // 登记人
  username: {
    type: 'input',
    label: '登记人',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  // 复核人
  confirmer: {
    type: 'input',
    label: '复核人',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}
// 过敏原使用管理台账 表格设置
export const TABLE_HEAD_DATA_GUO_MIN_YUAN_LEDGER = [
  { label: '供应时间', key: 'operate_date' },
  { label: '供应餐次', key: 'meal_type_alias' },
  { label: '使用的过敏原食材', key: 'allergen' },
  { label: '菜品名称', key: 'food_name' },
  { label: '登记人', key: 'username' },
  { label: '备注', key: 'remark' },
  { label: '复核人', key: 'confirmer' }
]
// 从业人员晨检表 筛选设置
export const SEARCH_SETTING_EMPLOYEES_CHEN_JIAN_BIAO = {
  select_time: {
    type: "month",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "月份",
    value: currentMonth,
    placeholder: "请选择",
    dataList: []
  },
  name: {
    type: 'input',
    label: '姓名',
    clearable: true,
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}
// 从业人员晨检表 表格设置
export const TABLE_HEAD_DATA_EMPLOYEES_CHEN_JIAN_BIAO = [
  { label: '序号', key: 'order_no', width: '60px' },
  {
    label: '日期',
    key: 'date',
    children: [
      { label: '姓名', key: 'name' }
    ]
  },
  {
    label: '',
    key: 'disease',
    children: [
      { label: '1', key: '1' },
      { label: '2', key: '2' },
      { label: '3', key: '3' },
      { label: '4', key: '4' },
      { label: '5', key: '5' },
      { label: '6', key: '6' },
      { label: '7', key: '7' },
      { label: '8', key: '8' },
      { label: '9', key: '9' },
      { label: '10', key: '10' },
      { label: '11', key: '11' },
      { label: '12', key: '12' },
      { label: '13', key: '13' },
      { label: '14', key: '14' },
      { label: '15', key: '15' },
      { label: '16', key: '16' },
      { label: '17', key: '17' },
      { label: '18', key: '18' },
      { label: '19', key: '19' },
      { label: '20', key: '20' },
      { label: '21', key: '21' },
      { label: '22', key: '22' },
      { label: '23', key: '23' },
      { label: '24', key: '24' },
      { label: '25', key: '25' },
      { label: '26', key: '26' },
      { label: '27', key: '27' },
      { label: '28', key: '28' },
      { label: '29', key: '29' },
      { label: '30', key: '30' },
      { label: '31', key: '31' }
    ]
  }
]

// 表3食堂设备定期维护清洗管理台账筛选设置
export const SEARCH_SETTING_SHITANGSHEBEI_LEDGER = {
  month: {
    type: "month",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "月份",
    value: dayjs().format("YYYY-MM"),
    placeholder: "请选择",
    dataList: []
  },
  cleaning_status: {
    type: 'select',
    label: '是否清洗',
    value: '',
    dataList: [
      { label: '全部', value: '' },
      { label: '是', value: 1 },
      { label: '否', value: 0 }
    ]
  },
  maintenance_status: {
    type: 'select',
    label: '是否维护',
    value: '',
    dataList: [
      { label: '全部', value: '' },
      { label: '是', value: 1 },
      { label: '否', value: 0 }
    ]
  },
  username: {
    type: 'input',
    label: '操作员',
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  confirmer: {
    type: 'input',
    label: '复核人',
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}

// 食堂设备定期维护清洗管理台账  表格设置
export const TABLE_HEAD_DATA_SHITANGSHEBEI_LEDGER = [
  { label: '日期', key: 'date' },
  {
    label: '设备名称',
    key: 'device',
    children: [
      { label: '消毒柜', key: 'xiaodu' },
      { label: '蒸烧柜', key: 'zhengshao' },
      { label: '热水器', key: 'reshuiqi' },
      { label: '热汤池', key: 'reshuiyou' },
      { label: '冰箱', key: 'bingxiang' },
      { label: '紫外线灯', key: 'purenweixiang' },
      { label: '灭蝇灯', key: 'weibolu' },
      { label: '保餐柜', key: 'baocanto' },
      { label: '抽油烟机', key: 'chouyouyan' }
    ]
  },
  { label: '清洗', key: 'cleaning_status' },
  { label: '维护', key: 'maintenance_status' },
  { label: '设备状态', key: 'device_status' },
  { label: '操作人员', key: 'username' },
  { label: '复核人', key: 'confirmer' }
]

// 表5学校食堂刀具消毒明细管理台账筛选设置
export const SEARCH_SETTING_SHITANGDAOJU_LEDGER = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: recentSevenDayTime,
    placeholder: "请选择",
    dataList: []
  },
  knife_name: {
    type: 'input',
    label: '刀具名称',
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  cleaning_status: {
    type: 'select',
    label: '是否消毒',
    value: '',
    dataList: [
      { label: '全部', value: '' },
      { label: '是', value: 1 },
      { label: '否', value: 0 }
    ]
  },
  confirmer: {
    type: 'input',
    label: '复核人',
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}

// 学校食堂刀具消毒明细管理台账管理台账  表格设置
export const TABLE_HEAD_DATA_SHITANGDAOJU_LEDGER = [
  { label: '日期', key: 'date' },
  { label: '刀具名称', key: 'knife_name' },
  { label: '在用数量', key: 'in_use_count' },
  { label: '消毒方式', key: 'disinfection_method' },
  { label: '是否消毒', key: 'disinfection_status' },
  { label: '刀具保管人', key: 'knife_keeper' },
  { label: '刀具保管人签名', key: 'knife_keeper_signature', type: 'slot', slotName: "knifeKeeperSignature" },
  { label: '废旧数量', key: 'waste_count' },
  { label: '废旧处理', key: 'waste_method' },
  { label: '废旧刀具处理人', key: 'waste_person' },
  { label: '废旧刀具处理人签名', key: 'waste_person_signature', type: 'slot', slotName: "wastePersonSignature" },
  { label: '复核人', key: 'confirmer' }
]
// 表19-餐厨垃圾处理管理台账  表格设置
export const CANCHU_LAJI_MANAGMENT_LEDGER_FORM = [
  { label: '日期', key: 'operate_date' },
  { label: '收货单位', key: 'unit' },
  { label: '时间', key: 'use_time' },
  { label: '收取量', key: 'quantity' },
  { label: '收货人', key: 'consignee' },
  { label: '去向', key: 'destination' },
  { label: '处置照片', key: 'images', type: 'slot', slotName: "images" },
  { label: '操作人', key: 'account_info' },
  { label: '复核人', key: 'confirmer' }
]

// 表12蛋类清洗记录表筛选设置
export const SEARCH_SETTING_DANLEI_QINGXI_LEDGER = {
  select_time: {
    type: "daterange",
    filterable: true,
    defaultExpandAll: true,
    clearable: false,
    label: "日期筛选",
    value: recentSevenDayTime,
    placeholder: "请选择",
    dataList: []
  },
  egg_name: {
    type: 'input',
    label: '蛋类名称',
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  username: {
    type: 'input',
    label: '操作人',
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  check_person: {
    type: 'input',
    label: '检查人',
    value: '',
    placeholder: '请输入',
    maxlength: 20
  },
  confirmer: {
    type: 'input',
    label: '复核人',
    value: '',
    placeholder: '请输入',
    maxlength: 20
  }
}

// 蛋类清洗记录表  表格设置
export const TABLE_HEAD_DATA_DANLEI_QINGXI_LEDGER = [
  { label: '日期', key: 'create_date' },
  { label: '名称', key: 'egg_name' },
  { label: '数量', key: 'count' },
  { label: '清洗时间', key: 'disinfection_method' },
  { label: '操作人', key: 'username' },
  { label: '检查人', key: 'check_person' },
  { label: '检查结果', key: 'result', type: 'slot', slotName: "result" },
  { label: '复核人', key: 'confirmer' }
]
