// 百度统计
// #ifdef H5
/* eslint-disable */
var _hmt = _hmt || [];
// 判断域名使用
if (location.host.indexOf('cashier-v4') > -1) {
  if (location.host.indexOf('debug') > -1) {
    // 测试环境
    createBaiduHm('https://hm.baidu.com/hm.js?01ad44b0e47393ec453b1de470a019f5')
  } else {
    // 开发环境
    createBaiduHm('https://hm.baidu.com/hm.js?541ca703f55e562d6ec221aaf9efe773')
  }
}

function createBaiduHm(src) {
  var hm = document.createElement('script')
  hm.src = src
  var s = document.getElementsByTagName('script')[0]
  s.parentNode.insertBefore(hm, s)
}
// #endif
