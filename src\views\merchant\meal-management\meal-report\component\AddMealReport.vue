<template>
  <el-drawer
    :title="type === 'add' ? '新增报餐规则': '编辑报餐规则'"
    :visible.sync="showDrawer"
    :direction="direction"
    :wrapperClosable="wrapperClosable"
    :size="'820px'"
    class="meal-drawer-wrapper"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <div class="meal-declaration container-wrapper">
      <el-form v-loading="isLoading" :model="mealForm" :rules="mealFormRules" ref="mealFormRef" label-width="125px">
        <div class="basic">
          <div class="l-title clearfix">
            <span class="float-l">基本设置</span>
          </div>
          <div class="form-line margin-button"></div>
          <div>
            <el-form-item size="small" label="当前组织：" prop="company">
              <template>
                <span>{{ mealForm.company }}</span>
              </template>
            </el-form-item>
            <el-form-item label="规则名称：" prop="name" size="small">
              <el-input v-model="mealForm.name" class="w-220" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item size="small" label="适用分组：" prop="report_card_groups">
              <user-group-select multiple v-model="mealForm.report_card_groups"></user-group-select>
            </el-form-item>
            <el-form-item size="small" label="适用消费点：" prop="report_organizations">
              <consume-select multiple v-model="mealForm.report_organizations"></consume-select>
            </el-form-item>
            <el-form-item label="餐段配置：" prop="meal_types">
              <div class="meal-types">
                <el-checkbox-group v-model="mealForm.meal_types">
                  <el-checkbox
                    v-for="mt in mealType"
                    :label="mt.value"
                    :key="mt.value"
                    name="meal_types"
                    class="ps-checkbox"
                  >
                    <span style="width: 50px;display: inline-block;">{{ mt.label }}</span>
                    <el-input
                      v-model="mealForm[mt.field2]"
                      :disabled="!mealForm.meal_types.includes(mt.value)"
                      size="mini"
                      style="width: 80px; margin-right: 10px;"
                    ></el-input>元
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
            <el-form-item label="下单方式：" prop="order_type">
              <el-checkbox-group v-model="mealForm.order_type">
                <el-checkbox label="report_meal" name="order_type" class="ps-checkbox">
                  报餐模式
                </el-checkbox>
                <el-checkbox label="meal_pack" name="order_type" class="ps-checkbox" :disabled="mealForm.consume_type === 'offline'">
                  餐包模式
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="取餐方式：" prop="take_out_type">
              <el-checkbox-group v-model="mealForm.take_out_type">
                <el-checkbox label="on_scene" name="take_out_type" class="ps-checkbox">
                  堂食
                </el-checkbox>
                <el-checkbox label="bale" name="take_out_type" class="ps-checkbox">
                  食堂自提
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="扣费方式：" prop="consume_type">
              <el-radio-group v-model="mealForm.consume_type">
                <el-radio class="ps-radio" label="online">线上支付</el-radio>
                <el-radio
                  class="ps-radio"
                  label="offline"
                  :disabled="mealForm.order_type.indexOf('meal_pack') !== -1"
                >
                  线下核销
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item size="small" label="限制份数：" prop="is_limit" style="height:42px;">
              <el-radio-group v-model="mealForm.is_limit" class="is_limit">
                <el-radio class="ps-radio" :label="0">不限制</el-radio>
                <el-radio class="ps-radio" :label="1">单人单餐限制</el-radio>
              </el-radio-group>
              <div class="inline-block" v-if="mealForm.is_limit === 1">
                <el-input-number
                  v-model="mealForm.limit_count"
                  :min="1"
                  controls-position="right"
                  style="margin: 0 12px;"
                ></el-input-number>
                份
              </div>
            </el-form-item>
            <el-form-item label="服务费：" prop="fuwu_fee">
              <el-input v-model="mealForm.fuwu_fee" style="width: 100px"></el-input>
              <span class="tail-unit">元</span>
            </el-form-item>
            <el-form-item size="small" label="取消订单：">
              <div class="box-flex m-t-5">
                <el-switch
                  v-model="mealForm.can_refund"
                  active-color="#ff9b45"
                  inactive-color="#ffcda2"
                ></el-switch>
                <span class="tips m-l-36">注：订单完成后不允许取消</span>
              </div>
              <div class="can-refund-wrapper" v-if="mealForm.can_refund">
                <el-radio class="ps-radio" v-model="mealForm.refund_remit_type" label="pay_after">
                  <template>
                    <span>下单后</span>
                    <div class="inline-block" v-if="mealForm.refund_remit_type === 'pay_after'">
                      <el-input-number
                        v-model="mealForm.refund_remit_time"
                        :min="1"
                        controls-position="right"
                        style="margin: 0 12px;"
                      ></el-input-number>
                      <span>小时内</span>
                    </div>
                  </template>
                </el-radio>
                <div>
                  <el-radio class="ps-radio" v-model="mealForm.refund_remit_type" label="meal_before">
                    <template>
                      <span>餐段结束前</span>
                      <div class="inline-block" v-if="mealForm.refund_remit_type === 'meal_before'">
                        <el-input-number
                          v-model="mealForm.refund_remit_time"
                          :min="1"
                          controls-position="right"
                          style="margin: 0 12px;"
                        ></el-input-number>
                        <span>小时内</span>
                      </div>
                    </template>
                  </el-radio>
                </div>
                <el-radio class="ps-radio" v-model="mealForm.refund_remit_type" label="day_hour_before">
                  <template>
                    <span>就餐当日前</span>
                    <div class="inline-block" v-if="mealForm.refund_remit_type === 'day_hour_before'">
                      <el-input-number
                        v-model="mealForm.refund_remit_day"
                        :min="0"
                        controls-position="right"
                        style="margin: 0 5px;"
                        @change="changeRefundRemitDay"
                      ></el-input-number>
                      <span>天</span>
                      <el-input-number
                        v-model="mealForm.refund_remit_before_hour"
                        controls-position="right"
                        :min="minHour"
                        :max="23"
                        style="margin: 0 5px;"
                        @change="changeRefundRemitBeforeHour"
                      ></el-input-number>
                      <span>小时</span>
                    </div>
                  </template>
                </el-radio>
              </div>
            </el-form-item>
            <el-form-item label="是否需要审核：" v-if="mealForm.can_refund">
              <div class="box-flex">
                <el-switch v-model="mealForm.cancel_review" active-color="#ff9b45" inactive-color="#ffcda2" class="m-t-10 m-r-20"></el-switch>
                <el-form-item label="" v-if="mealForm.cancel_review">
                  <el-radio-group v-model="mealForm.review_type" class="ps-radio">
                    <el-radio label="auto_reject">开餐后，自动拒绝</el-radio>
                    <el-radio label="auto_success">开餐后，自动同意</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <span class="tips">注：餐包订单的停餐/恢复就餐无需审核</span>
            </el-form-item>
          </div>
          <div class="l-title clearfix">
            <span class="float-l">点餐设置</span>
          </div>
          <div class="form-line margin-button"></div>
          <span class="tips m-l-20 m-b-10">注：餐包订单不受点餐设置影响</span>
          <div>
            <el-form-item size="small" label="可报餐天数：" prop="can_reservation_days">
              <el-input-number
                v-model="mealForm.can_reservation_days"
                :min="0"
                controls-position="right"
              ></el-input-number>
              <span class="tail-unit">天（含当天）</span>
            </el-form-item>
            <el-form-item label="报餐截止：">
              <div class="box-flex row-between">
                <div>餐段结束前</div>
                <div>单位：小时</div>
              </div>
              <div class="fake-table-wrapper">
                <div class="fake-table">
                  <div class="fake-col" v-for="mt in mealType" :key="mt.value">
                    <span class="fake-col-title">{{ mt.label }}</span>
                    <el-input-number
                      controls-position="right"
                      v-model="mealForm[mt.field]"
                      :min="0"
                      :disabled="!mealForm.meal_types.includes(mt.value)"
                      size="mini"
                      style="width: 86px; margin: 10px;"
                    ></el-input-number>
                  </div>
                </div>
              </div>
            </el-form-item>
            <!-- 节假日 -->
            <el-form-item label="以下日期跳过：" class="skip_wrapper">
              <div class="box-flex">
                <div class="m-r-20">
                  <el-checkbox v-model="mealForm.skip_weekends" class="ps-checkbox">周末</el-checkbox>
                  <el-checkbox v-model="mealForm.skip_holiday" class="ps-checkbox">节假日</el-checkbox>
                  <el-checkbox v-model="mealForm.skip_work_days" class="ps-checkbox">工作日</el-checkbox>
                </div>
                <div class="ship-box">
                  <div>
                    <el-button type="text" class="hidden-picker">
                      添加
                      <el-date-picker
                        type="dates"
                        :clearable="false"
                        v-model="mealForm.skip_days"
                        placeholder="选择一个或多个日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        popper-class="hidden-picker-year"
                        :picker-options="pickerOptions"
                      ></el-date-picker>
                    </el-button>
                    <el-button type="text" @click="clearShipDay">清空</el-button>
                  </div>
                </div>
              </div>
              <transition-group name="el-zoom-in-center" class="no-ship" tag="div">
                <div class="no-ship-time" v-for="(item, index) in mealForm.skip_days" :key="item">
                  {{ formatNoSkipTime(item) }}
                  <div class="del-time" @click="delSkipTime(index)">
                    <i class="el-icon-error"></i>
                  </div>
                </div>
              </transition-group>
            </el-form-item>
            <div class="skip_wrapper">
              <!-- 以下日期不跳过 -->
              <el-form-item label="以下日期不跳过：" class="skip_wrapper">
                <div>
                  <el-button type="text" class="hidden-picker">
                    添加
                    <el-date-picker
                      type="dates"
                      :clearable="false"
                      v-model="mealForm.no_skip_days"
                      placeholder="选择一个或多个日期"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      popper-class="hidden-picker-year"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </el-button>
                  <el-button type="text" @click="clearNoShipDay">清空</el-button>
                </div>
                <transition-group name="el-zoom-in-center" class="no-ship" tag="div">
                  <div class="no-ship-time" v-for="(item, index) in mealForm.no_skip_days" :key="item">
                    {{ formatNoSkipTime(item) }}
                    <div class="del-time" @click="delNoSkipTime(index)">
                      <i class="el-icon-error"></i>
                    </div>
                  </div>
                </transition-group>
              </el-form-item>
            </div>
            <!-- <el-form-item label="单餐价格：">
              <div class="fake-table-wrapper">
                <div class="fake-table">
                  <div class="fake-col" v-for="mt in mealType" :key="mt.value">
                    <span class="fake-col-title">{{ mt.label }}</span>
                    <el-input-number
                      controls-position="right"
                      v-model="mealForm[mt.field2]"
                      :min="0"
                      :disabled="!mealForm.meal_types.includes(mt.value)"
                      size="mini"
                      style="width: 120px; margin: 10px;"
                    ></el-input-number>
                  </div>
                </div>
                <span style="margin-left: 16px">元</span>
              </div>
            </el-form-item> -->
          </div>
        </div>
        <div class="ps-el-drawer-footer">
          <el-button size="small" class="w-100" @click="showDrawer = false">取消</el-button>
          <el-button size="small" type="primary" class="w-100" @click="saveSetting">保存</el-button>
        </div>
      </el-form>
    </div>
  </el-drawer>
</template>

<script>
import { mapGetters } from 'vuex'
import UserGroupSelect from '@/components/UserGroupSelect'
import ConsumeSelect from '@/components/ConsumeSelect'
import { MEAL_TYPES } from '@/utils/constants'
import { to, divide, times, deleteEmptyKey } from '@/utils'
import { confirm } from '@/utils/message'
export default {
  name: 'AddMealDeclaration',
  // mixins: [activatedLoadData],
  components: {
    UserGroupSelect,
    ConsumeSelect
  },
  props: {
    show: {
      required: true,
      type: Boolean
    },
    wrapperClosable: {
      type: Boolean,
      default: true
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    },
    type: {
      type: String,
      default: 'add'
    },
    confirm: Function
  },
  data() {
    let validMoney = (rule, value, callback) => {
      if (value) {
        let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      isLoading: false, // 刷新数据
      editData: [],
      mealType: MEAL_TYPES,
      mealForm: {
        // 基本设置
        company: 'xx公司',
        name: '',
        report_card_groups: [], // 适用分组
        report_organizations: [], // 消费点
        meal_types: [], // 可报餐餐段
        is_limit: 0, // 单餐限制份数
        limit_count: '', // 单餐份数
        can_reservation_days: '', // 可报餐天数
        breakfast_ahead: 0, // 餐段结束前(早餐)
        lunch_ahead: 0, // 餐段结束前(午餐)
        hit_tea_ahead: 0, // 餐段结束前(下午茶)
        dinner_ahead: 0, // 餐段结束前(晚餐)
        midnight_ahead: 0, // 餐段结束前(夜宵)
        early_ahead: 0, // 餐段结束前(凌晨餐)
        order_type: ['report_meal'], // 下单方式
        take_out_type: ['on_scene'], // 取餐方式
        consume_type: 'online', // 扣费方式
        breakfast_fixed: 0, // 固定单餐金额(早餐)
        lunch_fixed: 0, // 固定单餐金额(午餐)
        hit_tea_fixed: 0, // 固定单餐金额(下午茶)
        dinner_fixed: 0, // 固定单餐金额(晚餐)
        midnight_fixed: 0, // 固定单餐金额(夜宵)
        early_fixed: 0, // 固定单餐金额(凌晨餐)
        can_refund: true, // 是否支持取消
        cancel_review: true, // 是否支持取消
        review_type: 'auto_reject', // 是否支持取消
        refund_remit_type: 'pay_after', // 取消类型
        refund_remit_time: '', // 下单后/餐段结束前几小时内可退
        refund_remit_day: '', // 取消(天数)
        refund_remit_before_hour: '', // 取消(开餐前小时)
        fuwu_fee: '', // 服务费
        skip_holiday: false, // 跳过节假日
        skip_weekends: false, // 跳过周末
        skip_work_days: false, // 跳过工作日
        skip_days: [], // 跳过的日期
        no_skip_days: [] // 不跳过的日期
      },
      mealFormRules: {
        name: [{ required: true, message: '请输入规则名称', trigger: 'change, blur' }],
        report_card_groups: [{ required: true, message: '至少选择一个分组', trigger: 'change, blur' }],
        report_organizations: [
          { required: true, message: '至少选择一个消费点', trigger: 'change, blur' }
        ],
        meal_types: [{ required: true, message: '至少选择一个餐段', trigger: 'change, blur' }],
        can_reservation_days: [{ type: 'number', required: true, message: '时间必须大于或等于0' }],
        order_type: [{ required: true, message: '至少选择一个下单方式', trigger: 'change, blur' }],
        take_out_type: [{ required: true, message: '至少选择一个取餐方式', trigger: 'change, blur' }],
        consume_type: [{ required: true, message: '至少选择一个取餐方式', trigger: 'change, blur' }],
        is_limit: [{ required: true, message: '至少选择一个取餐方式', trigger: 'change, blur' }],
        fuwu_fee: [{ validator: validMoney, trigger: 'blur' }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 8.64e7=1000*60*60*24一天
        }
      },
      minHour: 0 // 审批最小时间
    }
  },
  created() {
  },
  computed: {
    ...mapGetters(['userInfo']),
    showDrawer: {
      get() {
        if (this.show) {
          this.initLoad()
        }
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      } 
    }
  },
  methods: {
    async initLoad() {
      console.log(999888, this.infoData)
      this.resetForm()
      this.mealForm.company = this.userInfo.company_name
      if (this.type === 'edit') {
        this.editData = this.infoData
        this.initEditForm()
      }
    },
    initEditForm() {
      console.log(this.editData)
      this.mealForm.meal_types = this.editData.meal_type_detail.meal_type
      let fields = this.mealType.map(d => d.field)
      let keys = [
        ...fields,
        'name',
        'report_card_groups',
        'report_organizations',
        // 'meal_types',
        'can_reservation_days',
        'order_type',
        'take_out_type',
        'consume_type',
        'can_refund',
        'refund_remit_type',
        'refund_remit_time',
        'cancel_review',
        'review_type',
        'refund_remit_before_hour',
        'refund_remit_day',
        'skip_holiday',
        'skip_weekends',
        'skip_work_days'
      ]
      keys.forEach(f => {
        this.mealForm[f] = this.editData[f]
      })
      if (this.editData.skip_days) {
        this.mealForm.skip_days =
          typeof this.editData.skip_days === 'string'
            ? JSON.parse(this.editData.skip_days)
            : this.editData.skip_days
      }
      if (this.editData.no_skip_days) {
        this.mealForm.no_skip_days =
          typeof this.editData.no_skip_days === 'string'
            ? JSON.parse(this.editData.no_skip_days)
            : this.editData.no_skip_days
      }
      let priceKeys = this.mealType.map(d => d.field2)
      priceKeys.push('fuwu_fee')
      priceKeys.forEach(f => {
        this.mealForm[f] = divide(this.editData[f])
      })
      this.mealForm.is_limit = this.editData.limit_count === 0 ? 0 : 1
      if (this.editData.limit_count > 0) {
        this.mealForm.limit_count = this.editData.limit_count
      }
    },
    changeRefundRemitDay(val) {
      // eslint-disable-next-line eqeqeq
      if (this.mealForm.refund_remit_day == 0 && val == 0) {
        this.$set(this.mealForm, 'refund_remit_before_hour', 1)
        this.minHour = 1
      } else {
        this.minHour = 0
      }
    },
    changeRefundRemitBeforeHour(val) {
      // eslint-disable-next-line eqeqeq
      if (this.mealForm.refund_remit_day == 0 && val == 0) {
        this.minHour = 1
      } else {
        this.minHour = 0
      }
    },
    saveSetting() {
      this.$refs.mealFormRef.validate(valid => {
        if (valid) {
          if (!this.checkForm()) return
          if (this.isLoading) return
          if (this.type === 'add') {
            this.addMealDeclaration(this.formatData())
          } else {
            this.checkPackageRule()
          }
        } else {
          console.log(valid)
        }
      })
    },
    async addMealDeclaration(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundReportMealReportMealSettingsAddPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirm', res.msg)
        // this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 检查是否关联餐包
    async checkPackageRule() {
      const res = await this.$apis.apiBackgroundReportMealReportMealSettingsCheckReportMealPackSettingsPost({
        id: this.editData.id
      })
      if (res.code === 0) {
        if (!res.data.flag) {
          this.modifyMealDeclaration(this.formatData())
        } else {
          confirm({ 
            content: `该规则已被餐包规则关联，修改后将同步下架对应餐包`
          }).then(_ => {
            this.modifyMealDeclaration(this.formatData())
          }).catch(e => {
            if (e === 'cancel') {
              console.log('cancel')
            }
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    async modifyMealDeclaration(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundReportMealReportMealSettingsModifyPost({
          ...params,
          id: this.editData.id
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirm', res.msg)
        // this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳过时间格式化
    formatNoSkipTime(date) {
      // let text = date.split('-')[0] + '月' + date.split('-')[1] + '日'
      let result = date.replace(/-/g, (result, key) => {
        let text = ''
        switch (key) {
          case 4:
            text = '年'
            break
          case 7:
            text = '月'
            break
        }
        return text
      })
      return result + '日'
    },
    // 单个删除
    delNoSkipTime(index) {
      this.mealForm.no_skip_days.splice(index, 1)
    },
    // 清除全部
    clearNoShipDay() {
      this.mealForm.no_skip_days = []
    },
    // 单个删除
    delSkipTime(index) {
      this.mealForm.skip_days.splice(index, 1)
    },
    // 清除全部
    clearShipDay() {
      this.mealForm.skip_days = []
    },
    // 手动校验下吧
    checkForm() {
      let result = true
      if (this.mealForm.can_refund) {
        if (!this.mealForm.refund_remit_type) {
          this.$message.error('请选择取消订单类型！')
          result = false
          return false
        }
        if (this.mealForm.refund_remit_type !== 'day_hour_before' && this.mealForm.refund_remit_time === undefined) {
          this.$message.error('取消时间不能为空！')
          result = false
          return false
        } else {
          if (this.mealForm.refund_remit_day === undefined || this.mealForm.refund_remit_before_hour === undefined) {
            this.$message.error('取消时间不能为空！')
            result = false
            return false
          }
        }
      }
      return result
    },
    formatData() {
      let params = {
        organization: sessionStorage.getItem('organization'),
        name: this.mealForm.name,
        report_card_groups: this.mealForm.report_card_groups,
        report_organizations: this.mealForm.report_organizations,
        meal_types: [],
        limit_count: this.mealForm.is_limit === 1 ? this.mealForm.limit_count : 0,
        can_reservation_days: this.mealForm.can_reservation_days,
        order_type: this.mealForm.order_type,
        take_out_type: this.mealForm.take_out_type,
        consume_type: this.mealForm.consume_type,
        can_refund: this.mealForm.can_refund,
        cancel_review: this.mealForm.cancel_review,
        review_type: this.mealForm.review_type,
        refund_remit_day: this.mealForm.refund_remit_day,
        refund_remit_before_hour: this.mealForm.refund_remit_before_hour,
        fuwu_fee: times(this.mealForm.fuwu_fee),
        skip_holiday: this.mealForm.skip_holiday,
        skip_weekends: this.mealForm.skip_weekends,
        skip_work_days: this.mealForm.skip_work_days,
        no_skip_days: JSON.stringify(this.mealForm.no_skip_days),
        skip_days: JSON.stringify(this.mealForm.skip_days)
      }
      this.mealType.map(item => {
        this.mealForm.meal_types.map(meal => {
          if (item.value === meal) {
            params[item.field] = this.mealForm[item.field]
            params[item.field2] = times(this.mealForm[item.field2])
            params.meal_types.push(meal)
          }
        })
      })
      if (this.mealForm.can_refund) {
        params.refund_remit_type = this.mealForm.refund_remit_type
        params.refund_remit_time = this.mealForm.refund_remit_time
      } else {
        params.cancel_review = false
      }
      return deleteEmptyKey(params)
    },
    resetForm() {
      this.mealForm = {
        // 基本设置
        company: 'xx公司',
        name: '',
        report_card_groups: [], // 适用分组
        report_organizations: [], // 消费点
        meal_types: [], // 可报餐餐段
        is_limit: 0, // 单餐限制份数
        limit_count: '', // 单餐份数
        can_reservation_days: '', // 可报餐天数
        breakfast_ahead: 0, // 餐段结束前(早餐)
        lunch_ahead: 0, // 餐段结束前(午餐)
        hit_tea_ahead: 0, // 餐段结束前(下午茶)
        dinner_ahead: 0, // 餐段结束前(晚餐)
        midnight_ahead: 0, // 餐段结束前(夜宵)
        early_ahead: 0, // 餐段结束前(凌晨餐)
        order_type: ['report_meal'], // 下单方式
        take_out_type: ['on_scene'], // 取餐方式
        consume_type: 'online', // 扣费方式
        breakfast_fixed: 0, // 固定单餐金额(早餐)
        lunch_fixed: 0, // 固定单餐金额(午餐)
        hit_tea_fixed: 0, // 固定单餐金额(下午茶)
        dinner_fixed: 0, // 固定单餐金额(晚餐)
        midnight_fixed: 0, // 固定单餐金额(夜宵)
        early_fixed: 0, // 固定单餐金额(凌晨餐)
        can_refund: true, // 是否支持取消
        cancel_review: true, // 是否支持取消
        review_type: 'auto_reject', // 是否支持取消
        refund_remit_type: 'pay_after', // 取消类型
        refund_remit_time: '', // 下单后/餐段结束前几小时内可退
        refund_remit_day: '', // 取消(天数)
        refund_remit_before_hour: '', // 取消(开餐前小时)
        fuwu_fee: '', // 服务费
        skip_holiday: false, // 跳过节假日
        skip_weekends: false, // 跳过周末
        skip_work_days: false, // 跳过工作日
        skip_days: [], // 跳过的日期
        no_skip_days: [] // 不跳过的日期
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.meal-declaration {
  padding: 0 20px;
  border-radius: 12px;
  background-color: #fff;

  .margin-button {
    margin-bottom: 15px;
  }
  .box-flex{
    display: flex;
  }
  .row-between{
    justify-content: space-between;
  }
  .tips {
    display: block;
    margin-top: -6px;
    color: #f41818;
    font-size: 14px;
  }
  .can-refund-wrapper{
    border: 1px solid #ddd;
    // margin-left: 65px;
    margin-top: 10px;
    padding: 10px;
  }
  .tail-unit {
    margin-left: 12px;
  }
  .fake-table-wrapper {
    display: flex;
    align-items: flex-end;
    .fake-table {
      display: flex;
      // width: 600px;
      border: 1px solid #ddd;
      .fake-col {
        display: flex;
        align-items: center;
        flex-direction: column;
        // width: 130px;
        border-right: 1px solid #ddd;

        .fake-col-title {
          display: block;
          width: 100%;
          border-bottom: 1px solid #ddd;
          text-align: center;
        }
      }
    }
  }
  .footer {
    margin-top: 30px;
    .el-button {
      min-width: 180px;
    }
  }
  .skip_wrapper {
    font-size: 14px;
    color: #606266;
    .ship-box {
      .ship-box-item {
        width: 250px;
        .switch-box {
          margin: 15px 0;
        }
      }
    }
    .no-ship {
      padding: 10px 0;
      .no-ship-time {
        display: inline-block;
        margin-right: 30px;
        margin-bottom: 5px;
        font-size: 15px;
        position: relative;
        .del-time {
          position: absolute;
          top: -8px;
          right: -12px;
          cursor: pointer;
        }
      }
    }
    .hidden-picker {
      position: relative;
      overflow: hidden;
      .el-date-editor {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        .el-input__inner {
          padding: 0 !important;
        }
        opacity: 0;
      }
    }
  }
}
::v-deep .el-drawer__header{
  margin-bottom: 0;
  padding: 23px 20px;
  background: #e7e9ef;
}
</style>
