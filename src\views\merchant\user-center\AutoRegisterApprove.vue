<template>
  <!-- eslint-disable vue/no-unused-vars -->
  <div class="receipt-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      label-width="105px"
      :form-setting="searchSetting"
      :loading="isLoading"
      @search="searchHandle"
    >
      <template slot="perv">
        <div style="margin-bottom: 20px;">
          <el-radio-group v-model="tabType" :disabled="isLoading" @change="changeTabHandle" class="ps-radio-btn">
            <el-radio-button v-for="tab in tabTypeList" :key="tab.key" :label="tab.key" class="min-w-100">{{ tab.label }}</el-radio-button>
            <!-- <el-radio-button label="finish" class="min-w-100" v-permission="['background_approve.approve_register.finish_list']">已同意</el-radio-button>
            <el-radio-button label="reject" class="min-w-100" v-permission="['background_approve.approve_register.revoke_list']">已拒绝</el-radio-button> -->
          </el-radio-group>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon v-if="tabType === 'pending'" @click="showDialogHandle('mul_approve')"  v-permission="['background_approve.approve_register.bulk_agree_approve']">批量同意</button-icon>
          <button-icon v-if="tabType === 'pending'" @click="showDialogHandle('mul_reject')" v-permission="['background_approve.approve_register.bulk_reject_approve']">批量拒绝</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon
            v-if="tabType === 'finish'"
            color="plain"
            type="export"
            v-permission="['background_approve.approve_register.finish_list_export']"
            @click="handleExport"
          >
            导出报表
          </button-icon>
          <button-icon
            v-if="tabType === 'reject'"
            color="plain"
            type="export"
            v-permission="['background_approve.approve_register.revoke_list_export']"
            @click="handleExport"
          >
            导出报表
          </button-icon>
          <button-icon
            v-if="tabType === 'pending'"
            color="plain"
            type="export"
            v-permission="['background_approve.approve_register.pending_list_export']"
            @click="handleExport"
          >
            导出报表
          </button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <el-table
          :data="tableData"
          v-loading="isLoading"
          stripe
          header-row-class-name="ps-table-header-row"
          :empty-text="isFirstSearch ? '暂无数据，请查询' : ''"
          row-key="id"
          reserve-selection
          @selection-change="handleSelectionChange"
        >
          <table-column
            :index="indexMethod"
            v-for="item in currentTableSetting"
            :key="item.key"
            :col="item"
          >
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="showDialogHandle('approve', row)"
                v-permission="['background_approve.approve_register.agree_approve']"
              >
                同意
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                @click="showDialogHandle('reject', row)"
                v-permission="['background_approve.approve_register.reject_approve']"
              >
                拒绝
              </el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
    <AutoRegisterDialog
      :showdialog.sync="showFormdialog"
      :type="dialogType"
      :title="dialogTitle"
      :params="dialogFormParams"
      :infoData="infoData"
      @cancelForm="cancelAutoRegister"
      @confirmForm="confirmAutoRegister"
    />
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { SEARCH_FORM_LIST, TABLE_SETTING_LIST } from './constants/autoRegisterApprove.js'
import AutoRegisterDialog from "./components/AutoRegisterDialog"
import { mapGetters } from 'vuex'

export default {
  name: 'AutoRegisterApprove',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  components: { AutoRegisterDialog },
  data() {
    return {
      isLoading: false,
      tabType: 'pending',
      tabTypeList: [],
      searchSetting: {},
      // 数据列表
      tableData: [],

      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      levelList: [],

      // 报表设置相关
      tableSetting: [],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'AutoRegisterApprovePending',
      isFirstSearch: true,
      showFormdialog: false,
      dialogType: '',
      dialogTitle: '',
      dialogFormParams: {
        ids: []
      },
      infoData: {}
    }
  },
  created() {
    this.ininPermissions()
    this.initLoad(true)
  },
  mounted() {
  },
  computed: {
    ...mapGetters([
      'allPermissions'
    ])
  },
  methods: {
    initLoad(isFirst) {
      if (this.tabType) {
        this.setPrintType(this.tabType)
        this.getTableList()
      } else {
        this.$message.error('无权限查看')
      }
    },
    // 初始化下页面权限
    ininPermissions() {
      const permissionVals = ['background_approve.approve_register.pending_list', 'background_approve.approve_register.finish_list', 'background_approve.approve_register.revoke_list']
      let tabList = []
      permissionVals.forEach(role => {
        if (this.allPermissions.includes(role)) {
          switch (role) {
            case 'background_approve.approve_register.pending_list':
              tabList.push({
                label: '待处理',
                key: 'pending'
              })
              break;
            case 'background_approve.approve_register.finish_list':
              tabList.push({
                label: '已同意',
                key: 'finish'
              })
              break;
            case 'background_approve.approve_register.revoke_list':
              tabList.push({
                label: '已拒绝',
                key: 'reject'
              })
              break;
          }
        }
      })
      this.tabType = tabList.length > 0 ? tabList[0].key : ''
      this.tabTypeList = tabList
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      this.currentPage = 1
      this.getTableList()
      this.isFirstSearch = false
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // tab切换
    changeTabHandle(e) {
      this.setPrintType(e)
      this.getTableList()
    },
    // 修改地址栏参数，以防用户刷新后又重置选中的tab
    changeHashHandle() {
      // this.$changehash({})
    },
    // 设置打印列表的type，报表设置中用到
    setPrintType(type) {
      this.searchSetting = SEARCH_FORM_LIST[type]
      this.tableSetting = TABLE_SETTING_LIST[type]
      switch (type) {
        case 'pending':
          this.printType = 'AutoRegisterApprovePending'
          break;
        case 'finish':
          this.printType = 'AutoRegisterApproveFinish'
          break;
        case 'reject':
          this.printType = 'AutoRegisterApproveReject'
          break;
      }
      this.initPrintSetting()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            if (data[key].value instanceof Array) {
              if (data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 格式化请求参数
    formatParams() {
      let params = {
        page: this.currentPage,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchSetting)
      }
      if (this.tabType === 'pending') {
        params.date_type = 'create_time'
      }
      return params
    },
    // 获取待处理订单列表
    async getTableList() {
      const apiList = {
        pending: 'apiBackgroundApproveApproveRegisterPendingListPost',
        finish: 'apiBackgroundApproveApproveRegisterFinishListPost',
        reject: 'apiBackgroundApproveApproveRegisterRevokeListPost'
      }
      this.isLoading = true
      const [err, res] = await to(
        this.$apis[apiList[this.tabType]](this.formatParams())
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取组织下的钱包
    async getOrgWallet() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetOrgWalletPost()
      if (res.code === 0) {
        // res.data.wallet.map(v => {
        //   this.columns.forEach(item => {
        //     if (this.showWalletList[v].includes(item.column)) {
        //       item.show = true
        //     }
        //   })
        // })
        console.log(this.columns)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getTableList()
    },
    // 选中列表的数据
    handleSelectionChange(e) {
      this.dialogFormParams.ids = e.map(v => v.id)
    },
    showDialogHandle(type, data) {
      if (type !== 'approve' && type !== 'reject' && (!this.dialogFormParams.ids || this.dialogFormParams.ids.length === 0)) {
        return this.$message.error('请先选择需要审批的数据！')
      }
      switch (type) {
        case 'mul_approve':
          this.dialogTitle = '批量同意'
          break;
        case 'approve':
          this.dialogTitle = '同意'
          delete this.dialogFormParams.ids
          this.dialogFormParams.id = data.id
          this.infoData = data
          break;
        case 'mul_reject':
          this.dialogTitle = '批量拒绝'
          break;
        case 'reject':
          this.dialogTitle = '拒绝'
          this.dialogFormParams.ids = [data.id]
          break;
      }
      this.dialogType = type
      this.showFormdialog = true
    },
    // 弹窗回调
    confirmAutoRegister(e) {
      // 更新下数据
      this.getTableList()
    },
    cancelAutoRegister() {
      if (this.dialogType === 'reject' || this.dialogType === 'approve') {
        this.dialogFormParams = {
          ids: []
        }
      }
    },
    // 同意审批
    async approveHandle(data, type) {
      // if (data.id) {
      //   this.dialogFormParams = {
      //     ids: [data.id]
      //   }
      //   this.showDialogHandle(type)
      //   return
      // }
      this.$confirm('确定同意申请吗？', `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundApproveApproveRegisterAgreeApprovePost({
                id: data.id
              })
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getTableList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              data.is_enable = !data.is_enable
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 导出报表
    handleExport() {
      let exportApi = ''
      switch (this.tabType) {
        case 'pending':
          exportApi = 'apiBackgroundApproveApproveRegisterPendingListExportPost'
          break;
        case 'finish':
          exportApi = 'apiBackgroundApproveApproveRegisterFinishListExportPost'
          break;
        case 'reject':
          exportApi = 'apiBackgroundApproveApproveRegisterRevokeListExportPost'
          break;
      }
      let params = this.formatQueryParams(this.searchSetting)
      if (this.tabType === 'pending') {
        params.date_type = 'create_time'
      }
      const option = {
        type: this.printType,
        url: exportApi,
        params: {
          page: this.currentPage,
          page_size: 999999,
          ...params
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
  .min-w-100 {
    ::v-deep.el-radio-button__inner {
      min-width: 130px;
    }
  }
</style>
