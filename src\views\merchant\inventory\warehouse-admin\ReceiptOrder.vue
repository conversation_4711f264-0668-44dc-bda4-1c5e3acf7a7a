<template>
  <div class="receipt-order-list container-wrapper">
    <refresh-tool v-if="showRefresh" @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          数据列表
          <span class="inline-block m-l-20 font-size-16">
            当前仓库：
            <span style="color: 000; font-weight: 700">{{ $route.query.warehouse_name }}</span>
          </span>
        </div>
        <div class="align-r">
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #relatedDocument="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="showDrawerHandle('receiving', row)"
              >
                关联单据
              </el-button>
            </template>
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="showDrawerHandle('detail', row)"
              >
                详情
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>

    <!-- 弹窗 -->
    <RelatedDocument
      :showdialog.sync="dialogVisible"
      :type="dialogType"
      orderType="receiving"
      :title="dialogTitle"
      :width="dialogWidth"
      :info-data="dialogInfo"
      :params="dialogParams"
      :api="dialogApi"
      @clickConfirm="searchHandle"
    ></RelatedDocument>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import RelatedDocument from '../components/related-document/RelatedDocument.vue'

export default {
  name: 'ReceiptOrder',
  components: {
    RelatedDocument
  },
  props: {
    showRefresh: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      warehouseId: this.$route.query.warehouse_id,
      tabType: 1,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '单据编号', key: 'trade_no' },
        { label: '关联单据', key: 'relatedDocument', type: 'slot', slotName: 'relatedDocument' },
        { label: '创建时间', key: 'create_time' },
        // { label: '收货时间', key: 'create_time' }, // , isComponents: true, type: 'date', format: 'YYYY-MM-DD'
        { label: '供应商名称', key: 'supplier_manage_name' },
        // { label: '经手人', key: 'expect_arrival_date1' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      searchFormSetting: {
        // date_type: {
        //   type: 'select',
        //   label: '',
        //   value: 'create_time',
        //   maxWidth: '130px',
        //   placeholder: '请选择',
        //   dataList: [
        //     {
        //       label: '创建时间',
        //       value: 'create_time'
        //     },
        //     // {
        //     //   label: '采购时间',
        //     //   value: 'purchase_time'
        //     // }
        //   ]
        // },
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '创建时间',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        }
      },
      dialogType: '', // 弹窗类型
      dialogOrderType: 'deliveryOrder',
      dialogVisible: false,
      dialogTitle: '新建分类',
      dialogWidth: '740px',
      dialogInfo: {},
      dialogParams: {},
      dialogApi: ''
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getReceiptOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getReceiptOrderList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: this.warehouseId,
        // audit_status: 'approve',
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpVendorDataVendorReceivingDataPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getReceiptOrderList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = 'apiBackgroundDrpPurchaseInfoStatusModifyPost'
      let params = {}
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg || '成功')
              this.getReceiptOrderList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    showDrawerHandle(type, row) {
      // this.dialogOrderType = 'receiving'
      // this.dialogParams = {
      //   id: row.id
      // }
      this.dialogInfo = row
      if (type === 'detail') {
        this.dialogType = type
        this.dialogTitle = '详情'
        this.dialogApi = 'apiBackgroundDrpVendorDataVendorReceivingDetailListPost'
      } else {
        this.dialogTitle = '关联单据'
        this.dialogType = 'order'
        this.dialogApi = 'apiBackgroundDrpVendorDataVendorDataInfoPost'
      }
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.receipt-order-list {
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
}
</style>
