<template>
  <div class="UserAccountList container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="refundOriginal" v-permission="['card_service.card_operate.batch_user_withdraw']">原路退款</button-icon>
          <button-icon color="plain" @click="openDialog('offWallet')" v-permission="['card_service.card_user.user_wallet_off']">禁用钱包</button-icon>
          <button-icon color="plain"  @click="openDialog('onWallet')" v-permission="['card_service.card_user.user_wallet_on']">启用钱包</button-icon>
          <button-icon color="origin" type="mul" @click="openDialog('mulRecharge')" v-permission="['card_service.card_operate.batch_recharge']">批量充值</button-icon>
          <button-icon color="plain" type="Import" @click="openImport('ImportRecharge')" v-permission="['card_service.card_operate.batch_import_user_recharge']">导入充值</button-icon>
          <button-icon color="origin" type="mul" @click="openDialog('mulWithdrawal')" v-permission="['card_service.card_operate.person_quit']">批量退户</button-icon>
          <button-icon color="plain" type="Import" @click="openImport('ImportConsumption')" v-permission="['card_service.card_operate.batch_import_user_consume']">导入历史消费</button-icon>
          <button-icon color="plain" type="Import" @click="openImport('ImportExtract')" v-permission="['card_service.card_operate.batch_import_user_draw']">导入提现</button-icon>
          <button-icon color="plain" type="Import" @click="openImport('ImportWithdrawal')" v-permission="['card_service.card_operate.import_person_quit']">导入退户</button-icon>
          <button-icon color="plain" type="export" @click="agreementGotoExport" v-permission="['card_service.card_user.account_list_export']">导出账户记录</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableDataRef"
          style="width: 100%"
          stripe
          row-key="id"
          :row-class-name="tableRowClassName"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" :reserve-selection="true" width="40" align="center" class-name="ps-checkbox" ></el-table-column>
          <el-table-column prop="name" min-width="85px" label="姓名" align="center" ></el-table-column>
          <el-table-column prop="person_no" label="人员编号" align="center" ></el-table-column>
          <el-table-column prop="organization_alias" label="来源" align="center" ></el-table-column>
          <el-table-column prop="phone" label="手机号码" align="center" ></el-table-column>
          <el-table-column prop="card_no" label="卡号" align="center" ></el-table-column>
          <el-table-column prop="card_status_alias" label="卡状态" align="center" >
            <template slot-scope="scope">
              <span :class="setCloumnTextColor(scope.row)">{{scope.row.card_status_alias}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="card_department_group_alias" label="部门" align="center" ></el-table-column>
          <el-table-column prop="card_user_group_alias" label="分组" align="center" ></el-table-column>
          <el-table-column prop="balance" label="储值钱包余额" align="center">
            <template slot-scope="scope">
              <div :style="{ color: isDisplayString(scope.row.display_balance) ? '#ccc' : '' }">{{ scope.row.balance_total }}</div>
              <el-popover placement="top-start" title="" width="180"  trigger="hover">
                <template>
                  <div class="popover-box" v-for="(item,index) in scope.row.wallet_balance" :key="index">
                    <span>{{item.source_organization}}</span>
                    <span>￥{{ item.balance}}</span>
                  </div>
                </template>
                <el-button type="text" size="mini" style="padding:0;" class="ps-text" slot="reference"
                  >查看更多</el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="subsidy_balance_total" label="补贴钱包余额" align="center">
            <template slot-scope="scope">
              <div :style="{ color: isDisplayString(scope.row.display_subsidy_balance) ? '#ccc' : '' }">{{ scope.row.subsidy_balance_total }}</div>
              <el-popover placement="top-start" title="" width="180" trigger="hover">
                <template>
                  <div class="popover-box" v-for="(item,index) in scope.row.wallet_subsidy_balance" :key="index">
                    <span>{{item.source_organization}}</span>
                    <span>￥{{ item.subsidy_balance}}</span>
                  </div>
                </template>
                <el-button type="text" size="mini" style="padding:0;" class="ps-text" slot="reference"
                  >查看更多</el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="complimentary_balance" label="赠送钱包余额" align="center">
            <template slot-scope="scope">
              <div :style="{ color: isDisplayString(scope.row.display_complimentary_balance) ? '#ccc' : '' }">{{ scope.row.complimentary_balance}}</div>
              <el-popover placement="top-start" title="" width="180" trigger="hover">
                <template>
                  <div class="popover-box" v-for="(item,index) in scope.row.wallet_complimentary_balance" :key="index">
                    <span>{{item.source_organization}}</span>
                    <span>￥{{ item.complimentary_balance}}</span>
                  </div>
                </template>
                <el-button type="text" size="mini" style="padding:0;" class="ps-text" slot="reference"
                  >查看更多</el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="account_status_alias" label="账户状态" align="center" >
            <template slot-scope="scope">
               <span :style="{color:(scope.row.person_status =='ENABLE'?'#56ba58':'#02a7f0')}">{{ scope.row.account_status_alias}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="total_rechange_fee" label="累计充值" align="center" >
            <template slot-scope="scope">
               {{ scope.row.total_rechange_fee | formatMoney }}
            </template>
          </el-table-column>
          <el-table-column prop="total_draw_fee" label="提现" align="center" >
            <template slot-scope="scope">
               {{ scope.row.total_draw_fee | formatMoney }}
            </template>
          </el-table-column>
          <el-table-column prop="flat_cost_fee" label="工本费" align="center" >
            <template slot-scope="scope">
               {{ scope.row.flat_cost_fee | formatMoney }}
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="230px" align="center">
            <template slot-scope="scope">
              <!-- <el-button type="text" size="small" @click="goToConsumptionFrom(scope.row)">明细</el-button> -->
              <el-button type="text" size="small" class="ps-green-text" :disabled="!isCurrentOrgs(scope.row.organization)" @click="openDialog('recharge', scope.row)" v-permission="['card_service.card_operate.recharge']">充值</el-button>
              <el-button type="text" size="small" class="delete-txt-btn" :disabled="!isCurrentOrgs(scope.row.organization)" @click="openDialog('extract', scope.row)" v-permission="['card_service.card_operate.withdrawal']">提现</el-button>
              <!-- <el-button type="text" size="small" class="ps-red" :disabled="!isCurrentOrgs(scope.row.organization)" @click="openDialog('refund', scope.row)">退款</el-button> -->
              <!-- <el-button type="text" size="small" class="ps-red" :disabled="!isCurrentOrgs(scope.row.organization)" @click="openDialog('writeOff', scope.row)">冲销</el-button> -->
              <el-button type="text" size="small" class="ps-red" :disabled="!isCurrentOrgs(scope.row.organization)" @click="openDialog('withdrawal', scope.row)" v-permission="['card_service.card_operate.person_quit']">退户</el-button>
              <el-button type="text" size="small" class="ps-text" :disabled="!isCurrentOrgs(scope.row.organization)" @click="openDialog('subsidy', scope.row)" v-permission="['card_service.card_operate.card_charge_off_detail']">补贴冲销</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 end -->
    <!-- 导入数据的弹窗 start -->
    <import-dialog-drawer
      :templateUrl="templateUrl"
      :tableSetting="tableSetting"
      :show.sync="showDialog"
      :title="dialogTitle"
      :openExcelType="openExcelType"
    ></import-dialog-drawer>
    <!-- 导入数据的弹窗 end -->
    <!-- 充值、提现、冲销、退款、退户弹窗 -->
    <account-dialog
      v-if="accountDialogVisible"
      :loading="accountDialogLoading"
      :isshow.sync="accountDialogVisible"
      :type="accountDialogType"
      :title="accountDialogTitle"
      :account-info="accountInfo"
      @confirm="searchHandle"
      :width="accountDialogwidth"
      :selectListData="selectListData"
      :tableSelectNum="tableSelectNum"
    />
    <!-- 原路退款 弹窗 start -->
    <el-dialog
      title="退款渠道"
      :visible.sync="showDrawDialog"
      width="460px"
      top="15vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      center
    >
      <div class="draw-dialog-content">
        <el-form ref="drawFormRef" v-loading="drawFormLoading" :rules="drawFormRuls" :model="drawFormData" class="draw-dialog-form" label-width="0">
          <el-form-item prop="payinfoId" label="">
            <el-radio-group v-model="drawFormData.payinfoId">
              <div v-for="item in drawInfoList" :key="item.id" class="m-b-10">
                <el-radio  :label="item.id">退款至{{ item.payway_full_alias }}</el-radio>
              </div>
              </el-radio-group>
            <div v-if="drawInfoList.length === 0" class="text-center">暂无数据</div>
          </el-form-item>
          <div class="red">注：仅支持全额退款</div>
        </el-form>

      </div>
      <span slot="footer" class="draw-dialog-footer">
        <el-button class="ps-cancel-btn" type="primary" :disabled="drawFormLoading" @click="canceDrawDialog">取消</el-button>
        <el-button class="ps-btn" type="primary" :disabled="drawFormLoading" @click="submitDrawDialog">确定</el-button>
      </span>
    </el-dialog>
    <!-- 原路退款 弹窗 end -->
    <!-- 协议弹窗 -->
    <dialog-message center width="600px" title="提示" :show.sync="agreementDialog" customClass="expire-dialog">
      <div style="margin-bottom: 20px;">是否导出当前列表？</div>
      <div class="flex flex-align-center">
        <el-radio v-model="isAgreement" label="agreement" style="margin-right: 0; display: flex;align-items: center;">
          已阅读并同意
        </el-radio>
        <el-link type="primary" @click="gotoAgreement" style="margin-left: 5px;">《{{agreementInfo.agreement_name}}》</el-link>
      </div>
      <template slot="tool">
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right">
          <el-button class="ps-cancel-btn" @click="agreementDialog = false">取消</el-button>
          <el-button :disabled="!isAgreement" :loading="exportLoading" class="ps-btn" type="primary" @click="confirmDialogAgreement">确定</el-button>
        </div>
      </template>
    </dialog-message>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, formatWallet, divide } from '@/utils'
import { isCurrentOrgs } from './utils'
import accountDialog from './components/accountDialog.vue'

export default {
  name: 'UserAccountList',
  components: { accountDialog },
  props: {},
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSelectNum: 0, // 勾选列表个数
      searchFormSetting: {
        card_no: {
          type: 'input',
          label: '卡号',
          value: '',
          placeholder: '请输入卡号'
        },
        person_name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        phone: {
          type: 'input',
          label: '手机号',
          value: '',
          placeholder: '请输入手机号'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        },
        card_user_group_ids: {
          type: 'select',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          dataList: [],
          listNameKey: 'group_name',
          listValueKey: 'id',
          multiple: true,
          collapseTags: true,
          filterable: true
        },
        card_department_group_id: {
          type: 'treeselect',
          multiple: false,
          flat: false,
          label: '部门',
          value: null,
          placeholder: '请选择部门',
          dataList: [],
          limit: 1,
          level: 1,
          normalizer: this.departmentNode
        },
        card_status: {
          type: 'select',
          label: '卡状态',
          value: [],
          placeholder: '请选择卡状态',
          multiple: true,
          collapseTags: true,
          dataList: [
            // {
            //   label: '全部',
            //   value: ''
            // },
            {
              label: '挂失',
              value: 'LOSS'
            }, {
              label: '退卡',
              value: 'QUIT'
            }, {
              label: '使用中',
              value: 'ENABLE'
            }, {
              label: '未发卡',
              value: 'UNUSED'
            }
          ]
        },
        account_status: {
          type: 'select',
          label: '账户状态',
          value: '',
          placeholder: '请选择账户状态',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '使用中',
            value: 'ENABLE'
          },
          // {
          //   label: '退户',
          //   value: 'PERSON_QUIT'
          // },
          {
            label: '冻结中',
            value: 'FREEZE'
          }]
        }
      },
      dialogTitle: '',
      showDialog: false,
      templateUrl: '',
      openExcelType: '',
      tableSetting: [],
      accountDialogLoading: false, // 充值、提现、冲销弹窗
      accountDialogVisible: false,
      accountDialogType: '',
      accountDialogTitle: '',
      accountDialogwidth: '',
      accountInfo: {}, // 弹窗数据
      selectListId: [], // 选择的数据
      selectListData: [], // 目前用于禁用钱包、启用钱包
      drawInfoList: [], // 原路退款-退款渠道
      showDrawDialog: false, // 原路退款
      drawFormLoading: false,
      drawFormData: {
        payinfoId: ''
      },
      drawFormRuls: {
        payinfoId: [{ required: true, message: '请选择退款渠道', trigger: 'blur' }]
      },
      agreementDialog: false, // 协议弹窗
      isAgreement: false, // 是否同意
      exportLoading: false,
      agreementInfo: { // 协议对象
        id: '',
        agreement_name: ''
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getCardAccountList()
      this.userGroupList()
      this.getDepartmentList()
      this.getDrawInfoList()
      this.getAgreementMessage()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.accountDialogVisible = false
        this.currentPage = 1;
        this.$refs.tableDataRef.clearSelection()
        this.getCardAccountList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getCardAccountList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserAccountListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = []
        res.data.results.map(item => {
          item.card_user_group_alias = item.card_user_group_alias.join('，')
          item.organization_alias = item.organization_alias.join('，')
          // 格式化下钱包
          item = formatWallet(item)
          // 计算一下提现后余额
          item = this.formatWithdraw(item)
          // // 退户的不显示
          if (item.account_status_alias !== '退户') {
            this.tableData.push(item)
          }
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置卡状态文字颜色
    setCloumnTextColor(row) {
      let style = [];
      switch (row.card_status) {
        case "LOSS": // 挂失
          style = ["loss"];
          break;
        case "FREEZE": // 冻结中
          style = ["blue"];
          break;
        case "QUIT": // 退卡
          style = ["warn"];
          break;
        case "ENABLE": // 使用中
          style = ["success"];
          break;
        case "UNUSED": // 未发卡
          style = ["warn"];
          break;
        default:
          style = ["success"];
          break;
      }
      return style;
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getCardAccountList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getCardAccountList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.tableSelectNum = val.length
      this.selectListId = []
      this.selectListData = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
        this.selectListData.push(item)
      })
    },
    openDialog(type, data) {
      let canOpen = true
      this.accountInfo = data
      this.accountDialogType = type
      switch (type) {
        case 'recharge':
          this.accountDialogTitle = '充值'
          this.accountDialogwidth = '520px'
          break;
        case 'extract':
          this.accountDialogTitle = '提现'
          this.accountDialogwidth = '600px'
          break;
        case 'writeOff':
          this.accountDialogTitle = '冲销'
          this.accountDialogwidth = '520px'
          break;
        case 'refund':
          this.accountDialogTitle = '退款'
          this.accountDialogwidth = '700px'
          break;
        case 'withdrawal':
          this.accountDialogTitle = '退户'
          this.accountDialogwidth = '400px'
          break;
        case 'subsidy':
          this.accountDialogTitle = '补贴冲销'
          this.accountDialogwidth = '580px'
          break;
        case 'mulRecharge':
          if (!this.selectListId.length) {
            canOpen = false
            return this.$message.error('请先选择数据！')
          }
          this.accountDialogTitle = '批量充值'
          this.accountDialogwidth = '400px'
          this.accountInfo = {}
          this.accountInfo.rechargeIds = this.selectListId
          break;
        case 'mulWithdrawal':
          if (!this.selectListId.length) {
            canOpen = false
            return this.$message.error('请先选择数据！')
          }
          this.accountDialogTitle = '批量退户'
          this.accountDialogwidth = '400px'
          this.accountInfo = {}
          this.accountInfo.withdrawalIds = this.selectListId
          break;
        case 'offWallet':
          if (!this.selectListId.length) {
            canOpen = false
            return this.$message.error('请先选择数据！')
          }
          if (this.selectListData.length === 1) {
            this.accountDialogTitle = `禁用钱包（${this.selectListData[0].name}）`
          } else {
            this.accountDialogTitle = `禁用钱包（${this.selectListData.length}）`
          }
          this.accountDialogwidth = '580px'
          this.accountInfo = {}
          this.accountInfo.selectListId = this.selectListId
          this.accountInfo.selectListData = this.selectListData
          break;
        case 'onWallet':
          if (!this.selectListId.length) {
            canOpen = false
            return this.$message.error('请先选择数据！')
          }
          if (this.selectListData.length === 1) {
            this.accountDialogTitle = `启用钱包（${this.selectListData[0].name}）`
          } else {
            this.accountDialogTitle = `启用钱包（${this.selectListData.length}）`
          }
          this.accountDialogwidth = '580px'
          this.accountInfo = {}
          this.accountInfo.selectListId = this.selectListId
          this.accountInfo.selectListData = this.selectListData
          break;
      }
      console.log('选择的数据', data)
      if (canOpen) this.accountDialogVisible = true
    },
    openImport(type) {
      this.showDialog = true
      switch (type) {
        case 'ImportRecharge':
          this.dialogTitle = '导入充值'
          this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入充值.xls'
          this.openExcelType = type
          this.tableSetting = [
            { key: 'name', label: '姓名' },
            { key: 'person_no', label: '人员编号' },
            { key: 'balance', label: '充值金额(钱区)' },
            { key: 'remark', label: '充值备注' }
          ]
          break;
        case 'ImportWithdrawal':
          this.dialogTitle = '导入退户'
          this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入退户.xls'
          this.openExcelType = type
          this.tableSetting = [
            { key: 'name', label: '姓名' },
            { key: 'person_no', label: '人员编号' },
            { key: 'type', label: '退户方式' }
          ]
          break;
        case 'ImportConsumption':
          this.dialogTitle = '导入历史消费'
          this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入消费.xls'
          this.openExcelType = type
          this.tableSetting = [
            { key: 'name', label: '姓名' },
            { key: 'person_no', label: '人员编号' },
            { key: 'pay_time', label: '支付时间' },
            { key: 'meal_type', label: '餐段' },
            { key: 'order_fee', label: '订单金额（元）' },
            { key: 'discount_type', label: '优惠类型' },
            { key: 'discount_fee', label: '优惠金额（元）' },
            { key: 'pay_fee', label: '实收金额（元）' },
            { key: 'org', label: '消费点' },
            { key: 'remark', label: '备注' }
          ]
          break;
        case 'ImportExtract':
          this.dialogTitle = '导入提现'
          this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入提现.xls'
          this.openExcelType = type
          this.tableSetting = [
            { key: 'name', label: '姓名' },
            { key: 'person_no', label: '人员编号' },
            { key: 'balance', label: '提现方式' }
          ]
          break;
      }
    },
    // 获取分组信息
    async userGroupList() {
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        page: 1,
        page_size: *********99
      })
      if (res.code === 0) {
        this.groupList = res.data.results
        this.searchFormSetting.card_user_group_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取部门信息
    async getDepartmentList() {
      const res = await this.$apis.apiCardServiceCardDepartmentGroupTreeListPost()
      if (res.code === 0) {
        this.searchFormSetting.card_department_group_id.dataList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    departmentNode(node) {
      return {
        id: node.id,
        label: node.group_name,
        children: node.children_list
      }
    },
    goToConsumptionFrom(data) {
      // this.$router.push({
      //   name: 'consumptionFrom',
      //   query: {
      //     person_no: data.person_no,
      //     name: data.name,
      //     all_operation: true
      //   }
      // })
    },
    // 跳转链接
    gotoAgreement() {
      let url = window.location.origin + '/#/agreement?type=' + 'ACCOUNT' + '&key=AGREEMENTLIST'
      window.open(url, '_blank')
    },
    // 确认协议导出
    async confirmDialogAgreement() {
      try {
        const option = {
          type: 'UserAccountList',
          immediate: true,
          params: {
            ...this.formatQueryParams(this.searchFormSetting),
            agreement_id: this.agreementInfo.id,
            page: 1,
            page_size: *********
          }
        }
        this.exportLoading = true
        await this.exportHandle(option)
        this.exportLoading = false
      } catch (error) {
        this.exportLoading = false
      }
    },
    // 协议弹窗
    agreementGotoExport() {
      this.agreementDialog = true
    },
    // 获取协议详情
    async getAgreementMessage() {
      const params = {
        agreement_type: 'ACCOUNT'
      }
      const [err, res] = await this.$to(this.$apis.getAgreementMessage(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        if (res.data) {
          this.agreementInfo = res.data
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出
    gotoExport() {
      const option = {
        type: "UserAccountList",
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 计算提金额
    formatWithdraw(item) {
      if (!item) {
        return {}
      }
      if (Reflect.has(item, "current_org_wallet") && typeof item.current_org_wallet === "object") {
        item.current_balance = item.current_org_wallet.balance ? divide(item.current_org_wallet.balance) : '0.00'
        // 这个值跟赠送的值永远是0. 产品海俊说写死就可以
        item.after_balance = "0.00"
        item.current_subsidy_balance = item.current_org_wallet.subsidy_balance ? divide(item.current_org_wallet.subsidy_balance) : "0.00"
        item.current_complimentary_balance = '0.00'
        console.log("item.current_subsidy_balance", item.current_org_wallet.complimentary_balance, item.current_subsidy_balance, " item.current_complimentary_balance", item.current_org_wallet.complimentary_balance, item.current_complimentary_balance);
      }
      return item
    },
    isCurrentOrgs,
    isDisplayString (value) {
      return typeof value === 'string';
    },
    // 原路退款二次弹窗（废弃）
    refundOriginal() {
      if (!this.selectListId.length) {
        this.$message.error('请先选择数据！')
        return
      }
      this.showDrawDialog = true
    },
    // 获取退款渠道
    async getDrawInfoList() {
      const [err, res] = await this.$to(this.$apis.apiCardServiceCardOperateWithdrawInfoPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.drawInfoList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重置表单
    resetDrawForm() {
      this.drawFormData = {
        refundPay: ''
      }
    },
    // 弹窗取消
    canceDrawDialog() {
      this.resetDrawForm()
      this.showDrawDialog = false
    },
    // 弹窗确定
    submitDrawDialog() {
      this.$refs.drawFormRef.validate(valid => {
        if (valid) {
          this.sendDrawFormHandle()
        } else {
          // console.log('error submit!!');
        }
      })
    },
    // 原路退款弹窗确定
    sendDrawFormHandle: debounce(async function() {
      if (this.drawFormLoading) return
      this.drawFormLoading = true
      // await this.$sleep(500)
      // const [err, res] = [null, {code: 0}]
      const [err, res] = await this.$to(this.$apis.apiCardServiceCardOperateBatchUserWithdrawPost({
        card_info_ids: this.selectListId,
        payinfo_id: this.drawFormData.payinfoId
      }))
      this.drawFormLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg || '成功')
        // 下载excel
        if (res.data.url) {
          window.open(res.data.url, '_blank')
        }
        this.resetDrawForm()
        this.showDrawDialog = false
        this.getCardAccountList()
      } else {
        this.$message.error(res.msg)
      }
    }, 500)
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>

<style lang="scss">
.draw-dialog-content {
  position: relative;
  display: flex;
  justify-content: center;
  .draw-dialog-form {
    display: inline-block;
    // margin-left: 100px;
    .el-radio__label {
      margin-left: 10px;
    }
  }
}
</style>
