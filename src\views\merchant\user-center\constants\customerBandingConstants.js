// 客户号绑定表单表头
export const TABLE_HEAD_DATA_CUSTOMER_BINDING = [
  { label: '客户号', key: 'client_number' },
  { label: '人员编号', key: 'person_no' },
  { label: '姓名', key: 'name' },
  { label: '性别', key: 'gender_alias' },
  { label: '部门', key: 'department_group_alias', type: "slot", slotName: "departmentGroups" },
  { label: '分组', key: 'card_user_groups_alias', type: "slot", slotName: "userGroups" },
  { label: '绑定时间', key: 'update_time' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "100" }
]

// 客户号绑定筛选设置
export const SEARCH_FORM_SET_DATA_CUSTOMER_BINDING = {
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入'
  },
  client_number: {
    type: 'input',
    value: '',
    label: '客户号',
    placeholder: '请输入'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入'
  },
  card_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    isLazy: false,
    checkStrictly: true,
    label: '部门',
    value: [],
    clearabe: true,
    placeholder: '请选择部门'
  },
  card_user_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    clearabe: true,
    collapseTags: true
  }
}

// 导入客户号绑定 表单表头
export const TABLE_HEAD_DATA_IMPORT_CUSTOMER = [
  { label: '客户号', key: 'no' },
  { label: '人员编号', key: 'person_no' }
]

// 模板链接
export const URL_TEMPLATE_MODEL = '/api/temporary/template_excel/银行客户号导入.xlsx'

// 出入方向字典
export const DIC_IN_OUT_DIRECTION = [
  { name: '全部', value: "" },
  { name: '入场', value: 'in' },
  { name: '出场', value: 'out' }
]

// 停车类型字典
export const DIC_PARK_TYPE = [
  { name: '全部', value: "" },
  { name: '会员', value: 'member' },
  { name: '长期', value: 'long_time' },
  { name: '临停', value: 'temporary' },
  { name: '长期过期', value: 'long_timeout' },
  { name: '长期超员', value: 'long_time_max' }
]
