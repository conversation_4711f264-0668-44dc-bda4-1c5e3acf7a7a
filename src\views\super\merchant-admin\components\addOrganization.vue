<template>
  <div class="container-wrapper super-add-organization is-fixed-footer">
    <!-- tab end -->
    <el-form
      ref="organizationFormRef"
      v-loading="isLoading"
      :rules="formDataRuls"
      :model="formData"
      class="organization-form-wrapper"
      size="small"
    >
      <div v-if="operate === 'add'" class="add-title">添加组织层级</div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">基本信息</span>
        <el-button v-if="!checkIsFormStatus" @click="changeOperate" size="mini" class="float-r">编辑</el-button>
      </div>
      <div class="item-box clearfix">
        <div v-if="labelName" class="item-b-l">{{ labelName }}</div>
        <div :class="{'item-b-r': labelName}">
          <el-form-item class="block-label" label="组织名称：" prop="name">
            <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.name"></el-input>
            <div class="item-form-text" v-else>{{formData.name}}</div>
          </el-form-item>
        </div>
      </div>
      <!-- 当前组织层次 -->
      <el-form-item class="block-label form-item-box" label="当前组织层次：" prop="levelName">
        <!-- <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.levelName"></el-input> -->
        <el-select v-if="checkIsFormStatus" v-model="formData.levelTag" placeholder="" class="ps-select" style="width:100%;">
          <el-option v-for="option in levelList" :key="option.level" :label="option.name" :value="option.level"></el-option>
        </el-select>
        <div class="item-form-text" v-else>{{formData.levelName}}</div>
      </el-form-item>

        <el-row class="form-item-row-box" :gutter="24">
          <!-- 行业性质 -->
          <el-col class="block-label form-item-box" :span="12">
            <el-form-item label="行业性质：" prop="industry">
              <el-select
                v-model="formData.industry"
                placeholder="请选择行业性质"
                class="ps-select"
                style="width: 100%;"
                size="small"
                :disabled="!checkIsFormStatus"
              >
                <el-option
                  v-for="item in industryTypeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 所在地址 -->
          <el-col class="block-label form-item-box" :span="12">
            <el-form-item label="所在地址：" prop="district">
              <el-cascader
                size="small"
                :options="addrOptions"
                v-model="formData.district"
                style="display: block;"
                :disabled="!checkIsFormStatus"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
      <!-- parentData.level === 5 || treeData.level === 5 -->
      <!-- <el-form-item v-if="formData.levelTag === 5" class="block-label form-item-box" label="退款密码：" prop="refundPassword">
        <el-input :disabled="!checkIsFormStatus" placeholder="不填则使用默认" class="ps-input" size="small" v-model="formData.refundPassword"></el-input>
      </el-form-item> -->

      <div class="form-line"></div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">联系方式</span>
      </div>

      <el-row class="form-item-row-box" :gutter="24">
        <!-- 联系人 -->
        <el-col :span="12">
          <el-form-item class="block-label" label="联系人：" prop="contact">
            <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.contact"></el-input>
            <div class="item-form-text" v-else>{{formData.contact}}</div>
          </el-form-item>
        </el-col>
        <!-- 手机号码 -->
        <el-col :span="12">
          <el-form-item class="block-label" label="手机号码：" prop="mobile">
            <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.mobile"></el-input>
            <div class="item-form-text" v-else>{{formData.mobile}}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 备注 -->
      <el-form-item class="block-label form-item-box" label="备注：" prop="remark">
        <el-input v-if="checkIsFormStatus" class="ps-input" v-model="formData.remark" type="textarea" :rows="3"></el-input>
        <div class="item-form-text" v-else>{{formData.remark}}</div>
      </el-form-item>

      <div class="form-line"></div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">其它设置</span>
      </div>
      <!-- 钱包设置 -->
      <el-form-item class="block-label form-item-box" label="钱包设置" prop="">
        <el-checkbox :disabled="!checkIsFormStatus" class="ps-checkbox" v-model="formData.storeWalletOn">储值钱包</el-checkbox>
        <el-checkbox :disabled="!checkIsFormStatus" class="ps-checkbox" v-model="formData.electronicWalletOn">电子钱包</el-checkbox>
        <el-checkbox :disabled="!checkIsFormStatus" class="ps-checkbox" v-model="formData.subsidyWalletOn">补贴钱包</el-checkbox>
        <el-checkbox :disabled="!checkIsFormStatus" class="ps-checkbox" v-model="formData.complimentaryWalletOn">优惠钱包</el-checkbox>
        <el-checkbox :disabled="!checkIsFormStatus" class="ps-checkbox" v-model="formData.otherWalletOn">第三方钱包</el-checkbox>
      </el-form-item>
      <!-- <el-form-item class="block-label form-item-box" label="" prop="">
        钱包扣款规则
        <el-radio-group v-model="formData.isWalletPayOrderAsc" :disabled="!checkIsFormStatus" class="ps-radio">
          <el-radio :label="false">扣上级钱包余额</el-radio>
          <el-radio :label="true">扣下级钱包余额</el-radio>
        </el-radio-group>
        <div style="margin-left: 88px;color: #ff9b45">当订单所属组织余额不足时将扣取余额充足的上级/下级钱包金额;该规则仅适合线下消费</div>
      </el-form-item> -->
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span >
          组合支付
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.combineWalletOn"
            active-color="#ff9b45"
          ></el-switch>
        </span>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span >
          是否农行项目点展示
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.isAbcProject"
            active-color="#ff9b45"
          ></el-switch>
        </span>
      </el-form-item>
      <div v-if="checkIsFormStatus" class="form-footer">
        <el-button @click="cancelFormHandle" size="small">取消</el-button>
        <el-button @click="sendFormdataHandle" class="ps-origin-btn" type="primary" size="small" >保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { to, debounce, camelToUnderline } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { regionData } from 'element-china-area-data'
import industryType from '@/assets/data/industryType.json'
import { validateName } from '@/assets/js/validata'
import md5 from 'js-md5';

export default {
  name: 'SuperAddOrganization',
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: { // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    parentData: Object,
    treeData: Object,
    id: [String, Number],
    operate: String,
    restoreHandle: Function
  },
  data() {
    let validatePass = (rule, value, callback) => {
      let regPass = /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/
      // let regPass = /(^\w{6,32}$)/;
      if (value && !regPass.test(value)) {
        callback(new Error("退款密码为数字与字母的组合，长度8到20位"));
      } else {
        callback();
      }
    };
    return {
      labelName: '',
      formOperate: 'detail',
      isLoading: false,
      industryTypeList: industryType,
      addrOptions: regionData,
      formData: {
        id: '',
        name: '',
        levelName: '',
        levelTag: '',
        // permission: [],
        url: '',
        district: [],
        contact: '',
        mobile: '',
        mailAddress: '',
        tel: '',
        industry: '',
        remark: '',
        // refundPassword: '',
        storeWalletOn: false,
        electronicWalletOn: false,
        subsidyWalletOn: false,
        complimentaryWalletOn: false,
        combineWalletOn: false,
        otherWalletOn: false,
        smsTemplateId: '',
        isAbcProject: false
        // isWalletPayOrderAsc: false
      },
      formDataRuls: {
        name: [{ required: true, message: '组织名称不能为空', trigger: "blur" },
          { validator: validateName, trigger: 'blur' }],
        level_name: [{ required: true, message: '层级名称不能为空', trigger: "blur" }],
        // mobile: [{ required: true, validator: validateTelphone, trigger: "blur" }],
        refundPassword: [{ validator: validatePass, trigger: "blur" }],
        district: [{ required: true, message: '所在地址不能为空', trigger: ['blur', 'change'] }]
      },
      levelList: [],
      permissionTree: [],
      loadingThirdInfo: false
    }
  },
  computed: {
    // formOperate: {
    //   get() {
    //     return this.operate
    //   },
    //   set(val) {
    //     // this.$emit('update:operate', val)
    //   }
    // },
    // 检查当前状态，编辑还是详情
    checkIsFormStatus: function() { // 默认为false
      let show = false
      switch (this.operate) { // 目前从父组件传过来的操作类型只会有2个add和detail
        case 'add':
          show = true
          break;
        case 'detail':
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break;
        default: // 没传的话
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break;
      }
      return show
    }
  },
  watch: {
    operate: function(val, old) {
      if (!val) {
        this.formOperate = 'detail'
      }
      this.initLoad()
    }
  },
  created() {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      if (this.operate) {
        this.formOperate = this.operate
      }
      if (this.operate === 'add') {
        this.getLevelList(this.parentData.company)
        this.formData.parent = this.parentData.id
        this.formData.company = this.parentData.company
      } else {
        this.getLevelList(this.treeData.company)
        this.labelName = this.treeData.name.substring(0, 1)
      }
      this.initInfoHandle()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
    }, 300),
    // 详情信息赋值
    initInfoHandle() {
      for (let key in this.formData) {
        let val = this.infoData[camelToUnderline(key)]
        if (val) {
          switch (key) {
            case 'industry':
              this.formData[key] = val.toString()
              break;
            case 'district':
              this.formData[key] = val ? JSON.parse(val) : []
              break;
            case 'refundPassword':
              this.formData[key] = val
              break;
            default:
              this.formData[key] = val
              break;
          }
        }
      }
    },
    // 处理下没有children_list
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (_that.checkIsFormStatus) {
            item.isDisabled = false
          } else {
            item.isDisabled = true
          }
          if (item[key]) {
            if (item[key].length > 0) {
              traversal(item[key])
            } else {
              _that.$delete(item, key)
            }
          } else {
            _that.$delete(item, key)
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 获取指定公司的层级列
    async getLevelList(companyId) {
      let params = {}
      if (companyId) {
        params.company_id = companyId
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationGetLevelNameMapPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.levelList = []
        if (res.data.length > 0) {
          res.data.forEach(item => {
            if (this.formOperate === 'add') {
              if (item.level === (this.parentData.level_tag + 1)) {
                this.formData.levelName = item.name
                this.formData.levelTag = item.level
              }
              if (item.level > this.parentData.level_tag) {
                this.levelList.push(item)
              }
            } else {
              if (item.level >= this.treeData.level_tag) {
                this.levelList.push(item)
              }
            }
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    changeOperate() {
      switch (this.operate) { // 目前从父组件传过来的操作类型只会有2个add和detail
        case 'add': // noth
          break;
        default:
          if (this.formOperate === 'detail') {
            this.formOperate = 'modify'
          } else {
            this.formOperate = 'detail'
          }
          break;
      }
      this.permissionTree = this.deleteEmptyChildren(this.permissionTree, 'children')
    },
    // 取消
    cancelFormHandle() {
      if (this.operate === 'add') {
        this.$refs.organizationFormRef.resetFields() // 重置表单数据
      } else {
        this.$refs.organizationFormRef.clearValidate() // 清空表单校验
        this.formOperate = 'detail'
        this.permissionTree = this.deleteEmptyChildren(this.permissionTree, 'children')
      }
      this.restoreHandle(this.type, this.formOperate)
    },
    // 生成第三方配置
    async generateThirdAppinfo() {
      this.loadingThirdInfo = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationGenerateThirdAppinfoPost({
        id: this.id
      }))
      this.loadingThirdInfo = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.formData.thirdAppKey = res.data.third_app_key
        this.formData.thirdSecretKey = res.data.third_secret_key
      } else {
        this.$message.error(res.msg)
      }
    },
    // 发送请求
    sendFormdataHandle() {
      this.$refs.organizationFormRef.validate(valid => {
        if (valid) {
          if (this.operate === 'add') { // 添加
            this.addRootOrganization(this.formatData())
          } else { // 编辑
            this.modifyOrganization(this.formatData())
          }
        }
      })
    },
    // 格式化下传给后台的参数
    formatData() {
      let params = {
        status: 'enable'
      }
      for (let key in this.formData) {
        let val = this.formData[key]
        if (val !== '') {
          switch (key) {
            case 'district':
              val = JSON.stringify(val)
              break;
            case 'password':
              // val = md5(val)
              break;
            case 'refundPassword':
              val = md5(val)
              break;
            case 'thirdAppUrl':
              val = encodeURIComponent(val)
              break;
          }
          if (key !== 'levelName') {
            params[camelToUnderline(key)] = val
          }
        }
      }
      if (this.formOperate === 'modify') {
        params.company = this.treeData.company
      }
      return params
    },
    // 根添加
    async addRootOrganization(params) {
      this.isLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.formOperate = 'detail'
        this.$message.success('添加成功')
        this.$refs.organizationFormRef.clearValidate() // 清空表单校验
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 根编辑
    async modifyOrganization(params) {
      this.isLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.formOperate = 'detail'
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.super-add-organization {
  position: relative;
  .add-title{
    font-size: 18px;
    text-align: center;
    font-family: 600;
  }
  .min-title-h{
    height: 28px;
    line-height: 28px;
  }
  .item-box{
    // display: flex;
    padding: 10px 0 0 !important;
    .item-b-l{
      // display: flex;
      // justify-content: center;
      // align-items: center;
      float: left;
      width: 56px;
      height: 56px;
      line-height: 56px;
      text-align: center;
      vertical-align: middle;
      background-color: #ff9b45;
      border-radius: 8px;
      font-size: 30px;
      letter-spacing: 2px;
      color: #ffffff;
    }
    .item-b-r{
      margin-left: 76px;
    }
    .item-text-box{
      display: flex;
      padding: 5px 0;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 24px;
      letter-spacing: 1px;
      color: #23282d;
      .item-label{
        opacity: 0.7;
      }
      .item-text{
        flex: 1;
      }
    }
  }
  .organization-form-wrapper {
    width: 100%;
    max-width: 800px;
    .block-label{
      width: 100%;
      .el-form-item__label{
        display: block;
        text-align: left;
        line-height: 1.5;
        float: none;
      }
    }
    .form-line{
      width: 100%;
      height: 1px;
      background-color: #e0e6eb;
    }
    .block-center{
      text-align: center;
    }
    .item-form-text{
      padding: 0 15px;
      color: #23282d;
      font-weight: bold;
      min-height: 32px;
      border: 1px solid #e0e6eb;
      opacity: 0.7;
      background-color: #fff;
    }
  }
  .form-footer{
    text-align: center;
    .el-button{
      width: 120px;
    }
  }
  &.is-fixed-footer{
    // padding-bottom: 30px;
    .form-footer{
      margin-top: 30px;
      // position: fixed;
      // padding: 10px 20px;
      // left: 263px;
      // right: 40px;
      // bottom: 0;
      // background-color: #fff;
      // box-shadow: -4px 0px 6px 0px rgba(214, 214, 214, 0.6);
      z-index: 99;
    }
  }
}
</style>
