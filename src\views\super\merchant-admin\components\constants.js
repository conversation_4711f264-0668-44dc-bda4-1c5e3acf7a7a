import * as dayjs from 'dayjs'
import NP from 'number-precision'

export const RECENTYEAR = [
  dayjs().format('YYYY-MM-DD HH:mm:ss'),
  dayjs()
    .add(90, 'day')
    .hour(23)
    .minute(59)
    .second(59)
    .format('YYYY-MM-DD HH:mm:ss')
]
// 格式化金额/100
export const divide = money => {
  if (!money) return '0.00'
  if (typeof money === 'number') {
    return NP.divide(money, 100).toFixed(2)
  } else if (typeof money === 'string' && !isNaN(Number(money))) {
    return NP.divide(money, 100).toFixed(2)
  } else {
    return money
  }
}
export const times = money => {
  return NP.times(money, 100)
}
