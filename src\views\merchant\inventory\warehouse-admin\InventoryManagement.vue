<template>
  <div class="inventory-management-wrapper container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle" @reset="resetHandler"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          数据列表
          <span class="early-warning m-l-20"><span class="round upper"></span>商品上限</span>
          <span class="early-warning m-l-20"><span class="round lower"></span>商品下限预警</span>
          <span class="early-warning m-l-20"><span class="round middle"></span>临期预警</span>
        </div>
        <div class="align-r">
          <button-icon v-permission="[]" color="origin" type="export" @click="gotoExport">导出</button-icon>
          <button-icon color="origin" @click="gotoHandle('ReturnsManagement')">退货管理</button-icon>
          <!-- <button-icon v-permission="['background_drp.inquiry.list']" color="origin" @click="gotoHandle('InquiryOrder')">询价单</button-icon> -->
          <button-icon v-permission="['background_drp.purchase_info.list']" color="origin" @click="gotoHandle('PurchaseOrderList')">采购单</button-icon>
          <button-icon v-permission="['background_drp.transfer_info.list']" color="origin" @click="gotoHandle('TransferOrder')">调拨单</button-icon>
          <button-icon v-permission="['background_drp.material_inventory.list']" color="origin" @click="gotoHandle('InventoryStock')">盘点</button-icon>
          <button-icon color="origin" @click="gotoHandle('InboundAndOutboundDetail')">出入库明细</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- content start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe header-row-class-name="ps-table-header-row" @selection-change="handleSelectionChange">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #name="{ row }">
              <div>
                {{ row.materials_name }}
                <span v-if="row.lower_limit_warning"  class="round lower small"></span>
                <span v-if="row.upper_limit_warning" class="round upper small"></span>
                <span v-if="row.near_expired_warning" class="round middle small"></span>
              </div>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoFlow(row)">库存流水</el-button>
              <el-button type="text" size="small" class="ps-text" @click="showDialogHandle('detail', row)">设置物资</el-button>
              <el-button type="text" size="small" class="ps-text" @click="showDialogUnitHandle(row)">单位换算</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- content end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination">
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize" :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount"></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <inventory-management-dialog :show="showDialog" :info-data="dialogData" :type="dialogType" :closehandle="closeDialogHandle" :confirmhandle="clickDialogConfirm"></inventory-management-dialog>
    <import-page-dialog v-if="showImportDialog" :show.sync="showImportDialog" :header-len="3" :templateUrl="importTemplateUrl" :url="importApi"></import-page-dialog>
    <unit-conversion-dialog ref='unitDialog' :is-show.sync="showUnitDialog" :info-data="unitDialogData" @closeHandle="closeUnitDialogHandle" @confirmDialog="clickUnitDialogConfirm"></unit-conversion-dialog>
    <!-- end -->
  </div>
</template>
<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, divide, to, getRequestParams } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import InventoryManagementDialog from '../components/InventoryManagementDialog'
import UnitConversionDialog from '../components/UnitConversionDialog.vue'

export default {
  name: 'InventoryManagement',
  mixins: [exportExcel],
  components: { InventoryManagementDialog, UnitConversionDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      query: {},
      tableSettings: [
        // { label: '', key: 'selection', type: "selection" },
        { label: '物资名称', key: 'materials_name', type: "slot", slotName: "name" },
        { label: '物资分类', key: 'materail_classification_name' },
        // { label: '分类', key: 'ingredient_sort_name' },
        { label: '库存', key: 'inventory_count' },
        { label: '最小单位', key: 'limit_unit_name' }, // , isComponents: true, type: 'date', format: 'YYYY-MM-DD'
        { label: '单价', key: 'entry_price' },
        { label: '供应商', key: 'supplier_manage_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      tableData: [], // table数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: {
        materials_name: {
          type: 'input',
          value: '',
          label: '物资名称',
          placeholder: '请输入物资名称'
        },
        // sort_ids: {
        //   type: 'lazySelect',
        //   label: '分类',
        //   clearable: true,
        //   value: [],
        //   apiUrl: 'apiBackgroundFoodIngredientSortListPost',
        //   isLazy: true,
        //   collapseTags: true,
        //   multiple: true
        // },
        supplier_ids: {
          type: 'lazySelect',
          label: '供应商',
          clearable: true,
          value: [],
          apiUrl: 'apiBackgroundDrpSupplierManageListPost',
          params: {},
          isLazy: true,
          collapseTags: true,
          multiple: true
        },
        materail_classification_ids: {
          type: 'select',
          label: '物资分类',
          clearable: true,
          value: [],
          dataList: [],
          multiple: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id'
        }

      },
      showDialog: false, // 是否开启弹窗
      dialogLoading: false, // 弹窗loading
      dialogType: '', // 弹窗的状态，add/modify
      dialogData: {}, // 弹窗数据
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/导入物资模板.xlsx',
      importApi: 'apiBackgroundDrpInventoryInfoSecondmentDataImportPost',
      showUnitDialog: false,
      unitDialogData: {}
    }
  },
  created() {
    this.query = this.$route.query
    // this.initLoad()
    this.getClassificationList()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getInventoryManagementList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getInventoryManagementList() {
      if (!this.query.warehouse_id) return this.$message.error('出错啦，缺少参数!')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: +this.query.warehouse_id,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpInventoryInfoListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          item.entry_price = divide(item.entry_price)
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getInventoryManagementList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 打开弹窗
    showDialogHandle(type, data) {
      // this.dialogLoading = true
      this.showDialog = true
      this.dialogType = type
      if (data) {
        this.dialogData = data
      }
    },
    // 弹窗关闭
    closeDialogHandle() {
      this.showDialog = false
    },
    // 弹窗确定
    clickDialogConfirm() {
      this.showDialog = false
      this.getInventoryManagementList()
    },
    // 跳转单位管理
    gotoUnitAdmin() {
      this.$router.push({
        name: 'InventoryUnitAdmin',
        query: {}
      })
    },
    // 跳转流水
    gotoFlow(row) {
      this.$router.push({
        name: 'InventoryFlow',
        query: {
          ...this.$route.query,
          id: row.id,
          materials_id: row.materials_id,
          inventory_info_id: row.inventory_info_id,
          supplier_manage_id: row.supplier_manage_id
        }
      })
    },
    // 跳转页面
    gotoHandle(type) {
      if (type === 'PurchaseOrderList') {
        this.$router.replace({
          name: "DocumentManagement",
          query: {
            tabType: 'purchaseOrder',
            warehouse_id: this.$route.query.warehouse_id,
            warehouse_name: this.$route.query.warehouse_name
          }
        })
        return
      }
      if (type === 'InquiryOrder') {
        this.$router.replace({
          name: "DocumentManagement",
          query: {
            tabType: 'InquiryOrder',
            warehouse_id: this.$route.query.warehouse_id,
            warehouse_name: this.$route.query.warehouse_name
          }
        })
        return
      }
      this.$router.push({
        name: type,
        query: {
          ...this.$route.query
        }
      })
    },
    // 显示弹窗
    showDialogUnitHandle(row) {
      this.unitDialogData = row
      if (this.$refs.unitDialog) {
        this.$refs.unitDialog.setInfoData(row)
      }
      this.showUnitDialog = true
    },
    // 弹窗关闭
    closeUnitDialogHandle() {
      this.showUnitDialog = false
    },
    // 弹窗确定
    clickUnitDialogConfirm() {
      this.showUnitDialog = false
      this.getInventoryManagementList()
    },
    // 导出
    gotoExport() {
      let params = getRequestParams(this.searchFormSetting, this.currentPage, this.totalCount)
      params.id = this.query.warehouse_id
      const option = {
        url: 'apiBackgroundDrpInventoryInfoListExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    // 获取物资分类列表
    async getClassificationList () {
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpMaterailClassificationListPost({
        page: 1,
        page_size: 9999
      }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        let data = res.data || []
        let results = data.results || []
        this.searchFormSetting.materail_classification_ids.dataList = results
      }
    }
  }
}

</script>
<style lang="scss" scoped>
.inventory-management-wrapper {
  .early-warning {
    font-size: 15px;
    vertical-align: middle;
  }

  .round {
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-right: 4px;
    border-radius: 50%;
    vertical-align: middle;

    &.small {
      width: 10px;
      height: 10px;
      margin-left: 4px;
    }

    &.upper {
      background-color: #d9001b;
    }

    &.lower {
      background-color: #95f204;
    }
    &.middle {
       background-color: #f59a23;
    }
  }

  .ps-pagination {
    padding-top: 0px !important;
  }
}

</style>
