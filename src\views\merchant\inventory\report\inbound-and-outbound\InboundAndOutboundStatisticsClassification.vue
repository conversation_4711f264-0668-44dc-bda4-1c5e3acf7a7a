<template>
  <div class="PurchaseListDetails container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      @reset="resetHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <!-- <button-icon color="origin" @click="handleExport" v-permission="['background_drp.inventory_info.materail_classification_statistics_record_export']">导出</button-icon> -->
          <button-icon color="origin" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <div class="m-b-20">
         <!-- 统计 start -->
        <table-statistics :statistics="collect" />
          <!-- 统计 end -->
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          :span-method="arraySpanMethod"
          row-key="id"
        >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #count="{ row }">
              <span>{{ row.record_type === 'PURCHASE_ENTRY' ? row.expected_entry_count : row.count }}</span>
            </template>
            <template #fujian="{ row }">
              <el-button type="text" class="ps-text" size="small" @click="clickViewerHandler(row)" :disabled="!row.image_json || row.image_json.length === 0">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right; ">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
    </div>
    <!-- 预览 -->
    <image-viewer  v-model="showViewer" :initial-index="0" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
    <!-- 报表设置 -->
    <print-setting v-if="dialogPrintVisible" :extraParams="{ printType: printType }" :tableSetting="tableSetting" :defaultCheckedSetting="currentTableSetting"
    :show.sync="dialogPrintVisible" @confirm="confirmPrintDialog"></print-setting>
  </div>
</template>

<script>
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, getSevenDateRange, deepClone } from '@/utils'
import { STATISTICS_CLASSIFICATION_TABLE_SETTING_PENDING } from './constants.js'
import report from '@/mixins/report' // 混入
export default {
  name: 'InboundAndOutboundStatisticsClassification',
  components: {
  },
  mixins: [exportExcel, report],
  data() {
    return {
      importLink: '',
      supplierId: '',
      tableData: [],
      isLoading: false, // 刷新数据
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '日期',
          labelWidth: '100px',
          clearable: false,
          value: getSevenDateRange(7)
        },
        materials_name: {
          type: 'input',
          value: '',
          label: '物资名称',
          placeholder: '请输入物资名称',
          maxlength: 20
        },
        materail_classification_ids: {
          type: 'select',
          label: '物资分类',
          clearable: true,
          value: [],
          dataList: [],
          multiple: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id'
        },
        warehouse_name: {
          type: 'input',
          value: '',
          label: '仓库名称',
          placeholder: '请输入仓库名称'
        }
      },
      collect: [ // 统计
        { key: 'total_all_extry', value: 0, label: '入库合计：', type: 'money' },
        { key: 'total_all_exit', value: 0, label: '出库合计：', type: 'money' }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      previewSrcList: [],
      showViewer: false,
      tableSetting: deepClone(STATISTICS_CLASSIFICATION_TABLE_SETTING_PENDING),
      currentTableSetting: deepClone(STATISTICS_CLASSIFICATION_TABLE_SETTING_PENDING),
      dialogPrintVisible: false,
      printType: 'InboundAndOutboundStatisticsClassification',
      mergeMap: new Map() // 用于存储合并信息
    }
  },
  created() {
    this.initPrintSetting()
    this.getClassificationList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getInboundAndOutboundReport()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getInboundAndOutboundReport()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    resetHandle() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            if (key === "materail_classification_ids") {
              if (Array.isArray(data[key].value) && data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getInboundAndOutboundReport() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDrpInventoryInfoMaterailClassificationStatisticsRecordPost({
        ...this.formatQueryParams(this.searchFormSetting),
        trade_no: this.$route.query.trade_no,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        let data = res.data || {}
        let results = data.results || []
        if (results && results.length > 0) {
          results = this.setSortData(results)
        }
        this.tableData = results
        this.totalCount = data.count
        this.setCollectData(res)
        // 预处理合并数据
        this.preprocessMergeData()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getInboundAndOutboundReport()
    },
    handleExport() {
      const option = {
        type: 'InboundAndOutboundReport',
        url: 'apiBackgroundDrpInventoryInfoMaterailClassificationStatisticsRecordExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.totalCount ? this.totalCount : 10
        }
      }
      this.exportHandle(option)
    },
    // 查看预览图
    clickViewerHandler(row) {
      console.log("clickViewerHandler", row);
      // don't show viewer when preview is false
      let imgList = row.image_json || []
      if (imgList) {
        imgList = Array.isArray(imgList) ? imgList : JSON.parse(imgList)
      }
      this.previewSrcList = imgList
      if (!this.previewSrcList || this.previewSrcList.length === 0) {
        return this.$message.error('暂无图片');
      }
      this.showViewer = true;
    },
    // 关闭图片预览
    closeViewer() {
      this.showViewer = false;
    },
    // 获取物资分类列表
    async getClassificationList () {
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpMaterailClassificationListPost({
        page: 1,
        page_size: 9999
      }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        let data = res.data || []
        let results = data.results || []
        this.searchFormSetting.materail_classification_ids.dataList = results
      }
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)

      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '分类出入库统计',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundDrpInventoryInfoMaterailClassificationStatisticsRecordPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          mergeMap: JSON.stringify(this.mergeMap),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount ? this.totalCount : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 排序
    setSortData(results) {
      // 先给每一条设置一个固定的key值，只使用仓库名称、日期、物资分类作为key
      results.forEach((item, index) => {
        item.key = item.warehouse_name + "_" + item.date + "_" + item.materail_classification_name;
      })
      // 正确的字符串排序
      results.sort((a, b) => a.key.localeCompare(b.key))
      return results
    },
    // 预处理合并数据
    preprocessMergeData() {
      this.mergeMap.clear();
      const keyMap = new Map(); // 用于存储相同key的行索引

      // 按key分组
      this.tableData.forEach((row, index) => {
        const key = row.key;
        if (!keyMap.has(key)) {
          keyMap.set(key, []);
        }
        keyMap.get(key).push(index);
      });

      // 计算每组的合并信息
      keyMap.forEach((indexes, key) => {
        const firstIndex = indexes[0];
        const mergeInfo = {
          rowspan: indexes.length,
          indexes: indexes,
          // 需要累加的列
          sumColumns: {
            'entry_total': 0, // 入库合计金额
            'exit_total': 0 // 出库合并金额
          }
        };

        // 计算需要累加的列的总和
        indexes.forEach(idx => {
          const row = this.tableData[idx];
          // 累加入库合计金额
          mergeInfo.sumColumns.entry_total += Number(row.entry_total || 0);
          // 累加出库合并金额
          mergeInfo.sumColumns.exit_total += Number(row.exit_total || 0);
        });

        this.mergeMap.set(firstIndex, mergeInfo);
      });
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列索引（仓库名称、日期、物资分类）
      const mergeColumns = [0, 1, 2];
      // 需要累加的列（入库合计、出库合计）
      const sumColumns = ['entry_all_total', 'exit_all_total'];

      // 获取当前列的属性名
      const prop = column.property;

      // 如果是需要累加的列
      if (sumColumns.includes(prop)) {
        // 查找当前行所属的合并组
        for (const [firstIndex, mergeInfo] of this.mergeMap.entries()) {
          if (mergeInfo.indexes.includes(rowIndex)) {
            // 如果是合并组的第一行，显示累加后的值
            if (rowIndex === firstIndex) {
              // 根据列名设置对应的累加值
              if (prop === 'entry_all_total') {
                row[prop] = mergeInfo.sumColumns.entry_total;
              } else if (prop === 'exit_all_total') {
                row[prop] = mergeInfo.sumColumns.exit_total;
              }
              return {
                rowspan: mergeInfo.rowspan,
                colspan: 1
              };
            }
            // 其他行隐藏
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      }

      // 处理需要合并的列
      if (mergeColumns.includes(columnIndex)) {
        // 查找当前行所属的合并组
        for (const [firstIndex, mergeInfo] of this.mergeMap.entries()) {
          if (mergeInfo.indexes.includes(rowIndex)) {
            // 如果是合并组的第一行，显示合并后的单元格
            if (rowIndex === firstIndex) {
              return {
                rowspan: mergeInfo.rowspan,
                colspan: 1
              };
            }
            // 其他行隐藏
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }
      }

      // 其他列不合并
      return {
        rowspan: 1,
        colspan: 1
      };
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
