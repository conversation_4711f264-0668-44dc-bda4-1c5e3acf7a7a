<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      width="450px"
      top="20vh"
      custom-class="ps-dialog"
      show-close
      :close-on-click-modal="false"
    >
      <div class="goods-dialog-wrapp">
        <el-form
          :model="goodsCategoryFormData"
          @submit.native.prevent
          status-icon
          label-width="125px"
        >
          <el-form-item label="分类名称：">
            <div
              class="ps-flex-align-c"
              v-for="(nameItem, nameIndex) in goodsCategoryFormData.nameList"
              :key="nameIndex"
            >
              <el-input
                class="ps-input"
                style="width: 190px"
                placeholder="请输入分类名称"
                maxlength="30"
                size="small"
                v-model="nameItem.name"
              ></el-input>
              <div class="p-l-20" v-if="type == 'batch'">
                <i
                  class="el-icon-circle-plus-outline p-r-10 ps-origin"
                  @click="addFormName"
                  style="font-size: 18px"
                ></i>
                <i
                  class="el-icon-remove-outline p-r-10 ps-red"
                  @click="removeName(nameIndex)"
                  v-if="goodsCategoryFormData.nameList.length > 1"
                  style="font-size: 18px"
                ></i>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="canceDialogHandle">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          v-loading="isLoading"
          @click="clickDetermineGoodsCategory"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { to } from '@/utils'
export default {
  props: {
    isshow: Boolean,
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    confirm: Function,
    dialogInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      dialogVisible: false, // 是否显示隐藏弹窗
      goodsCategoryFormData: {
        nameList: [{ name: '' }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {},
  created() {
    if (this.type === 'modify') {
      console.log(this.dialogInfo)
      this.goodsCategoryFormData.nameList = [{ name: this.dialogInfo.name }]
    }
  },
  methods: {
    addFormName() {
      this.goodsCategoryFormData.nameList.push({
        name: ''
      })
    },
    removeName(index) {
      this.goodsCategoryFormData.nameList.splice(index, 1)
    },
    async setGoodsCategoryAdd(name) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategoryAddPost({
          name_list: name
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    async setGoodsCategoryModify() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategoryModifyPost({
          id: this.dialogInfo.id,
          name: this.goodsCategoryFormData.nameList[0].name
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickDetermineGoodsCategory() {
      // 标签名称
      let name = []
      if (this.goodsCategoryFormData.nameList && this.goodsCategoryFormData.nameList.length) {
        for (let index = 0; index < this.goodsCategoryFormData.nameList.length; index++) {
          if (!this.goodsCategoryFormData.nameList[index].name) {
            return this.$message.error('请输入分类名称')
          } else {
            name.push(this.goodsCategoryFormData.nameList[index].name)
          }
        }
      }
      if (this.type === 'modify') {
        this.setGoodsCategoryModify()
      } else {
        this.setGoodsCategoryAdd(name)
      }
    },
    canceDialogHandle() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.goods-dialog-wrapp{
  max-height: 350px;
  overflow-y: auto;
}
</style>
