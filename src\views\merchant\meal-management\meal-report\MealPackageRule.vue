<template>
  <div class="meal-package-rule container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon
            color="origin"
            type="add"
            @click="openMealPackageRule('add')"
            v-permission="['background_report_meal.report_meal_pack_settings.add']"
          >
            新增餐包
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
        >
          <el-table-column prop="name" label="餐包名称"></el-table-column>
          <el-table-column prop="start_date" label="配餐日期">
            <template slot-scope="scope">
              {{scope.row.start_date}}~{{scope.row.end_date}}
            </template>
          </el-table-column>
          <el-table-column prop="meal_name" label="餐段"></el-table-column>
          <el-table-column prop="meal_days" label="用餐天数"></el-table-column>
          <el-table-column prop="meal_num" label="用餐份数"></el-table-column>
          <el-table-column prop="all_fee" label="餐包价格"></el-table-column>
          <el-table-column prop="toll_type_alias" label="收费类型"></el-table-column>
          <el-table-column prop="organization_name" label="消费点"></el-table-column>
          <el-table-column prop="create_time" label="创建时间"></el-table-column>
          <el-table-column prop="is_enable" label="状态">
            <template slot-scope="scope">
              <span>{{scope.row.is_enable ? '启用' : '未启用'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="report_meal_settings_name" label="关联规则"></el-table-column>
          <el-table-column prop="remark" label="备注"></el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-button type="text"
                size="small"
                @click="openMealPackageRule('edit', scope.row)"
                v-permission="['background_report_meal.report_meal_pack_settings.modify']"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                :disabled="scope.row.is_enable"
                @click="mulOperation('del', scope.row)"
                v-permission="['background_report_meal.report_meal_pack_settings.delete']"
              >
                删除
              </el-button>
              <el-button type="text" size="small" @click="openMealPackageRule('history', scope.row)">
                历史记录
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 抽屉弹窗 -->
    <AddMealPackageRule
      :show.sync="showDrawer"
      :title="drawerTitle"
      :infoData="drawerInfoData"
      :type="drawerType"
      :size="560"
      @confirm="confirmHandle"
      @cancel="cancelHandle"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, divide } from '@/utils'
import AddMealPackageRule from './component/AddMealPackageRule'
import { MEAL_TYPES } from '@/utils/constants'

export default {
  name: 'MealPackageRule',
  // mixins: [activatedLoadData],
  components: {
    AddMealPackageRule
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          value: '',
          label: '餐包名称',
          placeholder: '请输入'
        },
        is_enable: {
          type: 'select',
          value: "",
          label: '状态',
          dataList: [
            { label: '全部', value: '' },
            { label: '启用', value: true },
            { label: '未启用', value: false }
          ]
        },
        toll_type: {
          type: 'select',
          value: "",
          label: '收费类型',
          dataList: [
            { label: '全部', value: '' },
            { label: '固定收费', value: 'fixed' },
            { label: '实时收费', value: 'real_time' }
          ]
        },
        organization_ids: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          dataList: [],
          multiple: true,
          collapseTags: true,
          checkStrictly: true
        }
      },
      allMealTypeList: MEAL_TYPES,
      showDrawer: false,
      drawerInfoData: {},
      drawerType: '',
      drawerTitle: '',
      selectInfo: {},
      editStatus: false
    }
  },
  computed: {
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMealPackageRuleList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getMealPackageRuleList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getMealPackageRuleList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportMealReportMealPackSettingsListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results.map(item => {
          let meal_type_list = []
          item.meal_type.map(meal => {
            meal_type_list.push(this.allMealTypeList.find(i => i.value === meal).label)
          })
          item.meal_name = meal_type_list.join('，')
          item.all_fee = divide(item.fee)
          return item
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getMealPackageRuleList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMealPackageRuleList()
    },
    // 开关事件
    switchFn(e) {
      console.log(e)
    },
    // 各种操作 type表示类型，text是弹窗文字，data是单个操作的时候带过去的数据
    mulOperation(type, data) {
      if (!data && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let title = '提示'
      let content = ''
      switch (type) {
        case "del":
          content = '确定删除该餐包规则？'
          break;
        case "mulDel":
          content = '确定删除所选餐包规则？'
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            switch (type) {
              case "del": // 删除
                this.deleteSetting([data.id])
                break;
              case "mulDel": // 批量删除
                this.deleteSetting(this.selectListId)
                break;
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 删除事件
    async deleteSetting(ids) {
      const res = await this.$apis.apiBackgroundReportMealReportMealPackSettingsDeletePost({
        ids
      })
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getMealPackageRuleList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 开启和关闭报餐设置
    async isOpenSetting(data) {
      let params = {
        id: data.id,
        is_open: data.is_open
      }
      const res = await this.$apis.apiBackgroundReportMealReportMealSettingsModifyOpenStatusPost(params)
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getMealPackageRuleList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增or编辑
    openMealPackageRule(type, data) {
      this.drawerInfoData = data
      this.drawerType = type
      this.editStatus = false
      if (type === 'history') {
        this.drawerTitle = "历史记录"
        this.selectInfo = data
        this.showDrawer = true
      } else if (type === 'add') {
        this.drawerTitle = "新增餐包"
        this.showDrawer = true
      } else if (type === 'edit') {
        this.drawerTitle = "编辑餐包"
        if (data.is_enable) {
          this.editStatus = true
          let params = {
            id: this.drawerInfoData.id,
            name: this.drawerInfoData.name,
            organization: this.drawerInfoData.organization,
            is_enable: false
          }
          this.addAndEditMealPackageRule(params, 'editStatus')
        } else {
          this.showDrawer = true
        }
      }
    },
    confirmHandle() {
      this.showDrawer = false
      this.currentPage = 1
      this.getMealPackageRuleList()
    },
    cancelHandle() {
      if (this.editStatus) {
        let params = {
          id: this.drawerInfoData.id,
          name: this.drawerInfoData.name,
          organization: this.drawerInfoData.organization,
          is_enable: true
        }
        this.addAndEditMealPackageRule(params, 'cancel')
      } else {
        this.showDrawer = false
      }
    },
    async addAndEditMealPackageRule(params, type) {
      const res = await this.$apis.apiBackgroundReportMealReportMealPackSettingsModifyPost(params)
      if (res.code === 0) {
        if (type === 'editStatus') {
          this.drawerInfoData.is_enable = false
          this.showDrawer = true
        } else if (type === 'cancel') {
          this.drawerInfoData.is_enable = true
          this.showDrawer = false
        }
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.meal-package-rule {
}
</style>
