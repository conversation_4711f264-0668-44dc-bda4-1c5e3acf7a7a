<template>
  <div class="ingredients-label container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="125px"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-content" v-loading="isLoading">
        <div class="table-header">
          <div class="table-title">标签组</div>
          <div class="align-r">
            <!--20250529 产品建和要求隐藏，有BUG-->
            <!-- <button-icon color="origin" type="add" @click="clickTagDialog('add')" v-permission="['background_healthy.label_group.add']">
              添加标签组
            </button-icon> -->
          </div>
        </div>
        <div class="content-box" v-for="(item, index) in tableData" :key="index">
          <div class="ps-flex-bw">
            <div>
              {{ item.name }}
              <span>（{{ item.label_list.length }}）</span>
            </div>
            <div class="rnge-title ps-flex-align-c flex-align-c">
              可见范围；
              <span v-if="item.visible === 'all'">{{ item.visible_alias }}</span>
              <div v-if="item.visible === 'part'">
                <!-- 过多展示 -->
                <el-popover placement="top-start" width="200" trigger="hover">
                  <span
                    v-for="(organizationItem, organizationIndex) in item.visible_organization_list"
                    :key="organizationIndex"
                  >
                    {{
                      (organizationIndex === item.visible_organization_list.length - 1 &&
                        organizationItem.name) ||
                        organizationItem.name + '、'
                    }}
                  </span>
                  <div slot="reference" class="part-box">
                    <span
                      v-for="(organizationItem,
                      organizationIndex) in item.visible_organization_list"
                      :key="organizationIndex"
                    >
                      {{
                        (organizationIndex === item.visible_organization_list.length - 1 &&
                          organizationItem.name) ||
                          organizationItem.name + '、'
                      }}
                    </span>
                  </div>
                </el-popover>
              </div>
            </div>
          </div>
          <div class="tag-content">
            <div class="tag-box">
              <el-input
                v-if="item.inputVisible"
                v-model="item.inputValue"
                :ref="'saveTagInput' + item.id"
                class="ps-input w-100  p-r-10 p-t-5"
                size="mini"
                autofocus
                maxlength="15"
                @keyup.enter.native="handleInputConfirm(item)"
                @blur="handleInputConfirm(item)"
              ></el-input>
              <!-- <button-icon v-else color="origin" class="p-t-5" type="add" @click="showInput(item)" v-permission="['background_healthy.label_group.add_label']">
                添加标签
              </button-icon> -->
              <el-tag
                class="m-r-5 m-t-5"
                v-for="(labelItem, labelIndex) in item.label_list"
                :key="labelIndex"
                size="medium"
                effect="plain"
                type="info"
                color="#fff"
              >
                {{ labelItem.name }}
              </el-tag>
            </div>
            <div class="fun-click">
              <!-- <el-button
                type="text"
                size="small"
                icon="el-icon-top"
                class="ps-green-text"
                v-if="(index != 0 && currentPage == 1) || currentPage > 1"
                @click="weightClick('up', item)"
              >
                上移
              </el-button> -->
              <!-- <span style="margin: 0 5px; color: #e2e8f0"  v-if="(index != 0 && currentPage == 1) || currentPage > 1">|</span> -->
              <!--  最后一个下移就隐藏 -->
              <!-- <el-button
                type="text"
                size="small"
                icon="el-icon-bottom"
                v-if="tableData.length - 1 != index || currentPage < totalPageSize"
                @click="weightClick('lower', item)"
              >
                下移
              </el-button> -->
              <!-- <span style="margin: 0 5px; color: #e2e8f0" v-if="tableData.length - 1 != index || currentPage < totalPageSize">|</span> -->
              <!-- <el-button
                type="text"
                size="small"
                icon="el-icon-edit"
                class="ps-origin"
                @click="clickTagDialog('modify', item)"
                v-permission="['background_healthy.label_group.modify']"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                icon="el-icon-delete"
                class="ps-red"
                @click="tagDelClick(item)"
                v-permission="['background_healthy.label_group.delete']"
              >
                删除
              </el-button> -->
            </div>
          </div>
        </div>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @current-change="handleCurrentChange"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 分页 end -->
    <label-group-dialog
      v-if="labelRroupDialogVisible"
      :title="labelRroupTitle"
      :type="formOperate"
      :isshow.sync="labelRroupDialogVisible"
      visibleType="ingredient"
      :labelRroupInfo="labelRroupInfo"
      :confirm="getLabelGroupList"
      width="600px"
    />
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import { INGREDIENTS_LABEL } from './constants'
import labelGroupDialog from './components/labelGroupDialog.vue'
export default {
  data() {
    return {
      searchFormSetting: INGREDIENTS_LABEL,
      isLoading: false,
      tableData: [],
      dialogIsLoading: false,
      labelRroupTitle: '添加标签组',
      labelRroupDialogVisible: false,
      formOperate: 'add',
      labelRroupInfo: {},
      totalPageSize: 0,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  components: { labelGroupDialog },
  created() {
    this.getLabelGroupList()
  },
  mounted() {},
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getLabelGroupList()
      }
    }, 300),
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.getLabelGroupList()
    },
    // 标签组列表
    async getLabelGroupList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyLabelGroupListPost({
          type: 'ingredient',
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
        this.tableData = res.data.results.map(v => {
          // 输入标签
          v.inputVisible = false
          v.inputValue = ''
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    clickTagDialog(type, data) {
      this.formOperate = type
      if (type === 'add') {
        this.labelRroupTitle = '添加标签组'
      } else {
        this.labelRroupTitle = '修改标签组'
      }
      if (type === 'modify') {
        this.labelRroupInfo = data
      }
      this.labelRroupDialogVisible = true
    },
    handleInputConfirm(data) {
      if (data.inputValue) {
        this.getLabelGroupAddLabel(data)
      } else {
        data.inputVisible = false
      }
    },
    showInput(data) {
      data.inputVisible = true
      this.$nextTick(_ => {
        // 默认聚焦
        this.$refs['saveTagInput' + data.id][0].$refs.input.focus()
      })
    },
    // 添加标签
    async getLabelGroupAddLabel(data) {
      this.isLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundHealthyLabelGroupAddLabelPost({
          name: data.inputValue,
          label_group_id: data.id
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        data.inputValue = ''
        data.inputVisible = false
        this.getLabelGroupList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 上移下移
    weightClick(type, data) {
      let params = {
        weight: 0,
        id: data.id
      }
      if (type === 'up') {
        params.weight = data.weight - 1
      } else {
        params.weight = data.weight + 1
      }
      this.getLabelGroupModifyWeight(params)
    },
    // 修改权重
    async getLabelGroupModifyWeight(params) {
      this.isLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundHealthyLabelGroupModifyWeightPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.getLabelGroupList()
      } else {
        this.$message.error(res.msg)
      }
    },
    tagDelClick(data) {
      this.$confirm(`确定删除该标签组？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundHealthyLabelGroupDeletePost({
              ids: [data.id]
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                }
              }
              this.getLabelGroupList()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getLabelGroupList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.content-box {
  background: #fff;
  border: 1px solid #f1f4f8;
  padding: 20px 20px;
  border-radius: 10px;
  margin-bottom: 10px;
  .tag-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    .tag-box {
      flex: 1;
    }
    .fun-click {
      text-align: right;
    }
  }
  .part-box {
    max-width: 200px;
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
