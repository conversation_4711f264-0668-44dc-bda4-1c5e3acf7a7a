<template>
  <div class="AddSubscriptionOrder">
    <h3 class="p-t-20">{{ titleText }}</h3>
    <div class="form-container">
      <el-form
        v-loading="isLoading"
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        size="small"
        class="m-l-20 m-t-10"
      >
        <el-form-item label="" label-width="0">
          <el-form-item label="申请人" prop="subscribePersonName" class="inline-block">
            <el-input
              v-model="formData.subscribePersonName"
              placeholder="请输入"
              class="ps-input"
            ></el-input>
          </el-form-item>
          <el-form-item label="申请日期" prop="subscribeDate" class="inline-block" label-width="100px">
            <el-date-picker
              v-model="formData.subscribeDate"
              type="date"
              placeholder="选择日期时间"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
        </el-form-item>
        <el-form-item label="申购原因" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            class="ps-textarea"
            style="width: 500px;"
            :rows="3"
            :maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="物资清单" prop="">
          <el-button class="ps-origin-btn w-100" @click="addMaterials('subscription_order')">新增</el-button>
          <el-button class="ps-origin-btn w-100" @click="openImport('add')">导入物资</el-button>
        </el-form-item>
        <el-form-item label="">
          <div style="width: 92%; margin-bottom: 10px">
            <el-table
              :data="currentMaterialsTableData"
              ref="tableRef"
              stripe
              size="small"
              border
              header-row-class-name="ps-table-header-row"
            >
              <table-column v-for="item in materialsTableSettings" :key="item.key" :col="item" :width="item.width">
                <template #supplier="{ row, index }">
                  <el-form-item
                    label=""
                    :prop="'materialsTableData[' + getMaterialsIndex(index) + '].supplier_manage_id'"
                    :rules="formRules.supplier_manage_id"
                    class="m-b-0"
                  >
                    <el-select v-model="row.supplier_manage_id" filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeSupplier($event, index)">
                      <el-option v-for="option in row.supplier_list" :key="option.supplier_manage_id" :label="option.supplier_manage_name" :value="option.supplier_manage_id" ></el-option>
                    </el-select>
                  </el-form-item>
                </template>
                <template #specs="{ row, index }">
                  <el-form-item  
                    label=""
                    :prop="'materialsTableData[' + getMaterialsIndex(index) + '].specs'"
                    :rules="formRules.specs"
                    class="m-b-0"
                  >
                    <el-select v-model="row.material_specification_id" :disabled="!row.supplier_manage_id" clearable filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeSpecs($event, index)">
                      <el-option v-for="option in row.specification_list" :key="option.id" :label="`1${option.unit_management_name}*${option.count}${option.limit_unit_name}*${option.net_content}${option.net_content_unit}`" :value="option.id" ></el-option>
                    </el-select>
                  </el-form-item>
                </template>
                <template #count="{ row, index }">
                  <el-form-item
                    label=""
                    label-width="0"
                    class="m-b-0"
                    :rules="formRules.count"
                    :prop="'materialsTableData[' + getMaterialsIndex(index) + '].count'"
                  >
                    <el-input
                      v-model="row.count"
                      placeholder="请输入"
                      :maxlength="6"
                      class="ps-input"
                      @input="inputPirchaseHandle($event, index)"
                    ></el-input>
                  </el-form-item>
                </template>
                <template #operation="{ index }">
                  <el-button type="text" size="small" class="ps-origin" @click.stop="deleteMaterials(index)">
                    删除
                  </el-button>
                </template>
              </table-column>
            </el-table>
            <div v-if="errorTableData" class="red">{{ errorTableData }}</div>
            <pagination
              v-if="formData.materialsTableData.length > materialsPageSize"
              :onPaginationChange="onMaterialsPaginationChange"
              :current-page.sync="materialsPage"
              :page-size.sync="materialsPageSize"
              :layout="'total, prev, pager, next, jumper'"
              :total="formData.materialsTableData.length"
            ></pagination>
          </div>
        </el-form-item>
        <div class="m-l-40 m-t-60">
          <el-button class="ps-cancel-btn w-130" size="medium" @click="closeHandler">取消</el-button>
          <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle('draft')">存为草稿</el-button>
          <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle('')">保存</el-button>
          <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle('is_initiated')">保存并发起</el-button>
        </div>
      </el-form>
    </div>
    <!-- 导入物资 -->
    <import-page-dialog
      ref="importPageRef"
      :show.sync="showImportDialog"
      :title="importDialogTitle"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :loading.sync="importLoading"
      :isUpload="false"
      isDeleteFirst
      @confirm="confirmImportHandle"
    ></import-page-dialog>
    <!-- 添加物资/选择询价单 -->
    <choose-list-dialog
      :showdialog.sync="showChooseDialog"
      :title="dialogChooseTitle"
      :type="dialogChooseType"
      :api="dialogChooseApi"
      :detailApi="dialogChooseDetailApi"
      :search-setting="dialogChooseSearchSetting"
      :table-settings="dialogChooseTableSettings"
      :params="dialogChooseParams"
      :rowKey="dialogRowKey"
      showSelectLen
      @confirmChoose="confirmChooseHandle"
    ></choose-list-dialog>
    <!-- 保存草稿/模板 -->
    <form-dialog
      :showdialog.sync="showFormDialog"
      :type="dialogFormType"
      :api="dialogFormApi"
      :title="dialogFormTitle"
      :inputLabel="inputLabel"
      :params="dialogFormParams"
      @confirmForm="confirmFormHandle"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, deepClone, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import { validateNumber } from '@/utils/form-validata'
import * as dayjs from 'dayjs'
import NP from 'number-precision'
// import DraftManageDialog from '../warehouse-admin/components/DraftManageDialog.vue'
import FormDialog from '../components/FormDialog'
import ChooseListDialog from '../components/ChooseListDialog'
import { downloadJsonExcel } from '@/utils/excel'

export default {
  name: 'AddSubscriptionOrder',
  mixins: [exportExcel],
  components: {
    // DraftManageDialog
    ChooseListDialog,
    FormDialog
  },
  data() {
    return {
      type: 'add',
      titleText: this.$route.params.type === 'modify' ? '编辑申购单' : '新建申购单',
      isLoading: false, // 刷新数据
      queryData: this.$route.query,
      detailData: {},
      // form表单数据
      formData: {
        subscribePersonName: '',
        subscribeDate: '',
        remark: '',
        materialsTableData: []
      },
      formRules: {
        subscribePersonName: [{ required: true, message: '请输入申请人', trigger: 'change' }],
        remark: [{ required: true, message: '请输入申请原因', trigger: 'change' }],
        subscribeDate: [{ required: true, message: '请选择日期', trigger: 'change' }],
        count: [{ required: true, validator: validateNumber, trigger: 'change' }]
      },
      pickerPurchaseTimeOptions: {
        disabledDate(time) {
          return time.getTime() < dayjs().subtract(1, 'day').valueOf()
        }
      },
      // 入库物资
      materialsTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '供应商', key: 'supplier', type: 'slot', slotName: 'supplier', width: 200 },
        { label: '规格', key: 'material_specification_id', type: 'slot', slotName: 'specs' },
        { label: '最小单位', key: 'unit_name' },
        { label: '申购数量', key: 'count', type: 'slot', slotName: 'count' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      // 物资数据
      currentMaterialsTableData: [],
      materialsPage: 1,
      materialsPageSize: 10,
      errorTableData: '',
      supplierList: [], // 供应商列表
      unitList: [], // 单位列表
      // 导入的弹窗数据
      importLoading: false,
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/申购单-导入物资模板.xlsx',
      importHeaderLen: 1,
      importFailTableData: [], // 导入失败的数据
      importTableSettings: [
        { label: '物资名称', key: 'name' },
        { label: '导入失败原因', key: 'result' }
      ],
      // 选择物资/模板/询价单等弹窗
      showChooseDialog: false, // 是否开启弹窗
      dialogChooseLoading: false, // 弹窗loading
      dialogChooseTitle: '选择物资',
      dialogChooseType: '', // 弹窗的状态，add/modify
      dialogChooseData: {}, // 弹窗数据
      remoteChooseLoading: false,
      dialogChooseTableSettings: [],
      dialogChooseSearchSetting: {},
      dialogChooseParams: {
      },
      dialogChooseApi: '1', // 请求的接口
      dialogChooseDetailApi: '',
      dialogRowKey: 'materials_id',
      // 保存为草稿、模板、选择菜谱等的弹窗
      showFormDialog: false,
      dialogFormTitle: '保存为草稿',
      dialogFormType: '1',
      dialogFormPagetype: '', // 哪个页面的功能
      dialogFormApi: '1',
      inputLabel: '',
      dialogFormParams: {}
    }
  },
  computed: {
    // 花销大，因为每次materialsTableData数据变化都会触发它重新计算
    // currentMaterialsTableData() {
    //   return this.materialsTableData.slice((this.materialsPage - 1) * this.materialsPageSize, this.materialsPage * this.materialsPageSize)
    // }
  },
  created() {
    this.type = this.$route.params.type || 'add'
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.$route.query.type === 'recovery') {
        this.getdraftDetail(+this.$route.query.id)
      }
      if (this.type === 'modify') {
        this.getSubscriptionOrderDetail()
      }
    },
    // 初始化编辑显示
    async getSubscriptionOrderDetail() {
      if (!this.$route.query.id) return this.$message.error('获取id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: this.$route.query.id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.detailData = res.data
        this.formData.subscribeDate = res.data.subscribe_date
        this.formData.subscribePersonName = res.data.subscribe_person_name
        this.formData.remark = res.data.remark
        this.formData.subscribeDate = res.data.subscribe_date
        this.formData.materialsTableData = res.data.materials_info.map(v => {
          return {
            materials_name: v.materials_name,
            count: v.count, //
            unit_name: v.unit_name,
            unit_id: v.unit_id,
            materials_id: v.materials_id
          }
        })
        this.initCurrentTableMaterials()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 草稿箱详情
    async getdraftDetail(id) {
      if (!id) return this.$message.error('获取id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id,
        inventory_info_type: 'subscribe_info'
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoDraftDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.detailData = res.data.extra
        this.formData.subscribeDate = res.data.extra.subscribe_date
        this.formData.subscribePersonName = res.data.extra.subscribe_person_name
        this.formData.remark = res.data.extra.remark
        this.formData.subscribeDate = res.data.extra.subscribe_date
        // this.formData.materialsTableData = deepClone(res.data.extra.subscribe_data)
        if (res.data.extra.subscribe_data) {
          this.formData.materialsTableData = res.data.extra.subscribe_data.map(v => {
            v.supplier_list = v.price_info
            v.specification_list = v.price_info.find(item => item.supplier_manage_id === v.supplier_manage_id).specification
            v.specs_item = v.specification_list.find(item => item.id === v.material_specification_id)
            return deepClone(v)
          })
        }
        this.initCurrentTableMaterials()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取供应商数据列表
    async getSupplierManagementList() {
      const res = await this.$apis.apiBackgroundDrpSupplierManageListPost({
        page: 1,
        page_size: 999999
      })
      if (res.code === 0) {
        this.supplierList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化Materials分页数据
    initCurrentTableMaterials() {
      this.currentMaterialsTableData = this.formData.materialsTableData.slice(
        (this.materialsPage - 1) * this.materialsPageSize,
        this.materialsPage * this.materialsPageSize
      )
    },
    // 获取实际materialsTableData的index，这样做就不用重新遍历一次数据了
    getMaterialsIndex(index) {
      return (this.materialsPage - 1) * this.materialsPageSize + index
    },
    onMaterialsPaginationChange(val) {
      this.materialsPage = val.current
      this.materialsPageSize = val.pageSize
      this.initCurrentTableMaterials()
    },
    // 删除物资
    deleteMaterials(index) {
      this.formData.materialsTableData.splice(this.getMaterialsIndex(index), 1)
      if (this.formData.materialsTableData.length !== 0) {
        this.materialsPage = this.$computedTotalPageSize(
          this.formData.materialsTableData.length,
          this.materialsPageSize
        )
      } else {
        this.materialsPage = 1
      }
      // 初始化分页数据
      this.initCurrentTableMaterials()
    },
    // 实际采购数量校验
    inputPirchaseHandle(e, currentIndex) {
      let index = this.getMaterialsIndex(currentIndex)
      this.$set(this.formData.materialsTableData[index], 'count', e)
    },
    // 选择供应商
    changeSupplier(e, currentIndex) {
      let current = this.formData.materialsTableData[this.getMaterialsIndex(currentIndex)]
      let currentSupplier = current.supplier_list.find(v => v.supplier_manage_id === e)
      // 选择不同供应商的物资单价是不同的，需要对应切换
      // this.$set(current, 'ref_unit_price', currentSupplier.ref_unit_price)
      this.$set(current, 'supplier_manage_name', currentSupplier.supplier_manage_name)
      // let purchasePrice = (current.ref_unit_price) * (current.discounts / 100)
      // 合计也需要重新计算
      // purchase_count字段有可能为空字符串需要兼容处理下
      // let purchasePrice = NP.times(current.ref_unit_price, current.purchase_count || 0)
      // this.$set(current, 'total_price', purchasePrice)
      // this.$set(current, 'purchase_weight', currentSupplier.weight)
      // 设置下规格列表数据
      this.$set(current, 'specification_list', currentSupplier.specification || [])
      // 修改供应商得重置规格和单价、合计等数据
      this.$set(current, 'specs_item', '')
      this.$set(current, 'material_specification_id', '')
      this.$set(current, 'unit_name', '')
      // 合并相同物资和相同供应商的数据
      this.formData.materialsTableData = this.uniqueMaterials(this.formData.materialsTableData, true)
      // 初始化下分页数据
      this.initCurrentTableMaterials()
    },
    // 修改规格
    changeSpecs(e, currentIndex) {
      let current = this.formData.materialsTableData[this.getMaterialsIndex(currentIndex)]
      let currentSpecs = current.specification_list.find(v => v.id === e)
      this.$set(current, 'specs_item', currentSpecs)
      this.$set(current, 'unit_name', `${currentSpecs.limit_unit_name}*${currentSpecs.net_content}${currentSpecs.net_content_unit}`) // 最小单位：例：瓶*330ml
      // 合并相同物资和相同供应商的数据
      this.formData.materialsTableData = this.uniqueMaterials(this.formData.materialsTableData, true)
      // 初始化下分页数据
      this.initCurrentTableMaterials()
    },
    // 添加物资
    addMaterials(type) {
      this.dialogChooseType = type
      // 初始化分页数据
      // this.initCurrentTableMaterials()
      if (type === 'subscription_order') {
        this.dialogChooseTitle = '添加物资'
        this.dialogChooseApi = 'apiBackgroundDrpSubscribeInfoSubscribeMaterialListPost'
        this.dialogChooseParams = {}
        this.dialogRowKey = 'id'
        this.dialogChooseSearchSetting = {
          name: {
            type: 'input',
            value: '',
            label: '物资名称',
            placeholder: '请输入'
          }
        }
        this.dialogChooseTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '物资名称', key: 'name' },
          { label: '供应商', key: 'supplier', type: 'slot', slotName: 'supplier' },
          { label: '规格', key: 'specs', type: 'slot', slotName: 'specs' },
          { label: '申购数量', key: 'count', type: 'slot', slotName: 'count' }
        ]
      }
      this.showChooseDialog = true
    },
    dialogformatResult(data) {

    },
    // form弹窗
    openFormDialog(type, row) {
      this.dialogFormType = type
      switch (type) {
        case 'draft':
          this.dialogFormTitle = '创建草稿'
          this.inputLabel = '草稿名称'
          this.dialogFormApi = 'apiBackgroundDrpSubscribeInfoTempAddPost'
          this.dialogFormParams = this.setDialogFormParams(type)
          break;
      }
      this.showFormDialog = true
    },
    // 设置存为草稿或者存为模板的数据
    setDialogFormParams(type) {
      let params = {
        inventory_info_type: 'subscribe_info',
        temp_type: type,
        subscribe_date: this.formData.subscribeDate,
        subscribe_person_name: this.formData.subscribePersonName,
        remark: this.formData.remark,
        subscribe_data: this.formData.materialsTableData
      }
      return params
    },
    // form弹窗
    confirmFormHandle(e) {
      // 存为草稿需要返回上一页哦
      if (e && e.type === 'draft') {
        this.$backVisitedViewsPath(this.$route.path, 'SubscriptionOrderList')
      }
    },
    // 弹窗操作
    showDialogHandle(type, data, index) {
      this.dialogType = type
      if (type === 'importResult') {
        this.dialogTitle = '提示'
      }
      this.showDialog = true
    },
    // 检查表单数据
    checkedFormData() {
      let nopass = this.formData.materialsTableData.some(v => {
        return !v.count
      })
      if (nopass) {
        this.errorTableData = '请检查申购数量不能为空！'
      } else {
        this.errorTableData = ''
      }
      // nopass = false
      return !nopass
    },
    // 格式华参数
    formatParams() {
      let params = {
        subscribe_date: this.formData.subscribeDate,
        subscribe_person_name: this.formData.subscribePersonName,
        remark: this.formData.remark
      }
      params.subscribe_data = this.formData.materialsTableData.map(v => {
        let current = {
          materials_id: v.materials_id,
          count: v.count,
          supplier_manage_id: v.supplier_manage_id,
          limit_unit_id: v.unit_id,
          material_specification_id: v.material_specification_id,
          unit_name: v.unit_name,
          detail_json: JSON.stringify({
            materials_id: v.materials_id,
            count: v.count,
            supplier_manage_id: v.supplier_manage_id,
            limit_unit_id: v.unit_id,
            material_specification_id: v.material_specification_id,
            unit_name: v.unit_name,
          })
        }
        return current
      })
      return params
    },
    validateForm(type) {
      if (this.checkedFormData()) {
        let params = this.formatParams()
        if (!this.formData.materialsTableData.length) return this.$message.error('请先选择物资！')
        if (type === 'is_initiated') {
          params.is_initiated = 1
        }
        if (this.type === 'modify') {
          params.id = this.$route.query.id
          this.sendModifyFormdata(params)
        } else {
          this.sendFormdata(params)
        }
      } else {
        this.$message.error('请认真检查表单数据！')
      }
    },
    // 页面按钮点击
    async submitFormHandle(type) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (type === 'draft') {
            this.openFormDialog(type)
          } else {
            this.validateForm(type)
          }
        } else {
          this.checkedFormData()
        }
      })
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            // this.$backVisitedViewsPath(this.$route.path, 'ProcureList')
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 发送数据
    async sendFormdata(params) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        this.$backVisitedViewsPath(this.$route.path, 'SubscriptionOrderList')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑
    async sendModifyFormdata(params) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        this.$backVisitedViewsPath(this.$route.path, 'SubscriptionOrderList')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导入弹窗
    openImport(type) {
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    // 导入确定事件
    confirmImportHandle(data) {
      let importData = data
      // 删除示例数据
      // importData.splice(1, 1)
      console.log(111, importData)
      // importResult
      if (importData.length > 1) {
        // this.importLoading = true
        // 导入数据对应后端需要的字段
        const purchaseNameObject = {
          '物资名称': 'materials_name',
          '供应商': 'supplier_manage_name',
          '规格': 'spec_data',
          '申购数量': 'count',
        }
        // 根据purchaseNameObject转换的index key
        let resultKey = {}
        importData[0].forEach((v, index) => {
          resultKey[index] = purchaseNameObject[v]
        })
        let result = []
        importData.forEach((item, index) => {
          if (index > 0) {
            let current = {}
            item.forEach((v, k) => {
              current[resultKey[k]] = v
            })
            result.push(current)
          }
        })
        console.log(222, result)
        this.sendImportMaterials(result)
      } else {
        this.$message.error('导入物资为空！')
      }
    },
    // 发送导入物资数据给后端校验
    async sendImportMaterials(data) {
      if (this.importLoading) return
      this.importLoading = true
      let params = {
        data: data
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoImportMaterialsPost(params))
      // this.isLoading = false
      this.importLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$refs.importPageRef.reset()
        this.showImportDialog = false
        if (res.data.success && res.data.success.length > 0) {
          let result = []
          res.data.success.map(v => {
            let item = {
              materials_name: v.materials_name,
              materials_id: v.materials_id,
              count: v.count,
              unit_id: v.unit_id,
              unit_name: v.unit_name,
              supplier_manage_id: v.supplier_id,
              supplier_manage_name: v.supplier_manage_name,
              supplier_list: v.price_info,
              material_specification_id: v.specification_id,
              specs_item: null,
              specification_list: []
            }
            let currentSupplier = v.price_info.find(current => current.supplier_manage_id === item.supplier_manage_id)
            if (currentSupplier) {
              item.supplier_manage_name = currentSupplier.supplier_manage_name
              item.specification_list = currentSupplier.specification // 供应商的规格列表
            }
            // 为了反显规格，需要拿到规格的具体数据
            if (item.material_specification_id && item.specification_list) {
              item.specs_item = item.specification_list.find(sp => sp.id === item.material_specification_id)
              if (item.specs_item) item.unit_name = `${item.specs_item.limit_unit_name}*${item.specs_item.net_content}${item.specs_item.net_content_unit}`
            }
            result.push(item)
          })
          this.formData.materialsTableData = this.mergeArrays(this.formData.materialsTableData, result)
          this.initCurrentTableMaterials()
        }
        if (res.data.failure && res.data.failure.length > 0) {
          this.formatImportFailureResult(res.data.failure)
          this.$message.error('部分物资导入失败，请查看excel!')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化导入失败的数据，通过xlsx方式下载显示
    formatImportFailureResult(result) {
      const purchaseNameObject = {
        'materials_name': 0,
        'supplier_manage_name': 1,
        'spec_data': 2,
        'count': 3,
        'result': 4
      }
      let failureJson = [
        ['物资名称', '供应商', '规格', '申购数量', '导入结果']
      ]
      let json = result.map(v => {
        let current = []
        Object.keys(purchaseNameObject).forEach(k => {
          current[purchaseNameObject[k]] = v[k]
        })
        return current
      })
      failureJson = failureJson.concat(json)
      // 下载数据
      downloadJsonExcel(failureJson)
    },
    // 选择物资确定回调事件
    confirmChooseHandle(e) {
      console.log(1212, e.data)
      this.showChooseDialog = false
      let result = []
      // 新增物资
      e.data.forEach(v => {
        console.log(v, '8888888')
        let item = {
          materials_name: v.name,
          count: v.count, //
          unit_name: `${v.specs_item.limit_unit_name}*${v.specs_item.net_content}${v.specs_item.net_content_unit}`, // 最小单位：例：瓶*330ml,
          unit_id: v.specs_item.limit_unit_id,
          materials_id: v.id,
          // 新加的
          supplier_manage_id: v.supplier,
          supplier_manage_name: v.supplier_manage_name,
          supplier_list: v.price_info,
          material_specification_id: v.specs,
          specs_item: v.specs_item,
          specification_list: v.specification_list,
        }
        result.push(item)
      })
      if (this.formData.materialsTableData.length > 0) {
        this.formData.materialsTableData = this.mergeArrays(this.formData.materialsTableData, result)
      } else {
        this.formData.materialsTableData = result
      }
      this.initCurrentTableMaterials()
    },
    // 合并新旧数据，以供应商id、物资id、规格作为唯一值，相同的数据需要合并，数量这些需要累加起来
    mergeArrays(tableData, newData) {
      // 创建一个空对象用于存储合并的结果
      let merged = {}
      // 遍历 tableData 数组
      for (let i = 0; i < tableData.length; i++) {
        let current = tableData[i]
        merged[(current.materials_id + '-' + current.supplier_manage_id + '-' + current.material_specification_id)] = current
      }
      newData.forEach(item => {
        // 存在相同数据，合并数量并重新计算合计
        let key = item.materials_id + '-' + item.supplier_manage_id + '-' + item.material_specification_id
        if (merged[key]) {
          merged[key].count = NP.plus(merged[key].count, item.count)
        } else {
        // 不存在直接往merged里面新增
          merged[key] = item
        }
      })
      return Object.values(merged)
    },
    // 物资去重，根据供应商id和物资id判断和规格id判断唯一，如有相同的则合并，数量累加
    uniqueMaterials(data, showMessage = false) {
      const arr = deepClone(data)
      const tmp = {}
      let isRepeat = false
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        let key = i
        // 当物资、供应商、规格都存在时才执行去重
        if (item.materials_id && item.supplier_manage_id && item.material_specification_id) {
          key = `${item.materials_id}_${item.supplier_manage_id}_${item.material_specification_id}`
        }
        if (!tmp[key]) {
          tmp[key] = item
        } else { // 如果存在相同物资和供应商的数据则合并数据
          isRepeat = true
          tmp[key].count = NP.plus(tmp[key].count, item.count)
        }
      }
      if (showMessage && isRepeat) {
        // 弹窗提示下
        this.mergeSupplierMaterials()
      }
      return Object.values(tmp)
    },
    // 当列表已存在相同供应商的物资时，弹窗显示是否合并
    mergeSupplierMaterials() {
      this.$confirm(`同一供应商、相同物资的申购数量已合并`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        showCancelButton: false,
        center: true,
        beforeClose: (action, instance, done) => {
          done()
        }
      })
        .then(e => {})
        .catch(e => {})
    },
  }
}
</script>

<style lang="scss">
.AddSubscriptionOrder {
  position: relative;
  h3 {
    margin: 0;
  }
  .m-b-0 {
    margin-bottom: 0;
  }
  .w-280 {
    width: 280px;
  }
  .w-160 {
    width: 160px !important;
  }
  .w-auto {
    width: 300px;
  }
  .error {
    ::v-deep .el-input__inner {
      border-color: red;
    }
  }
  .red {
    color: red;
    .ps-origin {
      color: red !important;
    }
  }
  &.form-container {
    ::v-deep.el-form-item {
      // margin-bottom: 10px;
      > .el-form-item__content {
        > .el-input {
          // width: 280px;
        }
      }
    }
    .upload-img {
      width: 90px;
      height: 90px;
    }
    .img-item {
      display: inline-block;
      position: relative;
      transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
      .img-tools {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ffffff;
        font-size: 16px;
        transition: 0.3s;
        i {
          cursor: pointer;
          color: #ff9b45;
        }
      }
      &:hover {
        .img-tools {
          display: inline-block;
        }
      }
    }
  }
}
.el-date-table td.selected span {
  color: #fff !important;
}
.right-btn {
  position: absolute;
  right: 20px;
  top: 20px;
}
</style>
