<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            <span>数据列表</span>
            <span style="font-size: 14px">
              当前汇总日期为 {{ searchFormSetting.select_time.value[0] }} ~
              {{ searchFormSetting.select_time.value[1] }}
            </span>
          </div>
        </div>
        <div class="align-r">
          <el-button size="mini" @click="gotoExport" v-permission="['background_report_center.data_report.group_wallet_daily_list_export']">导出Excel</el-button>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :span-method="arraySpanMethod"
            header-row-class-name="ps-table-header-row"
          />
          <!-- :span-method="arraySpanMethod" -->
        </div>
        <!-- table content end -->
        <div class="p-l-20 p-b-20">
          <div>当前汇总表统计储值钱包的数据</div>
          <div>本期余额=上期余额+充值总额-消费总额</div>
          <div>其中个人消费=消费+储值钱包工本费-储值钱包工本费退费</div>
        </div>
        <!-- 分页 start -->
        <!-- <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="total"
        ></pagination> -->
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { GROUP_PREPAID_SUMMARY } from './constantsConfig'
import { debounce, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { mergeRowAction } from '@/utils/table'
import report from '@/mixins/report' // 混入

export default {
  name: 'GroupWalletDaily',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        // { label: '序号', key: 'index', type: 'index', width: '80' },
        { label: '分组', key: 'group_name' },
        { label: '上期余额', key: 'pre_balance', type: 'money' },
        {
          label: '充值汇总余额',
          key: 'recharge',
          children: [
            { label: '充值', key: 'charge_fee', type: 'money' },
            { label: '提现', key: 'withdraw_fee', type: 'money' },
            { label: '充值退款', key: 'charge_refund_fee', type: 'money' },
            { label: '充值总额', key: 'total_charge_fee', type: 'money' }
          ]
        },
        {
          label: '消费汇总金额',
          key: 'consumption',
          children: [
            { label: '个人消费', key: 'consume_fee', type: 'money' },
            { label: '退款', key: 'consume_refund_fee', type: 'money' },
            { label: '部分退款', key: 'consume_part_refund_fee', type: 'money' },
            { label: '消费总额', key: 'total_consume_fee', type: 'money' }
          ]
        },
        { label: '本期余额', key: 'this_balance', type: 'money' }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: deepClone(GROUP_PREPAID_SUMMARY),
      rowMergeArrs: [],
      mergeOpts: {
        useKeyList: {
          //   person_no: ['name', 'person_no', 'phone', 'payer_group', 'payer_department_group']
        } // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        // mergeKeyList: [] // 通用的合并字段，根據值合并
      },
      printType: 'GroupWalletDaily'
    }
  },
  async mounted() {
    this.initLoad()
    this.initPrintSetting()
  },
  methods: {
    async initLoad() {
      this.getPersonPaymentList()
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      // this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getPersonPaymentList()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getPersonPaymentList() {
      this.isLoading = true
      // 没有分页
      const res = await this.$apis.apiBackgroundReportCenterDataReportGroupWalletDailyListPost({
        ...this.formatQueryParams(this.searchFormSetting)
        // page: this.currentPage,
        // page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.result
        if (res.data.result.length) {
          // 设置合计的值
          this.setSummaryData(res)
        }
        //   this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
      } else {
        this.$message.error(res.msg)
      }
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getPersonPaymentList()
    },
    gotoExport() {
      // 没有分页
      const option = {
        type: 'GroupWalletDaily',
        url: 'apiBackgroundReportCenterDataReportGroupWalletDailyListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting)
          // page: this.currentPage,
          // page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '分组储值汇总表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportGroupWalletDailyListPost', // 请求的api
          show_summary: true, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          push_summary: true, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.el-table {
  text-align: center;
  font-size: 12px;
}
</style>
