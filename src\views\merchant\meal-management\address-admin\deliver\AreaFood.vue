<template>
  <div class="AreaFood container-wrapper">
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <el-checkbox v-model="isSelectAll" @change="selectAllChange" :disabled="tableData.length <=0">全部选择</el-checkbox>
          <button-icon color="plain" type="export" @click="handleExport" v-permission="['background_order.reservation_order.get_delivery_area_collect_export']">导出报表</button-icon>
          <button-icon color="plain" @click="openDialog">小票打印</button-icon>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          header-row-class-name="ps-table-header-row"
          :span-method="objectSpanMethod"
          @selection-change="handleSelectionChange"
          @select="selectCheckOne"
          @select-all="selectAll"
          :header-cell-class-name="cellClass"
          :header-cell-style="{background:'#f4f3f9'}"
        >
          <el-table-column type="selection" prop="selection" align="center" class-name="ps-checkbox"></el-table-column>
          <table-column v-for="(item, index) in currentTableSetting" :key="index" :col="item"></table-column>
          <!-- <el-table-column prop="name" label="区域" align="center"></el-table-column>
          <el-table-column prop="food_name" label="菜品" align="center"></el-table-column>
          <el-table-column prop="food_count" label="数量" align="center"></el-table-column> -->
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <print-ticket
      :isshow.sync="printVisible"
      type="cateringArea"
      :select-list-id="selectOrderId"
      :printInfo="printInfo"
      @confirm="searchHandle"
    ></print-ticket>
    <!-- 报表设置 -->
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, getSevenDateRange, deepClone } from '@/utils'
import { MEAL_TYPES } from '@/utils/constants'
import { mergeHandle, mergeRowAction } from '@/utils/table'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import PrintTicket from '@/components/PrintTicket'
import searchInfoMixin from '@/mixins/searchMixin'
import { TABLE_SETTING_AREA_FOOD, SEARCH_FORM_SETTING_AREA_FOOD } from './constants'
import report from '@/mixins/report' // 混入
export default {
  name: 'AddressAreaAdmin',
  mixins: [exportExcel, searchInfoMixin, report],
  components: { PrintTicket },
  data() {
    return {
      // 报表设置
      dialogPrintVisible: false,
      printType: 'AreaFood',
      currentTableSetting: deepClone(TABLE_SETTING_AREA_FOOD),
      tableSetting: deepClone(TABLE_SETTING_AREA_FOOD),
      tableData: [],
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      isLoading: false, // 刷新数据
      searchFormSetting: this.initSearchFormSetting(),
      mergeOpts: {
        useKeyList: {
          name: ['selection']
        }, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        mergeKeyList: [
          'name'
        ] // 通用的合并字段，根據值合并
      },
      printVisible: false,
      selectOrderId: [],
      printInfo: {}, // 打印需要额外的参数
      isSelectAll: false, // 是否全部选择
      selectListIdCount: 0,
      organizationList: [] // 组织列表
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    // 监听父组件的tabType变化，当切换到当前组件时恢复搜索信息
    '$parent.tabType': {
      handler(newVal) {
        if (newVal === 'areaFood') {
          // 切换到areaFood tab时恢复搜索信息
          this._restore_searchInfo('areaFood')
        }
      },
      immediate: true
    },
    'searchFormSetting.org_ids.value': function() {
      this.getAddressAreaList()
    }
  },
  mounted() {
    this.getOrganizationList()
  },
  beforeDestroy() {
    // 组件销毁前保存搜索信息
    this._save_searchInfo('areaFood')
  },
  methods: {
    /**
     * 初始化搜索表单配置
     */
    initSearchFormSetting() {
      const searchFormSetting = deepClone(SEARCH_FORM_SETTING_AREA_FOOD)

      // 设置日期范围
      searchFormSetting.select_date.value = getSevenDateRange(1)

      // 设置餐段数据
      searchFormSetting.take_meal_time.dataList = MEAL_TYPES

      // 设置组织ID
      searchFormSetting.org_ids.value = [this.$store.getters.organization]

      return searchFormSetting
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = this.currentTableSetting.filter(item => {
        return item.key !== 'img'
      })
      const organizationStrs = this.findNamesByIds(this.organizationList, params.org_ids).join('、')
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          isUstomTableHeaders: true,
          print_title: '区域配餐表',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundOrderReservationOrderGetDeliveryAreaCollectPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          // collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          show_current_page: true, // 显示底部当前页码
          params: JSON.stringify({
            ...params,
            start_date: params.reservation_date_start,
            end_date: params.reservation_date_end,
            page: 1,
            page_size: this.totalCount ? this.totalCount : 10,
            mealType: MEAL_TYPES.find(item => item.value === params.take_meal_time)?.label || '',
            organizationStrs
          })
        }
      })
      window.open(href, '_blank')
    },
    initLoad() {
      this.getAreaFoodList()
      this.getAddressAreaList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.printVisible = false
        this.currentPage = 1
        this.resetSelectAll()
        // 保存搜索信息
        this._save_searchInfo('areaFood')
        this.getAreaFoodList()
      }
    }, 300),
    resetSearchHandle() {
      this.$refs.searchRef.resetForm()
      this.searchHandle()
    },
    resetHandle() {
      this.resetSelectAll()
      this.searchFormSetting.org_ids.value = [this.$store.getters.organization]
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' || data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.reservation_date_start = data[key].value[0]
            params.reservation_date_end = data[key].value[1]
          }
        }
      }
      return params
    },
    async getAreaFoodList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderReservationOrderGetDeliveryAreaCollectPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = []
        res.data.results.map((item, index) => {
          item.foods_st.map(food => {
            this.tableData.push({
              ...item,
              ...food
            })
          })
        })
        // if (this.isSelectAll) {
        //   this.$refs.tableData.toggleAllSelection()
        // }
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    // 分页页数change事件
    async handleSizeChange(val) {
      this.pageSize = val
      await this.getAreaFoodList()
      this.changeTableSelection()
    },
    // 分页页码change事件
    async handleCurrentChange(val) {
      this.currentPage = val
      await this.getAreaFoodList()
      this.changeTableSelection()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      // this.selectOrderId = []
      // val.forEach(item => {
      //   if (item.area_id) {
      //     this.selectOrderId.push(item.area_id)
      //   } else {
      //     this.$message.error('订单所属地址未关联区域，无法发起打印')
      //     this.$refs.tableData.toggleRowSelection(item);
      //   }
      // })
    },
    // 单选
    selectCheckOne(val, row) {
      console.log(val, row)
      if (!row.area_id) {
        this.$message.error('订单所属地址未关联区域，无法发起打印')
        this.$refs.tableData.toggleRowSelection(row, false);
        return
      }
      // isSelect true为选中 false 为取消
      let isSelect = val.length && val.indexOf(row) > -1
      // findIndex返回-1则不存在，>0则存在
      let n = this.selectOrderId.findIndex(v => {
        return v === row.area_id
      })
      if (isSelect) {
        if (n < 0) this.selectOrderId.push(row.area_id)
      } else {
        if (n > -1) this.selectOrderId.splice(n, 1)
      }
    },
    // 全选
    selectAll(arr) {
      // 和全选有问题 先去隐藏掉全选按钮 --mtj
      // let _arr = [...new Set(arr.map(res => res.companyId))]
      // this.select_box = _arr
    },
    changeTableSelection() {
      if (this.tableData.length) {
        this.tableData.forEach((item, index) => {
          // 匹配勾选上
          if (this.selectOrderId.includes(item.area_id)) {
            this.$nextTick(() => {
              this.$refs.tableData.toggleRowSelection(item);
            })
          }
        })
      }
    },
    async getAddressAreaList() {
      const res = await this.$apis.apiAddressAddersAreaAllPost({
        used_org_ids: this.searchFormSetting.org_ids.value
      })
      if (res.code === 0) {
        this.searchFormSetting.area_ids.dataList = [
          {
            name: '未命名区域',
            id: 0
          },
          ...res.data
        ]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出报表
    handleExport() {
      const option = {
        type: 'AreaFood',
        url: 'apiBackgroundOrderReservationOrderGetDeliveryAreaCollectPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          is_export: true
        }
      }
      this.exportHandle(option)
    },
    openDialog() {
      if (!this.selectOrderId.length) {
        return this.$message.error('请先选择数据！')
      }
      let info = this.formatQueryParams(this.searchFormSetting)
      this.printInfo = {
        reservation_date_start: info.reservation_date_start,
        reservation_date_end: info.reservation_date_end,
        take_meal_time: info.take_meal_time ? info.take_meal_time : ''
      }
      this.printVisible = true
    },
    /**
     * 全选监听
     */
    async selectAllChange(value) {
      this.selectOrderId = []
      this.$refs.tableData.clearSelection();
      if (value) {
        // 全选
        if (Reflect.has(this.$parent, 'getDeliveryAreaCollectIds')) {
          this.isLoading = true
          var list = await this.$parent.getDeliveryAreaCollectIds({
            ...this.formatQueryParams(this.searchFormSetting),
            page: this.currentPage,
            page_size: this.pageSize
          })
          this.isLoading = false
          if (list && list.length > 0) {
            this.selectOrderId = deepClone(list)
            this.selectListIdCount = list.length
            this.changeTableSelection()
          } else {
            this.isSelectAll = false
          }
        }
      } else {
        // 反选列表数据
        this.$refs.tableData.clearSelection()
        this.selectOrderId = []
      }
    },
    cellClass(row) {
      if (row.columnIndex === 0) {
        return 'areaFoodDisabledCheck';
      }
    },
    /**
     * 重置全选
     */
    resetSelectAll() {
      // if (this.isSelectAll) {
      this.isSelectAll = false
      this.selectOrderId = []
      this.$refs.tableData.clearSelection()
      // }
    },
    // 通过组织id获取组织名称
    findNamesByIds(treeData, selectedIds) {
      // 创建映射表：id -> name
      const idToNameMap = new Map();
      // 递归遍历树形结构
      function traverse(nodes) {
        nodes.forEach(node => {
          // 将当前节点的id和name存入映射表
          idToNameMap.set(node.id, node.name);
          // 如果存在子节点，递归遍历子节点
          if (node.children_list && node.children_list.length > 0) {
            traverse(node.children_list);
          }
        });
      }
      // 从根节点开始遍历
      traverse(treeData);
      // 根据选中的id列表获取对应的名称
      return selectedIds.map(id => idToNameMap.get(id) || null);
    },
    // 获取下级组织数据
    async getOrganizationList(id) {
      const res = await this.$apis.apiBackgroundOrganizationOrganizationTreeListPost({
        org_id: id
      })
      if (res.code === 0) {
        this.organizationList = res.data
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
  // 深度选择器 去掉全选按钮
  .el-table .areaFoodDisabledCheck .cell .el-checkbox__inner {
      display: none !important;
  }
</style>
