<template>
  <custom-drawer
    :title="title"
    :show.sync="visible"
    direction="rtl"
    :wrapperClosable="true"
    :size="760"
    :confirmShow="false"
    :fixedFooter="true"
    class="drawer-wrapper"
    cancel-text="关闭"
    @close="handlerClose"
    @cancel="clickCancleHandle"
  >
    <div class="p-20">
      <div class="search-wrapper m-b-20">
        <span class="inline-block font-size-14 m-r-10">评价时间</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          unlink-panels
          size="small"
          :clearable="false"
          style="width: 300px;"
          @change="changeDateHandle"
        ></el-date-picker>
        <span class="inline-block font-size-14 m-l-30 m-r-10">评价类型</span>
        <el-select v-model="typevalue" class="ps-select" popper-class="ps-popper-select" size="small" placeholder="请选择">
          <el-option v-for="option in quarterList" :key="option.value" :label="option.label" :value="option.value" ></el-option>
        </el-select>
      </div>
      <div class="">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          size="small"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="id"
          max-height="600"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="operationHandle('recovery', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div v-if="totalCount" class="block" style="text-align: right">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
    </div>
    <custom-drawer
      title="查看评价"
      :show.sync="showDetail"
      direction="rtl"
      :wrapperClosable="true"
      :size="760"
      :fixedFooter="true"
      class="drawer-wrapper"
      cancel-text="返回"
      confirm-text="关闭"
      @close="handlerClose"
      @cancel="clickCancleHandle"
      @confirm="clickConfirmHandle"
    >
      <div class="p-20 font-size-14">
        <div class="m-b-20">
          <span>来源组织：</span> 我是别的组织
        </div>
        <div class="m-b-20">
          <span>评价时间：</span> 2024年11月22日06:00:10
        </div>
        <div class="m-b-20">
          <span>评价类型：</span> 服务质量、物资价格、产品质量
        </div>
        <div class="m-b-20">
          <span>评价内容：</span> 三个方面都太棒了，很开心的一次采购
        </div>
        <div class="m-b-20">
          <span>附件：</span> 
        </div>
      </div>
    </custom-drawer>
  </custom-drawer>
  <!-- end -->
</template>

<script>
// 草稿箱
import { debounce, getSevenDateRange } from '@/utils'
export default {
  name: 'SupplierEvaluateDialog',
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: '' //
    },
    title: {
      type: String,
      default: '查看评价'
    },
    // 调用接口的默认参数
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    InfoData: {
      type: Object,
      default() {
        return {}
      }
    },
    tableSettings: {
      type: Array,
      default() {
        return [
          { label: '来源组织', key: 'name' },
          { label: '评价时间', key: 'name1' },
          { label: '评价类型', key: 'name2' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right' }
        ]
      }
    }
    // modifyfun: Function,
    // deletefun: Function
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      dateRange: getSevenDateRange(7),
      tableData: [{}],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      typevalue: '',
      quarterList: [],
      showDetail: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        // this.init()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 初始化
    init() {
      this.getDraftBoxList()
    },
    // 格式化下参数
    formatQueryParams(data) {
      let params = {}
      // for (const key in data) {
      //   if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
      //     if (key !== 'select_time') {
      //       params[key] = data[key].value
      //     } else if (data[key].value.length > 0) {
      //       params.start_date = data[key].value[0]
      //       params.end_date = data[key].value[1]
      //     }
      //   }
      // }
      if (this.dateRange.length > 0) {
        params.start_date = this.dateRange[0]
        params.end_date = this.dateRange[1]
      }
      return params
    },
    // 获取草稿箱列表数据
    async getDraftBoxList() {
      if (!this.api) {
        return this.$message.error('获取接口地址失败！')
      }
      this.isLoading = true
      const res = await this.$apis[this.api]({
        ...this.formatQueryParams(),
        ...this.params
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    changeDateHandle: debounce(function () {
      this.currentPage = 1
      this.getDraftBoxList()
    }, 300),
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDraftBoxList()
    },
    // 操作
    operationHandle(type, row) {
      this.showDetail = true
    },
    handlerClose(e) {
      this.isLoading = false
      // this.visible = false
      // this.$emit('close')
    },
    clickCancleHandle() {},
    clickConfirmHandle() {
      this.showDetail = false
    }
  }
}
</script>

<style lang="scss" scope>
.dialog-form {
}
</style>
