<template>
  <div class="MealApproveList container-wrapper">
    <div class="table-type">
      <div :class="['table-type-btn', tableType==='PENDING'?'active-btn':'']" @click="changeTableType('PENDING')"  v-permission="['background_approve.approve_order_visitor.pending_list']">待处理</div>
      <div :class="['table-type-btn', tableType==='FINISH'?'active-btn':'']" @click="changeTableType('FINISH')"  v-permission="['background_approve.approve_order_visitor.finish_list']">已处理</div>
      <div :class="['table-type-btn', tableType==='REVOKE'?'active-btn':'']" @click="changeTableType('REVOKE')" v-permission="['background_approve.approve_order_visitor.revoke_list']">撤销申请</div>
    </div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">
          <span>数据列表</span>
          <button-icon color="plain" v-if="tableType==='PENDING' && isSelectAll" @click="selectAll(true)">全选</button-icon>
          <button-icon color="plain" v-if="tableType==='PENDING' && !isSelectAll" @click="selectAll(false)">取消全选</button-icon>
        </div>
        <div class="align-r">
          <button-icon color="origin" @click="handleExport">导出</button-icon>
          <button-icon color="plain" type="mul" v-if="tableType==='PENDING'" @click="openDialog('mulRefuse')" v-permission="['background_approve.approve_order_visitor.reject_approve']">批量拒绝</button-icon>
          <button-icon color="origin" type="mul" v-if="tableType==='PENDING'" @click="mulOperation('mulAgree')" v-permission="['background_approve.approve_order_visitor.agree_approve']">批量通过</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" align="center" class-name="ps-checkbox"></el-table-column>
          <table-column v-for="item in tableSetting" :key="item.key" :col="item" show-overflow-tooltip>
            <template #tooltip="{ row }">
              <el-tooltip class="item" effect="dark" :content="row[item.key]" placement="top">
                <div>{{textFormat(row[item.key], 10)}}</div>
              </el-tooltip>
            </template>
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                v-if="tableType==='PENDING'"
                v-permission="['background_approve.approve_order_visitor.agree_approve']"
                @click="mulOperation('yes', row.id)"
                >通过</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                v-if="tableType==='PENDING'"
                @click="openDialog('refuse', row)"
                v-permission="['background_approve.approve_order_visitor.reject_approve']"
                >拒绝</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-green"
                v-if="tableType==='FINISH'"
                @click="openDialog('history', row)"
                >历史记录</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
        <div class="tips" v-if="tableType==='processing'">
          <div>1、通过需二次确认</div>
          <div>2、拒绝审批需写明原因（必填）；如是即付订单，需原路退款</div>
        </div>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="400px"
      top="30vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false">
      <div v-if="dialogType === 'history'">
        <el-table
          :data="selectInfo.record_list"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="time" label="操作时间" align="center"></el-table-column>
          <el-table-column prop="content" label="内容" align="center"></el-table-column>
          <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
        </el-table>
      </div>
      <div v-if="dialogType === 'refuse' || dialogType === 'mulRefuse'">
        <el-input v-model="refuseReason" class="ps-input w-300"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-btn" type="primary" @click="dialogVisible = false">关 闭</el-button>
        <el-button v-if="dialogType === 'refuse' || dialogType === 'mulRefuse'" class="ps-btn" type="primary" @click="comfirmDialog">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, deepClone, textFormat } from '@/utils'
import { confirm } from '@/utils/message'
import { PROCESSIMG_SEARCH, PROCESSED_SEARCH, REVOKE_SEARCH, PROCESSIMG_TABLE, PROCESSED_TABLE, REVOKE_TABLE, getRequestParams } from './constants'
export default {
  name: "MealApproveList",
  mixins: [exportExcel],
  data() {
    return {
      tableType: 'PENDING',
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: {},
      searchFormSetting: {},
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      selectInfo: {},
      refuseReason: '',
      selectListId: [],
      isSelectAll: false,
      timer: null
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.isSelectAll = true
      if (this.tableType === 'PENDING') {
        this.currentPage = 1
        this.searchFormSetting = deepClone(PROCESSIMG_SEARCH)
        this.tableSetting = PROCESSIMG_TABLE
      } else if (this.tableType === 'FINISH') {
        this.currentPage = 1
        this.searchFormSetting = deepClone(PROCESSED_SEARCH)
        this.tableSetting = PROCESSED_TABLE
      } else {
        this.currentPage = 1
        this.searchFormSetting = deepClone(REVOKE_SEARCH)
        this.tableSetting = REVOKE_TABLE
      }
      this.getApproveOrder()
      this.getApplyTypeList()
    },
    changeTableType(type) {
      this.tableType = type
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.printDialogVisible = false
      this.printTicketVisible = false
      this.getApproveOrder()
    }, 300),
    async getApproveOrder() {
      this.isLoading = true
      let api
      if (this.tableType === 'PENDING') {
        api = this.$apis.apiBackgroundApproveApproveOrderVisitorPendingListPost
      } else if (this.tableType === 'FINISH') {
        api = this.$apis.apiBackgroundApproveApproveOrderVisitorFinishListPost
      } else if (this.tableType === 'REVOKE') {
        api = this.$apis.apiBackgroundApproveApproveOrderVisitorRevokeListPost
      }
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await api({
        ...params,
        deal_status: this.tableType
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results.map(item => {
          item.meal_type_list = item.meal_type_alias_list.join('，')
          item.org_name = item.org_name_list.join('，')
          return item
        })
        this.totalCount = res.data.count
        this.$nextTick(() => {
          this.tableData.map(item => {
            if (!this.isSelectAll) {
              this.$refs.tableData.toggleRowSelection(item, true)
            } else {
              this.$refs.tableData.toggleRowSelection(item, false)
            }
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getApproveOrder()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getApproveOrder()
    },
    selectAll(val) {
      this.isSelectAll = !val
      if (!this.isSelectAll) {
        this.getApproveOrderId()
        this.tableData.map(row => {
          this.$refs.tableData.toggleRowSelection(row, true)
        })
      } else {
        this.tableData.map(row => {
          this.$refs.tableData.toggleRowSelection(row, false)
        })
      }
    },
    async getApproveOrderId() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundApproveApproveOrderVisitorPendingListPost({
        ...params,
        deal_status: this.tableType,
        only_get_id: true
      })
      this.isLoading = false
      if (res.code === 0) {
        this.selectListId = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => { this.selectListId.push(item.id) })
    },
    openDialog(type, data) {
      this.selectInfo = data
      this.dialogType = type
      if (this.dialogType === 'history') {
        this.dialogTitle = '历史记录'
      } else if (this.dialogType === 'refuse') {
        this.dialogTitle = '请输入拒绝原因'
      } else if (this.dialogType === 'mulRefuse') {
        if (!this.selectListId.length) return this.$message.error('请选择数据')
        this.dialogTitle = '请输入拒绝原因'
      }
      this.dialogVisible = true
    },
    comfirmDialog() {
      if (!this.refuseReason) return this.$message.error('请输入拒绝理由')
      if (this.dialogType === 'refuse') {
        this.confirmRefuse()
      } else if (this.dialogType === 'mulRefuse') {
        this.confirmMulRefuse()
      }
    },
    async confirmRefuse(id) {
      const res = await this.$apis.apiBackgroundApproveApproveOrderVisitorRejectApprovePost({
        id: this.selectInfo.id,
        reject_reason: this.refuseReason
      })
      if (res.code === 0) {
        this.$message.success('成功')
        this.dialogVisible = false
        this.getApproveOrder()
      } else {
        this.$message.error(res.msg)
      }
    },
    async confirmMulRefuse() {
      const res = await this.$apis.apiBackgroundApproveApproveOrderVisitorBulkRejectApprovePost({
        ids: this.selectListId,
        reject_reason: this.refuseReason
      })
      if (res.code === 0) {
        this.dialogVisible = false
        this.getExcelUrl(res.data.query_id)
        this.timer = setInterval(() => {
          this.getExcelUrl(res.data.query_id)
        }, 3000)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 操作提示
    mulOperation(type, data) {
      if (type === 'yes') {
        confirm({ content: '是否确定通过审核？' }).then(e => {
          this.confirmAgree(data)
        })
      } else if (type === 'mulAgree') {
        if (!this.selectListId.length) return this.$message.error('请选择数据')
        confirm({ content: '是否通过所选订单的访客申请？' }).then(e => {
          this.confirmMulAgree(data)
        })
      }
    },
    async confirmAgree(id) {
      const res = await this.$apis.apiBackgroundApproveApproveOrderVisitorAgreeApprovePost({
        id
      })
      if (res.code === 0) {
        this.$message.success('成功')
        this.getApproveOrder()
      } else {
        this.$message.error(res.msg)
      }
    },
    async confirmMulAgree(id) {
      const res = await this.$apis.apiBackgroundApproveApproveOrderVisitorBulkAgreeApprovePost({
        ids: this.selectListId
      })
      if (res.code === 0) {
        this.getExcelUrl(res.data.query_id)
        this.timer = setInterval(() => {
          this.getExcelUrl(res.data.query_id)
        }, 3000)
      } else {
        this.$message.error(res.msg)
      }
    },
    async getExcelUrl(queryId) {
      try {
        this.isLoading = true
        const res = await this.$apis.apiBackgroundBaseTasksExportQueryPost({
          query_id: queryId
        })
        if (res.code === 0) {
          if (res.data.status === 'success') {
            this.getApproveOrder()
            clearTimeout(this.timer)
            this.isLoading = false
          } else if (res.data.status === 'failure') {
            clearTimeout(this.timer)
          }
        } else {
          this.$message.error(res.msg)
          clearTimeout(this.timer)
          this.isLoading = false
        }
      } catch (error) {
        // this.progress = 0
        this.$message.error('内部服务错误')
        clearTimeout(this.timer)
        this.isLoading = false
      }
    },
    async getApplyTypeList(id) {
      const res = await this.$apis.apiBackgroundApproveApproveOrderVisitorGetApplyTypeListPost({
        id
      })
      if (res.code === 0) {
        if (this.searchFormSetting.apply_type_list) {
          this.searchFormSetting.apply_type_list.dataList = []
          res.data.map(item => {
            this.searchFormSetting.apply_type_list.dataList.push({
              label: item,
              value: item
            })
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    handleExport() {
      let url
      if (this.tableType === 'PENDING') {
        url = 'apiBackgroundApproveApproveOrderVisitorPendingListExportPost'
      } else if (this.tableType === 'FINISH') {
        url = 'apiBackgroundApproveApproveOrderVisitorFinishListExportPost'
      } else if (this.tableType === 'REVOKE') {
        url = 'apiBackgroundApproveApproveOrderVisitorRevokeListExportPost'
      }
      const paramsData = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const option = {
        type: 'MealApproveList',
        url,
        params: {
          ...paramsData,
          deal_status: this.tableType,
          page: 1,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    },
    textFormat
  }
}
</script>
<style lang="scss" scoped>
.MealApproveList{
  .table-type{
    padding: 20px 0;
    display: flex;
    font-size: 16px;
    .table-type-btn{
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      background-color: #E8F0F8;
      border-radius: 40px;
      margin-right: 20px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn{
      color: #FFF;
      background-color: #ff9b45;
      border: none;
    }
  }
  .table-title{
    display: flex;
    align-items: center;
  }
  .tips{
    color: #ff9b45;
    font-size: 14px;
    margin-top: 15px;
  }
}
</style>
