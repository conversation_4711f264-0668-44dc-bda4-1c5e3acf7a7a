<template>
  <div class="AbnormalVideoList container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">设备列表</div>
        <div class="align-r">
          <button-icon name="批量删除" color="plain" type="mul" @click="mulOperation('mulDel')" v-permission="['background_device.abnormal_video.delete']">
            批量删除
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          :data="tableData"
          v-loading="isLoading"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="clickDialogVideo(row)">
                查看
              </el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 视频 -->
    <el-dialog
      v-if="dialogVideoVisible"
      title="视频"
      :visible.sync="dialogVideoVisible"
      width="30%"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="true"
    >
      <div>
        <videoPlayer height="500" :src="videoStc" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="dialogVideoVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
export default {
  name: 'DeviceList',
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      selectListId: [],
      tableData: [],
      tableSetting: [
        { label: '', key: 'selection', type: 'selection' },
        { label: '上传时间', key: 'update_time' },
        { label: '生成时间', key: 'create_time' },
        { label: '设备名', key: 'device_name' },
        { label: '设备地址', key: 'device_mac' },
        { label: '涉及订单号', key: 'trade_no' },
        {
          label: '查看',
          key: 'operation',
          type: 'slot',
          slotName: 'operation',
          fixed: 'right',
          width: '100'
        }
      ],
      searchFormSetting: {
        upload_date: {
          clearable: false,
          type: 'datetimerange',
          label: '上传日期',
          value: []
        },
        generate_date: {
          clearable: false,
          type: 'datetimerange',
          label: '生成日期',
          value: []
        },
        device_name: {
          type: 'input',
          label: '设备名',
          value: '',
          placeholder: '请输入设备名'
        },
        trade_no: {
          type: 'input',
          label: '涉及订单号',
          value: '',
          placeholder: '请输入涉及订单号'
        }
      },
      dialogVideoVisible: false,
      videoStc: '' // 视频地址
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDeviceAbnormalVideoList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getDeviceAbnormalVideoList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.tableData = []
      this.initLoad()
    },
    handleSelectionChange(val) {
      this.selectListId = val.map(item => {
        return item.id
      })
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'upload_date') {
            params.upload_start_time = data[key].value[0]
            params.upload_end_time = data[key].value[1]
          } else if (key === 'generate_date') {
            params.created_start_time = data[key].value[0]
            params.created_end_time = data[key].value[1]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    async getDeviceAbnormalVideoList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceAbnormalVideoListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDeviceAbnormalVideoList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDeviceAbnormalVideoList()
    },
    mulOperation() {
      if (!this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.$confirm(`确定删除吗?`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.cancelButtonLoading = true
            let params = {
              ids: this.selectListId
            }
            const [err, res] = await this.$to(
              this.$apis.apiBackgroundDeviceAbnormalVideoDeletePost(params)
            )
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (
                  this.currentPage === this.totalPageSize &&
                  this.selectListId.length === this.tableData.length
                ) {
                  this.currentPage--
                }
              }
              this.getDeviceAbnormalVideoList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    clickDialogVideo(item) {
      this.dialogVideoVisible = true
      this.videoStc = item.video_url
    }
  }
}
</script>

<style lang="scss">
// @import '~@/styles/variables.scss';
</style>
