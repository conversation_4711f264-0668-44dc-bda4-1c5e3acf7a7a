<template>
  <div class="AutoLabel container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="clickAutoDialog('add')">新增</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" @click="clickAutoDialog('modify', row)">
                编辑
              </el-button>
              <el-button type="text" size="small" class="ps-red" @click="clickDelete(row)">
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <auto-label-dialog
      v-if="autoLabelDialogVisible"
      :isshow.sync="autoLabelDialogVisible"
      :title="autoLabelDialogTitle"
      :type="autoLabelDialogType"
      :infoData="autoLabelInfo"
      @confirm="searchHandle"
    />
  </div>
</template>
<script>
import { debounce, getRequestParams, to } from '@/utils'
// import { AUTO_LABEL } from './constants'
import AutoLabelDialog from './AutoLabelDialog.vue'
import { confirm } from '@/utils/message'
export default {
  name: 'deviceCloudauto',
  components: { AutoLabelDialog },
  data() {
    return {
      tableType: 'foodLabel',
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '标签名称', key: 'label_name' },
        { label: '元素名称', key: 'nutrition_key_alias' },
        { label: '标签规则', key: 'rule_alias' },
        { label: '更新时间', key: 'update_time' },
        { label: '操作人', key: 'operator_name' },
        {
          label: '操作',
          key: 'operation',
          type: 'slot',
          slotName: 'operation',
          fixed: 'right',
          width: '180'
        }
      ],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '标签名称',
          value: ''
        }
      },
      autoLabelDialogVisible: false,
      autoLabelDialogType: '',
      autoLabelDialogTitle: '',
      autoLabelInfo: {}
    }
  },
  created() {
    this.initLoad()
  },
  beforeDestroy() {
    clearInterval(this.time)
  },
  mounted() {},
  methods: {
    initLoad() {
      this.currentPage = 1
      this.getAutoLabelList()
    },
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.getAutoLabelList()
    },
    changeTableType(type) {
      this.tableType = type
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getAutoLabelList()
    }, 300),
    async getAutoLabelList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      let [err, res] = await to(this.$apis.apiBackgroundHealthyAdminAutoLabelListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    async getAutoLabelDelete() {
      this.isLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundHealthyAdminAutoLabelDeletePost({
          id: this.autoLabelInfo.id,
          confirm: this.autoLabelInfo.confirm
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('删除成功')
        // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
        if (this.currentPage > 1) {
          if (this.tableData.length === 1) {
            this.currentPage--
          }
        }
        this.getAutoLabelList()
      } else if (res.code === 2) {
        this.autoLabelInfo.confirm = true
        confirm({ content: '该标签已有菜品应用，确认删除？', center: true, confirm_class: 'ps-warn' }, this.getAutoLabelDelete).catch(e => {
          this.autoLabelInfo.confirm = false
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    clickDelete(row) {
      this.autoLabelInfo = row
      this.autoLabelInfo.confirm = false
      confirm({ content: '删除后数据不可恢复，确定要删除？', center: true, confirm_class: 'ps-warn' }, this.getAutoLabelDelete)
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getAutoLabelList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getAutoLabelList()
    },
    clickAutoDialog(type, data) {
      this.autoLabelDialogType = type
      this.autoLabelDialogVisible = true
      if (type === 'add') {
        this.autoLabelDialogTitle = '新增'
      } else if (type === 'modify') {
        this.autoLabelInfo = data
        this.autoLabelDialogTitle = '编辑'
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.AutoLabel {
  .table-type {
    padding: 20px 0;
    display: flex;
    font-size: 16px;
    .table-type-btn {
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      background-color: #e8f0f8;
      border-radius: 10px;
      margin-right: 20px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn {
      color: #fff;
      background-color: #ff9b45;
      border: none;
    }
  }
}
</style>
