import { setSessionStorage, getSessionStorage, getTreeDeepkeyList, findRouteActiveNav, deepClone } from "@/utils"
import { asyncRoutes } from '@/router'
const state = {
  activeNavMenu: '', // 激活的
  navMenuList: [], // 头部菜单栏 { name: '', key: '', activePath: '', isActive: true }
  activeRoute: '', // 当前激活的路由
  indexRoute: '', // 默认首页
  visitedViews: [], // tabs
  cachedViews: [], // 需缓存的tab
  maxCachedViews: 15 // 最大可缓存的页面数量
}
let flag = true
export function getFirstHasPermissionPath(currentNavRoute, path, permissions) {
  for (let index = 0; index < currentNavRoute.length; index++) {
    let item = currentNavRoute[index]
    if (item.children) {
      path.push(item.path)
      getFirstHasPermissionPath(item.children, path, permissions)
    } else {
      permissions.map(per => {
        if (flag && !item.hidden && item.meta && item.meta.permission && (item.meta.permission.includes(per) || item.meta.no_permission)) {
          path.push(item.path)
          flag = false
        }
      })
      if (flag && index === currentNavRoute.length - 1) {
        path.splice(path.length - 1, 1)
      }
    }
    if (!flag) break
  }
  return path.join('/')
}

const mutations = {
  SET_ACTIVEROUTE: (state, data) => {
    state.activeRoute = data
  },
  SET_ACTIVENAVMENU: (state, data) => {
    state.activeNavMenu = data
  },
  SET_NAVMENULIST: (state, data) => {
    state.navMenuList = Object.assign([], data)
  },
  SET_INDEXROUTE: (state, data) => {
    state.indexRoute = data
  },
  ADD_VISITED_VIEW: (state, view) => {
    // 可能存在当前路由已经添加单下次还是进入当前页面需要替换当前保存的参数
    const visitedIndex = state.visitedViews.findIndex(v => v.path === view.path)
    // state.visitedViews.some(v => v.path === view.path)
    if (visitedIndex > -1) { // 更新数据，当前只判断query或params是否有值，有则更新
      const hasQuery = Object.keys(view.query)
      const hasParams = Object.keys(view.params)
      if (hasQuery.length || hasParams.length) {
        console.log('view update')
        state.visitedViews[visitedIndex] = Object.assign({}, view, {
          title: view.meta.title || 'no-name'
        })
      }
    } else { // 新增的
      state.visitedViews.push(
        Object.assign({}, view, {
          title: view.meta.title || 'no-name'
        })
      )
    }
  },
  ADD_CACHED_VIEW: (state, view) => {
    if (state.cachedViews.includes(view.name)) return
    if (!view.meta.noCache) {
      state.cachedViews.push(view.name)
    }
  },

  DEL_VISITED_VIEW: (state, view) => {
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.path === view.path) {
        state.visitedViews.splice(i, 1)
        break
      }
    }
  },
  DEL_CACHED_VIEW: (state, view) => {
    const index = state.cachedViews.indexOf(view.name)
    index > -1 && state.cachedViews.splice(index, 1)
  },

  DEL_OTHERS_VISITED_VIEWS: (state, view) => {
    state.visitedViews = state.visitedViews.filter(v => {
      return v.meta.affix || v.path === view.path
    })
  },
  DEL_OTHERS_CACHED_VIEWS: (state, view) => {
    const index = state.cachedViews.indexOf(view.name)
    if (index > -1) {
      state.cachedViews = state.cachedViews.slice(index, index + 1)
    } else {
      // if index = -1, there is no cached tags
      state.cachedViews = []
    }
  },

  DEL_ALL_VISITED_VIEWS: state => {
    // keep affix tags
    const affixTags = state.visitedViews.filter(tag => tag.meta.affix)
    state.visitedViews = affixTags
  },
  DEL_ALL_CACHED_VIEWS: state => {
    state.cachedViews = []
  },

  UPDATE_VISITED_VIEW: (state, view) => {
    for (let v of state.visitedViews) {
      if (v.path === view.path) {
        v = Object.assign(v, view)
        break
      }
    }
  }
}

const actions = {
  // 设置当前激活的导航栏
  setActiveRoute({ commit }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_ACTIVEROUTE', data)
      resolve()
    })
  },
  setActiveNavMenu({ commit, state }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_ACTIVENAVMENU', data)
      resolve()
    })
  },
  setNavMenuList({ commit, state }, data) {
    return new Promise((resolve, reject) => {
      setSessionStorage('navMenuList', JSON.stringify(data))
      commit('SET_NAVMENULIST', data)
      resolve()
    })
  },
  // 设置默认情况下的Menu，例如F5刷新后
  setDefaultMenu({ commit, dispatch, state, rootGetters }, data) {
    let activeNavMenu = ''
    let navPermission = []
    let allAsyncRoutes = deepClone(asyncRoutes)
    if (state.activeRoute === '/') {
      let id = this.state.user.userInfo.account_type
      if (id !== 3) { // 非超管端进去显示首页 超管端显示系统设置
        activeNavMenu = data && data.length > 0 ? data[0].key : ''
      } else {
        activeNavMenu = data && data.length > 0 ? data[data.length - 1].key : ''
      }
      // let redirectRoute = ''
      // for (let index = 0; index < asyncRoutes.length; index++) {
      //   if (asyncRoutes[index].meta && asyncRoutes[index].meta.permission && asyncRoutes[index].meta.permission.includes(activeNavMenu)) {
      //     redirectRoute = asyncRoutes[index].redirect
      //     navPermission = asyncRoutes[index].meta.permission
      //     break
      //   }
      // }
      // if (redirectRoute) {
      //   // dispatch('setActiveRoute', redirectRoute)
      //   commit('SET_ACTIVEROUTE', redirectRoute)
      // }
    } else {
      activeNavMenu = findRouteActiveNav(asyncRoutes, state.activeRoute, rootGetters.navMenuList)
    }
    console.log(123, activeNavMenu, navPermission)
    // 获取顶部菜单的key数组
    // 每次登录这玩意都是空数组，因为退出登录后会重置
    let oldNavMenuList = []
    try {
      oldNavMenuList = JSON.parse(getSessionStorage('navMenuList'))
    } catch (error) {
      console.log('parse navMenuList error')
    }
    let currentNavPermission = []
    // f5手动刷新时第一次是拿不到meta里的信息的，去路由表查吧
    let navMenuList = data.map(v => {
      let isActiveNav = false
      const permissions = getTreeDeepkeyList([v])
      if (activeNavMenu === v.key) {
        activeNavMenu = v.key
        isActiveNav = true
        currentNavPermission = permissions
      }
      let oldMenuData = ''
      if (oldNavMenuList && oldNavMenuList.length > 0) {
        for (let index = 0; index < oldNavMenuList.length; index++) {
          const nav = oldNavMenuList[index];
          if (nav.key === v.key) {
            oldMenuData = nav
            break
          }
        }
      }
      // let indexPath = ''
      // for (let index = 0; index < asyncRoutes.length; index++) {
      //   if (asyncRoutes[index].meta && asyncRoutes[index].meta.permission && asyncRoutes[index].meta.permission.includes(v.key)) {
      //     indexPath = asyncRoutes[index].path
      //     break
      //   }
      // }
      let indexPath = '' // 初始化页面保存第一个path
      let defaultActivePath = '' // 初始化页面保存第一个activePath，用来默认跳转的第一个页面
      allAsyncRoutes.map(item => {
        permissions.map(per => {
          if (item.meta && item.meta.permission && (item.meta.permission.includes(per) || item.meta.no_permission)) { // 符合权限的侧边栏
            if (!defaultActivePath) {
              if (item.children) {
                flag = true
                defaultActivePath = getFirstHasPermissionPath(item.children, [item.path], permissions)
              }
            }
            if (!indexPath) {
              indexPath = item.path
            }
          }
        })
      })
      return {
        name: v.verbose_name,
        key: v.key,
        indexPath: indexPath,
        test: defaultActivePath,
        activePath: oldMenuData.activePath ? oldMenuData.activePath : defaultActivePath, // 预留下吧
        isActive: isActiveNav, // 同上
        permissions: permissions // 当前nav所有权限看的侧栏
      }
    })
    if (state.activeRoute === '/' && navMenuList.length) {
      let redirectRoute = navMenuList.find(item => activeNavMenu === item.key).activePath
      if (redirectRoute) {
        // dispatch('setActiveRoute', redirectRoute)
        commit('SET_ACTIVEROUTE', redirectRoute)
      }
    }
    return new Promise((resolve, reject) => {
      // dispatch('user/setPermissions', currentNavPermission, { root: true })
      // 保存activeNavMenu
      commit('SET_ACTIVENAVMENU', activeNavMenu)
      // 设置下navMenuList
      commit('SET_NAVMENULIST', navMenuList)
      setSessionStorage('navMenuList', JSON.stringify(navMenuList))
      // dispatch('setNavMenuList', navMenuList)
      resolve(currentNavPermission)
    })
  },
  // 根据path还原headerMenu
  setHeaderMenu({ commit, dispatch, state, rootGetters }, routepath) {
    console.log('routepath', routepath)
    let activeNavMenu = ''
    // if (routepath === '/') { // 这步按照当前配置的话应该是根本就不会走
    //   activeNavMenu = rootGetters.navMenuList.length > 0 ? rootGetters.navMenuList[0].key : ''
    //   for (let index = 0; index < asyncRoutes.length; index++) {
    //     if (asyncRoutes[index].meta && asyncRoutes[index].meta.permission && asyncRoutes[index].meta.permission.includes(activeNavMenu)) {
    //       navPermission = asyncRoutes[index].meta.permission
    //       break
    //     }
    //   }
    // } else {
    //   navPermission = findRoutePermission(asyncRoutes, routepath, rootGetters.navMenuList)
    // }
    activeNavMenu = findRouteActiveNav(asyncRoutes, routepath, rootGetters.navMenuList)
    let currentNavPermission = []
    const newNavMenuList = rootGetters.navMenuList.map(v => {
      const permissions = v.permissions
      if (activeNavMenu === v.key) {
        activeNavMenu = v.key
        v.isActive = true
        v.activePath = routepath
        currentNavPermission = permissions
      } else {
        v.isActive = false
      }
      return v
    })

    return new Promise((resolve, reject) => {
      commit('SET_ACTIVENAVMENU', activeNavMenu)
      dispatch('setNavMenuList', newNavMenuList)
      resolve(currentNavPermission)
    })
  },
  updateHeaderMenuActivePath({ commit, dispatch, state, rootGetters }, routepath) {
    let activeNavMenu = findRouteActiveNav(asyncRoutes, routepath, rootGetters.navMenuList)
    const newNavMenuList = rootGetters.navMenuList.map(v => {
      if (activeNavMenu === v.key) {
        v.isActive = true
        v.activePath = routepath
      } else {
        v.isActive = false
      }
      return v
    })

    return new Promise((resolve, reject) => {
      dispatch('setNavMenuList', newNavMenuList)
      resolve(newNavMenuList)
    })
  },
  // 更新下当前激活栏的activePath， 暂时用于删除tab食用
  updateNavMenuActive({ commit, dispatch, state, rootGetters }, view) {
    const newNavMenuList = rootGetters.navMenuList.map(v => {
      const permissions = v.permissions
      let hasTab = rootGetters.visitedViews.some(item => {
        if (item.meta && item.meta.permission) {
          return item.meta.permission.some(p => permissions.includes(p))
        } else {
          return false
        }
      })
      if (hasTab) { // 算了直接清空吧，退回上一个tab好麻烦
        v.activePath = ''
      }
      return v
    })
    return new Promise((resolve, reject) => {
      dispatch('setNavMenuList', newNavMenuList)
      resolve(newNavMenuList)
    })
  },
  async upadtePermissions({ commit, dispatch, state, rootGetters }, data) {
    let permissions = []
    state.navMenuList.forEach(v => {
      if (v.key === state.activeNavMenu) {
        permissions = v.permissions
      }
    })
    await dispatch('user/setPermissions', permissions, { root: true })
    dispatch('permission/changeSlideMenu', null, { root: true })
  },
  addView({ dispatch }, view) {
    dispatch('addVisitedView', view)
    dispatch('addCachedView', view)
  },
  addVisitedView({ commit }, view) {
    commit('ADD_VISITED_VIEW', view)
  },
  addCachedView({ commit }, view) {
    commit('ADD_CACHED_VIEW', view)
  },

  delView({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delVisitedView', view)
      dispatch('delCachedView', view)
      dispatch('updateNavMenuActive', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delVisitedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_VISITED_VIEW', view)
      resolve([...state.visitedViews])
    })
  },
  delCachedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_CACHED_VIEW', view)
      resolve([...state.cachedViews])
    })
  },

  delOthersViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delOthersVisitedViews', view)
      dispatch('delOthersCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delOthersVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_VISITED_VIEWS', view)
      resolve([...state.visitedViews])
    })
  },
  delOthersCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_CACHED_VIEWS', view)
      resolve([...state.cachedViews])
    })
  },

  delAllViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delAllVisitedViews', view)
      dispatch('delAllCachedViews', view)
      dispatch('setActiveRoute', '')
      dispatch('setActiveNavMenu', '')
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delAllVisitedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_VISITED_VIEWS')
      resolve([...state.visitedViews])
    })
  },
  delAllCachedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_CACHED_VIEWS')
      resolve([...state.cachedViews])
    })
  },

  updateVisitedView({ commit }, view) {
    commit('UPDATE_VISITED_VIEW', view)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
