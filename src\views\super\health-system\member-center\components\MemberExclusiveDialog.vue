<template>
  <!--权益弹窗-->
  <el-dialog :title="title" :visible.sync="isShowDialog" :width="width" custom-class="ps-dialog"
    :close-on-press-escape="false" :close-on-click-modal="false" @close="handlerClose">
    <el-form ref="memberFormRef" :model="dialogForm" label-width="110px" :rules="dialogFormRules">
      <div v-if="type == 'add' || type == 'edit'">
        <el-form-item label="权益" prop="exclusive">
          <el-select v-model="dialogForm.exclusive" placeholder="请选择" class="w-250">
            <el-option v-for="item in exclusiveList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="有效期" prop="validityDate">
          <el-input v-model="dialogForm.validityDate" class="w-250" placeholder="请输入有效期" autocomplete="new-password"
            readOnly onfocus="this.removeAttribute('readonly')" onblur="this.setAttribute('readonly',true)"
            type="number"></el-input>
        </el-form-item>
        <el-form-item>
          <div class="ps-flex">
            <div :class="['day-item', chooseCurrentIndex == 0 ? 'active' : 'no-active']"
              @click="handlerChooseDate(0, 30)">
              30天</div>
            <div :class="['day-item', chooseCurrentIndex == 1 ? 'active' : 'no-active']"
              @click="handlerChooseDate(1, 90)">
              90天</div>
            <div :class="['day-item', chooseCurrentIndex == 2 ? 'active' : 'no-active']"
              @click="handlerChooseDate(2, 180)">
              180天</div>
          </div>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input v-model="dialogForm.price" class="w-250" placeholder="请输入价格(元)" autocomplete="new-password" readOnly
            onfocus="this.removeAttribute('readonly')" onblur="this.setAttribute('readonly',true)"></el-input>
        </el-form-item>
        <el-form-item label="说明" prop="remark">
          <el-input v-model="dialogForm.remark" class="w-250" placeholder="最多输入12个字" type="textarea" maxlength="12"
            show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="dialogForm.sort" class="w-250" placeholder="数字越小，位置越靠前" type="number"></el-input>
        </el-form-item>
        <el-form-item>
          <div>已有排序：{{ alreadySort }}</div>
        </el-form-item>
      </div>
      <div v-if="type == 'send'">
        <el-form-item label="用户手机号" prop="phone">
          <el-select v-model="dialogForm.phone" filterable placeholder="请输入手机号关键字搜索" remote reserve-keyword
            :remote-method="getPhoneList" :loading="phoneLoading" @change="phoneChooseChange">
            <el-option v-for="item in phoneList" :key="item.id" :label="item.phone" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户姓名" prop="name">
          <div>{{ dialogForm.name }}</div>
        </el-form-item>
        <el-form-item label="用户ID" prop="id">
          <div>{{ dialogForm.id }}</div>
        </el-form-item>
        <el-form-item label="发放权益" prop="distribute">
          <el-select v-model="dialogForm.distribute" placeholder="请选择" class="w-250">
            <el-option v-for="item in distributeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button class="ps-cancel-btn" @click="isShowDialog = false">取 消</el-button>
      <el-button class="ps-btn" type="primary" @click="handlerBtnConfirm" v-loading="isConfirmLoading">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { to, deepClone, times, divide } from '@/utils/index'

export default {
  name: 'MemberExclusiveDialog',
  props: {
    type: { // 弹窗类型
      type: String,
      default: ''
    },
    title: { // 弹窗标题
      type: String,
      default: ''
    },
    width: { // 弹窗的宽度
      type: String,
      default: '450px'
    },
    seqsList: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data() {
    let validataNumber = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        let number = /^[1-9][0-9]{0,2}$/
        if (!number.test(value)) {
          callback(new Error('请输入大于零的正整数，上限为999'))
        } else {
          callback()
        }
      }
    };
    let validataPrice = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        let number = /((^[1-9][0-9]{0,2})|(([0]\.\d{1,2}|^[1-9][0-9]{0,2}\.\d{1,2})))$/
        if (!number.test(value) || value === '0.0' || value === '0.00') {
          callback(new Error('请输入大于零的数值，上限为999.99'))
        } else {
          callback()
        }
      }
    };
    return {
      isLoading: false,
      dialogForm: {
        exclusive: '', // 权益
        validityDate: '', // 有效日期
        price: '', // 价格
        remark: '', // 说明
        sort: '', // 排序
        phone: '', // 手机号
        name: '', // 姓名
        distribute: '', // 发放权益
        id: '' // 用户ID
      },
      dialogFormRules: {
        exclusive: [
          { required: true, message: '请选择权益', trigger: 'change' }
        ],
        validityDate: [
          { required: true, message: '请输入有效期天数', trigger: 'blur' },
          { required: true, validator: validataNumber, trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入价格', trigger: 'blur' },
          { required: true, validator: validataPrice, trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请输入说明', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号搜索选择', trigger: 'change' }
        ],
        id: [
          { required: true, message: '用户ID不能为空，请重新选择用户手机号', trigger: 'change' }
        ],
        distribute: [
          { required: true, message: '请选择方法权益', trigger: 'change' }
        ]
      },
      autoLabelNameList: [],
      autoLabelIdList: [],
      labelNameList: [],
      labelList: [],
      exclusiveList: [], // 权益列表
      distributeList: [], // 发放权益列表
      alreadySort: "", // 已有排序
      isShowDialog: false, // 是否显示
      chooseCurrentIndex: -1, // 选择的天数
      phoneList: [], // 手机号列表
      phoneLoading: '' // 查找手机loading
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShowDialog
      },
      set(val) {
        this.$emit('update:isShowDialog', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.initData()
      } else {
        this.$refs.memberFormRef.resetFields()
      }
    },
    seqsList(val) {
      console.log("seqsList", val);
      if (val && Array.isArray(val)) {
        this.alreadySort = val.join(',')
      }
    }
  },
  created() {
  },
  mounted() { },
  methods: {
    // 初始化数据
    initData() {
      console.log("this.type", this.type);
      if (this.type === 'send') {
        this.getRightsSettingList()
        this.getPhoneList()
      }
      this.getMemberLabel()
    },
    // 设置弹窗是否展示
    showDialog(isShow) {
      this.isShowDialog = isShow
      // 如果弹窗关闭，重置弹窗属性
      if (!isShow) {
        this.handleClose()
      } else {
        this.initData()
      }
    },
    // 设置弹窗数据
    setDialogData(itemData) {
      console.log("setDialogData", itemData);
      if (itemData && typeof itemData === 'object') {
        var dialogForm = deepClone(this.dialogForm)
        var memberPermissions = itemData.member_permissions && itemData.member_permissions.length > 0 ? itemData.member_permissions[0] : ""
        dialogForm.id = itemData.id || ''
        dialogForm.exclusive = memberPermissions
        dialogForm.validityDate = itemData.days || ''
        dialogForm.price = divide(itemData.origin_fee) || ''
        dialogForm.sort = itemData.seq || ''
        dialogForm.remark = itemData.remark || ''
        console.log("dialogForm", dialogForm);
        this.$set(this, 'dialogForm', dialogForm)
      }
    },
    // 确定按钮
    handlerBtnConfirm() {
      this.$refs.memberFormRef.validate(valid => {
        if (valid) {
          let params = {}
          let api
          let successMsg = ''
          switch (this.type) {
            case 'add': // 添加权益
              params = {
                member_permissions: [this.dialogForm.exclusive], // 权益
                days: this.dialogForm.validityDate, // 有效日期
                origin_fee: times(this.dialogForm.price), // 价格
                remark: this.dialogForm.remark, // 说明
                seq: this.dialogForm.sort // 排序
              }
              api = this.$apis.apiBackgroundMemberRightsSettingAddPost(params)
              successMsg = '权益添加成功'
              break;
            case 'edit': // 编辑权益
              params = {
                id: this.dialogForm.id,
                member_permissions: [this.dialogForm.exclusive], // 权益
                days: this.dialogForm.validityDate, // 有效日期
                origin_fee: times(this.dialogForm.price), // 价格
                remark: this.dialogForm.remark, // 说明
                seq: this.dialogForm.sort // 排序
              }
              api = this.$apis.apiBackgroundMemberRightsSettingModifyPost(params)
              successMsg = '权益编辑成功'
              break;
            case 'send': // 手动发放权益
              params = {
                receive_type: 'manual_release', // 手动发放
                rights_setting: this.dialogForm.distribute, // 发放权益
                user_id: this.dialogForm.id // 用户ID
              }
              api = this.$apis.apiBackgroundMemberRightsReceiveAddPost(params)
              successMsg = '手动发放权益成功'
              break;
          }
          this.confirmOperation(api, successMsg)
        }
      })
    },
    // 联网修改数据
    async confirmOperation(api, successMsg) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(api)
      this.isLoading = false
      if (err) {
        return this.$message.error(err.message)
      }
      if (res && res.code === 0) {
        this.$message.success(successMsg)
        this.$emit('confirm', this.type)
        this.isShowDialog = false
      } else {
        this.$message.error(res.msg || '失败')
      }
    },
    // 关闭
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.memberFormRef.resetFields()
    },
    // 获取会员标签
    async getMemberLabel() {
      const res = await this.$apis.apiBackgroundMemberMemberPermissionListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.exclusiveList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取权益列表
    async getRightsSettingList() {
      const res = await this.$apis.apiBackgroundMemberRightsSettingListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        var list = res.data.results || []
        if (list) {
          list = list.map(item => {
            var memberPermissionsName = item.member_permissions_name && Array.isArray(item.member_permissions_name) ? item.member_permissions_name.join(" ") : ''
            var days = item.days ? '（' + item.days + '天）' : '0(天)'
            item.name = memberPermissionsName + days
            return item
          })
        }
        this.distributeList = deepClone(list)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 有效期天数选择
    handlerChooseDate(index, days) {
      this.chooseCurrentIndex = index
      this.$set(this.dialogForm, 'validityDate', days)
    },
    // 查询手机号
    async getPhoneList(value) {
      this.phoneLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMemberMemberUserListPost({
        phone: value,
        page: 1,
        page_size: 99999
      }))
      this.phoneLoading = false
      if (err) {
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        var list = data.results || []
        if (list) {
          this.phoneList = deepClone(list)
        }
        console.log("this.phoneList", this.phoneList);
      }
    },
    // 选择号码监听
    phoneChooseChange(value) {
      console.log("phoneChooseChange", value);
      var findItem = this.phoneList.find(item => {
        return item.id === value
      })
      if (findItem) {
        this.$set(this.dialogForm, 'name', findItem.nickname)
        this.$set(this.dialogForm, 'id', findItem.id)
      }
    },
    // 关闭弹窗
    handlerClose() {
      this.chooseCurrentIndex = -1
      if (this.$refs.memberFormRef) {
        this.$refs.memberFormRef.resetFields()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.day-item {
  width: 70px;
  height: 40px;
  border: 1px solid #e4e4e4;
  line-height: 40px;
  text-align: center;
  font-size: 16px;
  margin-right: 20px;
  border-radius: 5px;
}

.active {
  color: #fff;
  background: #ffa558;
}

.no-active {
  color: black;
  background: #ffffff;
}
</style>
