<template>
  <!-- 新增/编辑  改不动了有问题找AddCommodity以前做的人-->
  <custom-drawer :show="visible" :title="type === 'add' ? '新增菜品/商品' : '编辑菜品/商品'" size="60%" :showClose="false"
    @cancel="closeHandler" @confirm="submitHandler" :loading="isLoading" :confirmShow="true" :cancelShow="true"
    :wrapperClosable="false" confirm-text="保存" cancel-text="取消" v-bind="$attrs" v-on="$listeners">
    <div class="super-add-commodity-dialog container-wrapper">
      <el-form v-loading="isLoading" :rules="formRuls" :model="formData" ref="foodRef" class="" size="small"
        label-position="right">
        <div>
          <div style="max-width: 50%; padding: 0 20px">
            <el-form-item label="菜品/商品名称" label-width="120px" prop="name" class="form-content-flex">
              <el-input v-model="formData.name" placeholder="请输入菜品/商品名称" class="ps-input" style="width: 80%"></el-input>
              <!-- <el-tooltip effect="dark" content="增加菜品别名" placement="top">
                <img class="add-btn-img" @click="addFoodAliasName" src="@/assets/img/plus.png" alt="">
              </el-tooltip> -->
            </el-form-item>
            <div v-if="formData.aliasName.length">
              <el-form-item label="菜品别名" class="" label-width="120px">
                <el-form-item :class="[index > 0 ? 'm-t-10' : '', 'food-alias-name-form']"
                  v-for="(item, index) in formData.aliasName" :key="index" :rules="formRuls.aliasName"
                  :prop="`aliasName[${index}]`">
                  <el-input style="width: 80%" maxlength="20" v-model="formData.aliasName[index]" placeholder="请输入菜品别名"
                    class="ps-input"></el-input>
                  <img src="@/assets/img/plus.png" @click="addFoodAliasName" alt="">
                  <img src="@/assets/img/reduce_red.png" @click="delFoodAliasName(index)" alt="" v-if="index > 0">
                </el-form-item>
              </el-form-item>
            </div>
            <el-form-item label="属性" prop="attributes" class="" label-width="120px">
              <el-radio-group v-model="formData.attributes" class="ps-radio">
                <el-radio label="foods">菜品</el-radio>
                <el-radio label="goods">商品</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="图片" class="upload- upload-hidden" label-width="120px">
              <div class="ps-red">仅支持jpg、png、bmp格式，大小不超过5M</div>
              <div class="inline-block upload-w">
                <el-upload :class="{ 'file-upload': true, 'hide-upload': formData.foodImagesList.length > 0 }"
                  ref="fileUpload" drag :action="serverUrl" :data="uploadParams" :file-list="formData.foodImagesList"
                  :on-success="handleFoodImgSuccess" :on-change="handelChange" :before-upload="beforeFoodImgUpload"
                  :limit="1" list-type="picture-card" :multiple="false" :headers="headersOpts"
                  accept=".jpeg,.jpg,.png,.bmp">
                  <div v-if="fileLists.length < 1" class="upload-t">
                    <i class="el-icon-circle-plus"></i>
                    <div class="el-upload__text">
                      上传菜品/商品图片
                    </div>
                  </div>
                  <div slot="file" slot-scope="{file}" v-loading="file.status === 'uploading'"
                    element-loading-text="上传中">
                    <div class="upload-food-img">
                      <el-image class="el-upload-dragger" :src="file.url" fit="cover">
                      </el-image>
                    </div>
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span class="el-upload-list__item-delete" @click="handleImgRemove(file, 'foodImages', index)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="识别图片" class="upload-" label-width="120px">
              <div class="ps-red">仅支持jpg、png、bmp格式，大小不超过5M</div>
              <div class="inline-block upload-w">
                <el-upload class="file-upload" ref="fileUpload" v-loading="uploadingExtra" element-loading-text="上传中"
                  drag :action="serverUrl" :data="uploadParams" :file-list="formData.extraImagesList"
                  :on-success="handleExtraImgSuccess" :on-change="handelChange" :before-upload="beforeExtraImgUpload"
                  :limit="25" :multiple="true" :show-file-list="false" :headers="headersOpts"
                  accept=".jpeg,.jpg,.png,.bmp">
                  <div class="upload-t">
                    <i class="el-icon-circle-plus"></i>
                    <div class="el-upload__text">
                      上传识别图片
                    </div>
                  </div>
                </el-upload>
              </div>
              <!-- <div class="inline-block upload-tips">
              上传：识别图片。最多25张<br />
              建议图片需清晰，图片内容与名称相符。<br />
              仅支持jpg、png、bmp格式，大小不超过5M
            </div> -->
              <div v-show="showFoodImg" @click="showFoodImg = false" style="cursor: pointer;">
                查看已上传的图片（{{ formData.extraImages.length }}张）
                <i class="el-icon-arrow-up"></i>
              </div>
              <div v-show="!showFoodImg" @click="showFoodImg = true" style="cursor: pointer;">
                查看已上传的图片（{{ formData.extraImages.length }}张）
                <i class="el-icon-arrow-down"></i>
              </div>
              <div v-show="showFoodImg" class="food-img-wrap">
                <div class="food-img-item" v-for="(item, index) in formData.extraImages" :key="index">
                  <el-image :src="item" alt=""  fit="cover"/>
                  <div class="food-img-mask">
                    <i @click="perviewFoodImg(item)" class="el-icon-zoom-in"></i>
                    <i @click="handleImgRemoveExtra({ url: item }, 'extraImages', index)" class="el-icon-delete"></i>
                  </div>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="分类" prop="categoryId" label-width="120px">
              <el-cascader v-if="visible" v-model="formData.categoryId" :options="foodCategoryList" :props="{
                multiple: false,
                checkStrictly: false,
                value: 'id',
                label: 'name',
                children: 'children',
                emitPath: false
              }"></el-cascader>
            </el-form-item>
            <el-form-item label="烹饪方式" prop="cookingType" label-width="120px" v-if="formData.attributes === 'foods'">
              <div class="ps-flex">
                <div v-for="(item, index) in cookingTypeList" :key="item.value"
                  :class="['cooking-type-item', formData.cookingType === item.value ? 'active' : '', index > 0 ? 'm-l-10' : '']"
                  @click="handleCookingTypeChange(item)">{{ item.label }}</div>
              </div>
            </el-form-item>
            <!-- <el-form-item label="口味" prop="taste" class="">
              <el-input
                class="input-new-tag"
                v-if="inputVisible"
                v-model="inputValue"
                ref="saveTagInput"
                size="small"
                style="margin-right: 10px;"
                @keyup.enter.native="inputTasteConfirm"
                @blur="inputTasteConfirm"
              ></el-input>
              <el-button v-else class="ps-btn button-new-tag" type="primary" icon="el-icon-plus" @click="showTasteInput">添加</el-button>
              <el-tag
                :key="tag"
                v-for="tag in formData.tasteList"
                closable
                :disable-transitions="false"
                @close="closeTasteHandle(tag)"
                color="#fff"
              >
                {{ tag }}
              </el-tag>
            </el-form-item> -->
            <el-form-item label="标签" prop="" class="" label-width="120px">
              <el-cascader v-if="visible" v-model="formData.selectLabelIdList" :options="labelList" :props="{
                multiple: true,
                checkStrictly: false,
                value: 'id',
                label: 'name',
                children: 'label_list',
                emitPath: false
              }" placeholder="请选择标签" clearable :collapse-tags="true" :show-all-levels="false"
                @change="handleLabelChange" />
              <div class="selected-tags" v-if="selectedTags.length">
                <el-tag v-for="tag in selectedTags" :key="tag.id" closable class="tag-item"
                  @close="handleRemoveTag(tag)">
                  {{ tag.name }}
                </el-tag>
              </div>
            </el-form-item>
            <!--<el-form-item :label="`${groupKey}:`" prop="" class="" label-width="120px"
              v-for="(labelGroupItem, groupKey, labelGroupIndex) in formData.labelGroupInfoList" :key="labelGroupIndex">
              <el-tag class="m-r-5 collapse-data" v-for="(item, index) in labelGroupItem" :key="index" size="medium"
                effect="plain" type="info" color="#fff" closable @close="closeTag(groupKey, index, item)">
                {{ item.name }}
              </el-tag>
            </el-form-item>-->
            <el-form-item label="食材占比" prop="" class="" label-width="120px">
              <div class="table-title ps-flex flex-between">
                <span class="ps-red">（菜品每100g所含食材占比，相加必须等于100%）</span>
                <el-button class="" style="" type="text" @click="addIngredients">添加</el-button>
              </div>
              <div :class="['m-t-10 w-830', errorMsg.percentageError ? 'error-border' : '']">
                <el-table v-loading="isLoading" :data="formData.ingredientList" ref="tableData" style="width: 830px" border
                  header-row-class-name="ps-table-header-row">
                  <el-table-column prop="no" label="食材" align="center">
                    <template slot-scope="scope">
                      <div>
                        <virtual-select
                          :key="`select-${scope.$index}-${scope.row.selectId}`"
                          :ref="'virtualSelect'+ scope.$index"
                          v-model="scope.row.selectId"
                          :width="290"
                          :popover-width="290"
                          placeholder="请下拉选择"
                          class="ps-select"
                          filterable
                          :data-list="ingredientList"
                          @change="changeIngredient"
                          :option="{
                            label: 'name',
                            value: 'id'}">
                        </virtual-select>
                      </div>
                        <!--
                          <el-select v-model="scope.row.selectId"
                        placeholder="请下拉选择"
                        :popper-append-to-body="false"
                        popper-class="category-select-dropdown"
                        collapse-tags
                        clearable
                        filterable
                        @change="changeIngredient">
                        <el-option v-for="(ingredientItem,ingredientItemIndex) in ingredientList"
                          :key="ingredientItemIndex"
                          :label="ingredientItem.name"
                          :value="ingredientItem.id"
                          :disabled="ingredientItem.disabled && ingredientItem.id !== scope.row.selectId">
                        </el-option>
                      </el-select>
                      -->
                    </template>
                  </el-table-column>
                  <el-table-column prop="id" label="占比" align="center">
                    <template slot-scope="scope">
                      <!-- <el-progress
                    :percentage="50"
                    color="#fd953c"
                    class="cantent"
                  ></el-progress> -->
                      <div class="">
                        <el-input-number v-model="scope.row.percentage" @change="changePercentage" :min="0" :max="100"
                          label="请输入"></el-input-number>
                        <!-- <div style="width: 60px;">{{scope.row.percentage}}%</div> -->
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="xx" label="操作" align="center" width="180px">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" class="ps-warn-text"
                        @click="deleteIngredientHandle(scope.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div style="color: red;padding: 20px;" v-if="errorMsg.percentageError">{{ errorMsg.percentageError }}</div>
            </el-form-item>
            <el-form-item label="营养信息" prop="" class="" label-width="120px">
              <div class="ps-flex flex-wrap nutrition-wrapper">
                <div class="nutrition-item" :key="nutrition.key" v-for="(nutrition) in currentNutritionList">
                  <div class="nutrition-label">{{ nutrition.name + '（' + nutrition.unit + '）' + '：' }}</div>
                  <div class="nutrition-value">{{ formData[nutrition.key] }}</div>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <select-laber v-if="selectLaberDialogVisible" :isshow.sync="selectLaberDialogVisible" width="600px"
      @selectLaberData="selectLaberData" :ruleSingleInfo="ruleSingleInfo" />
  </custom-drawer>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone, replaceSingleQuote, getToken, getSuffix } from '@/utils'
import { NUTRITION_LIST, COOKING_TYPE_LIST } from '../constants'
import NP from 'number-precision'
// import { dtConfirm } from '@/utils/decorator'
import selectLaber from '../../components/selectLaber.vue'
import VirtualSelect from "@/components/VirtualSelect/index.vue";
export default {
  name: 'SuperAddCommodity',
  // mixins: [activatedLoadData, exportExcel],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'add'
    },
    editData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    let validataNutrition = (rule, value, callback) => {
      if (value) {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('营养数据有误，仅支持保留两位小数'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    let validatorImage = (rule, value, callback) => {
      if (!this.formData.imageList.length) {
        return callback(new Error('请上传菜品图片'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false, // 刷新数据
      formData: {
        name: '', // 菜品名
        aliasName: [], // 菜品别名
        attributes: 'foods',
        tasteList: [],
        foodImages: [],
        foodImagesList: [],
        extraImages: [],
        extraImagesList: [],
        ingredientList: [], // 食材组成
        food_id: '', // 重复的id
        categoryId: '',
        selectLabelListData: [], // 标签列表
        selectLabelIdList: [], // 标签id列表
        labelGroupInfoList: {}, // 带标签组名字的数据
        cookingType: ''
      },
      fileLists: [],
      serverUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      formRuls: {
        name: [{ required: true, message: '食材名称不能为空', trigger: ['blur', 'change'] }],
        aliasName: [{ required: false, message: '请输入菜品别名', trigger: ['blur', 'change'] }],
        attributes: [{ required: true, message: '请选择属性', trigger: ['blur', 'change'] }],
        nutrition: [{ validator: validataNutrition, trigger: 'change' }],
        imageList: [{ required: true, validator: validatorImage, trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择分类', trigger: ['blur', 'change'] }],
        cookingType: [{ required: true, message: '请选择烹饪方式', trigger: ['blur', 'change'] }]
      },
      nutritionList: NUTRITION_LIST,
      inputVisible: false,
      inputValue: '',
      limit: 25,
      actionUrl: '',
      uploadParams: {
        prefix: 'super_food_img'
      },
      uploadUrl: '',
      tableData: [],
      ingredientList: [], // 食材列表
      allSelectIngredient: [], // 所有选中的食材的集合
      errorMsg: { // 独立的form表单错误提示
        percentageError: ''
      },
      selectLaberDialogVisible: false,
      // 点击当前添加标签每条的数据
      ruleSingleInfo: {},
      dialogImageUrl: '',
      dialogVisible: false,
      showFoodImg: true,
      foodCategoryList: [],
      showAll: false,
      uploading: false,
      uploadingExtra: false,
      cookingTypeList: deepClone(COOKING_TYPE_LIST),
      labelList: [],
      selectedTags: [],
      ingredientCloneList: [], // 食材列表的副本
      loading: false
    }
  },
  computed: {
    currentNutritionList: function () {
      return this.nutritionList
    }
  },
  components: { selectLaber, VirtualSelect },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.initLoad()
        }
      }
    }
  },
  mounted() { },
  methods: {
    async initLoad() {
      this.isLoading = true
      await this.getIngredientslist()
      await this.getFoodLabelList()
      await this.foodFoodCategoryList()
      if (this.type === 'add') {
        this.formData.aliasName.push('')
        this.initIngredient()
        this.setNutritonData({})
      } else {
        let data = deepClone(this.editData)
        console.log(data)
        this.formData.id = data.id
        this.formData.aliasName = data.alias_name && data.alias_name.length > 0 ? data.alias_name : ['']
        this.formData.name = data.name
        this.formData.attributes = data.attributes
        this.formData.categoryId = data.category
        this.$set(this.formData, 'cookingType', data.cooking_manner)
        this.formData.selectLabelIdList = data.label ? data.label.map(v => { return v.id }) : []
        this.selectedTags = data.label ? data.label : []
        // if (data.taste_list) {
        //   this.formData.tasteList = data.taste_list.map(v => {
        //     return v.name
        //   })
        // }
        //  回显示标签组名字 {'aa':[{xx:xx}]}
        // if (data.label.length) {
        //   // 格式化标签
        //   this.initLabelGroup(data.label)
        // }
        this.formData.selectLabelListData = data.label
        // this.formData.selectLabelIdList = data.label.map(v => { return v.id })
        // 展示图片
        if (data.image) {
          this.formData.foodImages = [data.image]
          this.formData.foodImagesList = [
            {
              url: data.image,
              name: data.image,
              status: "success",
              uid: data.image
            }
          ]
        }
        // 识别图片
        if (data.extra_image) {
          data.extra_image.forEach(item => {
            this.formData.extraImagesList.push({
              url: item,
              name: item,
              status: "success",
              uid: item
            })
          })
          this.formData.extraImages = data.extra_image
        }

        this.initIngredient(data)
        this.setNutritonData(data)
        this.isDisabledOtherIngredients()
      }
      this.isLoading = false
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
    }, 300),
    // 设置营养的数据
    setNutritonData(row) {
      if (!row.nutrition) row.nutrition = {}
      let element = row.nutrition.element ? JSON.parse(replaceSingleQuote(row.nutrition.element)) : {}
      let vitamin = row.nutrition.vitamin ? JSON.parse(replaceSingleQuote(row.nutrition.vitamin)) : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(this.formData, nutrition.key, row.nutrition[nutrition.key] ? row.nutrition[nutrition.key] : 0)
        }
        if (nutrition.type === 'element') {
          this.$set(this.formData, nutrition.key, element[nutrition.key] ? element[nutrition.key] : 0)
        }
        if (nutrition.type === 'vitamin') {
          this.$set(this.formData, nutrition.key, vitamin[nutrition.key] ? vitamin[nutrition.key] : 0)
        }
      })
    },
    // 获取系统食材列表
    async getIngredientslist() {
      const [err, res] = await to(this.$apis.apiBackgroundAdminIngredientIngredientNamePost({
        page: 1,
        page_size: 999999
      }))

      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.ingredientList = res.data || []
        this.ingredientCloneList = deepClone(res.data || [])
        // this.initIngredient()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化下食材占比
    initIngredient(data) {
      this.formData.ingredientList = []
      if (this.type === 'add') {
        this.formData.ingredientList.push({
          index: this.formData.ingredientList.length,
          selectId: '',
          percentage: 0
        })
      } else if (data) {
        this.formData.ingredientList = data.ingredients_list.map((v, index) => {
          v.index = index
          v.selectId = Number(v.ingredient_id)
          v.percentage = v.ingredient_scale
          this.ingredientList.map(item => {
            if (item.id === v.selectId) {
              v.nutrition = item.nutrition_info
            }
          })
          return v
        })
        this.isDisabledOtherIngredients()
      }
    },
    // 格式化下参数
    formatParams() {
      console.log("this.formData", this.formData, this.formData.aliasName.toString())
      let params = {
        name: this.formData.name,
        attributes: this.formData.attributes,
        // taste_list: this.formData.tasteList,
        // formData.imageList的第一张图片传给image，剩余的传给extra_image，因为图片列表是分两个字段上传的，为了兼容原来只有一张图片的情况
        image: this.formData.foodImages[0], // 菜品主图
        extra_image: this.formData.extraImages, // 菜品其他图片
        label_list: this.formData.selectLabelIdList,
        ingredient_list: [],
        nutrition_info: {},
        category_id: this.formData.categoryId,
        cooking_manner: this.formData.cookingType
      }
      if (this.formData.aliasName && this.formData.aliasName.toString() !== '') {
        params.alias_name = this.formData.aliasName.filter(name => name.trim())
      } else {
        delete params.alias_name
      }
      if (this.type === 'modify') {
        params.id = this.formData.id
      }
      if (this.formData.food_id && this.type === 'add') {
        params.food_id = this.formData.food_id
      }
      this.formData.ingredientList.map(v => {
        if (v.selectId) {
          let obj = {
            ingredient_id: v.selectId,
            ingredient_scale: v.percentage
          }
          params.ingredient_list.push(obj)
        }
      })
      // 营养
      let element = {}
      let vitamin = {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          params.nutrition_info[nutrition.key] = this.formData[nutrition.key]
        }
        if (nutrition.type === 'element') {
          element[nutrition.key] = this.formData[nutrition.key]
        }
        if (nutrition.type === 'vitamin') {
          vitamin[nutrition.key] = this.formData[nutrition.key]
        }
      })
      params.nutrition_info.element = JSON.stringify(element)
      params.nutrition_info.vitamin = JSON.stringify(vitamin)
      return params
    },
    // 添加食材
    addIngredients() {
      this.formData.ingredientList.push({
        selectId: '',
        percentage: 0
      })
      this.isDisabledOtherIngredients()
    },
    // 删除食材
    deleteIngredientHandle(index) {
      this.formData.ingredientList.splice(index, 1)
      // 强制更新数组，触发虚拟列表重新渲染
      this.$nextTick(() => {
        this.formData.ingredientList = [...this.formData.ingredientList]
      })
      this.isDisabledOtherIngredients()
      this.computedNutritionAndPercentage()
      this.changePercentage()
    },
    // 食材选择修改
    changeIngredient(val) {
      console.log("val", val)
      let obj = deepClone(val)
      let index = this.formData.ingredientList.findIndex(item => item.selectId === val.id)
      this.formData.ingredientList[index].nutrition = obj.nutrition_info
      this.errorMsg.percentageError = ''
      this.isDisabledOtherIngredients()
      this.computedNutritionAndPercentage()
    },
    // 设置食材disabled
    isDisabledOtherIngredients() {
      this.allSelectIngredient = this.formData.ingredientList.filter(item => item.selectId)
      const selectedIds = new Set(this.allSelectIngredient.map(item => { return item.selectId }))
      console.log("isDisabledOtherIngredients", selectedIds);
      this.ingredientList.forEach(item => {
        item.disabled = selectedIds.has(item.id)
      })
    },
    // 计算营养和食材占比
    computedNutritionAndPercentage() {
      let objNutrition = {}
      NUTRITION_LIST.forEach(v => {
        objNutrition[v.key] = 0
      })
      let percentageCount = 0
      this.formData.ingredientList.forEach((v, index) => {
        if (v.selectId) {
          // 计算食材占比 按100克计算
          if (index < this.allSelectIngredient.length - 1) {
            v.percentage = parseInt(NP.divide(100, this.allSelectIngredient.length))
            percentageCount = NP.plus(v.percentage, percentageCount)
          } else {
            v.percentage = parseInt(NP.minus(100, percentageCount))
          }
          const percentage = v.percentage / 100
          if (!v.nutrition) {
            v.nutrition = {}
          }
          // objNutrition.energy_mj = +v.nutrition.energy_mj ? NP.plus(objNutrition.energy_mj, v.nutrition.energy_mj * percentage) : objNutrition.energy_mj ? objNutrition.energy_mj : 0
          objNutrition.energy_kcal = +v.nutrition.energy_kcal ? NP.plus(objNutrition.energy_kcal, v.nutrition.energy_kcal * percentage) : objNutrition.energy_kcal ? objNutrition.energy_kcal : 0
          objNutrition.protein = +v.nutrition.protein ? NP.plus(objNutrition.protein, v.nutrition.protein * percentage) : objNutrition.protein ? objNutrition.protein : 0
          objNutrition.axunge = +v.nutrition.axunge ? NP.plus(objNutrition.axunge, v.nutrition.axunge * percentage) : objNutrition.axunge ? objNutrition.axunge : 0
          objNutrition.carbohydrate = +v.nutrition.carbohydrate ? NP.plus(objNutrition.carbohydrate, v.nutrition.carbohydrate * percentage) : objNutrition.carbohydrate ? objNutrition.carbohydrate : 0
          objNutrition.cholesterol = +v.nutrition.cholesterol ? NP.plus(objNutrition.cholesterol, v.nutrition.cholesterol * percentage) : objNutrition.cholesterol ? objNutrition.cholesterol : 0
          objNutrition.dietary_fiber = +v.nutrition.dietary_fiber ? NP.plus(objNutrition.dietary_fiber, v.nutrition.dietary_fiber * percentage) : objNutrition.dietary_fiber ? objNutrition.dietary_fiber : 0
          if (v.nutrition.element && v.nutrition.vitamin) {
            try { // 防止JSON.parse出错
              let element = JSON.parse(replaceSingleQuote(v.nutrition.element))
              let vitamin = JSON.parse(replaceSingleQuote(v.nutrition.vitamin))
              // 找到对应的营养渲染 和累加进去
              for (const key in element) {
                objNutrition[key] = NP.plus(objNutrition[key], +element[key] ? element[key] * percentage : 0)
              }
              for (const key in vitamin) {
                objNutrition[key] = NP.plus(objNutrition[key], +vitamin[key] ? vitamin[key] * percentage : 0)
              }
            } catch (error) {
              // console.log('error', error)
            }
          }
          if (this.deepFormIngredients && this.deepFormIngredients.length) {
            this.deepFormIngredients.forEach(item => {
              if (item.id === v.id) {
                v.status = true
              }
            })
          }
        }
      })
      this.nutritionList.forEach(item => {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(objNutrition[item.key])) {
          this.$set(this.formData, item.key, objNutrition[item.key].toFixed(2))
        } else {
          this.$set(this.formData, item.key, objNutrition[item.key])
        }
      })
    },
    // 计算营养和食材占比
    setNutritionAndPercentage() {
      let objNutrition = {}
      NUTRITION_LIST.forEach(v => {
        objNutrition[v.key] = 0
      })
      this.formData.ingredientList.map((v, index) => {
        if (v.selectId) {
          // 计算食材占比 按100克计算
          if (!v.nutrition) {
            v.nutrition = {}
          }
          const percentage = v.percentage / 100
          // objNutrition.energy_mj = +v.nutrition.energy_mj ? NP.plus(objNutrition.energy_mj, v.nutrition.energy_mj * percentage) : objNutrition.energy_mj ? objNutrition.energy_mj : 0
          objNutrition.energy_kcal = +v.nutrition.energy_kcal ? NP.plus(objNutrition.energy_kcal, v.nutrition.energy_kcal * percentage) : objNutrition.energy_kcal ? objNutrition.energy_kcal : 0
          objNutrition.protein = +v.nutrition.protein ? NP.plus(objNutrition.protein, v.nutrition.protein * percentage) : objNutrition.protein ? objNutrition.protein : 0
          objNutrition.axunge = +v.nutrition.axunge ? NP.plus(objNutrition.axunge, v.nutrition.axunge * percentage) : objNutrition.axunge ? objNutrition.axunge : 0
          objNutrition.carbohydrate = +v.nutrition.carbohydrate ? NP.plus(objNutrition.carbohydrate, v.nutrition.carbohydrate * percentage) : objNutrition.carbohydrate ? objNutrition.carbohydrate : 0
          objNutrition.cholesterol = +v.nutrition.cholesterol ? NP.plus(objNutrition.cholesterol, v.nutrition.cholesterol * percentage) : objNutrition.cholesterol ? objNutrition.cholesterol : 0
          objNutrition.dietary_fiber = +v.nutrition.dietary_fiber ? NP.plus(objNutrition.dietary_fiber, v.nutrition.dietary_fiber * percentage) : objNutrition.dietary_fiber ? objNutrition.dietary_fiber : 0
          if (v.nutrition.element && v.nutrition.vitamin) {
            try { // 防止JSON.parse出错
              let element = JSON.parse(replaceSingleQuote(v.nutrition.element))
              let vitamin = JSON.parse(replaceSingleQuote(v.nutrition.vitamin))
              // 找到对应的营养渲染 和累加进去
              for (const key in element) {
                objNutrition[key] = NP.plus(objNutrition[key], +element[key] ? element[key] * percentage : 0)
              }
              for (const key in vitamin) {
                objNutrition[key] = NP.plus(objNutrition[key], +vitamin[key] ? vitamin[key] * percentage : 0)
              }
            } catch (error) {
              // console.log('error', error)
            }
          }
        }
      })

      this.nutritionList.forEach(item => {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(objNutrition[item.key])) {
          this.$set(this.formData, item.key, objNutrition[item.key].toFixed(2))
        } else {
          this.$set(this.formData, item.key, objNutrition[item.key])
        }
      })
    },
    // 食材占比
    changePercentage(e) {
      this.setNutritionAndPercentage()
      let countList = this.formData.ingredientList && this.formData.ingredientList.filter(item => {
        return item.percentage !== 0
      })
      if (countList.length === 0) {
        this.errorMsg.percentageError = ''
        return
      }
      let percentageCount = this.formData.ingredientList.reduce((total, current) => {
        return NP.plus(current.percentage, total)
      }, 0)
      console.log('percentageCount', percentageCount)
      if (percentageCount > 100 || percentageCount < 100) {
        this.$set(this.errorMsg, 'percentageError', '菜品每100g所含食材占比，相加必须等于100%')
      } else {
        this.$set(this.errorMsg, 'percentageError', '')
      }
      if (!this.formData.ingredientList.length) {
        this.errorMsg.percentageError = ''
      }
      console.log('this.formData.ingredientList', this.errorMsg, this.formData.ingredientList)
    },
    // 口味
    closeTasteHandle(tag) {
      this.formData.tasteList.splice(this.formData.tasteList.indexOf(tag), 1)
    },
    // 口味
    showTasteInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    // 口味
    inputTasteConfirm() {
      let inputValue = this.inputValue
      if (inputValue) {
        this.formData.tasteList.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },
    labelClick() {
      this.ruleSingleInfo = {
        labelType: 'food',
        selectLabelIdList: this.formData.selectLabelIdList,
        selectLabelListData: this.formData.selectLabelListData
      }
      this.selectLaberDialogVisible = true
    },
    // 删除标签
    closeTag(key, index, item) {
      // 删除
      let idx = this.formData.selectLabelIdList.indexOf(item.id)
      let ids = this.formData.selectLabelListData.indexOf(item)
      this.formData.selectLabelIdList.splice(idx, 1)
      this.formData.selectLabelListData.splice(ids, 1)
      // 重置数据
      this.formData.labelGroupInfoList = {}
      this.initLabelGroup(this.formData.selectLabelListData)
    },
    // 选择标签
    selectLaberData(params) {
      this.formData.selectLabelIdList = params.selectLabelIdList
      this.formData.selectLabelListData = params.selectLabelListData
      this.formData.labelGroupInfoList = {}
      this.initLabelGroup(this.formData.selectLabelListData)
    },
    initLabelGroup(data) {
      data.forEach(v => {
        if (!this.formData.labelGroupInfoList[v.label_group_name]) {
          this.formData.labelGroupInfoList[v.label_group_name] = []
        }
        if (this.formData.labelGroupInfoList[v.label_group_name] && !this.formData.labelGroupInfoList[v.label_group_name].includes(v)) {
          this.formData.labelGroupInfoList[v.label_group_name].push(v)
        }
      })
    },
    handelChange(file, fileList) {
      this.uploadParams.key =
        this.uploadParams.prefix + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    },
    // 预览图片
    perviewFoodImg(item) {
      this.dialogImageUrl = item;
      this.dialogVisible = true;
    },
    handleFoodImgSuccess(res, file, fileList) {
      this.uploading = false
      if (res.code === 0) {
        this.formData.foodImagesList = fileList
        this.formData.foodImages.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    handleExtraImgSuccess(res, file, fileList) {
      this.uploadingExtra = false
      if (res.code === 0) {
        this.formData.extraImagesList = fileList
        this.formData.extraImages.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    handleImgRemoveExtra(file, type, index) {
      console.log("handleImgRemoveExtra", file, type, index)
      this.formData.extraImages.splice(index, 1)
      this.formData.extraImagesList.splice(index, 1)
    },
    handleImgRemove(file, type, index) {
      console.log("handleImgRemove", file, type, index)
      this.formData.foodImages.splice(index, 1)
      this.formData.foodImagesList.splice(index, 1)
    },
    beforeFoodImgUpload(file) {
      return this.beforeImgUpload(file, 'uploading')
    },
    beforeExtraImgUpload(file) {
      return this.beforeImgUpload(file, 'uploadingExtra')
    },
    beforeImgUpload(file, type) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 < 5
      console.log(getSuffix(file.name))
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      if (type) this[type] = true
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 提交数据
    submitHandler() {
      this.$refs.foodRef.validate(valid => {
        console.log("submitHandler", valid)
        if (valid && !this.errorMsg.percentageError) {
          if (this.checkIngredientList(this.formData.ingredientList)) {
            return
          }
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          if (this.type === 'modify') {
            this.modifyFoodList()
          } else {
            this.$confirm('是否确定创建该菜品？', '提示', {
              confirmButtonText: '确 定',
              cancelButtonText: this.$t('dialog.cancel_btn'),
              closeOnClickModal: false,
              customClass: 'ps-confirm',
              cancelButtonClass: 'ps-cancel-btn',
              confirmButtonClass: 'ps-btn',
              center: true,
              beforeClose: async (action, instance, done) => {
                if (action === 'confirm') {
                  instance.confirmButtonLoading = true
                  this.addFoodList()
                  instance.confirmButtonLoading = false
                  done()
                } else {
                  if (!instance.confirmButtonLoading) {
                    done()
                  }
                }
              }
            })
              .then(e => { })
              .catch(e => {

              })
          }
        } else {
          console.log('error validate')
          this.$message.error('请认真检查数据格式！')
        }
      })
    },
    // 添加
    async addFoodList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminFoodAddPost(this.formatParams()))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // mtj 用在饮食推荐管理中的菜品列表
        this.getGoodsList()
        // end
        this.$message.success(res.msg)
        setTimeout(() => {
          this.resetData()
          if (this.$refs.foodRef) {
            this.$refs.foodRef.clearValidate()
          }
          this.$emit('submit')
        }, 100);
      } else if (res.code === 2) {
        this.formData.food_id = res.data.food_id
        this.$confirm(res.msg, '提示', {
          confirmButtonText: '覆 盖',
          cancelButtonText: this.$t('dialog.cancel_btn'),
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              await this.addFoodList()
              instance.confirmButtonLoading = false
              done()
            } else {
              this.formData.food_id = ""
              this.type = 'add'
              if (!instance.confirmButtonLoading) {
                done()
              }
            }
          }
        })
          .then(e => { })
          .catch(e => {
            this.formData.food_id = ''
          })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改
    async modifyFoodList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminFoodModifyPost(this.formatParams()))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // mtj 用在饮食推荐管理中的菜品列表
        this.getGoodsList()
        // end
        this.$message.success(res.msg)
        setTimeout(() => {
          this.resetData()
          if (this.$refs.foodRef) {
            this.$refs.foodRef.clearValidate()
          }
          this.$emit('submit')
        }, 100);
      } else {
        this.$message.error(res.msg)
      }
    },
    // 关闭当前页面
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = false
            setTimeout(() => {
              this.resetData()
              if (this.$refs.foodRef) {
                this.$refs.foodRef.clearValidate()
              }
              this.$emit('close')
            }, 100);
            done()
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => { })
    },
    // 添加菜品别名
    addFoodAliasName() {
      this.formData.aliasName.push('')
    },
    delFoodAliasName(index) {
      this.formData.aliasName.splice(index, 1);
    },
    // 二级列表
    async foodFoodCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodCategoryAllListPost({
          page: 1,
          page_size: 999999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodCategoryList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 烹饪方式选择
    handleCookingTypeChange(item) {
      this.$set(this.formData, 'cookingType', item.value)
      // 手动触发校验
      this.$nextTick(() => {
        this.$refs.foodRef && this.$refs.foodRef.validateField('cookingType')
      })
    },
    // 获取商品列表
    getGoodsList() {
      return new Promise((resolve, reject) => {
        this.$apis
          .apiBackgroundAdminFoodListPost({
            page: 1,
            page_size: 999999
          })
          .then(res => {
            sessionStorage.setItem(
              'allFoodList',
              res.data.results ? JSON.stringify(res.data.results) : '[]'
            )
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    // 重置数据
    resetData() {
      this.formData = {
        name: '', // 菜品名
        aliasName: [], // 菜品别名
        attributes: 'foods',
        tasteList: [],
        foodImages: [],
        foodImagesList: [],
        extraImages: [],
        extraImagesList: [],
        ingredientList: [], // 食材组成
        food_id: '', // 重复的id
        categoryId: '',
        selectLabelListData: [], // 标签列表
        selectLabelIdList: [], // 标签id列表
        labelGroupInfoList: {} // 带标签组名字的数据
      }
      this.tableData = []
      this.ingredientList = []
      this.allSelectIngredient = []
      this.errorMsg = {}
      this.isLoading = false
      this.type = 'add'
      this.selectedTags = []
    },
    // 获取标签列表
    async getFoodLabelList() {
      const [err, res] = await to(this.$apis.apiBackgroundHealthyAdminLabelGroupListPost({
        page: 1,
        page_size: 999999,
        type: "food"
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        let results = data.results || []
        this.labelList = results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理标签选择变化
    handleLabelChange(values) {
      // 清空已选标签
      this.selectedTags = []

      // 递归查找二级标签
      const findSecondLevelLabel = (labelId) => {
        for (const group of this.labelList) {
          if (group.label_list && group.label_list.length > 0) {
            const found = group.label_list.find(label => label.id === labelId)
            if (found) {
              return {
                id: found.id,
                name: found.name,
                parentId: group.id,
                parentName: group.name
              }
            }
          }
        }
        return null
      }

      // 处理选中的值
      values.forEach(labelId => {
        const foundLabel = findSecondLevelLabel(labelId)
        if (foundLabel && !this.selectedTags.some(tag => tag.id === foundLabel.id)) {
          this.selectedTags.push(foundLabel)
        }
      })

      // 更新表单数据
      this.formData.selectLabelListData = this.selectedTags
      this.formData.selectLabelIdList = this.selectedTags.map(tag => tag.id)
    },
    // 移除标签
    handleRemoveTag(tag) {
      this.selectedTags = this.selectedTags.filter(item => item.id !== tag.id)
      this.formData.selectLabelIdList = this.formData.selectLabelIdList.filter(value => value !== tag.id)
      this.formData.selectLabelListData = this.selectedTags
    },
    // 检测食材列表
    checkIngredientList(list) {
      if (!list || list.length === 0) {
        return false
      }
      let flag = false
      for (let i = 0; i < list.length; i++) {
        if (list[i].percentage !== 0 && !list[i].selectId) {
          flag = true
          this.$message.error('食材占比，请选择食材！')
          break
        }
      }
      return flag
    }
  }
}
</script>

<style lang="scss">
.super-add-commodity-dialog {

  .form-content-flex {
    .el-form-item__content {
      display: flex;
    }
  }

  .add-btn-img {
    width: 25px;
    height: 25px;
    margin: 3px 0 0 10px;
  }

  .food-alias-name-form {
    margin-bottom: 0px !important;

    .el-form-item__content {
      display: flex;
      align-items: center;

      img {
        width: 25px;
        height: 25px;
        margin-left: 10px;
      }
    }
  }

  .upload-hidden {
    overflow: hidden;
  }

  .upload- {
    .el-form-item__content {
      padding: 0 0 20px 0;
      background-color: #fff;
      border-radius: 4px;
    }

    .inline-block {
      margin-top: 15px;
    }

    .upload-w {
      width: 146px;
      height: 146px;
      border-radius: 4px;
      border: solid 1px #e0e6eb;
      text-align: center;
      vertical-align: top;
      // display: flex;
      // justify-content: center;
      // align-items: center;
    }

    .file-upload {
      height: 146px;

      .el-upload-list--picture-card .el-upload-list__item {
        width: 100%;
        height: 146px;
        border: none;
      }

      .el-upload--picture-card {
        width: auto;
        height: auto;
      }

      &.hide-upload {
        .el-upload--picture-card {
          display: none;
        }

        .el-upload-dragger {
          border: none;
        }
      }
    }

    .el-upload-dragger {
      width: 146px;
      height: 146px;
    }

    .avatar {
      display: block;
      width: 100%;
      max-height: 146px;
    }

    .upload-t {
      vertical-align: top;
      margin-top: 40px;
      line-height: 2;
      color: #ff9b45;

      .el-icon-circle-plus {
        font-size: 30px;
        color: #ff9b45;
      }
    }

    .upload-tips {
      margin-top: 30px;
      padding-left: 20px;
      color: #9fa7ad;
    }
  }

  .food-img-wrap {
    display: flex;
    flex-wrap: wrap;
    width: 900px;

    .food-img-item {
      width: 150px;
      height: 150px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: solid 1px #e0e6eb;
      margin: 20px 20px 0 0;
      overflow: hidden;
      position: relative;

      img {
        width: 150px;
        height: 150px;
        border-radius: 4px;
      }

      .food-img-mask {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: #2e313474;
        display: none;
        line-height: 150px;
        text-align: center;
        font-size: 30px;
        color: #FFF;

        i {
          cursor: pointer;
        }
      }
    }

    .food-img-item:hover {
      .food-img-mask {
        display: block;
      }
    }
  }

  .tip-o-7 {
    font-size: 14px;
    color: #23282d;
    opacity: 0.7;
  }

  .nutrition-wrapper {
    width: 1000px;
    display: flex;
    flex-wrap: wrap;
  }

  .nutrition-item {
    width: calc(33.33% - 20px);
    display: flex;
    padding: 0 10px 10px 10px;
    box-sizing: border-box;

    .nutrition-label {
      flex: 0 0 auto;
      width: auto;
      min-width: 120px;
      text-align: right;
      margin-right: 8px;
      font-size: 14px;
      color: #23282d;
    }

    .nutrition-value {
      flex: 1;
    }
  }

  .el-tag+.el-tag {
    margin-left: 10px;
  }

  .button-new-tag {
    margin-right: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }

  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }

  .cantent {
    flex: 1;
    text-align: center;
    // color: #fff;
  }

  .cooking-type-item {
    min-width: 60px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border: solid 1px #DCDFE6;
    border-radius: 4px;
    color: #54585c;
    cursor: pointer;

    &.active {
      background-color: #ff9b45;
      color: #fff;
      border: none;
    }
  }

  // 添加分类下拉框样式
  .category-select-dropdown {
    max-width: 190px !important;
    max-height: 300px !important;
    margin-top: 0 !important;
    position: fixed !important;

    .el-select-dropdown__wrap {
      max-height: 300px !important;
    }

    .el-scrollbar__wrap {
      margin-bottom: 0 !important;
    }
  }

  // 确保表格中的下拉框也能正确显示
  .el-table {
    .el-select {
      width: 100%;
    }
  }

  .selected-tags {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 800px;

    .tag-item {
      background-color: #ff9b51;
      color: #fff;
      border: none;
      margin-left: 0;

      .el-tag__close {
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
          color: #fff;
        }
      }
    }
  }
}
.text-center {
  text-align: center;
}
.w-830 {
  width: 830px;
}
.table-title {
  width: 830px;

}
.flex-between{
  justify-content: space-between;
}
</style>
