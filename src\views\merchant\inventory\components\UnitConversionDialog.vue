<template>
  <!-- dialog start -->
  <dialog-message :show.sync="visible" :loading="dialogLoading" :title="dialogTitle" :width="width" footer-center
    @close="closeDialog">
    <div class="m-b-10">
      <el-button :disabled="dialogLoading" class="ps-btn" type="primary" size="small" @click="addUnit">添加换算单位</el-button>
    </div>
    <el-table :data="tableData" ref="tableRef" style="width: 100%;" stripe size="small" border max-height="400"
      header-row-class-name="ps-table-header-row" v-loading="tableLoading">
      <el-table-column prop="nowUnit" label="当前单位" align="center">
        <template slot-scope="scope">
          {{ scope.row.nowUnit }}
        </template>
      </el-table-column>
      <el-table-column prop="relative" label="关系" align="center">
        <template>
          {{ "=" }}
        </template>
      </el-table-column>
      <el-table-column prop="rate" label="转换率" align="center">
        <template slot-scope="scope">
          <el-input v-model="scope.row.rate" placeholder='请输入' type="text" class="ps-input"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="rateUnit" label="换算单位" align="center">
        <template slot-scope="scope">
          <el-select class="w-100 ps-select" v-model="scope.row.rateUnit" placeholder="请选择" @change="selectChange"
            clearable filterable>
            <el-option v-for="(item, index) in unitList" :key="index" :label="item.name" :value="item.id"
              :disabled="item.disabled">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="operration" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" class="ps-origin"
            @click.stop="handlerDelete(scope.$index, scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div slot="tool" class="footer-center m-t-60">
      <el-button :disabled="dialogLoading" class="ps-cancel-btn min-w-100" @click="cancleDialog">取消</el-button>
      <el-button :disabled="dialogLoading" class="ps-btn min-w-100" type="primary" @click="confirmDialog">确定</el-button>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { deepClone, to } from '@/utils';
import { positiveMoney } from '@/utils/validata.js'

export default {
  name: 'UnitConversionDialog',
  props: {
    // 弹窗显示隐藏
    isShow: {
      required: true
    },
    dialogTitle: {
      type: String,
      default: '单位换算'
    },
    // 类型
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '650px'
    },
    infoData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogLoading: false,
      unitList: [], // 单位列表
      tableData: [],
      tableLoading: false,
      tableItem: {
        nowUnit: '',
        rate: '',
        rateUnit: ''
      },
      itemData: {},
      disabledUnitList: [],
      deleteIdList: [] // 删除的id列表
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('changeShow', val)
      }
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        console.log(33, val)
        this.init()
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    init() {
      this.getUnitManagementList()
      this.getInfoHandle()
      this.setInfoData(this.infoData)
    },
    closeDialog() {
      this.$emit('closeHandle')
      this.tableData = []
      this.disabledUnitList = []
      this.deleteIdList = []
    },
    cancleDialog() {
      this.closeDialog()
    },
    async confirmDialog() {
      if (!this.tableData || this.tableData.length === 0) {
        return this.$message.error('请添加一种换算')
      }
      for (let i = 0; i < this.tableData.length; i++) {
        if (!positiveMoney(this.tableData[i].rate) || parseFloat(this.tableData[i].rate) <= 0) {
          return this.$message.error("第" + (i + 1) + "行" + "转换率，只能填整数或者1位小数！")
        }
        if (!this.tableData[i].rateUnit) {
          return this.$message.error("第" + (i + 1) + "行" + "，请选择换算单位")
        }
      }
      console.log("confirmDialog", this.tableData)
      this.saveUnitList()
    },
    // 添加单位
    addUnit() {
      this.tableData.push(deepClone(this.tableItem))
    },
    // 获取单位列表数据
    async getUnitManagementList() {
      const [err, res] = await to(this.$apis.apiBackgroundDrpUnitManagementListPost({
        page: 1,
        page_size: 999999,
        organization_id: this.$store.getters.organization
      }))
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        this.unitList = data.results || []
        this.updateUnitList()
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 删除
    handlerDelete(index, row) {
      if (this.tableData && this.tableData.length > index) {
        let list = deepClone(this.tableData)
        list.splice(index, 1)
        this.tableData = deepClone(list)
        if (row.id) {
          this.deleteIdList.push(row.id)
        }
        this.updateUnitList()
      }
    },
    // 选择单位
    selectChange(value) {
      this.updateUnitList()
    },
    // 更新单位列表
    updateUnitList() {
      this.disabledUnitList = []
      // 列表已选Id
      this.tableData.forEach(item => {
        this.disabledUnitList.push(item.rateUnit)
      })
      // 传入id也要加上
      this.disabledUnitList.push(this.infoData.unit_management_id)
      if (this.disabledUnitList && this.unitList) {
        this.unitList = this.unitList.map(item => {
          if (this.disabledUnitList.includes(item.id)) {
            item.disabled = true
          } else {
            item.disabled = false
          }
          return item
        })
      }
    },
    // 保存单位换算列表
    async saveUnitList() {
      let addList = []
      let modifyList = []
      if (this.tableData) {
        this.tableData.forEach(item => {
          let paramsItem = {
            conversion_rate: item.rate,
            unit_management_id: item.rateUnit
          }
          if (item.id) {
            paramsItem.unit_conversion_id = item.id
            modifyList.push(paramsItem)
          } else {
            addList.push(paramsItem)
          }
        })
        this.dialogLoading = true
        let params = {
          materials_id: this.infoData.materials_id,
          supplier_manage_id: this.infoData.supplier_manage_id,
          inventoryinfo_id: this.infoData.id,
          add_list: addList
        }
        if (modifyList && modifyList.length > 0) {
          params.modify_list = modifyList
        }
        if (this.deleteIdList && this.deleteIdList.length > 0) {
          params.delete_list = this.deleteIdList
        }
        const [err, res] = await this.$to(this.$apis.apiBackgroundDrpUnitManagementInventoryUnitConversionPost(params))
        this.dialogLoading = false
        if (err) {
          this.$message.error(err.message || "保存失败")
          return
        }
        if (res && res.code === 0) {
          this.$message.success('保存成功')
          this.$emit('confirmDialog', this.tableData)
        } else {
          this.$message.error(res.msg || "保存失败")
        }
      }
    },
    // 设置弹窗数据
    setInfoData(val) {
      console.log(val, 123)
      if (val) {
        this.itemData = deepClone(val)
        this.$set(this.tableItem, 'nowUnit', val.unit_management_name)
        this.updateUnitList()
      }
    },
    // 获取数据
    async getInfoHandle() {
      this.dialogLoading = true
      this.tableData = []
      console.log(this.infoData)
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpUnitManagementInventoryUnitConversionListPost({
        materials_id: this.infoData.materials_id,
        supplier_manage_id: this.infoData.supplier_manage_id,
        inventoryinfo_id: this.infoData.id
      }))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data) {
          res.data.forEach(v => {
            this.tableData.push({
              nowUnit: v.inventoryinfo_unit_name,
              rate: v.conversion_rate,
              rateUnit: v.unit_management_id,
              id: v.id
            })
          })
        }
      } else {
        this.$message.error(res.msg || '出错啦！')
      }
    }
  }
}

</script>

<style scoped lang="scss"></style>
