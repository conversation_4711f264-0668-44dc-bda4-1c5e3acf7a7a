<template>
  <!-- <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="addFormData"
      @submit.native.prevent
      status-icon
      ref="addDataForm"
      :rules="addDataFormRules"
      label-width="80px"
      class="addData-form"
      inline
    >
      <div>
        <el-form-item label="选择商户" prop="commercialOwnerValue">
          <el-autocomplete
            v-model="addFormData.commercialOwnerValue"
            placeholder="请输入商户名"
            :fetch-suggestions="querySearch"
            :clearable="true"
            class="w-180">
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="交易类型" prop="transaction_type">
          <el-radio-group class="ps-radio" v-model="addFormData.transaction_type">
            <el-radio label="renew">续费</el-radio>
            <el-radio v-if="shouldShow" label="expansion">扩容</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addFormData.transaction_type === 'renew'" label="增加期限" prop="renew_days">
          <el-input v-model="addFormData.renew_days" class="ps-input w-180" type="number"></el-input>
          <span class="m-l-10">天</span>
        </el-form-item>
        <el-form-item v-if="addFormData.transaction_type === 'expansion'" label="用户上限" prop="user_scale">
          <el-input v-model="addFormData.user_scale" class="ps-input w-180" type="number"></el-input>
          <span class="m-l-10">人</span>
        </el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message> -->
  <div class="ps-el-drawer">
    <el-drawer
      :title="title"
      :visible="visible"
      :show-close="false"
      size="40%">
      <div class="p-20">
        <el-form
          :model="addFormData"
          @submit.native.prevent
          status-icon
          ref="addDataForm"
          :rules="addDataFormRules"
          label-width="80px"
          class="addData-form"
        >
          <div>
            <el-form-item label="选择商户" prop="commercialOwnerValue">
              <el-autocomplete
                v-model="addFormData.commercialOwnerValue"
                placeholder="请输入商户名"
                :fetch-suggestions="querySearch"
                :clearable="true"
                class="w-250">
              </el-autocomplete>
            </el-form-item>
            <el-form-item label="交易类型" prop="transaction_type">
              <el-radio-group class="ps-radio" v-model="addFormData.transaction_type">
                <el-radio label="renew">续费</el-radio>
                <el-radio v-if="shouldShow" label="expansion">扩容</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="addFormData.transaction_type === 'renew'" label="增加期限" prop="renew_days">
              <el-input v-model="addFormData.renew_days" class="ps-input w-250" type="number"></el-input>
              <span class="m-l-10">天</span>
            </el-form-item>
            <el-form-item v-if="addFormData.transaction_type === 'expansion'" label="用户上限" prop="user_scale">
              <el-input v-model="addFormData.user_scale" class="ps-input w-250" type="number"></el-input>
              <span class="m-l-10">人</span>
            </el-form-item>
          </div>
        </el-form>
        <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
          <div class="m-r-30">
            <el-button size="small"  class="w-100" @click="clickCancleHandle">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="clickConfirmHandle">保存</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { to } from '@/utils'
export default {
  name: 'AddDialog',
  props: {
    showAddDialog: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '400px'
    },
    formatClue: {
      type: Array,
      default: () => {
        return []
      }
    },
    isShow: Boolean
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    },
    shouldShow() {
      let type = ''
      this.searchData.forEach(item => {
        console.log(item)
        if (item.value === this.addFormData.commercialOwnerValue) {
          type = item.toll_type
        }
      })
      if (type === 2) {
        return false
      } else {
        return true
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        console.log('newVal', this.formatClue)
        this.searchData = this.formatClue
      }
    }
  },
  data() {
    let checkRenewDays = (rule, value, callback) => {
      if (value) {
        if (value > 9999) {
          return callback(new Error('最大值为4位数'))
        }
        if (value <= 0) {
          return callback(new Error('续费天数需大于或等于一天'))
        }
        callback()
      } else {
        return callback(new Error('请输入续费天数'))
      }
    }
    let checkUserScale = (rule, value, callback) => {
      if (value) {
        if (value > 99999) {
          return callback(new Error('最大值为5位数'))
        }
        if (value <= 0) {
          return callback(new Error('续费天数需大于或等于一人'))
        }
        callback()
      } else {
        return callback(new Error('请输入用户上限'))
      }
    }
    let checkCommercialOwner = (rule, value, callback) => {
      if (value) {
        if (!this.addFormData.commercialOwnerValue) {
          this.addFormData.transaction_type = ''
          callback()
        } else {
          callback()
        }
      } else {
        return callback(new Error('请先选择商户'))
      }
    }
    return {
      addDataFormRules: {
        renew_days: [{ required: true, validator: checkRenewDays, trigger: "blur" }],
        commercialOwnerValue: [{ required: true, message: '请选择商户', trigger: 'change' }],
        transaction_type: [
          { required: true, validator: checkCommercialOwner, trigger: 'change' }
        ],
        user_scale: [{ required: true, validator: checkUserScale, trigger: "blur" }]
      },
      // 新增订单
      addFormData: {
        commercialOwnerValue: '',
        company_id: '',
        transaction_type: '',
        renew_days: 1,
        user_scale: 1
      },
      isLoading: false,
      searchData: []
    }
  },
  methods: {
    querySearch(queryString, cb) {
      let results = queryString ? this.searchData.filter(this.createFilter(queryString)) : this.searchData
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (searchData) => {
        return (searchData.value.indexOf(queryString) === 0)
      }
    },
    clickCancleHandle() {
      this.$refs.addDataForm.resetFields()
      this.visible = false
    },
    clickConfirmHandle() {
      this.orderConfirmation()
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.addDataForm.resetFields()
    },
    // 确认下单
    async orderConfirmation() {
      this.$refs.addDataForm.validate(async valid => {
        if (valid) {
          let flag = false
          console.log('this.searchData', this.searchData, this.addFormData.commercialOwnerValue)
          this.searchData.forEach(item => {
            if (item.value === this.addFormData.commercialOwnerValue) {
              this.addFormData.company_id = item.id
              flag = true
            }
          })
          if (!flag) {
            return this.$message.error('尚无此商户，请确认后重试')
          }
          let params = {
            company_id: this.addFormData.company_id,
            transaction_type: this.addFormData.transaction_type,
            renew_days: this.addFormData.transaction_type === 'renew' ? parseInt(this.addFormData.renew_days) : 0,
            user_scale: this.addFormData.transaction_type === 'expansion' ? parseInt(this.addFormData.user_scale) : 0
          }
          const [err, res] = await to(
            this.$apis.apiBackgroundAdminBackgroundTollOrderCreatePost(params)
          )
          if (err) {
            this.$message.error(err.msg)
          }
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.$refs.addDataForm.resetFields()
            this.$emit('confirm')
          } else {
            this.$message.error(res.msg)
          }
          this.showDialog = false
        } else {
          this.$message.error('校验不通过，请完善信息')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
</style>
