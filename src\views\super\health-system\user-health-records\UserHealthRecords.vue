<template>
  <div class="UserHealthRecords">
    <refresh-tool @refreshPage="refreshHandle" />

    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- @click="gotoExport" -->
          <el-select
            v-model="exportType"
            placeholder="请选择导出类型"
            class="ps-select"
            popper-class="ps-popper-select"
            style="width:130px;"
            @change="changeExport"
            size="mini"
          >
            <el-option
              v-for="item in exportList"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            ></el-option>
          </el-select>
          <!-- <button-icon color="origin" type="export">导入体检报告</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
          row-key="user_id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            :reserve-selection="true"
            class-name="ps-checkbox"
          ></el-table-column>
          <el-table-column prop="id" label="档案ID" align="center"></el-table-column>
          <el-table-column prop="user_id" label="用户ID" align="center"></el-table-column>
          <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="gender" label="性别" align="center"></el-table-column>
          <el-table-column prop="weight_target" label="体重目标" align="center"></el-table-column>
          <el-table-column prop="company" label="组织" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="status_alias" label="档案状态" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="gotoNutritionalAnalysis(scope.row)">
                营养分析
              </el-button>
              <el-button type="text" size="small" @click="clickDetails(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @current-change="handleCurrentChange"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { USERHEALTHRECORDS } from './constants'

export default {
  name: 'HealthAssessment',
  mixins: [exportExcel],
  components: {},
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: USERHEALTHRECORDS,
      exportType: '',
      exportList: [
        {
          name: '导出档案',
          key: 'ExportHealthyInfoList'
        }
        // {
        //   name: '导出体检报告',
        //   key: '2'
        // },
        // {
        //   name: '导出饮食数据',
        //   key: '3'
        // },
        // {
        //   name: '导出运动数据',
        //   key: '4'
        // }
      ] // 导出列表
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getHealthyInfoList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 用户档案列表
    async getHealthyInfoList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminHealthyInfoListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    changeExport(item) {
      this.gotoExport(item)
    },
    gotoExport(type) {
      const option = {
        type: type,
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    handleSelectionChange(e) {
      // this.selectTableCoumn = e.map(v => {
      //   return v.id
      // })
    },
    clickDetails(row) {
      this.$router.push({
        name: 'SuperRecordsDetail',
        query: {
          id: row.id,
          type: row.type,
          user_id: row.user_id
        }
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getHealthyInfoList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getHealthyInfoList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    gotoNutritionalAnalysis(data) {
      console.log(data)
      // return
      this.$router.push({
        name: "SuperNutritionalAnalysis",
        query: {
          id: data.id,
          date: '',
          type: ''
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.UserHealthRecords {
  .motion-box {
    padding: 10px 20px 10px 20px;
    border: 1px solid #797979;
    border-radius: 10px;
    .text {
      font-size: 30px;
    }
  }
}
</style>
