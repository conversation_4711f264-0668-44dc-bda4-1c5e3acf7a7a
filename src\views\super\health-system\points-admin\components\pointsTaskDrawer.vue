<template>
  <div class="drawer-box">
    <customDrawer
      :show.sync="visible"
      :loading="isLoading"
      :title="drawerTitle"
      :confirmShow="drawerType !== 'details'"
      @confirm="saveSetting"
      :cancelClass="drawerType === 'details' ? 'ps-btn' : 'ps-cancel-btn'"
      :cancelText="drawerType === 'details' ? '关 闭' : '取 消'"
      :size="800"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form
            :model="drawerFormData"
            ref="drawerFormDataRef"
            :rules="drawerFormDataRuls"
            label-width="90px"
          >
            <el-form-item label="任务名称" prop="name">
              <el-input
                v-model="drawerFormData.name"
                maxlength="20"
                placeholder="请输入任务名称"
                class="w-350 ps-input"
                :disabled="drawerType === 'details'"
              ></el-input>
            </el-form-item>
            <el-form-item label="完成条件" prop="conditionType">
              <el-radio-group
                class="ps-radio"
                :disabled="drawerType === 'details'"
                v-model="drawerFormData.conditionType"
              >
                <el-radio label="specify_page">跳转指定界面</el-radio>
                <el-radio label="specify_action">完成指定动作</el-radio>
                <el-radio label="specify_url">跳转指定链接</el-radio>
              </el-radio-group>
              <el-form-item
                key="specifyPageType"
                prop="specifyPageType"
                v-if="drawerFormData.conditionType === 'specify_page'"
              >
                <el-select
                  v-model="drawerFormData.specifyPageType"
                  placeholder="请选择指定界面"
                  class="ps-select w-350"
                  :disabled="drawerType === 'details'"
                >
                  <el-option
                    v-for="item in specifyPageTypeList"
                    :key="item.type"
                    :label="item.name"
                    :value="item.type"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                prop="specifyActionType"
                key="specifyActionType"
                v-if="drawerFormData.conditionType === 'specify_action'"
              >
                <el-select
                  v-model="drawerFormData.specifyActionType"
                  placeholder="请选择指定动作"
                  class="ps-select w-350"
                  :disabled="drawerType === 'details'"
                >
                  <el-option
                    v-for="item in specifyActionTypeList"
                    :key="item.type"
                    :label="item.name"
                    :value="item.type"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                prop="specifyUrl"
                key="specifyUrl"
                v-if="drawerFormData.conditionType === 'specify_url'"
              >
                <el-input
                  v-model="drawerFormData.specifyUrl"
                  maxlength="20"
                  placeholder="请输入链接"
                  class="w-350 ps-input"
                  :disabled="drawerType === 'details'"
                ></el-input>
              </el-form-item>
            </el-form-item>
            <el-form-item label="完成次数" prop="completionsNumType">
              <el-radio-group
                class="ps-radio"
                :disabled="drawerType === 'details'"
                v-model="drawerFormData.completionsNumType"
              >
                <el-radio label="day">每天</el-radio>
                <el-radio label="week">每周</el-radio>
                <el-radio label="month">每月</el-radio>
                <el-radio label="one">一次性</el-radio>
              </el-radio-group>
              <el-form-item
                v-if="
                  drawerFormData.completionsNumType === 'week' ||
                  drawerFormData.completionsNumType === 'month'
                "
              >
                <span style="margin-right: 10px">按天执行</span>
                <el-switch
                  v-model="drawerFormData.isDay"
                  active-color="#ff9b45"
                  inactive-color="#ffcda2"
                  :disabled="drawerType === 'details'"
                ></el-switch>
              </el-form-item>
            </el-form-item>
            <el-form-item label="获得积分">
              <el-form-item
                v-for="(item, index) in drawerFormData.pointsList"
                :key="'pointsList' + index"
                :label="'第' + (index + 1) + '次获得'"
                class="points-content"
                :prop="`pointsList[${index}]` + '.points'"
                label-width="100px"
                :rules="drawerFormDataRuls.points"
                :show-message="false"
              >
                <div style="display: flex; align-items: center">
                  <el-input
                    v-model="item.points"
                    maxlength="6"
                    placeholder="请输入积分"
                    class="w-150 ps-input"
                    :disabled="drawerType === 'details'"
                  ></el-input>
                  <span style="padding-left: 10px">积分</span>
                  <img
                    src="@/assets/img/plus.png"
                    alt=""
                    v-if="drawerFormData.pointsList.length < 20 && drawerType !== 'details'"
                    @click="clickPointsList()"
                  />
                  <img
                    src="@/assets/img/reduce.png"
                    alt=""
                    v-if="index > 0 && drawerType !== 'details'"
                    @click="delPointsList(index)"
                  />
                </div>
              </el-form-item>

              <el-form-item label="往后每次获得" class="points-content" label-width="100px">
                <el-input
                  v-model="drawerFormData.afterCount"
                  maxlength="6"
                  placeholder="请输入积分"
                  class="w-150 ps-input"
                  :disabled="drawerType === 'details'"
                ></el-input>
                <span style="padding-left: 10px">积分</span>
              </el-form-item>
            </el-form-item>
            <el-form-item label="优先级" prop="priority">
              <el-input
                v-model="drawerFormData.priority"
                maxlength="6"
                placeholder="请输入优先级"
                class="w-350 ps-input"
                :disabled="drawerType === 'details'"
              ></el-input>
              <div>现有优先级：{{ priorityText() }}</div>
            </el-form-item>
            <el-form-item label="按钮文案" prop="canRemark">
              <el-input
                v-model="drawerFormData.canRemark"
                maxlength="6"
                placeholder="请输入按钮文案"
                class="w-350 ps-input"
                :disabled="drawerType === 'details'"
              ></el-input>
            </el-form-item>
            <el-form-item label="提示文案" prop="taskRemark">
              <el-input
                v-model="drawerFormData.taskRemark"
                placeholder="请输入提示文案"
                class="w-350 ps-input"
                maxlength="20"
                :disabled="drawerType === 'details'"
              ></el-input>
            </el-form-item>
            <!-- 放form-item div样式变了 -->
            <div style="display: flex; align-items: center; margin-left: 33px">
              <div style="padding-right: 13px; color: #606266; font-weight: 700; font-size: 14px">
                h5样式
              </div>
              <div class="case-btn">
                <div class="case-title">
                  <div>{{ drawerFormData.name ? drawerFormData.name : '任务名称' }}</div>
                  <div class="case-tips">
                    {{ drawerFormData.taskRemark ? drawerFormData.taskRemark : '提示文案' }}
                  </div>
                </div>
                <div>
                  <el-button class="ps-btn" type="primary">
                    {{ drawerFormData.canRemark ? drawerFormData.canRemark : '按钮文案' }}
                  </el-button>
                </div>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  props: {
    drawerType: {
      type: String,
      default() {
        return 'add'
      }
    },
    isshow: Boolean,
    drawerModifyData: {
      type: Object,
      default() {
        return {}
      }
    },
    collectData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    let validateCount = (rule, value, callback) => {
      let reg = /^\d+$/
      if (!value) {
        return callback(new Error('格式错误'))
      } else {
        if (!reg.test(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      }
    }
    return {
      drawerTitle: '新建任务',
      isLoading: false,
      drawerFormData: {
        name: '',
        conditionType: 'specify_page',
        specifyPageType: '',
        specifyActionType: '',
        specifyUrl: '',
        completionsNumType: 'day',
        isDay: false,
        pointsList: [
          {
            num: 1,
            points: ''
          }
        ],
        afterCount: '',
        priority: '',
        canRemark: '',
        taskRemark: ''
      },
      drawerFormDataRuls: {
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        conditionType: [{ required: true, trigger: ['change', 'blur'] }],
        specifyPageType: [{ required: true, message: '请选择指定界面', trigger: 'blur' }],
        specifyActionType: [
          { required: true, message: '请选择指定动作', trigger: ['change', 'blur'] }
        ],
        specifyUrl: [{ required: true, message: '请输入链接', trigger: ['change', 'blur'] }],
        completionsNumType: [{ required: true, trigger: ['change', 'blur'] }],
        points: [{ required: true, validator: validateCount, trigger: ['change', 'blur'] }],
        priority: [{ required: true, validator: validateCount, trigger: ['change', 'blur'] }],
        canRemark: [{ required: true, message: '请输入按钮文案', trigger: 'blur' }],
        taskRemark: [{ required: true, message: '请输入提示文案', trigger: 'blur' }]
      },
      specifyPageTypeList: [
        {
          name: '首页',
          type: 'front_page'
        },
        {
          name: '营养分析',
          type: 'nutritional_analysis'
        },
        {
          name: '营养周报',
          type: 'nutrition_weekly'
        }
      ],
      specifyActionTypeList: [
        {
          name: '运动打卡',
          type: 'sports_check_in'
        },
        {
          name: '记录体重',
          type: 'record_weight'
        },
        {
          name: '记录饮食',
          type: 'record_diet'
        },
        {
          name: '习惯打卡',
          type: 'habit_punching'
        },
        {
          name: '分享',
          type: 'share'
        }
      ]
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    this.initDrawerForm()
  },
  methods: {
    // 回填数据
    initDrawerForm() {
      console.log(this.drawerModifyData)
      if (this.drawerType === 'modify' || this.drawerType === 'details') {
        if (this.drawerType === 'modify') {
          this.drawerTitle = '修改任务'
        } else if (this.drawerType === 'details') {
          this.drawerTitle = '任务详情'
        }
        this.drawerFormData.name = this.drawerModifyData.name
        this.drawerFormData.conditionType = this.drawerModifyData.condition_type
        this.drawerFormData.completionsNumType = this.drawerModifyData.completions_num_type
        this.drawerFormData.priority = this.drawerModifyData.priority
        this.drawerFormData.canRemark = this.drawerModifyData.can_remark
        this.drawerFormData.taskRemark = this.drawerModifyData.task_remark
        if (this.drawerModifyData.condition_type === 'specify_page') {
          this.drawerFormData.specifyPageType = this.drawerModifyData.specify_page_type
        } else if (this.drawerModifyData.condition_type === 'specify_action') {
          this.drawerFormData.specifyActionType = this.drawerModifyData.specify_action_type
        } else if (this.drawerModifyData.condition_type === 'specify_url') {
          this.drawerFormData.specifyUrl = this.drawerModifyData.specify_url
        }
        if (
          this.drawerModifyData.completions_num_type === 'week' ||
          this.drawerModifyData.completions_num_type === 'month'
        ) {
          this.drawerFormData.isDay = this.drawerModifyData.is_day
        }
        this.drawerFormData.pointsList = []
        // 积分列表
        // this.drawerFormData.pointsList =

        this.drawerModifyData.points_list.forEach(v => {
          if (v.num === -1) {
            this.drawerFormData.afterCount = v.points
          } else {
            this.drawerFormData.pointsList.push({
              num: v.num,
              points: v.points
            })
          }
        })
      }
    },
    priorityText() {
      return this.collectData.priority_list && this.collectData.priority_list.length
        ? this.collectData.priority_list.join(',')
        : ''
    },
    clickPointsList() {
      this.drawerFormData.pointsList.push({
        num: this.drawerFormData.pointsList.length + 1,
        points: ''
      })
    },
    delPointsList(index) {
      this.drawerFormData.pointsList.splice(index, 1)
    },
    initParams() {
      let params = {
        name: this.drawerFormData.name,
        condition_type: this.drawerFormData.conditionType,
        completions_num_type: this.drawerFormData.completionsNumType,
        priority: this.drawerFormData.priority,
        can_remark: this.drawerFormData.canRemark,
        task_remark: this.drawerFormData.taskRemark
      }
      if (this.drawerFormData.conditionType === 'specify_page') {
        params.specify_page_type = this.drawerFormData.specifyPageType
      } else if (this.drawerFormData.conditionType === 'specify_action') {
        params.specify_action_type = this.drawerFormData.specifyActionType
      } else if (this.drawerFormData.conditionType === 'specify_url') {
        params.specify_url = this.drawerFormData.specifyUrl
      }
      if (
        this.drawerFormData.completionsNumType === 'week' ||
        this.drawerFormData.completionsNumType === 'month'
      ) {
        params.is_day = this.drawerFormData.isDay
      }
      params.points_list = deepClone(this.drawerFormData.pointsList)
      if (this.drawerFormData.afterCount) {
        params.points_list.push({
          num: -1,
          points: this.drawerFormData.afterCount
        })
      }
      return params
    },
    saveSetting() {
      this.$refs.drawerFormDataRef.validate(valid => {
        if (valid) {
          console.log(this.initParams())
          if (this.drawerType === 'modify') {
            this.modifyDrawerForm()
          } else {
            this.addDrawerForm()
          }
        }
      })
    },
    // 添加
    async addDrawerForm() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundMemberPointsPointsTaskAddPost(this.initParams())
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('修改成功')
        this.$emit('clickSaveDrawer')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改
    async modifyDrawerForm() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundMemberPointsPointsTaskModifyPost({
          id: this.drawerModifyData.id,
          ...this.initParams()
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('修改成功')
        this.$emit('clickSaveDrawer')
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    padding: 30px;
    .drawer-content {
      .points-content {
        margin-bottom: 10px;
        .el-form-item__label {
          text-align: left;
          padding: 0;
        }
        .el-form-item__content {
          display: flex;
          align-items: center;
          img {
            width: 25px;
            height: 25px;
            margin-left: 10px;
          }
        }
      }
    }
    .case-btn {
      width: 350px;
      min-height: 70px;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      border-radius: 5px;
      background: #edf2fa;
      .case-title {
        // display: flex;
        // flex-direction: column;
      }
      .case-tips {
        // padding-top: 50px;
        font-size: 13px;
        color: #b7b7b7;
      }
    }
  }
}
</style>
