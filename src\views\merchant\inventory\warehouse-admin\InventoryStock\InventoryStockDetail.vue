<template>
  <div class="inventory-flow-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" :show-refresh="false" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="origin" @click="handleExport">导出</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- content start -->
        <el-table
          v-loading="isLoading"
          :data="viewTableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >

        <el-table-column prop="materials_name" label="物资名称" align="center">
          <template slot-scope="scope">
             <span :class="{ red: scope.row.current_num < 0 }">{{ scope.row.materials_name }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="current_num" label="当前库存" align="center">
          <!-- <template slot-scope="scope">
            <span :class="{ red: scope.row.current_num < 0 }">{{ scope.row.current_num }}</span>
          </template> -->
        </el-table-column>
        <el-table-column prop="inventory_num" label="盘点数量" align="center">
          <!-- <template slot-scope="scope">
             <span :class="{ red: scope.row.inventory_num < 0 }">{{ scope.row.inventory_num }}</span>
            </template> -->
        </el-table-column>
        <el-table-column prop="limit_unit_name" label="最小单位" align="center"></el-table-column>
        <el-table-column prop="inventory_profit" label="盘盈盘亏" align="center">
          <template slot-scope="scope">
            <span :class="{ red: scope.row.inventory_profit < 0 }">{{ scope.row.inventory_profit }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="file_url" label="图片" align="center">
          <template slot-scope="scope">
            <el-button :disabled="imageJsonDisabled(scope.row)" type="text" size="small" class="ps-text" @click="handleClick(scope.row)">
                查看
              </el-button>
            </template>
        </el-table-column>
        <el-table-column prop="inventory_time" label="最近盘点时间" align="center">
          <template slot-scope="scope">
             <span>{{ scope.row.inventory_time }}</span>
            </template>
        </el-table-column>
        </el-table>
        <!-- content end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'InventoryStockDetail',
  mixins: [exportExcel],
  components: {
    ElImageViewer
  },
  data() {
    return {
      id: '',
      isLoading: false, // 刷新数据
      tableData: [], // table数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: {
        // select_time: {
        //   type: 'datetimerange',
        //   label: '盘点时间',
        //   format: 'yyyy-MM-dd HH:mm:ss',
        //   clearable: false,
        //   value: []
        // },
        name: {
          type: 'input',
          value: '',
          label: '物资名称',
          placeholder: '请输入',
          maxlength: 20
        },
        type: {
          type: 'select',
          value: '',
          label: '盘点类型',
          placeholder: '请选择',
          dataList: [
            { label: '全部', value: '' },
            { label: '正常', value: 'normal' },
            { label: '盘赢单', value: 'profit' },
            { label: '盘亏单', value: 'lose' }
          ]
        }
      },
      showImagePreview: false,
      previewList: [],
      filterTableData: [] // 显示的数据
    }
  },
  computed: {
    // 当前table显示的数据
    viewTableData() {
      return this.filterTableData.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
    }
  },
  watch: {
    currentPage: function (val, old) {
      
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.id = Number(this.$route.query.id)
      this.getInventoryDetail()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initViewTable()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initViewTable()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getInventoryDetail() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      params.id = this.id
      const [err, res] = await to(
        this.$apis.apiBackgroundDrpMaterialInventoryDetailsPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        // this.tableData = res.data
        this.tableData = res.data.inventory_json.map(v => {
          return {
            ...v,
            inventory_time: res.data.inventory_time
          }
        })
        // this.totalCount = this.tableData.length
        this.initViewTable()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化下显示数据，后端会返回所有的数据，筛选的参数需要前端控制数据的显示
    initViewTable() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let result = this.tableData.filter(v => {
        if (params.name && params.type) {
          return (v.materials_name.indexOf(params.name) > -1) && (v.profit_status === params.type)
        } else if (params.name) {
          return v.materials_name.indexOf(params.name) > -1
        } else if (params.type) {
          return v.profit_status === params.type
        } else {
          return true
        }
      })
      this.totalCount = result.length
      this.filterTableData = result
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
    },
    handleClick(row) {
      this.previewList = [row.file_url]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    imageJsonDisabled(row) {
      let status = true
      if (row.file_url) {
        status = false
      }
      return status
    },
    // 导出
    handleExport() {
      const option = {
        type: 'InventoryStockDetail',
        url: 'apiBackgroundDrpInventoryInfoStockTackingDetailPost',
        params: {
          id: this.id,
          is_export: true,
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.inventory-flow-wrapper {
  .ps-pagination {
    padding-top: 0px !important;
  }
}
</style>
