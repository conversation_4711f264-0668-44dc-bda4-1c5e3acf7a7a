<template>
  <customDrawer :show.sync="visible" cancelText="关闭" :confirmShow="type==='add'" :loading="isLoading" :title="title"
    :size="size" :wrapperClosable="false" :showClose="false" @confirm="confirmDialog" @cancel="closeDialog">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="140px" label-position="right" class="tianjiaji-form">
      <div v-for="(item, index) in formData.infoData" :key="index" class="tianjiaji-form-wrap">
        <div class="form-item">
          <el-form-item label="添加剂名称" :prop="`infoData.${index}.additive_name`" :rules="formRules.additive_name">
            <el-input v-model="formData.infoData[index].additive_name" class="w-300" placeholder="请输入添加剂名称" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="生产经营单位" :prop="`infoData.${index}.unit`" :rules="formRules.unit">
            <el-input v-model="formData.infoData[index].unit" class="w-300" placeholder="请输入生产经营单位" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="限量标准（kg/g）" :prop="`infoData.${index}.limit_standard`" :rules="formRules.limit_standard">
            <el-input v-model="formData.infoData[index].limit_standard" class="w-300" placeholder="请输入限量标准" maxlength="40"></el-input>
          </el-form-item>
        </div>
        <div>
          <img src="@/assets/img/plus.png" class="m-r-10" alt="" @click="addInfo()"  v-if="formData.infoData.length -1 === index">
          <img src="@/assets/img/reduce_red.png" alt="" @click="delInfo(index)" v-if="formData.infoData.length > 1">
        </div>
      </div>
    </el-form>
  </customDrawer>
  <!-- end -->
</template>

<script>
import { to } from '@/utils'
export default {
  name: 'TianJiaJiAddDrawer',
  props: {
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'add'
    },
    size: {
      type: String,
      default: '50%'
    },
    isshow: Boolean
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      // 表单数据
      formData: {
        infoData: [{
          additive_name: "",
          unit: "",
          limit_standard: ""
        }]
      },
      // 表单验证规则
      formRules: {
        additive_name: [{ required: true, message: '请输入添加剂名称', trigger: 'blur' }],
        unit: [{ required: true, message: '请输入生产经营单位', trigger: 'blur' }],
        limit_standard: [{ required: true, message: '请输入限量标准', trigger: 'blur' },
        // 限制输入正数，并且限制最多两位小数
          { validator: this.validateLimitStandard, trigger: 'blur' }
        ]
      },
      keyType: 'Additive'
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    isshow(val) {
      if (val) {
        // 初始化数据
        this.initData()
      }
    }
  },
  created() {
  },
  mounted() { },
  methods: {
    // 初始化数据
    async initData() {
      let configData = await this.getConfigList()
      if (configData && configData.length > 0) {
        this.formData.infoData = configData
      } else {
        this.formData.infoData = [{
          additive_name: "",
          unit: "",
          limit_standard: ""
        }]
      }
    },

    async confirmDialog(e) {
      console.log('confirmDialog', this.formData.tableData)
      try {
        // 先进行表单验证
        this.$refs.formRef.validate((valid) => {
          if (valid) {
            // 验证通过
            this.saveData()
          }
        })
      } catch (error) {
        // 验证失败，显示错误信息
        console.log('error', error)
        this.$message.error('请检查表单数据')
        return
      }
    },
    // 保存数据
    async saveData() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyModifyLedgerItemConfig({
          ledger_data_type: this.keyType,
          ledger_item_config: this.formData.infoData
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '保存失败')
        return
      }
      if (res && res.code === 0) {
        this.$message.success('保存成功')
        this.visible = false
        this.$emit('confirm', this.formData.infoData)
      } else {
        this.$message.error(res.msg || '保存失败')
      }
    },
    closeDialog() {
      this.$emit('close')
    },
    delInfo(index) {
      this.formData.infoData.splice(index, 1)
    },
    addInfo() {
      this.formData.infoData.push({
        additive_name: "",
        unit: "",
        limit_standard: ""
      })
    },
    // 获取配置项
    getConfigList() {
      return new Promise((resolve) => {
        let params = {
          ledger_data_type: this.keyType
        }
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetLedgerItemConfig(params).then(res => {
          console.log('res', res)
          if (res && res.code === 0) {
            let data = res.data || []
            if (data && data.length > 0) {
              const tag = data[0] || {}
              const ledgerItemConfig = tag.ledger_item_config || []
              let newList = []
              if (ledgerItemConfig && ledgerItemConfig.length > 0) {
                ledgerItemConfig.forEach((item, index) => {
                  newList.push({
                    additive_name: item.additive_name,
                    unit: item.unit,
                    limit_standard: item.limit_standard
                  })
                })
              }
              resolve(newList)
            } else {
              resolve([])
            }
          } else {
            this.$message.error(res.msg || '获取配置项失败')
            resolve(false)
          }
        }).catch(err => {
          this.$message.error(err.message || '获取配置项失败')
          resolve(false)
        })
      })
    },
    validateLimitStandard(rule, value, callback) {
      if (!/^\d+(\.\d{1,2})?$/.test(value)) {
        callback(new Error('请输入正数，并且限制最多两位小数'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tianjiaji-form{
  padding: 20px;
  .tianjiaji-form-wrap{
    display: flex;
    align-items: end;
    margin-bottom: 20px;
    .form-item{
      border-radius: 10px;
      padding: 20px 20px 10px;
      background: #F5F5F5;
      margin-right: 20px;
    }
  }
}
</style>
