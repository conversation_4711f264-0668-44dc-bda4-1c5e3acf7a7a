<template>
  <div id="notice-admin-container" class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="notice-admin-list">
      <!-- <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form> -->
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-popover
              placement="top-start"
              title=""
              trigger="hover">
              <div class="popover-text">
                <p>说明：发布新公告后，①若设置永不弹出，用户登录时不会弹窗提示有新公告</p>
                <p>②若设置弹出一次，则用户第一次登录时弹窗提示有新公告</p>
                <p>③若设置每次弹出，则用户每次登录时均弹窗提示有新公告</p>
              </div>
              <el-icon class="el-icon-question pointer" slot="reference"></el-icon>
            </el-popover>
            <el-select v-model="showNoticeType" placeholder="弹出类型" size="mini" class="m-l-10 ps-select" popper-class="ps-popper-select" @change="changeNoticePopupType" :disabled="!allPermissions.includes('background_marketing.marketing_notice.set_popup_type')">
              <el-option v-for="item in noticeTypeList" :key="item.value" :label="item.name" :value="item.value"></el-option>
            </el-select>
            <button-icon style="display: inline-block;" :buttonData="buttonData" @gotoAddNotice="addNoticeHandle('add')"></button-icon>
          </div>
        </div>
        <div class="table-content">
          <div style="font-size: 13px; text-align: right; margin-bottom: 20px;">可置顶一条公告，置顶后，将会在移动端首页以跑马灯的形式展示</div>
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            row-key="id"
            border
            :row-class-name="tableRowClassName"
            header-row-class-name="ps-table-header-row"
            class="ps-table"
          >
            <el-table-column
              prop="name"
              label="名称"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="display_limit"
              label="适用用户"
              align="center"
              width="200"
              :show-overflow-tooltip="true"
            >
            <template slot-scope="scope">
              {{ showName(scope.row.display_limit) }}
             </template>
            </el-table-column>
            <el-table-column
              prop="userGroups"
              label="适用分组"
              align="center"
              width="200"
              :show-overflow-tooltip="true"
            >
             <template slot-scope="scope">
                 {{ showGroupName(scope.row) }}
             </template>
            </el-table-column>
            <el-table-column
              prop="read_count"
              label="已查看人数"
              align="center"
            >
              <template slot-scope="scope">
                <div class="pointer ps-origin" @click="goToPage(scope.row)">
                  {{ scope.row.read_count }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              prop="create_time"
              label="创建时间"
              align="center"
            >
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="addNoticeHandle('modify', scope.row)"
                  v-permission="['background_marketing.marketing_notice.modify']"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  @click="setNoticeTopHandle(!(topNotice === scope.row.id), scope.row)"
                  v-permission="['background_marketing.marketing_notice.set_top']"
                  >{{ topNotice === scope.row.id ? '取消置顶' : '置顶' }}</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  @click="deleteNoticeHandle(scope.row)"
                  v-permission="['background_marketing.marketing_notice.delete']"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!-- table end -->
        </div>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { to, debounce, setSessionStorage } from '@/utils'
import { mapGetters } from 'vuex'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'NoticeAdmin',
  // mixins: [activatedLoadData],
  data() {
    return {
      searchForm: {
      },
      tableData: [], // 列表数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      isLoading: false,
      selectDate: [],
      searchFormSetting: {
      },
      buttonData: [
        {
          name: this.$t('button.add_notice'),
          click: 'gotoAddNotice',
          type: 'add',
          color: 'origin',
          permission: ['background_marketing.marketing_notice.add']
        }
      ],
      showNoticeType: '', // 公告展示的方式
      noticeTypeList: [
        { name: '永不弹出', value: 0 },
        { name: '弹出一次', value: 1 },
        { name: '每次弹出', value: 2 }
      ],
      topNotice: '' // 置顶的公告id
    }
  },
  created() {
    this.initLoad()
  },
  computed: {
    ...mapGetters(['allPermissions'])
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMsgList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getMsgList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      // this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取公告列表
    async getMsgList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMarketingMarketingNoticeListPost({
        // ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        this.showNoticeType = res.data.popup_type
        this.topNotice = res.data.top_notice
      } else {
        this.$message.error(res.msg)
      }
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getMsgList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMsgList()
    },
    // 新建公告
    addNoticeHandle(type, data) {
      let query = {}
      if (data) {
        let key = 'MOBILENOTICEdETAIL'
        query.key = key
        query.id = data.id
        setSessionStorage(key, this.$encodeQuery(data))
      }
      this.$router.push({
        name: 'MerchantAddMobileNotice',
        params: {
          type
        },
        query: query
      })
    },
    // 删除
    async deleteNoticeHandle(row) {
      if (this.isLoading) return;
      let params = {
        ids: [row.id]
      }
      this.$confirm('确定要删除公告吗，删除后不可恢复', '提示', {
        confirmButtonText: '删 除',
        cancelButtonText: '取 消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundMarketingMarketingNoticeDeletePost(params))
            this.isLoading = false
            instance.confirmButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            done()
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getMsgList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 置顶
    async setNoticeTopHandle(type, row) {
      if (this.isLoading) return;
      let params = {
        id: row.id,
        is_set_top: type
      }
      let message = type ? '置顶' : '取消置顶'
      this.$confirm(`确定要${message}当前公告吗?`, '提示', {
        confirmButtonText: '确 定',
        cancelButtonText: '取 消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundMarketingMarketingNoticeSetTopPost(params))
            this.isLoading = false
            instance.confirmButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            done()
            if (res.code === 0) {
              this.$message.success('修改成功！')
              this.getMsgList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    changeNoticePopupType(e) {
      this.setNoticePopup(e)
    },
    async setNoticePopup(type) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingMarketingNoticeSetPopupTypePost({
          popup_type: this.showNoticeType
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改成功!')
        this.getMsgList()
      } else {
        this.$message.error(res.msg)
      }
    },
    showName(type) {
      switch (type) {
        case 'all':
          return '所有人'
        case 'group':
          return '限制分组'
        case 'user':
          return '指定人员'
      }
    },
    // 显示分组名字
    showGroupName(itemData) {
      let displayLimit = itemData.display_limit || ''
      if (displayLimit === 'all') {
        return '所有人'
      } else {
        let list = itemData.group_name_list || []
        if (!list || list.length === 0) {
          return ''
        }
        return list.join(',')
      }
    },
    goToPage(data) {
      this.$router.push({
        name: 'MerchantHaveViewed',
        query: {
          id: data.id
        }
      })
    }
  }
}
</script>

<style lang="scss">
#notice-admin-container {
  .notice-admin-list {
    min-width: 0;
    .search-form{
      padding: 20px 20px 0;
    }
    .table-content {
      .no-backgroundColor:hover > td {
        background-color: #ffffff !important;
      }
      .backgroundColor:hover > td {
        background-color: #f5f7fa !important;
      }
      .backgroundColor {
        background-color: #f5f7fa;
        // height: 100%;
      }
      th.el-table-column--selection .cell {
        display: none;
      }
    }
    .column-flex {
      display: flex;
      justify-content: space-between;
      .column-l {
        flex: 1;
        margin-right: 30px;
      }
    }
  }
}
</style>
