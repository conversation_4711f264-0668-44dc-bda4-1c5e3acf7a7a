<template>
  <div class="assignment-ledger-permission container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"
      :autoSearch="false" @reset="resetHandler"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="handlerMultiEdit"
            :disabled="!selectedIds || selectedIds.length === 0" v-permission="['background_fund_supervision.ledger.ledger_permission_bulk_save']">批量编辑</button-icon>
          <button-icon color="origin" @click="clickHistoricalRecords">历史记录</button-icon>
          <button-icon color="origin" @click="clickConfigurationPermission('add')" v-permission="['background_fund_supervision.ledger.ledger_permission_save']">配置权限</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe
          header-row-class-name="ps-table-header-row" @selection-change="handleSelectionChange"
          height="calc(100vh - 570px)" :max-height="600">
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #ledger_type_alias_list="{ row }">
              <el-tooltip class="item" effect="dark" :content="getLedgerTypeAliasList(row.ledger_type_alias_list)" placement="top">
                <div class="line-1">{{getLedgerTypeAliasList(row.ledger_type_alias_list)}}</div>
              </el-tooltip>
            </template>
            <template #operators_list="{ row }">
              <el-tooltip class="item" effect="dark" :content="getOperatorsList(row.operators_list)" placement="top">
                <div class="line-1">{{getOperatorsList(row.operators_list)}}</div>
              </el-tooltip>
            </template>
            <template #reviewers_list="{ row }">
              <el-tooltip class="item" effect="dark" :content="getReviewersList(row.reviewers_list)" placement="top">
                <div class="line-1">{{getReviewersList(row.reviewers_list)}}</div>
              </el-tooltip>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" @click="clickConfigurationPermission('edit', row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-red" @click="handlerDelete(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount">
        </pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 历史记录 -->
    <ledger-permission-record-drawer :isshow.sync="isShowRecordDialog" @confirm="confirmRecordDialog"
      @close="confirmRecordDialog"></ledger-permission-record-drawer>
    <!-- 配置权限 -->
    <configuration-permission-drawer :visible="isShowConfigurationPermissionDialog" :dialogType="dialogType"
      :permissionData="permissionData" :dialogTitle="dialogTitle" :chooseData="chooseData"
      @confirm="confirmConfigurationPermissionDialog"
      @close="isShowConfigurationPermissionDialog = false"></configuration-permission-drawer>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import { SEARCH_SETTING_MORNING_LEDGER_PERMISSION, TABLE_HEAD_DATA_LEDGER_PERMISSION } from './constants'
import LedgerPermissionRecordDrawer from './compontents/LedgerPermissionRecordDrawer.vue'
import ConfigurationPermissionDrawer from './compontents/ConfigurationPermissionDrawer.vue'
export default {
  name: 'AssignmentLedgerPermission',
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_LEDGER_PERMISSION), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_LEDGER_PERMISSION), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_MORNING_LEDGER_PERMISSION), // 查询表单配置
      printType: 'AssignmentLedgerPermission', // 类型
      isShowRecordDialog: false, // 历史记录
      isShowConfigurationPermissionDialog: false, // 配置权限
      dialogType: 'add', // 配置权限类型
      permissionData: {}, // 配置权限数据
      dialogTitle: '配置权限', // 配置权限标题
      chooseData: [], // 批量编辑数据
      selectedIds: [], // 批量编辑选中id
      ledgerTypeList: [] // 管理台账列表
    }
  },
  created() {
    this.initLoad()
  },
  components: {
    LedgerPermissionRecordDrawer,
    ConfigurationPermissionDrawer
  },
  mounted() { },
  methods: {
    initLoad() {
      this.getDataList()
      this.getLedgerTypeList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (key === 'ledger_type_list') {
              params.ledger_type_list = [data[key].value]
            } else if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取留样记录
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerLedgerPermissionListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    async getLedgerTypeList() {
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerLedgerTypeListPost({})
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || []
        this.ledgerTypeList = deepClone(data)
        this.searchFormSetting.ledger_type_list.dataList = deepClone(data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log('handleSelectionChange', val)
      this.selectedIds = val.map(item => item.id)
      this.chooseData = deepClone(val)
    },
    // 查看详情
    gotoDetail(data) {
      this.$router.push({
        name: 'MerchantAiRetentionInstrumentDetail',
        query: {
          id: data.id,
          data: this.$encodeQuery(data)
        }
      })
    },
    // 导出弹窗
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tabbleSetting = deepClone(this.tableSetting)
      tabbleSetting = tabbleSetting.filter(item => item.key !== 'operation')
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '台账权限分配',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tabbleSetting),
          current_table_setting: JSON.stringify(tabbleSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 点击历史记录
    clickHistoricalRecords() {
      this.isShowRecordDialog = true
    },
    // 确定
    confirmRecordDialog() {
      this.isShowRecordDialog = false
    },
    // 配置权限
    clickConfigurationPermission(type, row) {
      this.dialogType = type
      this.dialogTitle = type === 'add' ? '配置权限' : '编辑权限'
      this.permissionData = type === 'edit' ? deepClone(row) : {}
      this.chooseData = []
      this.isShowConfigurationPermissionDialog = true
    },
    // 确定配置权限
    confirmConfigurationPermissionDialog() {
      console.log('confirmConfigurationPermissionDialog')
      this.isShowConfigurationPermissionDialog = false
      this.currentPage = 1
      this.getDataList()
    },
    // 批量编辑
    handlerMultiEdit() {
      this.dialogType = 'multi'
      this.dialogTitle = '批量编辑'
      this.permissionData = {}
      this.isShowConfigurationPermissionDialog = true
    },
    // 删除
    handlerDelete(row) {
      console.log('handlerDelete', row)
      let message = row.ledger_type_alias_list || []
      if (message && message.length > 0) {
        message = message.join(',')
      } else {
        message = ""
      }
      this.$confirm(`是否确认删除对'${message}'的权限分配？删除后，任意管理员均可上传相关记录。`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundFundSupervisionLedgerLedgerPermissionDeletePost({
              ids: [row.id]
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.currentPage = 1
              this.initLoad()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => { })
    },
    // 获取管理台账名称
    getLedgerTypeAliasList(ledgerTypeList) {
      let list = []
      if (ledgerTypeList && ledgerTypeList.length > 0) {
        ledgerTypeList.forEach(item => {
          list.push(item)
        })
      }
      return list.join(',')
    },
    // 获取指定操作员
    getOperatorsList(operatorsList) {
      let list = []
      if (operatorsList && operatorsList.length > 0) {
        operatorsList.forEach(item => {
          list.push(item.member_name + "(" + item.username + " )")
        })
      }
      return list.join(',')
    },
    // 获取复核人
    getReviewersList(reviewersList) {
      let list = []
      if (reviewersList && reviewersList.length > 0) {
        reviewersList.forEach(item => {
          list.push(item.member_name + "(" + item.username + " )")
        })
      }
      return list.join(',')
    }
  }
}
</script>
<style lang="scss" >
.el-tooltip__popper {
  max-width: 30% !important;
  word-break: break-all;
  white-space: normal;
}
</style>
