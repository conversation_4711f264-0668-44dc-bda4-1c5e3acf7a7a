<template>
  <div class="warehouse-wrapper container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="handleExport">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- content start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <!-- <el-button type="text" class="ps-text" size="small" @click="gotoInventory(row)">进入仓库</el-button> -->
            </template>
          </table-column>
        </el-table>
        <!-- content end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- end -->
      <!-- 分页 start -->
      <div class="block ps-pagination">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange, divide } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import WarehouseDialog from '../components/WarehouseDialog'
import { mapGetters } from 'vuex'

export default {
  name: 'InventoryBalanceSheet',
  mixins: [exportExcel],
  components: { WarehouseDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '日期',
          clearable: false,
          value: getSevenDateRange(7)
        },
        materials_name: {
          type: 'input',
          value: '',
          label: '物资名称',
          placeholder: '请输入物资名称'
        }
      },
      tableData: [],
      tableSettings: [
        { label: '日期', key: 'operate_time', isComponents: true, type: 'date', format: 'YYYY年MM月DD日  HH:mm:ss' },
        { label: '销售金额', key: 'materials_name' },
        { label: '销售成', key: 'operate_price_alias' },
        { label: '毛利', key: 'total_price_alias1' },
        { label: '毛利率', key: 'total_price_alias21' },
        // { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", minWidth: '120px' }
      ],
      collect: [ // 统计
      { key: 'entry_total_price', value: 0, label: '总毛利', type: 'money' },
      { key: 'entry_total_price1', value: 0, label: '毛利率', type: 'money' }
      ],
      elementLoadingText: "数据正在加载，请耐心等待...",
      isLoadingCollect: false,
    }
  },
  computed: {
    ...mapGetters(['organization'])
  },
  created() {
    // this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getWarehouseList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getWarehouseList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: +this.$route.query.warehouse_id,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpInventoryInfoCostDetailPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data) {
          this.totalCount = res.data.count
          // 处理下后端返回的数据
          this.tableData = res.data.results.map(v => {
            v.operate_price_alias = '￥' + divide(v.operate_price) + '/' + v.unit_name
            return v
          })
          this.collect.forEach(item => {
            for (let i in res.data.collect) {
              if (item.key === i) {
                item.value = res.data.collect[i]
              }
            }
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getWarehouseList()
    },
    handleExport() {
      const option = {
        type: 'Costbreakdown',
        url: 'apiBackgroundDrpInventoryInfoCostDetailExportPost',
        params: {
          warehouse_id: +this.$route.query.warehouse_id,
          ...this.formatQueryParams(this.searchFormSetting),
          page: 1,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
