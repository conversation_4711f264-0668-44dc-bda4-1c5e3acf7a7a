<template>
  <div class="warehouse-wrapper container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="handleExport">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- content start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #amend="{ row }">
             <div>{{ getAmendName(row.amend) }}</div>
            </template>
            <template #original_count="{ row }">
             <div>{{ row.amend === 0 ? row.total_variation_count : row.original_count }}</div>
            </template>
            <!-- <template #total_variation_count="{ row }">
             <div>{{ row.amend === 0 ? row.original_count : row.total_variation_count }}</div>
            </template> -->
            <template #operation="{ row }">
              <el-button type="text" class="ps-text" size="small" @click="showRemarkDialog(row)" v-if="!row.err_remark ||  row.err_remark.length === 0">备注</el-button>
            </template>
            <template #fujian="{ row }">
              <el-button type="text" class="ps-text" size="small" @click="clickViewerHandler(row)" :disabled="!row.image_json || row.image_json.length === 0">查看</el-button>
            </template>
            <template #operatorList="{ row }">
              <div>{{ getOperatorList(row.operator_list) }}</div>
            </template>
          </table-column>
        </el-table>
        <!-- content end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- end -->
      <!-- 分页 start -->
      <div class="block ps-pagination">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 预览 -->
    <image-viewer  v-model="showViewer" :initial-index="0" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
    <!--异常备注弹窗-->
    <remark-edit-dialog ref="remarkEditDialogRef" :show="isShowRemarkDialog"  :type="remarkDialogType" :title="remarkDialogTitle"  @confirmDialog="confirmRemarkDialogHandle" @closeDialog="closeRemarkDialogHandle"/>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange, divide } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import RemarkEditDialog from '../components/RemarkEditDialog'
import { mapGetters } from 'vuex'

export default {
  name: 'WarehouseAdmin',
  mixins: [exportExcel],
  components: { RemarkEditDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: {
        materials_name: {
          type: 'input',
          value: '',
          label: '物资名称',
          placeholder: '请输入物资名称'
        },
        record_type_alias: {
          type: 'select',
          value: [],
          multiple: true,
          label: '类型',
          placeholder: '请选择类型',
          dataList: [
            {
              label: '采购入库',
              value: 'PURCHASE_ENTRY'
            },
            // {
            //   label: '赠予入库',
            //   value: 'BESTOW_ENTRY'
            // },
            {
              label: '调拨入库',
              value: 'BORROW_ENTRY'
            },
            {
              label: '调拨出库',
              value: 'BORROW_EXIT'
            },
            {
              label: '损耗出库',
              value: 'EXPEND_EXIT'
            },
            {
              label: '过期出库',
              value: 'OVERDUE_EXIT'
            },
            {
              label: '采购退货出库',
              value: 'REFUND_EXIT'
            },
            {
              label: '领料出库',
              value: 'RECEIVE_EXIT'
            },
            {
              label: '归还入库',
              value: 'RETURN_ENTRY'
            },
            {
              label: '归还出库',
              value: 'RETURN_EXIT'
            },
            {
              label: '其他入库',
              value: 'OTHER_ENTRY'
            },
            {
              label: '其他出库',
              value: 'OTHER_EXIT'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '日期',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入单据编号'
        },
        amend: {
          type: 'select',
          label: '是否修改',
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '是',
              value: 1
            },
            {
              label: '否',
              value: 0
            }
          ]
        }
      },
      tableData: [],
      tableSettings: [
        { label: '单据编号', key: 'trade_no' },
        { label: '日期', key: 'operate_time', isComponents: true, type: 'date', format: 'YYYY年MM月DD日  HH:mm:ss' },
        { label: '是否修改', key: 'amend_alias' },
        { label: '类型', key: 'record_type_alias' },
        { label: '所属供应商', key: 'supplier_manage_name' },
        { label: '物资名称', key: 'materials_name' },
        { label: '物资分类', key: 'materail_classification_name' },
        { label: '单价', key: 'operate_price', type: 'moneyFloat', unit: '¥' },
        { label: '最小单位', key: 'unit_name' },
        // { label: '数量', key: 'original_count', type: "slot", slotName: "original_count" },
        { label: '数量', key: 'total_variation_count' },
        { label: '合计成本', key: 'total_price', type: 'moneyFloat', unit: '¥' },
        { label: '附件信息', key: 'fujian', type: 'slot', slotName: 'fujian' },
        { label: '备注', key: 'remark' },
        { label: '异常备注', key: 'err_remark', showTooltip: true, labelClassName: 'ps-red' },
        { label: '领用人', key: 'recipient' },
        { label: '录入人', key: 'account' },
        { label: '审核人', key: 'operator_list', type: 'slot', slotName: 'operatorList' },
        // { label: '当前库存数量', key: 'total_price_alias1' },
        // { label: '出入库图片', key: 'total_price_alias2' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", minWidth: '120px' }
      ],
      collect: [ // 统计
        { key: 'entry_total_price', value: 0, label: '入库合计：', type: 'moneyFloat' },
        { key: 'exit_total_price', value: 0, label: '出库合计：', type: 'moneyFloat' }
      ],
      elementLoadingText: "数据正在加载，请耐心等待...",
      isLoadingCollect: false,
      previewSrcList: [],
      showViewer: false,
      isShowRemarkDialog: false, // 弹窗是否显示
      remarkDialogType: "add", // 弹窗类型
      remarkDialogTitle: "异常备注" // 弹窗标题
    }
  },
  computed: {
    ...mapGetters(['organization'])
  },
  created() {
    // this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getWarehouseList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getWarehouseList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: +this.$route.query.warehouse_id,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpInventoryInfoCostDetailPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data) {
          this.totalCount = res.data.count
          // 处理下后端返回的数据
          this.tableData = res.data.results.map(v => {
            v.operate_price_alias = '￥' + divide(v.operate_price)
            v.total_price_alias = '￥' + divide(v.total_price)
            return v
          })
          this.collect.forEach(item => {
            for (let i in res.data.collect) {
              if (item.key === i) {
                item.value = res.data.collect[i]
              }
            }
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getWarehouseList()
    },
    handleExport() {
      const option = {
        type: 'Costbreakdown',
        url: 'apiBackgroundDrpInventoryInfoCostDetailExportPost',
        params: {
          warehouse_id: +this.$route.query.warehouse_id,
          ...this.formatQueryParams(this.searchFormSetting),
          page: 1,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    },
    // 查看预览图
    clickViewerHandler(row) {
      console.log("clickViewerHandler", row);
      // don't show viewer when preview is false
      let imgList = row.image_json || []
      if (imgList) {
        imgList = Array.isArray(imgList) ? imgList : JSON.parse(imgList)
      }
      this.previewSrcList = imgList
      if (!this.previewSrcList || this.previewSrcList.length === 0) {
        return this.$message.error('暂无图片');
      }
      this.showViewer = true;
    },
    // 关闭图片预览
    closeViewer() {
      this.showViewer = false;
    },
    // 获取审核人
    getOperatorList(list) {
      if (!list || list.length === 0) {
        return '--'
      }
      return list.join('、')
    },
    // 显示备注弹窗
    showRemarkDialog(row) {
      if (this.$refs.remarkEditDialogRef) {
        this.$refs.remarkEditDialogRef.setRemark(row.remark, row.id)
      }
      this.isShowRemarkDialog = true;
    },
    // 确认关闭备注弹窗
    confirmRemarkDialogHandle() {
      this.isShowRemarkDialog = false;
      this.getWarehouseList()
    },
    // 取消备注弹窗
    closeRemarkDialogHandle() {
      this.isShowRemarkDialog = false;
    },
    // 获取修改名称
    getAmendName(flag) {
      return flag === 1 ? '是' : '否'
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
