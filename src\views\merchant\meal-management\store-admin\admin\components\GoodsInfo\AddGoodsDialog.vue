<template>
  <div class="goods-dialog">
    <el-dialog
      :title="dialogTitle"
      :visible.sync="visible"
      width="1250px"
      top="10vh"
      custom-class="ps-dialog"
      show-close
      :close-on-click-modal="false"
    >
      <div class="ps-goods-dialog">
        <el-form ref="formData" v-loading="isLoading" :model="formData" inline label-width="120px" :rules="rules">
          <el-form-item label="商品名称" prop="name">
            <el-input v-model="formData.name" class="ps-input" maxlength="30"></el-input>
          </el-form-item>
          <el-form-item label="分类" prop="goods_category_id">
            <el-select
              v-model="formData.goods_category_id"
              class="ps-select"
              style="width: 180px"
              popper-class="ps-popper-select"
              clearable
              filterable
              placeholder="请选择分类"
            >
              <el-option
                v-for="item in goodsCategoryList"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="供应商" prop="supplier_id">
            <el-select
              v-model="formData.supplier_id"
              class="ps-select"
              style="width: 180px"
              popper-class="ps-popper-select"
              clearable
              filterable
              placeholder="请选择分类"
            >
              <el-option v-for="item in supplierList" :key="item.name" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="一品多码" prop="is_multi_barcode">
            <el-radio-group
              v-model="formData.is_multi_barcode"
              class="ps-radio-btn"
              size="medium"
              :disabled="isRadioDisabledRule()"
              @change="changeMultiBarcode"
            >
              <el-radio-button :label="true">是</el-radio-button>
              <el-radio-button :label="false">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上下架" prop="sale_status">
            <el-radio-group v-model="formData.sale_status" class="ps-radio-btn" size="medium">
              <el-radio-button :label="1">上架</el-radio-button>
              <el-radio-button :label="0">下架</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="条码"
            v-if="!formData.is_multi_spec"
            prop="barcode"
            width="120px"
            class="form-item-block"
            :key="1"
          >
            <div class="extended-barcode ps-flex flex-align-c">
              <el-input placeholder="" v-model="formData.barcode" class="ps-input w-300" maxlength="13">
                <template slot="prepend">主条码</template>
                <el-tooltip
                  class="item"
                  slot="append"
                  effect="dark"
                  content="主条码用于各种操作界面及报表展示"
                  placement="bottom"
                >
                  <el-button icon="el-icon-warning-outline" type="primary"></el-button>
                </el-tooltip>
              </el-input>
              <el-button
                slot="append"
                class="ps-green-btn m-l-10"
                type="primary"
                @click="clickGenerateUniqueID('barcode')"
              >
                生成
              </el-button>
            </div>
          </el-form-item>
          <!-- 一品多码多规格 -->
          <div v-if="formData.is_multi_barcode">
            <el-form-item
              label=" "
              width="120px"
              class="form-item-block"
              v-for="(extendedBarcodeItem, extendedBarcodeIndex) in formData.extendedBarcodeList"
              :key="extendedBarcodeIndex"
              :prop="'extendedBarcodeList.' + extendedBarcodeIndex + '.barcode'"
              :rules="extendedBarcodeRules"
            >
              <div class="extended-barcode ps-flex flex-align-c p-b-10">
                <el-input placeholder="" v-model="extendedBarcodeItem.barcode"  maxlength="13" class="ps-input w-300">
                  <template slot="prepend">扩展条码</template>
                </el-input>
                <div>
                  <el-button
                    slot="append"
                    class="ps-green-btn m-l-10"
                    type="primary"
                    @click="clickGenerateUniqueID('extendedBarcode', extendedBarcodeIndex)"
                  >
                    生成
                  </el-button>
                </div>
                <i
                  class="el-icon-circle-plus-outline p-l-10"
                  style="font-size: 20px"
                  @click="clickAddExtendedBarcode"
                ></i>
                <i
                  class="el-icon-remove-outline p-l-10"
                  style="font-size: 20px"
                  v-if="formData.extendedBarcodeList.length > 1"
                  @click="clickDelectExtendedBarcode(extendedBarcodeIndex)"
                ></i>
              </div>
            </el-form-item>
          </div>
          <el-form-item label="多规格" prop="is_multi_spec">
            <el-radio-group
              class="ps-radio-btn"
              v-model="formData.is_multi_spec"
              size="medium"
              @change="changeMultiSpec"
              :disabled="isRadioDisabledRule()"
            >
              <el-radio-button :label="true">是</el-radio-button>
              <el-radio-button :label="false">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="主规格" class="form-item-block">
            <el-table
              :data="formData.goods_list"
              border
              header-row-class-name="ps-table-header-row"
              class="ps-table"
              :key="formData.is_multi_spec ? 'multi' : 'single'"
            >
              <el-table-column prop="image" label="图片" align="center" width="120px">
                <template slot-scope="scope">
                  <el-upload
                    :ref="`fileUpload${scope.$index}`"
                    :action="'/api/background/file/upload'"
                    :data="uploadParams"
                    :file-list="scope.row.fileLists"
                    :on-success="
                      function (res, file, fileList) {
                        return uploadSuccess(res, file, fileList, scope.row, scope.$index)
                      }
                    "
                    :before-upload="
                      function (file) {
                        return beforeFoodImgUpload(file, scope.row, scope.$index)
                      }
                    "
                    :limit="1"
                    :multiple="false"
                    :show-file-list="false"
                    :headers="headersOpts"
                    v-loading="scope.row.goodsUploadDisabled"
                  >
                    <img
                      v-if="scope.row.goods_image"
                      :src="scope.row.goods_image"
                      class="avatar"
                      @click="clearFileHandle(scope.$index, scope.row)"
                    />
                    <el-button type="text" v-else>上传</el-button>
                  </el-upload>
                </template>
              </el-table-column>
              <el-table-column prop="spec" label="*规格" align="center" width="120px">
                <template slot-scope="scope">
                  <el-form-item label="" :prop="'goods_list.' + scope.$index + '.spec'" :rules="rules.spec">
                    <el-input class="ps-input" size="mini" maxlength="20" v-model="scope.row.spec"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="spec" label="*单位" align="center" width="120px">
                <template slot-scope="scope">
                  <el-form-item label="" :prop="'goods_list.' + scope.$index + '.goods_unit'" :rules="rules.goods_unit">
                    <el-select
                      v-model="scope.row.goods_unit"
                      size="mini"
                      clearable
                      filterable
                      class="ps-select"
                      popper-class="ps-popper-select"
                    >
                      <el-option
                        v-for="item in unitList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="cost_price" label="成本价" align="center" width="120px">
                <template slot-scope="scope">
                  <el-form-item label="" :prop="'goods_list.' + scope.$index + '.cost_price'" :rules="rules.cost_price">
                    <el-input class="ps-input" size="mini" v-model="scope.row.cost_price"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="sales_price" label="*零售价" align="center" width="120px">
                <template slot-scope="scope">
                  <el-form-item
                    label=""
                    :prop="'goods_list.' + scope.$index + '.sales_price'"
                    :rules="rules.sales_price"
                  >
                    <el-input class="ps-input" size="mini" v-model="scope.row.sales_price"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="stock_num" label="库存" align="center" width="120px">
                <template slot-scope="scope">
                  <el-input
                    class="ps-input"
                    size="mini"
                    maxlength="4"
                    :disabled="scope.row.modify"
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                    v-model="scope.row.stock_num"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="barcode" label="*条码" align="center" width="210px" v-if="formData.is_multi_spec">
                <template slot-scope="scope">
                  <div class="ps-flex flex-align-c">
                    <!-- :disabled="scope.row.modify" -->
                    <!-- v-if="!scope.row.modify" -->
                    <el-form-item label="" :prop="'goods_list.' + scope.$index + '.barcode'" :rules="rules.specBarcode">
                      <el-input v-model="scope.row.barcode" class="ps-input" size="mini" maxlength="13"></el-input>
                    </el-form-item>
                    <el-button
                      slot="append"
                      size="mini"
                      class="ps-green-btn m-l-10"
                      type="primary"
                      @click="clickGenerateUniqueID('multiSpec', scope.$index)"
                    >
                      生成
                    </el-button>
                  </div>
                </template>
                <!-- barcode -->
              </el-table-column>
              <el-table-column label="操作" align="center" fixed="right" width="120px" v-if="formData.is_multi_spec">
                <template slot-scope="scope">
                  <el-button type="text" size="small" class="ps-text" @click="clickAddGoods">新增</el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="ps-warn"
                    @click="clickDelectGoods(scope.$index)"
                    v-if="
                      (type === 'add' && formData.goods_list.length > 1) ||
                      (type === 'modify' && (scope.row.stock_num <= 0) & (formData.goods_list.length > 1))
                    "
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="canceDialogHandle">取 消</el-button>
        <el-button class="ps-btn" type="primary" v-loading="isLoading" @click="clickDetermineDialog">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import NP from 'number-precision'
import { to, generateUniqueID, getToken, getSuffix } from '@/utils/index'
import { positiveMoney } from '@/utils/validata'

export default {
  props: {
    isshow: Boolean,
    type: {
      type: String,
      default: ''
    },
    dialogModifyId: {
      type: Number
    },
    dialogTitle: {
      type: String,
      default: '新增商品'
    },
    goodsCategoryId: {
      type: String,
      default: ''
    },
    confirm: Function
  },
  data() {
    let validateBarcode = (rule, value, callback) => {
      let regPass = /^[a-zA-Z0-9_-]+$/
      // 扩展条码不需要必填 如果填了就要校验
      if (rule.field === 'barcode' && !value) {
        return callback(new Error('商品条码必填'))
      } else if (rule.field === 'barcode' && value.length < 13) {
        return callback(new Error('条码长度不能少于13位'))
      } else if (value && !regPass.test(value)) {
        callback(new Error("条码由：数字、字母、'_'、'-'组成"))
      } else if (value && value.length < 13) {
        return callback(new Error('条码长度不能少于13位'))
      } else {
        callback()
      }
    }
    let validateSpecBarcode = (rule, value, callback) => {
      let regPass = /^[a-zA-Z0-9_-]+$/
      if (!value) {
        return callback(new Error('请输入条码'))
      } else if (value.length < 13) {
        return callback(new Error('条码长度不能少于13位'))
      } else if (!regPass.test(value)) {
        callback(new Error("条码由：数字、字母、'_'、'-'组成"))
      } else {
        callback()
      }
    }
    let validataCostPrice = (rule, value, callback) => {
      if ((!positiveMoney(value) || Number(value) >= 10000) && value) {
        callback(new Error('格式错误'))
      } else {
        callback()
      }
    }
    let validataSalesPrice = (rule, value, callback) => {
      if (!positiveMoney(value) || Number(value) >= 10000) {
        callback(new Error('格式错误'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: ['blur', 'change'] }],
        goods_category_id: [{ required: true, message: '请选择分类', trigger: ['blur', 'change'] }],
        // supplier_id: [{ required: true, message: '请选择供应商', trigger: ['blur', 'change'] }],
        barcode: [
          {
            required: true,
            validator: validateBarcode,
            trigger: ['blur', 'change']
          }
        ],
        spec: [{ required: true, message: '请输入规格', trigger: ['blur', 'change'] }],
        goods_unit: [{ required: true, message: '请选择单位', trigger: ['blur', 'change'] }],
        cost_price: [
          { required: false, validator: validataCostPrice, message: '请输入成本价', trigger: ['blur', 'change'] }
        ],
        sales_price: [
          { required: true, validator: validataSalesPrice, message: '请输入零售价', trigger: ['blur', 'change'] }
        ],
        specBarcode: [{ required: true, validator: validateSpecBarcode, trigger: ['blur', 'change'] }]
      },
      goodsCategoryList: [],
      supplierList: [],
      unitList: [],
      extendedBarcodeRules: {
        validator: validateBarcode,
        trigger: ['blur', 'change']
      },
      formData: {
        name: '', // 商品名字
        goods_category_id: '', // 分类id
        supplier_id: '', // 供应商id
        is_multi_barcode: null, // 一品多码
        barcode: '', // 主条码
        extendedBarcodeList: [
          {
            barcode: ''
          }
        ], // 扩展条码
        sale_status: null, // 上下架状态
        is_multi_spec: null, // 是否多规格
        // 多规格情况下的数据
        goods_list: [
          {
            goodsUploadDisabled: false,
            fileLists: [
              // {
              //   name : "woman.png",
              //   status:"success",
              //   uid:1719905152945,
              //   url:"https://cashier-v4-dev.packertec.com/cashier_v4/goods/png/20240702/goods171990515281816e9ead6c7f55a2.png?_tk=c3d5d71ff0b14a3234bd1e8f5b575867"
              // }
            ],
            goods_image: '',
            spec: '',
            goods_unit: null,
            cost_price: '',
            sales_price: '',
            stock_num: '',
            barcode: ''
          }
        ]
      },
      goodsDetails: {}, // 接口返回的详情
      headersOpts: {
        TOKEN: getToken()
      },
      uploadParams: {
        prefix: 'goods'
      }
    }
  },
  computed: {
    visible: {
      get() {
        console.log(this.isshow, 6666666)
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  async created() {
    this.getGoodsUnitList()
    this.getFoodIngredientSupplierList()
    this.getGoodsCategoryList()
    if (this.type === 'modify') {
      this.getGoodsDetails()
    } else {
      this.initAddGoodsData()
    }
  },
  mounted() {},
  methods: {
    initAddGoodsData() {
      this.formData = {
        name: '', // 商品名字
        goods_category_id: '', // 分类id
        supplier_id: '', // 供应商id
        is_multi_barcode: false, // 一品多码
        barcode: '', // 主条码
        extendedBarcodeList: [
          {
            barcode: ''
          }
        ], // 扩展条码
        sale_status: 1, // 上下架状态
        is_multi_spec: true, // 是否多规格
        // 多规格情况下的数据
        goods_list: [
          {
            goodsUploadDisabled: false,
            fileLists: [
              // {
              //     name : "woman.png",
              //     status:"success",
              //     uid:1719905152945,
              //     url:"https://cashier-v4-dev.packertec.com/cashier_v4/goods/png/20240702/goods171990515281816e9ead6c7f55a2.png?_tk=c3d5d71ff0b14a3234bd1e8f5b575867"
              //   }
            ],
            goods_image: '',
            spec: '',
            goods_unit: null,
            cost_price: '',
            sales_price: '',
            stock_num: '',
            barcode: ''
          }
        ]
      }
    },
    // 单位
    async getGoodsUnitList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsUnitListPost({
          page: 1,
          page_size: 99999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.unitList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getFoodIngredientSupplierList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundFoodIngredientSupplierListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        // 传给弹窗
        this.supplierList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getGoodsCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategoryListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 传给弹窗
        this.goodsCategoryList = res.data.results
        // 商品分类传进来
        if (this.goodsCategoryId) {
          this.formData.goods_category_id = this.goodsCategoryId
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取详情
    async getGoodsDetails() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsDetailsPost({
          id: this.dialogModifyId
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.goodsDetails = res.data
        let details = res.data
        // fileLists: [
        //     // {
        //     //     name : "woman.png",
        //     //     status:"success",
        //     //     uid:1719905152945,
        //     //     url:"https://cashier-v4-dev.packertec.com/cashier_v4/goods/png/20240702/goods171990515281816e9ead6c7f55a2.png?_tk=c3d5d71ff0b14a3234bd1e8f5b575867"
        //     //   }
        //     ],
        //     goods_image: '',
        let formDetails = {
          name: details.name, // 商品名字
          goods_category_id: String(details.goods_category_id), // 分类id
          supplier_id: details.supplier_id, // 供应商id
          is_multi_barcode: details.is_multi_barcode, // 一品多码
          barcode: details.barcode, // 主条码
          extendedBarcodeList: [
            {
              barcode: ''
            }
          ], // 扩展条码
          sale_status: details.sale_status, // 上下架状态
          is_multi_spec: details.is_multi_spec, // 是否多规格
          // 多规格情况下的数据
          goods_list: [
            {
              goodsUploadDisabled: false,
              fileLists: [],
              goods_image: '',
              spec: '',
              goods_unit: null,
              cost_price: '',
              sales_price: '',
              stock_num: '',
              barcode: ''
            }
          ]
        }
        // 赋值
        if (details.extended_barcode.length) {
          formDetails.extendedBarcodeList = details.extended_barcode.map(v => {
            return { barcode: v, modify: true }
          })
        } else {
          formDetails.extendedBarcodeList = [
            {
              barcode: ''
            }
          ]
        }
        if (details.goods_list.length) {
          formDetails.goods_list = details.goods_list.map(v => {
            v.cost_price = NP.divide(v.cost_price, 100)
            v.sales_price = NP.divide(v.sales_price, 100)
            v.modify = true
            v.goodsUploadDisabled = false
            if (v.goods_image) {
              v.fileLists = [
                {
                  uid: new Date().getTime() + Math.floor(Math.random() * 150),
                  url: v.goods_image ? v.goods_image : ''
                }
              ]
            }
            return v
          })
          console.log(formDetails.goods_list)
        } else {
          formDetails.goods_list = [
            {
              goodsUploadDisabled: false,
              fileLists: [],
              goods_image: '',
              spec: '',
              goods_unit: null,
              cost_price: '',
              sales_price: '',
              stock_num: '',
              barcode: ''
            }
          ]
        }
        this.formData = formDetails
      } else {
        this.$message.error(res.msg)
      }
    },
    canceDialogHandle() {
      this.visible = false
    },
    clickDetermineDialog() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          // let reg = /^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/
          let barcodeReg = /^[a-zA-Z0-9_-]+$/
          let params = {
            name: this.formData.name, // 商品名字
            goods_category_id: this.formData.goods_category_id, // 分类id
            is_multi_barcode: this.formData.is_multi_barcode, // 一品多码
            // barcode:this.formData.is_multi_spe?  this.formData.barcode, // 主条码
            extended_barcode: [],
            sale_status: this.formData.sale_status, // 上下架状态
            is_multi_spec: this.formData.is_multi_spec, // 是否多规格
            goods_list: []
          }
          if (this.formData.supplier_id) {
            params.supplier_id = this.formData.supplier_id // 供应商id
          }
          if (this.formData.is_multi_barcode) {
            if (this.formData.extendedBarcodeList.length) {
              if (this.formData.extendedBarcodeList.some(item => item.barcode === this.formData.barcode)) {
                return this.$message.error('条码有相同,请修改')
              }
              // 判断是否相同
              const barcodeSet = new Set()
              for (let i = 0; i < this.formData.extendedBarcodeList.length; i++) {
                if (barcodeSet.has(this.formData.extendedBarcodeList[i].barcode)) {
                  return this.$message.error('条码有相同,请修改')
                }
                barcodeSet.add(this.formData.extendedBarcodeList[i].barcode)
                if (this.formData.extendedBarcodeList[i].barcode) {
                  params.extended_barcode.push(this.formData.extendedBarcodeList[i].barcode)
                }
              }
            }
          }
          if (this.formData.goods_list.length) {
            const goodsBarcodeSet = new Set()
            for (let index = 0; index < this.formData.goods_list.length; index++) {
              const element = this.formData.goods_list[index]
              if (this.formData.is_multi_spec && !element.barcode) {
                return this.$message.error(`多规格下的第${index + 1}条，请输入条码`)
              } else if (this.formData.is_multi_spec && !barcodeReg.test(element.barcode)) {
                return this.$message.error(`多规格下的第${index + 1}条，条码由：数字、字母、'_'、'-'组成`)
              }
              // 判断是否相同
              if (goodsBarcodeSet.has(element.barcode)) {
                return this.$message.error('条码有相同,请修改')
              }
              goodsBarcodeSet.add(element.barcode)
              let multiSpecParams = {
                goods_image: element.goods_image ? element.goods_image : '',
                spec: element.spec,
                goods_unit: element.goods_unit,
                cost_price: NP.times(element.cost_price, 100),
                sales_price: NP.times(element.sales_price, 100),
                stock_num: element.stock_num ? Number(element.stock_num) : 0,
                barcode: this.formData.is_multi_spec ? element.barcode : this.formData.barcode
              }
              // 如果是编辑 他数据里面有id 需要传
              if (this.type === 'modify' && element.id) {
                multiSpecParams.id = element.id
              }
              params.goods_list.push(multiSpecParams)
            }
          }
          // 多规格情况下 就拿主规格下的第一个
          if (this.formData.is_multi_spec) {
            if (params.goods_list.length) {
              params.barcode = params.goods_list[0].barcode
            }
          } else {
            params.barcode = this.formData.barcode
          }
          this.setGoodsAddOrModify(params)
        }
      })
    },
    async setGoodsAddOrModify(params) {
      this.isLoading = true
      let [err, res] = ''
      if (this.type === 'modify') {
        ;[err, res] = await to(this.$apis.apiBackgroundStoreGoodsModifyPost({ id: this.dialogModifyId, ...params }))
      } else {
        ;[err, res] = await to(this.$apis.apiBackgroundStoreGoodsAddPost(params))
      }
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.visible = false
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickGenerateUniqueID(type, index) {
      let generateUnique = generateUniqueID()
      switch (type) {
        case 'barcode':
          this.formData.barcode = generateUnique
          break
        case 'extendedBarcode':
          this.formData.extendedBarcodeList[index].barcode = generateUnique
          break
        case 'multiSpec':
          this.formData.goods_list[index].barcode = generateUnique
          break

        default:
          break
      }
    },
    // 选择一品多码
    changeMultiBarcode(row) {
      this.formData.goods_list = [
        {
          goodsUploadDisabled: false,
          fileLists: [],
          goods_image: '',
          spec: '',
          goods_unit: null,
          cost_price: '',
          sales_price: '',
          stock_num: '',
          barcode: ''
        }
      ]
      if (row) {
        this.formData.is_multi_spec = false
      }
      // 如果一品多码选择了是 然后多规格就是否
      console.log(row, 77777)
      this.initBarcodeMultiSpec()
    },
    // 选择多规格
    changeMultiSpec(row) {
      // 1.一品多码 选择是 多规格不能 是 同理 多规格也是
      // 2.一品多码选择否 多规格也可以否 需要显示主条码 不显示矿展条码
      // 如果多规格选择了是 然后一品多码就是否
      this.formData.goods_list = [
        {
          goodsUploadDisabled: false,
          fileLists: [],
          goods_image: '',
          spec: '',
          goods_unit: null,
          cost_price: '',
          sales_price: '',
          stock_num: '',
          barcode: ''
        }
      ]
      if (row) {
        this.formData.is_multi_barcode = false
      }
      this.initBarcodeMultiSpec()
    },
    // 初始化条码和多规格
    initBarcodeMultiSpec() {
      // if (this.type === 'modify') {
      //   if (this.goodsDetails.is_multi_spec === this.formData.is_multi_spec) {
      //     this.formData.goods_list = this.goodsDetails.goods_list.map(v => {
      //       v.cost_price = NP.divide(v.cost_price, 100)
      //       v.sales_price = NP.divide(v.sales_price, 100)
      //       v.modify = true
      //       v.goodsUploadDisabled = false
      //       if (v.goods_image) {
      //         v.fileLists = [
      //           {
      //             uid: new Date().getTime() + Math.floor(Math.random() * 150),
      //             url: v.goods_image
      //           }
      //         ]
      //       }
      //       return v
      //     })
      //   } else {
      //     this.formData.goods_list = [
      //       {
      //         goodsUploadDisabled: false,
      //         fileLists: [],
      //         goods_image: '',
      //         spec: '',
      //         goods_unit: null,
      //         cost_price: '',
      //         sales_price: '',
      //         stock_num: '',
      //         barcode: ''
      //       }
      //     ]
      //   }
      // }
      // this.formData.barcode = ''
      this.$nextTick(_ => {
        this.$refs.formData.clearValidate(['barcode'])
      })
    },
    clickAddExtendedBarcode() {
      this.formData.extendedBarcodeList.push({ barcode: '' })
    },
    clickDelectExtendedBarcode(index) {
      this.formData.extendedBarcodeList.splice(index, 1)
    },
    clickAddGoods() {
      this.formData.goods_list.push({
        goodsUploadDisabled: false,
        fileLists: [],
        goods_image: '',
        spec: '',
        goods_unit: null,
        cost_price: '',
        sales_price: '',
        stock_num: '',
        barcode: ''
      })
    },
    clickDelectGoods(index) {
      this.formData.goods_list.splice(index, 1)
    },
    clearFileHandle(index, row) {
      row.fileLists = []
      row.goodsUploadDisabled = true
      this.$refs['fileUpload' + index].clearFiles()
    },
    // 图片上传前检测
    beforeFoodImgUpload(file, row, index) {
      row.goodsUploadDisabled = true
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 < 1
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 1MB!')
        return false
      }
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList, row, index) {
      if (res && res.code === 0) {
        this.formData.goods_list[index].fileLists = fileList
        this.formData.goods_list[index].goods_image = res.data.public_url
      } else {
        this.$message.error(res.msg)
      }
      row.goodsUploadDisabled = false
    },
    // 一品多码
    isRadioDisabledRule() {
      let status = false
      if (this.type === 'modify') {
        if (this.goodsDetails.is_multi_barcode || this.goodsDetails.is_multi_spec) {
          status = true
        }
      }
      return status
    }
  }
}
</script>

<style lang="scss" scoped>
.goods-dialog {
  .ps-goods-dialog {
    max-height: calc(100vh - 40vh);
    overflow-y: auto;
  }
  ::v-deep .el-radio-button__inner {
    width: 90px;
  }
  .item-multi-barcode {
    // margin-bottom: 15px !important;
  }
  .form-item-block {
    display: block;
  }
  .add-multi_barcode {
    color: #fd9a46;
    cursor: pointer;
  }
  .extended-barcode {
    width: 450px;
    .el-icon-circle-plus-outline {
      color: #fd9a46;
    }
    .el-icon-remove-outline {
      color: #fd9a46;
    }
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar {
    width: 60px;
    height: 60px;
    display: block;
  }
}
</style>
