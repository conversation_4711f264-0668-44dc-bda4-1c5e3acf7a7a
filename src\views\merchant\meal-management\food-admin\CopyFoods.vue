<template>
  <div class="ingredients-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="110px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表
          <span style="color: red; font-size: 13px;margin-left: 10px;">注：勾选菜品可快速导出编辑和同步，缺失的食材会自动同步</span>
        </div>
        <button-icon color="origin" type="add" @click="addAndEditMealFood('add')">
            新建菜品
          </button-icon>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="clickExportHandle('export')">
            导出编辑
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <el-table-column prop="" label="图片" align="center" width="200px">
            <template slot-scope="scope">
              <el-image
                style="width: 150px; height: 100px"
                :src="scope.row.image"
                fit="contain"
                v-if="scope.row.image"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="名称" align="center"></el-table-column>
          <el-table-column prop="all_alias_name" label="菜品 / 商品别名" align="center" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="attributes" label="属性" align="center">
            <template slot-scope="scope">{{scope.row.attributes === 'goods' ? '商品' : '菜品'}}</template>
          </el-table-column>
          <el-table-column prop="" label="食材" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickShowDialogIngredients(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="name1" label="营养信息" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickShowDialogNutrition(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="" label="口味" align="center" width="150px">
            <template slot-scope="scope">
              <div class="tast-wrapper">
                <el-tag v-for="(item, index) in scope.row.taste_list" :key="index">
                  {{ item.name }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="is_copy" label="是否已复制" align="center">
            <template slot-scope="scope">
              {{ scope.row.is_copy ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="copySystemFoods('single', scope.row.id)"
              >
                复制
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- tree -->
        <!-- table end -->
      </div>
    </div>
    <!-- 表中营养信息 -->
    <el-dialog
      title="营养信息"
      :visible.sync="showDialogNutrition"
      width="700px"
      custom-class="ps-dialog"
    >
      <nutrition-data :tableDataNutrition="tableDataNutrition" readonly />
    </el-dialog>
    <el-dialog
      title="食材组成"
      :visible.sync="showDialogIngredients"
      width="50%"
      custom-class="ps-dialog"
    >
      <el-table :data="tableDataIngredients" style="width: 100%">
        <el-table-column prop="ingredient_name" label="食材名称" align="center"></el-table-column>
        <el-table-column prop="ingredient_scale" label="占比" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.ingredient_scale }}%</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 新增 -->
    <add-and-edit-mealFood
      v-if="showDialogMealFood"
      :showDialogMealFood.sync="showDialogMealFood"
      :showDialogMealFoodType="showDialogMealFoodType"
      :formFoodDataDialog="formFoodDataDialog"
      :foodDialogTitle="foodDialogTitle"
      :foodCategoryList="foodCategoryDataList"
      :selectListId="selectListId"
      :confirm="confirmHandle"
    />
    <!-- 添加菜品导出弹窗 -->
    <dialog-message width="415px" :title="dialogTitle" :show.sync="showDialog">
      <div v-if="dialogType==='exportOther'" class="import-food" v-loading="dialogLoading">
        <div>其他商户还添加了以下菜品，勾选导出后可快速编辑，需补充价格信息，导入编辑成功后可使用。</div>
        <ul class="food-box m-t-10 m-b-20">
          <el-checkbox-group v-model="selectImportFood" class="font-size-14" @change="changeSelectFoodHandle" :disabled="dialogLoading">
            <li v-for="food in otherImportFoodList" :key="food.id" class="food-item ps-flex-bw">
              <div class="">{{ food.name }}</div>
              <div>
                <el-checkbox class="ps-checkbox" :label="food.id"></el-checkbox>
              </div>
            </li>
          </el-checkbox-group>
        </ul>
        <div class="total ps-flex-bw m-b-20">
          <div>已选<span class="origin m-l-10">{{ this.selectImportFood.length }}</span></div>
          <div><span class="m-r-10">全选</span><el-checkbox v-model="selectAll" @change="selectAllHandle" :disabled="dialogLoading" class="ps-checkbox"></el-checkbox></div>
        </div>
      </div>
      <div v-else class="import-food m-b-50" v-loading="dialogLoading">
        确认导出勾选的菜品时会同步该菜品到菜品库，编辑完成后可导入编辑完成菜品的应用。
      </div>
      <div slot="tool" class="text-center">
        <el-button v-if="dialogType==='export'" :disabled="dialogLoading" class="ps-cancel-btn w-110" style="margin-right: 20px;" @click="clickCancleHandle">取消</el-button>
        <el-button :disabled="dialogLoading" class="ps-btn w-110" type="primary" style="margin-right: 20px;" @click="clickConfirmHandle">同步并导出</el-button>
        <el-button v-if="dialogType==='exportOther'" :disabled="dialogLoading" class="ps-cancel-btn w-110" @click="clickCancleHandle">我知道了</el-button>
      </div>
    </dialog-message>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import { confirm } from '@/utils/message'
import nutritionData from '../components/mealFoodList/NutritionTable.vue'
import { NUTRITION_LIST } from './constants'
import addAndEditMealFood from '../components/mealFoodList/AddAndEditMealFoodDialog' // 弹框用的新增或者编辑

export default {
  name: 'CopyIngredients',
  props: {},
  components: { nutritionData, addAndEditMealFood },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        food_name: {
          type: 'input',
          label: '菜品 / 商品名称',
          value: '',
          placeholder: '请输入'
        },
        // category_id: {
        //   type: 'select',
        //   label: '分类',
        //   value: '',
        //   listNameKey: 'name',
        //   listValueKey: 'id',
        //   placeholder: '请选择分类',
        //   dataList: []
        // },
        attributes: {
          type: 'select',
          label: '属性',
          value: 'foods',
          placeholder: '请选择',
          collapseTags: true,
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '商品',
              value: 'goods'
            },
            {
              label: '菜品',
              value: 'foods'
            }
          ]
        },
        is_copy: {
          type: 'select',
          label: '是否已复制',
          value: 'not_copy',
          placeholder: '请选择',
          collapseTags: true,
          dataList: [{
            label: '全部',
            value: 'all'
          }, {
            label: '是',
            value: 'finish_copy'
          }, {
            label: '否',
            value: 'not_copy'
          }]
        }
      },
      selectListId: [], // 多选选中的id
      copyModel: '',
      showDialogNutrition: false,
      tableDataNutrition: {},
      organizationDisabled: false,
      structureType: '',
      dialogDataRow: {},
      showDialogIngredients: false,
      tableDataIngredients: [],
      // 添加菜品弹窗数据 start
      showDialogMealFood: false,
      showDialogMealFoodType: 'add',
      showFoodDiscountDialog: false,
      showDiscountDialogType: '',
      formFoodDataDialog: {},
      foodDialogTitle: '',
      foodCategoryDataList: [],
      // end
      dialogType: '', // 弹窗类型export导出编辑，exportOther 导出其它用户导入的数据
      dialogTitle: '保存成功',
      dialogLoading: false,
      showDialog: false,
      otherImportFoodList: [],
      selectImportFood: [],
      selectAll: false
    }
  },
  created() {
    // this.initLoad()
    // this.getIngredientSortList()
    this.foodFoodCategoryList()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSystemFoodList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.getSystemFoodList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.getSystemFoodList()
    },
    // 分类列表
    async getIngredientSortList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSortListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.category_id.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 二级列表
    async foodFoodCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodCategoryListPost({
          page: 1,
          page_size: 9999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodCategoryDataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 系统食材库列表
    async getSystemFoodList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodStockListPost({
          ...this.formatQueryParams(this.searchFormSetting)
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        this.tableData = []
        return
      }
      console.log('res', res)
      if (res.code === 0) {
        this.tableData = res.data.results.map(item => {
          if (item.alias_name) {
            item.all_alias_name = item.alias_name.join(',')
          } else {
            item.alias_name = []
          }
          return item
        })
      } else {
        this.$message.error(res.msg)
        this.tableData = []
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      val.map(item => {
        this.selectListId.push(item.id)
      })
    },
    async copySystemFoods(type, id, model) {
      let ids = []
      if (type === 'multi') {
        ids = this.selectListId
      } else {
        ids = [id]
      }
      let params = {
        ids: ids
      }
      if (model) {
        params.copy_model = model
      }
      if (!ids.length) return this.$message.error('请先选择复制的数据！')
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodStockDistributePost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('res', res)
      if (res.code === 0) {
        this.selectListId = []
        this.$message.success(res.msg)
        this.getSystemFoodList()
      } else if (res.code === 2) {
        let htmlEl = ''
        if (res.data.same_food && res.data.same_food.length) {
          htmlEl += '<div style="text-align:left;font-size: 16px;color:#080808;">菜品重名</div>'
          htmlEl += `<div style="text-align:left;">${res.data.same_food.join('&nbsp&nbsp')}</div>`
        }
        if (res.data.same_ingredient && res.data.same_ingredient.length) {
          htmlEl += '<div style="margin-top:10px;text-align:left;font-size: 16px;color:#080808;">食材重名</div>'
          htmlEl += `<div style="text-align:left;">${res.data.same_ingredient.join('&nbsp&nbsp')}</div>`
        }
        confirm({ useHTML: true, content: htmlEl, confirmButtonText: '覆盖', cancelButtonText: '去重保存' }).then(_ => {
          this.copySystemFoods(type, id, 'cover')
        }).catch(e => {
          if (e === 'cancel') {
            this.copySystemFoods(type, id, 'deduplication')
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    clickShowDialogIngredients(row) {
      this.showDialogIngredients = true
      this.tableDataIngredients = row.ingredients_list
    },
    clickShowDialogNutrition(row) {
      this.showDialogNutrition = true
      this.tableDataNutrition = {}
      if (!row.nutrition_info) row.nutrition_info = {}
      let element = row.nutrition_info.element ? JSON.parse(row.nutrition_info.element) : {}
      let vitamin = row.nutrition_info.vitamin ? JSON.parse(row.nutrition_info.vitamin) : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(this.tableDataNutrition, nutrition.key, row.nutrition_info[nutrition.key] ? row.nutrition_info[nutrition.key] : 0)
        }
        if (nutrition.type === 'element') {
          this.$set(this.tableDataNutrition, nutrition.key, element[nutrition.key] ? element[nutrition.key] : 0)
        }
        if (nutrition.type === 'vitamin') {
          this.$set(this.tableDataNutrition, nutrition.key, vitamin[nutrition.key] ? vitamin[nutrition.key] : 0)
        }
      })
    },
    addAndEditMealFood(type, row) {
      if (type === 'add') {
        this.foodDialogTitle = '新增'
      }
      this.formFoodDataDialog = row
      this.showDialogMealFoodType = type
      this.showDialogMealFood = true
    },
    // 新增完成回调
    async confirmHandle() {
      return // 产品说暂时不需要哦
      this.dialogType = 'exportOther'
      this.dialogTitle = '保存成功'
      this.dialogLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodFoodRecommendPost()
      )
      this.dialogLoading = false
      if (err) {
        // this.$message.error(err.message)
        return
      }
      console.log('res', res)
      if (res.code === 0) {
        this.otherImportFoodList = res.data.results
        this.showDialog = true
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 弹窗菜品单选事件
    changeSelectFoodHandle(e) {
      console.log(e)
      // 当选中的数量和菜品数量一致时全选设为true
      if (e.length === this.otherImportFoodList.length) {
        this.selectAll = true
      } else {
        this.selectAll = false
      }
    },
    // 弹窗全选事件
    selectAllHandle(e) {
      if (e) {
        this.selectImportFood = this.otherImportFoodList.map(v => {
          return v.id
        })
      } else {
        this.selectImportFood = []
      }
    },
    // 弹窗确定
    clickConfirmHandle() {
      if (this.dialogType === 'exportOther') {
        if (!this.selectImportFood.length) return this.$message.error('请先选择数据！')
        this.exportFoodHandle(this.selectImportFood)
      } else {
        this.exportFoodHandle(this.selectListId)
      }
    },
    clickCancleHandle() {
      this.showDialog = false
      this.dialogLoading = false
      this.selectImportFood = []
      this.otherImportFoodList = []
      this.dialogType = ''
    },
    // 导出编辑点击事件
    clickExportHandle(type) {
      this.dialogTitle = '确认导出编辑'
      this.dialogType = type
      if (!this.selectListId.length) return this.$message.error('请先选择数据！')
      this.showDialog = true
      // this.exportFoodHandle(this.selectListId)
    },
    // 导出并编辑接口
    async exportFoodHandle(ids) {
      if (this.dialogLoading) return
      this.dialogLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodStockListExportPost({
          export: true, // 跟后端约定好的需要传个识别字段
          ids: ids
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('res', res)
      if (res.code === 0) {
        this.$router.push({
          name: 'Excel',
          params: {
            random: new Date().getTime()
          },
          query: {
            type: 'CopyFoods',
            query_id: res.data.query_id
          }
        })
        this.showDialog = false
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss">
.import-food{
  .el-checkbox__label{
    display: none;
  }
  ul,li{
    list-style: none;
  }
  .font-size-14.el-checkbox-group{
    font-size: 14px !important;
  }
  .food-box{
    padding: 20px;
    max-height: 280px;
    overflow-y: auto;
    background-color: rgba(242, 242, 242, 1);
    .food-item{
      &:not(:last-child){
        margin-bottom: 12px;
      }
    }
  }
  .w-110{
    width: 110px;
  }
}
</style>
