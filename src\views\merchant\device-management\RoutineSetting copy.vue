<template>
  <div class="RoutineSetting container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper" style="margin-top: 0px;margin-bottom: 50px;">
      <div class="table-header" style="display: flex; justify-content: space-between;">
        <div style="display: flex;align-items: center;">
          <div class="table-title">消费点：</div>
          <!-- <tree-select
            :multiple="true"
            :options="organizationList"
            :flat="true"
            :limit="1"
            :limitText="count => '+' + count"
            :default-expand-level="1"
            :normalizer="normalizer"
            placeholder="请选择"
            v-model="organizationIds"
            :appendToBody="true"
            @select="selectOrg"
            @deselect="selectOrg"
          >
          </tree-select> -->
          <consume-select
            class="ps-input"
            v-model="organizationIds"
            placeholder="请选择"
            :multiple="true"
            @change="changeOrg"
            :collapse-tags="true"
            ></consume-select>
        </div>
        <div style="padding-right:20px;">
          <el-button size="small" type="primary" class="ps-origin-btn" @click="checkForm">保存</el-button>
        </div>
      </div>
      <div>
        <el-form
          :model="settingForm"
          :rules="settingFormRules"
          ref="settingForm"
          label-width="170px"
        >
          <el-form-item label="绑定额度限制：">
            <el-radio-group class="ps-radio" v-model="settingForm.limit" style="margin-right:30px;">
              <el-radio label="noLimit">无限制</el-radio>
              <el-radio label="quotaLimit">额度限制</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="form-quota" v-if="settingForm.limit==='quotaLimit'" prop="quota">
            金额大于等于<el-input v-model="settingForm.quota" class="margin-input w-180 ps-input"></el-input>元，才能绑定托盘
          </el-form-item>
          <div>
            <el-form-item class="form-weight" label="菜品余量不足提示：" prop="notEnough">
              设备菜品余量低于<el-input v-model="settingForm.notEnough" class="margin-input w-180 ps-input"></el-input>克，提示菜品余量不足
            </el-form-item>
          </div>
          <div>
            <el-form-item class="form-weight" label="菜品售罄提示：" prop="sellOut">
              设备菜品余量低于<el-input v-model="settingForm.sellOut" class="margin-input w-180 ps-input"></el-input>克，提示菜品售罄
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { times, divide } from '@/utils'
import ConsumeSelect from '@/components/ConsumeSelect'

export default {
  name: 'RoutineSetting',
  components: { ConsumeSelect },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    let validMoney = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('金额不能为空'))
      } else {
        let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      }
    }
    let validWeight = (rule, value, callback) => {
      if (value) {
        let reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/
        if (!reg.test(value)) {
          callback(new Error('菜品余量格式有误'))
        }
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      organizationList: [],
      organizationIds: [],
      settingForm: {
        limit: 'noLimit',
        quota: '',
        notEnough: '',
        sellOut: ''
      },
      settingFormRules: {
        quota: [{ required: true, validator: validMoney, trigger: 'blur' }],
        notEnough: [{ validator: validWeight, trigger: 'blur' }],
        sellOut: [{ validator: validWeight, trigger: 'blur' }]
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getOrganizationTreeList()
    },
    // 刷新页面
    refreshHandle() {
    },
    checkForm() {
      this.$refs.settingForm.validate(valid => {
        if (valid) {
          this.saveRoutineSetting()
        } else {
          return false
        }
      })
    },
    async saveRoutineSetting() {
      let params = {
        stall_nos: this.organizationIds,
        limit_fee: this.settingForm.limit === 'quotaLimit' ? times(this.settingForm.quota) : null
      }
      if (this.settingForm.notEnough) {
        params.warn_weight = this.settingForm.notEnough
        params.out_weight = this.settingForm.sellOut
      }
      const res = await this.$apis.apiBackgroundDeviceBuffetAddPost(params)
      if (res.code === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    async getSettingDetail() {
      const res = await this.$apis.apiBackgroundDeviceBuffetDetailsPost({
        stall_no: this.organizationIds[0]
      })
      if (res.code === 0) {
        if (res.data) {
          this.settingForm.limit = res.data.limit_fee === null ? 'noLimit' : 'quotaLimit'
          this.settingForm.quota = res.data.limit_fee === null ? '' : divide(res.data.limit_fee)
          this.settingForm.notEnough = res.data.warn_weight
          this.settingForm.sellOut = res.data.out_weight
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    resetForm() {
      this.settingForm.limit = 'noLimit'
      this.settingForm.quota = ''
      this.settingForm.notEnough = ''
      this.settingForm.sellOut = ''
    },
    changeOrg() {
      if (this.organizationIds.length === 1) {
        this.getSettingDetail()
      } else {
        this.resetForm()
      }
    },
    // 获取组织结构tree表
    async getOrganizationTreeList() {
      const res = await this.$apis.apiBackgroundOrganizationOrganizationTreeListPost()
      if (res.code === 0) {
        this.organizationList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children_list
      }
    }
  }
}
</script>

<style lang="scss">
.RoutineSetting{
  .vue-treeselect{
    display: inline-block;
    width: 250px;
  }
  .margin-input{
    margin: 0 10px;
  }
  .form-weight .el-form-item__error{
    margin-left: 125px!important;
  }
  .form-quota .el-form-item__error{
    margin-left: 96px!important;
  }
}
</style>
