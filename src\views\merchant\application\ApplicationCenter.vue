<template>
  <div style="height: 100%">
    <el-row :gutter="20">
      <div class="application-center p-t-20">
        <el-col :span="19">
          <div class="application-center-left">
            <div class="application-center-left-top bg-style p-20 m-b-20">
              <div class="application-center-left-title flex-b-c m-b-30">
                <div class="flex-center">
                  <span class="font-size-20 m-r-20 f-w-700">常用功能</span>
                  <span class="font-size-18" style="color: #9C9F9F;">({{ commonFunction.length }}/18)</span>
                </div>
                <div class="ps-flex-align-c flex-align-c">
                  <el-button class="ps-origin-btn m-r-30" size="mini" @click="customDrawerShow = true">定制</el-button>
                </div>
              </div>
              <div class="application-center-left-content">
                <el-row v-if="commonFunction.length">
                  <el-col :span="4" v-for="(item, index) in commonFunction" :key="index">
                    <div class="flex-a-c flex-col m-b-80 pointer" @click="goToPath(item.key)">
                      <svg-icon :icon-class="hasThisKey(item.key)" class="m-b-10" style="width: 38px; height: 38px;" />
                      <span class="font-size-14">{{item.verbose_name}}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row v-else>
                  <el-empty description="暂无常用功能"></el-empty>
                </el-row>
              </div>
            </div>
            <div class="application-center-left-bottom bg-style p-20">
              <div class="application-center-left-title m-b-30 ps-flex-align-c">
                <div class="ps-flex-align-c flex-align-c">
                  <span class="font-size-20 m-r-20 f-w-700">拓展应用</span>
                </div>
              </div>
              <div class="application-center-left-content">
                <el-row v-if="expandApplicationList.length">
                  <el-col :span="4" v-for="(item, index) in expandApplicationList" :key="index">
                    <div class="flex-a-c flex-col m-b-40" @click="clickHandle(item)" :class="['flex-center', 'flex-col', 'item', item.disabled ? 'is-disabled' : '']">
                      <img :src="item.src" alt="" />
                      <span class="m-t-8 font-size-14">{{item.label}}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row v-else>
                  <el-empty description="暂无拓展应用"></el-empty>
                </el-row>
              </div>
            </div>
          </div>

        </el-col>
        <el-col :span="5">
          <div class="application-center-right bg-style">
            <div class="application-center-right-top">
              <div class="f-w-700 m-b-25">
                <div class="font-size-26">{{ userInfo.member_name }}</div>
                <div class="font-size-18">欢迎登录本系统</div>
              </div>
              <div class="font-size-16 f-w-700">当前版本号：{{ updateDataList.length ? updateDataList[0].versio_no : '' }} <i class="el-icon-info pointer origin" @click="updateRecordDrawerShow = true"></i></div>
            </div>
            <div class="application-center-right-content p-20">
              <div class="m-b-20">
                <div class="font-size-20 f-w-700 m-b-10">用户情况</div>
                <div class="application-center-right-content-box flex-col" v-if="tollInfo.toll_type === 1">
                  <el-statistic
                    class="m-b-20"
                    group-separator=","
                    :value-style="{ fontSize: '16px' }"
                    :value="tollInfo.use_user_count"
                  >
                    <template #prefix>
                      <span class="font-size-16">当前系统用户数：</span>
                    </template>
                    <template #suffix>
                      <span class="font-size-16">人</span>
                    </template>
                  </el-statistic>
                  <el-statistic
                    group-separator=","
                    :value-style="{ fontSize: '16px' }"
                    :value="(tollInfo.user_scale - tollInfo.use_user_count)"
                  >
                    <template #prefix>
                      <span class="font-size-16">剩余可新增用户数：</span>
                    </template>
                    <template #suffix>
                      <span class="font-size-16">人</span>
                    </template>
                  </el-statistic>
                </div>
                <div class="application-center-right-content-box flex-col" v-else>
                  <el-statistic
                    group-separator=","
                    :value-style="{ fontSize: '16px' }"
                  >
                    <template #prefix>
                      <span class="font-size-16">当前系统用户数：</span>
                    </template>
                    <template #formatter>
                      <span>不限制</span>
                    </template>
                  </el-statistic>
                </div>
              </div>
              <div class="m-b-20">
                <div class="font-size-20 f-w-700 m-b-10">系统使用情况</div>
                <div class="application-center-right-content-box flex-col" v-if="tollInfo.toll_type === 1">
                  <el-statistic
                    class="m-b-20"
                    group-separator=","
                    :value-style="{ fontSize: '16px' }"
                    :value="tollInfo.use_order_count"
                  >
                    <template #prefix>
                      <span class="font-size-16">已使用订单量：</span>
                    </template>
                    <template #suffix>
                      <span class="font-size-16">笔</span>
                    </template>
                  </el-statistic>
                  <el-statistic
                    group-separator=","
                    :value-style="{ fontSize: '16px' }"
                    :value="(tollInfo.restricted_order_count_num - tollInfo.use_order_count)"
                  >
                    <template #prefix>
                      <span class="font-size-16">剩余可使用订单量：</span>
                    </template>
                    <template #suffix>
                      <span class="font-size-16">笔</span>
                    </template>
                  </el-statistic>
                </div>
                <div class="application-center-right-content-box flex-col" v-else>
                  <el-statistic
                    group-separator=","
                    :value-style="{ fontSize: '16px' }"
                  >
                    <template #prefix>
                      <span class="font-size-16">已使用订单量：</span>
                    </template>
                    <template #formatter>
                      <span>不限制</span>
                    </template>
                  </el-statistic>
                </div>
              </div>
              <div>
                <div class="font-size-20 f-w-700 m-b-10">系统有效期</div>
                <div class="application-center-right-content-box flex-col">
                  <span class="font-size-16">系统到期时间：{{ tollInfo.toll_type !== 3 ? computedDate(tollInfo.service_end_time) : '不限制' }}</span>
                </div>
              </div>
            </div>
            <div class="application-center-right-bottom p-20">
              <el-button size="small" class="w-100 ps-origin-btn" @click="goToPage">升级服务</el-button>
            </div>
          </div>
        </el-col>
      </div>
    </el-row>

    <div class="ps-el-drawer">
      <el-drawer
        title="更新记录"
        :visible="updateRecordDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-l-20 p-r-20 p-b-20">
          <div class="tip" style="z-index: 10;">
            <span>提示：仅提供近2个月的系统更新记录，如有其他需求，请联系客服。</span>
          </div>
          <div class="drawer-box m-b-20" v-for="(item, index) in updateDataList" :key="index">
            <div class="font-size-20 f-w-700 m-b-10">版本号：{{ item.versio_no }}</div>
            <div class="drawer-box-bg">
              <div class="p-t-10 p-b-10" v-for="(itemI, indexI) in item.detail" :key="indexI">
                <div class="font-size-20 f-w-700 m-b-10">{{ itemI.label }}:</div>
                <el-input
                  :disabled="true"
                  :resize="'none'"
                  type="textarea"
                  v-model="itemI.text"
                  :autosize="true">
                </el-input>
              </div>
            </div>
          </div>
          <div class="ps-el-drawer-footer">
            <el-button size="small" type="primary" class="w-100" @click="updateRecordDrawerShow = false">关闭</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        title="常用功能定制"
        :visible="customDrawerShow"
        :show-close="false"
        size="60%">
        <div class="p-l-20 p-r-20 p-b-20">
          <div>
            <el-tabs v-model="activeTab">
              <el-tab-pane :label="item.verbose_name" :name="item.key" v-for="(item, index) in functionList" :key="index">
                <!-- 第一种情况：item下有功能组 -->
                <div v-if="item.children.length">
                  <div class="function-content" v-for="(item1, index1) in item.children" :key="index1">
                    <!-- 功能组下有二级功能的显示二级功能 -->
                    <div v-if="item1.children.length">
                      <el-row :gutter="20">
                        <el-col :span="8" v-for="(item2, index2) in item1.children" :key="index2" class="m-t-15">
                          <div class="function-content-item flex-b-c">
                            <div class="flex-a-c">
                              <svg-icon :icon-class="hasThisKey(item2.key)" class="m-r-8" style="width: 38px; height: 38px;" />
                              <div>{{ item2.verbose_name }}</div>
                            </div>
                            <el-button size="small" :type="item2.isSelect ? 'primary' : 'default'"  class="w-100" @click="selectThis(item2, item2.isSelect)">{{ item2.isSelect ? '已添加' : '添加' }}</el-button>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                    <!-- 功能组下没有二级功能的显示功能组本身 -->
                    <div v-if="!item1.children.length">
                      <el-row :gutter="20">
                        <el-col :span="8" class="m-t-15">
                          <div class="function-content-item flex-b-c">
                            <div class="flex-a-c">
                              <svg-icon :icon-class="hasThisKey(item1.key)" class="m-r-8" style="width: 38px; height: 38px;" />
                              <div>{{ item1.verbose_name }}</div>
                            </div>
                            <el-button size="small" :type="item1.isSelect ? 'primary' : 'default'"  class="w-100" @click="selectThis(item1, item1.isSelect)">{{ item1.isSelect ? '已添加' : '添加' }}</el-button>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                </div>
                <!-- 尚未开发 -->
                <div v-if="!item.children.length">
                  <el-empty description="该分组功能尚未开发"></el-empty>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
            <div class="m-r-30">
              <el-button size="small"  class="w-100" @click="cancelHandle">取消</el-button>
              <el-button size="small" type="primary" class="w-100" @click="saveHandle">保存</el-button>
            </div>
            <span>可定制数量:{{ commonFunctionList.length }}/18</span>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { deepClone, to } from '@/utils'
import { URL_MANUFACTURER, URL_MANUFACTURER_STAGING } from '@/views/merchant/user-center/constants/cardManageConstants'
import dayjs from 'dayjs'
import { mapGetters } from 'vuex'
import { defaultPathList, EXPAND, ULR_DO_BEI_SYSTEM_DEV, ULR_DO_BEI_SYSTEM_STAGING, ULR_DO_BEI_SHOP_DEV, ULR_DO_BEI_SHOP_STAGING, URL_WASHING_SYSTEM_DEV, URL_WASHING_SYSTEM_STAGING } from './constants'
export default {
  data() {
    return {
      updateRecordDrawerShow: false,
      customDrawerShow: false,
      activeTab: 'user_center',
      functionList: [],
      tollInfo: {},
      commonFunction: [],
      commonFunctionList: [],
      expand: EXPAND,
      expandApplicationList: [],
      updateDataList: [],
      needToJudgmentKeyList: [
        'background_reservation.background_reservation_settings.list',
        'background_report_meal.report_meal_settings.list',
        'background_order.order_report_meal.not_report_list'
      ]
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'allPermissions'
    ]),
    computedDate() {
      return d => {
        if (d) {
          return dayjs(d).format('YYYY-MM-DD')
        } else {
          return '不限制'
        }
      }
    },
    hasChildren() {
      return d => {
        let flag = false
        if (d.children.length) {
          d.children.forEach(item => {
            if (item.children.length) {
              flag = true
            }
          })
        }
        return flag
      }
    },
    hasThisKey() {
      return d => {
        const requireContext = require.context('@/icons/svg', false, /\.svg$/)

        const fileNames = requireContext.keys().map(filePath => {
          return filePath.replace('./', '').replace('.svg', '')
        })
        if (!fileNames.includes(d)) {
          return 'defaultIcon'
        } else {
          return d
        }
      }
    }
  },
  watch: {
    customDrawerShow(newVal, oldVal) {
      if (newVal) {
        // 反显已选
        this.commonFunctionList = this.commonFunction.map(item => {
          return item.key
        })
        this.showSelect(this.functionList)
      }
    }
  },
  created() {
    this.getPermissionList()
    this.getSystemInfo()
    this.getCommonFunctionList()
    this.getThirdLogin()
    this.getUpdateDataList()
  },
  methods: {
    // 获取第三方登录
    async getThirdLogin() {
      this.isLoading = true
      let organization = this.$store.getters.organization || {}
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationGetOpenThirdPermissionsPost({ id: organization }))
      this.isLoading = false
      if (err) {
        return
      }
      if (res.code === 0) {
        let resultList = res.data || []
        let newArr = []
        // 获取后台的数据跟我们写的列表对比，有的我们就放开可以跳转
        newArr = this.expand.map(item => {
          let type = item.type || ''
          let hasPermissionItem
          if (type) {
            hasPermissionItem = resultList.find(subItem => {
              return subItem.name === type
            })
            // 存在权限
            if (hasPermissionItem) {
              item.extra = hasPermissionItem.extra || {}
              item.disabled = false
            }
          }
          return item
        })
        this.expandApplicationList = newArr.filter(item => !item.disabled)
        console.log('expandApplicationList', this.expandApplicationList)
      } else {
        // this.$message.error(res.msg)
      }
    },
    clickHandle(data) {
      console.log("clickHandle", data)
      var url = process.env.NODE_ENV === 'development' ? URL_MANUFACTURER : URL_MANUFACTURER_STAGING
      if (!data.disabled) {
        // window.location.href = data.url
        if (data.id === 6) {
          // 车辆管理如果要免登需要先获取token
          this.getCarToken(data.extra, url)
        } else if (Reflect.has(data, 'type') && data.type === 'DoBay') {
          // 东贝系统跳转
          this.goToDoBeiSystem(data)
        } else if (data.id === 9) {
          // 对接中卡洗衣
          url = process.env.NODE_ENV === 'development' ? URL_WASHING_SYSTEM_DEV : URL_WASHING_SYSTEM_STAGING
          this.getWashingSystemToken(data.extra, url)
        } else if (data.id === 13) {
          window.open(data.extra ? data.extra.login_url : '', '_blank')
        } else {
          window.open(data.url ? data.url : url, '_blank')
        }
      } else {
        this.$message.error('功能正在开发中！')
      }
    },
    /***
     * 获取第三方车辆管理token
     */
    async getCarToken(data, urlPath) {
      var params = {
        project_no: data.project_no,
        app_secret: data.app_secret,
        appid: data.appid
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundCarTravelCarTravelInfoGenerateRedirectTokenPost(params))
      this.isLoading = false
      if (err) {
        window.open(urlPath + 'login', '_blank')
        return
      }
      if (res.code === 0) {
        var resultData = res.data || {}
        if (Reflect.has(resultData, "data") && Reflect.has(resultData.data, 'data') && resultData.data.data !== null && Reflect.has(resultData.data.data, 'token')) {
          var newPath = urlPath + "parkingLot/homePage?token=" + resultData.data.data.token
          window.open(newPath, '_blank')
        } else {
          window.open(urlPath + 'login', '_blank')
        }
      } else {
        window.open(urlPath + 'login', '_blank')
      }
    },
    /**
     * 跳转东贝系统
     * @param {} data
     */
    async goToDoBeiSystem(data) {
      var label = data.label || ''
      var urlManage = process.env.NODE_ENV === 'development' ? ULR_DO_BEI_SYSTEM_DEV : ULR_DO_BEI_SYSTEM_STAGING
      var urlShop = process.env.NODE_ENV === 'development' ? ULR_DO_BEI_SHOP_DEV : ULR_DO_BEI_SHOP_STAGING
      var returnUrl = ''
      var message = "获取第三方授权信息失败"
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDobayGetLoginUrlPost({ login_type: label === '督贝管理' ? '1' : '2' }))
      this.isLoading = false
      if (Reflect.has(res, "code") && res.code === 0) {
        returnUrl = res.data || urlManage
      } else if (Reflect.has(res, "msg") && res.msg) {
        message = res.msg || message
        this.$message.error(message)
      }
      switch (label) {
        case "督贝管理":
          urlManage = returnUrl || urlManage
          window.open(urlManage, '_blank')
          break;
        case "智慧门店":
          urlShop = returnUrl || urlShop
          window.open(urlShop, '_blank')
          break;
        default:
          break;
      }
    },
    /**
     * 获取第三方中卡洗衣对接token
     * @param {*} data
     * @param {*} urlPath
     */
    async getWashingSystemToken(data, urlPath) {
      var url = urlPath
      // 重新确认Url
      if (Reflect.has(data, "login_url") && data.login_url !== null && data.login_url !== '') {
        url = data.login_url
      }
      // 由于对方系统没有免登的功能，所以改为直接跳转他们的URL，优先义后台配置的为准
      window.open(url, '_blank')
    },
    // 获取商户目前的收费系统使用情况
    getSystemInfo() {
      this.$apis.apiBackgroundTollBackgroundTollGetTollInfoPost().then(res => {
        if (res.code === 0) {
          this.tollInfo = deepClone(res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取功能菜单
    getPermissionList() {
      this.$apis.apiBackgroundBaseMenuGetListPost().then(res => {
        if (res.code === 0) {
          this.functionList = res.data.map(item => {
            // 循环插入isSelect
            this.pushIsSelect(item)
            return item
          })
          // 把首页的东西踢掉
          this.functionList.splice(0, 1)
        }
      })
    },
    // 获取常用功能
    getCommonFunctionList() {
      this.$apis.apiBackgroundHomepageCommonGetCommonListPost().then(res => {
        if (res.code === 0) {
          this.commonFunction = deepClone(res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 循环往下查询，有children的话向里面的item插入isSelect
    pushIsSelect(item) {
      Object.assign(item, { isSelect: false })
      if (item.children && item.children.length) {
        item.children.forEach(item1 => {
          this.pushIsSelect(item1)
        })
      }
    },
    goToPage() {
      this.$router.push({
        name: 'UpgradeService'
      })
    },
    // 选中
    selectThis(data, status) {
      if (this.commonFunctionList.length >= 18 && !data.isSelect) {
        return this.$message.error('定制数量已达上限')
      }
      data.isSelect = !data.isSelect
      let arr = deepClone(this.commonFunctionList)
      // 存入key
      if (!status) {
        arr.push(data.key)
      } else {
        if (arr.length) {
          arr = arr.filter(item => item !== data.key)
        }
      }
      this.commonFunctionList = deepClone(arr)
    },
    // 反显
    showSelect(arr) {
      if (arr.length) {
        arr.forEach(item => {
          if (this.commonFunctionList.includes(item.key)) {
            item.isSelect = true
          } else {
            item.isSelect = false
          }
          if (item.children && item.children.length) {
            this.showSelect(item.children)
          }
        })
      }
    },
    saveHandle() {
      let params = {
        background_common_select_permission: this.commonFunctionList
      }
      this.$apis.apiBackgroundHomepageCommonSetCommonPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('保存成功')
          this.getCommonFunctionList()
          this.customDrawerShow = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    cancelHandle() {
      this.commonFunctionList = []
      this.showSelect(this.functionList)
      this.customDrawerShow = false
    },
    goToPath(key) {
      defaultPathList.forEach(item => {
        if (item.key === key) {
          if (item.name) {
            if (this.needToJudgmentKeyList.includes(item.key)) {
              // 如果当前key在需要判断的列表里
              if (this.allPermissions.includes(item.key)) {
                // 如果当前用户的权限里有这个权限，就放他进去
                this.$router.push({
                  name: item.name
                })
              } else {
                this.$message.error('您暂无权限使用该功能，请联系管理员恢复权限后重试')
              }
            } else {
              // 不在的话直接跳转
              this.$router.push({
                name: item.name
              })
            }
          } else {
            this.$message.error('该功能暂未开发')
          }
        }
      })
    },
    // 获取更新记录
    getUpdateDataList() {
      let params = {
        page: 1,
        page_size: 9999,
        end_update_time: dayjs().hour(23).minute(59).second(59).format('YYYY-MM-DD HH:mm:ss'),
        star_update_time: dayjs().subtract(2, 'month').format('YYYY-MM-DD HH:mm:ss'),
        push_status: 1
      }
      this.$apis.apiBackgroundStoreSysUpdateRecordListPost(params).then(res => {
        if (res.code === 0) {
          this.updateDataList = deepClone(res.data.results)
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.application-center {
  &-left {
    height: calc(100vh - 150px);
    display: grid;
    grid-gap: 20;
    grid-template-rows: 2fr 1fr;
    grid-auto-flow: column;
  }
  &-right {
    height: calc(100vh - 150px);
    position: relative;
    &-top {
      background-image: url('/tinymce/img/application_center_bg.png');
      background-size: fill;
      color: #23282D;
      border-radius: 8px 8px 0px 0px;
      padding: 20px 20px 20px;
      margin-bottom: 20px;
    }
    &-content {
      &-box {
        padding: 20px;
        border-radius: 4px;
        background-color: #f8f9fa;
      }
    }
    &-bottom {
      position: absolute;
      bottom: 0px;
      left: 0px;
    }
  }
  .bg-style {
    background-color: #fff;
    border-radius: 8px;
  }
  .item {
    cursor: pointer;
    img {
      width: 50px;
      height: 50px;
    }
  }
  .is-disabled {
    opacity: .5;
  }
}
.tip {
  position: sticky;
  top: 0px;
  padding: 20px 0px;
  background-color: #fff;
  color: #FF9B45;
}
.drawer-box {
  &-bg {
    min-height: 100px;
    padding: 20px;
    border-radius: 4px;
    background-color: #f8f9fa;
  }
}
.function-content {
  padding: 0px 0px 15px;
  border-bottom: 1px solid #DCDFE6;
  &-item {
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #DCDFE6;
  }
}
::v-deep .con {
  justify-content: flex-start
}
::v-deep .el-tabs__nav {
  padding: 10px 5px;
}
</style>
