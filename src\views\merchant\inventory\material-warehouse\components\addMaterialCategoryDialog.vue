<template>
  <!-- 添加/编辑 -->
  <custom-drawer
    :title="title"
    :show.sync="visible"
    :direction="direction"
    :wrapperClosable="wrapperClosable"
    :size="size"
    class="drawer-wrapper"
    v-bind="$attrs"
    v-on="$listeners"
    confirm-text="确定"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <el-form
      :model="formData"
      ref="formData"
      :rules="formDataRules"
      label-width="90px"
      class="dialog-form m-t-20"
      v-loading="isLoading"
      :status-icon="false"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="formData.name" :maxlength="20" class="search-item-w"></el-input>
      </el-form-item>
      <el-form-item label="分类属性" required>
        <el-form-item v-for="(item, index) in formData.attribute" :key="index" label="" label-width="0" :prop="`attribute.${index}`" :rules="formDataRules.attribute">
          <div class="flex flex-align-c">
            <el-input v-model="formData.attribute[index]" :maxlength="20" class="search-item-w"></el-input>
            <div class="tool-box flex flex-align-c m-l-10">
              <i v-if="formData.attribute.length < 50" class="tool-icon el-icon-circle-plus" @click="clickToolIcon('add', index)"></i>
              <i v-if="formData.attribute.length > 1" class="tool-icon el-icon-remove" @click="clickToolIcon('remove', index)"></i>
            </div>
          </div>
        </el-form-item>
      </el-form-item>
    </el-form>
  </custom-drawer>
  <!-- end -->
</template>

<script>
import { deepClone } from '@/assets/js/util'

export default {
  name: 'addMaterialCategoryDialog',
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    wrapperClosable: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '新建分类'
    },
    size: {
      type: [String, Number],
      default: '40%'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      formData: {
        name: '',
        attribute: ['']
      },
      formDataRules: {
        name: [{ required: true, message: '请输入分类名称', trigger: 'change' }],
        attribute: [{ required: true, message: '请输入属性', trigger: 'change' }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        this.initLoad()
      }
    },
    'formData.unitRateList': function(val) {
      console.log('2222', val)
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.type === 'modify') {
        this.formData.name = this.infoData.name
        this.formData.attribute = deepClone(this.infoData.attribute)
      } else {
        this.formData.name = ''
        this.formData.attribute = ['']
      }
    },
    // 添加/删除
    clickToolIcon(type, index) {
      if (type === 'add') {
        this.formData.attribute.push('')
      } else {
        this.formData.attribute.splice(index, 1)
      }
    },
    clickConfirmHandle() {
      let api
      let params = {
        name: this.formData.name,
        attribute: this.formData.attribute
      }

      this.$refs.formData.validate(valid => {
        if (valid) {
          if (this.isLoading) return
          this.isLoading = true
          if (this.type === 'add') {
            api = this.$apis.apiBackgroundDrpMaterailClassificationAddPost(params)
          } else {
            params.id = this.infoData.id
            api = this.$apis.apiBackgroundDrpMaterailClassificationModifyPost(params)
          }
          this.sendFormData(api)
        } else {
        }
      })
    },
    async sendFormData(api) {
      const [err, res] = await this.$to(api)
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('成功')
        this.$emit('clickConfirm')
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handlerClose(e) {
      this.formData = {
        name: '',
        attribute: []
      }
      if (this.$refs.formData) {
        this.$refs.formData.resetFields()
      }
      this.isLoading = false
      // this.visible = false
      // this.$emit('close')
    }
  }
}
</script>

<style lang="scss">
.drawer-wrapper {
  font-size: 14px;
  .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
  .search-item-w {
    width: 260px;
  }
  .flex {
    display: flex;
  }
  .tool-icon {
    cursor: pointer;
    font-size: 24px;
    &+.tool-icon {
      margin-left: 6px;
    }
    &:hover{
      color: #ff9b45;
    }
  }
  .el-form-item .el-form-item {
    margin-bottom: 22px;
  }
}
</style>
