// import Layout from '@/layout'
// 注意layout一定要最后生成再替换成import的方式引入，不然会触发页面重复渲染
let Layout = 'Layout'

// 进销存模块路由
const inventory = [
  { // 进销存
    path: '/inventory',
    name: 'Inventory',
    component: Layout,
    alwaysShow: true,
    redirect: '/inventory/warehouse_admin',
    meta: {
      title: 'inventory',
      noCache: true,
      permission: ['background_drp']
      // no_permission: true
    },
    children: [
      { // 仓库管理
        path: 'warehouse_admin',
        component: () =>
          import(
            /* webpackChunkName: "warehouse_admin" */ '@/views/merchant/inventory/warehouse-admin/WarehouseAdmin'
          ),
        name: 'WarehouseAdmin',
        meta: {
          noCache: true,
          title: 'warehouse_admin',
          permission: ['background_drp.warehouse.list']
        }
      },
      { // 单位管理
        path: 'unit_admin',
        component: () =>
          import(
            /* webpackChunkName: "inventory_unit_admin" */ '@/views/merchant/inventory/warehouse-admin/UnitAdmin'
          ),
        name: 'InventoryUnitAdmin',
        // hidden: true,
        meta: {
          noCache: true,
          title: 'inventory_unit_admin',
          permission: ['background_drp.unit_management.list']
        }
      },
      { // 库存管理
        path: 'inventory_management',
        component: () =>
          import(
            /* webpackChunkName: "inventory_management" */ '@/views/merchant/inventory/warehouse-admin/InventoryManagement'
          ),
        name: 'InventoryManagement',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inventory_management',
          activeMenu: '/inventory/warehouse_admin',
          permission: ['background_drp.inventory_info.list']
        }
      },
      { // 库存流水
        path: 'inventory_flow',
        component: () =>
          import(
            /* webpackChunkName: "Inventory_flow" */ '@/views/merchant/inventory/warehouse-admin/InventoryFlow'
          ),
        name: 'InventoryFlow',
        hidden: true,
        meta: {
          noCache: true,
          title: 'Inventory_flow',
          activeMenu: '/inventory/warehouse_admin',
          permission: ['background_drp.inventory_info.inventory_record']
        }
      },
      { // 盆点
        path: 'inventory_stock',
        component: () =>
          import(
            /* webpackChunkName: "inventory_stock" */ '@/views/merchant/inventory/warehouse-admin/InventoryStock/InventoryStock'
          ),
        name: 'InventoryStock',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inventory_stock_management',
          activeMenu: '/inventory/warehouse_admin',
          permission: ['background_drp.warehouse.list']
        }
      },
      // 新增盆点
      {
        path: 'inventory_stock_:type',
        component: () =>
          import(
            /* webpackChunkName: "add_inventory_stock" */ '@/views/merchant/inventory/warehouse-admin/InventoryStock/AddInventoryStock'
          ),
        name: 'AddInventoryStock',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inventory_stock',
          activeMenu: '/inventory/warehouse_admin',
          permission: ['background_drp.warehouse.list']
        }
      },
      { // 盘点详情
        path: 'inventory_stock_detail',
        component: () =>
          import(
            /* webpackChunkName: "inventory_stock_detail" */ '@/views/merchant/inventory/warehouse-admin/InventoryStock/InventoryStockDetail'
          ),
        name: 'InventoryStockDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inventory_stock_detail',
          activeMenu: '/inventory/warehouse_admin',
          permission: ['background_drp.warehouse.list']
        }
      },
      { // 入库单
        path: 'inbound_order',
        component: () =>
          import(
            /* webpackChunkName: "inbound_order" */ '@/views/merchant/inventory/warehouse-admin/InboundOrder/InboundOrder'
          ),
        name: 'InboundOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inbound_order',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['background_drp.inventory_info.inventory_enter_add']
          no_permission: true
        }
      },
      { // 添加入库单
        path: 'inbound_order_:type',
        component: () =>
          import(
            /* webpackChunkName: "add_inbound_order" */ '@/views/merchant/inventory/warehouse-admin/InboundOrder/AddInboundOrder'
          ),
        name: 'AddInboundOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inbound_order',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['background_drp.inventory_info.inventory_enter_add']
          no_permission: true
        }
      },
      { // 入库
        path: 'warehousing',
        component: () =>
          import(
            /* webpackChunkName: "warehousing" */ '@/views/merchant/inventory/warehouse-admin/InboundOrder/Warehousing'
          ),
        name: 'Warehousing',
        hidden: true,
        meta: {
          noCache: true,
          title: 'warehousing',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['background_drp.inventory_info.inventory_exit_add']
          no_permission: true
        }
      },
      { // 入库单详情
        path: 'inbound_detail',
        component: () =>
          import(
            /* webpackChunkName: "inbound_order_detail" */ '@/views/merchant/inventory/warehouse-admin/InboundOrder/InboundOrderDetail'
          ),
        name: 'InboundOrderDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inbound_order_detail',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['background_drp.inventory_info.inventory_exit_add']
          no_permission: true
        }
      },
      { // 出库单
        path: 'outbound_order',
        component: () =>
          import(
            /* webpackChunkName: "outbound_order" */ '@/views/merchant/inventory/warehouse-admin/OutboundOrder/OutboundOrder'
          ),
        name: 'OutboundOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'outbound_order',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['background_drp.inventory_info.inventory_enter_add']
          no_permission: true
        }
      },
      { // 添加出库单
        path: 'outbound_order/:type',
        component: () =>
          import(
            /* webpackChunkName: "add_outbound_order" */ '@/views/merchant/inventory/warehouse-admin/OutboundOrder/AddOutboundOrder'
          ),
        name: 'AddOutboundOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'outbound_order',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['background_drp.inventory_info.inventory_exit_add']
          no_permission: true
        }
      },
      { // 出库单详情
        path: 'outbound_order_detail',
        component: () =>
          import(
            /* webpackChunkName: "outbound_order_detail" */ '@/views/merchant/inventory/warehouse-admin/OutboundOrder/OutboundOrderDetail'
          ),
        name: 'OutboundOrderDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'outbound_order_detail',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['background_drp.inventory_info.inventory_exit_add']
          no_permission: true
        }
      },
      { // 出入库明细
        path: 'inbound_and_outbound_detail',
        component: () =>
          import(
            /* webpackChunkName: "InboundAndOutboundDetail" */ '@/views/merchant/inventory/report/inbound-and-outbound/InboundAndOutboundDetail'
          ),
        name: 'InboundAndOutboundDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inbound_and_outbound_detail',
          activeMenu: '/inventory/warehouse_admin',
          no_permission: true
          // permission: ['background_drp.inventory_info.inventory_record']
        }
      },
      { // 出入库报表
        path: 'inbound_and_outbound_report',
        component: () =>
          import(
            /* webpackChunkName: "InboundAndOutboundReport" */ '@/views/merchant/inventory/report/inbound-and-outbound/InboundAndOutboundReport'
          ),
        name: 'InboundAndOutboundReport',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inbound_and_outbound_report',
          activeMenu: '/inventory/warehouse_admin',
          no_permission: true
          // permission: ['background_drp.inventory_info.inventory_record']
        }
      },
      { // 物资库
        path: 'material_warehouse',
        component: () =>
          import(
            /* webpackChunkName: "material_warehouse" */ '@/views/merchant/inventory/material-warehouse/MaterialWarehouse'
          ),
        name: 'MaterialWarehouse',
        meta: {
          noCache: true,
          title: 'material_warehouse',
          permission: ['background_drp']
        }
      },
      { // 供应商管理
        path: 'supplier_management',
        component: () =>
          import(
            /* webpackChunkName: "supplier_management" */ '@/views/merchant/inventory/supplier-management/SupplierManagement'
          ),
        name: 'SupplierManagement',
        meta: {
          noCache: true,
          title: 'supplier_management',
          permission: ['background_drp']
        }
      },
      { // 新建供应商
        path: 'supplier_management_:type',
        component: () =>
          import(
            /* webpackChunkName: "add_supplier_management" */ '@/views/merchant/inventory/supplier-management/AddSupplierManagement'
          ),
        name: 'AddSupplierManagement',
        hidden: true,
        meta: {
          activeMenu: '/inventory/supplier_management',
          noCache: true,
          title: 'supplier_management',
          permission: ['background_drp']
        }
      },
      { // 关联物资
        path: 'supplier_management/related_materials',
        component: () =>
          import(
            /* webpackChunkName: "related_materials" */ '@/views/merchant/inventory/supplier-management/RelatedMaterials'
          ),
        name: 'RelatedMaterials',
        hidden: true,
        meta: {
          activeMenu: '/inventory/supplier_management',
          noCache: true,
          title: 'related_materials',
          permission: ['background_drp']
        }
      },
      { // 采购清单明细
        path: 'pulchase_list_report',
        component: () =>
          import(
            /* webpackChunkName: "PurchaseListReport" */ '@/views/merchant/inventory/report/PurchaseListReport'
          ),
        name: 'PurchaseListReport',
        meta: {
          noCache: true,
          title: 'pulchase_list_report',
          permission: ['background_drp']
        }
      },
      { // 采购单
        path: 'purchase_order_list',
        component: () =>
          import(
            /* webpackChunkName: "purchase_order_list" */ '@/views/merchant/inventory/warehouse-admin/purchase-order/PurchaseOrderList'
          ),
        name: 'PurchaseOrderList',
        hidden: true,
        meta: {
          noCache: true,
          title: 'purchase_order_list',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['background_drp.inventory_info.inventory_enter_add']
          no_permission: true
        }
      },
      {
        path: 'purchase_order_:type',
        component: () =>
          import(
            /* webpackChunkName: "modify_purchase_order" */ '@/views/merchant/inventory/warehouse-admin/purchase-order/ModifyPurchaseOrder'
          ),
        name: 'ModifyPurchaseOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'modify_purchase_order',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      { // 采购单详情
        path: 'pulchase_detail',
        component: () =>
          import(
            /* webpackChunkName: "PurchaseDetail" */ '@/views/merchant/inventory/warehouse-admin/purchase-order/PurchaseDetail'
          ),
        name: 'PurchaseDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'pulchase_detail',
          activeMenu: '/inventory/warehouse_admin',
          permission: ['background_drp']
        }
      },
      // 询价单
      {
        path: 'inquiry_order',
        component: () =>
          import(
            /* webpackChunkName: "InquiryOrder" */ '@/views/merchant/inventory/warehouse-admin/inquiry-order/InquiryOrderList'
          ),
        name: 'InquiryOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inquiry_order',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      // 添加询价单
      {
        path: 'inquiry_order:type',
        component: () =>
          import(
            /* webpackChunkName: "AddInquiry" */ '@/views/merchant/inventory/warehouse-admin/inquiry-order/AddInquiry'
          ),
        name: 'AddInquiry',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inquiry_order',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      // 询价单详情
      {
        path: 'inquiry_detail',
        component: () =>
          import(
            /* webpackChunkName: "InquiryDetail" */ '@/views/merchant/inventory/warehouse-admin/inquiry-order/InquiryDetail'
          ),
        name: 'InquiryDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inquiry_detail',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      // 调拨单
      {
        path: 'transfer_order',
        component: () =>
          import(
            /* webpackChunkName: "TransferOrder" */ '@/views/merchant/inventory/warehouse-admin/transfer-order/TransferOrder'
          ),
        name: 'TransferOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'transfer_order',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      {
        path: 'transfer_orders_:type',
        component: () =>
          import(
            /* webpackChunkName: "AddTransferOrder" */ '@/views/merchant/inventory/warehouse-admin/transfer-order/AddTransferOrder'
          ),
        name: 'AddTransferOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'transfer_order',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      {
        path: 'transfer_order_return',
        component: () =>
          import(
            /* webpackChunkName: "ReturnTransferOrder" */ '@/views/merchant/inventory/warehouse-admin/transfer-order/ReturnTransferOrder'
          ),
        name: 'ReturnTransferOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'transfer_order_return',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      {
        path: 'transfer_order_detail',
        component: () =>
          import(
            /* webpackChunkName: "TransferOrderDetail" */ '@/views/merchant/inventory/warehouse-admin/transfer-order/TransferOrderDetail'
          ),
        name: 'TransferOrderDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'transfer_order_detail',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      // 退货管理
      {
        path: 'returns_management',
        component: () =>
          import(
            /* webpackChunkName: "ReturnsManagement" */ '@/views/merchant/inventory/warehouse-admin/returns-management/ReturnsManagement'
          ),
        name: 'ReturnsManagement',
        hidden: true,
        meta: {
          noCache: true,
          title: 'returns_management',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      {
        path: 'returns_management_detail',
        component: () =>
          import(
            /* webpackChunkName: "ReturnsManagementDetail" */ '@/views/merchant/inventory/warehouse-admin/returns-management/ReturnsManagementDetail'
          ),
        name: 'ReturnsManagementDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'returns_management_detail',
          activeMenu: '/inventory/warehouse_admin',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      // 审批管理
      {
        path: 'approval_management',
        name: 'approval_management',
        redirect: '/approval_management_list',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'approval_management',
          // permission: ['inventory_system']
          no_permission: true
        },
        children: [
          {
            path: 'approval_management_list',
            component: () =>
              import(
                /* webpackChunkName: "ApprovalManagementList" */ '@/views/merchant/inventory/approval-management/ApprovalManagementList'
              ),
            name: 'ApprovalManagementList',
            meta: {
              noCache: true,
              title: 'approval_management_list',
              // permission: ['inventory_system']
              no_permission: true
            }
          },
          {
            path: 'approval_management_detail',
            component: () =>
              import(
                /* webpackChunkName: "ApprovalManagementDetail" */ '@/views/merchant/inventory/approval-management/ApprovalManagementDetail'
              ),
            name: 'ApprovalManagementDetail',
            hidden: true,
            meta: {
              noCache: true,
              title: 'approval_management_detail',
              activeMenu: '/inventory/approval_management/approval_management_list',
              // permission: ['inventory_system']
              no_permission: true
            }
          },
          {
            path: 'approval_settings',
            component: () =>
              import(
                /* webpackChunkName: "ApprovalSettings" */ '@/views/merchant/inventory/approval-management/ApprovalSettings'
              ),
            name: 'ApprovalSettings',
            meta: {
              noCache: true,
              title: 'approval_settings',
              // permission: ['inventory_system']
              no_permission: true
            }
          }
        ]
      },
      // 申购单列表
      {
        path: 'subscription_order_list',
        component: () =>
          import(
            /* webpackChunkName: "subscription_order_list" */ '@/views/merchant/inventory/subscription-order/SubscriptionOrderList'
          ),
        name: 'SubscriptionOrderList',
        meta: {
          noCache: true,
          title: 'subscription_order_list',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      {
        path: 'subscription_order_:type',
        component: () =>
          import(
            /* webpackChunkName: "AddSubscriptionOrder" */ '@/views/merchant/inventory/subscription-order/AddSubscriptionOrder'
          ),
        name: 'AddSubscriptionOrder',
        hidden: true,
        meta: {
          noCache: true,
          title: 'subscription_order',
          activeMenu: '/inventory/subscription_order_list',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      {
        path: 'subscription_orderdetail',
        component: () =>
          import(
            /* webpackChunkName: "SubscriptionOrderDetail" */ '@/views/merchant/inventory/subscription-order/SubscriptionOrderDetail'
          ),
        name: 'SubscriptionOrderDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'subscription_order_detail',
          activeMenu: '/inventory/subscription_order_list',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      // 申购记录
      {
        path: 'subscription_record_list',
        component: () =>
          import(
            /* webpackChunkName: "SubscriptionRecordList" */ '@/views/merchant/inventory/subscription-record/SubscriptionRecordList'
          ),
        name: 'SubscriptionRecordList',
        meta: {
          noCache: true,
          title: 'subscription_record_list',
          // permission: ['inventory_system']
          no_permission: true
        }
      },
      {
        path: 'subscription_record_detail',
        component: () =>
          import(
            /* webpackChunkName: "subscription_record_detail" */ '@/views/merchant/inventory/subscription-record/SubscriptionRecordDetail'
          ),
        name: 'SubscriptionRecordDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'subscription_record_detail',
          activeMenu: '/inventory/subscription_record_list',
          // permission: ['inventory_system']
          no_permission: true
        }
      }
    ]
  }
]
export default inventory
