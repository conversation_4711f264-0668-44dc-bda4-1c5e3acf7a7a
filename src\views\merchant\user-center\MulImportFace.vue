<template>
  <div class="mul-import-face">
    <el-form class="import-face-form-wrapper" label-width="140px">
      <el-form-item label="人脸导入模板">
        <el-link
          type="primary"
          :href="templateUrl"
        >
          点击下载
        </el-link>
      </el-form-item>
      <el-form-item label="上传人脸录入模板">
        <file-upload
          drag
          :data="uploadParams"
          :limit="limit"
          @fileLists="getSuccessUploadRes"
          :before-upload="beforeUpload"
          prefix="face"
          :action="actionUrl"
          :on-remove="remove"
        >
          <div class="">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
          </div>
          <div class="el-upload__tip" slot="tip">只能上传zip文件</div>
        </file-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" class="import-face-btn-wrapper" @click="mulImortFace">
          确定
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  mixins: [exportExcel],
  data() {
    return {
      limit: 1,
      actionUrl: '',
      uploadParams: {},
      uploadUrl: '',
      templateUrl: location.origin + "/api/temporary/template_excel/卡务模板/导入人脸.zip"
    }
  },
  methods: {
    async getUploadToken() {
      const res = await this.$apis.getUploadToken({
        prefix: 'face_url_zip'
      })
      if (res.code === 0) {
        this.actionUrl = res.data.host
        this.uploadParams = {
          key: res.data.prefix + new Date().getTime() + Math.floor(Math.random() * 150),
          prefix: res.data.prefix,
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          signature: res.data.signature,
          callback: res.data.callback,
          success_action_status: '200'
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeUpload(file) {
      let reg = /application\/\S*zip\S*/
      if (!reg.test(file.type)) {
        this.$message.error('请上传后缀名为.zip的压缩包文件')
        return false
      }
    },
    remove() {
      this.uploadUrl = ''
    },
    getSuccessUploadRes(res) {
      // if (res.code === 0) {
      //   this.uploadUrl = res.data.public_url
      // }
      this.uploadUrl = res[0].url
    },
    mulImortFace() {
      if (!this.uploadUrl) {
        this.$message.error('人脸压缩包还没上传完毕或未上传')
        return
      }
      const option = {
        type: 'MulImportFace',
        message: '确定导入人脸？',
        params: {
          face_zip_url: this.uploadUrl
        }
      }
      this.exportHandle(option)
    }
  },
  created() {
    // this.getUploadToken()
  }
}
</script>

<style lang="scss">
.mul-import-face {
  margin-top: 20px;
  padding: 20px 0;
  background-color: #fff;
  border-radius: 12px;
  .import-face-form-wrapper {
    width: 500px;
  }
  .buttons {
    padding-bottom: 20px;
  }
  .import-face-btn-wrapper {
    width: 100%;
  }
}
</style>
