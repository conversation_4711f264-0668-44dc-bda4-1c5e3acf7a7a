<template>
  <div class="MealApplyOrder container-wrapper">
    <div class="table-type">
      <div :class="['table-type-btn', tableType==='JZ'?'active-btn':'']" @click="changeTableType('JZ')" v-permission="['background_approve.order_approve_visitor.jz_list']">记账订单</div>
      <div :class="['table-type-btn', tableType==='JF'?'active-btn':'']" @click="changeTableType('JF')" v-permission="['background_approve.order_approve_visitor.jf_list']">即付订单</div>
    </div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" v-if="tableType==='JZ'" @click="mulOperation()" v-permission="['background_approve.order_approve_visitor.settle_order']">已对账</button-icon>
          <button-icon color="plain" type="export" @click="handleExport()" v-if="tableType==='JZ'" v-permission="['background_approve.order_approve_visitor.jz_list_export']">导出Excel</button-icon>
          <button-icon color="plain" type="export" @click="handleExport()" v-if="tableType==='JF'" v-permission="['background_approve.order_approve_visitor.jf_list_export']">导出Excel</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" align="center" class-name="ps-checkbox"></el-table-column>
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                v-if="row.can_refund && tableType==='JF'"
                @click="openDialog('refund', row)"
                v-permission="['background_approve.order_approve_visitor.refund']"
                >退款</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="goteDetail(row.order_payment_id)"
                >详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
    <el-dialog class="refund-dialog" top="200px" title="退款" :visible.sync="refundVisible" width="800px" customClass="ps-dialog">
      <el-table
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ textAlign: 'center' }"
        :data="refundData.food_list"
        border
        row-key="id"
        style="width: 100%;"
        ref="refundTable"
      >
        >
        <el-table-column
          type="selection"
          :show-overflow-tooltip="true"
          :reserve-selection="true"
          class-name="ps-checkbox"
          width="55"
        ></el-table-column>
        <el-table-column prop="food_extra.food_img" label="图片">
          <template slot-scope="scope">
            <img style="width:60px" :src="scope.row.food_extra.food_img" alt="">
          </template>
        </el-table-column>
        <el-table-column prop="name" label="菜品名称"></el-table-column>
        <el-table-column prop="food_price" label="销售价格">
          <template slot-scope="scope">
            <!-- raw_fee -->
            <span>￥{{ scope.row.food_price | formatMoney}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="数量"></el-table-column>
        <el-table-column prop="weight" label="重量"></el-table-column>
        <el-table-column prop="buy_price" label="消费金额">
          <template slot-scope="scope">
            <span>￥{{ scope.row.real_fee | formatMoney }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="food_status" label="退款状态">
          <template slot-scope="scope">
            <span>{{ scope.row.food_status === 'ORDER_REFUND_SUCCESS' ? '退款成功' : '未退款' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="refund-radio">
        <el-radio text-color="#FF9B45" v-model="refundType" @change="changeRefundType" label="all" class="ps-radio">全额退款</el-radio>
        <el-radio text-color="#FF9B45" v-model="refundType" @change="changeRefundType" label="part" class="ps-radio">部分退款</el-radio>
      </div>
      <div class="refund-info">
        <el-form
          :model="refundForm"
          @submit.native.prevent
          status-icon
          ref="refundFormRef"
          :rules="refundFormRules"
          label-width="100px"
          class="attendance-form"
          inline
        >
          <div class="inline-box m-r-20">
            <span>可退款余额：</span>
            <span v-if="refundType === 'all'">{{ refundData.net_fee }}</span>
            <span v-if="refundType === 'part'">{{ refundData.part_net_fee }}</span>
          </div>
          <div v-if="refundType === 'all'" class="inline-box">
            <span>退款金额：</span>
            <span>{{ refundData.pay_fee | formatMoney }}</span>
          </div>
          <div v-if="refundType === 'part' && refundData.payway !== 'PushiPay'" class="inline-box">
            <el-form-item label="退款金额：" prop="refundMoney">
              <el-input
                class="w-180 ps-input"
                placeholder="请输入退款金额"
                v-model="refundForm.refundMoney"
              ></el-input>
            </el-form-item>
          </div>
          <div v-if="refundType === 'part' && refundData.payway === 'PushiPay'">
            <el-form-item label="补贴钱包：" prop="refundSubsidyMoney">
              <el-input
                class="w-180 ps-input"
                :placeholder="'可退金额<' + refundData.part_subsidy_fee"
                :disabled="!Number(refundData.part_subsidy_fee)"
                v-model="refundForm.refundSubsidyMoney"
              ></el-input>
            </el-form-item>
            <el-form-item label="储值钱包：" prop="refundWalletMoney">
              <el-input
                class="w-180 ps-input"
                :placeholder="'可退金额<' + refundData.part_wallet_fee"
                :disabled="!Number(refundData.part_wallet_fee)"
                v-model="refundForm.refundWalletMoney"
              ></el-input>
            </el-form-item>
            <el-form-item label="赠送钱包：" prop="refundComplimentaryMoney">
              <el-input
                class="w-180 ps-input"
                :placeholder="'可退金额<' + refundData.part_complimentary_fee"
                :disabled="!Number(refundData.part_complimentary_fee)"
                v-model="refundForm.refundComplimentaryMoney"
              ></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="备注：">
              <el-input v-model="refundData.remark" class="ps-input w-250" maxlength="100"></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <el-dialog width="30%" title="温馨提示" customClass="ps-dialog" :visible.sync="tipVisible" append-to-body top="280px">
        <p class="twoRefund" v-if="refundType === 'all'" style="font-size: 20px;">
          确定要对该订单进行退款吗
        </p>
        <p class="twoRefund" v-else style="font-size: 20px;">
          确定要对该订单进行
          <span style="font-weight: bold;">部分退款吗?</span>
        </p>
        <p class="twoRefund" style="color:#E0364C;">温馨提示: 确定后不可撤销</p>
        <div slot="footer" class="footer-btn">
          <el-button class="ps-cancel-btn" @click="tipVisible = false">取消</el-button>
          <el-button class="ps-btn" :disabled="dialogLoading" @click="handleConfirm">确定</el-button>
        </div>
      </el-dialog>
      <div slot="footer">
        <el-button class="ps-cancel-btn" @click="refundVisible = false">取 消</el-button>
        <el-button class="ps-btn" @click="handleRefund">确 定</el-button>
      </div>
      </el-dialog>
  </div>
</template>
<script>
import { debounce, deepClone, divide, times } from '@/utils'
import { confirm } from '@/utils/message'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { ACCOUNTIMG_SEARCH, PAYABLE_SEARCH, ACCOUNTIMG_TABLE, PAYABLE_TABLE, ACCOUNTIMG_COLLECT, PAYABLE_COLLECT, getRequestParams } from './constants'
import { mapGetters } from 'vuex'
export default {
  name: "MealApplyOrder",
  mixins: [exportExcel, report],
  data() {
    let validataRefundMoney = (rule, value, callback) => {
      let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      let price
      console.log(price)
      if (rule.field === 'refundWalletMoney') {
        price = this.refundData.part_wallet_fee
      } else if (rule.field === 'refundSubsidyMoney') {
        price = this.refundData.part_subsidy_fee
      } else if (rule.field === 'refundComplimentaryMoney') {
        price = this.refundData.part_complimentary_fee
      } else if (rule.field === 'refundMoney') {
        price = this.refundData.part_net_fee
      }
      if (value) {
        if (Number(value) === 0) {
          callback(new Error('金额不能为0'))
        } else if (value >= Number(price)) {
          callback(new Error('金额不能大于等于可退金额'))
        } else if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      tableType: 'JZ',
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: {},
      searchFormSetting: {},
      currentTableSetting: [],
      collect: {},
      selectListId: [],
      printType: 'JZMealApplyOrder',
      refundVisible: false,
      refundData: {},
      refundType: 'all',
      refundForm: {
        refundMoney: '',
        refundSubsidyMoney: '',
        refundWalletMoney: '',
        refundComplimentaryMoney: ''
      },
      refundFormRules: {
        refundMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }],
        refundWalletMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }],
        refundSubsidyMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }],
        refundComplimentaryMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }]
      },
      tipVisible: false,
      dialogLoading: false
    }
  },
  computed: {
    ...mapGetters(['allPermissions'])
  },
  watch: {
    allPermissions: {
      handler: function(newVal, oldVal) {
        if (newVal !== oldVal) {
          if (newVal.includes('background_approve.order_approve_visitor.jz_list') && !newVal.includes('background_approve.order_approve_visitor.jf_list')) {
            this.tableType = 'JZ'
          } else if (!newVal.includes('background_approve.order_approve_visitor.jz_list') && newVal.includes('background_approve.order_approve_visitor.jf_list')) {
            this.tableType = 'JF'
          }
        } else {
          if (oldVal.includes('background_approve.order_approve_visitor.jz_list') && !oldVal.includes('background_approve.order_approve_visitor.jf_list')) {
            this.tableType = 'JZ'
          } else if (!oldVal.includes('background_approve.order_approve_visitor.jz_list') && oldVal.includes('background_approve.order_approve_visitor.jf_list')) {
            this.tableType = 'JF'
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.tableType === 'JZ') {
        this.currentPage = 1
        this.searchFormSetting = deepClone(ACCOUNTIMG_SEARCH)
        this.tableSetting = deepClone(ACCOUNTIMG_TABLE)
        this.collect = deepClone(ACCOUNTIMG_COLLECT)
        this.printType = 'JZMealApplyOrder'
      } else if (this.tableType === 'JF') {
        this.currentPage = 1
        this.searchFormSetting = deepClone(PAYABLE_SEARCH)
        this.tableSetting = deepClone(PAYABLE_TABLE)
        this.collect = deepClone(PAYABLE_COLLECT)
        this.printType = 'JFMealApplyOrder'
      }
      this.initPrintSetting()
      this.getVisitorOrder()
    },
    changeTableType(type) {
      this.tableType = type
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.printDialogVisible = false
      this.printTicketVisible = false
      this.getVisitorOrder()
    }, 300),
    async getVisitorOrder() {
      this.isLoading = true
      let api
      if (this.tableType === 'JF') {
        api = this.$apis.apiBackgroundApproveOrderApproveVisitorJfListPost
      } else if (this.tableType === 'JZ') {
        api = this.$apis.apiBackgroundApproveOrderApproveVisitorJzListPost
      }
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await api(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getVisitorOrder()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getVisitorOrder()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => { this.selectListId.push(item.id) })
    },
    // 操作
    mulOperation() {
      if (!this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      confirm({ content: '确定所选订单已对账？' }).then(e => {
        this.confirmSettleorder()
      })
    },
    async confirmSettleorder() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundApproveOrderApproveVisitorSettleOrderPost({
        ids: this.selectListId
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getVisitorOrder()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出报表
    handleExport() {
      let url
      if (this.tableType === 'JF') {
        url = 'apiBackgroundApproveOrderApproveVisitorJfListExportPost'
      } else if (this.tableType === 'JZ') {
        url = 'apiBackgroundApproveOrderApproveVisitorJzListExportPost'
      }
      let params = getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      const option = {
        type: 'MealVisitorOrder',
        url,
        params: params
      }
      this.exportHandle(option)
    },
    goteDetail(id) {
      this.$router.push({
        name: 'MerchantMealOrderDetail',
        query: {
          id
        }
      })
    },
    openDialog(type, data) {
      this.refundForm = {
        refundMoney: '', // 部分退款金额
        refundWalletMoney: '', // 部分退款金额
        refundSubsidyMoney: '', // 部分退款金额
        refundComplimentaryMoney: '' // 部分退款金额
      }
      this.refundData = deepClone(data)
      this.refundData.part_wallet_fee = divide(Math.abs(this.refundData.part_wallet_fee))
      this.refundData.part_subsidy_fee = divide(Math.abs(this.refundData.part_subsidy_fee))
      this.refundData.part_complimentary_fee = divide(Math.abs(this.refundData.part_complimentary_fee))
      this.refundData.part_net_fee = divide(Math.abs(this.refundData.part_net_fee))
      this.refundData.net_fee = divide(Math.abs(this.refundData.net_fee))
      this.refundVisible = true
      this.refundType = 'all'
    },
    changeRefundType() {
      if (this.refundType === 'part') {
        this.$refs.refundTable.clearSelection()
      }
    },
    handleRefund() {
      if ((this.refundType === 'part' && this.refundData.payway === 'PushiPay' &&
      !this.refundForm.refundWalletMoney && !this.refundForm.refundSubsidyMoney && !this.refundForm.refundComplimentaryMoney) ||
      (this.refundType === 'part' && this.refundData.payway !== 'PushiPay' && !this.refundForm.refundMoney)) {
        return this.$message.error("请输入退款金额")
      }
      this.$refs.refundFormRef.validate(valid => {
        if (valid) {
          this.tipVisible = true
        }
      })
    },
    handleConfirm() {
      let params = {
        trade_no: this.refundData.trade_no
      }
      if (this.refundType === 'all') {
        params.refund_fee = this.refundData.pay_fee
      } else {
        if (this.refundData.payway === 'PushiPay') {
          params.refund_wallet_fee = times(this.refundForm.refundWalletMoney)
          params.refund_subsidy_fee = times(this.refundForm.refundSubsidyMoney)
          params.refund_complimentary_fee = times(this.refundForm.refundComplimentaryMoney)
        } else {
          params.refund_fee = times(this.refundForm.refundMoney)
        }
      }
      if (this.refundData.remark) {
        params.remark = this.refundData.remark
      }
      this.orderRefund(params)
    },
    async orderRefund(params) {
      if (this.dialogLoading) return
      this.dialogLoading = true
      const res = await this.$apis.apiBackgroundApproveOrderApproveVisitorRefundPost(params)
      this.dialogLoading = false
      if (res.code === 0) {
        this.refundVisible = false
        this.tipVisible = false
        this.getVisitorOrder()
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.MealApplyOrder{
  .table-type{
    padding: 20px 0;
    display: flex;
    font-size: 16px;
    .table-type-btn{
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      background-color: #E8F0F8;
      border-radius: 40px;
      margin-right: 20px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn{
      color: #FFF;
      background-color: #ff9b45;
      border: none;
    }
  }
  .tips{
    color: #ff9b45;
    font-size: 14px;
    margin-top: 15px;
  }
  .refund-dialog {
    .refund-radio{
      margin: 25px 0 10px;
    }
    .refund-info{
      line-height: 40px;
      .inline-box{
        display: inline-block;
      }
      .refund-info-item{
        min-width: 150px;
      }
    }
  }
}
</style>
