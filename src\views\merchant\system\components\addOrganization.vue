<template>
  <div class="container-wrapper super-add-organization is-fixed-footer">
    <div v-if="operate === 'add'" class="add-title">添加组织层级</div>
    <!-- tab end -->
    <el-form
      ref="organizationFormRef"
      v-loading="isLoading"
      :rules="formDataRuls"
      :model="formData"
      class="organization-form-wrapper"
      size="small"
    >
      <div class="l-title clearfix">
        <span class="float-l min-title-h">基本信息</span>
        <!-- v-permission="['background_organization.organization.modify']" -->
        <el-button v-if="!checkIsFormStatus" @click="changeOperate" size="mini" class="float-r">编辑</el-button>
      </div>
      <div class="item-box clearfix">
        <div v-if="labelName" class="item-b-l">{{ labelName }}</div>
        <div :class="{'item-b-r': labelName}">
          <el-form-item class="block-label" label="组织名称：" prop="name">
            <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.name"></el-input>
            <div class="item-form-text" v-else>{{formData.name}}</div>
          </el-form-item>
        </div>
      </div>
      <!-- 当前组织层次 -->
      <el-form-item class="block-label form-item-box" label="所属层级：" prop="levelName">
        <!-- <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.levelName"></el-input> -->
        <el-select v-if="checkIsFormStatus && formOperate === 'add'"  v-model="formData.levelTag" placeholder="" class="ps-select" style="width:100%;">
          <el-option v-for="option in levelList" :key="option.level" :label="option.name" :value="option.level"></el-option>
        </el-select>
        <div v-else class="item-form-text">{{formData.levelName}}</div>
      </el-form-item>

        <el-row class="form-item-row-box" :gutter="24">
          <!-- 行业性质 -->
          <el-col class="block-label form-item-box" :span="12">
            <el-form-item label="行业性质：" prop="industry">
              <el-select
                v-model="formData.industry"
                placeholder="请选择行业性质"
                class="ps-select"
                style="width: 100%;"
                size="small"
                :disabled="!checkIsFormStatus"
              >
                <el-option
                  v-for="item in industryTypeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 所在地址 -->
          <el-col class="block-label form-item-box" :span="12">
            <el-form-item label="所在地址：" prop="district">
              <el-cascader
                size="small"
                :options="addrOptions"
                v-model="formData.district"
                style="display: block;"
                :disabled="!checkIsFormStatus"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
      <!-- parentData.level === 5 || treeData.level === 5 -->
      <!-- <el-form-item v-if="formData.levelTag === 5" class="block-label form-item-box" label="退款密码：" prop="refundPassword">
        <el-input type="password" :disabled="!checkIsFormStatus" placeholder="不填则使用默认" class="ps-input" size="small" v-model="formData.refundPassword"></el-input>
      </el-form-item> -->

      <div class="form-line"></div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">联系方式</span>
      </div>

      <el-row class="form-item-row-box" :gutter="24">
        <!-- 联系人 -->
        <el-col :span="12">
          <el-form-item class="block-label" label="联系人：" prop="contact">
            <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.contact"></el-input>
            <div class="item-form-text" v-else>{{formData.contact}}</div>
          </el-form-item>
        </el-col>
        <!-- 手机号码 -->
        <el-col :span="12">
          <el-form-item class="block-label" label="手机号码：" prop="mobile">
            <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.mobile"></el-input>
            <div class="item-form-text" v-else>{{formData.mobile}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!infoData.level_tag">
          <el-form-item label="自注册设置：" prop="contact">
            <span>{{infoData.allow_register?'是':'否'}}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="form-line" v-if="this.operate !== 'add' && !infoData.level_tag"></div>
      <div class="l-title clearfix" v-if="this.operate !== 'add' && !infoData.level_tag">
        <span class="float-l min-title-h">客服配置</span>
      </div>

      <el-row class="form-item-row-box" :gutter="24" v-if="this.operate !== 'add' && !infoData.level_tag">
        <el-col :span="12">
          <el-form-item class="block-label" label="客服电话：" prop="serviceAgentMobile">
            <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.serviceAgentMobile" :placeholder="'请输入联系方式（座机号/手机号）'"></el-input>
            <div class="item-form-text" v-else>{{formData.serviceAgentMobile}}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="block-label" prop="kefuWeChat">
            <template #label>
              <span>客服微信：</span>
              <span class="m-l-10 font-size-12" style="color: #fda04d">支持上传图片文件，不大于2M。</span>
            </template>
            <div>
              <div class="flex-between">
                <el-upload
                  ref="upload"
                  :on-success="getSuccessUploadRes"
                  :before-upload="beforeUpload"
                  :action="serverUrl"
                  :on-remove="remove"
                  :headers="headersOpts"
                  :show-file-list="false"
                  :limit="1">
                  <el-button size="small" type="primary" :disabled="!checkIsFormStatus">点击上传</el-button>
                </el-upload>
                <transition name="el-fade-in-linear">
                  <div class="flex-b-c">
                    <div>
                      <el-image
                        v-if="this.fileList.length"
                        style="width: 36px; height: 24px; margin-top: 9px;"
                        :src="checkUrl"
                        :preview-src-list="fileList">
                      </el-image>
                    </div>
                    <el-button  v-if="hasPhoto && checkIsFormStatus" type="text" size="small" class="ps-warn" @click="remove">删除</el-button>
                  </div>
                </transition>
              </div>
              <transition name="el-fade-in-linear">
                <div class="file-list flex-b-c m-t-5" v-for="(item, index) in fileList" :key="index">
                  <span class="no-pointer">{{ cutOutStr(item) }}</span>
                  <i v-if="checkIsFormStatus" class="el-icon-circle-close m-l-10 pointer" @click="remove"></i>
                </div>
              </transition>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 备注 -->
      <!-- <el-form-item class="block-label form-item-box" label="备注：" prop="remark">
        <el-input v-if="checkIsFormStatus" class="ps-input" v-model="formData.remark" type="textarea" :rows="3"></el-input>
        <div class="item-form-text" v-else>{{formData.remark}}</div>
      </el-form-item> -->
      <div v-if="checkIsFormStatus" class="form-footer">
        <el-button @click="cancelFormHandle" size="small">取消</el-button>
        <!-- v-permission="['background.admin.organization.add', 'background.admin.organization.modify']" -->
        <el-button @click="sendFormdataHandle" class="ps-origin-btn" type="primary" size="small" >保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { to, debounce, camelToUnderline, getToken } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { regionData } from 'element-china-area-data'
import industryType from '@/assets/data/industryType.json'
import md5 from 'js-md5';
import { validateName } from '@/assets/js/validata'

export default {
  name: 'AddOrganization',
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: { // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    parentData: Object,
    treeData: Object,
    id: [String, Number],
    operate: String,
    restoreHandle: Function
  },
  data() {
    let validateTelphone = (rule, value, callback) => {
      if (!value) {
        if (this.infoData.level_tag === 0) {
          return callback(new Error('手机号不能为空'))
        } else {
          callback()
        }
      } else {
        let regTelphone = /^1[3456789]\d{9}$/
        if (!regTelphone.test(value)) {
          callback(new Error('请输入正确手机号'))
        } else {
          callback()
        }
      }
    }
    let validatePass = (rule, value, callback) => {
      let regPass = /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/
      // let regPass = /(^\w{6,32}$)/;
      if (value && value !== '********' && !regPass.test(value)) {
        callback(new Error("退款密码为数字与字母的组合，长度8到20位"));
      } else {
        callback();
      }
    };
    return {
      labelName: '',
      formOperate: 'detail',
      isLoading: false,
      industryTypeList: industryType,
      addrOptions: regionData,
      formData: {
        id: '',
        name: '',
        levelName: '',
        levelTag: '',
        district: [],
        contact: '',
        mobile: '',
        mailAddress: '',
        tel: '',
        industry: '',
        remark: '',
        serviceAgentImage: '',
        serviceAgentMobile: ''
        // refundPassword: ''
      },
      fileList: [],
      serverUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      checkUrl: require('@/assets/img/check.png'),
      hasPhoto: false,
      formDataRuls: {
        name: [{ required: true, message: '组织名称不能为空', trigger: "blur" },
          { validator: validateName, trigger: 'blur' }],
        level_name: [{ required: true, message: '层级名称不能为空', trigger: "blur" }],
        mobile: [{ validator: validateTelphone, trigger: "blur" }],
        refundPassword: [{ validator: validatePass, trigger: "blur" }],
        district: [{ required: true, message: '所在地址不能为空', trigger: ['blur', 'change'] }]
      },
      levelList: [],
      permissionTree: [],
      loadingThirdInfo: false
    }
  },
  computed: {
    // formOperate: {
    //   get() {
    //     return this.operate
    //   },
    //   set(val) {
    //     // this.$emit('update:operate', val)
    //   }
    // },
    // 检查当前状态，编辑还是详情
    checkIsFormStatus: function() { // 默认为false
      let show = false
      switch (this.operate) { // 目前从父组件传过来的操作类型只会有2个add和detail
        case 'add':
          show = true
          break;
        case 'detail':
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break;
        default: // 没传的话
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break;
      }
      return show
    }
  },
  watch: {
    operate: function(val, old) {
      if (!val) {
        this.formOperate = 'detail'
      }
      this.initLoad()
    },
    fileList: {
      handler: function (newVal, oldVal) {
        if (newVal.length) {
          this.hasPhoto = true
        } else {
          this.hasPhoto = false
        }
      },
      immediate: true
    }
  },
  created() {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    getSuccessUploadRes(res, file) {
      this.remove()
      if (res.code === 0) {
        this.formData.serviceAgentImage = res.data.public_url
        this.fileList.push(this.formData.serviceAgentImage)
      }
    },
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    },
    beforeUpload(file) {
      if (this.fileList.length) {
        this.fileList = []
      }
      let fileType = ['.jpg', '.jpeg', '.png', '.svg']
      if (!fileType.includes(this.getSuffix(file.name))) {
        this.$message.error('请上传后缀名为.jpg或.jpeg或.png或.svg的文件')
        return false
      }
      const isLt2M = (file.size / 1024 / 1024) <= 2
      if (!isLt2M) {
        return this.$message.error('上传附件大小不能超过 2M')
      }
    },
    remove() {
      this.$refs.upload.clearFiles()
      this.fileList = []
    },
    async initLoad() {
      if (this.operate) {
        this.formOperate = this.operate
      }
      if (this.operate === 'add') {
        await this.getLevelList(this.parentData.company)
        this.formData.parent = this.parentData.id
        // this.formData.permission = this.infoData.permission
        this.formData.company = this.parentData.company
      } else {
        await this.getLevelList(this.treeData.company)
        this.labelName = this.treeData.name.substring(0, 1)
        this.initInfoHandle()
      }
      console.log('this.infoData', this.infoData)
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
    }, 300),
    cutOutStr(str) {
      if (str) {
        const startIndex = str.lastIndexOf('/') + 1
        const endIndex = str.lastIndexOf('.')
        const fileName = str.substring(startIndex, endIndex)
        return fileName
      }
    },
    // 详情信息赋值
    initInfoHandle() {
      console.log('this.formData', this.formData)
      for (let key in this.formData) {
        let val = this.infoData[camelToUnderline(key)]
        switch (key) {
          case 'industry':
            this.formData[key] = val.toString()
            break;
          case 'district':
            this.formData[key] = JSON.parse(val)
            break;
          case 'level_tag':
            this.formData[key] = this.infoData.level_tag
            break;
          case 'refundPassword':
            // this.formData[key] = val
            if (val) {
              this.formData[key] = '********'
            }
            break;
          case 'serviceAgentImage':
            if (val) {
              this.fileList.push(val)
            }
            break;
          default:
            this.formData[key] = val
            break;
        }
      }
    },
    // 处理下没有children_list
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (_that.checkIsFormStatus) {
            item.isDisabled = false
          } else {
            item.isDisabled = true
          }
          if (item[key]) {
            if (item[key].length > 0) {
              traversal(item[key])
            } else {
              _that.$delete(item, key)
            }
          } else {
            _that.$delete(item, key)
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 获取指定公司的层级列
    async getLevelList(companyId) {
      let params = {}
      if (companyId) {
        params.company_id = companyId
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationGetLevelNameMapPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.levelList = []
        if (res.data.length > 0) {
          res.data.forEach(item => {
            if (item.level === (this.parentData.level_tag + 1)) {
              this.formData.levelName = item.name
              this.formData.levelTag = item.level
            }
            if (this.formOperate === 'add') {
              if (item.level > this.parentData.level_tag) {
                this.levelList.push(item)
              }
            } else {
              if (item.level >= this.treeData.level_tag) {
                this.levelList.push(item)
              }
            }
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    changeOperate() {
      switch (this.operate) { // 目前从父组件传过来的操作类型只会有2个add和detail
        case 'add': // noth
          break;
        default:
          if (this.formOperate === 'detail') {
            this.formOperate = 'modify'
          } else {
            this.formOperate = 'detail'
          }
          break;
      }
      // this.permissionTree = this.deleteEmptyChildren(this.permissionTree, 'children')
    },
    // 取消
    cancelFormHandle() {
      if (this.operate === 'add') {
        this.$refs.organizationFormRef.resetFields() // 重置表单数据
      } else {
        this.$refs.organizationFormRef.clearValidate() // 清空表单校验
        this.formOperate = 'detail'
        // this.permissionTree = this.deleteEmptyChildren(this.permissionTree, 'children')
      }
      this.restoreHandle(this.type, this.formOperate)
    },
    // 发送请求
    sendFormdataHandle() {
      this.$refs.organizationFormRef.validate(valid => {
        if (valid) {
          if (this.operate === 'add') { // 添加
            this.addOrganization(this.formatData())
          } else { // 编辑
            this.modifyOrganization(this.formatData())
          }
        }
      })
    },
    // 格式化下传给后台的参数
    formatData() {
      let params = {
        status: 'enable'
      }
      console.log('this.formData', this.formData)
      for (let key in this.formData) {
        let val = this.formData[key]
        switch (key) {
          case 'district':
            val = JSON.stringify(val)
            break;
          case 'refundPassword':
            if (val && val !== '********') {
              val = md5(val)
            }
            break;
          case 'industry':
            if (!val) { // 给个默认
              val = '41'
            }
            break;
          case 'serviceAgentImage':
            val = this.fileList[0] || ''
            break
        }
        if (key === 'refundPassword') {
          console.log(val)
          if (val && val !== '********') {
            params[camelToUnderline(key)] = val
          }
        } else if (key !== 'levelName') {
          params[camelToUnderline(key)] = val
        }
        if (key === 'serviceAgentImage') {
          console.log('转换后的val', val)
        }
      }
      console.log('params', params)
      if (this.formOperate === 'modify') {
        params.company = this.treeData.company
        // params.level_tag = -1
      }
      return params
    },
    // 添加
    async addOrganization(params) {
      this.isLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.formOperate = 'detail'
        this.$message.success('添加成功')
        this.$refs.organizationFormRef.clearValidate() // 清空表单校验
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑
    async modifyOrganization(params) {
      this.isLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.formOperate = 'detail'
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.super-add-organization {
  position: relative;
  .add-title{
    font-size: 18px;
    text-align: center;
    font-family: 600;
  }
  .min-title-h{
    height: 28px;
    line-height: 28px;
  }
  .item-box{
    // display: flex;
    padding: 10px 0 0 !important;
    .item-b-l{
      // display: flex;
      // justify-content: center;
      // align-items: center;
      float: left;
      width: 56px;
      height: 56px;
      line-height: 56px;
      text-align: center;
      vertical-align: middle;
      background-color: #ff9b45;
      border-radius: 8px;
      font-size: 30px;
      letter-spacing: 2px;
      color: #ffffff;
    }
    .item-b-r{
      margin-left: 76px;
    }
    .item-text-box{
      display: flex;
      padding: 5px 0;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 24px;
      letter-spacing: 1px;
      color: #23282d;
      .item-label{
        opacity: 0.7;
      }
      .item-text{
        flex: 1;
      }
    }
  }
  .organization-form-wrapper {
    width: 100%;
    max-width: 800px;
    .block-label{
      width: 100%;
      .el-form-item__label{
        display: block;
        text-align: left;
        line-height: 1.5;
        float: none;
      }
    }
    .form-line{
      width: 100%;
      height: 1px;
      background-color: #e0e6eb;
    }
    .block-center{
      text-align: center;
    }
    .item-form-text{
      padding: 0 15px;
      color: #23282d;
      font-weight: bold;
      min-height: 32px;
      border: 1px solid #e0e6eb;
      opacity: 0.7;
      background-color: #fff;
    }
  }
  .form-footer{
    text-align: center;
    .el-button{
      width: 120px;
    }
  }
  &.is-fixed-footer{
    // padding-bottom: 30px;
    .form-footer{
      margin-top: 30px;
      // position: fixed;
      // padding: 10px 20px;
      // left: 263px;
      // right: 40px;
      // bottom: 0;
      // background-color: #fff;
      // box-shadow: -4px 0px 6px 0px rgba(214, 214, 214, 0.6);
      // z-index: 99;
    }
  }
  .file-list {
    border-radius: 8px;
    border: 1px solid #DCDFE6;
    padding: 5px 10px;
  }
}
</style>
