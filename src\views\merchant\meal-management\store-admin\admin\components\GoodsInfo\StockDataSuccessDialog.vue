<template>
  <div class="StockDataSuccessDialog">
    <!-- 成功的 -->
    <el-dialog
      title="入库成功"
      :visible.sync="visible"
      top="20vh"
      custom-class="ps-dialog"
      show-close
      :close-on-click-modal="false"
    >
      <el-table
        style="width: 100%"
        border
        :data="stockListData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
        header-row-class-name="ps-table-header-row"
        class="ps-table"
      >
        <el-table-column prop="index" label="序号" width="70" align="center"></el-table-column>
        <el-table-column prop="name" label="商品名称" align="center"></el-table-column>
        <el-table-column prop="spec" label="规格" align="center"></el-table-column>
        <el-table-column prop="barcode" label="条码" align="center" width="180"></el-table-column>
        <el-table-column prop="before_stock_num" label="原库存" align="center"></el-table-column>
        <el-table-column prop="add_stock_num" label="当前入库数量" align="center"></el-table-column>
        <el-table-column prop="stock_num" label="现库存" align="center"></el-table-column>
      </el-table>
      <div class="pageSizeItem ps-pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-size="pageSize"
          layout="prev, pager, next, total, jumper"
          :total="stockListData.length"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="canceDialogHandle">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    isshow: Boolean,
    type: {
      type: String,
      default: ''
    },
    stockListData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {},
  methods: {
    canceDialogHandle() {
      this.visible = false
    },
    handleSizeChange(val) {
      this.pageSize = val
      // this.currentPageLoad = 1; // 改变页面大小后，跳转回第一页
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style lang="scss" scoped>
.StockDataSuccessDialog {
}
</style>
