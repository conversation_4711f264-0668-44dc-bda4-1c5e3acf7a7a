<template>
  <div v-loading="isLoading" class="delivery-order-box">
    <div class="info-wrap">
      <div class="title">基本信息</div>
      <div class="form-item"><span class="form-label">供应商名称：</span>{{ infoData.supplier_manage_name }}</div>
      <div class="form-item"><span class="form-label">单据编号：</span>{{ detailData.trade_no }}</div>
      <div class="form-item"><span class="form-label">配送日期：</span>{{ detailData.delivery_date }}</div>
      <div class="form-item"><span class="form-label">预计送达时间：</span>{{ detailData.expect_arrival_date }}</div>
      <div class="form-item"><span class="form-label">配送状态：</span>{{ detailData.order_status_alias }}</div>
      <div class="form-item"><span class="form-label">创建时间：</span>{{ detailData.create_time }}</div>
    </div>
    <div class="info-wrap">
      <div class="title">配送信息</div>
      <div class="form-item"><span class="form-label">配送温度：</span>{{ detailData.delivery_temperature }}°c</div>
    </div>
    <!-- 司机信息 -->
    <DriverInformation :driver-list="detailData.driver_info"></DriverInformation>
    <!-- 车辆信息 -->
    <VehicleInformation :vehicle-list="detailData.vehicle_info"></VehicleInformation>

    <div class="info-wrap">
      <div class="title">物资信息</div>
      <div v-if="type !== 'detail'">合计金额：￥{{ totalPrice }}</div>
      <el-table
        :data="detailData.ingredient_data"
        ref="tableData"
        style="width: 100%"
        stripe
        size="mini"
        max-height="600"
        header-row-class-name="ps-table-header-row"
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item">
          <template #qualityTesting="{ row }">
            <el-button
              type="text"
              size="small"
              class="ps-text"
              @click="clickViewerHandler(row.inspection_report, 0)"
            >
              质检报告
            </el-button>
          </template>
        </table-column>
      </el-table>
    </div>
    <image-viewer v-model="showViewer" :initial-index="imgIndex" :z-index="3000" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
  </div>
  <!-- end -->
</template>

<script>
import { divide } from '@/utils'
import VehicleInformation from './VehicleInformation'
import DriverInformation from './DriverInformation'
import NP from 'number-precision'

export default {
  name: 'DeliveryOrderBox',
  props: {
    type: { //
      type: String,
      default: 'detail'
    },
    params: {
      type: Object,
      default: () => {}
    },
    api: {
      type: String,
      default: 'apiBackgroundDrpVendorDataVendorDeliveryDetailListPost'
    },
    infoData: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    VehicleInformation,
    DriverInformation
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '配送数量', key: 'purchase_count' },
        { label: '规格', key: 'specification_record' },
        { label: '成本价', key: 'unit_price' },
        { label: '合计金额', key: 'total_price' },
        { label: '保质期', key: 'valid_date' },
        { label: '质检报告', key: 'qualityTesting', type: 'slot', slotName: 'qualityTesting'  },
      ],
      detailData: {},
      totalPrice: 0, // 合计金额
      // 图片
      imgIndex: 0,
      previewSrcList: [],
      showViewer: false
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getDataInfo()
    },
    async getDataInfo() {
      if (this.isLoading) return
      let currentApi = this.api
      if (!this.api && this.type === 'detail') {
        currentApi = 'apiBackgroundDrpVendorDataVendorDeliveryDetailListPost'
      }
      if (!currentApi) {
        return this.$message.error('缺少参数，请检查！')
      }
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis[currentApi](this.params))
      this.isLoading = false
      this.detailData = {}
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        this.totalPrice = 0
        // eslint-disable-next-line camelcase
        if (data?.ingredient_data) {
          data.ingredient_data = data.ingredient_data.map((v) => {
            this.totalPrice = NP.plus(this.totalPrice, v.total_price)
            v.purchase_count = v.purchase_count + v.purchase_unit
            v.unit_price = "￥" + divide(v.unit_price)
            v.total_price = "￥" + divide(v.total_price)
            v.valid_date = v.start_valid_date + "-" + v.end_valid_date
            return v
          })
        }
        this.totalPrice = divide(this.totalPrice)
        this.detailData = data || {}
      } else {
        this.$message.error(res.msg)
      }
    },
    clickViewerHandler(row, index) {
      // don't show viewer when preview is false
      this.previewSrcList = row || []
      this.imgIndex = index
      this.showViewer = true;
    },
    closeViewer() {
      this.showViewer = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.delivery-order-box{
  .info-wrap{
    font-size: 14px;
    line-height: 30px;
    .title{
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0 10px;
    }
    .form-item {
      display: flex;
      font-size: 14px;
      line-height: 30px;
    }
    .form-img-box {
      flex: 1;
    }
    .detault-img {
      width: 160px;
      height: 92px;
      border: 1px dashed #e1e1e1;
      border-radius: 2px;
    }
  }
}
</style>
