/* eslint-disable no-unused-vars */
import {
  setSessionStorage,
  getSessionStorage,
  removeSessionStorage,
  sleep
} from '@/utils'

const state = {
  showSettings: (process.env.NODE_ENV === 'development') || false, // 是否显示设置工具栏
  isCollapse: getSessionStorage('isCollapse') || false,
  baseUrl: getSessionStorage('baseUrl') || '', // 请求地址
  formData: {} // form表单数据
}

const mutations = {
  SET_COLLAPSE: (state, isCollapse) => {
    state.isCollapse = isCollapse
  },
  SET_SHOWSETTINGS: (state, showSettings) => {
    state.showSettings = showSettings
  },
  SET_BASEURL: (state, baseUrl) => {
    state.baseUrl = baseUrl
  },
  SET_FORMDATA: (state, formData) => {
    state.formData = formData
  }
}

const actions = {
  // 设置是否显示侧栏小图标
  setCollapse({ commit, dispatch }, data) {
    return new Promise((resolve, reject) => {
      // dispatch('setUserInfo', data)
      commit('SET_COLLAPSE', data)
      resolve()
    })
  },
  // 设置是否显示设置栏
  setShowSettings({ commit, dispatch }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_SHOWSETTINGS', data)
      resolve()
    })
  },
  // 设置默认请求地址
  setBaseUrl({ commit, dispatch }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_BASEURL', data)
      resolve()
    })
  },
  // 设置表单数据
  setFormData({ commit, dispatch }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_FORMDATA', data)
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
