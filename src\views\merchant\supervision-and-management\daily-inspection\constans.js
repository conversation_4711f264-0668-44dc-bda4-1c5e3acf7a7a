
import * as dayjs from 'dayjs'
export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const TABLE_HEAD_SETTING = [
  { label: '巡查时间', key: 'create_time', width: "150px", type: "slot", slotName: "create_time" },
  { label: '组织名称', key: 'org_name' },
  { label: '巡查项目', key: 'project' },
  { label: '巡查人员', key: 'name', type: "slot", slotName: 'name' },
  { label: '巡查凭证', key: 'image_json', type: "slot", slotName: "image_json" },
  { label: '巡查结果', key: 'result', align: 'left' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
]

export const SEARCH_FORM_SETTING = {
  select_time: {
    type: 'daterange',
    format: 'yyyy-MM-dd',
    label: '日期筛选',
    value: recentSevenDay,
    clearable: false
  },
  organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '组织名称',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  project: {
    type: 'select',
    label: '巡查项目',
    value: '',
    placeholder: '默认为空',
    listNameKey: 'label',
    listValueKey: 'value',
    multiple: true,
    collapseTags: true,
    dataList: [],
    clearable: true
  },
  name: {
    type: 'input',
    label: '巡查人员',
    value: '',
    placeholder: '请输入巡查人员'
  }
}
