<template>
  <div class="ps-el-drawer">
    <el-drawer :title="title" :visible="visible" :show-close="false" :size="width" >
      <div class="p-20" v-loading="loading">
        <div class="ps-red" v-if="isEdit"> 预警信息由教育局配置，请配合预警项目进行经营资金的上传。</div>
        <div class="ps-red" v-if="!isEdit"> 当前组织未被监管渠道绑定，预警功能请自行配置</div>
        <el-form :model="dialogForm" ref="addDataForm" :rules="addDataFormRules">
          <div class="form-box">
            <el-form-item label="利润率预警：" label-width="150px" prop='surplus_val'>
              <div class="ps-flex">
                <span>盈余阙值</span>
                <el-input class="el-width-100" v-model="dialogForm.surplus_val"  :disabled="isEdit"/>
                <span>%</span>
                <span class="tips m-l-10">超过该值时触发预警</span>
              </div>
            </el-form-item>
            <el-form-item label="" label-width="150px" prop='loss_val'>
              <div class="ps-flex">
                <span>亏损阙值</span>
                <el-input class="el-width-100" v-model="dialogForm.loss_val"  :disabled="isEdit"/>
                <span>%</span>
                <span class="tips m-l-10">超过该值时触发预警</span>
              </div>
            </el-form-item>
            <el-form-item label="预警周期：" label-width="150px">
              <div class="ps-flex">
                <span>每月预警时间</span>
                <el-select class="el-width-100" v-model="dialogForm.time" placeholder="请选择" :disabled="isEdit">
                  <el-option v-for="(item, index) in 31" :key="index" :label="`${item}日`" :value="item" />
                </el-select>
                <span class="tips m-l-10">选择当月日期预警上月的数据</span>
              </div>
            </el-form-item>
          </div>
          <div class="form-box">
            <el-form-item label="原材料支出预警：" label-width="150px" prop='threshold'>
              <div class="ps-flex">
                <span>阙值</span>
                <el-input class="el-width-100" v-model="dialogForm.threshold" :disabled="isEdit"/>
                <span>%</span>
                <span class="tips m-l-10">超过该值时触发预警</span>
              </div>
            </el-form-item>
            <el-form-item label="预警周期：" label-width="150px">
              <div class="ps-flex">
                <span>每月预警时间</span>
                <el-select class="el-width-100" v-model="dialogForm.timeRaw" placeholder="请选择" :disabled="isEdit">
                  <el-option v-for="(item, index) in 31" :key="index" :label="`${item}日`" :value="item" />
                </el-select>
                <span class="tips m-l-10">选择当月日期预警上月的数据</span>
              </div>
            </el-form-item>
          </div>
        </el-form>
        <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
          <div class="m-r-30">
            <el-button size="small" class="w-100" @click="clickCancleHandle" v-if="!isEdit">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="clickConfirmHandle" v-if="!isEdit"
              v-loading="confirmLoading">保存</el-button>
            <el-button size="small" type="primary" class="w-100" @click="clickCancleHandle" v-if="isEdit"
              v-loading="confirmLoading">关闭</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { to } from '@/utils'
import { validateNumberPosAndNeg } from "@/utils/form-validata.js"
export default {
  name: 'WarningTimeDialog',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'edit'
    },
    title: {
      type: String,
      default: '预警时间'
    },
    width: {
      type: String,
      default: '900px'
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        console.log('newVal', newVal)
        this.getWarnConfig()
      }
    }
  },
  data() {
    return {
      addDataFormRules: {
        surplus_val: [{ required: false, validator: validateNumberPosAndNeg, trigger: "blur" }],
        loss_val: [{ required: false, validator: validateNumberPosAndNeg, trigger: "blur" }],
        threshold: [{ required: false, validator: validateNumberPosAndNeg, trigger: "blur" }]
      },
      // 新增订单
      dialogForm: {
        surplus_val: "",
        loss_val: "",
        time: 0,
        threshold: "",
        timeRaw: 0
      },
      confirmLoading: false,
      loading: false,
      isEdit: false // 是否被绑 ,true  就是绑了不能改 ，false 能改。怎么叫这个名字，树森！！！
    }
  },
  methods: {
    // 取消
    clickCancleHandle() {
      console.log("clickCancleHandle")
      this.$refs.addDataForm.resetFields()
      this.visible = false
      this.$emit("closeDialog")
    },
    // 确认
    async clickConfirmHandle() {
      console.log("clickConfirmHandle", this.$refs.addDataForm)
      this.$refs.addDataForm.validate(async valid => {
        if (valid) {
          console.log("clickConfirmHandle 验证通过")
          this.savaSetting()
        }
      })
    },
    // 保存设置
    async savaSetting() {
      let params = {
        org_id: this.$store.getters.organization,
        profit_rate: {
          surplus_val: parseFloat(this.dialogForm.surplus_val),
          loss_val: parseFloat(this.dialogForm.loss_val),
          date: this.dialogForm.time === 31 ? 0 : this.dialogForm.time
        },
        raw_materials: {
          threshold: parseFloat(this.dialogForm.threshold),
          date: this.dialogForm.timeRaw === 31 ? 0 : this.dialogForm.timeRaw
        }
      }
      this.confirmLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionWarnManageBusinessWarningEditPost(params))
      this.confirmLoading = false
      if (err) {
        return
      }

      if (res && res.code === 0) {
        this.$message.success("保存成功")
        this.$emit("confirmDialog")
      } else {
        this.$message.error(res.msg || "保存失败")
      }
    },
    // 获取预警配置
    async getWarnConfig () {
      this.loading = true
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionWarnManageBusinessWarningDetailPost({}))
      this.loading = false
      if (err) {
        return
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        this.isEdit = data.is_edit || false
        let profitRate = data.profit_rate || {}
        if (profitRate) {
          this.$set(this.dialogForm, 'surplus_val', profitRate.surplus_val)
          this.$set(this.dialogForm, 'loss_val', profitRate.loss_val)
          this.$set(this.dialogForm, 'time', profitRate.date === 0 ? 31 : profitRate.date)
        }
        let rawMaterials = data.raw_materials || {}
        if (rawMaterials) {
          this.$set(this.dialogForm, 'threshold', rawMaterials.threshold)
          this.$set(this.dialogForm, 'timeRaw', rawMaterials.date === 0 ? 31 : rawMaterials.date)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-box {
  background-color: #F8F9FA;
  border-radius: 20px;
  padding: 10px;
  margin-top: 20px;
}

.el-width-100 {
  width: 100px !important;
  margin: 0 10px;
}

.el-width-180 {
  width: 180px !important;
  margin: 0 10px;
}
</style>
