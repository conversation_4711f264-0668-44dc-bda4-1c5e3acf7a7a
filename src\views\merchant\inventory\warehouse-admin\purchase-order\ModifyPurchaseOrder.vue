<template>
  <div class="add-procure form-container">
    <h3>{{ titleText }}</h3>
    <el-form
      v-loading="isLoading"
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="110px"
      size="small"
      class="m-l-20 m-t-10"
    >
      <el-form-item label="状态" prop="">
        <div>未发起</div>
      </el-form-item>
      <el-form-item label="采购日期" prop="purchase_time">
        <el-date-picker
          v-model="formData.purchase_time"
          type="date"
          placeholder="选择日期时间"
          value-format="yyyy-MM-dd"
          :picker-options="pickerPurchaseTimeOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="入库仓库" prop="warehouse_id">
        {{ queryData.warehouse_name }}
      </el-form-item>
      <el-form-item label="物资清单" prop="">
        <el-button class="ps-origin-btn w-100" @click="addMaterials('purchase')">新增</el-button>
        <el-button class="ps-origin-btn w-100" @click="openFormDialog('recipes')">选择菜谱</el-button>
        <!-- <el-button class="ps-origin-btn w-100" @click="addMaterials('inquiry')">选择询价单</el-button> -->
        <el-button class="ps-origin-btn w-100" @click="addMaterials('template')">选择模板</el-button>
        <el-button class="ps-origin-btn w-100" @click="openImport('add')">导入物资</el-button>
      </el-form-item>
      <el-form-item label="">
        <div style="width: 92%; margin-bottom: 10px">
          <el-table
            :data="currentMaterialsTableData"
            ref="tableRef"
            stripe
            size="small"
            border
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in materialsTableSettings" :key="item.key" :col="item" :width="item.width">
              <template #ingredientPurchase="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.purchase_count"
                  :prop="'materialsTableData[' + getMaterialsIndex(index) + '].purchase_count'"
                >
                  <div class="flex">
                    <el-input
                      v-model="row.purchase_count"
                      placeholder="请输入"
                      :maxlength="6"
                      class="ps-input"
                      @input="inputPirchaseHandle($event, index)"
                    ></el-input>
                    <span v-if="row.specs_item" style="margin-left: 2px;">{{ row.specs_item.unit_management_name }}</span>
                  </div>
                </el-form-item>
              </template>
              <template #supplier="{ row, index }">
                <el-form-item
                  label=""
                  :prop="'materialsTableData[' + getMaterialsIndex(index) + '].supplier_manage_id'"
                  :rules="formRules.supplier_manage_id"
                  class="m-b-0"
                >
                  <el-select v-model="row.supplier_manage_id" filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeSupplier($event, index)">
                    <el-option v-for="option in row.supplier_list" :key="option.supplier_manage_id" :label="option.supplier_manage_name" :value="option.supplier_manage_id" ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template #specs="{ row, index }">
                <el-form-item
                  label=""
                  :prop="'materialsTableData[' + getMaterialsIndex(index) + '].specs'"
                  :rules="formRules.specs"
                  class="m-b-0"
                >
                  <el-select v-model="row.material_specification_id" :disabled="!row.supplier_manage_id" clearable filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeSpecs($event, index)">
                    <el-option v-for="option in row.specification_list" :key="option.id" :label="`1${option.unit_management_name}*${option.count}${option.limit_unit_name}*${option.net_content}${option.net_content_unit}`" :value="option.id" ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <!-- <template #limitUnit="{ row }">
                <div v-if="row.specs_item">
                  <span>{{ row.specs_item.limit_unit_name }}</span>
                </div>
              </template>
              <template #unitPrice="{ row }">
                <div v-if="row.specs_item">
                  <span>￥{{ row.specs_item.unit_price | formatMoney }}</span>
                </div>
              </template> -->
              <template #purchasePrice="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.purchase_price"
                  :prop="'materialsTableData.' + index + '.purchase_price'"
                >
                  <el-input v-model="row.purchase_price" placeholder="请输入" class="ps-input" @change="changeValidateHandle(row)"></el-input>
                </el-form-item>
              </template>
              <template #operation="{ index }">
                <el-button type="text" size="small" class="ps-origin" @click.stop="deleteMaterials(index)">
                  删除
                </el-button>
              </template>
            </table-column>
          </el-table>
          <div v-if="errorTableData" class="red">{{ errorTableData }}</div>
          <pagination
            v-if="formData.materialsTableData.length > materialsPageSize"
            :onPaginationChange="onMaterialsPaginationChange"
            :current-page.sync="materialsPage"
            :page-size.sync="materialsPageSize"
            :layout="'total, prev, pager, next, jumper'"
            :total="formData.materialsTableData.length"
          ></pagination>
        </div>
      </el-form-item>
      <el-form-item label="合计">￥{{ materialsTotalPrice }}</el-form-item>
      <el-form-item label="上传附件">
        <file-upload
          ref="fileUploadRef"
          :fileList="formData.fileLists"
          type="enclosure"
          prefix="inventory"
          :show-file-list="false"
          accept=".jpeg,.jpg,.png,.bmp"
          :rename="false"
          :multiple="true"
          :limit="6"
          :before-upload="beforeUpload"
          @fileLists="getFileLists"
        >
          <template v-slot="scope">
            <!-- {{ scope }} -->
            <el-button :loading="scope.loading" class="ps-origin" size="small" type="text">
              上传{{ scope.loading ? '中' : '' }}
            </el-button>
          </template>
        </file-upload>
        <!-- <p style="color:#a5a5a5; line-height: 1.5;">附件不超过20M</p> -->
      </el-form-item>
      <el-form-item v-if="previewList.length">
        <el-collapse v-model="activeCollapse" style="max-width: 60%">
          <el-collapse-item :title="collapseTitle" name="1">
            <div class="img-item" v-for="(img, index) in previewList" :key="img + index">
              <el-image
                :preview-src-list="previewList"
                :initial-index="index"
                class="upload-img m-r-6"
                :src="img"
                fit="contain"
              ></el-image>
              <span class="img-tools">
                <!-- <i class="el-icon-zoom-in m-r-10"></i> -->
                <i class="el-icon-delete" @click.stop="deleteUploadImg(index)"></i>
              </span>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-form-item>
      <div class="m-l-40 m-t-60">
        <el-button class="ps-cancel-btn w-130" size="medium" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle('')">保存</el-button>
        <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle('draft')">存为草稿</el-button>
        <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle('template')">存为模板</el-button>
      </div>
    </el-form>
    <!-- 导入物资 -->
    <import-page-dialog
      ref="importPageRef"
      :show.sync="showImportDialog"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :loading="importLoading"
      :isUpload="false"
      isDeleteFirst
      @confirm="confirmImportHandle"
    ></import-page-dialog>
    <!-- 添加物资/选择询价单 -->
    <choose-list-dialog
      :showdialog.sync="showChooseDialog"
      :title="dialogChooseTitle"
      :type="dialogChooseType"
      :api="dialogChooseApi"
      :detailApi="dialogChooseDetailApi"
      :search-setting="dialogChooseSearchSetting"
      :table-settings="dialogChooseTableSettings"
      :params="dialogChooseParams"
      :rowKey="dialogRowKey"
      showSelectLen
      @confirmChoose="confirmChooseHandle"
    ></choose-list-dialog>
    <!-- 保存草稿/模板 -->
    <form-dialog
      :showdialog.sync="showFormDialog"
      :type="dialogFormType"
      :api="dialogFormApi"
      :title="dialogFormTitle"
      :inputLabel="inputLabel"
      :params="dialogFormParams"
      @confirmForm="confirmFormHandle"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange, deepClone, getSuffix, getUrlFilename, parseTime, times, divide } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import { oneDecimal } from '@/utils/validata'
import { validateOneDecimalCount, validataPositiveNoZeroMoney } from '@/utils/form-validata'
import * as dayjs from 'dayjs'
import NP from 'number-precision'
// import DraftManageDialog from '../warehouse-admin/components/DraftManageDialog.vue'
import FormDialog from '../../components/FormDialog'
import ChooseListDialog from '../../components/ChooseListDialog'
import { downloadJsonExcel } from '@/utils/excel'

export default {
  name: 'AddProcure',
  mixins: [exportExcel],
  components: {
    // DraftManageDialog
    ChooseListDialog,
    FormDialog
  },
  data() {
    return {
      type: 'add',
      titleText: this.$route.params.type === 'modify' ? '编辑采购单' : '新建采购单',
      isLoading: false, // 刷新数据
      queryData: this.$route.query,
      // form表单数据
      formData: {
        warehouse_id: '',
        purchase_time: '', // 采购日期
        materialsTableData: [],
        fileLists: []
      },
      formRules: {
        purchase_time: [{ required: true, message: '请选择日期', trigger: 'change' }],
        warehouse_id: [{ required: true, message: '请选择入库仓库', trigger: 'change' }],
        purchase_count: [{ required: true, validator: validateOneDecimalCount, trigger: 'change' }],
        supplier_manage_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        purchase_price: [{ validator: validataPositiveNoZeroMoney, trigger: 'blur' }]
      },
      pickerPurchaseTimeOptions: {
        disabledDate(time) {
          return time.getTime() < dayjs().subtract(1, 'day').valueOf()
        }
      },
      // 入库物资
      materialsTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '供应商', key: 'supplier', type: 'slot', slotName: 'supplier', width: 180 },
        { label: '规格', key: 'material_specification_id', type: 'slot', slotName: 'specs', width: 170 },
        { label: '最小单位', key: 'purchase_unit' },
        { label: '采购数量', key: 'purchase_count', type: 'slot', slotName: 'ingredientPurchase', width: 100 },
        // { label: '采购价', key: 'ref_unit_price', type: 'money', prefix: '￥' },
        { label: '采购价', key: 'purchase_price', type: 'slot', slotName: 'purchasePrice', width: 100 },
        { label: '合计', key: 'total_price', type: 'money', prefix: '￥' },
        { label: '采购重量（kg）', key: 'purchase_weight' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      // 物资数据
      materialsTableData: [],
      currentMaterialsTableData: [],
      materialsPage: 1,
      materialsPageSize: 10,
      errorTableData: '',
      // 导入的弹窗数据
      importLoading: false,
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/采购单导入物资.xlsx',
      importHeaderLen: 2,
      importFailTableData: [], // 导入失败的数据
      importTableSettings: [
        { label: '物资名称', key: 'name' },
        { label: '导入失败原因', key: 'result' }
      ],
      // 选择物资/模板/询价单等弹窗
      showChooseDialog: false, // 是否开启弹窗
      dialogChooseLoading: false, // 弹窗loading
      dialogChooseTitle: '选择物资',
      dialogChooseType: '', // 弹窗的状态，add/modify
      dialogChooseData: {}, // 弹窗数据
      remoteChooseLoading: false,
      dialogChooseTableSettings: [],
      dialogChooseSearchSetting: {},
      dialogChooseParams: {
        warehouse_id: +this.$route.query.warehouse_id
      },
      dialogChooseApi: '1', // 请求的接口
      dialogChooseDetailApi: '',
      dialogRowKey: 'materials_id',
      // 保存为草稿、模板、选择菜谱等的弹窗
      showFormDialog: false,
      dialogFormTitle: '选择菜谱',
      dialogFormType: '1',
      dialogFormPagetype: '', // 哪个页面的功能
      dialogFormApi: '1',
      inputLabel: '',
      dialogFormParams: {},
      activeCollapse: []
    }
  },
  computed: {
    // 花销大，因为每次materialsTableData数据变化都会触发它重新计算
    // currentMaterialsTableData() {
    //   return this.materialsTableData.slice((this.materialsPage - 1) * this.materialsPageSize, this.materialsPage * this.materialsPageSize)
    // }
    previewList() {
      const result = this.formData.fileLists.map(v => v.url)
      this.setPreviewListTitle(result)
      return result
    },
    materialsTotalPrice() {
      let price = this.formData.materialsTableData.reduce((prev, next) => {
        return NP.plus(next.total_price, prev)
      }, 0)
      return divide(price)
    }
  },
  created() {
    this.type = this.$route.params.type || 'add'
    if (this.$route.query.warehouse_id) {
      this.formData.warehouse_id = this.$route.query.warehouse_id
    }
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.$route.query.type === 'recovery') {
        this.getdraftDetail(+this.$route.query.id)
      }
      if (this.$route.query.type === 'to_purchase_info') {
        this.getSubscriptionMaterials(this.$route.query.sg_trade_no)
      }
    },
    // 草稿箱详情
    async getdraftDetail(id) {
      if (!id) return this.$message.error('获取id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id,
        inventory_info_type: 'purchase'
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpPurchaseInfoDraftDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let result = res.data.extra
        this.formData.purchase_time = result.purchase_time ? parseTime(result.purchase_time, '{y}-{m}-{d}') : ''
        this.formData.materialsTableData = result.purchase_data.map(v => {
          v.purchase_price = divide(v.purchase_price)
          v.supplier_list = v.price_info
          v.specification_list = v.price_info.find(item => item.supplier_manage_id === v.supplier_manage_id).specification
          v.specs_item = v.specification_list.find(item => item.id === v.material_specification_id)
          return v
        })
        if (res.data.extra.image_json && res.data.extra.image_json.length > 0) {
          let fileImages = []
          res.data.extra.image_json.forEach((v, index) => {
            fileImages.push({
              url: v,
              name: getUrlFilename(v),
              status: "success",
              uid: new Date().getTime() + index
            })
          })
          this.formData.fileLists = fileImages
        } else {
          this.formData.fileLists = []
        }
        this.initCurrentTableMaterials()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取申购单物资
    async getSubscriptionMaterials(tradeNo) {
      if (!tradeNo) return this.$message.error('获取申购单单号失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        trade_no: tradeNo,
        warehouse_id: +this.$route.query.warehouse_id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoSubscribeInfoGetMaterialsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // this.formData.materialsTableData = result.purchase_data.map(v => {
        //   v.purchase_weight = ''
        //   return v
        // })
        // this.initCurrentTableMaterials()
        this.confirmChooseHandle({
          type: 'inquiry',
          data: res.data
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 初始化Materials分页数据
    initCurrentTableMaterials() {
      this.currentMaterialsTableData = this.formData.materialsTableData.slice(
        (this.materialsPage - 1) * this.materialsPageSize,
        this.materialsPage * this.materialsPageSize
      )
    },
    // 获取实际materialsTableData的index，这样做就不用重新遍历一次数据了
    getMaterialsIndex(index) {
      return (this.materialsPage - 1) * this.materialsPageSize + index
    },
    onMaterialsPaginationChange(val) {
      this.materialsPage = val.current
      this.materialsPageSize = val.pageSize
      this.initCurrentTableMaterials()
    },
    // 实际采购数量校验
    inputPirchaseHandle(e, currentIndex) {
      if (!oneDecimal(e)) {
        this.errorTableData = '请检查采购量'
        return
      } else if (e > 999999) {
        this.errorTableData = '采购数量最大不能超过6位数'
        return
      } else {
        this.errorTableData = ''
      }
      let current = this.formData.materialsTableData[this.getMaterialsIndex(currentIndex)]
      // let purchaseTotalPrice = (current.ref_unit_price) * (current.discounts / 100)
      if (current.purchase_price) {
        let purchaseTotalPrice = Math.round(NP.times(times(current.purchase_price), current.purchase_count))
        this.$set(current, 'total_price', purchaseTotalPrice)
      } else {
        this.$set(current, 'total_price', '')
      }
    },
    // 选择供应商
    changeSupplier(e, currentIndex) {
      let current = this.formData.materialsTableData[this.getMaterialsIndex(currentIndex)]
      let currentSupplier = current.supplier_list.find(v => v.supplier_manage_id === e)
      // 选择不同供应商的物资单价是不同的，需要对应切换
      // this.$set(current, 'ref_unit_price', currentSupplier.ref_unit_price)
      this.$set(current, 'supplier_manage_name', currentSupplier.supplier_manage_name)
      // let purchaseTotalPrice = (current.ref_unit_price) * (current.discounts / 100)
      // 合计也需要重新计算
      // purchase_count字段有可能为空字符串需要兼容处理下
      // let purchaseTotalPrice = NP.times(current.ref_unit_price, current.purchase_count || 0)
      // this.$set(current, 'total_price', purchaseTotalPrice)
      // this.$set(current, 'purchase_weight', currentSupplier.weight)
      // 设置下规格列表数据
      this.$set(current, 'specification_list', currentSupplier.specification || [])
      // 修改供应商得重置规格和单价、合计等数据
      this.$set(current, 'specs_item', '')
      this.$set(current, 'material_specification_id', '')
      this.$set(current, 'purchase_unit', '')
      this.$set(current, 'ref_unit_price', '')
      this.$set(current, 'purchase_price', '')
      this.$set(current, 'total_price', '')
      // 合并相同物资和相同供应商的数据
      this.formData.materialsTableData = this.uniqueMaterials(this.formData.materialsTableData, true)
      // 初始化下分页数据
      this.initCurrentTableMaterials()
    },
    // 修改规格
    changeSpecs(e, currentIndex) {
      let current = this.formData.materialsTableData[this.getMaterialsIndex(currentIndex)]
      let currentSpecs = current.specification_list.find(v => v.id === e)
      this.$set(current, 'specs_item', currentSpecs)
      this.$set(current, 'purchase_unit', `${currentSpecs.limit_unit_name}*${currentSpecs.net_content}${currentSpecs.net_content_unit}`) // 最小单位：例：瓶*330ml
      this.$set(current, 'ref_unit_price', currentSpecs.unit_price)
      this.$set(current, 'purchase_price', divide(currentSpecs.unit_price))
      if (current.purchase_price) {
        let purchaseTotalPrice = Math.round(NP.times(current.ref_unit_price, current.purchase_count))
        this.$set(current, 'total_price', purchaseTotalPrice)
      } else {
        this.$set(current, 'total_price', '')
      }
      // 合并相同物资和相同供应商的数据
      this.formData.materialsTableData = this.uniqueMaterials(this.formData.materialsTableData, true)
      // 初始化下分页数据
      this.initCurrentTableMaterials()
    },
    changeValidateHandle(current) {
      let purchaseTotalPrice = Math.round(NP.times(times(current.purchase_price), current.purchase_count))
      this.$set(current, 'total_price', purchaseTotalPrice)
      // 合并相同物资和相同供应商的数据
      this.formData.materialsTableData = this.uniqueMaterials(this.formData.materialsTableData, true)
      // 初始化下分页数据
      this.initCurrentTableMaterials()
    },
    // 添加物资
    addMaterials(type) {
      this.dialogChooseType = type
      // 初始化分页数据
      // this.initCurrentTableMaterials()
      // 选择物资弹窗
      if (type === 'purchase') {
        this.dialogChooseTitle = '添加物资'
        this.dialogChooseApi = 'apiBackgroundDrpMaterialsGetMaterialsListPost'
        this.dialogChooseParams = {
          warehouse_id: +this.$route.query.warehouse_id
        }
        this.dialogRowKey = 'materials_id'
        this.dialogChooseSearchSetting = {
          name: {
            type: 'input',
            value: '',
            label: '物资名称',
            placeholder: '请输入'
          }
        }
        this.dialogChooseTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '物资名称', key: 'materials_name' },
          // { label: '当前库存', key: 'current_num' },
          { label: '供应商', key: 'supplier', type: 'slot', slotName: 'supplier', width: 180 },
          { label: '规格', key: 'specs', type: 'slot', slotName: 'specs' },
          { label: '单价', key: 'unit_price', type: 'slot', slotName: 'unitPrice' },
          { label: '采购数量', key: 'count', type: 'slot', slotName: 'oneDecimalCount' },
          { label: '合计', key: 'total_price', type: 'money', prefix: '￥' }
        ]
      }
      // 选择模板
      if (type === 'template') {
        this.dialogChooseTitle = '模板管理'
        this.dialogChooseApi = 'apiBackgroundDrpTemplateInfoTempListPost'
        this.dialogChooseDetailApi = 'apiBackgroundDrpPurchaseInfoTempDetailsListPost'
        this.dialogChooseParams = {
          temp_type: 'template',
          inventory_info_type: 'purchase',
          warehouse_id: +this.$route.query.warehouse_id
        }
        this.dialogRowKey = 'id'
        this.dialogChooseSearchSetting = {}
        this.dialogChooseTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '模板名称', key: 'name' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
        ]
      }
      // 选择询价单
      if (type === 'inquiry') {
        this.dialogChooseTitle = '选择询价单'
        this.dialogChooseApi = 'apiBackgroundDrpInquiryListPost'
        this.dialogChooseDetailApi = 'apiBackgroundDrpInquiryInquiryGetMaterialsPost'
        this.dialogChooseParams = {
          temp_type: 'inquiry',
          inventory_info_type: 'purchase',
          warehouse_id: +this.$route.query.warehouse_id
        }
        this.dialogRowKey = 'id'
        this.dialogChooseSearchSetting = {
          date_type: {
            type: 'select',
            label: '',
            value: 'create_time',
            maxWidth: '130px',
            placeholder: '请选择',
            dataList: [
              {
                label: '创建时间',
                value: 'create_time'
              },
              {
                label: '发起时间',
                value: 'initiation_time'
              }
            ]
          },
          select_time: {
            type: 'daterange',
            format: 'yyyy-MM-dd',
            label: '',
            clearable: true,
            value: getSevenDateRange(7)
          }
        }
        this.dialogChooseTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '单据编号', key: 'trade_no' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
        ]
      }
      this.showChooseDialog = true
    },
    // 删除物资
    deleteMaterials(index) {
      this.formData.materialsTableData.splice(this.getMaterialsIndex(index), 1)
      if (this.formData.materialsTableData.length !== 0) {
        this.materialsPage = this.$computedTotalPageSize(
          this.formData.materialsTableData.length,
          this.materialsPageSize
        )
      } else {
        this.materialsPage = 1
      }
      // 初始化分页数据
      this.initCurrentTableMaterials()
    },
    // 表单类型的弹窗
    openFormDialog(type, row) {
      this.dialogFormType = type
      switch (type) {
        case 'recipes':
          this.dialogFormTitle = '选择菜谱'
          this.dialogFormApi = 'apiBackgroundDrpMaterialsMenuToMaterialsPost'
          this.dialogFormParams = {}
          break;
        case 'draft':
          this.dialogFormTitle = '存为草稿'
          this.inputLabel = '草稿名称'
          this.dialogFormApi = 'apiBackgroundDrpPurchaseInfoTempAddPost'
          this.dialogFormParams = this.setDialogFormParams(type)
          break;
        case 'template':
          this.dialogFormTitle = '创建模板'
          this.inputLabel = '模板名称'
          this.dialogFormApi = 'apiBackgroundDrpPurchaseInfoTempAddPost'
          this.dialogFormParams = this.setDialogFormParams(type)
          break;
      }
      this.showFormDialog = true
    },
    // 设置存为草稿或者存为模板的数据
    setDialogFormParams(type) {
      // inventory_info_type
      // purchase : "采购单"
      // entry_info : "入库单"
      // exit_info : "出库单"
      // return_info : "退货单"
      // inquiry : "询价单"
      let params = {
        inventory_info_type: 'purchase',
        warehouse_id: +this.formData.warehouse_id,
        temp_type: type,
        purchase_time: this.formData.purchase_time + " 00:00:00",
        image_json: this.previewList,
        purchase_data: this.formData.materialsTableData.map(v => {
          v.purchase_price = times(v.purchase_price)
          v.material_specification_record = `1${v.specs_item.unit_management_name}*${v.specs_item.count}${v.specs_item.limit_unit_name}*${v.specs_item.net_content}${v.specs_item.net_content_unit}`,
          v.limit_count_record = `${v.purchase_count*v.specs_item.count}${v.specs_item.limit_unit_name}`
          return v
        })
        // 需要保留供应商的筛选数据才行
        // .map(v => {
        //   let current = {}
        //   for (const key in v) {
        //     if (key !== 'supplier_list') {
        //       current[key] = v[key]
        //     }
        //   }
        //   return current
        // })
      }
      return params
    },
    // form弹窗
    confirmFormHandle(e) {
      if (e && e.type === 'recipes') {
        // 统一处理数据
        this.confirmChooseHandle(e)
      }
      // 存为草稿需要返回上一页哦
      if (e && e.type === 'draft') {
        this.$backVisitedViewsPath(this.$route.path, 'PurchaseOrderList')
      }
    },
    // 弹窗操作
    showDialogHandle(type, data, index) {
      if (!this.formData.warehouse_id) {
        return this.$message.error('请先选择仓库！')
      }
      this.dialogType = type
      if (type === 'importResult') {
        this.dialogTitle = '提示'
      }
      this.showDialog = true
    },
    // 根据物菜谱取市场询价
    async getMenuMarketIngredient(params) {
      this.dialogLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDrpPurchaseInfoGetMenuMarketIngredientPost(params))
      if (err) {
        // this.$message.error(err.message)
        return
      }
      this.dialogLoading = false
      if (res.code === 0) {
      } else {
        this.$message.error(res.msg)
      }
    },
    // 检查表单数据
    checkedFormData() {
      let nopass = false
      this.formData.materialsTableData.forEach(v => {
        if (!v.purchase_count || !v.supplier_manage_id) {
          nopass = true
        }
        if (!oneDecimal(v.purchase_count)) {
          nopass = true
        }
      })
      if (nopass) {
        this.errorTableData = '请检查采购数量，供应商等数据！'
      } else {
        this.errorTableData = ''
      }
      // nopass = false
      return !nopass
    },
    // 格式华参数
    formatParams() {
      let params = {
        warehouse_id: this.formData.warehouse_id,
        purchase_time: this.formData.purchase_time + " 00:00:00"
      }
      if (this.$route.query.type === 'to_purchase_info') {
        params.sg_trade_no = this.$route.query.sg_trade_no
      }
      console.log('this.formData.materialsTableData', this.formData.materialsTableData)
      params.purchase_data = this.formData.materialsTableData.map(v => {
        let current = {
          // 后端要保存的字段，放在最后保存这里，就不用每次更新数据都要去重新set了
          material_specification_record: `1${v.specs_item.unit_management_name}*${v.specs_item.count}${v.specs_item.limit_unit_name}*${v.specs_item.net_content}${v.specs_item.net_content_unit}`,
          limit_count_record: `${v.purchase_count * v.specs_item.count}${v.specs_item.limit_unit_name}`
        }
        for (const key in v) {
          if (key === 'purchase_count') {
            current[key] = Number(v[key])
          }
          if (key !== 'supplier_list') {
            current[key] = v[key]
          }
          if (key === 'purchase_price') {
            current[key] = times(v[key])
          }
        }
        current.ref_unit_price = current.purchase_price
        current.total_price = Math.round(v.total_price)
        return current
      })
      console.log('params.purchase_data', params.purchase_data)
      params.image_json = this.previewList
      return params
    },
    validateForm(type) {
      if (this.checkedFormData()) {
        let params = this.formatParams()
        console.log('v.total_price', params)
        if (!this.formData.materialsTableData.length) return this.$message.error('请先选择物资！')
        this.sendFormdata(params)
      } else {
        this.$message.error('请检查表单信息！')
      }
    },
    // 页面按钮点击
    async submitFormHandle(type) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (type) {
            this.openFormDialog(type)
          } else {
            this.validateForm()
          }
        } else {
          this.checkedFormData()
        }
      })
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$backVisitedViewsPath(this.$route.path, 'PurchaseOrderList')
            // this.$backVisitedViewsPath(this.$route.path, 'ProcureList')
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 发送数据
    async sendFormdata(params) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDrpPurchaseInfoAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        this.$backVisitedViewsPath(this.$route.path, 'PurchaseOrderList')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导入弹窗
    openImport(type) {
      if (!this.formData.warehouse_id) {
        return this.$message.error('获取仓库数据失败！')
      }
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    // 导入确定事件
    confirmImportHandle(data) {
      let importData = data
      // 删除示例数据
      // importData.splice(1, 1)
      console.log(111, importData)
      // importResult
      if (importData.length > 1) {
        // this.importLoading = true
        // 导入数据对应后端需要的字段
        const purchaseNameObject = {
          '物资名称': 'materials_name',
          '供应商': 'supplier_manage_name',
          '规格': 'spec_data',
          '采购数量': 'purchase_count',
          '采购价': 'purchase_price'
        }
        // 根据purchaseNameObject转换的index key
        let resultKey = {}
        importData[0].forEach((v, index) => {
          resultKey[index] = purchaseNameObject[v]
        })
        let result = []
        importData.forEach((item, index) => {
          if (index > 0) {
            let current = {}
            item.forEach((v, k) => {
              if (resultKey[k] === 'purchase_price') { // 金额要转分
                current[resultKey[k]] = times(v)
              } else {
                current[resultKey[k]] = v
              }
            })
            result.push(current)
          }
        })
        console.log(222, result)
        this.sendImportMaterials(result)
      } else {
        this.$message.error('导入物资为空！')
      }
    },
    // 发送导入物资数据给后端校验
    async sendImportMaterials(data) {
      if (this.importLoading) return
      this.importLoading = true
      let params = {
        data: data
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpPurchaseInfoImportMaterialsPost(params))
      // this.isLoading = false
      this.importLoading = false
      this.$refs.importPageRef.reset()
      this.showImportDialog = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data.success && res.data.success.length > 0) {
          let result = []
          res.data.success.map(v => {
            let item = {
              materials_name: v.materials_name,
              purchase_weight: 0,
              purchase_count: v.purchase_count || 0,
              purchase_unit: '',
              purchase_unit_id: '',
              ref_unit_price: '', // 参考价
              purchase_price: '', // 采购价
              total_price: 0, // 合计
              supplier_manage_id: v.supplier_id,
              supplier_manage_name: v.supplier_manage_name,
              supplier_list: v.price_info,
              materials_id: v.materials_id,
              material_specification_id: v.specification_id,
              specs_item: null,
              specification_list: []
            }
            let currentSupplier = v.price_info.find(current => current.supplier_manage_id === item.supplier_manage_id)
            if (currentSupplier) {
              item.supplier_manage_name = currentSupplier.supplier_manage_name
              item.specification_list = currentSupplier.specification // 供应商的规格列表
            }
            // 为了反显规格，需要拿到规格的具体数据
            if (item.material_specification_id && item.specification_list) {
              item.specs_item = item.specification_list.find(sp => sp.id === item.material_specification_id)
              item.ref_unit_price = item.specs_item.unit_price // 参考价
              item.purchase_price = Number(v.purchase_price) ? divide(v.purchase_price) : divide(item.specs_item.unit_price) // 采购价
              item.purchase_unit = `${item.specs_item.limit_unit_name}*${item.specs_item.net_content}${item.specs_item.net_content_unit}`
              item.purchase_unit_id = item.specs_item.limit_unit_id
              item.total_price = Math.round(NP.times(times(item.purchase_price), item.purchase_count))
            }
            result.push(item)
          })
          this.formData.materialsTableData = this.mergeArrays(this.formData.materialsTableData, result)
          this.initCurrentTableMaterials()
        }
        if (res.data.failure && res.data.failure.length > 0) {
          this.formatImportFailureResult(res.data.failure)
          this.$message.error('部分物资导入失败，请查看excel!')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化导入失败的数据，通过xlsx方式下载显示
    formatImportFailureResult(result) {
      const purchaseNameObject = {
        'materials_name': 0,
        'supplier_manage_name': 1,
        'spec_data': 2,
        'purchase_count': 3,
        'result': 4
      }
      let failureJson = [
        ['物资名称', '供应商', '规格', '采购数量', '导入结果']
      ]
      let json = result.map(v => {
        let current = []
        Object.keys(purchaseNameObject).forEach(k => {
          current[purchaseNameObject[k]] = v[k]
        })
        return current
      })
      failureJson = failureJson.concat(json)
      // 下载数据
      downloadJsonExcel(failureJson)
    },
    // 选择物资确定回调事件
    confirmChooseHandle(res) {
      console.log(1212, res)
      this.showChooseDialog = false
      // 添加的数据
      let result = []
      // 新增物资
      // purchase_weight 只有菜谱才有推荐重量
      if (res.type === 'purchase') {
        res.data.forEach(v => {
          // 弹出添加物资specs_item是一定会存在的
          // v.specs_item
          console.log('98989vvv', v)
          let item = {
            materials_name: v.materials_name,
            purchase_weight: 0,
            purchase_count: v.count,
            purchase_unit: `${v.specs_item.limit_unit_name}*${v.specs_item.net_content}${v.specs_item.net_content_unit}`, // 最小单位：例：瓶*330ml
            purchase_unit_id: v.specs_item.limit_unit_id,
            ref_unit_price: v.specs_item.unit_price, // 参考价
            purchase_price: divide(v.specs_item.unit_price), // 采购价
            total_price: Math.round(v.total_price), // 合计
            supplier_manage_id: v.supplier,
            supplier_manage_name: v.supplier_manage_name,
            supplier_list: v.price_info,
            materials_id: v.materials_id,
            material_specification_id: v.specs,
            specs_item: v.specs_item,
            specification_list: v.specification_list
          }
          // 旧版供应商是多选
          // 因为供应商是多选，但在外层列表中是单选的情况，得拆分
          // v.supplier.forEach(supplier => {
          //   item.supplier_manage_id = supplier
          //   let currentSupplier = v.price_info.find(current => current.supplier_manage_id === supplier)
          //   if (currentSupplier) {
          //     item.ref_unit_price = currentSupplier.ref_unit_price || 0
          //     // item.purchase_weight = currentSupplier.weight
          //     item.total_price = NP.times(item.ref_unit_price, item.purchase_count) || 0
          //     item.supplier_manage_name = currentSupplier.supplier_manage_name
          //   }
          //   // 数据得拷贝下
          //   result.push(deepClone(item))
          // })
          result.push(deepClone(item))
        })
      }
      // 选择模板
      if (res.type === 'template') {
        res.data.forEach(v => {
          let item = {
            materials_name: v.materials_name,
            purchase_weight: 0,
            purchase_count: v.purchase_count || 0,
            purchase_unit: v.unit_name,
            purchase_unit_id: v.unit_id,
            ref_unit_price: '', // 参考价
            purchase_price: '', // 采购价
            total_price: 0, // 合计
            supplier_manage_id: v.current_supplier_manage_id,
            supplier_manage_name: v.current_supplier_manage_name,
            supplier_list: v.supplier_data,
            materials_id: v.materials_id,
            specification_list: [],
            specs_item: null,
            material_specification_id: v.material_specification_id
          }
          // v.supplier = [v.current_supplier_manage_id]
          // // 因为供应商是多选，但在外层列表中是单选的情况，得拆分
          // v.supplier.forEach(supplier => {
          //   item.supplier_manage_id = supplier
          //   let currentSupplier = v.price_info.find(current => current.supplier_manage_id === supplier)
          //   if (currentSupplier) {
          //     item.ref_unit_price = currentSupplier.ref_unit_price || 0
          //     // item.purchase_weight = currentSupplier.weight
          //     item.total_price = NP.times(item.ref_unit_price, item.purchase_count) || 0
          //     item.supplier_manage_name = currentSupplier.supplier_manage_name
          //   }
          //   result.push(item)
          // })
          let currentSupplier = v.supplier_data.find(current => current.supplier_manage_id === item.supplier_manage_id)
          if (currentSupplier) {
            item.specification_list = currentSupplier.specification // 供应商的规格列表
          }
          console.log(98989, item.material_specification_id, item.specification_list)
          // 为了反显规格，需要拿到规格的具体数据
          if (item.material_specification_id && item.specification_list) {
            item.specs_item = item.specification_list.find(sp => sp.id === item.material_specification_id)
            item.ref_unit_price = item.specs_item.unit_price // 参考价
            item.purchase_price = divide(item.specs_item.unit_price) // 采购价
            item.purchase_unit = `${item.specs_item.limit_unit_name}*${item.specs_item.net_content}${item.specs_item.net_content_unit}`
          }
          result.push(item)
        })
        // 模板的物资可能会重复需要去重下合并下
        result = this.uniqueMaterials(result)
      }
      // 选择菜谱
      if (res.type === 'recipes') {
        res.data.forEach(v => {
          let item = {
            materials_name: v.materials_name,
            purchase_weight: v.weight ? NP.divide(v.weight, 1000) : 0,
            purchase_count: v.count || 0,
            purchase_unit: v.unit_name,
            purchase_unit_id: v.unit_id,
            ref_unit_price: '', // 参考价
            purchase_price: '', // 采购价
            total_price: 0, // 合计
            supplier_manage_id: '',
            supplier_manage_name: '',
            supplier_list: v.price_info,
            materials_id: v.materials_id
          }
          // 选择菜谱这些的物资是没有默认的供应商的，需要手动选择，后面合并数据需要对应修改下规则
          result.push(item)
        })
      }
      // 选择询价单/申购单
      if (res.type === 'inquiry') {
        res.data.forEach(v => {
          let item = {
            materials_name: v.materials_name,
            purchase_weight: 0,
            // purchase_count: v.purchase || 0,
            purchase_unit: v.unit_name,
            purchase_unit_id: v.unit_id,
            ref_unit_price: v.ref_unit_price, // 参考价
            purchase_price: divide(v.ref_unit_price), // 采购价
            // total_price: NP.times(v.ref_unit_price, v.purchase), // 合计
            supplier_manage_id: v.supplier_manage_id,
            supplier_manage_name: v.supplier_manage_name,
            supplier_list: v.price_info,
            materials_id: v.materials_id
          }
          // 申购单转采购单
          // 加了规格以后新增的
          let detailJson = JSON.parse(v.detail_json)
          item.material_specification_id = detailJson.material_specification_id
          item.supplier_manage_id = detailJson.supplier_manage_id
          item.specs_item = null
          item.specification_list = []
          let currentSupplier = v.price_info.find(current => current.supplier_manage_id === item.supplier_manage_id)
          if (currentSupplier) {
            item.supplier_manage_name = currentSupplier.supplier_manage_name
            item.specification_list = currentSupplier.specification // 供应商的规格列表
          }
          // 为了反显规格，需要拿到规格的具体数据
          if (item.material_specification_id && item.specification_list) {
            item.specs_item = item.specification_list.find(sp => sp.id === item.material_specification_id)
            item.ref_unit_price = item.specs_item.unit_price // 参考价
            item.purchase_price = divide(item.specs_item.unit_price) // 采购价
          }

          if (this.$route.query.type === 'to_purchase_info') {
            item.purchase_count = v.count || 0
            item.total_price = Math.round(NP.times(item.ref_unit_price || 0, v.count || 0)) // 合计
          } else {
            item.purchase_count = v.purchase || 0
            item.total_price = Math.round(NP.times(v.ref_unit_price || 0, v.purchase || 0)) // 合计
          }
          // v.price_info.forEach(supplier => {
          //   if (supplier.supplier_manage_id === v.supplier_manage_id) {
          //     item.purchase_weight = supplier.weight
          //   }
          // })
          // 选择菜谱这些的物资是没有默认的供应商的，需要手动选择，后面合并数据需要对应修改下规则
          result.push(item)
        })
      }
      if (this.formData.materialsTableData.length > 0) {
        this.formData.materialsTableData = this.mergeArrays(this.formData.materialsTableData, result)
      } else {
        this.formData.materialsTableData = result
      }
      this.initCurrentTableMaterials()
    },
    // 合并新旧数据，以供应商id、物资id、规格、采购价作为唯一值，相同的数据需要合并，数量这些需要累加起来
    mergeArrays(tableData, newData) {
      // 创建一个空对象用于存储合并的结果
      let merged = {}
      // 遍历 tableData 数组
      for (let i = 0; i < tableData.length; i++) {
        let current = tableData[i]
        merged[(current.materials_id + '-' + current.supplier_manage_id + '-' + current.material_specification_id + '-' + times(current.purchase_price))] = current
      }
      newData.forEach(item => {
        // 存在相同数据，合并数量并重新计算合计
        let key = item.materials_id + '-' + item.supplier_manage_id + '-' + item.material_specification_id + '-' + times(item.purchase_price)
        if (merged[key]) {
          merged[key].purchase_count = NP.plus(merged[key].purchase_count, item.purchase_count)
          merged[key].total_price = NP.times(times(merged[key].purchase_price), merged[key].purchase_count)
          merged[key].purchase_weight = NP.plus(merged[key].purchase_weight || 0, merged[key].purchase_weight || 0)
        } else {
        // 不存在直接往merged里面新增
          merged[key] = item
        }
      })
      return Object.values(merged)
    },
    // 物资去重，根据供应商id和物资id判断和规格id、采购价判断唯一，如有相同的则合并，数量累加
    uniqueMaterials(data, showMessage = false) {
      const arr = deepClone(data)
      const tmp = {}
      let isRepeat = false
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        let key = i
        // 当物资、供应商、规格都存在时才执行去重
        if (item.materials_id && item.supplier_manage_id && item.material_specification_id && item.purchase_price) {
          key = `${item.materials_id}_${item.supplier_manage_id}_${item.material_specification_id}_${times(item.purchase_price)}`
        }
        if (!tmp[key]) {
          tmp[key] = item
        } else { // 如果存在相同物资和供应商的数据则合并数据
          isRepeat = true
          tmp[key].purchase_count = NP.plus(tmp[key].purchase_count, item.purchase_count)
          tmp[key].total_price = NP.times(times(tmp[key].purchase_price), tmp[key].purchase_count)
          tmp[key].purchase_weight = NP.plus(tmp[key].purchase_weight, item.purchase_weight)
        }
      }
      if (showMessage && isRepeat) {
        // 弹窗提示下
        this.mergeSupplierMaterials()
      }
      return Object.values(tmp)
    },
    // 当列表已存在相同供应商的物资时，弹窗显示是否合并
    mergeSupplierMaterials() {
      this.$confirm(`同一供应商、相同物资的采购数量已合并`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        showCancelButton: false,
        center: true,
        beforeClose: (action, instance, done) => {
          done()
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    getFileLists(fileLists) {
      this.formData.fileLists = fileLists
    },
    // 上传图片前钩子
    beforeUpload(file) {
      let uploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      if (!uploadType.includes(getSuffix(file.name))) {
        this.$message.error('请检查上传文件格式！')
        return false
      }
      const isLt20M = file.size / 1024 / 1024 <= 2
      if (!isLt20M) {
        this.$message.error('上传附件大小不能超过 2M')
      }
      return isLt20M
    },
    //
    setPreviewListTitle(result) {
      this.collapseTitle = '查看附件(' + result.length + ')'
    },
    // 删除图片
    deleteUploadImg(index) {
      const fileUploadRef = this.$refs.fileUploadRef
      if (this.formData.fileLists[index]) {
        fileUploadRef && fileUploadRef.spliceFileData(this.formData.fileLists[index].uid)
      }
      this.formData.fileLists.splice(index, 1)
    }
  }
}
</script>

<style lang="scss">
.add-procure {
  position: relative;
  .flex{
    display: flex;
  }
  h3 {
    margin: 0;
  }
  .m-b-0 {
    margin-bottom: 0;
  }
  .w-280 {
    width: 280px;
  }
  .w-160 {
    width: 160px !important;
  }
  .w-auto {
    width: 300px;
  }
  .error {
    ::v-deep .el-input__inner {
      border-color: red;
    }
  }
  .red {
    color: red;
    .ps-origin {
      color: red !important;
    }
  }
  &.form-container {
    ::v-deep.el-form-item {
      // margin-bottom: 10px;
      > .el-form-item__content {
        > .el-input {
          // width: 280px;
        }
      }
    }
    .upload-img {
      width: 90px;
      height: 90px;
    }
    .img-item {
      display: inline-block;
      position: relative;
      transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
      .img-tools {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ffffff;
        font-size: 16px;
        transition: 0.3s;
        i {
          cursor: pointer;
          color: #ff9b45;
        }
      }
      &:hover {
        .img-tools {
          display: inline-block;
        }
      }
    }
  }
}
.el-date-table td.selected span {
  color: #fff !important;
}
.right-btn {
  position: absolute;
  right: 20px;
  top: 20px;
}
</style>
