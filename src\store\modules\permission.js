// eslint-disable-next-line no-unused-vars
import router, { resetRouter, defaultRoutes, asyncRoutes } from '@/router'
import { deepClone } from '@/utils'
import Layout from '@/layout'
import store from '@/store'
import axios from 'axios'

function hasPermission(roles, route, allRoles) {
  if (route.component === 'Layout') { // Layout组件特殊处理
    route.component = Layout
  }
  if (route.meta && route.meta.no_permission) {
    return true
  }
  if (route.meta && route.meta.permission) {
    if (!roles.some(role => route.meta.permission.includes(role))) {
      route.hidden = true
    }
    return allRoles.some(role => route.meta.permission.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles, allRoles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp, allRoles)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles, allRoles)
      }
      res.push(tmp)
    }
  })

  return res
}

function setRouteItem(customRouter) {
  let arr = []
  customRouter.forEach((itemIn, indexIn) => {
    let obj = {
      path: `customReport${indexIn}`,
      component: () =>
        import(
          /* webpackChunkName: "custom_report" */ '@/views/merchant/report/custom-report/CustomReport'
        ),
      name: `MerchantCustomReport${indexIn}`,
      meta: {
        noCache: true,
        title: itemIn.name,
        // 使用 no_permission: true 来跳过权限验证，或者确保用户有正确的权限
        no_permission: true, // 暂时跳过权限验证，确保路由能正常访问
        customReportData: itemIn // 传递自定义报表数据到组件
      }
    }
    arr.push(obj)
  })
  return arr
}

const state = {
  routes: [],
  addRoutes: [],
  permissionData: {
    tollVersion: null,
    permission: [],
    app_permission: [],
    merchant_app_permission: []
  }, // 该组织获取的全部权限
  versionPermissionData: {}, // 版本给的权限
  customReport: [] // 自定义报表
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = defaultRoutes.concat(routes)
  },
  CLEAR_ROUTES: (state, routes) => {
    state.addRoutes = []
    state.routes = []
  },
  SET_PERMISSIONDATA: (state, data) => {
    state.permissionData = { ...data }
  },
  SET_VERSIONPERMISSIONDATA: (state, data) => {
    state.versionPermissionData = { ...data }
  },
  SET_CUSTOMREPORT: (state, data) => {
    state.customReport = data
  }
}

const actions = {
  clearRoute({ commit, rootGetters }, roles) {
    // commit('SET_ROUTES', [])
    commit('CLEAR_ROUTES', [])
  },
  async generateRoutes({ commit, rootGetters }, roles) {
    let accessedRoutesMenu // 侧边栏
    let accessedRoutes // 处理过的路由
    // 三级路由处理
    let handleAsyncRoutes = deepClone(asyncRoutes)
    handleAsyncRoutes.map(first => {
      if (first.children) {
        first.children.map(second => {
          if (second.children) {
            second.children.map(third => {
              third.path = second.path + '/' + third.path
              first.children = first.children.concat(third)
            })
            // 删除下原来的children, 防止继续注册路由
            delete second.children
          }
        })
      }
    })
    // 所有符合当前权限的路由都注册
    accessedRoutesMenu = filterAsyncRoutes(deepClone(asyncRoutes), roles, rootGetters.allPermissions)
    accessedRoutes = filterAsyncRoutes(handleAsyncRoutes, roles, rootGetters.allPermissions)

    // 这里接口获取一下自定义报表的路由
    let customRouter = []
    if (store.getters.userInfo.company_id !== 1 && store.getters.token) {
      try {
        const result = await axios.post('/api/background_report_center/jmreport/jmreport_menu_list', {}, {
          headers: {
            TOKEN: store.getters.token
          }
        })
        let res = result.data || null
        if (res && res.code === 0) {
          customRouter = deepClone(res.data)
          // 获取一下现有的路由,在现有的路由里找到报表中心的路由
          // 将路由操作一下
          let arr = accessedRoutes.map(item => {
            if (item.name === "MerchantCustomizedReport") {
            // 找到自定义报表的路由，往children插进去
              item.children = setRouteItem(customRouter) // 处理自定义报表的路由
            }
            return item
          })
          accessedRoutes = deepClone(arr)
          // 还要将侧边栏处理一下
          let arrMenu = accessedRoutesMenu.map(item => {
            if (item.name === "MerchantCustomizedReport") {
            // 找到自定义报表的路由，往children插进去
              item.children = setRouteItem(customRouter) // 处理自定义报表的路由
            }
            return item
          })
          accessedRoutesMenu = deepClone(arrMenu)

          // 存储自定义报表数据
          store.dispatch('permission/setCustomReport', customRouter)
        } else {
          console.error('获取自定义报表路由失败:', res)
        }
      } catch (error) {
        console.error('获取自定义报表路由失败(catch):', error)
      }
    }
    // 默认没有home页面的，要根据配置跳默认
    accessedRoutes.unshift({
      path: '/',
      hidden: true,
      redirect: rootGetters.activeRoute === '/' ? '/error/404' : rootGetters.activeRoute
    })
    commit('SET_ROUTES', accessedRoutesMenu)
    return accessedRoutes
  },
  // dynamically modify SlideMenu
  async changeSlideMenu({ commit, dispatch, state, rootGetters }, role) {
    // generate accessible routes map based on permission
    console.log('在changeSlideMenu', rootGetters)
    await dispatch('permission/generateRoutes', rootGetters.permissions, { root: true })

    // reset visited views and cached views
    // dispatch('navTabs/delAllViews', null, { root: true })
  },
  // dynamically modify permission route
  async changeRoutes({ commit, dispatch, state, rootGetters }, role) {
    // reset current route
    resetRouter()
    // get new permission routes
    const accessRoutes = await dispatch('permission/generateRoutes', rootGetters.permissions, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)
    commit('SET_ROUTES', accessRoutes)

    // reset visited views and cached views
    dispatch('navTabs/delAllViews', null, { root: true })
  },
  setPermissionData({ commit, dispatch }, data) {
    return new Promise(resolve => {
      commit('SET_PERMISSIONDATA', data)
      resolve()
    })
  },
  setVersionPermissionData({ commit, dispatch }, data) {
    return new Promise(resolve => {
      commit('SET_VERSIONPERMISSIONDATA', data)
      resolve()
    })
  },
  async setCustomReport({ commit }, data) {
    return new Promise(resolve => {
      commit('SET_CUSTOMREPORT', data)
      resolve()
    })
  },
  setAddRoutes({ commit }, data) {
    return new Promise(resolve => {
      commit('SET_ROUTES', data)
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
