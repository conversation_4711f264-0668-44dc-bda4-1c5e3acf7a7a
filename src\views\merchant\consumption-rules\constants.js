import { getSevenDateRange } from '@/utils'
export const WALLET_LIST = [
  // { name: '全部', key: '' },
  { name: '储值钱包', key: 'store_wallet_on' },
  { name: '补贴钱包', key: 'subsidy_wallet_on' },
  { name: '赠送钱包', key: 'complimentary_wallet_on' },
  { name: '电子账户钱包', key: 'electronic_wallet_on' },
  { name: '组合支付', key: 'combine_wallet_on' }
]
export const MEAL_LIST = [
  // { name: '全部', value: 'all', disabled: false },
  { name: '早餐', value: 'breakfast', disabled: false },
  { name: '午餐', value: 'lunch', disabled: false },
  { name: '下午茶', value: 'afternoon', disabled: false },
  { name: '晚餐', value: 'dinner', disabled: false },
  { name: '夜宵', value: 'supper', disabled: false },
  { name: '凌晨餐', value: 'morning', disabled: false }
]
export const ALL_MEAL_LIST = [
  { name: '全部', value: 'all', disabled: false },
  { name: '早餐', value: 'breakfast', disabled: false },
  { name: '午餐', value: 'lunch', disabled: false },
  { name: '下午茶', value: 'afternoon', disabled: false },
  { name: '晚餐', value: 'dinner', disabled: false },
  { name: '夜宵', value: 'supper', disabled: false },
  { name: '凌晨餐', value: 'morning', disabled: false }
]
export const DEDUCTION_SERVICE = {
  name: {
    type: 'input',
    label: '规则名称',
    value: '',
    placeholder: '请输入规则名称'
  },
  group_nos: {
    type: 'groupSelect',
    label: '适用分组',
    value: [],
    placeholder: '请选择',
    multiple: true
  },
  org_nos: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    dataList: [],
    multiple: true,
    collapseTags: true,
    checkStrictly: true
  },
  status: {
    type: 'select',
    label: '状态',
    value: '',
    placeholder: '请选择活动状态',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '启用',
        value: 'enable'
      },
      {
        label: '停用',
        value: 'stop'
      }
    ]
  }
}
export const RECHARGE_SERVICE = {
  name: {
    type: 'input',
    label: '规则名称',
    value: '',
    placeholder: '请输入规则名称'
  },
  group_nos: {
    type: 'groupSelect',
    label: '适用分组',
    value: [],
    placeholder: '请选择',
    multiple: true
  },
  status: {
    type: 'select',
    label: '状态',
    value: '',
    placeholder: '请选择活动状态',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '启用',
        value: 'enable'
      },
      {
        label: '停用',
        value: 'stop'
      }
    ]
  }
}
// 操作记录筛选设置
export const OPERATION_RECORD_LIST_SEARCH_SETTING = {
  select_time: {
    type: 'daterange',
    label: '创建时间',
    labelWidth: '80px',
    format: 'yyyy-MM-dd',
    maxWidth: "400px",
    clearable: false,
    value: getSevenDateRange(7)
  },
  operator_name: {
    type: 'input',
    value: '',
    label: '创建人',
    placeholder: '请输入'
  },
  rule_ids: {
    type: 'select',
    label: '消费规则名称',
    value: [],
    placeholder: '请选择',
    listNameKey: 'name',
    listValueKey: 'consume_rule_id',
    multiple: true,
    collapseTags: true,
    dataList: []
  }
}

// 操作记录表格设置
export const OPERATION_RECORD_LIST_TABLE_SETTING = [
  { label: '创建时间', key: 'create_time' },
  { label: 'ID', key: 'guid', width: "150px" },
  { label: '消费规则', key: 'name' },
  { label: '创建人', key: 'operator_name' },
  { label: '生效时间', key: 'effective_time' },
  { label: '失效时间', key: 'expiration_time' },
  { label: '详情', key: 'operation', type: "slot", slotName: "operation" }
]
