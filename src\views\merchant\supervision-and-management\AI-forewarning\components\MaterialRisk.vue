<template>
  <div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
  </div>
</template>

<script>
import { debounce, deepClone } from '@/utils'
import dayjs from 'dayjs'

export default {
  props: ['isShow'],
  data() {
    return {
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '预警时间',
          clearable: false,
          value: [
            dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD')
          ]
        },
        drp_type: {
          type: 'select',
          label: '类型',
          value: '',
          placeholder: '请选择操作类型',
          isLinkage: true,
          dataList: [
            {
              label: '领料出库',
              value: 'RECEIVE_EXIT'
            },
            {
              label: '调拨出库',
              value: 'BORROW_EXIT'
            },
            {
              label: '损耗出库',
              value: 'EXPEND_EXIT'
            },
            {
              label: '退货出库',
              value: 'REFUND_EXIT'
            },
            {
              label: '其他出库',
              value: 'OTHER_EXIT'
            },
            {
              label: '采购入库',
              value: 'PURCHASE_ENTRY'
            },
            {
              label: '调拨入库',
              value: 'BORROW_ENTRY'
            },
            {
              label: '其他入库',
              value: 'OTHER_ENTRY'
            },
            {
              label: '采购下单',
              value: 'ORDER_PURCHASE'
            }
          ]
        }
      },
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '预警时间', key: 'warn_time' },
        { label: '所属仓库', key: 'warehouse_name' },
        { label: '类型', key: 'drp_type_alias' },
        { label: '物资名称', key: 'materials_name' },
        { label: '所属供应商', key: 'supplier_manage_name' },
        { label: '预警情况', key: 'warn_text' },
        { label: '经手人', key: 'operator' },
        { label: '关联单据', key: 'trade_no' }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },
  created() {
    this.getDataList()
  },
  watch: {
    isShow: {
      handler(val) {
        if (val) {
          this.getDataList()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    getDataList() {
      this.isLoading = true
      let params = {
        page: this.currentPage,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchFormSetting)
      }
      this.$apis.apiBackgroundFundSupervisionEarlyWarningMaterialsRiskListPost(params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.totalCount = res.data.count
          this.tableData = deepClone(res.data.results || [])
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
