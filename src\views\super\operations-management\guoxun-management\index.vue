<template>
  <div class="guoxinsms-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="balance p-20 m-b-20 font-size-14">
      账户余额：{{ balance }} <el-button type="text" class="ps-text m-l-20 font-size-14" v-loading="isLoading" @click="queryBalance">查询余额</el-button>
    </div>
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    >
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addTemplateHandle">
            新增模板
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column  v-for="item in tableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-warn"  @click="deleteConsume(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- 编辑/添加弹窗 start -->
    <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        width="460px"
        top="20vh"
        custom-class="ps-dialog"
        :close-on-click-modal="false"
        @closed="dialogHandleClose"
      >
        <el-form
          ref="dialogFormRef"
          v-loading="dialogLoading"
          :rules="dialogFormDataRuls"
          :model="dialogFormData"
          class="dialog-form"
          label-width="90px"
        >
          <el-form-item label="短信名称" prop="name">
            <el-input class="ps-input" v-model="dialogFormData.name" placeholder="请输入模板名称"></el-input>
          </el-form-item>
          <el-form-item label="模板内容" prop="content">
            <el-input class="ps-input" type="textarea" :rows="4" v-model="dialogFormData.content" placeholder="请输入模板内容"></el-input>
          </el-form-item>
          <el-form-item label="模板格式" prop="" class="template-text">
            【朴食科技】+ 模板内容，内容中的变量格式为{%变量%}
            <el-tooltip placement="top">
              <div slot="content">余额：balance<br/>支付时间：pay_time</div>
              <i class="el-icon-question pointer"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input class="ps-input" type="textarea" :rows="4" v-model="dialogFormData.remark" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" class="ps-cancel-btn" :disabled="dialogLoading" @click="closeDialog">
            取消
          </el-button>
          <el-button
            class="ps-origin-btn"
            :disabled="dialogLoading"
            type="primary"
            size="small"
            @click="submitHandle"
          >
            确定
          </el-button>
        </span>
      </el-dialog>
      <!-- 弹窗 end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, divide } from '@/utils'

export default {
  components: {
  },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      balance: '',
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '短信名称',
          value: '',
          placeholder: '请输入名称'
        }
      },
      tableSetting: [
        { label: '短信名称', key: 'name' },
        { label: '短信id', key: 'template_id' },
        { label: '短信内容', key: 'content' },
        { label: '备注', key: 'remark' },
        { label: '创建人', key: 'operator_name' },
        { label: '状态', key: 'template_status_alias' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
      ],
      dialogTitle: '新建模板',
      dialogVisible: false,
      dialogLoading: false,
      dialogFormData: {
        name: '',
        content: '',
        remark: ''
      },
      dialogFormDataRuls: {
        name: [
          { required: true, message: "请输入模板名称", trigger: "blur" }
        ],
        content: [
          { required: true, message: '请输入模板内容', trigger: "blur" }
        ]
      }
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getTableList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getTableList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 获取规则列表
    async getTableList() {
      this.isLoading = true
      let params = {
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (this.searchFormSetting.name.value) {
        params.name = this.searchFormSetting.name.value
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminSmsInfoTemplateListPost(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async queryBalance() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminSmsInfoTemplateGetBalancePost());
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.balance = divide(res.data)
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除
    deleteConsume(data) {
      this.$confirm(`是否删除？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            // await this.$sleep(2222)
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminSmsInfoTemplateDeletePost({
                ids: [data.id]
              })
            )
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getTableList()
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getTableList()
    },
    addTemplateHandle() {
      this.dialogVisible = true
    },
    closeDialog() {
      const refDom = this.$refs.dialogFormRef
      refDom && this.$refs.dialogFormRef.resetFields()
      this.dialogHandleClose()
      this.dialogVisible = false
    },
    dialogHandleClose() {
      this.dialogFormData = {
        name: '',
        content: '',
        remark: ''
      }
    },
    submitHandle() {
      this.$refs.dialogFormRef.validate(async valid => {
        if (valid) {
          if (this.dialogLoading) return;
          let params = {
            name: this.dialogFormData.name,
            content: this.dialogFormData.content,
            remark: this.dialogFormData.remark
          }
          this.dialogLoading = true
          const [err, res] = await to(this.$apis.apiBackgroundAdminSmsInfoTemplateAddPost(params));
          this.dialogLoading = false
          if (err) {
            this.$message.error(err.message)
            return
          }
          if (res.code === 0) {
            this.getTableList()
            this.$message.success(res.msg)
            this.closeDialog()
          } else {
            this.$message.error(res.msg)
          }
        }
      })
    }
  }
}
</script>
<style lang="scss">
.guoxinsms-wrapper {
  .balance {
    background-color: #fff;
    border-radius: 12px;
  }
  .template-text {
    .el-form-item__content {
      line-height: 1.5;
      margin-top: 10px;
    }
  }
}
</style>
