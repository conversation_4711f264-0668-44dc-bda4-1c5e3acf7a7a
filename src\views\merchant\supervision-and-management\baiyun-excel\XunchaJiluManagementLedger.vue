<template>
  <div class="assignment-ledger-permission container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="auto"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandler"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="dialogVisible = true" v-permission="['background_fund_supervision.ledger_food_safety.inspection_config_add']">巡检项目配置</button-icon>
          <button-icon color="origin" @click="historyDialogVisible = true">历史记录</button-icon>
          <button-icon color="origin" @click="gotoExport" v-permission="['background_fund_supervision.ledger_food_safety.get_inspection_record_ledger_export']">导 出</button-icon>
          <button-icon color="origin" @click="gotoPrint">打 印</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <!-- 顶部合并表头 -->
        <!-- height="calc(100vh - 570px)" -->
        <div class="table-custom-header">
          <div class="header-content">巡检记录表</div>
        </div>
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          header-row-class-name="ps-table-header-row"
          :max-height="600"
        >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item"></table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 添加配置弹窗 -->
    <configuration-add-edit-drawer
      ref="configurationAddEditDrawer"
      @confirm="confirmRecordDialog"
      @close="closeDialog"
      :isshow="dialogVisible"
      :drawerData="drawerData"
      keyType="Inspection"
    />
    <!-- 历史记录弹窗 -->
    <HistoryRecordDialog
      :visible.sync="historyDialogVisible"
      title="历史记录"
      type="Inspection"
      api="apiBackgroundFundSupervisionLedgerFoodSafetyOperateLogList"
      @close="historyDialogVisible = false"
    >
      <!-- 操作前内容插槽 -->
      <template #afterContent="{ row }">
        <div>{{ row.extra && row.extra.after && row.extra.after.length ? row.extra.after.join('、') : '--' }}</div>
      </template>
      <!-- 操作后内容插槽 -->
      <template #beforeContent="{ row }">
        <div>{{ row.extra && row.extra.before && row.extra.before.length ? row.extra.before.join('、') : '--' }}</div>
      </template>
    </HistoryRecordDialog>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import { SEARCH_SETTING_XUNCHAJILUMANAGEMENTLEDGER } from './constants'
import HistoryRecordDialog from './compontents/HistoryRecordDrawer.vue'
import ConfigurationAddEditDrawer from './compontents/ConfigurationAddEditDrawer.vue'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import * as dayjs from 'dayjs'
export default {
  name: 'XunchaJiluManagementLedger',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: [], // 表格配置
      currentTableSetting: [], // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_XUNCHAJILUMANAGEMENTLEDGER), // 查询表单配置
      printType: 'XunchaJiluManagementLedger', // 类型
      historyDialogVisible: false, // 历史记录
      dialogVisible: false, // 配置记录
      drawerData: {} // 配置弹窗数据
    }
  },
  created() {
    if (this.$route.query.date) {
      this.searchFormSetting.month.value = this.$route.query.date
    }
    this.initLoad()
  },
  components: {
    HistoryRecordDialog,
    ConfigurationAddEditDrawer
  },
  mounted() {
    console.log(this.getMonthDays())
  },
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
          if (key === 'month') {
            params.start_date = data[key].value
            params.end_date = data[key].value
          }
        }
      }
      return params
    },
    // 获取列表
    async getDataList() {
      if (this.isLoading) return
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (!params?.month) {
        this.$message.error('请选择月份后再筛选')
        return false
      }
      this.isLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetInspectionRecordLedger(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        // 加工处理下后端返回的数据
        let formatResults = []
        if (results.length) {
          results.map(item => {
            formatResults.push({
              ...item,
              ...item.date
            })
          })
        }
        console.log('加工处理下后端返回的数据', formatResults)
        this.setTableHeader()
        this.tableData = deepClone(formatResults)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理月份表头生成
    setTableHeader() {
      const formatTableHeader = this.getMonthDays(this.searchFormSetting.month.value)
      const baseHeader = [{ label: '日期/项目名称', key: 'inspection_item' }]

      this.tableSetting = [...baseHeader, ...formatTableHeader]
      this.currentTableSetting = [...baseHeader, ...formatTableHeader]
    },
    // 生成指定月份的日期列表，参数为"YYYY-MM"格式字符串，不传则默认当前月
    getMonthDays(dateStr) {
      let targetDate
      if (dateStr) {
        if (!/^\d{4}-\d{2}$/.test(dateStr)) {
          throw new Error('日期格式必须为"YYYY-MM"（例如：2025-07）')
        }
        targetDate = dayjs(dateStr)
        if (!targetDate.isValid()) {
          throw new Error('传入的日期无效')
        }
      } else {
        targetDate = dayjs()
      }
      const daysInMonth = targetDate.daysInMonth()
      const daysList = []
      for (let i = 1; i <= daysInMonth; i++) {
        daysList.push({
          label: `${i}日`,
          key: `${i}`
        })
      }

      return daysList
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 导出弹窗
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetInspectionRecordLedgerExport',
        params: params
      }
      this.exportHandle(option)
    },
    // 确认
    confirmRecordDialog(data) {
      console.log('confirmRecordDialog', data)
      this.dialogVisible = false
    },
    closeDialog() {
      this.dialogVisible = false
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.currentTableSetting)
      // tableSetting = tableSetting.filter(item => item.key !== 'delivery_unit')
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '巡检记录表',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetInspectionRecordLedger', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>

.table-custom-header {
  background-color: #f5f7fa;
  text-align: center;
  padding: 8px 0 10px 0;
  border: 1px solid #ebeef5;

  .header-content {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }
}
</style>
