<template>
  <div class="container-wrapper">
    <div class="inspection-record">
      <h2 class="table-title">巡检记录表</h2>

      <div class="date-header">
        <el-input v-model="year" class="year-input" size="mini"></el-input>
        <span>年</span>
        <el-input v-model="month" class="month-input" size="mini"></el-input>
        <span>月</span>
      </div>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column label="序号" prop="index" width="50" align="center"></el-table-column>
        <el-table-column label="项目" prop="item"  align="center"></el-table-column>

        <el-table-column label="日期" align="center">
          <el-table-column v-for="day in 31" :key="day" :label="day" width="40" align="center">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.days[day-1]" label=' '></el-checkbox>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>

      <div class="inspector-section">
        <div class="label">检查人</div>
        <div class="inspector-grid">
          <el-table
            :data="inspectorData"
            border
            :show-header="false"
            style="width: 100%"
            :cell-style="cellStyle">
            <el-table-column v-for="day in 31" :key="day" width="49" align="center">
              <div><el-input  style="width:30px"/></div>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div class="note-section">
        <div class="label w-100">说明:</div>
        <div class="note-content">每日随机抽查, 检查合格则记录巡检抽查情况.</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'XunchaJiluManagementLedger',
  data() {
    const inspectionItems = [
      { index: 1, item: '原料按放是否准确', days: Array(31).fill(false) },
      { index: 2, item: '工器具是否卫生洁净', days: Array(31).fill(false) },
      { index: 3, item: '是否有异物', days: Array(31).fill(false) },
      { index: 4, item: '产品状态是否正常', days: Array(31).fill(false) },
      { index: 5, item: '中心温度是否正常', days: Array(31).fill(false) },
      { index: 6, item: '售卖温度是否正常', days: Array(31).fill(false) }
    ];

    return {
      year: new Date().getFullYear().toString(),
      month: (new Date().getMonth() + 1).toString(),
      tableData: inspectionItems,
      inspectorData: [{ signatures: Array(31).fill('') }]
    };
  },
  methods: {
    cellStyle() {
      return {
        padding: '3px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '5px 0'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.inspection-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 15px;
    font-size: 22px;
    font-weight: bold;
  }

  .date-header {
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    .year-input, .month-input {
      width: 60px;
      margin: 0 5px;
    }
  }

  .inspector-section {
    margin-top: 15px;
    display: flex;

    .label {
      width: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      border: 1px solid #EBEEF5;
      border-right: none;
    }

    .inspector-grid {
      flex: 1;

      ::v-deep .el-input__inner {
        height: 25px;
        padding: 0 5px;
        text-align: center;
      }
    }
  }

  .note-section {
    margin-top: 15px;
    display: flex;
    border: 1px solid #EBEEF5;

    .label {
      width: 50px;
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      color: red;
    }

    .note-content {
      flex: 1;
      padding: 10px;
      color: red;
    }
  }
}
</style>
