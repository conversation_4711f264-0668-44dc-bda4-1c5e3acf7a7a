<template>
  <div class="settlement-order-list container-wrapper">
    <refresh-tool v-if="showRefresh" @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          数据列表
          <span class="inline-block m-l-20 font-size-16">
            当前仓库：
            <span style="color: 000; font-weight: 700">{{ $route.query.warehouse_name }}</span>
          </span>
        </div>
        <div class="align-r">
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #relatedDocument="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="showDrawerHandle('finalstatement', row)"
              >
                关联单据
              </el-button>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showDrawerHandle('detail', row)">详情</el-button>
              <el-button v-if="row.settle_status === 'unliquidated'" type="text" size="small" class="ps-text" @click="openApplyOrder(row)">结算申请</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 -->
    <RelatedDocument
      :showdialog.sync="dialogVisible"
      :type="dialogType"
      orderType="finalstatement"
      :title="dialogTitle"
      :width="dialogWidth"
      :info-data="dialogInfo"
      :params="dialogParams"
      :api="dialogApi"
      @clickConfirm="searchHandle"
    >
      <template v-if="dialogType === 'detail'" slot="drawer-footer">
        <div class="ps-drawer-footer">
          <el-button :disabled="isLoading" @click="dialogVisible = false">取消</el-button>
          <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="dialogVisible = false">确定</el-button>
          <el-button v-if="dialogInfo.settle_status === 'unliquidated'" class="ps-btn" type="primary" @click="openApplyOrder(dialogInfo)"> 结算申请 </el-button>
        </div>
      </template>
    </RelatedDocument>

    <div class="ps-el-drawer to-apply-order">
      <el-drawer
        :title="'结算申请'"
        :visible="applyOrderShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <div>
            <div class="form-item">
              <span class="form-label">结算金额：</span>￥{{ applyInfoData.settle_fee | formatMoney }}
            </div>
            <div class="form-item">
              <span class="form-label">关联收货单据：</span>
              <div v-for="(item, index) in applyInfoData.delivery_info_nos" :key="index">
                <div class="btn-wrap">
                  <span :class="['m-r-10', item.isdel ? 'is-del-trade-no' : '']">{{item.trade_no}}</span>
                  <el-button v-if="!item.isdel" type="text" size="small" class="ps-red m-r-10" @click="delTradeNo(item)">删除</el-button>
                  <el-button v-if="item.isdel" type="text" size="small" class="ps-text" @click="returnDelTradeNo(item)">撤回操作</el-button>
                </div>
              </div>
            </div>
            <el-upload
              ref="uploadExtraImage"
              class="upload-attachments"
              drag
              :data="uploadParams"
              :action="actionUrl"
              :multiple="true"
              :file-list="fileList"
              list-type="picture-card"
              :on-change="handelChange"
              :on-success="handleImgSuccess"
              :before-upload="beforeFoodImgUpload"
              :limit="25"
              :headers="headersOpts"
            >
              <el-button v-if="fileList.length<6" type="text" size="small" class="upload-attachments-btn ps-text">上传附件</el-button>
              <div slot="file" slot-scope="{file}" v-loading="file.status==='uploading'" element-loading-text="上传中">
                <div class="upload-food-img"><img :src="file.url" alt=""></div>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-delete" @click="handleFoodImgRemove(file, index)">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </div>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle()">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle()">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
    <el-dialog :visible.sync="dialogImageVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange, getToken, deepClone, getSuffix } from '@/utils'
import RelatedDocument from '../components/related-document/RelatedDocument.vue'

export default {
  name: 'SettlementOrder',
  components: {
    RelatedDocument
  },
  props: {
    showRefresh: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      warehouseId: this.$route.query.warehouse_id,
      tabType: 1,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '单据编号', key: 'trade_no' },
        { label: '关联单据', key: 'relatedDocument', type: 'slot', slotName: 'relatedDocument' },
        { label: '创建时间', key: 'create_time' },
        { label: '结算时间', key: 'settle_time' }, // , isComponents: true, type: 'date', format: 'YYYY-MM-DD'
        { label: '结算金额', key: 'settle_fee', type: 'money' },
        { label: '结算状态', key: 'settle_status_alias' },
        { label: '审批状态', key: 'expect_arrival_date12' },
        { label: '供应商名称', key: 'supplier_manage_name' },
        { label: '经手人', key: 'expect_arrival_date14' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      searchFormSetting: {
        // date_type: {
        //   type: 'select',
        //   label: '',
        //   value: 'create_time',
        //   maxWidth: '130px',
        //   placeholder: '请选择',
        //   dataList: [
        //     {
        //       label: '创建时间',
        //       value: 'create_time'
        //     },
        //     // {
        //     //   label: '采购时间',
        //     //   value: 'purchase_time'
        //     // }
        //   ]
        // },
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '创建时间',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        }
      },
      dialogType: '', // 弹窗类型
      dialogOrderType: 'deliveryOrder',
      dialogVisible: false,
      dialogTitle: '新建分类',
      dialogWidth: '740px',
      dialogInfo: {},
      dialogParams: {},
      dialogApi: '',
      applyOrderShow: false,
      applyInfoData: {},
      uploading: false, // 上传加载中
      actionUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'settlement_order'
      },
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      dialogImageVisible: false,
      dialogImageUrl: '',
      fileList: [],
      fileListUrl: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getSettlementOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getSettlementOrderList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: this.warehouseId,
        // audit_status: 'approve',
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpVendorDataFinalStatementListPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          item.settle_time = item.settle_status === 'settled' ? item.settle_time : '--'
          item.delivery_info_nos.map(info => {
            info.isdel = false
          })
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSettlementOrderList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 操作点击
    clickOperationHandle(type, data) {
      let content = ''
      let apiUrl = 'apiBackgroundDrpVendorDataPurchaseFinalStatementApplyPost'
      let params = {}
      this.$confirm(content, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg || '成功')
              this.getSettlementOrderList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    showDrawerHandle(type, row) {
      // this.dialogOrderType = 'finalstatement'
      // this.dialogParams = {
      //   id: row.id
      // }
      this.dialogInfo = row
      if (type === 'detail') {
        this.dialogType = type
        this.dialogTitle = '详情'
        this.dialogApi = ''
      } else {
        this.dialogTitle = '关联单据'
        this.dialogType = 'order'
        this.dialogApi = 'apiBackgroundDrpVendorDataVendorDataInfoPost'
      }
      this.dialogVisible = true
    },
    openApplyOrder(data) {
      this.dialogVisible = false
      this.applyOrderShow = true
      this.applyInfoData = deepClone(data)
    },
    async saveHandle() {
      let delivery_info_nos = []
      this.applyInfoData.delivery_info_nos.map( item => {
        if (!item.isdel) delivery_info_nos.push(item.trade_no)
      })
      const [err, res] = await to(this.$apis.apiBackgroundDrpVendorDataStatementApplyPost({
        settle_fee: this.applyInfoData.settle_fee,
        final_statement_id: this.applyInfoData.id,
        delivery_info_nos: delivery_info_nos,
        extra: this.fileListUrl
      }))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success("成功")
        this.applyOrderShow = false
        this.getSettlementOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    cancelHandle() {
      this.applyOrderShow = false
    },
    handelChange(file, fileList) {
      this.uploadParams.key =
        this.uploadParams.prefix + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    },
    handleImgSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.fileList = fileList
        this.fileListUrl.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogImageVisible = true;
    },
    handleFoodImgRemove(file) {
      let index = this.fileList.findIndex(item => item.url === file.url)
      this.fileList.splice(index, 1)
      this.fileListUrl.splice(index, 1)
    },
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是JPG/BMP/PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
    },
    delTradeNo(data) {
      data.isdel = true
    },
    returnDelTradeNo(data) {
      data.isdel = false
    }
  }
}
</script>

<style lang="scss">
.settlement-order-list {
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
  .to-apply-order{
    .form-item {
      font-size: 14px;
      line-height: 30px;
    }
    .is-del-trade-no{
      text-decoration: line-through;
    }
  }
  .btn-wrap{
    position: relative;
  }
  .upload-attachments{
    position: relative;
    .upload-food-img{
      img{
        width: 148px;
        height: 148px;
      }
    }
    padding-top: 30px;
    .el-upload-dragger {
      width: 30px;
      height: 10px;
      overflow: inherit;
    }
    .el-upload.el-upload--picture-card {
      height: 28px;
      line-height: 28px;
      background-color: #00000000;
      position: absolute;
      top: 0;
      left: 0;
    }
    &.hide-upload{
      .el-upload--picture-card{
        display: none;
      }
    }
    .el-upload--picture-card{
      border: none;
      .el-upload-dragger{
        border: none;
      }
    }
  }
}
</style>
