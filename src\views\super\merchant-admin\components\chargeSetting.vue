<template>
  <div class="charge-form" v-loading="isLoading">
    <div class="top-title">
      <div class="l-title">收费设置</div>
    </div>
    <div class="charge-form-content">
      <el-form
        ref="chargeSettingForm"
        :model="chargeSettingData"
        :rules="changeRules(chargeSettingData.toll_type)"
        label-width="100px">
        <el-form-item label="项目状态" prop="project_status">
          <el-select class="standard-use-select" v-model="chargeSettingData.project_status" placeholder="请选择项目状态" @change="resetTollType">
            <el-option v-for="(item, index) in projectStatusList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收费模式" prop="toll_type">
          <el-select class="standard-use-select" v-model="chargeSettingData.toll_type" placeholder="请选择收费模式" @change="clearValidate">
            <el-option v-for="(item, index) in tollTypeList" :key="index" :label="item.label" :value="item.value" :disabled="['accepting', 'accepted'].includes(chargeSettingData.project_status) && item.value === '3'"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="收费规则" v-if="chargeSettingData.toll_type === '1'" prop="toll_rule">
          <el-select class="standard-use-select" v-model="chargeSettingData.toll_rule" placeholder="请选择收费规则">
            <el-option
              v-for="item in ruleList"
              :label="item.name + ' ' + item.alias"
              :key="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="使用期限" v-if="chargeSettingData.toll_type !== '3'" prop="serviceTime">
          <el-date-picker
            class="standard-use-date ps-picker"
            :picker-options="pickerOptions"
            v-model="chargeSettingData.serviceTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            unlink-panels
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
          <div class="text">请确认信息真实有效性</div>
        </el-form-item>
        <el-form-item label="用户规模" v-if="chargeSettingData.toll_type === '1'" prop="user_scale">
          <el-input
            class="standard-user-scale ps-input"
            v-model.number="chargeSettingData.user_scale"
            placeholder="请输入初始用户规模"
            type="number"
          >
            <template #append>人</template>
          </el-input>
          <div class="text">请确认信息真实有效性</div>
        </el-form-item>
        <el-form-item label="收费金额" prop="first_year_charge" v-if="chargeSettingData.toll_type === '2'">
          <el-input v-model="chargeSettingData.first_year_charge" type="number">
            <template #prepend>续费1年</template>
            <template #append>元</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="second_year_charge" v-if="chargeSettingData.toll_type === '2'">
          <el-input
            v-model="chargeSettingData.second_year_charge"
            :disabled="!chargeSettingData.first_year_charge"
            type="number"
          >
            <template #prepend>续费2年</template>
            <template #append>元</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="third_year_charge" v-if="chargeSettingData.toll_type === '2'">
          <el-input
            v-model="chargeSettingData.third_year_charge"
            :disabled="!chargeSettingData.second_year_charge"
            type="number"
          >
            <template #prepend>续费3年</template>
            <template #append>元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="IC卡校验" prop="use_card_no_limit">
          <el-radio-group v-model="chargeSettingData.use_card_no_limit" class="ps-radio">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <!-- 按钮 -->
    <div class="btn-area">
      <el-button type="plain" @click="resetForm">取消</el-button>
      <el-button class="ps-origin-btn" @click="save(chargeSettingData.toll_type)">保存</el-button>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import { RECENTYEAR, divide, times } from './constants'
import dayjs from 'dayjs'

export default {
  name: 'chargeSetting',
  props: {
    treeSelectId: {
      type: Number,
      default: 0
    }
  },
  data() {
    let checkoutUserScale = (rule, value, callback) => {
      if (value) {
        let reg = /^-?(0|([1-9][0-9]*))(\.[\d]+)$/
        if (value <= 10) {
          return callback(new Error('用户数需大于10人'))
        }
        if (reg.test(value)) {
          return callback(new Error('不能为小数'))
        }
        callback()
      } else {
        return callback(new Error('请选择用户规模'))
      }
    }
    let checkoutPrice = (rule, value, callback) => {
      if (value) {
        let reg = /^(?:\d{1,6}(?:\.\d{1,2})?)$/
        if (!reg.test(value)) {
          return callback(new Error('请输入至多为六位或小数点至多两位的金额'))
        }
        callback()
      } else {
        return callback(new Error('请输入收费金额'))
      }
    }
    return {
      chargeSettingRules_one: {
        toll_type: [{ required: true, message: '请选择收费模式', trigger: "change" }],
        toll_rule: [{ required: true, message: '请选择收费规则', trigger: "change" }],
        serviceTime: [
          { required: true, message: '请选择使用期限', trigger: "change" },
          {
            validator: (rule, value, callback) => {
              console.log(dayjs(value[0]))
              if (value[0] === value[1]) {
                callback(new Error('开始时间需不等于结束时间'))
              } else if (dayjs(value[0]) >= dayjs(value[1]).subtract(1, 'day')) {
                callback(new Error('开始时间需与结束时间相隔至少一天'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        user_scale: [{ required: true, validator: checkoutUserScale, trigger: "change" }],
        use_card_no_limit: [{ required: true, trigger: "change" }]
      },
      chargeSettingRules_two: {
        toll_type: [{ required: true, message: '请选择收费模式', trigger: "change" }],
        serviceTime: [{ required: true, message: '请选择使用期限', trigger: "change" }],
        first_year_charge: [{ required: true, validator: checkoutPrice, trigger: "blur" }],
        second_year_charge: [{ required: true, validator: checkoutPrice, trigger: "blur" }],
        third_year_charge: [{ required: true, validator: checkoutPrice, trigger: "blur" }],
        use_card_no_limit: [{ required: true, trigger: "change" }]
      },
      chargeSettingRules_three: {
        toll_type: [{ required: true, message: '请选择收费模式', trigger: "blur" }],
        use_card_no_limit: [{ required: true, trigger: "blur" }]
      },
      isLoading: false,
      tollTypeList: [
        {
          label: '标准收费',
          value: '1'
        },
        {
          label: '固定收费',
          value: '2'
        },
        {
          label: '一次性收费',
          value: '3'
        }
      ],
      projectStatusList: [
        {
          label: '待验收',
          value: 'accepting'
        },
        {
          label: '已验收',
          value: 'accepted'
        },
        {
          label: '维保中',
          value: 'maintenance'
        },
        {
          label: '其他',
          value: 'other'
        }
      ],
      ruleList: [],
      defaultFormData: {
        toll_type: '1',
        toll_rule: '',
        project_status: '',
        serviceTime: [],
        user_scale: '',
        use_card_no_limit: false,
        first_year_charge: '',
        second_year_charge: '',
        third_year_charge: ''
      },
      chargeSettingData: {
        id: '',
        toll_type: '1',
        toll_rule: '',
        project_status: '',
        serviceTime: [],
        user_scale: '',
        use_card_no_limit: false,
        first_year_charge: '',
        second_year_charge: '',
        third_year_charge: ''
      },
      pickerOptions: {
        // disabledDate: (time) => {
        //   return time.getTime() < Date.now() - 8.64e7
        // }
      }
    }
  },
  created() {
    this.getIcCardData()
    this.getChargeRuleList()
    this.getChargeModeDetail()
  },
  methods: {
    // 重置收费模式
    resetTollType() {
      this.$nextTick(() => {
        this.chargeSettingData.toll_type = '1'
      })
      // 根据收费模式赋值使用期限
      switch (this.chargeSettingData.project_status) {
        case "accepting":
          this.chargeSettingData.serviceTime = [dayjs().format('YYYY-MM-DD HH:mm:ss'), dayjs().add(3, 'month').format('YYYY-MM-DD HH:mm:ss')]
          break
        default:
          this.chargeSettingData.serviceTime = [dayjs().format('YYYY-MM-DD HH:mm:ss'), dayjs().add(1, 'year').format('YYYY-MM-DD HH:mm:ss')]
          break
      }
    },
    changeRules(e) {
      switch (e) {
        case '1':
          return this.chargeSettingRules_one
        case '2':
          return this.chargeSettingRules_two
        case '3':
          return this.chargeSettingRules_three
      }
    },
    // 获取收费规则
    getChargeRuleList() {
      this.$apis.apiBackgroundAdminBackgroundTollRuleListPost()
        .then(res => {
          if (res.code === 0) {
            console.log(res.data)
            this.ruleList = res.data.results ? res.data.results : []
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    clearValidate() {
      this.$nextTick(() => {
        this.$refs.chargeSettingForm.clearValidate()
      })
    },
    resetForm() {
      this.chargeSettingData = deepClone(this.defaultFormData)
    },
    async save(e) {
      let params = {
        id: this.chargeSettingData.id,
        toll_type: this.chargeSettingData.toll_type,
        project_status: this.chargeSettingData.project_status,
        renew_fee_list: [],
        toll_rule: NaN
      }
      switch (e) {
        case '1':
          params.renew_fee_list = [0, 0, 0]
          params.toll_rule = this.chargeSettingData.toll_rule
          Object.assign(params, {
            service_end_time: this.chargeSettingData.serviceTime[1],
            user_scale: this.chargeSettingData.user_scale
          })
          break
        case '2':
          params.renew_fee_list = [
            times(this.chargeSettingData.first_year_charge),
            times(this.chargeSettingData.second_year_charge),
            times(this.chargeSettingData.third_year_charge)
          ]
          Object.assign(params, {
            service_end_time: this.chargeSettingData.serviceTime[1]
          })
          break
        case '3':
          break
      }
      this.$refs.chargeSettingForm.validate(async (valid) => {
        if (!valid) {
          return false;
        } else {
          let flag1 = await this.saveChargeData(params)
          let flag2 = await this.saveIcCard()
          if (flag1 && flag2) {
            this.$message.success('保存成功')
          } else {
            this.$message.error('保存失败')
          }
        }
      });
    },
    saveChargeData(params) {
      return new Promise((resolve, reject) => {
        this.$apis.apiBackgroundAdminBackgroundTollSaveSettingsPost(params).then(res => {
          resolve(res.code === 0)
        }).catch(error => {
          console.log("error", error.msg);
          resolve(false)
        })
      })
    },
    // 获取ic卡数据
    async getIcCardData() {
      this.$apis.apiBackgroundAdminOrganizationGetInfoPost({
        id: this.treeSelectId
      })
        .then(res => {
          if (res.code === 0) {
            this.chargeSettingData.use_card_no_limit = res.data.use_card_no_limit
          }
        })
    },
    // 保存IC卡校验
    async saveIcCard() {
      let params = {
        id: this.treeSelectId,
        use_card_no_limit: this.chargeSettingData.use_card_no_limit
      }
      return new Promise((resolve, reject) => {
        this.$apis.apiBackgroundAdminOrganizationModifyPost(params).then(res => {
          resolve(res.code === 0)
        }).catch(error => {
          console.log("error", error.msg);
          resolve(false)
        })
      })
    },
    // 获取收费设置
    async getChargeModeDetail() {
      this.$apis.apiBackgroundAdminBackgroundTollGetSettingsPost({
        org_id: this.treeSelectId
      })
        .then(res => {
          if (res.code === 0) {
            this.chargeSettingData.toll_type = res.data.toll_type.toString()
            this.chargeSettingData.id = res.data.id
            this.chargeSettingData.project_status = res.data.project_status
            if (this.chargeSettingData.toll_type !== '3') {
              this.chargeSettingData.serviceTime = [dayjs(res.data.service_start_time).format('YYYY-MM-DD HH:mm:ss'),
                dayjs(res.data.service_end_time).format('YYYY-MM-DD HH:mm:ss') ? dayjs(res.data.service_end_time).format('YYYY-MM-DD HH:mm:ss') : dayjs(res.data.service_start_time).add(90, 'day').format('YYYY-MM-DD HH:mm:ss')]
            } else {
              this.chargeSettingData.serviceTime = RECENTYEAR
            }
            if (this.chargeSettingData.toll_type === '1') {
              this.chargeSettingData.toll_rule = res.data.toll_rule
              this.chargeSettingData.user_scale = res.data.user_scale
            } else {
              this.chargeSettingData.toll_rule = ''
              this.chargeSettingData.user_scale = NaN
            }
            if (this.chargeSettingData.toll_type === '2') {
              this.chargeSettingData.first_year_charge = divide(res.data.renew_fee_list[0])
              this.chargeSettingData.second_year_charge = divide(res.data.renew_fee_list[1])
              this.chargeSettingData.third_year_charge = divide(res.data.renew_fee_list[2])
            } else {
              this.chargeSettingData.first_year_charge = NaN
              this.chargeSettingData.second_year_charge = NaN
              this.chargeSettingData.third_year_charge = NaN
            }
            this.defaultFormData = deepClone(this.chargeSettingData)
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.top-title {
  width: 850px;
  display: flex;
  justify-content: space-between;
  padding-bottom: 15px;
  border-bottom: 1px solid #e7e9ef;
  vertical-align: middle;
  margin-bottom: 25px;
}
.charge-form {
  &-content{
    width: 600px;
    .standard-use-date {
      width: 446px
    }
    .standard-use-select {
      width: 446px;
    }
  }
  .text {
    margin-bottom: 0;
    color: #a19ca5;
  }
  .btn-area {
    width: 600px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
::v-deep .el-input-group__append,
.el-input-group__prepend {
  border: none;
  background-color: rgba(0, 0, 0, 0);
}
::v-deep .el-input-group__prepend {
  color: #606266;
  border: none;
  background-color: rgba(0, 0, 0, 0);
  margin-right: 15px;
  margin-left: 0px;
  padding-left: 0;
}

</style>
