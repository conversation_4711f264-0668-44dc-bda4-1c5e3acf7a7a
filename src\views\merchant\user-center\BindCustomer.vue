<template>
  <div class="customerbinding container-wrapper">
    <!--客户号绑定 -->
    <!--头部-->
    <refresh-tool @refreshPage="refreshHandler(true)" />
    <!--筛选 -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler" @reset="resetHandler"
      :autoSearch="false"></search-form>
    <!--数据列表 -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="gotoExport"
            v-permission="['card_service.third_card_user.client_number_list_export']">导出</button-icon>
          <button-icon color="origin" @click="handlerShowDialog('add', null)"
            v-permission="['card_service.third_card_user.abc_client_modify']">新建</button-icon>
          <button-icon color="origin" @click="handlerCustomerImport"
            v-permission="['card_service.third_card_user.batch_import_client_bind']">导入关联</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" height="460" stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="(item, index) in tableSettings" :key="index" :col="item">
            <template #userGroups="{ row }">
              {{ row.card_user_group_alias | listToString }}
            </template>
            <template #departmentGroups="{ row }">
              {{ row.card_department_group_alias | listToString }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text-blue"
                @click="handlerShowDialog('edit', row)"
                v-permission="['card_service.third_card_user.abc_client_modify']">编辑</el-button>
              <el-button type="text" size="small" class="ps-text-blue" @click="handlerDeleteBinding(row)"
                v-permission="['card_service.third_card_user.abc_client_modify']">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination @size-change="handleSizeChange" @current-change="onPaginationChange" :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount" background class="ps-text" popper-class="ps-popper-select"></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!--导入弹窗-->
    <import-dialog-drawer :templateUrl="templateUrl" :tableSetting="tableSettingImport" :show.sync="isShowImportDialog"
      :title="'导入客户号绑定'" :openExcelType="openExcelType">
    </import-dialog-drawer>
    <!--编辑弹窗-->
    <add-customer-dialog ref="addCustomerDialogRef" :show.sync='isShowAddDialog' :title="addDialogTitle"
      :type="addDialogType" @confirmDialog="confirmCustomerBinding" @close="closeDialog">
    </add-customer-dialog>
  </div>
</template>

<script>
import { TABLE_HEAD_DATA_CUSTOMER_BINDING, SEARCH_FORM_SET_DATA_CUSTOMER_BINDING, TABLE_HEAD_DATA_IMPORT_CUSTOMER, URL_TEMPLATE_MODEL } from './constants/customerBandingConstants'
import { deepClone, to, debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import AddCustomerDialog from './components/AddCustomerDialog'
export default {
  name: "BindCustomer",
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSettings: deepClone(TABLE_HEAD_DATA_CUSTOMER_BINDING), // 表格头部设置内容
      searchFormSetting: deepClone(SEARCH_FORM_SET_DATA_CUSTOMER_BINDING), // 搜索设置内容
      isShowImportDialog: false, // 是否显示导入dialog
      templateUrl: '', // 导入模板的链接
      tableSettingImport: deepClone(TABLE_HEAD_DATA_IMPORT_CUSTOMER), // 导入表格头部设置
      openExcelType: 'CustomerBindingImport', // 是否显示确认键loading
      isShowAddDialog: false, // 是否显示新增弹窗
      addDialogTitle: '新建', // 新增弹窗标题
      addDialogType: 'add', // 新增弹窗类型
      collect: [
        // 统计
        {
          key: 'total_count',
          value: '',
          label: '总人数：',
          unit: '人'
        }
      ]
    }
  },
  components: { AddCustomerDialog },
  mixins: [exportExcel, report],
  created() {
    this.initLoad()
  },
  filters: {
    operationTypeName(value) {
      if (value && value === 'background_import') return '后台导入'
      if (value && value === 'manual_bind') return '手动绑定'
    },
    listToString(value) {
      console.log("value", typeof value);
      if (!value || value === "" || typeof value === 'undefined') {
        return '--'
      }
      if (typeof value === 'string') {
        return value
      }
      if (value && Array.isArray(value) && value.length >= 0) {
        return value.length === 0 ? '-- ' : value.join(",")
      }
      return value
    }

  },
  destroyed() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
  },
  methods: {
    /**
     * 刷新页面
     */
    refreshHandler(flag) {
      // 搜索重置
      this.currentPage = 1;
      if (flag) {
        this.$refs.searchRef.resetForm()
      }
      this.tableData = []
      this.initLoad()
    },
    /**
     * 初始化数据
     */
    initLoad() {
      console.log("customerbinding初始化");
      // 获取数据列表
      this.getCustomerBindingList()
      // 获取模板链接
      this.templateUrl = this.getTempUrl()
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    onPaginationChange(page) {
      console.log("onPaginationChange", page);
      this.currentPage = page
      this.getCustomerBindingList()
    },
    /**
     * 显示条数改变
     * @param {*} pageSize
     */
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getCustomerBindingList()
    },
    /**
     * 获取客户号绑定列表
     */
    async getCustomerBindingList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiCardServiceThirdCardUserClientNumberListPost(params))
      console.log("getCustomerBindingList", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        var resultList = data.results || []
        this.tableData = deepClone(resultList)
        console.log("this.tableData", this.tableData)
        this.totalCount = data.count || -1
        this.collect[0].value = data.total_count
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'select_date') {
            params[key] = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    /**
     * 筛选
     */
    searchHandler: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.initLoad()
      }
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.refreshHandler(false)
    },
    /**
     * 导入按钮点击
     */
    handlerCustomerImport() {
      this.isShowImportDialog = true
    },
    /**
     * 导入确认
     * @param {*} data
     */
    async confirmImportData(data) {
      var dataList = data.allData || []
      var currentPage = data.currentPage
      console.log("confirmImportData", data, currentPage)
      if (Array.isArray(dataList) && dataList.length > 0) {
        this.isShowImportDialog = false
        const [err, res] = await to(this.$apis.apiBackgroundCarTravelCarTravelInfoBatchImportCarTravelInfoPost({ url: dataList }))
        if (err) {
          this.$message.error('导入失败 ' + err.message)
          return
        }
        if (res.code === 0) {
          this.$message.success('导入成功')
          this.getCustomerBindingList()
        } else {
          this.$message.error('导入失败 ' + res.msg)
        }
      } else {
        this.$message.error('请先导入数据')
      }
    },
    /**
     * 获取模板链接
     */
    getTempUrl() {
      var url = URL_TEMPLATE_MODEL
      url = process.env.NODE_ENV === 'development' ? "https://cashier-v4.debug.packertec.com/" + url : location.origin + url
      console.log("url", url);
      return url
    },
    /**
     * 删除按钮点击提示
     */
    handlerDeleteBinding(row) {
      var id = row.id || ''
      console.log("row", row);
      this.$confirm('是否删除当前客户号，删除后将影响该客户号在掌银移动端的正常登录！', '提示', {
        confirmButtonText: '确定',
        type: 'warning'
      }).then(() => {
        this.unbindingCarNo(id)
      })
    },
    /**
     * 联网解绑删除
     */
    async unbindingCarNo(id) {
      let params = {
        id: id,
        is_delete: true
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiCardServiceThirdCardUserAbcClientModifyPost(params))
      this.isLoading = false

      if (err) {
        this.$message.error('删除失败 ' + err.message)
        return
      }

      if (res.code === 0) {
        this.$message.success('删除成功')
        this.isShowEditDialog = false
        this.getCustomerBindingList()
      } else {
        this.$message.error('删除失败 ' + res.msg)
      }
    },
    /**
     * 打印
     */
    gotoExport() {
      var params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const option = {
        url: 'apiCardServiceThirdCardUserClientNumberExportPost',
        type: this.printType,
        params: params
      }
      this.exportHandle(option)
    },
    // 是否显示弹窗
    handlerShowDialog(type, row) {
      console.log("handlerShowDialog", type, row);
      this.addDialogType = type
      this.addDialogTitle = type === 'add' ? '新增' : '编辑'
      if (this.$refs.addCustomerDialogRef && row) {
        this.$refs.addCustomerDialogRef.setInfoData(row)
      }
      this.isShowAddDialog = true
    },
    // 确认新增或编辑
    confirmCustomerBinding(data) {
      console.log("confirmCustomerBinding", data);
      this.isShowAddDialog = false
      this.currentPage = 1
      this.getCustomerBindingList()
    },
    closeDialog() {
      this.isShowAddDialog = false
    }
  }
}

</script>

<style lang="scss" scoped>
.customerbinding {
  .el-table {
    border: 1px solid #fff;
  }

  .dialog-person-info {
    width: 250px;
    height: 100px;
    background-color: #F2F2F2;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

}
</style>
