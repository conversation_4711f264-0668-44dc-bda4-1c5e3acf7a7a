import * as dayjs from 'dayjs'
import { MEAL_TYPES } from '@/utils/constants'

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const FoodSaleRanking = {
  select_time: {
    type: 'daterange',
    label: '就餐时间',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  // meal_type: {
  //   type: 'select',
  //   value: '',
  //   label: '餐段',
  //   clearable: true,
  //   dataList: [
  //     {
  //       label: '全部',
  //       value: ''
  //     },
  //     ...MEAL_TYPES
  //   ]
  // },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  food_name: {
    type: 'input',
    value: '',
    label: '菜品名称',
    maxlength: 20,
    placeholder: '请输入菜品名称'
  }
}

export const SummaryOfSales = {
  select_time: {
    type: 'daterange',
    label: '就餐时间',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}

// 取用量绑定表单表头
export const TABLE_HEAD_DATA_DISHES_TAKEN = [
  { label: '所属组织', key: 'org_name' },
  { label: '菜品一级分类', key: 'sort' },
  { label: '菜品二级分类', key: 'category' },
  { label: '菜品名称', key: 'name' },
  { label: '取用量', key: 'food_weight', type: "slot", slotName: 'foodWeight' },
  { label: '取菜人数', key: 'use_count' },
  { label: '平均取用量', key: 'average_weight',type: "slot", slotName: 'averageWeight' }
]

// 取用量筛选设置
export const SEARCH_FORM_SET_DATA_DISHES_TAKEN = {
  select_time: {
    type: 'daterange',
    label: '就餐时间',
    value: [],
    format: 'yyyy-MM-dd',
    clearable: false
  },
  food_name: {
    type: 'input',
    value: '',
    label: '菜品名称',
    maxlength: 20,
    placeholder: '请输入菜品名称'
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  meal_type: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  }
}
