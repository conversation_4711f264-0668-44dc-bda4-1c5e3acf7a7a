<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :user-info="userInfo"
    @close="handleClose"
    customClass="ps-dialog"
    width="750px"
  >
    <el-form
      :model="dialogForm"
      @submit.native.prevent
      status-icon
      inline
      ref="dialogForm"
      :rules="dialogFormRules"
      label-width="120px"
      class="dialog-form"
    >
      <div>
        <div class="title">人员信息</div>
        <el-form-item label="姓名："><span class="content">{{userInfo.name}}</span></el-form-item>
        <el-form-item label="人员编号："><span class="content">{{userInfo.person_no}}</span></el-form-item>
        <!-- <el-form-item label="人群："><span class="content">{{dialogForm.}}</span></el-form-item> -->
        <el-form-item label="分组："><span class="content">{{userInfo.card_user_group_alias}}</span></el-form-item>
        <el-form-item label="部门："><span class="content">{{userInfo.card_department_group_alias}}</span></el-form-item>
        <el-form-item label="储值钱包余额："><span class="content">{{userInfo.balance | formatMoney }}</span></el-form-item>
        <el-form-item label="补贴钱包余额："><span class="content">{{userInfo.subsidy_balance | formatMoney }}</span></el-form-item>
      </div>
      <div style="border-top: 1px #E4E8EE solid; padding-top: 20px;" v-if="type === 'repair' || type === 'publish'">
        <div class="title">填写信息</div>
        <div v-if="type === 'repair'">
          <el-form-item label="原卡号："><span>{{userInfo.card_no}}</span></el-form-item>
          <el-form-item v-if="!dialogForm.isUseOld" label="新卡号：" prop="newCardNo">
            <el-input v-model="dialogForm.newCardNo" placeholder="请输入新卡号" class="ps-input"></el-input>
          </el-form-item>
          <el-form-item label-width="40px" label=" ">
            <el-checkbox v-model="dialogForm.isUseOld" class="ps-checkbox">沿用旧卡</el-checkbox>
          </el-form-item>
        </div>
        <div v-else>
          <el-form-item label="卡号：" prop="cardNo">
            <el-input v-model="dialogForm.cardNo" placeholder="请输入卡号" class="ps-input"></el-input>
          </el-form-item>
        </div>
      </div>
      <div v-if="type === 'repair' || type === 'publish'" style="border-top: 1px #E4E8EE solid; padding-top: 20px;">
        <div class="title">收款信息</div>
        <el-form-item :label="type === 'repair'?'补卡费':'工本费：'"><span class="content">{{type === 'repair'?patchCost:flatCost}}元</span></el-form-item>
        <el-form-item label="收款方式">
          <el-radio class="ps-radio" v-for="item in payMethodList" :key="item.key" v-model="dialogForm.payMethod" :label="item.key">{{item.name}}</el-radio>
        </el-form-item>
        <el-form-item v-if="type === 'publish' && dialogForm.isDisplay" label="是否扣费">
            <el-radio class="ps-radio" v-model="dialogForm.isDeduet" :label="true">扣费</el-radio>
            <el-radio class="ps-radio" v-model="dialogForm.isDeduet" :label="false">不扣费</el-radio>
        </el-form-item>
        <!-- <el-form-item v-if="dialogForm.payChoice==='CHANNEL'" label="渠道收款">
          <el-select
            v-model="dialogForm.channel"
            placeholder="请下拉选择"
            class="ps-select"
            popper-class="ps-popper-select"
          >
            <el-option
              v-for="item in dialogForm.channelList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item> -->
      </div>
      <div v-if="type === 'quit'">
        <div class="title">卡信息</div>
        <el-form-item label="卡号："><span class="content">{{userInfo.card_no}}</span></el-form-item>
        <el-form-item label="需退还工本费："><span class="content">{{flatCostFee}}元</span></el-form-item>
        <el-form-item label="退还方式">
          <el-radio class="ps-radio" v-for="item in payMethodList" :key="item.key" v-model="dialogForm.payMethod" :label="item.key">{{item.name}}</el-radio>
        </el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { divide, to } from '@/utils';
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'userDialog',
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: 'repair'
    },
    title: {
      type: String,
      default: '补卡'
    },
    isshow: Boolean,
    userInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    confirm: Function
  },
  // mixins: [activatedLoadData],
  data() {
    var cardNoValidate = (rule, value, callback) => {
      if (value === '') {
        callback(new Error("请输入卡号"));
      } else if (value && !/^[a-zA-Z0-9_]+$/i.test(value)) {
        callback(new Error("请输入正确的卡号"));
      } else {
        callback();
      }
    };
    return {
      isLoading: false,
      dialogForm: {
        cardNo: '',
        newCardNo: '',
        isUseOld: false, // 是否沿用旧卡
        payMethod: '',
        channel: '',
        channelList: [{
          value: 'WECHAT',
          name: '微信支付(C扫B)'
        }, {
          value: 'ALIPAY',
          name: '支付宝支付(C扫B)'
        }, {
          value: 'ABC',
          name: '农行支付(C扫B)'
        }],
        isDeduet: false, // 是否默认扣费
        isDisplay: false // 是否展示默认扣费
      },
      dialogFormRules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        cardNo: [{ required: true, validator: cardNoValidate, trigger: "blur" }],
        newCardNo: [{ required: true, validator: cardNoValidate, trigger: "blur" }]
      },
      payMethodList: [],
      balance: '',
      subsidyBalance: '',
      flatCost: '',
      flatCostFee: '',
      patchCost: ''
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        console.log("visible");
        this.getPayMethod()
        this.initLoad()
      }
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    initLoad() {
      // 如果是发卡就获取
      console.log("initLoad ..... ", this.type);
      if (this.type === 'publish') {
        this.getDeductInfo(this.userInfo.id)
      }
    },
    clickConfirmHandle() {
      this.$refs.dialogForm.validate(valid => {
        if (valid) {
          let params = {
            card_user_id: this.userInfo.id
          }
          if (this.type === 'repair') {
            params.card_no = this.dialogForm.newCardNo ? this.dialogForm.newCardNo : this.userInfo.card_no
            params.is_open_card = this.dialogForm.isUseOld ? 0 : 1
            if (!this.dialogForm.isUseOld) {
              params.pay_method = this.dialogForm.payMethod
            }
            this.repairCard(params)
          } else if (this.type === 'publish') {
            params.card_no = this.dialogForm.cardNo
            params.pay_method = this.dialogForm.payMethod
            if (this.dialogForm.isDisplay) {
              params.is_deduct = this.dialogForm.isDeduet
            }
            this.publishCard(params)
          } else if (this.type === 'quit') {
            params.pay_method = this.dialogForm.payMethod
            this.quitCard(params)
          }
        } else {
        }
      })
    },
    async repairCard(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardOperateChangePost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    async publishCard(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardOperatePublishPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    async quitCard(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardOperateCardQuitPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.dialogForm.resetFields()
    },
    async getPayMethod() {
      const res = await this.$apis.apiCardServiceFlatCostGetPayMethodListPost({
        card_user_id: this.userInfo.id
      })
      if (res.code === 0) {
        this.payMethodList = res.data.pay_method_list
        this.balance = divide(res.data.balance)
        this.subsidyBalance = divide(res.data.subsidy_balance)
        this.flatCost = divide(res.data.flat_cost)
        if (res.data.delay_flat_cost === 3) {
          this.flatCostFee = 0 // 对于已操作退费的，此时退卡需退还的工本费应该是0
        } else {
          this.flatCostFee = divide(res.data.flat_cost_fee) // 用户工本费没有扣费情况下退卡
        }
        this.patchCost = divide(res.data.patch_cost)
        this.dialogForm.payMethod = res.data.pay_method_list[0].key
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取 扣费信息（发卡的时候判断要不要给用户选择扣费按钮和按钮默认值）
    async getDeductInfo(id) {
      const [err, res] = await to(this.$apis.apiCardServiceCardOperateDeductInfoPost({
        card_user_id: id
      }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        var isDisplay = data.is_display || false
        var defaultValue = data.default_value || false
        console.log("data", data);
        this.$set(this.dialogForm, 'isDisplay', isDisplay)
        this.$set(this.dialogForm, 'isDeduet', defaultValue)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.dialog-form {
  .title{
    font-size: 16px;
    font-weight: bold;
    border-left: 5px #ff9b45 solid;
    padding: 0 10px;
    margin-bottom: 10px;
  }
  .content{
    width: 100px;
    display: inline-block;
  }
}
</style>
