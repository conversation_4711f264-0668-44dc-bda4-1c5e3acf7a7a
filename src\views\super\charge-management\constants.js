import * as dayjs from 'dayjs'
import NP from 'number-precision'

export const RECENTSEVEN = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const chargeOrderTableSetting = [
  { label: '商户', key: 'company_name' },
  { label: '订单号', key: 'trade_no' },
  { label: '创建时间', key: 'create_time' },
  { label: '支付时间', key: 'pay_time' },
  { label: '到账时间', key: 'finish_time' },
  { label: '支付金额', key: 'real_fee', type: "slot", slotName: "realFee" },
  { label: '支付方式', key: 'pay_method_alias' },
  { label: '交易类型', key: 'transaction_type_alias' },
  { label: '交易内容', key: 'transaction_type', type: "slot", slotName: "transactionContent" },
  { label: '订单状态', key: 'order_status_alias' },
  { label: '转账凭证', key: 'voucher_url', type: "slot", slotName: "voucherUrl" },
  { label: '发票申请', key: 'invoice_status', type: "slot", slotName: "invoiceStatus" },
  { label: '操作员', key: 'operator' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
]

export const ProjectAcceptanceTableSetting = [
  { label: '项目点', key: 'org_name' },
  { label: '项目状态', key: 'project_status_alias' },
  { label: '到期时间', key: 'service_end_time' },
  { label: '验收意愿', key: 'will_alias' },
  { label: '验收人', key: 'acceptance_name' },
  { label: '联系电话', key: 'phone' },
  { label: '问题反馈', key: 'remark', type: "slot", slotName: "remark" },
  { label: '提交人', key: 'operator_name' },
  { label: '创建时间', key: 'create_time' }
]

export const chargeTrailTableSetting = [
  { type: 'selection', width: "55", reserveSelection: true },
  { label: '商户名称', key: 'company_name' },
  { label: '项目状态', key: 'project_status_alias' },
  { label: '收费模式', key: 'toll_type', type: "slot", slotName: "tollType" },
  { label: '系统版本', key: 'toll_version_name' },
  { label: '收费规则', key: 'toll_rule', type: "slot", slotName: "tollRule" },
  { label: '用户规模', key: 'user_scale', type: "slot", slotName: "userScale" },
  { label: '规模使用率', key: 'use_user_rate', type: "slot", slotName: "useUserRate" },
  { label: '用户数预警', key: 'is_user_scale_warning_alias' },
  { label: '使用期限', key: 'date_range', type: "slot", slotName: "dateRange" },
  { label: '距离到期', key: 'due_day_num', type: "slot", slotName: "dueDayNum" },
  { label: '到期预警', key: 'is_expire_warning_alias' }
]

export const chargeRulesTableSetting = [
  { label: '规则名称', key: 'name' },
  { label: '系统版本', key: 'toll_version_name' },
  { label: '收费规则', key: 'alias' },
  { label: '使用商户数', key: 'use_count' },
  { label: '创建人', key: 'creater_alias' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "200" }
]

// 格式化金额/100
export const divide = money => {
  // if (!money) return '0.00'
  if (typeof money === 'number') {
    return NP.divide(money, 100).toFixed(2)
  } else if (typeof money === 'string' && !isNaN(Number(money))) {
    return NP.divide(money, 100).toFixed(2)
  } else {
    return money
  }
}
export const times = money => {
  return NP.times(money, 100)
}
