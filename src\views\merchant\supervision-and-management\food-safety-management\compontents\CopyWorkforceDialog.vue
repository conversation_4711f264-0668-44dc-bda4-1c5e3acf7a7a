<template>
  <el-drawer :visible.sync="visible" :title="title" :loading.sync="isLoading" @close="handleClose" size="678px"
    :wrapperClosable="false" class="ps-el-drawer">
    <div class="p-10">
      <div>已选择天数：{{ days }} 天</div>
      <el-date-picker v-model="chooseDate" type="daterange" :clearable="true" format="yyyy-MM-dd"
        value-format="yyyy-MM-dd" align="left" unlink-panels range-separator="⇀" start-placeholder="请选择起始日期"
        end-placeholder="请选择结束日期" :picker-options="pickerOptions" class="ps-picker m-t-10"
        popper-class="ps-poper-picker" @change="dateChange">
      </el-date-picker>
    </div>
    <div class="ps-el-drawer-footer m-l-20">
      <el-button :disabled="isBtnLoading" class="ps-cancel-btn" @click="clickCancleHandle">
        取消
      </el-button>
      <el-button class="ps-btn" type="primary" @click="clickConfirmHandle"
        :disabled="isBtnLoading || !chooseDate || chooseDate.length <= 0" v-loading="isBtnLoading">
        确定
      </el-button>
    </div>
  </el-drawer>
  <!-- end -->
</template>

<script>
import { to, deepClone } from '@/utils'

export default {
  name: 'CopyWorkforceDialog',
  props: {
    loading: Boolean,
    isshow: Boolean,
    title: {
      type: String,
      default: '请选择复制到的日期'
    },
    limitId: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: 'default'
    },
    useDate: {
      type: String,
      default: ''
    },
    confirm: Function
  },

  data() {
    return {
      isLoading: false,
      isBtnLoading: false,
      chooseDate: [],
      personList: [],
      days: 0, // 天数
      pickerOptions: {
        // 禁用当前以前的日期
        disabledDate(time) {
          const today = new Date();
          today.setHours(0, 0, 0, 0); // 将时间设置为当天的0点0分0秒
          return time.getTime() < today.getTime()
        }
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        console.log("this.visible", this.personList)
      }
    }
  },
  mounted() { },
  methods: {
    clickCancleHandle() {
      this.handleClose()
    },
    // 确认选择
    async clickConfirmHandle() {
      if (!this.chooseDate || this.chooseDate.length <= 0) {
        return this.$message.error("请选择日期")
      }
      let selectListId = this.personList.map(item => item.id)
      this.isBtnLoading = true
      let params = {
        job_person_ids: selectListId,
        start_date: this.chooseDate[0] || '',
        end_date: this.chooseDate[1] || '',
        copy_date: this.useDate,
        operate_type: 'copy'
      }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionCanteenSafetyManagementModifyPersonSchedulePost(params))
      this.isBtnLoading = false
      if (err) {
        return this.$message.error('保存失败')
      }
      if (res && res.code === 0) {
        this.$message.success('保存成功')
        this.$emit('confirm', this.personList, this.chooseDate)
      } else {
        this.$message.error(res.msg)
      }
    },

    // 关闭
    handleClose(e) {
      this.isLoading = false
      this.chooseDate = []
      this.visible = false
      this.type = 'default'
      this.personList = []
      this.days = 0
      this.$emit('close', false)
    },
    // 设置人员列表
    setPersonList(list) {
      if (!list) {
        return
      }
      this.personList = deepClone(list)
    },
    // 日期选择
    dateChange(value) {
      console.log("dateChange", value);
      this.days = this.calculateDays(value[0], value[1])
    },
    calculateDays(date1, date2) {
      // 解析输入的日期
      const startDate = new Date(date1 + ' 00:00:00');
      const endDate = new Date(date2 + ' 23:59:59');

      // 检查日期是否有效
      if (isNaN(startDate) || isNaN(endDate)) {
        alert('请输入有效的日期格式 (YYYY-MM-DD)');
        return;
      }

      // 计算时间差（毫秒）
      const timeDifference = endDate - startDate;

      // 将时间差转换为天数
      const daysDifference = timeDifference / (1000 * 60 * 60 * 24);

      // 更新数据
      return Math.abs(Math.round(daysDifference));
    }

  }
}
</script>

<style lang="scss" scoped>
.ps-picker {
  width: 400px !important;

}
</style>
