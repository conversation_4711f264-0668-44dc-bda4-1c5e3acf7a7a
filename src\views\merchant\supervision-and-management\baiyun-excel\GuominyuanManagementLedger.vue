<!--食品安全风险管控清单-->
<template>
  <div class="guominyuan-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"
      :autoSearch="false" @reset="resetHandler"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="clickHistoricalRecords">历史记录</button-icon>
          <button-icon color="origin" @click="handlerShowDialog('add', null)" v-permission="['background_fund_supervision.ledger_food_safety.allergen_config_add']">过敏原配置</button-icon>
          <button-icon color="origin" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="gotoExport" v-permission="['background_fund_supervision.ledger_food_safety.get_allergen_use_record_ledger_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe
          header-row-class-name="ps-table-header-row" height="calc(100vh - 640px)" :max-height="600">
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operatorUsername="{ row }">
               <div>{{ getOperatorUsername(row) }}</div>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-origin" @click="gotoDetail(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount">
        </pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 添加弹窗 -->
    <configuration-add-edit-drawer ref="configurationAddEditDrawer" @confirm="confirmRecordDialog" @close="closeDialog" :isshow="dialogVisible"  :type="dialogType" :drawerData="drawerData" :keyType="keyType" />
    <!-- 历史记录抽屉 -->
    <HistoryRecordDrawer
      :visible.sync="isShowRecordDialog"
      title="历史记录"
      api="apiBackgroundFundSupervisionLedgerFoodSafetyOperateLogList"
      :type="keyType"
      @close="isShowRecordDialog = false"
    >
    </HistoryRecordDrawer>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import { SEARCH_SETTING_GUO_MIN_YUAN_LEDGER, TABLE_HEAD_DATA_GUO_MIN_YUAN_LEDGER } from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report'
import ConfigurationAddEditDrawer from './compontents/ConfigurationAddEditDrawer.vue'
import HistoryRecordDrawer from './compontents/HistoryRecordDrawer.vue'
export default {
  name: 'GuominyuanManagementLedger',
  mixins: [exportExcel, report],
  components: {
    ConfigurationAddEditDrawer,
    HistoryRecordDrawer
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_GUO_MIN_YUAN_LEDGER), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_GUO_MIN_YUAN_LEDGER), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_GUO_MIN_YUAN_LEDGER), // 查询表单配置
      printType: 'GuominyuanManagementLedger', // 类型
      isShowRecordDialog: false, // 历史记录
      dialogType: '', // 弹窗类型
      dialogTitle: '', // 弹窗标题
      dialogVisible: false, // 弹窗是否显示
      drawerData: {}, // 弹窗数据
      keyType: 'Allergen' // 配置项类型
    }
  },
  created() {
    const date = this.$route.query.date
    if (date) {
      this.searchFormSetting.select_time.value = [date, date]
    }
    this.initLoad()
  },
  mounted() { },
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取提交记录
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetAllergenUseRecordLedger(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log('handleSelectionChange', val)
      this.selectedIds = val.map(item => item.id)
      this.chooseData = deepClone(val)
    },
    // 查看详情
    gotoDetail(data) {
      console.log('gotoDetail', data)
      this.dialogType = 'detail'
      this.dialogTitle = '详情'
      this.drawerData = deepClone(data || {})
      this.dialogVisible = true
    },
    // 导出弹窗
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetAllergenUseRecordLedgerExport',
        params: params
      }
      this.exportHandle(option)
    },
    // 点击历史记录
    clickHistoricalRecords() {
      this.isShowRecordDialog = true
    },
    // 添加弹窗
    handlerShowDialog(type, data) {
      this.dialogType = type
      this.dialogTitle = type === 'add' ? '添加' : '编辑'
      if (type === 'add') {
        this.drawerData = {}
      } else {
        this.drawerData = deepClone(data || {})
      }
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
    },
    // 确认
    confirmRecordDialog(data) {
      console.log('confirmRecordDialog', data)
      this.dialogVisible = false
    },
    // 获取操作员名称
    getOperatorUsername(row) {
      let operatorMemberName = row.operator_member_name || ''
      let operatorUsername = row.operator_username || ''
      return operatorMemberName + (operatorUsername ? `(${operatorUsername})` : '')
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.tableSetting)
      tableSetting = tableSetting.filter(item => item.key !== 'operation')
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '过敏原使用管理台账',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetAllergenUseRecordLedger', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
.guominyuan-list {
  ::v-deep .el-dialog {
    .el-dialog__body {
      padding: 0 20px !important;
    }
  }

  .dialog-content {
    padding: 10px 0;
    text-align: left;

    .dialog-content-title {
      margin-bottom: 15px;
      font-size: 14px;
    }

    .reject-reason {
      text-align: left;

      .label {
        margin-bottom: 8px;
        font-size: 14px;

        &.required::before {
          content: '*';
          color: #F56C6C;
          margin-right: 4px;
        }
      }
    }
  }
}
</style>
