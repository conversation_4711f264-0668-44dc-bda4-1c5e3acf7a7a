<template>
  <div class="TransferOrder container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表
          <span class="inline-block m-l-20 font-size-16">当前仓库：<span style="color: 000; font-weight: 700;">{{$route.query.warehouse_name}}</span></span>
        </div>
        <div class="align-r">
          <button-icon color="origin" @click="gotoHandle('add')">新增调拨单</button-icon>
          <!-- <button-icon color="plain" @click="openImport">导入数据</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button v-if="row.transfer_type === 'lend' && row.transfer_status === 'returned_pend_confirm'" type="text" size="small" class="ps-text" @click="clickOperationHandle('confirmReturn', row)">确认归还</el-button>
              <el-button v-if="row.transfer_type === 'lend' && row.transfer_status !== 'finish'" type="text" size="small" class="ps-text" @click="clickOperationHandle('sales', row)" >销单</el-button>
              <el-button v-if="(row.transfer_type === 'borrow') && row.transfer_status === 'pend_returned'" type="text" size="small" class="ps-text" @click="gotoHandle('return', row)">归还</el-button>
              <el-button v-if="row.transfer_type === 'borrow' && row.transfer_status === 'pend_confirm'" type="text" size="small" class="ps-text" @click="clickOperationHandle('receipt', row)">确认收货</el-button>
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
              <!-- <el-button type="text" size="small" class="ps-text" @click="clickOperationHandle('delete', row)">删除</el-button> -->
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <dialog-message :show.sync="showDialog" title="新增单位" width="435px" @close="closeDialogHandle">
      <el-form :model="dialogForm" :rules="dialogrules" label-position="left" label-width="80px" size="medium">
        <el-form-item label="单位名称" label-width="80px">
          <el-input v-model="dialogForm.name" class="ps-input"></el-input>
        </el-form-item>
        <el-form-item label="关联食物" prop="org_id" label-width="80px">
          <!-- <el-input v-model="dialogForm.org_id" class="ps-input"></el-input> -->
          <el-select class="" style="width:100%;">
            <el-option></el-option>
            <el-option></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="tool" class="footer-center m-t-60">
        <el-button :disabled="dialogLoading" class="ps-cancel-btn w-150" @click="closeDialogHandle">取消</el-button>
        <el-button :disabled="dialogLoading" class="ps-btn w-150" type="primary" @click="clickDialogConfirm">确定</el-button>
      </div>
    </dialog-message>
    <!-- end -->
    <!-- dialog start -->
    <import-page-dialog v-if="showImportDialog" :show.sync="showImportDialog" :header-len="importHeaderLen" :templateUrl="importTemplateUrl" :url="importApi" :paramsData="importParamsData"></import-page-dialog>
    <!-- dialog end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入

export default {
  name: 'TransferOrder',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '提交时间', key: 'create_time' },
        { label: '单据编号', key: 'trade_no' },
        { label: '关联业务号', key: 'contact_trade_text', showTooltip: true },
        { label: '调拨仓库', key: 'transfer_warehouse_name' },
        { label: '经手人', key: 'account_name' },
        { label: '调拨类型', key: 'transfer_type_alias' },
        { label: '状态', key: 'transfer_status_alias' },
        { label: '单据备注', key: 'remark', showTooltip: true },
        { label: '预计归还日期', key: 'expected_return_date' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '提交时间',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        },
        transfer_type: {
          type: 'select',
          label: '调拨类型',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '借出',
              value: 'lend'
            },
            {
              label: '借入',
              value: 'borrow'
            }
          ]
        },
        transfer_status: {
          type: 'select',
          label: '状态',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            }, {
              label: '待归还',
              value: 'pend_returned'
            },
            {
              label: '待确认',
              value: 'pend_confirm'
            },
            {
              label: '归还待确认',
              value: 'returned_pend_confirm'
            },
            {
              label: '已归还',
              value: 'returned'
            },
            {
              label: '已结束',
              value: 'finish'
            }
          ]
        }
      },
      showDialog: false,
      dialogLoading: false,
      dialogForm: {
        name: '',
        org_id: ''
      },
      dialogrules: {
        // name: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }]
      },
      // 导入的弹窗数据
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/调拨单导入.xlsx',
      importHeaderLen: 2,
      importApi: 'apiBackgroundDrpTransferInfoImportDataPost',
      importParamsData: {
        warehouse_id: this.$route.query.warehouse_id
      }
    }
  },
  watch: {
    'searchFormSetting.transfer_type.value': function(val) {
      console.log(val)
      this.searchFormSetting.order_status.value = ''
      if (val === 'lend') {
        this.searchFormSetting.order_status.dataList = [
          {
            label: '待归还',
            value: 'pend_returned'
          },
          {
            label: '归还待确认',
            value: 'returned_pend_confirm'
          },
          {
            label: '已归还',
            value: 'returned'
          }
        ]
      } else if (val === 'borrow') {
        this.searchFormSetting.order_status.dataList = [
          {
            label: '待归还',
            value: 'pend_returned'
          },
          {
            label: '待确认',
            value: 'pend_confirm'
          },
          {
            label: '归还待确认',
            value: 'returned_pend_confirm'
          },
          {
            label: '已归还',
            value: 'returned'
          }
        ]
      } else {
        this.searchFormSetting.order_status.dataList = [
          {
            label: '全部',
            value: ''
          }, {
            label: '待归还',
            value: 'pend_returned'
          },
          {
            label: '待确认',
            value: 'pend_confirm'
          },
          {
            label: '归还待确认',
            value: 'returned_pend_confirm'
          },
          {
            label: '已归还',
            value: 'returned'
          }
        ]
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.$route.query.currentPage) {
        this.currentPage = Number(this.$route.query.currentPage)
      }
      this.getTransferOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key.indexOf('time') < 0) {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 切换tab
    changeTabHandle(e) {
      this.searchHandle()
      this.replaceHash()
    },
    // 保存下参数
    replaceHash() {
      this.$router.replace({
        name: "TransferOrder",
        query: {
          currentPage: this.currentPage
        }
      })
    },
    // 获取列表数据
    async getTransferOrderList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: +this.$route.query.warehouse_id,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpTransferInfoListPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          if (v.contact_trade_list && v.contact_trade_list.length > 0) {
            v.contact_trade_text = v.contact_trade_list.join('，')
          } else {
            v.contact_trade_text = ''
          }
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getTransferOrderList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = ''
      switch (type) {
        case 'confirmReturn':
          title = '确认归还吗？'
          apiUrl = 'apiBackgroundDrpTransferInfoConfirmReturnPost'
          this.showOperationDialog(title, apiUrl, { id: data.id, warehouse_id: +this.$route.query.warehouse_id })
          break;
        case 'delete':
          title = '确定删除吗？'
          // apiUrl = 'apiBackgroundDrpInquiryDeletePost'
          // this.showOperationDialog(title, apiUrl, { ids: [data.id] })
          break;
        case 'sales':
          title = '确定销单吗？'
          apiUrl = 'apiBackgroundDrpTransferInfoSalesOrderPost'
          this.showOperationDialog(title, apiUrl, { id: data.id, warehouse_id: +this.$route.query.warehouse_id })
          break;
        case 'receipt':
          title = '确定收货吗？'
          apiUrl = 'apiBackgroundDrpTransferInfoConfirmReceiptPost'
          this.showOperationDialog(title, apiUrl, { id: data.id, warehouse_id: +this.$route.query.warehouse_id })
          break;
      }
    },
    // 显示操作弹窗
    showOperationDialog(title, apiUrl, params) {
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis[apiUrl](params)
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getTransferOrderList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    showDialogHandle(data) {
      this.showDialog = true
    },
    closeDialogHandle() {
      this.showDialog = false
    },
    clickDialogConfirm() {
      this.showDialog = false
    },
    // 导入
    openImport(type) {
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    // 跳转
    gotoHandle(type, row) {
      // 新增
      if (type === 'add') {
        this.$router.push({
          name: 'AddTransferOrder',
          query: this.$route.query,
          params: {
            type
          }
        })
        return
      }
      // 详情
      if (type === 'detail') {
        this.$router.push({
          name: 'TransferOrderDetail',
          query: {
            type,
            id: row.id,
            ...this.$route.query
          }
        })
        return
      }
      // 归还
      if (type === 'return') {
        this.$router.push({
          name: 'ReturnTransferOrder',
          query: {
            type,
            id: row.id,
            ...this.$route.query
          }
        })
        return
      }
    },
    // 导出
    handleExport(row) {
      const option = {
        type: 'InquiryList',
        url: 'apiBackgroundDrpInquiryExportInquiryPost',
        params: {
          id: row.id
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.TransferOrder{
  .w-medium{
    width: 140px;
    height: 40px;
    &.m-r-20{
      margin-right: 20px;
    }
  }
}
</style>
