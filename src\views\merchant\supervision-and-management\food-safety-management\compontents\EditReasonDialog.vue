<template>
  <el-dialog :visible.sync="visible" :title="title" :width="width">
    <div class="dialog-content">
      <div class="">
        <el-input v-model="formData.reason" placeholder="请输入" clearable maxlength="50" show-word-limit
          style="width: 360px;" type="textarea" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handlerClose">
          取消
        </el-button>
        <el-button type="primary" @click="handlerConfirm">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
import { to } from '@/utils/index'

export default {
  name: 'EditReasonDialog',
  props: {
    loading: Boolean,
    isshow: Boolean,
    title: { // 标题
      type: String,
      default: '修改原因'
    },
    width: { // 宽度
      type: String,
      default: '400px'
    },
    dialogType: { // 类型
      type: String,
      default: 'notEntry'
    },
    content: {
      type: String,
      default: ''
    },
    id: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      isLoading: false,
      formData: {
        reason: ''
      }
    }
  },
  computed: {
    visible: {
      get() {
        console.log("visible", this.isshow);
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.formData.reason = this.content
      }
    }
  },
  mounted() { },
  methods: {
    // 关闭
    handlerClose() {
      this.visible = false
      this.$emit('close', false)
    },
    handlerConfirm() {
      if (!this.formData.reason || !this.formData.reason.trim()) {
        return this.$message.error('请输入原因')
      }
      this.modifyReason()
    },
    // 修改原因
    async modifyReason() {
      console.log("this.type", this.type);
      let params = {
        id: this.id,
        reason: this.formData.reason
      }
      this.isLoading = true
      const [err, res] = await to(
        this.dialogType === "notEntry"
          ? this.$apis.apiBackgroundFundSupervisionChannelCanteenManagementModifyNotEntryReasonPost(params)
          : this.$apis.apiBackgroundFundSupervisionChannelCanteenManagementModifyNotReservedReasonPost(params)
      )
      this.isLoading = false
      if (err) {
        return
      }
      if (res && res.code === 0) {
        this.$message.success("修改成功")
        this.$emit('confirm', this.formData)
        this.visible = false
      } else {
        this.$message.error(res.msg || "修改失败")
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-footer {
  text-align: center;
}
</style>
