<template>
  <dialog-message
    :show.sync="visible"
    title="安全校验"
    :loading.sync="isLoading"
    custom-class="ps-dialog ps-paysetting-dialog"
    top="20vh"
    width="520px"
    @close="clickCancel"
  >
    <div class="red m-b-20">提示：修改必要信息需要进行安全校验</div>
    <!-- :width="width" -->
    <el-form :model="dialogForm" label-width="110px" @submit.native.prevent status-icon ref="dialogFormRef">
      <el-form-item label="帐号：">
        <div>{{ userInfo.member_name }}（{{ userInfo.username }}）</div>
      </el-form-item>
      <el-form-item label="手机号：">{{ userInfo.mobile }}</el-form-item>
      <el-form-item prop="smsCode" class="phone-code">
        <!-- :disabled="sendCodeDisabled" -->
        <verification-code
          placeholder="请输入手机验证码"
          :sendAuthCode="sendAuthCode"
          @click="getPhoneCode"
          :reset-handle="resetHandle"
          v-model="dialogForm.smsCode"
        ></verification-code>
      </el-form-item>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right">
        <el-button class="ps-cancel-btn" size="small" @click="clickCancel">取消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          :disabled="dialogForm.smsCode && dialogForm.smsCode.length > 6"
          size="small"
          @click="clickConfirmHandle"
        >
          保存并校验
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'MealNutritionDialog',
  props: {
    loading: Boolean,
    isshow: Boolean
    // confirm: Function
  },
  data() {
    return {
      isLoading: false,
      dialogForm: {
        smsCode: ''
      },
      sendAuthCode: true
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  created() {},
  mounted() {
    console.log(this.userInfo, 9999)
  },
  methods: {
    clickCancel() {
      this.$emit('phoneVerificationCancel')
    },
    clickConfirmHandle() {
      // this.$emit('phoneVerificationConfirm')
      this.setCheckVerificationCode()
    },
    async getPhoneCode() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundVerificationCodeAutoPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.sendAuthCode = false
        this.$message.success('发送成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 校验
    async setCheckVerificationCode() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundCheckVerificationCodePost({
          sms_code: this.dialogForm.smsCode
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$emit('phoneVerificationConfirm')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重置验证码倒计时
    resetHandle(e) {
      this.sendAuthCode = true
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';
</style>
