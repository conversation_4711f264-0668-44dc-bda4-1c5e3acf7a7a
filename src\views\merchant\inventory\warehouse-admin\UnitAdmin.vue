<template>
  <div class="unit-admin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon
            v-permission="['background_drp.unit_management.batch_modify_unit']"
            color="origin"
            @click="showImportDialog = true"
            size="medium"
          >
            导入
          </button-icon>
          <button-icon
            color="origin"
            v-permission="['background_drp.unit_management.add']"
            @click="showDialogHandle('add')"
            size="medium"
          >
            新增
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #ingredient="{ row }">
              <span>{{ getingredientName(row) }}</span>
            </template>
            <template #operation="{ row }">
              <!-- <el-button v-permission="['background_drp.unit_management.modify']" type="text" size="small" class="ps-text" @click="showDialogHandle('ingredient', row)" >关联食物</el-button> -->
              <!-- <el-button  v-permission="['background_drp.unit_management.modify']" type="text" size="small" class="ps-text" :disabled="!!row.enable"  @click="showDialogHandle('modify', row)">编辑</el-button> -->
              <el-button
                v-permission="['background_drp.unit_management.delete']"
                type="text"
                size="small"
                class="ps-text"
                :disabled="!!row.enable"
                @click="deleteHandle(row)"
              >
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <dialog-message :show.sync="showDialog" :title="dialogTitle" width="435px" @close="closeDialogHandle">
      <el-form
        v-loading="dialogLoading"
        ref="dialogFormRef"
        :model="dialogForm"
        :rules="dialogrules"
        label-position="left"
        label-width="80px"
        size="medium"
      >
        <el-form-item v-if="dialogType !== 'ingredient'" label="单位名称" label-width="80px" prop="name">
          <el-input
            :disabled="dialogType !== 'add'"
            v-model="dialogForm.name"
            :maxlength="15"
            class="ps-input"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="关联食物" prop="ingredient_ids" label-width="80px">
          <el-select v-model="dialogForm.ingredient_ids" :loading="remoteLoading" multiple clearable collapse-tags filterable class="ps-select" popper-class="ps-popper-select" style="width:100%;">
            <el-option v-for="option in ingredientList" :key="option.id" :label="option.name" :value="option.id" ></el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
      <div slot="tool" class="footer-center m-t-60">
        <el-button :disabled="dialogLoading" class="ps-cancel-btn w-150" @click="closeDialogHandle">取消</el-button>
        <el-button :disabled="dialogLoading" class="ps-btn w-150" type="primary" @click="clickDialogConfirm">
          确定
        </el-button>
      </div>
    </dialog-message>
    <!-- end -->
    <!-- 导入数据的弹窗 start -->
    <!-- <import-dialog
      :templateUrl="templateUrl"
      :tableSetting="importTableSetting"
      :show.sync="importShowDialog"
      :title="importDialogTitle"
      :openExcelType="openExcelType"
      :params="importParams"
    ></import-dialog> -->
    <import-page-dialog
      v-if="showImportDialog"
      :show.sync="showImportDialog"
      :header-len="2"
      :templateUrl="importTemplateUrl"
      :url="importApi"
    ></import-page-dialog>
    <!-- 导入数据的弹窗 end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getRequestParams } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入

export default {
  name: 'InventoryUnitAdmin',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 列表数据
      tableSettings: [
        { label: '单位名称', key: 'name' },
        // { label: '关联食物', key: 'ingredient_name', type: "slot", slotName: "ingredient", showTooltip: true },
        { label: '创建时间', key: 'create_time' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
      ],
      searchFormSetting: {
        name: {
          type: 'input',
          value: '',
          label: '单位名称',
          placeholder: '请输入单位名称'
        }
      },
      // 弹窗
      showDialog: false,
      dialogLoading: false,
      dialogType: '',
      dialogTitle: '新增单位',
      dialogForm: {
        id: '',
        name: '',
        ingredient_ids: []
      },
      dialogrules: {
        name: [{ required: true, message: '请输入单位名称', trigger: 'blur' }]
      },
      remoteLoading: false, // 食材数据的加载状态
      ingredientList: [], // 食材列表
      // 导入的弹窗数据
      // importDialogTitle: '',
      // importShowDialog: false,
      // templateUrl: '',
      // openExcelType: '',
      // importTableSetting: [],
      // importParams: {}, // 导入的参数
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/导入单位模板.xlsx',
      importApi: 'apiBackgroundDrpUnitManagementBatchModifyUnitPost'
    }
  },
  created() {
    this.initLoad()
    this.getIngredientList()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getUnitList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 获取列表数据
    async getUnitList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
        // organization_id: this.$store.getters.organization
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpUnitManagementListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          v.is_enable = !!v.enable
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getUnitList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 获取关联食材的名字集合
    getingredientName(row) {
      let result = ''
      if (row.ingredient_info) {
        result = row.ingredient_info.reduce((prev, next) => {
          if (prev) {
            return `${prev}、${next.name}`
          } else {
            return next.name
          }
        }, '')
      }
      return result
    },
    // 获取食材列表
    async getIngredientList() {
      this.remoteLoading = true
      let params = {
        page: 1,
        page_size: 999999
      }
      const [err, res] = await to(this.$apis.apiBackgroundFoodIngredientListPost(params))
      this.remoteLoading = false
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.ingredientList = res.data.results
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 删除
    async deleteHandle(data) {
      this.$confirm('确定删除吗？', `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundDrpUnitManagementDeletePost({
                id: data.id
              })
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              if (res.data && res.data.exist) {
                await this.$sleep(300)
                this.switchStatus(data, 1)
              } else {
                this.$message.success(res.msg)
                this.searchHandle()
              }
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              data.is_enable = !data.is_enable
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 重置弹窗表单
    clearDialogForm() {
      this.dialogForm = {
        id: '',
        name: '',
        ingredient_ids: []
      }
      this.$refs.dialogFormRef.resetFields()
    },
    // 显示弹窗
    showDialogHandle(type, data) {
      this.dialogType = type
      switch (type) {
        case 'modify':
          this.dialogTitle = '编辑单位'
          break
        case 'ingredient':
          this.dialogTitle = '关联食物'
          break
        default:
          this.dialogTitle = '新增单位'
          break
      }
      if (type === 'modify' || type === 'ingredient') {
        this.dialogForm.id = data.id
        if (data.ingredient_info) {
          this.dialogForm.ingredient_ids = data.ingredient_info.map(v => {
            return v.id
          })
        }
        this.dialogForm.name = data.name
      }
      this.showDialog = true
    },
    // 关闭弹窗
    closeDialogHandle() {
      this.clearDialogForm()
      this.showDialog = false
    },
    // 弹窗确定事件
    clickDialogConfirm() {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          if (this.dialogType === 'add') {
            this.addUnitHandle()
          } else {
            this.modifyUnitHandle()
          }
        } else {
        }
      })
    },
    // 新增单位
    async addUnitHandle() {
      if (this.dialogLoading) return
      this.dialogLoading = true
      let params = {
        // organization_id: this.$store.getters.organization,
        name: this.dialogForm.name
      }
      // if (this.dialogForm.ingredient_ids.length) {
      //   params.ingredient_ids = this.dialogForm.ingredient_ids
      // }
      const [err, res] = await to(this.$apis.apiBackgroundDrpUnitManagementAddPost(params))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg || '添加成功')
        this.searchHandle()
        this.clearDialogForm()
        this.showDialog = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑单位数据
    async modifyUnitHandle() {
      if (this.dialogLoading) return
      this.dialogLoading = true
      let params = {
        id: this.dialogForm.id
        // organization_id: this.$store.getters.organization
      }
      // if (this.dialogType !== 'add') {
      //   params.ingredient_ids = this.dialogForm.ingredient_ids
      // }
      const [err, res] = await to(this.$apis.apiBackgroundDrpUnitManagementModifyPost(params))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg || '添加成功')
        this.searchHandle()
        this.clearDialogForm()
        this.showDialog = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导入
    openImport(type) {
      this.importDialogTitle = '批量导入'
      this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入卡号.xls'
      this.openExcelType = type
      this.importTableSetting = [
        { key: 'card_no1', label: '单位名称' },
        { key: 'card_no', label: '关联食物' }
      ]
      this.importParams = {}
      this.importShowDialog = true
    }
  }
}
</script>

<style lang="scss" scoped></style>
