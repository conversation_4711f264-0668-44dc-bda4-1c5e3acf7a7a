<template>
  <div class="DeductStockDialog">
    <!-- 成功的 -->
    <el-dialog
      title="出库"
      :visible.sync="visible"
      top="20vh"
      custom-class="ps-dialog"
      show-close
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        v-loading="isLoading"
        :model="formData"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="出库数量" prop="stock_num">
          <el-input
            v-model="formData.stock_num"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            maxlength="4"
            class="ps-input w-350"
          ></el-input>
        </el-form-item>
        <el-form-item label="出库原因" prop="type">
          <el-select
            v-model="formData.type"
            class="ps-select w-350"
            popper-class="ps-popper-select"
            clearable
            filterable
            placeholder="请选择分类"
            @change="handleTypeChange"
          >
            <el-option
              v-for="item in typeList"
              :key="item.name"
              :label="item.name"
              :value="item.type"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出库备注" prop="remark">
          <el-input
            type="textarea"
            class="ps-input w-350"
            :autosize="{ minRows: 4, maxRows: 8 }"
            placeholder="请输入内容"
            maxlength="30"
            show-word-limit
            v-model="formData.remark"
          ></el-input>
        </el-form-item>
      </el-form>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="canceDialogHandle">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          v-loading="isLoading"
          @click="clickDetermineDialog"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { to } from '@/utils'
export default {
  props: {
    isshow: Boolean,
    type: {
      type: String,
      default: ''
    },
    selectListId: {
      type: Array,
      default() {
        return []
      }
    },
    confirm: Function
  },
  data() {
    return {
      isLoading: false,
      rules: {
        stock_num: [{ required: true, message: '请输入出库数量', trigger: ['blur', 'change'] }],
        type: [{ required: true, message: '请选择出库原因', trigger: ['blur', 'change'] }],
        remark: [{ required: false, message: '请输入出库备注', trigger: ['blur', 'change'] }]
      },
      formData: {
        stock_num: '',
        type: '',
        remark: ''
      },
      typeList: [
        {
          name: '销售出库',
          type: 'sale'
        },
        {
          name: '盘点出库',
          type: 'check'
        },
        {
          name: '保质期出库',
          type: 'allot'
        },
        {
          name: '破损出库',
          type: 'breakage'
        },
        {
          name: '其他',
          type: 'other'
        }
      ]
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {},
  methods: {
    handleTypeChange() {
      // 如果选择的是 "其他" 类型，出库备注必填
      this.rules.remark = [
        {
          required: this.formData.type === 'other',
          message: '请输入出库备注',
          trigger: ['blur', 'change']
        }
      ]
    },
    canceDialogHandle() {
      this.visible = false
    },
    clickDetermineDialog() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.getGoodsDeductStock()
          console.log(this.formData)
        }
      })
    },
    async getGoodsDeductStock() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsDeductStockPost({
          ids: this.selectListId,
          stock_num: Number(this.formData.stock_num),
          type: this.formData.type,
          remark: this.formData.remark
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$emit('deductStockSuccess')
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.DeductStockDialog {
}
</style>
