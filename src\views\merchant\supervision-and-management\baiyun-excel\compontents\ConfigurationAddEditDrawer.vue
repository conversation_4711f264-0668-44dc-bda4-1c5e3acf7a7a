<template>
  <custom-drawer :show.sync="visible" cancelText="关闭" :confirmShow="type === 'add'" :loading="isLoading" :title="title"
    :size="size" :wrapperClosable="false" :showClose="false" @confirm="confirmDialog" @cancel="closeDialog"
    :printShow="false" :exportShow="false">
    <div class="table-content m-t-20">
      <!-- 表单包装表格 start -->
      <el-form ref="formRef" :model="formData" :rules="formRules" label-position="left">
        <el-form-item v-for="(item, index) in formData.tableData" :key="index" :label="item.label + (index + 1)"
          :prop="`tableData.${index}.value`" :rules="formRules.tableData">
          <div class="ps-flex">
          <!-- 当前输入限制统一40个字符 -->
          <el-input class="input-item" v-model="item.value" placeholder="请输入" maxlength="40" />
             <div v-if="index === formData.tableData.length - 1">
              <img src="@/assets/img/plus.png" class="m-l-20 ic-32" @click.stop="handlerAddItem" />
             </div>
             <div v-if="index > 0">
              <img src="@/assets/img/reduce_red.png" class="m-l-20 ic-32" @click="handlerDeleteItem(item, index)"
               />
             </div>
          </div>
        </el-form-item>
      </el-form>
      <!-- 表单包装表格 end -->
    </div>
  </custom-drawer>
</template>

<script>
import { to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report'
export default {
  name: 'ConfigurationAddEditDrawer',
  props: {
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    },
    type: {
      type: String,
      default: 'add'
    },
    size: {
      type: String,
      default: '40%'
    },
    isshow: Boolean,
    keyType: { // 配置项类型
      type: String,
      default: 'Disease' // Disease : "疾病特征"  Egg : "蛋类名称"  Allergen : "过敏原"  Additive : "添加剂名称"  Inspection : "巡检项目"  WipingAgent : "擦拭剂"  Sanitizer : "消毒剂"  Tool : "工具名称"  CookingUtensils : "餐炊具名称"  DeliveryUnit : "配送单位信息"
    }
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      // 表单数据
      formData: {
        tableData: []
      },
      keyName: '', // 配置项名称
      // 表单验证规则
      formRules: {
        tableData: [
          { required: true, message: '请输入配置项名称', trigger: 'blur' }
        ]
      },
      title: '' // 弹窗标题
    }
  },
  mixins: [exportExcel, report],
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    isshow(val) {
      if (val) {
        // 初始化数据
        this.initData()
      }
    }
  },
  created() {
  },
  mounted() { },
  methods: {
    // 初始化数据
    async initData() {
      switch (this.keyType) {
        case 'Disease':
          this.keyName = '疾病特征'
          break;
        case 'Egg':
          this.keyName = '蛋类名称'
          break;
        case 'Allergen':
          this.keyName = '过敏原'
          break;
        case 'Additive':
          this.keyName = '添加剂名称'
          break;
        case 'Inspection':
          this.keyName = '巡检项目'
          break;
        case 'WipingAgent':
          this.keyName = '擦拭剂'
          break;
        case 'Sanitizer':
          this.keyName = '消毒剂'
          break;
        case 'Tool':
          this.keyName = '工具名称'
          break;
        case 'CookingUtensils':
          this.keyName = '餐炊具名称'
          break;
        case 'DeliveryUnit':
          this.keyName = '配送单位信息'
          break;
        default:
          this.keyName = ''
          break;
      }
      let configData = await this.getConfigList()
      console.log('configData', configData)
      if (configData && configData.length > 0) {
        this.formData.tableData = configData
      } else {
        this.formData.tableData = [{
          label: this.keyName,
          value: ''
        }]
      }
      this.title = this.keyName + '配置'
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate()
        }
      })
    },
    // 获取配置项
    getConfigList() {
      return new Promise((resolve) => {
        let params = {
          ledger_data_type: this.keyType
        }
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetLedgerItemConfig(params).then(res => {
          console.log('res', res)
          if (res && res.code === 0) {
            let data = res.data || []
            if (data && data.length > 0) {
              const tag = data[0] || {}
              const ledgerItemConfig = tag.ledger_item_config || []
              let newList = []
              if (ledgerItemConfig && ledgerItemConfig.length > 0) {
                ledgerItemConfig.forEach((item, index) => {
                  newList.push({
                    label: this.keyName,
                    value: item
                  })
                })
              }
              resolve(newList)
            } else {
              resolve([])
            }
          } else {
            this.$message.error(res.msg || '获取配置项失败')
            resolve(false)
          }
        }).catch(err => {
          this.$message.error(err.message || '获取配置项失败')
          resolve(false)
        })
      })
    },

    async confirmDialog(e) {
      console.log('confirmDialog', this.formData.tableData)
      try {
        // 先进行表单验证
        this.$refs.formRef.validate((valid) => {
          if (valid) {
            // 验证通过
            this.saveData()
            // this.visible = false
            // this.$emit('confirm', this.formData.tableData)
          } else {
            // 再手动检查所有责任人字段
            const emptyFields = this.formData.tableData.filter((item, index) => {
              return !item.responsible_person || item.responsible_person.trim() === ''
            })
            const firstEmptyIndex = this.formData.tableData.findIndex(item => !item.value || item.value.trim() === '')
            if (emptyFields.length > 0) {
              this.$message.error('序号' + (emptyFields[0].index || (firstEmptyIndex + 1)) + '未填写')
              return
            }
            return
          }
        })
      } catch (error) {
        // 验证失败，显示错误信息
        console.log('error', error)
        this.$message.error('请填写所有配置项')
        return
      }
    },
    // 保存数据
    async saveData() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyModifyLedgerItemConfig({
          ledger_data_type: this.keyType,
          ledger_item_config: this.formData.tableData.map(item => item.value)
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '保存失败')
        return
      }
      if (res && res.code === 0) {
        this.$message.success('保存成功')
        this.visible = false
        this.$emit('confirm', this.formData.tableData)
      } else {
        this.$message.error(res.msg || '保存失败')
      }
    },
    // 添加配置项
    handlerAddItem() {
      this.formData.tableData.push({
        label: this.keyName,
        value: ''
      })
    },
    // 删除配置项
    handlerDeleteItem(item, index) {
      console.log('item', item)
      this.formData.tableData.splice(index, 1)
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.input-item {
  width: 400px;
}

.multiline-text {
  white-space: pre-wrap;
  text-align: left;
}

.penson-txt {
  text-align: center;
}
</style>
