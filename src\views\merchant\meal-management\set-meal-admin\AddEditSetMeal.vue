<template>
  <div class="add-edit-set-meal container-wrapper">
    <el-form
      v-loading="isLoading"
      :rules="formRuls"
      :model="formData"
      ref="formIngredients"
      size="small"
      label-width="80px"
    >
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">套餐基本信息</div>
        </div>
        <div style="padding: 0 20px">
          <el-form-item label="套餐名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入标题"
              class="ps-input p-r-48"
              style="width: 240px"
              maxlength="15"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="套餐分类" prop="category_id">
            <el-select
              v-model="formData.category_id"
              placeholder="请下拉选择"
              class="ps-select"
              popper-class="ps-popper-select"
            >
              <el-option
                v-for="(item, index) in categoryList"
                :key="index"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="套餐图片" prop="image">
            <el-upload
              class="avatar-uploader"
              :headers="headersOpts"
              :data="uploadParams"
              ref="uploadFood"
              :action="actionUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <el-image v-if="formData.image" :src="formData.image" class="avatar" fit="cover" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div class="el-upload__tip" slot="tip">图片仅支持jpg，png格式，且不超过5M</div>
            </el-upload>
          </el-form-item>
          <el-form-item label="套餐价格" label-width="100px">
            <el-radio class="ps-radio" v-model="formData.price_type" label="total">
              菜品价格合计
            </el-radio>
            <el-radio class="ps-radio" v-model="formData.price_type" label="fixed">
              固定套餐价格
            </el-radio>
            <el-form-item
              :key="formData.price_type"
              class="form-item--inline no-label w-300"
              prop="fixed_price"
              label=""
              :rules="formRuls.fixed_price"
            >
              <el-input
                class="ps-input"
                :disabled="formData.price_type !== 'fixed'"
                v-model="formData.fixed_price"
                style="width: 100px;"
              ></el-input>
            </el-form-item>
          </el-form-item>
          <el-form-item label="套餐打包费" label-width="100px" prop="pack_price">
            <el-input
              v-model="formData.pack_price"
              placeholder="请输入套餐打包费"
              class="ps-input p-r-48"
              style="width: 240px"
              show-word-limit
            >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="table-header">
          <div class="table-title">套餐样式</div>
        </div>
        <div style="padding: 0 20px">
          <el-form-item class="" label="菜品是否可重复选择" label-width="170px">
            <el-radio-group class="ps-radio" v-model="formData.food_repeat">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="" label="">
            <div class="ps-red">
              勾选“是”的情况下，不同样式内同个菜品用户可重复选择；勾选“否”则选过菜品，在其他样式内用户不可再选
            </div>
          </el-form-item>
          <div class="p-b-10" style="color:#229bff; cursor: pointer" @click="addStyle">
            添加样式
          </div>
          <div
            class="set-meal-table"
            v-for="(item, index) in formData.set_meal_style_data"
            :key="index"
          >
            <div class="p-t-10">
              <el-form-item
                label="名称"
                :key="index"
                :rules="formRuls.name"
                :prop="'set_meal_style_data[' + index + '].set_meal_style_name'"
              >
                <div class="ps-flex-bw ">
                  <el-input
                    v-model="item.set_meal_style_name"
                    placeholder="请输入名称"
                    class="ps-input p-r-48"
                    style="width: 240px"
                    maxlength="15"
                    show-word-limit
                  ></el-input>
                  <div class="ps-red p-r-10" style="cursor: pointer" @click="delStyle(index)">
                    删除样式
                  </div>
                </div>
              </el-form-item>
            </div>
            <el-form-item label="样式内菜品是否必选" prop="" label-width="170px">
              <el-checkbox
                v-model="item.food_required"
                :true-label="1"
                :false-label="0"
                class="ps-checkbox"
              >
                必选
              </el-checkbox>
            </el-form-item>

            <el-form-item label="" prop="">
              <div class="m-r-10">
                <el-table
                  :data="item.foods"
                  style="width: 100%"
                  border
                  header-row-class-name="ps-table-header-row"
                >
                  <el-table-column
                    prop="food_name"
                    label="菜品名称"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="food_category"
                    label="菜品分类"
                    align="center"
                  ></el-table-column>
                  <el-table-column prop="" label="规格" align="center">
                    <template slot-scope="scope">
                      <el-select
                        v-model="scope.row.spec_id"
                        placeholder="请下拉选择"
                        class="ps-select"
                        popper-class="ps-popper-select"
                        style="width: 100px"
                        @change="specsChange(scope.row)"
                      >
                        <el-option
                          v-for="(specsItem, specsIndex) in scope.row.spec_list"
                          :key="specsIndex"
                          :label="specsItem.name"
                          :value="specsItem.id"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column prop="food_price" label="菜品价格" align="center">
                    <template slot-scope="scope">
                      {{ scope.row.food_price | formatMoney }}
                    </template>
                  </el-table-column>
                  <el-table-column fixed="right" label="操作" width="180" align="center">
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        size="small"
                        class="ps-warn"
                        @click="delSetMealFood(index, scope.$index)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                  <template #append>
                    <div class="t-a-c">
                      <el-button type="text" size="medium" @click="addSetMealFood(index)">
                        添加菜品
                        <i class="el-icon-plus el-icon--right"></i>
                      </el-button>
                    </div>
                  </template>
                </el-table>
              </div>
            </el-form-item>
          </div>
        </div>
      </div>

      <div class="footer" style="margin-top: 20px">
        <el-button style="width: 120px" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitHandler">
          {{ type === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-form>
    <div v-if="showFoodDialog">
      <el-dialog
        title="添加菜品"
        :visible.sync="showFoodDialog"
        width="900px"
        top="20vh"
        custom-class="ps-dialog"
        :close-on-click-modal="false"
      >
        <!-- v-loading="formFoodLoading" -->
        <div>
          <set-meal-dialog ref="setMealDialogRef" :alreadyFoodData="alreadyFoodData" />
          <!-- :currentEditData="currentEditData" -->
          <!-- :currentEditDate="currentEditDate" -->
          <!-- :mealType="currentEditMealType" -->
          <!-- @select-food="selectFoodHandle" -->
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="showFoodDialog = false">取 消</el-button>
          <el-button size="small" type="primary" @click="confirmSetMealDialog">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, divide, deepClone, times, getToken } from '@/utils'

import { validataPlateAmountPrice } from '@/assets/js/validata'
import SetMealDialog from './components/SetMealDialog'

export default {
  name: 'AddEditSetMeal',
  // mixins: [activatedLoadData, exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      type: 'add', // 类型
      formData: {
        name: '', // 套餐名称
        category_id: '', // 套餐分类
        image: '', // 套餐图片
        price_type: 'total', // 套餐价格类型
        fixed_price: '', // 固定费用
        pack_price: '', // 打包费
        food_repeat: 0, // 菜品是否可重复
        set_meal_style_data: []
      },
      alreadyFoodData: [], // 当前的样式菜品数据
      categoryList: [],
      formRuls: {
        name: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur'
          }
        ],
        category_id: [
          {
            required: true,
            message: '请选择分类',
            trigger: 'blur'
          }
        ],
        fixed_price: [
          {
            required: false,
            validator: validataPlateAmountPrice,
            message: '请输入正确固定套餐价格',
            trigger: 'blur'
          }
        ],
        pack_price: [
          {
            required: false,
            validator: validataPlateAmountPrice,
            message: '请正确套餐打包费',
            trigger: 'blur'
          }
        ]
      },
      actionUrl: '/api/background/file/upload',
      uploadParams: {
        prefix: 'setMeal'
      },
      headersOpts: {
        TOKEN: getToken()
      },
      showFoodDialog: false,
      setMealFoodIndex: 0
    }
  },
  components: {
    SetMealDialog
  },
  created() {
    // this.getUploadToken()
    this.type = this.$route.query.type
    this.getSetMealCategoryList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.type === 'modify') {
        let data = this.$decodeQuery(this.$route.query.data)
        this.formData = {
          id: data.id,
          name: data.name, // 套餐名称
          category_id: data.category, // 套餐分类
          image: data.image, // 套餐图片
          price_type: data.price_type, // 套餐价格类型
          fixed_price: divide(data.fixed_price), // 固定费用
          pack_price: divide(data.pack_price), // 打包费
          food_repeat: data.food_repeat, // 菜品是否可重复
          set_meal_style_data: data.set_meal_style
        }
        console.log(data)
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
    }, 300),
    async getUploadToken() {
      const res = await this.$apis.getUploadToken({
        prefix: 'setMeal'
      })
      if (res.code === 0) {
        this.actionUrl = res.data.host
        this.uploadParams = {
          key: res.data.prefix + new Date().getTime() + Math.floor(Math.random() * 150),
          prefix: res.data.prefix,
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          signature: res.data.signature,
          callback: res.data.callback,
          success_action_status: '200'
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分类列表
    async getSetMealCategoryList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodSetMealCategoryListPost({
          page: 1,
          page_size: 9999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.categoryList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // async getArticleChildTagList() {
    //   const [err, res] = await to(
    //     this.$apis.apiBackgroundArticleTagChildListPost({
    //       page: 1,
    //       page_size: 10000
    //     })
    //   )
    //   if (err) {
    //     this.$message.error(err.message)
    //     return
    //   }
    //   if (res.code === 0) {
    //     // 一级分类
    //     this.tagsList = res.data.results
    //     this.initLoad()
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    handleAvatarSuccess(res, file) {
      if (res.code === 0) {
        this.$refs.uploadFood.clearFiles()
        this.formData.image = res.data.public_url
        // this.getUploadToken()
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 5
      this.uploadParams.key = new Date().getTime() + Math.floor(Math.random() * 150)
      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG PNG格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 5MB!')
      }
      return isJPG && isLt2M
    },
    // 添加样式
    addStyle() {
      this.formData.set_meal_style_data.push({
        id: 0, // 新增传0
        set_meal_style_name: '',
        food_required: 0,
        foods: []
      })
    },
    delStyle(index) {
      this.formData.set_meal_style_data.splice(index, 1)
    },
    // 添加菜品
    addSetMealFood(index) {
      this.showFoodDialog = true
      this.setMealFoodIndex = index
      this.alreadyFoodData = this.formData.set_meal_style_data[index].foods
      // this.formData.set_meal_style_data[index].foods.push({
      //   name: 111,
      //   classify: '真香',
      //   specs: '',
      //   food_price: '10'
      // })
    },
    // 删除菜品
    delSetMealFood(index, tableIndex) {
      this.formData.set_meal_style_data[index].foods.splice(tableIndex, 1)
    },
    specsChange(specsItem, index, tableIndex) {
      let obj = specsItem.spec_list.filter(i => i.id === specsItem.spec_id)[0]
      this.$set(specsItem, 'food_price', obj.food_price)
    },
    confirmSetMealDialog() {
      const data = this.$refs.setMealDialogRef.getPostData()
      if (data.length) {
        this.showFoodDialog = false
        data.map(v => {
          this.formData.set_meal_style_data[this.setMealFoodIndex].foods.push({
            food_id: v.id, // 菜品id
            food_name: v.name,
            food_category: v.food_category,
            spec_id: v.spec_id,
            food_price: v.food_price,
            spec_list: v.spec_list
          })
        })
      } else {
        this.$message.error('请选择菜品')
      }
    },
    // 添加 编辑
    async foodModifySetMealAdd(params) {
      this.isLoading = true
      let [err, res] = ''
      if (this.type === 'add') {
        ;[err, res] = await to(this.$apis.apiBackgroundFoodSetMealAddPost(params))
      } else {
        ;[err, res] = await to(this.$apis.apiBackgroundFoodSetMealModifyPost(params))
      }
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 提交数据
    submitHandler() {
      this.$refs.formIngredients.validate(valid => {
        // 格式化数据
        let params = deepClone(this.formData)
        params.fixed_price = times(this.formData.fixed_price)
        params.pack_price = times(this.formData.pack_price)
        params.set_meal_style_data = params.set_meal_style_data.map(v => {
          v.food_data = v.foods.map(k => {
            return {
              food_id: k.food_id,
              spec_id: k.spec_id
            }
          })
          return {
            food_required: v.food_required,
            id: v.id,
            set_meal_style_name: v.set_meal_style_name,
            food_data: v.food_data
          }
        })
        if (valid) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          this.foodModifySetMealAdd(params)
        }
      })
    },

    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss">
.add-edit-set-meal {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .form-item--inline {
    display: inline-block;
  }
  .set-meal-table {
    width: 100%;
    border: 1px solid #ccc;
    margin-bottom: 20px;
  }
}
.el-input__count .el-input__count-inner {
  background-color: transparent !important;
}
</style>
