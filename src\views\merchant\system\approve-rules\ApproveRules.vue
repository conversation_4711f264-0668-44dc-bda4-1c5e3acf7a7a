<template>
  <div class="ApproveRules container-wrapper">
    <refresh-tool @refreshPage="refreshHandle">
      <template slot="title">
        <div class="">
          <el-button :class="[orderType === item.value ? 'ps-origin-btn' : '']" v-for="(item, index) in orderTypeList" :key="index" @click="changeOrderType(item.value)">{{ item.label }}</el-button>
        </div>
      </template>
    </refresh-tool>
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="del" v-permission="['background_approve.approve_rule.delete']" @click="mulOperation('del',{id:''})" v-loading='isDelLoading'>删除</button-icon>
          <button-icon color="origin" type="add" @click="gotoAddOrEdit('add')" v-permission="['background_approve.approve_rule.add']">新增规则</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="name" label="规则名称" align="center"></el-table-column>
          <el-table-column prop="approve_type_alias" label="审批类型" align="center"></el-table-column>
          <el-table-column prop="approve_method_alias" label="审批方式" align="center"></el-table-column>
          <el-table-column prop="status_alias" label="状态" align="center" width="90">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.rules_status" active-color="#ff9b45" inactive-color="#ffcda2" @change="mulOperation('status', scope.row)" :disabled="!allPermissions.includes('background_approve.approve_rule.status_modify')"></el-switch>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="openDialog(scope.row)"
                >历史记录</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-gree"
                @click="gotoAddOrEdit('detail', scope.row)"
                >查看</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                v-if="scope.row.status!=='enable'"
                @click="gotoAddOrEdit('edit', scope.row)"
                v-permission="['background_approve.approve_rule.modify']"
                >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <el-dialog
      title="历史记录"
      :visible.sync="historyDialogShow"
      width="400px"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false">
      <div>
        <el-table
          :data="selectInfo.record_list"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
        <el-table-column prop="time" label="操作时间" align="center"></el-table-column>
        <el-table-column prop="content" label="内容" align="center"></el-table-column>
        <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-btn" type="primary" @click="historyDialogShow = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  name: 'ApproveRulesList',
  data() {
    return {
      isLoading: false,
      orderType: 'company',
      orderTypeList: [
        { label: '项目规则', value: 'company' },
        { label: '组织规则', value: 'org' }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: {
        select_date: {
          type: 'daterange',
          label: '创建时间',
          value: [],
          clearable: true
        },
        approve_type: {
          type: 'select',
          label: '审批类型',
          value: '',
          clearable: true,
          placeholder: '请选择审批类型',
          dataList: [{
            label: '访客就餐',
            value: 'approve_order_visitor'
          }, {
            label: '自注册',
            value: 'register'
          }]
        }
      },
      selectListId: [],
      selectList: [], // 选择的列表
      historyTable: [],
      historyDialogShow: false,
      selectInfo: {},
      isDelLoading: false
    }
  },
  created() {
    let orgData = this.userInfo.organizationList.filter(item => item.id === this.organization)
    if (orgData[0].level === 0) {
      this.searchFormSetting.approve_type.dataList = [
        ...this.searchFormSetting.approve_type.dataList,
        {
          key: 'fund',
          name: '资金上传'
        },
        {
          key: 'finance',
          name: '财务审批'
        }
      ]
    }
  },
  mounted() {
    this.initLoad()
  },
  computed: {
    ...mapGetters(['allPermissions', 'userInfo'])
  },
  methods: {
    // 切换列表
    changeOrderType(e) {
      this.orderType = e
      this.currentPage = 1
      this.initLoad()
    },
    async initLoad() {
      this.getApproveRulesList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getApproveRulesList()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getApproveRulesList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundApproveApproveRuleListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize,
        rule_type: this.orderType
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results.map(item => {
          item.rules_status = !(item.status === 'disable')
          return item
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getApproveRulesList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      this.selectList = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
        this.selectList.push(item)
      })
    },
    // 操作提示
    mulOperation(type, data) {
      let title = '提示'
      let content = ''
      let status
      switch (type) {
        case 'del':
          content = '确定删除该规则吗？'
          var flag = false
          for (let i = 0; i < this.selectList.length; i++) {
            if (Reflect.has(this.selectList[i], 'status') && this.selectList[i].status === 'enable') {
              flag = true
              break
            }
          }
          if (flag) {
            return this.$message.error('不能删除启用的规则')
          }
          if (!this.selectListId || this.selectListId.length === 0) {
            return this.$message.error('请选择要删除的规则')
          }
          break;
        case 'status':
          if (data.status === 'disable') {
            content = '确定启用该规则吗？'
            status = 'enable'
          } else {
            content = '确定停用该规则吗？'
            status = 'disable'
          }
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {
              id: data.id
            }
            switch (type) {
              case 'del':
                params.status = 'delete'
                this.deleteOrder(this.selectListId)
                break;
              case 'status':
                params.status = status
                this.confirmStatus(params)
                break;
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            this.getApproveRulesList()
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    async confirmStatus(params) {
      const res = await this.$apis.apiBackgroundApproveApproveRuleStatusModifyPost(params)
      this.getApproveRulesList()
      if (res.code === 0) {
        this.$message.success('成功')
        this.getApproveRulesList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除规则
    async deleteOrder(idsList) {
      this.isDelLoading = true
      const res = await this.$apis.apiBackgroundApproveApproveRuleDeletePost({ ids: idsList })
      this.isDelLoading = false
      if (res && res.code === 0) {
        this.$message.success('成功')
        this.getApproveRulesList()
      } else {
        this.$message.error(res.msg || '删除失败')
      }
    },
    openDialog(data) {
      this.selectInfo = data
      this.historyDialogShow = true
    },
    gotoAddOrEdit(type, data) {
      let query = {
        rule_type: this.orderType,
        type
      }
      if (type === 'edit' || type === 'detail') {
        query.data = JSON.stringify(data)
      }
      this.$router.push({
        name: 'MerchantAddApproveRules',
        params: {
          type
        },
        query
      })
    }
  }
}
</script>
