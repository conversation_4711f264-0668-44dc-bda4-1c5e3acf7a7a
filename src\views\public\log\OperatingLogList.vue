<template>
  <div class="log-container container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="log-list">
      <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
        </div>
        <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          row-key="id"
          border
          :row-class-name="tableRowClassName"
          header-row-class-name="ps-table-header-row"
          class="ps-table"
        >
          <el-table-column prop="create_time" align="center" label="操作时间"></el-table-column>
          <el-table-column prop="module_operation" align="center" label="操作类型"></el-table-column>
          <el-table-column prop="detail" align="center" label="操作详情"></el-table-column>
          <el-table-column prop="operator" align="center" label="操作人员"></el-table-column>
        </el-table>
        <!-- table end -->
        </div>
      <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { to, debounce, getSevenDateRange } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'LogList',
  // mixins: [activatedLoadData],
  data() {
    const defaultdate = getSevenDateRange(7);
    return {
      searchForm: {
        operator: '',
        module: ''
      },
      tableData: [], // 列表数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      isLoading: false,
      selectDate: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      searchFormSetting: {
        select_date: {
          type: 'daterange',
          label: '操作时间',
          value: [defaultdate[0], defaultdate[1]]
        },
        module: {
          type: 'treeselect',
          multiple: false,
          flat: false,
          label: '操作类型',
          value: null,
          placeholder: '请选择操作类型',
          dataList: [],
          limit: 1,
          level: 0,
          normalizer: this.operationNode
        },
        operator: {
          type: 'input',
          label: this.$t('search.operation_staff'),
          value: '',
          placeholder: ''
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    initLoad() {
      this.getLogList()
      this.operationList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getLogList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取日志列表
    async getLogList () {
      this.isLoading = true
      let api
      if (this.$route.path === '/super_system/loglist') {
        api = this.$apis.apiBackgroundAdminLogListLogPost
      } else {
        api = this.$apis.apiBackgroundLogListPost
      }
      const [err, res] = await to(
        api({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getLogList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getLogList()
    },
    async operationList() {
      const res = await this.$apis.apiBackgroundAdminLogOperationLogTreePost();
      if (res.code === 0) {
        this.searchFormSetting.module.dataList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg);
      }
    },
    // 处理下没有children
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children) {
            if (item.children.length > 0) {
              traversal(item.children)
            } else {
              _that.$delete(item, 'children')
            }
          } else {
            _that.$delete(item, 'children')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    operationNode(node) {
      return {
        id: node.key,
        label: node.verbose_name,
        children: node.children
      }
    }
  }
}
</script>

<style lang="scss">
.log-container {
  // margin-top: 20px;
  // padding: 20px;
  .log-list {
    // background-color: #ffffff;
    min-width: 0;
    .search-form{
      padding: 20px 20px 0;
    }
    .table-wrapper{
      // margin-top: 0 !important;
    }
    .table-content{
      .no-backgroundColor:hover > td {
        background-color: #ffffff !important;
      }
      .backgroundColor:hover > td {
        background-color: #f5f7fa !important;
      }
      .backgroundColor{
        background-color: #f5f7fa;
        // height: 100%;
      }
      th.el-table-column--selection .cell{
        display: none;
      }
    }
    .column-flex{
      display: flex;
      justify-content: space-between;
      .column-l{
        flex: 1;
        margin-right: 30px;
      }
    }
  }
}
.ps-dialog{
  .dialog-form{
    .flex{
      display: flex;
      align-items: center;
    }
    .form-img{
      display: inline-block;
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-left: 10px;
      cursor: pointer;
      opacity: .8;
      &:hover{
        opacity: 1;
      }
    }
  }
}
</style>
