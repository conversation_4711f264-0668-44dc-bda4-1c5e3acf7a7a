<template>
  <div>
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="m-b-20">
      <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
        <el-radio-button v-for="tab in tabTypeList" :key="tab.key" :label="tab.key">{{ tab.label }}</el-radio-button>
      </el-radio-group>
    </div>
    <div class="container-wrapper">
      <InventoryManagement ref="inventoryManagement" v-show="tabType === 'inventoryManagement'" />
      <Costbreakdown ref="costbreakdown" v-show="tabType === 'costbreakdown'" />
      <InventoryBalanceSheet ref="inventoryBalanceSheet" v-show="tabType === 'inventoryBalanceSheet'" />
      <GrossProfitAnalysis ref="grossProfitAnalysis" v-show="tabType === 'grossProfitAnalysis'" />
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import InventoryManagement from "./InventoryManagement"
import Costbreakdown from "./Costbreakdown"
import InventoryBalanceSheet from "./InventoryBalanceSheet"
import GrossProfitAnalysis from "./GrossProfitAnalysis"
import { mapGetters } from 'vuex'

export default {
  name: 'Inventory',
  components: { InventoryManagement, Costbreakdown, InventoryBalanceSheet, GrossProfitAnalysis },
  data() {
    return {
      tabType: 'inventoryManagement',
      tabTypeList: [],
    }
  },
  computed: {
    ...mapGetters([
      'allPermissions'
    ])
  },
  created() {
  },
  mounted() {
    this.ininPermissions()
  },
  methods: {
    // 初始化下页面权限
    ininPermissions() {
      // const permissionVals = ['background_operation_management.order_evaluation.list', 'background_operation_management.order_evaluation.food_evaluation_list', 'background_food.food.food_evaluation_list']
      // let tabList = []
      // permissionVals.forEach(role => {
      //   if (this.allPermissions.includes(role)) {
      //     switch (role) {
      //       case 'background_operation_management.order_evaluation.list':
      //         tabList.push({
      //           label: '库存列表',
      //           key: 'inventoryManagement'
      //         })
      //         break;
      //       case 'background_operation_management.order_evaluation.food_evaluation_list':
      //         tabList.push({
      //           label: '成本明细',
      //           key: 'costbreakdown'
      //         })
      //         break;
      //       // case 'background_food.food.food_evaluation_list':
      //       //   tabList.push({
      //       //     label: '菜品评分排行',
      //       //     key: 'ratings'
      //       //   })
      //       //   break;
      //     }
      //   }
      // })
      let tabList = [
        {
          label: '库存列表',
          key: 'inventoryManagement'
        },
        {
          label: '成本明细',
          key: 'costbreakdown'
        },
        // {
        //   label: '库存余额表',
        //   key: 'inventoryBalanceSheet'
        // },
        // {
        //   label: '毛利分析',
        //   key: 'grossProfitAnalysis'
        // }
      ]
      this.tabType = tabList.length > 0 ? tabList[0].key : ''
      this.tabTypeList = tabList
      this.$nextTick(_ => {
        this.changeTabHandle()
      })
    },
    refreshHandle() {
      this.$refs[this.tabType].refreshHandle()
    },
    changeTabHandle(e) {
      this.$refs[this.tabType].initLoad()
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
