<template>
  <div class="nav-tabs-wrapper">
  <el-tabs type="card"
    v-model="activeTab"
    ref="tabRef"
    @tab-click="tabClickHandle"
    @tab-remove="tabRemoveHandle">
      <el-tab-pane
        v-for="(tab) in visitedViews"
        :key="tab.path"
        :name="tab.path"
        :label="generateTabTitle(tab.title, tab.path)"
        :closable="showClosable"
      ></el-tab-pane>
    </el-tabs>
    <!-- <div class="nav-arc">
      <div class="arc"></div>
    </div> -->
  </div>
</template>

<script>
import { generateTabTitle } from '@/utils/i18n'

export default {
  name: 'NavTabs',
  data() {
    return {
      activeTab: '',
      showClosable: false
    }
  },
  computed: {
    visitedViews() {
      // if (this.$store.state.navTabs.visitedViews.length > 1) {
      //   this.showClosable = true
      // } else {
      //   this.showClosable = false
      // }
      return this.$store.state.navTabs.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    }
  },
  watch: {
    $route() {
      this.addTags()
    },
    visitedViews(val) {
      if (val.length > 1) {
        this.showClosable = true
      } else {
        this.showClosable = false
      }
    }
  },
  created() {},
  mounted () {
    this.initTags()
    this.addTags()
  },
  methods: {
    async tabClickHandle() {
      for (let index = 0; index < this.visitedViews.length; index++) {
        const currentTab = this.visitedViews[index];
        console.log(currentTab)
        if (currentTab.path === this.activeTab && this.activeTab !== this.$route.path) {
          this.$router.push(currentTab)
          console.log('currentTab', currentTab)
          await this.$store.dispatch('navTabs/setHeaderMenu', currentTab.path)
          this.$store.dispatch('navTabs/upadtePermissions')
          break
        }
      }
      // this.$store.dispatch('navTabs/upadtePermissions')
    },
    async tabRemoveHandle(tab) {
      if (tab.indexOf('/meal_management/meal_apply/meal_apply/') !== -1 && this.$route.path.indexOf('/meal_management/meal_apply/meal_apply/') !== -1 && (this.$route.query.type === 'add' || this.$route.query.type === 'edit')) {
        return this.$eventBus.$emit('form-data-save')
      }
      if (this.visitedViews.length > 1) {
        for (let index = 0; index < this.visitedViews.length; index++) {
          if (this.visitedViews[index].path === tab) {
            this.closeSelectedTag(this.visitedViews[index])
            break
          }
        }
      } else {
        this.$message.error('不能再少了')
      }
      // this.visitedViews.forEach(item => {
      //   if (item.path === tab) {
      //     this.closeSelectedTag(item)
      //   }
      // })
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    filterTags(routes, basePath = '/') {
      let tags = []
      routes.forEach(route => {
        if (route.children) {
          const tempTags = this.filterTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    initTags() {
      const affixTags = this.affixTags = this.filterTags(this.routes)
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch('navTabs/addVisitedView', tag)
        }
      }
    },
    addTags() {
      const { name } = this.$route
      this.activeTab = this.$route.path
      if (name) {
        this.$store.dispatch('navTabs/addView', this.$route)
      }
      return false
    },
    refreshSelectedTag(view) {
      this.$store.dispatch('navTabs/delCachedView', view).then(() => {
        const { fullPath } = view
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    closeSelectedTag(view) {
      this.$store.dispatch('navTabs/delView', view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store.dispatch('navTabs/delOthersViews', this.selectedTag)
    },
    closeAllTags(view) {
      this.$store.dispatch('navTabs/delAllViews').then(({ visitedViews }) => {
        if (this.affixTags.some(tag => tag.path === view.path)) {
          return
        }
        this.toLastView(visitedViews, view)
      })
    },
    async toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      let currentPath = ''
      if (latestView) {
        currentPath = latestView.fullPath
      } else {
        currentPath = '/'
      }
      this.$router.push(currentPath)
      await this.$store.dispatch('navTabs/setHeaderMenu', currentPath)
      this.$store.dispatch('navTabs/upadtePermissions')
    },
    generateTabTitle
  }
}
</script>

<style lang="scss">
.nav-tabs-wrapper{
}
</style>
