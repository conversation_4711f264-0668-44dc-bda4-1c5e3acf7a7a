<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" v-permission="['background_order.finance_report.person_meal_list_export']" type="export" @click="gotoExport">导出Excel</button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :index="indexMethod"
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row"
          />
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <common-pagination
          ref="pagination"
          :total="total"
          :onPaginationChange="onPaginationChange"
        ></common-pagination>
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import CommonPagination from '../../meal-management/booking-setting/CommonPagination.vue'
import { PersonMealReportSearchForm } from './constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { deepClone } from '@/utils'

export default {
  name: 'DetailTotalList',
  components: {
    CommonPagination
  },
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '所属分组', key: 'payer_group' },
        { label: '餐段', key: 'meal_type_alias' },
        { label: '一级组织', key: 'primary' },
        { label: '二级组织', key: 'secondary' },
        { label: '三级组织', key: 'company' },
        { label: '食堂', key: 'canteen' },
        { label: '食堂区域', key: 'area' },
        { label: '消费点', key: 'consume_org_name' },
        { label: '点餐情况', key: 'meal_status_alias' }
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: deepClone(PersonMealReportSearchForm),
      collect: [
        // 统计
        { key: 'user_count', value: 0, label: '总人数:' },
        { key: 'stall_count', value: 0, label: '本档口点餐人数:' },
        { key: 'other_stall_count', value: 0, label: '其他档口点餐人数:' },
        { key: 'no_meal_count', value: 0, label: '未点餐人数:' }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'PersonMealReport',
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
  },
  mounted() {
    this.initPrintSetting()
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        this.getPersonMealReportList()
        this.requestDeviceType()
        // this.userGroupList() // 分组
        // this.getWalletList() // 动账钱包
      }
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.tableData = []
      this.isFirstSearch = true
      // this.onPaginationChange({ current: 1, pageSize: 10 })
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.$refs.pagination.handleCurrentChange(1, true)
        this.$refs.pagination.handleSizeChange(10, true)
        this.onPaginationChange({ current: 1, pageSize: 10 })
        this.isFirstSearch = false
      }
    },
    // 格式化查询参数
    formatQueryParams(data, currentPage, pageSize) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          params[key] = data[key].value
        }
      }
      params.page = currentPage
      params.page_size = pageSize
      return params
    },
    // 获取列表数据
    async getPersonMealReportList() {
      const params = this.formatQueryParams(this.searchFormSetting, this.page, this.pageSize)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportPersonMealListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.result
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 翻页
    onPaginationChange(data) {
      this.page = data.current
      this.pageSize = data.pageSize
      this.getPersonMealReportList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'ExportPersonMealReport',
        url: 'apiBackgroundReportCenterDataReportPersonMealListExportPost',
        params: this.formatQueryParams(this.searchFormSetting, this.page, this.pageSize)
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_type: this.printType,
          print_title: '人员就餐统计报表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportPersonMealListPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped></style>
