<template>
  <custom-drawer
    :title="title"
    :show.sync="visible"
    direction="rtl"
    :wrapperClosable="false"
    :size="size"
    class="related-drawer-wrapper"
    destroy-on-close
    v-bind="$attrs"
    v-on="$listeners"
    fixedFooter
    confirm-text="确定"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <!-- 判断下visible保证不用缓存页面 -->
    <div v-loading="isLoading" class="related-document p-l-20 p-r-20">
      <div v-if="type !== 'detail'">
        <!-- tab切换 -->
        <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn m-t-10">
          <el-radio-button v-for="tab in tabTypeList" :key="tab.value" :label="tab.value">{{ tab.label }}</el-radio-button>
        </el-radio-group>

        <!-- 采购单 -->
        <PurchaseOrder v-if="tabType === 'purchase'" :type="type" :params="apiParams" :api="api" :infoData="infoData"></PurchaseOrder>
        <!-- 配送单 -->
        <DeliveryOrder v-if="tabType === 'delivery'" :type="type" :params="apiParams" :api="api" :infoData="infoData"></DeliveryOrder>
        <!-- 收货单 -->
        <ReceiptOrder v-if="tabType === 'receiving'" :type="type" :params="apiParams" :api="api" :infoData="infoData"></ReceiptOrder>
        <!-- 结算单 -->
        <SettlementOrder v-if="tabType === 'finalstatement'" :type="type" :params="apiParams" :api="api" :infoData="infoData"></SettlementOrder>
      </div>
      <div v-else>
        <!-- 采购单 -->
        <PurchaseOrder v-if="orderType === 'purchase'" :type="type" :params="apiParams" :api="api" :infoData="infoData"></PurchaseOrder>
        <!-- 配送单 -->
        <DeliveryOrder v-if="orderType === 'delivery'" :type="type" :params="apiParams" :api="api" :infoData="infoData"></DeliveryOrder>
        <!-- 收货单 -->
        <ReceiptOrder v-if="orderType === 'receiving'" :type="type" :params="apiParams" :api="api" :infoData="infoData"></ReceiptOrder>
        <!-- 结算单 -->
        <SettlementOrder v-if="orderType === 'finalstatement'" :type="type" :params="apiParams" :api="api" :infoData="infoData"></SettlementOrder>
      </div>
    </div>
    <template slot="footer">
      <slot name="drawer-footer"></slot>
    </template>
  </custom-drawer>
  <!-- end -->
</template>

<script>
// import { debounce, to, divide } from '@/utils'
import DeliveryOrder from './DeliveryOrder'
import ReceiptOrder from './ReceiptOrder'
import SettlementOrder from './SettlementOrder'
import PurchaseOrder from './PurchaseOrder'

export default {
  name: 'RelatedDocument',
  props: {
    showdialog: Boolean,
    loading: Boolean,
    title: {
      type: String,
      default: '关联单据'
    },
    size: {
      type: [Number, String],
      default: 680
    },
    orderType: { // 订单类型
      type: String,
      default: ''
    },
    type: { // 弹窗类型，单据或者详情，结算单不用详情接口哦，其它的都走节口获取
      type: String,
      default: '' // order、detail
    },
    params: { // 接口参数
      type: Object,
      default: () => {
        // id: '',
        // xxx: ''
      }
    },
    infoData: {
      type: Object,
      default: () => {}
    },
    api: { // 接口key
      type: String,
      default: ''
    },
    confirm: Function
  },
  components: {
    DeliveryOrder,
    ReceiptOrder,
    SettlementOrder,
    PurchaseOrder
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      tabType: '',
      defaultTabList: [
        {
          label: '采购单',
          value: 'purchase'
        },
        {
          label: '配送单',
          value: 'delivery'
        },
        {
          label: '收货单',
          value: 'receiving'
        },
        {
          label: '结算单',
          value: 'finalstatement'
        }
      ],
      tabTypeList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    },
    apiParams() {
      let currentParams = {}
      if (this.type === 'detail') {
        currentParams = {
          id: this.infoData.id
          // ...this.params
        }
      } else {
        currentParams = {
          main_type: this.orderType,
          second_type: this.tabType,
          main_id: this.infoData.id
          // ...this.params
        }
      }
      return currentParams
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        this.initLoad()
      }
    }
  },
  mounted() {

  },
  methods: {
    initLoad() {
      // 过滤下tab选项
      if (this.type !== 'detail') {
        this.tabTypeList = this.defaultTabList.filter(v => v.value !== this.orderType)
        this.tabType = this.tabTypeList[0].value
      }
    },
    changeTabHandle(val) {
      switch (val) {
        case 'deliveryOrder':
          break;
        case 'receiptOrder':
          break;
        case 'settlementOrder':
          break;
        case 'purchaseOrder':
          break;
      }
    },
    clickConfirmHandle() {
      this.visible = false
    },
    clickCancleHandle() {
      this.visible = false
    },
    handlerClose() {
      this.tabType = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.related-document{
  .info-wrap{
    font-size: 14px;
    line-height: 30px;
    .title{
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0 10px;
    }
    .form-item {
      display: flex;
      font-size: 14px;
      line-height: 30px;
    }
    .form-img-box {
      flex: 1;
    }
    .detault-img {
      width: 160px;
      height: 92px;
      border: 1px dashed #e1e1e1;
      border-radius: 2px;
    }
  }
}
</style>
