
import * as dayjs from 'dayjs'
export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const TYPE_WARNING = [
  {
    label: "全部",
    value: ""
  },
  {
    label: "利润率盈余",
    value: "surplus"
  },
  {
    label: "利润率亏损",
    value: "loss"
  },
  {
    label: "原材料占比",
    value: "raw_material_percentage"
  }
]

// 经营预警筛选设置
export const SEARCH_FORM_BUSINESS_WARNING = {
  select_time: {
    type: 'daterange',
    format: 'yyyy-MM-dd',
    label: '预警时间',
    value: recentSevenDay,
    clearable: false
  },
  warn_type_list: {
    type: 'select',
    label: '预警类型',
    value: '',
    placeholder: '请选择预警类型',
    listNameKey: 'label',
    listValueKey: 'value',
    dataList: TYPE_WARNING
  }
}
// 经营预警 表格设置
export const TABLE_HEAD_BUSINESS_WARNING = [
  { label: '预警时间', key: 'create_time' },
  { label: '预警类型', key: 'warn_type', type: "slot", slotName: "accountType" },
  { label: '预警内容', key: 'warn_detail', type: "slot", slotName: "warnDetail" }
]
