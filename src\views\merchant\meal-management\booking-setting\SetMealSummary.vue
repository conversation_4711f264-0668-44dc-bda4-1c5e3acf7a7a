<template>
  <div class="booking-meal-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      @search="searchHandle"
      :form-setting="searchFormSetting"
      :autoSearch="false"
    ></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <el-button size="mini">打印</el-button> -->
          <el-button size="mini" @click="handleExport" v-permission="['background_order.reservation_order.set_meal_collect_list_export']">导出</el-button>
        </div>
      </div>

      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          :span-method="objectSpanMethod"
        >
          <el-table-column
            v-for="col in columns"
            :prop="col.column"
            :label="col.label"
            align="center"
            :key="col.column"
          >
            <template slot-scope="scope">
              <span v-if="col.column === 'set_meal_real_fee' || col.column === 'food_real_fee'">
                {{ scope.row[col.column] | formatMoney }}
              </span>
              <span v-else>{{ scope.row[col.column] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->

      <common-pagination
        ref="pagination"
        :total="total"
        :onPaginationChange="onPaginationChange"
      ></common-pagination>
    </div>
  </div>
</template>

<script>
import { to } from '@/utils'
import { recentSevenDay, dateTypes } from './constantsAndConfig'
import CommonPagination from './CommonPagination'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { mergeHandle, mergeRowAction } from '@/utils/table'
export default {
  name: 'CategoryList',
  mixins: [exportExcel],
  components: {
    CommonPagination
  },
  mounted() {
    this.initLoad()
    this.getSetMealCategoryList()
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'create_time',
          dataList: dateTypes
        },
        select_date: {
          clearable: false,
          type: 'daterange',
          // label: '预约时间',
          value: recentSevenDay
        },
        organization_id: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: [],
          multiple: true,
          checkStrictly: true,
          collapseTags: true
        },
        payer_department_group_ids: {
          type: 'departmentSelect',
          multiple: true,
          flat: false,
          label: '部门',
          value: [],
          placeholder: '请选择部门',
          checkStrictly: true,
          dataList: [],
          limit: 1,
          level: 1
        },

        category_id: {
          type: 'select',
          label: '套餐分类',
          listNameKey: 'name',
          listValueKey: 'id',
          value: '',
          placeholder: '请选择套餐分类',
          multiple: false,
          collapseTags: true,
          dataList: []
        },
        set_meal_name: {
          type: 'input',
          label: '套餐名称',
          value: '',
          placeholder: '请输入套餐名称'
        },
        food_name: {
          type: 'input',
          label: '菜品名称',
          value: '',
          placeholder: '请输入菜品名称'
        }
      },
      isLoading: false,
      tableData: [],
      rowMergeArrs: [],
      mergeOpts: {
        useKeyList: {
          set_meal_id: [
            'set_meal_name',
            'set_meal_category_name',
            'set_meal_real_fee',
            'set_meal_count'
          ]
        }, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        mergeKeyList: [
          'reservation_date',
          'organization__name',
          'payer_department_group_name',
          'meal_type_alias'
        ] // 通用的合并字段，根據值合并
      },
      columns: [
        { label: '日期', column: 'reservation_date' },
        { label: '组织', column: 'organization__name' },
        { label: '部门', column: 'payer_department_group_name' },
        { label: '餐段', column: 'meal_type_alias' },
        { label: '套餐名称', column: 'set_meal_name' },
        { label: '套餐分类', column: 'set_meal_category_name' },
        { label: '套餐金额', column: 'set_meal_real_fee' },
        { label: '套餐数量', column: 'set_meal_count' },
        { label: '菜品名称', column: 'food_name' },
        { label: '规格', column: 'food_spec_name' },
        { label: '菜品单价', column: 'food_real_fee' },
        { label: '菜品数量', column: 'food_count' }
      ]
    }
  },
  methods: {
    initLoad() {
      this.requestFoodCollectList()
    },
    // 分类列表
    async getSetMealCategoryList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodSetMealCategoryListPost({
          page: 1,
          page_size: 9999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.category_id.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async requestFoodCollectList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderReservationSetMealCollectListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        let setMealtableData = []
        res.data.results.map(v => {
          v.food_data.map(food => {
            let obj = {
              ...v,
              food_name: food.name,
              food_spec_name: food.spec,
              food_count: food.food_count,
              food_real_fee: food.real_fee,
              food_raw_fee: food.raw_fee
            }
            setMealtableData.push(obj)
          })
        })

        this.tableData = setMealtableData
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
        this.total = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 套餐合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.onPaginationChange({ current: 1, pageSize: 10 })
    },
    onPaginationChange(data) {
      this.pageSize = data.pageSize
      this.currentPage = data.current
      this.requestFoodCollectList()
    },
    searchHandle(e) {
      if (e) {
        this.$refs.pagination.handleCurrentChange(1, true)
        this.$refs.pagination.handleSizeChange(10, true)
        this.onPaginationChange({ current: 1, pageSize: 10 })
      }
    },
    // // 获取组织信息
    // async userGroupList() {
    //   const res = await this.$apis.apiBackgroundOrganizationOrganizationListPost({
    //     status: 'enable',
    //     page: 1,
    //     page_size: 9999999
    //   })
    //   if (res.code === 0) {
    //     this.searchFormSetting.organization_id.dataList = res.data.results
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    handleExport() {
      const option = {
        type: 'ExportSetMealSummary',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style scoped lang="scss">
.condition-wrapper {
  padding: 20px;
  // margin-top: 20px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .grid-content {
    display: flex;
    align-items: center;
    margin-right: 20px;
    .label {
      margin-right: 8px;
      font-size: 12px;
    }
  }
}
</style>
