<template>
  <div class="TransferOrder container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表
          <span class="inline-block m-l-20 font-size-16">当前仓库：<span style="color: 000; font-weight: 700;">{{$route.query.warehouse_name}}</span></span>
        </div>
        <div class="align-r">
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button v-if="row.approve_status === 'PROCESSING'" type="text" size="small" class="ps-text" @click="clickOperationHandle('revoke_approve', row)">撤销申请</el-button>
              <el-button v-if="row.refund_order_status === 'agree'" type="text" size="small" class="ps-text" @click="clickOperationHandle('confirm_refund', row)" >确认已退货</el-button>
              <el-button v-if="row.approve_status === 'AGREE' && row.refund_order_status === 'pend_send'" type="text" size="small" class="ps-text" @click="clickOperationHandle('send_order', row)">发送订单</el-button>
              <el-button v-if="row.refund_order_status === 'rejected'" type="text" size="small" class="ps-text" @click="clickOperationHandle('retry_approve', row)">重新申请</el-button>
              <el-button v-if="row.refund_order_status === 'rejected'" type="text" size="small" class="ps-text" @click="showMessageHandle(row)">查看原因</el-button>
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <dialog-message :show.sync="showDialog" title="拒绝原因" width="435px" :message="dialogMessageContent" :showFooter="false" @close="closeDialogHandle">
    </dialog-message>
    <!-- end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入

export default {
  name: 'ReturnsManagement',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '创建时间', key: 'create_time' },
        { label: '退货出库时间', key: 'refund_time' },
        { label: '单据编号', key: 'trade_no' },
        { label: '经手人', key: 'account_name' },
        { label: '审批状态', key: 'approve_status_alias' },
        { label: '订单状态', key: 'refund_order_status_alias' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '',
          value: 'create_time',
          maxWidth: '130px',
          placeholder: '请选择',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '退货出库时间',
              value: 'exit_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        },
        refund_order_status_list: {
          type: 'select',
          label: '订单状态',
          clearable: true,
          value: [],
          multiple: true,
          collapseTags: true,
          dataList: [
            // {
            //   label: '全部',
            //   value: ''
            // },
            {
              label: '待发送',
              value: 'pend_send'
            },
            {
              label: '订单待确认',
              value: 'pend_confirm'
            },
            {
              label: '拒绝退货',
              value: 'rejected'
            },
            {
              label: '同意退货',
              value: 'agree'
            },
            {
              label: '已退货',
              value: 'confirm_refund'
            }
          ]
        },
        approve_status_list: {
          type: 'select',
          label: '审批状态',
          clearable: true,
          value: [],
          multiple: true,
          collapseTags: true,
          dataList: [
            // {
            //   label: '全部',
            //   value: ''
            // },
            {
              label: '未发起',
              value: 'PENDING'
            },
            {
              label: '审批中',
              value: 'PROCESSING'
            },
            {
              label: '同意',
              value: 'AGREE'
            },
            {
              label: '拒绝',
              value: 'REJECT'
            }
          ]
        }
      },
      showDialog: false,
      dialogLoading: false,
      dialogMessageContent: ''
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.$route.query.currentPage) {
        this.currentPage = Number(this.$route.query.currentPage)
      }
      this.getExitRefundList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key.indexOf('time') < 0) {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 切换tab
    changeTabHandle(e) {
      this.searchHandle()
      this.replaceHash()
    },
    // 保存下参数
    replaceHash() {
      this.$router.replace({
        name: "ReturnsManagement",
        query: {
          currentPage: this.currentPage
        }
      })
    },
    // 获取列表数据
    async getExitRefundList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: this.$route.query.warehouse_id,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpExitInfoRefundInfoListPost(params))
      // this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getExitRefundList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    showMessageHandle(row) {
      this.dialogMessageContent = '测试下'
      this.showDialog = true
    },
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = 'apiBackgroundDrpExitInfoRefundInfoOperatePost'
      switch (type) {
        case 'revoke_approve':
          title = '确定撤销该退货单申请吗？'
          this.showOperationDialog(title, apiUrl, { id: data.id, warehouse_id: +this.$route.query.warehouse_id, refund_operate: 'revoke_approve' })
          break;
        case 'confirm_refund':
          title = '确定物资已完成退货？'
          this.showOperationDialog(title, apiUrl, { id: data.id, warehouse_id: +this.$route.query.warehouse_id, refund_operate: 'confirm_refund' })
          break;
        case 'send_order':
          title = '确定发送订单吗？'
          this.showOperationDialog(title, apiUrl, { id: data.id, warehouse_id: +this.$route.query.warehouse_id, refund_operate: 'send_order' })
          break;
        case 'retry_approve':
          title = '确定重新申请吗？'
          this.showOperationDialog(title, apiUrl, { id: data.id, warehouse_id: +this.$route.query.warehouse_id, refund_operate: 'retry_approve' })
          break;
      }
    },
    // 显示操作弹窗
    showOperationDialog(title, apiUrl, params) {
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis[apiUrl](params)
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getExitRefundList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    showDialogHandle(data) {
      this.showDialog = true
    },
    closeDialogHandle() {
      this.showDialog = false
    },
    clickDialogConfirm() {
      this.showDialog = false
    },
    // 跳转
    gotoHandle(type, row) {
      // 详情
      if (type === 'detail') {
        this.$router.push({
          name: 'ReturnsManagementDetail',
          query: {
            type,
            id: row.id,
            ...this.$route.query
          }
        })
        return
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.TransferOrder{
  .w-medium{
    width: 140px;
    height: 40px;
    &.m-r-20{
      margin-right: 20px;
    }
  }
}
</style>
