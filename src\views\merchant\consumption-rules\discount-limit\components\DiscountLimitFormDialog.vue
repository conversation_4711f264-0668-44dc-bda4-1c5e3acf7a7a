<template>
  <el-drawer :title="dialogTitle" :visible.sync="isShowDialog" size="678px" custom-class="" :direction="direction"
    class="ps-el-drawer" @close="closeTab">
    <div id="DiscountLimitForm" v-loading="isFormLoading">
      <div class="form-wrapper">
        <el-form :model="formData" label-width="110px" :rules="noticeInfoRules" ref="form">
          <el-form-item label="名称：" prop="name">
            <el-input v-model="formData.name" placeholder="请输入名称" class="ps-input" style="width: 400px"
              :disabled="dialogType === 'modify'" maxlength="30"></el-input>
          </el-form-item>
          <!-- <el-form-item class="min-label-w" label="分组（可多选）：" prop="groups">
            <user-group-select :multiple="true" :collapse-tags="true" class="ps-input" style="width: 400px"
              v-model="formData.groups" placeholder="请选择分组"></user-group-select>
          </el-form-item> -->
          <el-form-item label="选择人员：" prop="choosePeople" v-if="dialogType === 'add'">
            <div class="ps-flex">
              <div class='ps-text-orange pointer' @click="handlerChoosePeople">选择人员 <span
                  v-if="formData.choosePeople > 0">（已选：{{ formData.choosePeople }}）</span></div>
              <div class="m-l-20 ps-text-orange pointer" @click="handlerImportPeople">导入人员信息</div>
            </div>
            <div class="ps-red font-size-14">一个用户只能有一条优惠限制</div>
          </el-form-item>
          <el-form-item label="餐段限制：" prop="limitMealList">
            <div>总额/次数任意达到配置限制，则触发禁止/原价消费</div>
            <div class="" v-for="(item, index) in formData.limitMealList" :key="index">
              <el-form-item :prop="'limitMealList.' + index + '.mealType'" :rules="noticeInfoRules.mealType">
                <div class="ps-flex">
                  <el-select v-model="item.mealType" placeholder="请选择餐段" style="width: 320px;"
                    @visible-change="handlerMealTypeChange($event, item.mealType)" multiple
                    :disabled="dialogType === 'modify'">
                    <el-option :label="meal.name" :value="meal.key" v-for="(meal, mealIndex) in mealTypeList"
                      :key="mealIndex" :disabled="meal.disabled"></el-option>
                  </el-select>
                  <img src="@/assets/img/plus.png" class="m-l-20 ic-32" @click.stop="handlerAddMeal"
                    v-if="dialogType === 'add'" />
                  <img src="@/assets/img/reduce_red.png" class="m-l-20 ic-32" @click="handlerDeleteMeal(item, index)"
                    v-if="index > 0 && dialogType === 'add'" />
                </div>
              </el-form-item>
              <div class="ps-flex m-t-15">
                <div>限制总额</div>
                <el-form-item :prop="'limitMealList.' + index + '.limitTotal'" :rules="noticeInfoRules.limitTotal">
                  <el-input v-model="item.limitTotal" placeholder="请输入" class="ps-input m-l-20" style="width: 200px"
                    :disabled="dialogType === 'modify'"></el-input>
                </el-form-item>
                <div class="m-l-20">元</div>
              </div>
              <div class="ps-flex m-t-15 m-b-15">
                <div>限制次数</div>
                <el-form-item :prop="'limitMealList.' + index + '.limitTimes'" :rules="noticeInfoRules.limitTimes">
                  <el-input v-model="item.limitTimes" placeholder="请输入" class="ps-input m-l-20"
                    :disabled="dialogType === 'modify'" style="width: 200px"></el-input>
                </el-form-item>
                <div class="m-l-20">次</div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="达到限制后：" prop="limit_type">
            <el-radio-group class="ps-radio" v-model="formData.limit_type" :disabled="dialogType === 'modify'">
              <el-radio label="PROHIBIT">禁止消费</el-radio>
              <el-radio label="ORIGINAL_PRICE">原价消费</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="优惠周期：" prop="discount_cycle">
            <el-radio-group class="ps-radio" v-model="formData.discount_cycle" :disabled="dialogType === 'modify'">
              <el-radio label="NATURAL_WEEK">自然周</el-radio>
              <el-radio label="NATURAL_MONTH">自然月</el-radio>
              <el-radio label="TIME_CYCLE">自定义</el-radio>
            </el-radio-group>
            <div v-if="formData.discount_cycle === 'TIME_CYCLE'">
              <el-date-picker v-model="formData.discount_cycle_range" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" format="yyyy-MM-dd"
                :disabled="dialogType === 'modify'" value-format="yyyy-MM-dd">
              </el-date-picker>
            </div>
            <span class="ps-red font-size-14 block">周期结束后，重置“优惠限额总额”</span>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input type="textarea" class="ps-input" style="width: 400px" :autosize="{ minRows: 6, maxRows: 10 }"
              placeholder="请输入内容" maxlength="100" show-word-limit v-model="formData.remark"
              :disabled="dialogType === 'modify'"></el-input>
          </el-form-item>
          <div class="ps-el-drawer-footer">
            <el-button :disabled="isLoading" plain @click="closeTab" style="width: 120px">
              {{ dialogType === 'modify' ? '关闭' : '取消' }}
            </el-button>
            <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="submitForm" style="width: 120px"
              v-if="dialogType === 'add'">
              保存
            </el-button>
          </div>
        </el-form>
      </div>
      <!--选择人员弹窗-->
      <choose-user-dialog ref="chooseUserDialog" :isshow.sync="isShowUserDialog" @confirm="confirmUserDialog"
        @close="closeUserDialog" />
      <!--导入弹窗-->
      <import-dialog-drawer :import-type="importType" :templateUrl="templateUrl" :tableSetting="tableSettingImport"
        :show.sync="isShowImportDialog" :title="'导入人员信息'" :openExcelType="openExcelType" @confirm="confirmImport">
      </import-dialog-drawer>
    </div>
  </el-drawer>
</template>

<script>
import { to, deepClone, uniqueArrKey, times } from '@/utils'
// import UserGroupSelect from '@/components/UserGroupSelect'
import ChooseUserDialog from './ChooseUserDialog.vue'
import { mapActions } from 'vuex'
import { positiveMoney, integer } from '@/utils/validata'
import NP from 'number-precision' // 解决精确度问题
import { TABLE_HEAD_DATA_IMPORT_PERSON, TIME_DAY_OPTION, LIMIT_MEAL_TYPE } from "../constants.js"
export default {
  name: 'DiscountLimitFormDialog',
  props: {
    show: {
      type: Boolean,
      required: true
    },
    dialogTitle: {
      type: String,
      default: '提示'
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    dialogType: {
      type: String,
      default: 'add'
    }
  },
  components: {
    ChooseUserDialog
    // UserGroupSelect
  },
  // mixins: [activatedLoadData],
  data() {
    let validatorList = (rule, value, callback) => {
      if (value && value.length > 0) {
        callback()
      } else {
        callback(new Error('请配置餐段限制'))
      }
    }
    let validatorZero = (rule, value, callback) => {
      if (value) {
        if (parseInt(value) > 0) {
          callback()
        } else {
          callback(new Error('请选择人员'))
        }
      } else {
        callback(new Error('请选择人员'))
      }
    }
    let validatorRangeOne = (rule, value, callback) => {
      if (value) {
        if (Number(value) <= 0) {
          callback(new Error('请输入大于0的金额'))
        } else if (positiveMoney(value)) {
          if (Number(value) > 999.99) {
            return callback(new Error('最大值不能超过999.99'))
          }
          callback()
        } else {
          callback(new Error('格式错误'))
        }
      } else {
        callback()
      }
    }
    let validatorInteger = (rule, value, callback) => {
      if (value) {
        if (integer(value)) {
          if (Number(value) <= 0) {
            callback(new Error('请输入大于0'))
          } else if (Number(value) > 99) {
            callback(new Error('最大值不能超过99'))
          }
          callback()
        } else {
          callback(new Error('请输入整数'))
        }
      } else {
        callback()
      }
    }
    return {
      formData: {
        name: '',
        discount_fee: '',
        limit_type: '',
        discount_cycle: '',
        remark: '',
        choosePeople: 0,
        limitMealList: []
      },
      noticeInfoRules: {
        name: [{ required: true, message: '请输入名称', trigger: ['blur', 'change'] }],
        discount_fee: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          { validator: validatorRangeOne, trigger: ['blur', 'change'] }
        ],
        choosePeople: [{ required: true, validator: validatorZero, trigger: ['blur', 'change'] }],
        limitMealList: [{ required: true, validator: validatorList, trigger: ['blur', 'change'] }],
        groups: [{ required: true, message: '请选择分组', trigger: ['blur', 'change'] }],
        limit_type: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        discount_cycle: [{ required: true, message: '请选择优惠周期', trigger: ['blur', 'change'] }],
        mealType: [{ required: true, message: '请选择优惠周期', trigger: ['blur', 'change'] }],
        limitTotal: [
          { required: false, message: '请输入限制总数', trigger: ['blur', 'change'] },
          { validator: validatorRangeOne, trigger: 'change' }
        ],
        limitTimes: [
          { required: false, message: '请输入限制次数', trigger: ['blur', 'change'] },
          { validator: validatorInteger, trigger: 'change' }
        ]
      },
      isLoading: false,
      infoData: {},
      isShowUserDialog: false,
      personList: [], // 选择人员列表
      templateUrl: location.origin + '/api/temporary/template_excel/优惠限制导入用户信息.xlsx', // 导入模板的链接 to do debug
      tableSettingImport: deepClone(TABLE_HEAD_DATA_IMPORT_PERSON), // 导入表格头部设置
      openExcelType: 'importRulePerson', // 导入类型
      isShowImportDialog: false, // 是否显示确认键loading
      importType: 'data',
      defaultLimitItem: {
        mealType: [],
        limitTotal: '',
        limitTimes: ''
      },
      mealTypeList: deepClone(LIMIT_MEAL_TYPE),
      pickerOptions: TIME_DAY_OPTION,
      isFormLoading: false
    }
  },
  computed: {
    isShowDialog: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  watch: {
    isShowDialog(newValue) {
      console.log('isShowDialog 111111', newValue);
      if (newValue) {
        console.log("this.dialogtype", this.dialogType);
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
        if (this.dialogType === 'add') {
          this.handlerAddMeal()
        }
      }
    }
  },
  created() {
    // this.getCompanyList()
    // if (this.$route.params.type) {
    //   this.type = this.$route.params.type
    // }
    // if (this.$route.query.data && this.type === 'modify') {
    //   this.initDefaul(JSON.parse(decodeURIComponent(this.$route.query.data)))
    //   this.initLoading = true
    // }

  },
  mounted() { },
  methods: {
    ...mapActions({}),
    initDefaul(data) {
      this.infoData = data
      this.formData = {
        name: data.name,
        groups: data.groups,
        discount_fee: NP.divide(data.discount_fee, 100),
        limit_type: data.limit_type,
        discount_cycle: data.discount_cycle,
        remark: data.remark
      }
    },
    // 创建优惠限制
    async getAddDiscountLimit(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMarketingDiscountLimitAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.closeTab()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑公告
    async messagesEdit(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingDiscountLimitModifyPost({
          id: this.infoData.id,
          ...params
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.closeTab()
      } else {
        this.$message.error(res.msg)
      }
    },
    submitForm() {
      console.log("submitForm");
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.checkoutLitmitMealList()) {
            return
          }
          let params = {
            name: this.formData.name,
            limit_type: this.formData.limit_type,
            discount_cycle: this.formData.discount_cycle,
            remark: this.formData.remark,
            is_enable: true
          }
          if (this.personList && this.personList.length > 0) {
            let ids = this.personList.map(item => item.id)
            params.card_info_ids = ids
          }
          if (this.formData.limitMealList && this.formData.limitMealList.length > 0) {
            let limitType = []
            this.formData.limitMealList.forEach(item => {
              let curItem = {
                meal_type: item.mealType
              }
              if (item.limitTotal) {
                curItem.discount_fee = times(item.limitTotal)
              }
              if (item.limitTimes) {
                curItem.discount_num = parseInt(item.limitTimes)
              }
              limitType.push(curItem)
              params.meal_type_limit = limitType
            })
          }
          // 自定义日期要填时间段
          if (this.formData.discount_cycle === 'TIME_CYCLE') {
            params.cycle_start_time = this.formData.discount_cycle_range.length > 0 ? this.formData.discount_cycle_range[0] : ''
            params.cycle_end_time = this.formData.discount_cycle_range.length > 1 ? this.formData.discount_cycle_range[1] : ''
          }
          if (this.type === 'modify') {
            this.messagesEdit(params)
          } else {
            this.getAddDiscountLimit(params)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getFileLists(fileLists) {
      this.noticeForm.fileLists = fileLists
    },
    closeTab() {
      // this.$closeCurrentTab(this.$route.path)
      if (this.$refs.form) {
        this.mealTypeList = deepClone(LIMIT_MEAL_TYPE)
        this.formData = {
          name: '',
          discount_fee: '',
          limit_type: '',
          discount_cycle: '',
          remark: '',
          choosePeople: 0,
          limitMealList: []
        }
        this.$refs.form.clearValidate()
      }
      this.personList = []
      this.isShowUserDialog = false
      this.$emit("close", false)
    },
    // 选择人员
    handlerChoosePeople() {
      if (this.$refs.chooseUserDialog) {
        this.$refs.chooseUserDialog.setPersonList(this.personList)
      }
      this.isShowUserDialog = true
      console.log("handlerChoosePeople", this.isShowUserDialog);
    },
    // 选择人员弹窗确认回调
    confirmUserDialog(dataList) {
      this.isShowUserDialog = false
      this.personList = deepClone(dataList)
      this.personList = uniqueArrKey(this.personList, 'id')
      this.$set(this.formData, 'choosePeople', this.personList.length)
    },
    // 导入人员弹窗展示
    handlerImportPeople() {
      console.log('handlerImportPeople')
      this.isShowImportDialog = true
    },
    // 关闭选择人员弹窗
    closeUserDialog() {
      this.isShowUserDialog = false
    },
    // 确认导入
    async confirmImport(data) {
      console.log("confirmImport", data);
      this.isShowImportDialog = false
      if (!data.allData || data.allData.length === 0) {
        return this.$message.error('导入数据为空')
      }
      let importDataList = data.allData || []
      let ids = importDataList.map(item => item.person_no)
      this.isFormLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingDiscountLimitCheckCardPost({
          person_no_list: ids
        })
      )
      this.isFormLoading = false
      if (err) {
        return this.$message.error('导入失败')
      }
      if (res && res.code === 0) {
        let data = res.data || []
        data = await this.getChoosePersonList(data)
        let personList = deepClone(this.personList)
        personList = personList.concat(data)
        // 去重
        personList = uniqueArrKey(personList, 'id')
        this.personList = deepClone(personList)
        this.$set(this.formData, 'choosePeople', this.personList.length)
      }
    },
    // 获取已选人员
    getChoosePersonList(list) {
      return new Promise((resolve) => {
        this.$apis.apiBackgroundMarketingDiscountLimitChosenCardInfoIdsPost().then(res => {
          if (res && res.code === 0) {
            let data = res.data || {}
            let cardInfoIds = data.card_info_ids || []
            let ids = []
            if (cardInfoIds) {
              cardInfoIds.forEach(item => {
                ids = ids.concat(item || [])
              })
            }
            // 去除存ids id的数据
            list = list.filter(item => !ids.includes(item.id))
            resolve(list)
          } else {
            resolve(list)
          }
        }).catch(error => {
          console.log(error);
          resolve(list)
        })
      })
    },
    // 添加餐段
    handlerAddMeal() {
      let list = deepClone(this.formData.limitMealList)
      list.push(deepClone(this.defaultLimitItem))
      this.$set(this.formData, 'limitMealList', list)
      console.log("handlerAddMeal", list);
    },
    // 删除餐段
    handlerDeleteMeal(item, index) {
      let list = deepClone(this.formData.limitMealList)
      list.splice(index, 1)
      this.$set(this.formData, 'limitMealList', list)
    },
    // 餐段类型选择
    handlerMealTypeChange(value, item) {
      if (value) {
        this.setChangeMealTypeDisable(item)
      }
    },
    // 设置餐段类型选择禁用
    setChangeMealTypeDisable(currentList) {
      // 将已经选择的餐段汇总
      let alreadyList = []

      this.formData.limitMealList.forEach(item => {
        alreadyList = alreadyList.concat(item.mealType)
      })
      alreadyList = alreadyList.filter(item => !currentList.includes(item))
      this.$nextTick(() => {
        let list = deepClone(this.mealTypeList)
        list.forEach(item => {
          item.disabled = alreadyList.includes(item.key)
        })
        this.$set(this, 'mealTypeList', list)
      })
      console.log("alreadyList", alreadyList, this.formData.limitMealList, this.mealTypeList);
    },
    // 设置表单数据
    setFormData(data) {
      console.log("setFormData", data);
      if (data && typeof data === 'object') {
        this.formData = {
          name: data.name,
          limit_type: data.limit_type,
          discount_cycle: data.discount_cycle,
          remark: data.remark,
          is_enable: data.is_enable,
          limitMealList: data.meal_type_limit
        }
        if (data.meal_type_limit && Array.isArray(data.meal_type_limit)) {
          let list = data.meal_type_limit.map(item => {
            return {
              mealType: item.meal_type,
              limitTotal: item.discount_fee ? NP.divide(item.discount_fee, 100) : '',
              limitTimes: item.discount_num
            }
          })
          this.formData.limitMealList = list
        }
        if (data.discount_cycle === 'TIME_CYCLE') {
          this.formData.discount_cycle_range = data.cycle_start_time ? [data.cycle_start_time, data.cycle_end_time] : []
        }
      }
    },
    // 检查餐段数据
    checkoutLitmitMealList() {
      let list = deepClone(this.formData.limitMealList)
      for (let index = 0; index < list.length; index++) {
        const item = list[index];
        const limitTotal = item.limitTotal
        const limitTimes = item.limitTimes
        if (!limitTotal && !limitTimes) {
          this.$message.error('第' + (index + 1) + '条限制餐段数据不完整，请填写限制总额或限制次数')
          return true
        }
      }
      return false
    }
  }
}
</script>

<style lang="scss">
#DiscountLimitForm {
  .pointer {
    cursor: pointer;
  }

  .m-750 {
    max-width: 750px;
  }

  .form-wrapper {
    margin: 20px;
    padding: 30px 20px;
    background-color: #fff;
    border-radius: 5px;
  }

  .ps-text-orange {
    color: #ff9b45;
    font-size: 14px;
    text-decoration: underline;
  }

  .ic-32 {
    width: 32px;
    height: 32px;
    cursor: pointer;
  }

  .font-size-14 {
    font-size: 14px;
  }

  .m-l-150 {
    margin-left: 150px;
  }
}
</style>
