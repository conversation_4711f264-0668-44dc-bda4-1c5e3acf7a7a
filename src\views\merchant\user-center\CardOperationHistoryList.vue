<template>
  <div class="RepairCardList container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"
      @reset="resetHandler"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="handleExport" v-permission="['card_service.flat_cost.card_operation_list_export']">导出记录</button-icon>
          <!-- <button-icon color="plain" @click="openPrinterDialog">小票打印</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe
          @selection-change="handleSelectionChange" header-row-class-name="ps-table-header-row">
          <table-column v-for="(item, i) in tableSetting" :key="i" :col="item">
            <template #industryType="{ row }">
              {{ formatIndustry(row.industry) }}
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination @current-change="handleCurrentChange" :current-page="currentPage" :page-size="pageSize"
          layout="total, prev, pager, next" :total="totalCount" background class="ps-text"
          popper-class="ps-popper-select"></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-ticket :isshow.sync="printTicketVisible" type="card" cardType="SUPPLEMENTARY" title="小票打印"
      :select-list-id="selectListId" :confirm="searchHandle"></print-ticket>
  </div>
</template>

<script>
import { debounce, divide, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import PrintTicket from '@/components/PrintTicket'
export default {
  name: 'CardOperationHistoryList',
  components: { PrintTicket },
  props: {},
  mixins: [exportExcel],
  data() {
    const defaultdate = getSevenDateRange(7);
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        select_date: {
          clearable: false,
          type: 'daterange',
          label: '搜索日期',
          value: [defaultdate[0], defaultdate[1]]
        },
        person_name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        },
        operate_type: {
          type: 'select',
          label: '操作类型',
          value: [],
          placeholder: '请选择操作类型',
          dataList: [{
            label: '发卡',
            value: "PUBLISH"
          }, {
            label: '补卡',
            value: 'SUPPLEMENTARY'
          }, {
            label: '退卡',
            value: 'QUIT'
          }, {
            label: '补扣',
            value: 'DEDUCT_FLAT'
          }]
        },
        card_user_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          multiple: true,
          collapseTags: true,
          filterable: true
        },
        card_department_group_id: {
          type: 'departmentSelect',
          multiple: false,
          isLazy: false,
          checkStrictly: true,
          label: '部门',
          value: '',
          placeholder: '请选择部门'
        }
      },
      tableSetting: [
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '分组', key: 'user_group_name' },
        { label: '卡号', key: 'card_no' },
        { label: '部门', key: 'department_group_name' },
        { label: '操作类型', key: 'operate_type_alias' },
        { label: '说明', key: 'remark' },
        { label: '涉及金额', key: 'pay_fee', type: 'money' },
        { label: '收款方式', key: 'pay_method_alias' },
        { label: '时间', key: 'create_time' }
      ],
      selectListId: [],
      printTicketVisible: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() { },
  methods: {
    initLoad() {
      this.getHistoryCardList()
    },
    // 重置
    resetHandler() {
      this.currentPage = 1;
      this.tableData = []
      this.getHistoryCardList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.printTicketVisible = false
      this.currentPage = 1;
      this.tableData = []
      this.getHistoryCardList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      if (this.$refs.searchRef) {
        this.$refs.searchRef.resetForm()
      }
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取卡历史
    async getHistoryCardList(status) {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceFlatCostCardOperationListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.tableData.map(item => {
          item.repair_fee = divide(item.pay_fee)
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getHistoryCardList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getHistoryCardList()
    },
    handleExport() {
      const option = {
        type: 'ExportFlatCostCardOperationList',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = val.map(item => {
        return item.id
      })
    },
    openPrinterDialog() {
      if (!this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.printTicketVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";
</style>
