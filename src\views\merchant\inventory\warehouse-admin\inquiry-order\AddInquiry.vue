<template>
  <div class="AddInquiry form-container">
    <h3>{{ titleText }}</h3>
    <el-form
      v-loading="isLoading"
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="110px"
      size="small"
      class="m-l-20 m-t-10"
    >
      <el-form-item label="询价对象" prop="supplier_ids">
        <el-select v-model="formData.supplier_ids" multiple collapse-tags class="ps-select" popper-class="ps-popper-select" placeholder="请选择">
          <el-option v-for="option in supplierList" :key="option.id" :label="option.name" :value="option.id" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="截止报价日期" prop="end_time">
        <el-date-picker
          v-model="formData.end_time"
          type="date"
          placeholder="选择日期时间"
          value-format="yyyy-MM-dd"
          :picker-options="pickerPurchaseTimeOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="物资清单" prop="">
        <el-button class="ps-origin-btn w-100" @click="addMaterials('purchase')">新增</el-button>
        <el-button class="ps-origin-btn w-100" @click="openImport('add')">导入物资</el-button>
        <el-button class="ps-origin-btn w-100" @click="addMaterials('template')">选择模板</el-button>
      </el-form-item>
      <el-form-item label="">
        <div style="width: 92%; margin-bottom: 10px">
          <el-table
            :data="currentMaterialsTableData"
            ref="tableRef"
            stripe
            size="small"
            border
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in materialsTableSettings" :key="item.key" :col="item">
              <template #count="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.purchase"
                  :prop="'materialsTableData[' + getMaterialsIndex(index) + '].purchase'"
                >
                  <el-input
                    v-model="row.purchase"
                    placeholder="请输入"
                    :maxlength="6"
                    class="ps-input"
                    @input="inputPirchaseHandle($event, index)"
                  ></el-input>
                </el-form-item>
              </template>
              <template #unit="{ row, index }">
                <el-form-item
                  label=""
                  :prop="'materialsTableData[' + getMaterialsIndex(index) + '].unit_id'"
                  :rules="formRules.unit_id"
                  class="m-b-0"
                >
                  <el-select v-model="row.unit_id" filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择">
                    <el-option v-for="option in unitList" :key="option.id" :label="option.name" :value="option.id" ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template #operation="{ index }">
                <el-button type="text" size="small" class="ps-origin" @click.stop="deleteMaterials(index)">
                  删除
                </el-button>
              </template>
            </table-column>
          </el-table>
          <div v-if="errorTableData" class="red">{{ errorTableData }}</div>
          <pagination
            v-if="formData.materialsTableData.length > materialsPageSize"
            :onPaginationChange="onMaterialsPaginationChange"
            :current-page.sync="materialsPage"
            :page-size.sync="materialsPageSize"
            :layout="'total, prev, pager, next, jumper'"
            :total="formData.materialsTableData.length"
          ></pagination>
        </div>
      </el-form-item>
      <div class="m-l-40 m-t-60">
        <el-button class="ps-cancel-btn w-130" size="medium" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle('')">保存</el-button>
        <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle('is_enable')">保存并发起</el-button>
        <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle('template')">存为模板</el-button>
      </div>
    </el-form>
    <!-- 导入物资 -->
    <import-page-dialog
      ref="importPageRef"
      :show.sync="showImportDialog"
      :title="importDialogTitle"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :loading.sync="importLoading"
      :isUpload="false"
      @confirm="confirmImportHandle"
    ></import-page-dialog>
    <!-- 添加物资/选择询价单 -->
    <choose-list-dialog
      :showdialog.sync="showChooseDialog"
      :title="dialogChooseTitle"
      :type="dialogChooseType"
      :api="dialogChooseApi"
      :detailApi="dialogChooseDetailApi"
      :search-setting="dialogChooseSearchSetting"
      :table-settings="dialogChooseTableSettings"
      :params="dialogChooseParams"
      :rowKey="dialogRowKey"
      showSelectLen
      @confirmChoose="confirmChooseHandle"
    ></choose-list-dialog>
    <!-- 保存草稿/模板 -->
    <form-dialog
      :showdialog.sync="showFormDialog"
      :type="dialogFormType"
      :api="dialogFormApi"
      :title="dialogFormTitle"
      :inputLabel="inputLabel"
      :params="dialogFormParams"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import { integer, oneDecimal  } from '@/utils/validata'
import { validateOneDecimalCount } from '@/utils/form-validata'
import * as dayjs from 'dayjs'
import NP from 'number-precision'
// import DraftManageDialog from '../warehouse-admin/components/DraftManageDialog.vue'
import FormDialog from '../../components/FormDialog'
import ChooseListDialog from '../../components/ChooseListDialog'
import { downloadJsonExcel } from '@/utils/excel'

export default {
  name: 'AddInquiry',
  mixins: [exportExcel],
  components: {
    // DraftManageDialog
    ChooseListDialog,
    FormDialog
  },
  data() {
    return {
      type: 'add',
      titleText: this.$route.params.type === 'modify' ? '编辑询价单' : '新建询价单',
      isLoading: false, // 刷新数据
      queryData: this.$route.query,
      // form表单数据
      formData: {
        supplier_ids: [], // 供应商
        warehouse_id: '',
        end_time: '', // 截止日期
        materialsTableData: []
      },
      formRules: {
        supplier_ids: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        end_time: [{ required: true, message: '请选择日期', trigger: 'change' }],
        warehouse_id: [{ required: true, message: '请选择入库仓库', trigger: 'change' }],
        purchase: [{ required: true, validator: validateOneDecimalCount, trigger: 'change' }]
        // purchase: [{ required: true, validator: validateNumber, trigger: 'change' }]
      },
      pickerPurchaseTimeOptions: {
        disabledDate(time) {
          return time.getTime() < dayjs().subtract(1, 'day').valueOf()
        }
      },
      // 入库物资
      materialsTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '预计采购量', key: 'purchase', type: 'slot', slotName: 'count' },
        { label: '单位', key: 'purchase_unit' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      // 物资数据
      materialsTableData: [],
      currentMaterialsTableData: [],
      materialsPage: 1,
      materialsPageSize: 10,
      errorTableData: '',
      supplierList: [], // 供应商列表
      unitList: [], // 单位列表
      // 导入的弹窗数据
      importLoading: false,
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/询价单导入物资.xlsx',
      importHeaderLen: 1,
      importFailTableData: [], // 导入失败的数据
      importTableSettings: [
        { label: '物资名称', key: 'name' },
        { label: '导入失败原因', key: 'result' }
      ],
      // 选择物资/模板/询价单等弹窗
      showChooseDialog: false, // 是否开启弹窗
      dialogChooseLoading: false, // 弹窗loading
      dialogChooseTitle: '选择物资',
      dialogChooseType: '', // 弹窗的状态，add/modify
      dialogChooseData: {}, // 弹窗数据
      remoteChooseLoading: false,
      dialogChooseTableSettings: [],
      dialogChooseSearchSetting: {},
      dialogChooseParams: {
        warehouse_id: +this.$route.query.warehouse_id
      },
      dialogChooseApi: '1', // 请求的接口
      dialogChooseDetailApi: '',
      dialogRowKey: 'materials_id',
      // 保存为草稿、模板、选择菜谱等的弹窗
      showFormDialog: false,
      dialogFormTitle: '选择菜谱',
      dialogFormType: '1',
      dialogFormPagetype: '', // 哪个页面的功能
      dialogFormApi: '1',
      inputLabel: '',
      dialogFormParams: {}
    }
  },
  computed: {
    // 花销大，因为每次materialsTableData数据变化都会触发它重新计算
    // currentMaterialsTableData() {
    //   return this.materialsTableData.slice((this.materialsPage - 1) * this.materialsPageSize, this.materialsPage * this.materialsPageSize)
    // }
  },
  created() {
    this.type = this.$route.params.type || 'add'
    if (this.$route.query.warehouse_id) {
      this.formData.warehouse_id = this.$route.query.warehouse_id
    }
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getSupplierManagementList()
      if (this.type === 'modify') {
        this.getInquiryDetail()
      }
    },
    // 初始化编辑显示
    async getInquiryDetail() {
      if (!this.$route.query.id) return this.$message.error('获取id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: this.$route.query.id,
        warehouse_id: +this.formData.warehouse_id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpInquiryDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 初始化询价对象
        this.formData.supplier_ids = res.data.supplier_manage_data.map(v => v.id)
        this.formData.end_time = res.data.end_time || ''
        this.formData.materialsTableData = res.data.materials_data.map(v => {
          return {
            materials_name: v.materials_name,
            materials_id: v.materials,
            purchase: v.act_purchase,
            purchase_unit: v.act_purchase_unit,
            purchase_unit_id: v.unit_id
          }
        })
        this.initCurrentTableMaterials()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取供应商数据列表
    async getSupplierManagementList() {
      const res = await this.$apis.apiBackgroundDrpSupplierManageListPost({
        page: 1,
        page_size: 999999
      })
      if (res.code === 0) {
        this.supplierList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取单位列表数据
    async getUnitManagementList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundDrpUnitManagementListPost({
          page: 1,
          page_size: 999999,
          organization_id: this.$store.getters.organization
        })
      )
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.unitList = res.data.results
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 初始化Materials分页数据
    initCurrentTableMaterials() {
      this.currentMaterialsTableData = this.formData.materialsTableData.slice(
        (this.materialsPage - 1) * this.materialsPageSize,
        this.materialsPage * this.materialsPageSize
      )
    },
    // 获取实际materialsTableData的index，这样做就不用重新遍历一次数据了
    getMaterialsIndex(index) {
      return (this.materialsPage - 1) * this.materialsPageSize + index
    },
    onMaterialsPaginationChange(val) {
      this.materialsPage = val.current
      this.materialsPageSize = val.pageSize
      this.initCurrentTableMaterials()
    },
    // 删除物资
    deleteMaterials(index) {
      this.formData.materialsTableData.splice(this.getMaterialsIndex(index), 1)
      if (this.formData.materialsTableData.length !== 0) {
        this.materialsPage = this.$computedTotalPageSize(
          this.formData.materialsTableData.length,
          this.materialsPageSize
        )
      } else {
        this.materialsPage = 1
      }
      // 初始化分页数据
      this.initCurrentTableMaterials()
    },
    // 实际采购数量校验
    inputPirchaseHandle(e, currentIndex) {
      // if (!integer(e)) {
      //   this.errorTableData = '请检查采购量'
      //   return
      // } else if (e > 999999) {
      //   this.errorTableData = '采购数量最大不能超过6位数'
      //   return
      // }
    },
    // 添加物资
    addMaterials(type) {
      this.dialogChooseType = type
      // 初始化分页数据
      // this.initCurrentTableMaterials()
      if (type === 'purchase') {
        this.dialogChooseTitle = '添加物资'
        this.dialogChooseApi = 'apiBackgroundDrpMaterialsGetMaterialsListPost'
        this.dialogChooseParams = {
          warehouse_id: +this.$route.query.warehouse_id
        }
        this.dialogRowKey = 'materials_id'
        this.dialogChooseSearchSetting = {}
        this.dialogChooseTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '物资名称', key: 'materials_name' },
          { label: '预计采购量', key: 'count', type: 'slot', slotName: 'oneDecimalCount' }
        ]
      }
      if (type === 'template') {
        this.dialogChooseTitle = '模板管理'
        this.dialogChooseApi = 'apiBackgroundDrpTemplateInfoTempListPost'
        this.dialogChooseDetailApi = 'apiBackgroundDrpInquiryTempDetailsListPost'
        this.dialogChooseParams = {
          temp_type: 'template',
          inventory_info_type: 'inquiry',
          warehouse_id: +this.$route.query.warehouse_id
        }
        this.dialogRowKey = 'id'
        this.dialogChooseSearchSetting = {}
        this.dialogChooseTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '模板名称', key: 'name' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
        ]
      }
      this.showChooseDialog = true
    },
    // form弹窗
    openFormDialog(type, row) {
      this.dialogFormType = type
      switch (type) {
        case 'template':
          this.dialogFormTitle = '创建模板'
          this.inputLabel = '模板名称'
          this.dialogFormApi = 'apiBackgroundDrpInquiryTempAddPost'
          this.dialogFormParams = this.setDialogFormParams(type)
          break;
      }
      this.showFormDialog = true
    },
    // 设置存为草稿或者存为模板的数据
    setDialogFormParams(type) {
      let params = {
        inventory_info_type: 'inquiry',
        warehouse_id: +this.formData.warehouse_id,
        supplier_manage_ids: this.formData.supplier_ids,
        temp_type: type,
        end_time: this.formData.end_time,
        ingredient_data: this.formData.materialsTableData
      }
      return params
    },
    // 弹窗操作
    showDialogHandle(type, data, index) {
      if (!this.formData.warehouse_id) {
        return this.$message.error('请先选择仓库！')
      }
      this.dialogType = type
      if (type === 'importResult') {
        this.dialogTitle = '提示'
      }
      this.showDialog = true
    },
    // 检查表单数据
    checkedFormData() {
      let nopass = false
      let message = ''
      this.formData.materialsTableData.forEach(v => {
        if (!oneDecimal(v.purchase)) {
          nopass = true
          message = '请检查采购量格式是否正确！'
        }
      })
      if (!this.formData.materialsTableData.length) {
        message = '请先选择物资！'
      }
      if (nopass) {
        this.errorTableData = message
      } else {
        this.errorTableData = ''
      }
      // nopass = false
      return !nopass
    },
    // 格式华参数
    formatParams() {
      let params = {
        warehouse_id: +this.formData.warehouse_id,
        supplier_manage_ids: this.formData.supplier_ids,
        end_time: this.formData.end_time
      }
      params.ingredient_data = this.formData.materialsTableData
      return params
    },
    validateForm(type) {
      if (this.checkedFormData()) {
        let params = this.formatParams()
        if (!this.formData.materialsTableData.length) return this.$message.error('请先选择物资！')
        if (type === 'is_enable') {
          params.is_enable = true
        }
        if (this.type === 'modify') {
          params.id = this.$route.query.id
          this.sendModifyFormdata(params)
        } else {
          this.sendFormdata(params)
        }
      } else {
        this.$message.error('请认真检查表单数据！')
      }
    },
    // 页面按钮点击
    async submitFormHandle(type) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (type === 'template') {
            this.openFormDialog(type)
          } else {
            this.validateForm(type)
          }
        } else {
          this.checkedFormData()
        }
      })
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$backVisitedViewsPath(this.$route.path, 'InquiryOrder')
            // this.$backVisitedViewsPath(this.$route.path, 'ProcureList')
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 发送数据
    async sendFormdata(params) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDrpInquiryAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        this.$backVisitedViewsPath(this.$route.path, 'InquiryOrderList')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑
    async sendModifyFormdata(params) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDrpInquiryModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        this.$backVisitedViewsPath(this.$route.path, 'InquiryOrderList')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导入弹窗
    openImport(type) {
      if (!this.formData.warehouse_id) {
        return this.$message.error('获取仓库数据失败！')
      }
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    // 导入确定事件
    confirmImportHandle(data) {
      let importData = data
      // 删除示例数据
      importData.splice(1, 1)
      console.log(111, importData)
      // importResult
      if (importData.length > 1) {
        // this.importLoading = true
        // 导入数据对应后端需要的字段
        const purchaseNameObject = {
          '物资名称': 'materials_name',
          '预计采购量': 'purchase_count',
          '单位': 'unit_name'
        }
        // 根据purchaseNameObject转换的index key
        let resultKey = {}
        importData[0].forEach((v, index) => {
          resultKey[index] = purchaseNameObject[v]
        })
        let result = []
        importData.forEach((item, index) => {
          if (index > 0) {
            let current = {}
            item.forEach((v, k) => {
              current[resultKey[k]] = v
            })
            result.push(current)
          }
        })
        console.log(222, result)
        this.sendImportMaterials(result)
      } else {
        this.$message.error('导入物资为空！')
      }
    },
    // 发送导入物资数据给后端校验
    async sendImportMaterials(data) {
      if (this.importLoading) return
      this.importLoading = true
      let params = {
        data: data,
        warehouse_id: +this.formData.warehouse_id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpInquiryImportMaterialsPost(params))
      // this.isLoading = false
      this.importLoading = false
      this.$refs.importPageRef.reset()
      this.showImportDialog = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data.success && res.data.success.length > 0) {
          let result = res.data.success.map(v => {
            return {
              materials_name: v.materials_name,
              materials_id: v.materials_id,
              purchase: v.purchase_count,
              purchase_unit_id: v.unit_id,
              purchase_unit: v.unit_name
            }
          })
          this.formData.materialsTableData = this.mergeArrays(this.formData.materialsTableData, result)
          this.initCurrentTableMaterials()
        }
        if (res.data.failure && res.data.failure.length > 0) {
          this.formatImportFailureResult(res.data.failure)
          this.$message.error('部分物资导入失败，请查看excel!')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化导入失败的数据，通过xlsx方式下载显示
    formatImportFailureResult(result) {
      const purchaseNameObject = {
        'materials_name': 0,
        'purchase_count': 1,
        'unit_name': 2,
        'result': 3
      }
      let failureJson = [
        ['物资名称', '预计采购量', '单位', '导入结果']
      ]
      let json = result.map(v => {
        let current = []
        Object.keys(purchaseNameObject).forEach(k => {
          current[purchaseNameObject[k]] = v[k]
        })
        return current
      })
      failureJson = failureJson.concat(json)
      // 下载数据
      downloadJsonExcel(failureJson)
    },
    // 选择物资确定回调事件
    confirmChooseHandle(e) {
      console.log(1212, e.data)
      this.showChooseDialog = false
      let result = []
      // 新增物资
      e.data.forEach(v => {
        let item = {
          materials_name: v.materials_name,
          purchase: e.type === 'purchase' ? v.count : v.purchase, //
          purchase_unit: v.unit_name,
          purchase_unit_id: v.unit_id,
          materials_id: v.materials_id
        }
        // 因为供应商是多选，但在外层列表中是单选的情况，得拆分
        result.push(item)
      })
      // 去重合并
      result = this.uniqueMaterials(result)
      if (this.formData.materialsTableData.length > 0) {
        this.formData.materialsTableData = this.mergeArrays(this.formData.materialsTableData, result)
      } else {
        this.formData.materialsTableData = result
      }
      this.initCurrentTableMaterials()
    },
    // 合并新旧数据，以供应商id和物资id作为唯一值，相同的数据需要合并，数量这些需要累加起来
    mergeArrays(tableData, newData) {
      // 创建一个空对象用于存储合并的结果
      let merged = {}
      // 遍历 tableData 数组
      for (let i = 0; i < tableData.length; i++) {
        let current = tableData[i]
        merged[current.materials_id] = current
      }
      newData.forEach(item => {
        // 存在相同数据，合并数量并重新计算合计
        let key = item.materials_id
        if (merged[key]) {
          merged[key].purchase = NP.plus(merged[key].purchase, item.purchase)
        } else {
        // 不存在直接往merged里面新增
          merged[key] = item
        }
      })
      return Object.values(merged)
    },
    // 物资去重，根据物资id判断，如有相同的则合并，数量累加
    uniqueMaterials(data) {
      const arr = deepClone(data)
      const newArray = []
      const tmp = {}
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        const key = `${item.materials_id}`
        if (!tmp[key]) {
          tmp[key] = item
        } else { // 如果存在相同物资和供应商的数据则合并数据
          tmp[key].purchase = NP.plus(tmp[key].purchase, item.purchase)
        }
      }
      return Object.values(tmp)
    }
  }
}
</script>

<style lang="scss">
.AddInquiry {
  position: relative;
  h3 {
    margin: 0;
  }
  .m-b-0 {
    margin-bottom: 0;
  }
  .w-280 {
    width: 280px;
  }
  .w-160 {
    width: 160px !important;
  }
  .w-auto {
    width: 300px;
  }
  .error {
    ::v-deep .el-input__inner {
      border-color: red;
    }
  }
  .red {
    color: red;
    .ps-origin {
      color: red !important;
    }
  }
  &.form-container {
    ::v-deep.el-form-item {
      // margin-bottom: 10px;
      > .el-form-item__content {
        > .el-input {
          // width: 280px;
        }
      }
    }
    .upload-img {
      width: 90px;
      height: 90px;
    }
    .img-item {
      display: inline-block;
      position: relative;
      transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
      .img-tools {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ffffff;
        font-size: 16px;
        transition: 0.3s;
        i {
          cursor: pointer;
        }
      }
      &:hover {
        .img-tools {
          display: inline-block;
        }
      }
    }
  }
}
.el-date-table td.selected span {
  color: #fff !important;
}
.right-btn {
  position: absolute;
  right: 20px;
  top: 20px;
}
</style>
