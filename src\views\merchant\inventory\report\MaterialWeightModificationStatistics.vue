<template>
  <div class="PurchaseListDetails container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      @reset="resetHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="handleExport" v-permission="['background_drp.inventory_info.fix_cost_detail_export']">导出</button-icon>
          <button-icon color="origin" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <div class="m-b-20">
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="id"
        >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #count="{ row }">
              <span>{{ row.record_type === 'PURCHASE_ENTRY' ? row.expected_entry_count : row.count }}</span>
            </template>
            <template #imgs="{ row }">
              <el-button type="text" class="ps-text" size="small" @click="clickViewerHandler(row)" :disabled="!row.image_json || row.image_json.length === 0">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- 统计 end -->
      <!-- 分页 start -->
      <div class="block" style="text-align: right; ">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
    </div>
    <!-- 预览 -->
    <image-viewer  v-model="showViewer" :initial-index="0" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
    <!-- 报表设置 -->
    <print-setting v-if="dialogPrintVisible" :extraParams="{ printType: printType }" :tableSetting="tableSetting" :defaultCheckedSetting="currentTableSetting"
    :show.sync="dialogPrintVisible" @confirm="confirmPrintDialog"></print-setting>
  </div>
</template>

<script>
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, deepClone } from '@/utils'
import { MATERIAL_WEIGHT_MODIFICATION_STATISTICS_SEARCH_SETTING, MATERIAL_WEIGHT_MODIFICATION_STATISTICS_TABLE_SETTING } from './constants.js'
import report from '@/mixins/report' // 混入
export default {
  name: 'MaterialWeightModificationStatistics',
  components: {
  },
  mixins: [exportExcel, report],
  data() {
    return {
      importLink: '',
      supplierId: '',
      tableData: [],
      isLoading: false, // 刷新数据
      searchFormSetting: deepClone(MATERIAL_WEIGHT_MODIFICATION_STATISTICS_SEARCH_SETTING),
      collect: [ // 统计
        { key: 'entry_original_total_price', value: 0, label: '实际合计：', type: 'moneyFloat' },
        { key: 'entry_total_price', value: 0, label: '修改后合计：', type: 'moneyFloat' },
        { key: 'entry_margin_price', value: 0, label: '差额合计：', type: 'moneyFloat' }
        // { key: 'exit_original_total_price', value: 0, label: '出库实际合计：', type: 'moneyKey' },
        // { key: 'exit_total_price', value: 0, label: '出库修改后合计：', type: 'moneyKey' },
        // { key: 'exit_margin_price', value: 0, label: '出库差额合计：', type: 'moneyKey' }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      previewSrcList: [],
      showViewer: false,
      tableSetting: deepClone(MATERIAL_WEIGHT_MODIFICATION_STATISTICS_TABLE_SETTING),
      currentTableSetting: deepClone(MATERIAL_WEIGHT_MODIFICATION_STATISTICS_TABLE_SETTING),
      dialogPrintVisible: false,
      printType: 'MaterialWeightModificationStatistics',
      mergeMap: new Map() // 用于存储合并信息
    }
  },
  created() {
    this.initPrintSetting()
    // this.getClassificationList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getDataList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    resetHandle() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            if (key === "materail_classification_ids") {
              if (Array.isArray(data[key].value) && data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getDataList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDrpInventoryInfoFixCostDetailPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        let data = res.data || {}
        let results = data.results || []
        this.tableData = deepClone(results)
        this.totalCount = data.count
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 导出
    handleExport() {
      const option = {
        type: this.printType,
        url: 'apiBackgroundDrpInventoryInfoFixCostDetailExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.totalCount ? this.totalCount : 10
        }
      }
      this.exportHandle(option)
    },
    // 查看预览图
    clickViewerHandler(row) {
      console.log("clickViewerHandler", row);
      // don't show viewer when preview is false
      let imgList = row.image_json || []
      if (imgList) {
        imgList = Array.isArray(imgList) ? imgList : JSON.parse(imgList)
      }
      this.previewSrcList = imgList
      if (!this.previewSrcList || this.previewSrcList.length === 0) {
        return this.$message.error('暂无图片');
      }
      this.showViewer = true;
    },
    // 关闭图片预览
    closeViewer() {
      this.showViewer = false;
    },
    // 获取物资分类列表
    async getClassificationList () {
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpMaterailClassificationListPost({
        page: 1,
        page_size: 9999
      }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        let data = res.data || []
        let results = data.results || []
        console.log(results)
        // this.searchFormSetting.materail_classification_ids.dataList = results
      }
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = this.currentTableSetting.filter(item => {
        return item.key !== 'img'
      })
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '入库物资修改记录',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundDrpInventoryInfoFixCostDetailPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount ? this.totalCount : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 排序
    setSortData(results) {
      // 先给每一条设置一个固定的key值，只使用仓库名称、日期、物资分类作为key
      results.forEach((item, index) => {
        item.key = item.warehouse_name + "_" + item.date + "_" + item.materail_classification_name;
      })
      // 正确的字符串排序
      results.sort((a, b) => a.key.localeCompare(b.key))
      return results
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
