'use strict'
const path = require('path')
const ThemeColorReplacer = require('webpack-theme-color-replacer')
const forElementUI = require('webpack-theme-color-replacer/forElementUI')

const dynamicProxy = require('./environments/proxy.js')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = 'background_v4' // page title

console.log('env', process.env.NODE_ENV)
// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 8080 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  transpileDependencies: ['@packy-tang/vue-tinymce', 'tinymce', '@riophae/vue-treeselect'],
  devServer: {
    host: '0.0.0.0', // 如果是真机测试，就使用这个IP
    port: port,
    open: false,
    // overlay: {
    //   warnings: false,
    //   errors: true
    // },
    compress: false, // 代码压缩
    disableHostCheck: true, // webpack4.0 开启热更新
    proxy: dynamicProxy.proxy
    // proxy: {
    //   '/api': {
    //     target: 'http://cashier-v4.debug.packertec.com',
    //     // target: 'http://**************:9000',
    //     // target: 'http://**************:8000',
    //     // target: 'http://*************:8000',
    //     // target: 'http://**************:8000',
    //     // target: 'http://127.0.0.1:9090/v4_background',
    //     // target: 'http://**************:8000',
    //     // target: 'http://**************:8001', // 荣哥
    //     // target: 'http://**************:8000', // 刘浩铭
    //     // target: 'http://**************:8000', // 啊树
    //     // target: 'http://**************:8888', // 森
    //     ws: true,
    //     changOrigin: true
    //     // pathRewrite: {
    //     //   '^/funny-booking': '/'
    //     // }
    //   }
    // }
    // before: require('./mock/mock-server.js')
  },
  configureWebpack: {
    // devtool: 'source-map', // 源码断点
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    plugins: [
      // 生成仅包含颜色的替换样式（主题色等）
      new ThemeColorReplacer({
        fileName: 'style/theme-colors.[contenthash:8].css',
        matchColors: [
          ...forElementUI.getElementUISeries("#409EFF")
        ],
        changeSelector: forElementUI.changeSelector,
        isJsUgly: process.env.NODE_ENV === 'production' ? true : undefined
      })
    ]
    // optimization: process.env.NODE_ENV === 'production' ? {
    //   minimize: true,
    //   minimizer: [new TerserPlugin({
    //     terserOptions: {
    //       compress: {
    //         drop_debugger: true,
    //         drop_console: true,
    //         pure_funcs: ['console.log'] // 删除打印语句
    //       },
    //       format: {
    //         comments: false // 删除所有注释
    //       }
    //     },
    //     parallel: true, // 多核打包，提升打包速度
    //     extractComments: false // 是否将注释全部集中到一个文件中
    //   })]
    // } : {}
  },
  chainWebpack(config) {
    // it can improve the speed of the first screen, it is recommended to turn on preload
    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch')
    // 配置 file-loader
    const fileRule = config.module.rule('file')
    fileRule.uses.clear()
    fileRule
      .test(/\.pdf|\.wps|\.docx|\.txt|ico$/)
      .use('file-loader')
      .loader('file-loader')
      .options({
        // limit: 10000
      })
    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config.when(process.env.NODE_ENV !== 'development', config => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/
          }
        ])
        .end()
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial' // only package third parties that are initially dependent
          },
          elementUI: {
            name: 'chunk-elementUI', // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true
          }
        }
      })
      // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
      config.optimization.runtimeChunk('single')
    })

    // 设置cdn
    // var externals = {
    //   vue: 'Vue',
    //   axios: 'axios',
    //   'element-ui': 'ELEMENT',
    //   'vue-router': 'VueRouter',
    //   vuex: 'Vuex'
    // }
    // config.externals(externals)
    const cdn = {
      css: [
        // element-ui css
        // '//unpkg.com/element-ui/lib/theme-chalk/index.css'
      ],
      js: [
        // vue
        // '//cdn.staticfile.org/vue/2.5.22/vue.min.js',
        // // vue-router
        // '//cdn.staticfile.org/vue-router/3.0.2/vue-router.min.js',
        // // vuex
        // '//cdn.staticfile.org/vuex/3.1.0/vuex.min.js',
        // // axios
        // '//cdn.staticfile.org/axios/0.19.0-beta.1/axios.min.js',
        // debug.js
        '//192.168.50.248:6060/static/debug/debug.js'
      ]
    }
    config.plugin('html')
      .tap(options => {
        options[0].cdn = cdn
        // options[0].title = '朴食科技'
        return options
      })
  }
}
