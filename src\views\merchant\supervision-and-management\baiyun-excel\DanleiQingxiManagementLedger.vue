<template>
  <div class="container-wrapper">
    <div class="egg-cleaning-record">
      <h2 class="table-title">蛋类清洗记录表</h2>

      <div class="year-month-row">
        <span class="year-month-label">年</span>
        <span class="year-month-value">{{ currentYear }}</span>
        <span class="year-month-label">月</span>
        <span class="year-month-value">{{ currentMonth }}</span>
      </div>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="date" label="日期" align="center"></el-table-column>
        <el-table-column prop="name" label="名称" align="center"></el-table-column>
        <el-table-column prop="quantity" label="数量" align="center"></el-table-column>
        <el-table-column label="清洗时间" align="center" >
          <template slot-scope="scope">
           <div v-if="scope.row.startTime">{{ scope.row.startTime }} 至 {{ scope.row.endTime }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
        <el-table-column prop="inspector" label="检查人" align="center"></el-table-column>
        <el-table-column prop="result" label="检查结果" align="center"></el-table-column>
      </el-table>

      <div class="notes">
        <p>备注：1、建议清洗、浸泡时间不低于30分钟</p>
        <p class="indent">2、如需清洗多种蛋类，请如实全部记录</p>
      </div>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 31]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DanleiQingxiManagementLedger',
  data() {
    return {
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      currentPage: 1,
      pageSize: 31,
      totalItems: 31,
      tableData: this.generateDaysData()
    };
  },
  methods: {
    generateDaysData() {
      const days = [];
      for (let i = 1; i <= 31; i++) {
        days.push({
          date: i,
          name: '',
          quantity: '',
          startTime: '',
          endTime: '',
          operator: '',
          inspector: '',
          result: ''
        });
      }
      return days;
    },
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    cellStyle() {
      return {
        padding: '8px 5px',
        fontSize: '14px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 5px',
        textAlign: 'center'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.egg-cleaning-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: bold;
  }

  .year-month-row {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 16px;

    .year-month-label {
      margin: 0 5px;
    }

    .year-month-value {
      min-width: 40px;
      text-align: center;
      border-bottom: 1px solid #000;
      margin: 0 15px 0 5px;
    }
  }

  .notes {
    margin-top: 20px;
    font-size: 14px;
    line-height: 1.5;

    .indent {
      margin-left: 24px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
