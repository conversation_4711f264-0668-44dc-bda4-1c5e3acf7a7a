<template>
  <div class="ShitangDaojuManagementLedger container-wrapper">
    <refresh-tool @refreshPage="refreshHandle"></refresh-tool>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="clickHistoricalRecords">历史记录</button-icon>
          <button-icon color="origin" @click="handlerShowDialog('add', null)" v-permission="['background_fund_supervision.ledger_food_safety.egg_cleaning_record_ledger']">蛋类配置</button-icon>
          <button-icon color="origin" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="gotoExport" v-permission="['background_fund_supervision.ledger_food_safety.egg_cleaning_record_ledger']">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="pagedTableData"
          border
          style="width: 100%"
          header-row-class-name="ps-table-header-row"
          >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
            </template>
            <template #result="{ row }">
              <div class="result-text"> {{ row.result === 1 ? '正常' : '不合格' }}</div>
            </template>
          </table-column>
        </el-table>
      </div>
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 31, 50]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      />
    </div>
     <!-- 添加弹窗 -->
     <configuration-add-edit-drawer ref="configurationAddEditDrawer" @confirm="confirmRecordDialog" @close="closeDialog" :isshow="dialogVisible" :type="dialogType" :drawerData="drawerData" :keyType="keyType" />
     <!-- 历史记录抽屉 -->
     <HistoryRecordDrawer
      :visible.sync="isShowRecordDialog"
      title="历史记录"
      api="apiBackgroundFundSupervisionLedgerFoodSafetyOperateLogList"
      type="Egg"
      @close="isShowRecordDialog = false"
    >
    </HistoryRecordDrawer>
  </div>
</template>

<script>
import exportExcel from '@/mixins/exportExcel'
import { SEARCH_SETTING_DANLEI_QINGXI_LEDGER, TABLE_HEAD_DATA_DANLEI_QINGXI_LEDGER } from './constants'
import { debounce, to, deepClone } from '@/utils'
import report from '@/mixins/report'
import ConfigurationAddEditDrawer from './compontents/ConfigurationAddEditDrawer.vue'
import HistoryRecordDrawer from './compontents/HistoryRecordDrawer.vue'
export default {
  name: 'DanleiQingxiManagementLedger',
  mixins: [exportExcel, report],
  components: {
    ConfigurationAddEditDrawer,
    HistoryRecordDrawer
  },
  data() {
    return {
      isLoading: false,
      currentPage: 1,
      pageSize: 10,
      totalCount: 31,
      tableData: [],
      searchFormSetting: deepClone(SEARCH_SETTING_DANLEI_QINGXI_LEDGER),
      tableSettings: deepClone(TABLE_HEAD_DATA_DANLEI_QINGXI_LEDGER),
      printType: 'DanleiQingxiManagementLedger',
      isShowRecordDialog: false, // 历史记录
      dialogType: '', // 弹窗类型
      dialogTitle: '', // 弹窗标题
      dialogVisible: false, // 弹窗是否显示
      drawerData: {}, // 弹窗数据
      keyType: 'Egg', // 配置项类型
      diseaseList: [] // 疾病特征
    }
  },
  created() {
    this.initLoad()
  },
  computed: {
    pagedTableData() {
      const start = (this.currentPage - 1) * this.pageSize
      return this.tableData.slice(start, start + this.pageSize)
    }
  },
  methods: {
    initLoad() {
      this.getDataList()
    },
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetEggCleaningRecord(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.tableData = deepClone(results)
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg || '获取数据失败')
      }
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.tableSettings)
      tableSetting = tableSetting.filter(item => item.key !== 'operation')
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: false,
          print_type: this.printType,
          print_title: '蛋类清洗记录表',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetEggCleaningRecord', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    },
    // 导出
    gotoExport() {
      const option = {
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetEggCleaningRecordExport',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          date_type: this.printType,
          page: this.currentPage,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    },
    // 点击历史记录
    clickHistoricalRecords() {
      this.isShowRecordDialog = true
    },
    // 添加弹窗
    handlerShowDialog(type, data) {
      this.dialogType = type
      this.dialogTitle = type === 'add' ? '添加' : '编辑'
      if (type === 'add') {
        this.drawerData = {}
      } else {
        this.drawerData = deepClone(data || {})
      }
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
    },
    // 确认
    confirmRecordDialog(data) {
      console.log('confirmRecordDialog', data)
      this.dialogVisible = false
      this.initLoad()
    }
  }
}
</script>

<style lang="scss" scoped>
.ShitangDaojuManagementLedger {
  .table-wrapper {
    background: #fff;
    padding: 12px;
    border-radius: 4px;
  }
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .table-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
