<template>
  <div calss="ps-dialog-doublefactor">
    <el-dialog
      :custom-class="'ps-dialog ' +  customClass"
      :title="title"
      :visible.sync="visible"
      :width="width"
      :top="top"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :show-close="false"
      :center="center"
      @closed="handleClose">
      <div class="content" v-loading="isLoading">
        <el-form
          :model="checkFormData"
          :rules="rules"
          ref="doubleFormRef"
          class="form"
          label-width="0"
          @submit.native.prevent
        >
          <el-form-item prop="phone" class="phone">
            手机号: {{ userInfo.mobile }}
          </el-form-item>
          <el-form-item prop="smsCode" class="phone-code">
            <verification-code :sendAuthCode="sendAuthCode" :disabled="sendCodeDisabled" @click="getPhoneCode" :reset-handle="resetHandle" v-model="checkFormData.smsCode"></verification-code>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :disabled="isLoading" class="check-btn" type="primary" @click="clickConfirmHandle">{{ confirmText }}</el-button>
        <el-button :disabled="isLoading" class="ps-warn-text" style="margin-left: 0;" type="text" @click="layoutOutHandle">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 登录过期弹窗，过期弹窗 -->
    <dialog-message width="450px" :title="messageTitle" :show-close="false" :message="messageContent" :confirm-text="massageConfirmText" :show.sync="showDialog" @close="closeTimeDialogHandle('close')" @confirm="closeTimeDialogHandle" />
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import { to, setSessionStorage, getSessionStorage } from '@/utils'
import { log } from "@chenfengyuan/vue-qrcode"

export default {
  name: 'DoubleFactor',
  props: {
    title: {
      type: String,
      default: '验证登录'
    },
    width: {
      type: String,
      default: '330px'
    },
    top: {
      type: String,
      default: '15vh'
    },
    confirmText: {
      type: String,
      default: '验 证'
    },
    customClass: {
      type: String,
      default() {
        return 'ps-dialog-doublefactor'
      }
    },
    center: Boolean,
    userInfo: {
      type: Object
    }
  },
  data() {
    return {
      isLoading: false,
      visible: false,
      sendAuthCode: true,
      sendCodeDisabled: false,
      checkFormData: {
        phone: '',
        smsCode: ''
      },
      rules: {
        smsCode: [{ required: true, message: '请输入验证码', trigger: 'change' }]
      },
      showDialog: false,
      countDownHandle: null,
      messageType: '', // 1长时间未登录, 2密码长时间修改，3 登录已过期强制退出
      messageTitle: '',
      messageContent: '',
      massageConfirmText: ''
    }
  },
  computed: {
    // ...mapGetters([
    //   'userInfo'
    // ])
  },
  watch: {
    // $route: {
    //   handler: function(route) {
    //     const query = route.query
    //   },
    //   immediate: true
    // }
  },
  created () {
  },
  mounted () {
    this.visible = !(getSessionStorage('CHECKDOUBLEFACTOR') === '1')
    if (!this.userInfo.mobile) {
      this.visible = false
    }
    // this.countDown()
    // this.showChangePwdHandle()
    document.addEventListener('keydown', this.enterKeydowHandler)
  },
  methods: {
     // 监听键盘事件
     enterKeydowHandler(e) {
      console.log("enterKeydowHandler", e.keyCode);
      if (e.keyCode === 13) {
        this.clickConfirmHandle()
      }
    },
    async getPhoneCode() {
      const [err, res] = await to(this.$apis.apiBackgroundVerificationCodeAutoPost())
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.sendAuthCode = false
        this.$message.success('发送成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重置验证码倒计时
    resetHandle(e) {
      this.sendAuthCode = true
    },
    clickConfirmHandle() {
      // this.$emit('confirm')
      this.$refs.doubleFormRef.validate(valid => {
        if (valid) {
          this.checkSmsCode();
        }
      })
    },
    // 验证
    async checkSmsCode() {
      if (this.isLoading) return;
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundCheckVerificationCodePost({
        sms_code: this.checkFormData.smsCode
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        setSessionStorage('CHECKDOUBLEFACTOR', '1')
        this.$message.success('验证成功')
        this.showChangePwdHandle()
        this.$emit('doubleConfirm')
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      this.$emit('cancel')
    },
    handleClose(e) {
      this.isLoading = false
      this.$emit('close')
    },
    // 倒计时
    countDown() {
      let time = Number(getSessionStorage('REQUESTTIME'))
      let diff = new Date().getTime() - time
      console.log(1223132131, new Date().getTime() - time, new Date().getTime(), time)
      if (diff > 1.8e6 && !this.showDialog) {
        clearTimeout(this.countDownHandle)
        this.messageType = '1'
        this.massageConfirmText = '重新登录'
        this.messageTitle = '确定登出'
        this.messageContent = '由于长时间没有操作，登录已过期，请重新登录。'
        this.showDialog = true
        return
      }
      this.countDownHandle = setTimeout(this.countDown, 1000)
    },
    closeTimeDialogHandle(type) {
      this.showDialog = false
      this.layoutOutHandle()
    },
    layoutOutHandle(type) {
      this.visible = false
      this.$store.dispatch('user/logout')
      this.$emit('doubleCancel')
      return
      if (this.messageType === '2' && type !== 'close') {
        this.$router.push({
          path: '/account/setting'
        })
      } else {
        this.$store.dispatch('user/logout')
        this.$router.push({
          path: '/login'
        })
      }
    },
    showChangePwdHandle() {
      // 开启了到期修改密码并且，密码长时间未修改
      if (this.userInfo.last_change_pwd_time && this.userInfo.is_expire_change_pwd) {
        let diffTime = new Date().getTime() - new Date((this.userInfo.last_change_pwd_time).replace(new RegExp(/-/gm), '/')).getTime()
        // let diffTime = new Date('2022/05/17 12:12:12').getTime() - new Date().getTime()
        if (diffTime <= (90 * 24 * 60 * 60 * 1000)) {
          // 小于3天弹窗
          if (diffTime >= (87 * 24 * 60 * 60 * 1000)) {
            this.messageType = '2'
            this.massageConfirmText = '修改密码'
            this.messageTitle = '提示'
            let day = parseInt(90 - Math.floor(diffTime / (24 * 60 * 60 * 1000)))
            if (day) {
              this.messageContent = `您的登录密码${day}天后即将过期，为了不影响您正常使用，建议及时修改。`
            } else {
              this.messageContent = `您的登录密码1天后即将过期，为了不影响您正常使用，建议及时修改。`
            }
            if (getSessionStorage('ISEXPIRECHANGEPWD') !== '1') {
              this.showDialog = true
            }
          }
        } else {
          this.messageType = '3'
          this.massageConfirmText = '确 定'
          this.messageContent = `帐号失效，无法登陆`
          this.showDialog = true
        }
      }
    }
  },
  beforeDestroy() {
    console.log("beforeDestroy");
    document.removeEventListener('keydown', this.enterKeydowHandler)
    if (this.countDownHandle) {
      clearTimeout(this.countDownHandle)
    }
  }
}
</script>

<style lang="scss">
.ps-dialog-doublefactor{
  .el-dialog__header{
    text-align: center;
    .el-dialog__title{
      font-weight: 500;
    }
  }
  .el-dialog__body{
    padding: 15px 20px;
  }
  .content{
    // text-align: center;
  }
  .dialog-footer{
    text-align: center;
    .check-btn {
      width: 100%;
      display: block;
      color: #ffffff;
      background-image: linear-gradient(90deg, #ffa545 0%, #ff8346 100%),
        linear-gradient(#ff9b45, #ff9b45);
      background-blend-mode: normal, normal;
      box-shadow: 0px 5px 7px 0px rgba(255, 155, 69, 0.5);
      border-radius: 4px;
      border: none;
      &:hover {
        opacity: 0.8;
      }
      &:active {
        opacity: 0.9;
      }
    }
  }
}
</style>
