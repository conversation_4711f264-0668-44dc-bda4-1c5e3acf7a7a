<template>
  <!-- 表26-学校校园食品安全工作管理清单-详情 -->
  <div class="content" v-loading="isLoading">
    <div class="title">配送单位信息</div>
    <div class="basic-info m-b-20">
      <el-descriptions :column="2" v-if="detailInfo">
        <el-descriptions-item v-for="item in unitInfoKey" :key="item.key" :label="item.name">
          <template v-if="item.type === 'array'">
            <span>{{ detailInfo.extra[item.key][0] }} 至 {{ detailInfo.extra[item.key][1] }}</span>
          </template>
          <span v-else>{{ detailInfo.extra[item.key] || '--' }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="title">配送信息</div>
    <div class="basic-info m-b-20">
      <el-descriptions :column="2" v-if="detailInfo">
        <el-descriptions-item v-for="item in baseInfoKey" :key="item.key" :label="item.name">
          <div v-if="item.type === 'array' && detailInfo[item.key] && detailInfo[item.key].length" class="flex">
            <div v-for="(item, index) in detailInfo[item.key]" :key="index" class="m-r-20">
              <div>{{ `菜品${index + 1}信息` }}</div>
              <div>菜品名称：{{ item.dishName }}</div>
              <div>到达中心温度：{{ item.temperature }} ℃</div>
            </div>
          </div>
          <span v-else>{{ detailInfo[item.key] || '--' }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div v-if="detailInfo">
      <div class="title">菜品信息</div>
      <el-table :data="detailInfo.food_info" stripe border style="width: 100%">
        <el-table-column prop="dishName" label="菜品名称" align="center"></el-table-column>
        <el-table-column prop="temperature" label="到达中心温度(°C)" align="center"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { to } from '@/utils'
export default {
  name: 'Table26JitiYongcanManagementLedger',
  props: {
    ledgerId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      detailInfo: null,
      baseInfoKey: [
        { name: '配送日期', key: 'operate_date' },
        { name: '配送年级', key: 'delivery_grade' },
        { name: '配送人数(人)', key: 'delivery_person_count' },
        { name: '配送餐次', key: 'meal_type_verbose' },
        { name: '配送份数(份)', key: 'delivery_count' },
        { name: '配送人', key: 'delivery_person' },
        { name: '出发时间', key: 'start_time' },
        { name: '签收人', key: 'receiver' },
        { name: '到达时间', key: 'end_time' }
        // { name: '配送单位', key: 'delivery_unit' },
        // { name: '菜品信息', key: 'food_info', type: 'array' }
      ],
      unitInfoKey: [
        { name: '单位名称', key: 'delivery_unit_name' },
        { name: '营业执照号码', key: 'business_license' },
        { name: '食品经营许可证', key: 'aptitude_name' },
        { name: '许可证有效期至', key: 'end_date' },
        { name: '配送合同起止时间', key: 'delivery_contract_time', type: 'array' },
        { name: '经营地址', key: 'address' },
        { name: '法定代表人', key: 'contact_name' },
        { name: '联系电话', key: 'contact_Phone' },
        { name: '食品安全管理员', key: 'food_safety_admin' },
        { name: '联系电话', key: 'food_safety_admin_Phone' },
        { name: '配送负责人', key: 'delivery_manager' },
        { name: '联系电话', key: 'delivery_manager_Phone' }
      ]
    }
  },
  mounted() {
    // this.getLedgerReviewDetail()
  },
  watch: {
    // 监听 ledgerId 变化
    ledgerId: {
      handler(newVal) {
        if (newVal) {
          this.getLedgerReviewDetail(newVal)
        }
      },
      immediate: true // 立即执行
    }
  },
  methods: {
    // 获取详情通用接口
    async getLedgerReviewDetail(id) {
      this.isLoading = true
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyLedgerReviewDetail({ id }))
      if (err) {
        this.$message.error(err.message)
        this.isLoading = false
        return
      }
      if (res.code === 0) {
        this.detailInfo = res.data
      } else {
        this.$message.error(res.msg)
      }
      this.isLoading = false
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 0 10px;
}
::v-deep .el-descriptions__body {
  background-color: #f2f2f2;
}
.title {
  margin-bottom: 10px;
  position: relative;
  padding-left: 15px; /* 为竖线留出空间 */
}

.title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px; /* 竖线宽度 */
  height: 16px; /* 竖线高度 */
  background-color: #FF9B45; /* 竖线颜色，可根据需要调整 */
  border-radius: 2px; /* 可选，圆角效果 */
}
.basic-info {
  padding: 20px;
  background-color: #f2f2f2;
  border-radius: 20px;
}
</style>
