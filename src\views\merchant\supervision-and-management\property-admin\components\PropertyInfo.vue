<template>
  <!-- 晨检记录-->
  <div class="container-wrapper">
    <div class="tab-item m-t-20">
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            数据列表
            <el-popover placement="left" title="" width="300" trigger="hover">
              <div>原材料统计数据每日随系统库存变动</div>
              <i class="el-icon-question" slot="reference" style="color: #ff9b45"></i>
            </el-popover>
          </div>
          <div class="align-r">
            <button-icon color="plain" @click="clickHistory">历史记录</button-icon>
            <button-icon color="plain" @click="propertyCategoryDrawerShow = true">资产分类</button-icon>
            <button-icon color="plain" @click="clickPropertyUploadDrawerShow">上传</button-icon>
            <!-- <button-icon color="plain" @click="gotoExport">导出</button-icon> -->
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            tooltip-effect="dark property-info-tooltips"
            header-row-class-name="ps-table-header-row"
            empty-text="暂无数据，请查询"
          >
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #operator_name="{ row }">
                <span v-if="row.operator_member_name">{{ row.operator_member_name }}（{{ row.operator_name }}）</span>
                <span v-else>--</span>
              </template>
              <template #operation="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="gotoExport('download', row)">
                  下载
                </el-button>
                <el-button type="text" size="small" class="ps-text" @click="clickAssetInfoDetails(row)">
                  查看明细
                </el-button>
                <el-button type="text" size="small" class="ps-red" :disabled="row.is_sys" @click="clickDel(row)">删除</el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- 历史记录 -->
    <property-info-history-drawer
      :isShow.sync="propertyInfoHistoryDrawerShow"
      v-if="propertyInfoHistoryDrawerShow"
    ></property-info-history-drawer>
    <!-- 详情 -->
    <property-info-details-drawer
      :isShow.sync="propertyInfoDetailsDrawerShow"
      :infoData="infoDrawerData"
      v-if="propertyInfoDetailsDrawerShow"
    ></property-info-details-drawer>
    <!-- 资产分类 -->
    <property-category-drawer
      :isShow.sync="propertyCategoryDrawerShow"
      v-if="propertyCategoryDrawerShow"
    ></property-category-drawer>
    <!-- 导入数据的弹窗 start -->
    <import-dialog-drawer
      v-if="propertyUploadDrawerShow"
      :templateUrl="templateUrl"
      :tableSetting="importTableSetting"
      :show.sync="propertyUploadDrawerShow"
      :title="'上传'"
      importType="data"
      ref="importDialogRef"
      openExcelType="ImportPropertyInfo"
      @confirm="confirmImportData"
    >
      <div slot="content">
        <el-form ref="formDataRef" label-width="120px" :model="formData" :rules="formRules">
          <el-form-item label="日期归属：" prop="upload_date">
            <el-date-picker
              v-model="formData.upload_date"
              type="month"
              value-format="yyyy-MM-dd"
              class="w-250"
              placeholder="请选择选择日期归属"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="文件名称：" prop="name">
            <el-input
              class="ps-input w-250"
              v-model="formData.name"
              maxlength="10"
              placeholder="请输入文件名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注：" prop="remark">
            <el-input
              class="ps-input w-250"
              v-model="formData.remark"
              type="textarea"
              maxlength="20"
              :autosize="{ minRows: 3, maxRows: 4 }"
              placeholder="请输入备注"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </import-dialog-drawer>
  </div>
</template>

<script>
import { SEARCH_PROPERTY_INFO, TABLE_PROPERTY_INFO } from './constants'
import { debounce, deepClone } from '@/utils'
import PropertyInfoHistoryDrawer from './PropertyInfoHistoryDrawer.vue'
import PropertyInfoDetailsDrawer from './PropertyInfoDetailsDrawer.vue'
import PropertyCategoryDrawer from './PropertyCategoryDrawer.vue'
import * as dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'PropertyInfo',
  mixins: [exportExcel],
  components: {
    PropertyInfoHistoryDrawer,
    PropertyInfoDetailsDrawer,
    PropertyCategoryDrawer
  },
  data() {
    return {
      isLoading: false,
      tableSetting: deepClone(TABLE_PROPERTY_INFO),
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: deepClone(SEARCH_PROPERTY_INFO),
      printType: 'PropertyInfo',
      propertyInfoHistoryDrawerShow: false,
      propertyInfoDetailsDrawerShow: false, // 详情
      propertyCategoryDrawerShow: false, // 资产分类
      propertyUploadDrawerShow: false, // 上传
      importTableSetting: [
        { key: 'number_no', label: '资产编号（必填）' },
        { key: 'asset_category_alias', label: '资产类别（必填）' },
        { key: 'asset_type_alias', label: '资产类型（必填）' },
        { key: 'asset_classify_name', label: '资产分类（必填）' },
        { key: 'name', label: '资产名称（必填）' },
        { key: 'original_fee', label: '原值（必填）' },
        { key: 'depreciation_fee', label: '累计折旧' },
        { key: 'net_worth_fee', label: '净值（必填）' },
        { key: 'quantity', label: '数量' },
        { key: 'purchase_date', label: '购置日期' },
        { key: 'change_date', label: '变更日期' },
        { key: 'unit', label: '单位' },
        { key: 'place', label: '存放地点' },
        { key: 'use_unit', label: '在用单位' },
        { key: 'remark', label: '备注' }
      ],
      templateUrl: location.origin + '/api/temporary/template_excel/channel/资产信息导入.xlsx',
      formData: {
        upload_date: '',
        name: '',
        remark: ''
      },
      formRules: {
        upload_date: [{ required: true, message: '请选择日期归属', trigger: ['change', 'blur'] }],
        name: [{ required: true, message: '请输入名字', trigger: ['change', 'blur'] }]
      },
      infoDrawerData: {}
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getDataList()
    },
    // 筛选
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time' && key !== 'upload_start') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            if (key === 'upload_start') {
              // 日期归属
              let yearMonth = dayjs(data[key].value[1])
              // 获取下个月的第一天
              let startOfNextMonth = yearMonth.add(1, 'month').startOf('month')
              // 回退一天得到本月的最后一天
              let lastDayOfMonth = startOfNextMonth.subtract(1, 'day')
              params.upload_start_date = data[key].value[0] + '-01'
              params.upload_end_date = lastDayOfMonth.format('YYYY-MM-DD')
            }
            if (key === 'select_time') {
              params.create_start_date = data[key].value[0]
              params.create_end_date = data[key].value[1]
            }
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getDataList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundFundSupervisionAssetAssetInfoListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        let results = data.results.map(v => {
          v.upload_date = v.upload_date ? dayjs(v.upload_date).format('YYYY年MM月') : ''
          return v
        })
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    clickHistory() {
      this.propertyInfoHistoryDrawerShow = true
    },
    clickPropertyUploadDrawerShow() {
      this.propertyUploadDrawerShow = true
      this.formData = {
        upload_date: '',
        name: '',
        remark: ''
      }
    },
    confirmImportData(data) {
      this.$refs.formDataRef.validate(async valid => {
        if (valid) {
          let params = {
            upload_date: this.formData.upload_date,
            name: this.formData.name,
            remark: this.formData.remark
          }
          // 额外table多一些参数 需要额外加两个字段
          this.$refs.importDialogRef.importTypeRef = 'excel'
          this.$refs.importDialogRef.paramsRef = params
          this.$refs.importDialogRef.clickConfirmHandle()
        }
      })
    },
    clickAssetInfoDetails(item) {
      this.infoDrawerData = item
      this.propertyInfoDetailsDrawerShow = true
    },
    clickDel(item) {
      this.$confirm(`确定删除该内容？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundFundSupervisionAssetAssetInfoDeletePost({
              id: item.id
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                }
              }
              this.getDataList()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    gotoExport(type, item) {
      const option = {
        url: 'apiBackgroundFundSupervisionAssetAssetInfoDetailsListExportPost',
        params: {
          page: 1,
          page_size: 9999,
          asset_info_id: item.id
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
.property-info-tooltips {
  white-space: normal; /* 允许换行 */
  word-break: break-all; /* 长单词自动换行 */
  max-width: 200px; /* 设置最大宽度，超出后换行 */
}
</style>
