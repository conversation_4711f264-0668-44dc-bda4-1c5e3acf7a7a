export default {
  route: {
    no_permission: '暂无权限访问',
    merchant_system: '系统管理',
    organization_list: '组织架构',
    account_management: '账号管理',
    role_account_management: '账号管理',
    role_management: '角色管理',
    role_setting: '权限设置',
    excel: '导出Excel',
    user_management: '员工管理',
    merchant_management: '商户管理',
    system_management: '系统管理',
    department_management: '部门管理',
    list_log: '操作日志',
    admin_merchant_management: '商户管理',
    large_screen_configuration: '大屏配置',
    companyadmin: '公司管理',
    organizationAdmin: '组织管理',
    company: '公司',
    user_admin: '用户管理',
    user_admin_list: '用户列表',
    user_admin_list_detail: '用户详情',
    user_center: '用户中心',
    user_department: '用户部门',
    user_group: '用户分组',
    user_account_admin: '账户管理',
    user_account_list: '账户列表',
    user_account_setting: '账户设置',
    overdraft_setting: '透支设置',
    Overdraft_detail: '透支明细',
    withdrawal_admin: '退户管理',
    card_loss_list: '挂失卡管理',
    user_flat_cost: '工本费',
    user_flat_cost_list: '工本费列表',
    user_repair_card_list: '补卡费列表',
    subsidy_rules: '补贴发放规则',
    user_card_subsidy: '补贴管理',
    user_card_subsidy_add: '补贴',
    user_card_subsidy_detail: '补贴详情',
    user_card_log: '账户操作日志',
    information_collection: '信息收集',
    device_management: '设备管理',
    device_list: '设备列表',
    abnormal_video_list: '异常视频',
    device_admin_card: '设备管理卡',
    cupboard_edit: '取餐柜编辑',
    cupboard_setting: '取餐柜设置',
    weight_food: '绑定菜品',
    weight_setting: '智能秤设置',
    routine_setting: '常规设置',
    tray_qrcode: '托盘码管理',
    print_setting: '打印设备设置',
    print_device: '打印设备',
    active_code_admin: '激活码管理',
    import_active_code_admin: '批量导入激活码',
    dingTalkMessageSeting: '钉钉消息推送配置',
    announcement_management: '公告管理',
    notice_admin: '通知公告',
    update_record: "更新记录",
    notice: '用户公告',
    haveViewed: '已查看人数',
    notice_list: '查看公告',
    notice_detail: '公告详情',
    questionnaire_detail: '问卷详情',
    mul_import_face: '上传人脸',
    accountSetting: '账号设置',
    upgradeService: '升级服务',
    generalSettings: '常规设置',
    meal_management: '用餐管理',
    food_admin: '菜品 / 商品管理',
    ingredients_admin: '食材管理',
    ingredients_library: '食材库',
    health_nutrition_library: '健康营养库',
    ingredients: '食材',
    ingredients_category: '分类列表',
    meal_food_classification: '菜品/商品分类',
    meal_food_list: '菜品/商品库',
    diet_nutrition: '膳食营养素',
    diet_crowd: '膳食人群',
    recipes_manage: '菜谱管理',
    booking: '预约设置',
    booking_meal: '预约规则',
    add_booking_order: '新增预约点餐',
    booking_order_list: '预约点餐明细',
    canteen_booking: '预约点餐汇总',
    group_order: '分组点餐汇总',
    food_category: '菜品汇总',
    add_week_recipes: '添加菜品（周）',
    add_month_recipes: '添加菜品（月）',
    menu_catering: '添加菜品',
    nutrition_analysis: '营养分析',
    menu_rule_setting: '菜谱规则设置',
    collective_admin: '集体管理',
    intention_menu_rank: '意向菜谱排行',
    address: '地址管理',
    address_admin: '配送地址',
    address_area_admin: '配送区域',
    distribution: '配送管理',
    get_meal_report: '配送区域领餐表',
    transverse_report: '配送汇总表（横）',
    vertical_report: '配送汇总表（竖）',
    consumption_rules: '消费规则',
    consumption_rules_list: '消费规则',
    consumption_rules_form: '消费规则',
    activity_recharges: '充值活动',
    activity_recharges_list: '活动列表',
    add_activity_recharges: '新建充值活动',
    activity_recharges_statistics: '充值活动统计',
    dataReport: '数据报表',
    financialStatement: '财务报表',
    dataReporting: '数据报表',
    consume_detail_list: '消费明细表',
    consume_reconciliation: '消费点对账表',
    detail_total_list: '明细总表',
    device_cost: '设备消费明细',
    top_up_detail: '充值明细表',
    order: '订单管理',
    consumption: '消费订单',
    consumption_detail: '消费订单详情',
    consumption_auto_selling_detail: '售货柜订单详情',
    order_refund: '退款订单',
    order_recharge: '充值订单',
    consumption_failure: '异常订单',
    consumption_failure_detail: '异常订单详情',
    withdraw_order: '提现订单',
    appeal_order: '申诉审批',
    appeal_order_detail: '申诉审批详情',
    meal_report: '报餐设置',
    meal_report_admin: '报餐规则',
    head_count: '未报餐人员统计',
    add_meal_report: '报餐',
    meal_report_detail: '报餐点餐明细',
    meal_package_rule: '餐包规则',
    meal_package_detail: '餐包点餐明细',
    dep_meal_report: '部门报餐汇总',
    canteen_meal_report: '食堂报餐汇总',
    home: '首页',
    home_page: '经营数据',
    application: '应用',
    application_center: '应用中心',
    health_system: '档案管理',
    parameter_configuration: '参数配置',
    user_health_system: '健康档案',
    user_physical_examination_report: '体检报告',
    user_disease_management: '疾病管理',
    super_commodity: '菜品/商品库',
    ingredients_commodity: '菜品/商品库',
    super_add_commodity: '创建到菜品/商品库',
    import_ingredients: '食材',
    import_commodity: '菜品/商品',
    business_list: '营业额日报表',
    super_device_log: '设备日志',
    third_party_equipment_admin: '第三方设备',
    d2_consumer_machine_seting: 'D2消费机设置',
    withdraw_list: '提现明细表',
    write_off_detail: '冲销明细表',
    copy_ingredients: '拉取食材',
    copy_foods: '添加菜品',
    super_health_assessment: '健康测评',
    add_edit_questionnaire: '添加/编辑问卷',
    super_habit_cultivate: '习惯养成',
    super_article_push: '文章推送',
    super_article_admin: '文章管理',
    add_edit_article: '添加/编辑文章',
    super_article_label: '文章标签',
    flat_cost_report: '工本费收款明细',
    flat_cost_refund: '工本费退款明细',
    personal_recharge_summary: '个人充值汇总',
    departmental_consumption_summary: '部门消费汇总',
    personal_consumption_summary: '个人消费汇总',
    group_wallet_daily: '分组储值汇总表',
    user_health_records: '用户健康档案',
    records_detail: '档案详情',
    body_detail: '身体检测',
    view_report: '查看报告',
    motion_admin: '运动管理',
    add_edit_motion_admin: '添加/编辑运动',
    parameter_config: '配置参数',
    nutrition_health: '营养健康',
    add_modify_nutrition_health: '添加/编辑营养健康',
    health_info_config: '健康信息配置',
    health_fraction_rule: '健康分规则',
    add_modify_health_fraction_rule: '编辑健康分规则',
    score_time: '评分餐段',
    modify_score_time: '修改评分餐段',
    account_wallet_daily: '账户钱包日报表',
    personal_wallet_daily: '个人钱包日报表',
    collectionl_code_report: '收款码明细表',
    service_charge_report: '手续费总表',
    device_ceil_set: '取餐柜设置',
    jiaofei_center: '缴费中心',
    jiaofei_admin: '缴费管理',
    jiaofei_setting: '缴费设置',
    jiaofei_detail: '缴费明细',
    jiaofei_order: '缴费订单',
    jiaofei_refund_order: '退款订单',
    jiaofei_refund_apply: '退款申请',
    jiaofei_data_report: '缴费数据',
    supplier_admin: '供应商管理',
    add_edit_supplier: '新增或编辑供应商',
    relation_supplier_ingredient: '关联食材',
    approve_order: '取消订单审批',
    approve_order_detail: '取消订单审批详情',
    approve_cancel_order: '审批订单',
    set_meal_admin: '套餐管理',
    add_edit_set_meal: '新建或编辑套餐',
    set_meal_classify: '套餐分类',
    set_meal_summary: '套餐汇总',
    crowd_admin: '人群管理',
    add_or_modify_crowd: '人群',
    cupboard_order: '取餐柜订单',
    report_management: '经营报表',
    customized_report: '定制报表',
    food_sale_ranking: '菜品销售排行',
    summary_of_sales: '菜品分类销售汇总表',
    import_third_party_equipment: '导入第三方设备',
    secret_key_admin: '碗碟秘钥管理',
    operations_management: '运营管理',
    evaluate_admin: '订单审批',
    evaluate_list: '订单评价',
    feedback: '建议反馈',
    operations_setting: '评价设置',
    attendance: '考勤管理',
    push_setting_admin: '推送设置',
    push_setting: '推送设置',
    attendance_group_admin: '考勤组管理',
    group_administrators: '考勤组管理员',
    attendance_setting: '考勤设置',
    person_attendance_report: '个人考勤统计',
    attendance_record_detail: '考勤记录',
    attendance_record: '考勤明细',
    miss_card_record: '缺卡记录',
    access_control: '门禁管理',
    access_control_setting: '门禁设置',
    through_statistics: '通行统计',
    through_record: '通行记录',
    reservation_management: '订餐管理',
    reservation_report: '订餐明细表',
    department_report_collect: '科室订餐汇总',
    user_recharge_refund_summary: '用户充值/退费汇总',
    qrcode_template: '地址码模板',
    on_scene: '堂食设置',
    table_admin: '包厢/桌台',
    deliver_report: '配送汇总表',
    board_admin: '看板管理',
    large_data_screen: '数据大屏',
    third_reconciliation: '第三方对账表',
    third_party_reconciliation_summary: '第三方对账汇总表',
    third_party_reconciliation: '第三方对账明细表',
    abnormal_third_party_reconciliation: '第三方对账异常订单',
    refund_pending_order: '待退费订单',
    bankfinancialStatement: '银行财务报表',
    settlement_details: '结算明细表',
    account_billing_details: '账户结账明细',
    bank_flow_details: '交易流水明细',
    coupon: '优惠券',
    coupon_admin: '优惠券管理',
    coupon_detail: '优惠券详情',
    coupon_detail_list: '优惠券明细',
    // discount_coupon_statistics: '优惠券统计',
    marketing: '营销活动',
    banner_setting: '移动端banner图',
    banner: 'banner',
    mobile_popup: '弹窗',
    module_admin: '移动端管理',
    public_information: '公示信息',
    to_do_list: '待办事项',
    fund_supervision: '资金监管',
    fund_upload: '资金上传',
    fund_approval: '资金审批',
    income_and_expenditure_detail: '收支明细表',
    subsidy_receive_detail: '补贴领取明细',
    import_commodity_image: '批量导入图片',
    nutrition_rules_core: '营养规则中心',
    nutrition_guidance_rules: '营养指导规则',
    modify_guidance_rules: '修改营养指导规则',
    label_admin: '标签管理',
    food_label: '菜品标签',
    ingredients_label: '食材标签',
    user_label: '用户标签',
    device_cloud_print_admin: '云打印管理',
    print_admin: '打印管理',
    printer_settings: '打印设置',
    receipt_list: '开票记录',
    consume_machine_setting: 'AI消费机设置',
    super_merchant_banner: '商户banner图',
    super_merchant_channel: '渠道管理',
    channel_statistics: '渠道数据统计',
    agreement_list: '协议管理',
    add_agreement: '添加/编辑协议',
    agreement_record: '协议记录',
    service_admin: '手续费管理',
    recharge_deduction_service: '手续费规则',
    add_service_rule_consume: '扣款手续费规则',
    add_service_rule_charge: '充值手续费规则',
    face_traceback: '人脸查询',
    face_traceback_detail: '人脸查询详情',
    import_ingredient_image: '导入食材图片',
    member_center: '会员中心',
    member_list: '会员列表',
    member_detail: '会员详情',
    member_level: '会员等级',
    member_label: '会员标签',
    member_permission: '会员权限管理',
    member_charge_rule: '会员收费规则',
    member_receive_record: '会员领取记录',
    messages_push: '短信推送',
    recharge_rules: '充值规则',
    messages_admin: '推送管理',
    messages_setting: '推送配置',
    bank_merchant: '农行二级商户管理',
    bank_merchant_detail: '二级商户',
    diet_manage: '饮食推荐管理',
    menu_plan_list: '推荐食谱',
    three_meal_list: '三餐推荐',
    leave_message: '查看留言',
    super_device_management: '设备管理',
    super_device_version: '版本推送管理',
    super_device_version_list: '管理更新',
    add_super_device_version: '新建',
    ai_retention_instrument: '菜品留样记录',
    ai_retention_instrument_detail: '记录详情',
    car_management: '车辆管理',
    car_binding: '车辆绑定',
    traffic_records: '通行记录',
    traffic_orders: '通行订单',
    super_article_category: '文章分类',
    report_verify_handle: '对账处理',
    approve_rules_list: '审批规则',
    approve_rules: '规则',
    meal_apply: '就餐申请',
    meal_apply_admin: '就餐申请规则',
    meal_approve_list: '访客就餐审批',
    meal_apply_order: '访客餐订单',
    meal_apply_total: '访客餐汇总表',
    meal_apply_prepare: '访客餐备餐表',
    meal_order_detail: '订单详情',
    import_recipes_separate: '导入菜品',
    person_meal_report: '人员就餐统计报表',
    face_synchronization_history: '设备同步记录',
    device_cloud_print: '云打印管理',
    alipay_enterprise_code_rles: '支付宝企业码规则',
    add_alipay_enterprise_code_rules: '额度规则',
    alipay_report: '支付宝报表',
    alipay_enterprise_code_order: '额度使用明细表',
    subsidy_clear_detail: '补贴清零明细表',
    consumption_auto_selling: '售货柜订单',
    nutritional_analysis: '营养分析',
    disease_admin: '疾病管理',
    auto_label: '自动化标签',
    store_admin: '商店管理',
    store_goods_admin: '商品管理',
    stock_goods_stock: '商品库存',
    stock_goods_sales: '商品销量',
    goods_stock_details: '商品出入库明细',
    member_exclusive_rights: '独享权益',
    member_exclusive_setting: '功能配置',
    sms_call_record: '短信调用记录',
    sms_manager: '短信管理',
    member_charge_rule_detail: '查看会员收费规则',
    member_permission_manager: '会员权限管理',
    member_key_manager: '键值管理',
    promotional_picture_setting_list: '推广图设置列表',
    promotional_picture_setting: '推广图设置',
    charge_management: '收费管理',
    charging_rules: '收费规则',
    charging_trail: '收费线索',
    charge_order: '收费订单',
    version_configuration: '版本配置',
    recharge_withdraw_order: '提现审批',
    recharge_refund_order: '充值退款审批',
    consumption_summary_report: '消费点汇总表',
    super_evaluation_management: '评价管理',
    sms_management: '短信管理',
    sms_setting: '短信设置',
    guoxun_sms_management: '国讯短信模版管理',
    sms_template: '模板管理',
    user_card_operation_history: '卡操作历史',
    user_banding_water: '商城水控用户绑定',
    dishes_taken_ranking: '菜品取用量排行',
    points_admin: '积分管理',
    points_order: '积分订单',
    points_commodity: '积分商品',
    points_task: '积分任务',
    ai_settlement_setting: '结算台设置',
    nutrition_orders: '营养订单',
    supervise_fund_management: '监管平台',
    jimu_statement: "积木报表",
    report_application: "报表应用",
    supervision_channel: '监管渠道',
    customer_bind_user: '农行客户号绑定',
    auto_register_approve: '自注册审批',
    consumption_summary: '消费汇总表',
    recharge_summary: '充值汇总表',
    download_center: '下载中心',
    download_center_detail: '下载详情',
    recipe_template_admin: '菜谱模板管理',
    dish_ratings_list: '菜品评分排行',
    ration_recipe: '带量食谱',
    secondary_account_details: '二级缴费账户明细表',
    secondary_transaction_flow_details: '二级缴费交易流水明细表',
    inventory: '进销存',
    warehouse_admin: '仓库管理',
    inventory_unit_admin: '单位管理',
    inventory_management: '库存管理',
    Inventory_flow: '库存流水',
    add_warehousing_entry: '入库单',
    Add_outbound_order: '出库单',
    inquiry_detail: '询价单详情',
    preview_recipe: '预览菜谱',
    supplier_offer_list: '报价',
    modify_supplier_offer: '编辑报价',
    procure_list: '采购',
    add_procure: '新增采购单',
    procure_detail: '采购单详情',
    purchase_order_list: '采购单',
    modify_purchase_order: '采购单',
    intelligent_food_setting: '智能菜品设置',
    inventory_purchase_approval: '审批管理',
    inventory_purchase_detail: '采购详情',
    inventory_dishes_praise_ranking: '菜品好评排行',
    inventory_meal_scale_report: '餐标报表',
    inventory_inbound_outbound_reports: '出入库报表',
    supplier_management: '供应商信息管理',
    information_management: '信息管理',
    supplier: '供应商',
    related_materials: '关联物资',
    on_loan_order_detail: '借调详情',
    device_warehouse_setting: '入库称设置',
    inventory_stock_management: '盘点管理',
    inventory_stock: '盘点',
    inventory_stock_detail: '盘点详情',
    inbound_order: '入库单',
    warehousing: '入库',
    inbound_order_detail: '入库单详情',
    material_warehouse: '物资库',
    pulchase_list_report: '采购清单明细',
    outbound_order: '出库单',
    outbound_order_detail: '出库单详情',
    pulchase_detail: '采购单详情',
    inquiry_order: '询价单',
    inbound_and_outbound_detail: '出入库明细',
    inbound_and_outbound_report: '出入库报表',
    transfer_order: '调拨单',
    transfer_order_return: '调拨单归还',
    transfer_order_detail: '调拨单详情',
    returns_management: '退货管理',
    returns_management_detail: '退货管理详情',
    approval_management: '审批管理',
    approval_management_list: '审批列表',
    approval_management_detail: '审批查看',
    approval_settings: '审批设置',
    survey_admin: '调查问卷',
    survey_detail: '查看反馈',
    survey_data_total: '数据汇总',
    kitchen_info: '后厨人员信息公示',
    subscription_order_list: '申购单列表',
    subscription_order: '申购单',
    subscription_order_detail: '申购单详情',
    subscription_record_list: '申购记录',
    subscription_record_detail: '申购记录详情',
    bank_school_manager: '农行校园管理',
    bank_school_user: '农行校园用户',
    campus_management: '校园管理',
    architecture: '校园架构',
    canteen_feedback: '食堂反馈',
    discount_limit: '优惠限制',
    discount_limit_form: '优惠限制',
    supplier_management_info: '供应商信息',
    modify_supplier_management: '编辑供应商',
    history_record: '历史记录',
    meal_report_summary: '报餐点餐汇总',
    device_cost_summary: '设备消费汇总表',
    material_category: '物资分类',
    document_management: '单据管理',
    canteen_management: '食堂管理',
    canteen_info: '食堂信息',
    democratic_feedback: '民主反馈',
    survey_detail_data: '数据明细',
    fund_management: '财务监管',
    financial_application: '财务审批',
    financial_approval: '拨款申请',
    AI_forewarning: 'AI预警',
    operation_warning: '智能预警',
    food_safety_management: '食安监管',
    food_safety_roots: "食安溯源",
    scheduling_management: '排班管理',
    morning_check_log: '晨检记录',
    pest_control: '有害生物防制',
    sample_record: '留样记录',
    satisfaction_setting: '满意度设置',
    satisfaction_total: '满意度统计',
    satisfaction_detail: '满意度明细',
    scale_recognition_statistics: '入库秤识别率统计',
    property_admin: '资产管理',
    property_info: '资产信息',
    debt_info: '负债信息',
    accompanying_meal_record: '陪餐记录',
    device_group: '设备组管理',
    classification_of_inbound_and_outbound_statistics: '分类出入库统计',
    daily_inspection: '每日巡查',
    material_weight_modification_statistics: '入库物资修改记录',
    management_ledger: '管理台账',
    fengxian_management_ledger: '风险管控清单管理台账',
    congye_management_ledger: '从业人员健康管理台账',
    shitang_shebei_management_ledger: '食堂设备定期维护清洗管理台账',
    chufang_chouyou_management_ledger: '厨房抽油烟机清洗管理台账',
    shitang_daoju_management_ledger: '学校食堂刀具消毒明细管理台账',
    shitang_caigou_management_ledger: '食堂采购进货粘贴式管理台账',
    danlei_qingxi_management_ledger: '蛋类清洗管理台账',
    shipin_tianjiaji_management_ledger: '食品添加剂使用管理台账',
    guominyuan_management_ledger: '过敏原使用管理台账',
    bianzhi_shipin_management_ledger: '变质、超过保质期食品销毁管理台账',
    xuncha_jilu_management_ledger: '巡查记录管理台账',
    shipin_zhongxin_wendu_management_ledger: '食品中心温度探测管理台账',
    shipin_liuyang_management_ledger: '食品留样管理台账',
    peicanjian_management_ledger: '配餐间（专间）紫外线灯管空气消毒管理台账',
    chufang_canting_xiaodu_management_ledger: '厨房餐厅消毒管理台账',
    chufang_gongju_xiaodu_management_ledger: '厨房工具消毒管理台账',
    canyinju_xiaodu_management_ledger: '餐炊具消毒管理台账',
    xuexiao_shitang_peican_management_ledger: '学校食堂陪餐管理台账',
    canshu_laji_management_ledger: '餐厨垃圾处理管理台账',
    youhang_shengwu_management_ledger: '有害生物防治管理台账',
    congye_renyuan_peixun_management_ledger: '从业人员食品安全培训管理台账',
    xuexiao_shipin_anquan_management_ledger: '学校每日食品安全检查表',
    xuexiao_shitang_pingjia_management_ledger: '学校食品安全负责人定期对食堂经营状况综合评价记录管理台账',
    xuexiao_huiyi_jiyao_management_ledger: '学校每月食品安全调度会议纪要-月调度管理台账',
    jiti_yongcan_management_ledger: '集体用餐配送单位配送记录管理台账',
    consumption_rule_operation_record_list: '规则变更记录',
    meal_allowance_used_summary_list: '餐补使用汇总表',
    meal_allowance_used_details_list: '餐补使用明细表',
    group_meal_company_list: '团餐公司',
    catering_company_list: '配餐公司'
  },
  navHeader: {
    dashboard: '首页'
  },
  navTab: {},
  login: {
    title: '系统登录',
    loginBtn: '登录',
    forgetPassword: '忘记密码',
    rememberPassword: '记住密码'
  },
  form: {
    confirm_btn: '确 定',
    add_btn: '添 加',
    cancel_btn: '取 消',
    submit: '提交',
    superior_organization: '上级组织',
    add_organization: '添加组织架构',
    edit_organization: '修改组织架构',
    role_add_name: '角色名称',
    role_select_organization: '选择组织架构',
    push_range: '推送范围',
    notice_title: '公告标题',
    notice_type: '公告类型',
    notice_content: '公告正文',
    upload_file: '公告附件',
    send_time: '发布时间',
    department: '部门',
    add_department: '添加部门',
    modify_department: '修改部门',
    superior_department: '上级部门',
    select_department: '选择部门'
  },
  placeholder: {
    account: '请输入账号或手机号',
    password: '请输入密码',
    phone: '请输入手机号',
    code: '请输入验证码',
    oldPassword: '请输入旧密码',
    newPassword: '密码长度8~20位，英文加数字',
    changeNewPassword: '请输入新密码',
    checkPassword: '请再次输入新密码',
    name: '请输入姓名',
    newPhone: '请输入新手机号码',
    organization_name: '请输入组织架构名称',
    role_tree_search: '请输入要搜索的组织结构名称',
    role_search: '请输入要搜索的角色名称',
    role_name_empty: '请输入角色名称',
    role_password_empty: '请输入密码',
    role_organization_empty: '请选择组织架构',
    account_search_name: '请输入要搜索的账号',
    account_search_role: '请选择角色',
    account_search_user_name: '请输入要搜索的用户名',
    account_search_status: '请选择状态',
    account_name_empty: '请输入账号',
    account_status_empty: '请选择状态',
    creator: '请输入创建人',
    push_company_id: '请选择接收商户',
    notice_title: '请输入公告标题',
    notice_type: '请输入公告类型',
    notice_content: '请输入公告内容',
    date: '请选择时间',
    notice_type_title: '请输入公告类型 / 标题'
  },
  dialog: {
    confirm_btn: '确 定',
    add_btn: '添 加',
    cancel_btn: '取 消',
    add_title: '新建',
    edit_title: '编辑',
    account_title: '查看账号',
    close_btn: '关 闭',
    tips_title: '提示',
    notice_dialog: '查看公告'
  },
  table: {
    // 公共字段
    create_time: '创建时间',
    operate: '操作',
    edit: '编辑',
    delete: '删除',
    status: '状态',
    // 组织架构独享
    organization_name: '组织架构名称',
    next_organization_name: '下级组织架构名称',
    organization_role_num: '角色数',
    organization_account_num: '账号数',
    organization_add: '增加组织架构',
    account_username: '账号',
    account_member_name: '用户名称',
    role_name: '角色名称',
    role_num: '角色数',
    account_num: '账号数',
    role_account_num: '关联账号数',
    account_organization: '所属组织架构',
    role_setting_btn: '权限设置',
    role_show_account_btn: '查看账号',
    account: '账号',
    l_address: '所属地区',
    // 公告
    post_time: '发送时间',
    notice_type: '公告类型',
    notice_title: '公告标题',
    post_status: '发送状态',
    receiving: '收件人',
    read_num: '阅读统计',
    creator: '创建人',
    department: '部门',
    account_department: '所属部门',
    mobile: '手机号'
  },
  message: {
    delete: '删除',
    loading: '加载中...',
    success: '成功',
    add_success: '添加成功',
    delete_success: '删除成功',
    role_select_empty: '请先选择要删除的数据！',
    role_delete_select: '是否删除所选角色',
    account_delete_select: '是否删除所选账号'
  },
  search: {
    all: '全部',
    btn: '搜索',
    role_add: '新增角色',
    export: '导出EXCEL',
    multi_del: '批量删除',
    account: '账号',
    account_user_name: '用户名称',
    status: '状态',
    account_add: '新增账号',
    operation_type: '操作类型',
    operation_staff: '操作人员',
    post_time: '发送时间',
    creator: '创建人',
    notice_type_title: '类型 / 标题'
  },
  button: {
    add_notice: '新建公告',
    upload_file: '点击上传',
    add: '新增',
    export: '导出'
  },
  error: {
    send_time: '发送时间不能早于当前时间'
  }
}
