<template>
  <!-- 添加/编辑 -->
  <custom-drawer
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :wrapperClosable="false"
    :fixedFooter="true"
    :size="760"
    destroy-on-close
    v-bind="$attrs"
    v-on="$listeners"
    confirm-text="确定"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <el-form
      :model="formData"
      ref="formData"
      :rules="formDataRules"
      label-width="100px"
      class="dialog-form m-t-20"
      v-loading="isLoading"
    >
      <el-form-item label="物资名称" prop="materials_id">
        <el-select
          v-model="formData.materials_id"
          placeholder="请选择"
          filterable
          class="ps-select w-260"
          @change="changeMateriaHandle"
          :disabled="type === 'modify'"
        >
          <el-option
            v-for="item in materialList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="单位" prop="unit">
        {{ formData.unitName }}
      </el-form-item> -->
      <el-form-item label="物资分类">
        {{ formData.category }}
      </el-form-item>
      <el-form-item label="分类属性">
        {{ formData.categoryAttribute }}
      </el-form-item>
      <!-- <el-form-item label="重量" prop="weight">
        <el-input v-model="formData.weight" :maxlength="20" class="w-220"></el-input>
        <span class="m-l-20">kg</span>
      </el-form-item>
      <el-form-item label="参考单价" prop="price">
        <el-input v-model="formData.price" :maxlength="20" class="w-260"></el-input>
      </el-form-item> -->
      <el-form-item label="最小单位" prop="">
        {{ formData.limitUnitAlias }}
      </el-form-item>
      <el-form-item label-width="30px" label="" prop="" class="">
        <div class="m-b-6">规格设置</div>
        <el-form-item label-width="0" label="" v-for="(specs, k) in formData.specificationsList" :key="k" prop="" :rules="formDataRules.isRepeat" class="specifications">
          <el-table
            :data="specs.children"
            ref="tableData"
            style="width: 100%"
            size="small"
            stripe
            header-row-class-name="ps-table-header-row"
            row-key="id"
            border
            :span-method="mergeSpanHandle"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item">
              <template #specs="{ row, index }">
                <el-form-item label="" label-width="0" :show-message="false" :prop="'specificationsList.'+k+'.children.'+index+'.unit'" :rules="formDataRules.unit" :class="{'is-error': row.isRepeat }">
                  <el-select
                    v-model="row.unit"
                    placeholder="请选择"
                    filterable
                    class="ps-select w-100 m-r-10"
                    size="small"
                    :disabled="index === 0"
                  >
                    <el-option
                      v-for="item in unitList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template #count="{ row, index }">
                <el-form-item label="" label-width="0" :show-message="false" :prop="'specificationsList.'+k+'.children.'+index+'.count'" :rules="formDataRules.count" :class="{'is-error': row.isRepeat }">
                  <el-input v-model="row.count" :maxlength="20" :disabled="index === 0" size="small" class="inline-block w-80"></el-input>
                  <div class="specs-unit ellipsis m-l-4" style="max-width: 70px;">{{ row.netContentAlias }}</div>
                </el-form-item>
              </template>
              <template #price="{ row, index }">
                <el-form-item label="" label-width="0" :show-message="false" :prop="'specificationsList.'+k+'.children.'+index+'.price'" :rules="formDataRules.price" :class="{'is-error': row.isRepeat }">
                  <span class="m-r-4">￥</span>
                  <el-input v-model="row.price" :maxlength="20" size="small" class="inline-block w-80"></el-input>
                </el-form-item>
              </template>
              <template #operation="{ row, index }">
                <div class="tool-box inline-block">
                  <i class="tool-icon el-icon-circle-plus" @click="clickToolIcon('add', index, specs, row)"></i>
                  <i class="tool-icon el-icon-remove" v-if="index > 0 && specs.children.length > 1" @click="clickToolIcon('remove', index, specs, row)"></i>
                </div>
              </template>
            </table-column>
          </el-table>
          <div v-if="specs.isRepeat" class="fixed-tip red">数据重复!</div>
        </el-form-item>
      </el-form-item>
    </el-form>
  </custom-drawer>
  <!-- end -->
</template>

<script>
import { positiveMoney } from '@/utils/validata'
import { times, divide } from '@/utils'
export default {
  inheritAttrs: false,
  name: 'addMaterialWarehouseDialog',
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '关联物资'
    },
    width: {
      type: String,
      default: '460px'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    },
    confirm: Function
  },
  // mixins: [activatedLoadData],
  data() {
    let validataPrice = (rule, value, callback) => {
      if (value) {
        if (!positiveMoney(value) || value === '0') {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      }
    }
    let validataWeight = (rule, value, callback) => {
      if (value) {
        if (!positiveMoney(value)) {
          callback(new Error('仅支持数字，且最多保留2位小数'))
        } else {
          callback()
        }
      }
    }
    // 校验规格是否重复
    const validateIsRepeat = (rule, value, callback) => {
      if (value) {
        callback(new Error('数据重复！'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      formData: {
        id: '',
        materials_id: '',
        unit: '',
        unitName: '',
        weight: '',
        price: '',
        category: '',
        categoryAttribute: '',
        limitUnitAlias: '',
        specificationsList: [ // 规格，children有id表示是添加过的规格数据，没有则是新增的
          // {
          //   limiUnit: '瓶',
          //   xxx: '瓶*330m',
          //   isRepeat: false,
          //   children: [
          //     { limiUnit: '瓶', unit: '', count: '', price: '', netContentAlias: '瓶*330m', isRepeat: false },
          //     { limiUnit: '瓶', unit: '', count: '', price: '', netContentAlias: '瓶*330m', isRepeat: false }
          //   ]
          // }
        ]
      },
      unitList: [],
      materialList: [], // 物资列表
      formDataRules: {
        materials_id: [{ required: true, message: '请选择物资', trigger: 'change' }],
        category: [{ required: true, message: '请选择物资分类', trigger: 'change' }],
        // unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        weight: [
          { required: true, message: '请选择输入重量', trigger: 'change' },
          { validator: validataWeight, trigger: 'change' }
        ],
        unit: [{ required: true, message: '请选择', trigger: 'change' }],
        price: [
          { required: true, message: '请输入单价', trigger: 'change' },
          { validator: validataPrice, trigger: 'change' }
        ],
        count: [
          { required: true, message: '请输入数量', trigger: 'change' },
          { validator: validataPrice, trigger: 'change' }
        ],
        isRepeat: [{ validator: validateIsRepeat, trigger: 'change' }]
      },
      materialCategoryList: [],
      categoryAttributeList: [],
      tableSettings: [
        { label: '最小单位', key: 'limiUnit' },
        { label: '规格', key: 'specs', type: 'slot', slotName: 'specs', minWidth: '120px' },
        { label: '数量', key: 'count', type: 'slot', slotName: 'count', minWidth: '160px' },
        { label: '单价', key: 'price', type: 'slot', slotName: 'price', minWidth: '120px' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      deleteSpecsList: [] // 删除的规格列表
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        if (this.materialList.length === 0) {
          this.getMaterialList()
        }
        this.initLoad()
      }
    },
    'formData.specificationsList': {
      handler(val) {
        console.log(23211321, val)
        if (val && val.length > 0) {
          val.forEach(v => {
            // 数据重复校验，当最小单位+规格+数量+单价重复时
            // 由于数量跟单价都支持输入小数点后两位，所以需要对这两个进行.toFixed(2)处理
            let isRepeat = false
            let allValObj = {}
            v.children.forEach(item => {
              if (item.unit && item.count && item.price) {
                let key = `${item.unit}-${parseFloat(item.count).toFixed(2)}-${parseFloat(item.price).toFixed(2)}`
                if (allValObj[key]) {
                  allValObj[key] += 1
                  isRepeat = true
                } else {
                  allValObj[key] = 1
                }
              }
            })
            this.$set(v, 'isRepeat', isRepeat)
            v.children.forEach(item => {
              if (item.unit && item.count && item.price) {
                let key = `${item.unit}-${parseFloat(item.count).toFixed(2)}-${parseFloat(item.price).toFixed(2)}`
                if (allValObj[key] && allValObj[key] > 1) {
                  this.$set(item, 'isRepeat', true)
                } else {
                  this.$set(item, 'isRepeat', false)
                }
              } else {
                this.$set(item, 'isRepeat', false)
              }
            })
          })
        }
      },
      deep: true
    }
  },
  created() {
  //   this.getIngredientNameList()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getUnitManagementList()
      if (this.type === 'modify') {
        this.deleteSpecsList = []
        this.formData.materials_id = this.infoData.materials_id
        this.formData.name = this.infoData.name
        this.formData.unitName = this.infoData.unit_name
        this.formData.weight = this.infoData.weight
        this.formData.price = (this.infoData.ref_unit_price / 100).toFixed(1)
        this.formData.category = this.infoData.materail_classification_name
        this.formData.categoryAttribute = this.infoData.attribute
        // 最小单位显示
        let limitUnitObj = {}
        this.formData.limitUnitAlias = this.infoData.limit_unit_management.reduce((prev, current) => {
          if (current.status !== 'delete') {
            limitUnitObj[`${current.id}`] = {
              initialize: false,
              ...current
            }
            if (prev) {
              return `${prev}、${current.unit_management_name}*${current.net_content}${current.net_content_unit}`
            } else {
              return `${current.unit_management_name}*${current.net_content}${current.net_content_unit}`
            }
          } else {
            return prev
          }
        }, '')
        // 规格数据还原
        let specificationsObj = {}
        this.infoData.material_specification.forEach(v => {
          if (v.status !== 'delete') {
            if (limitUnitObj[`${v.limit_unit_id}`]) {
              // 已经初始化过的最小规格的数据
              limitUnitObj[`${v.limit_unit_id}`].initialize = true
            }
            if (specificationsObj[`${v.limit_unit_id}`]) {
              specificationsObj[`${v.limit_unit_id}`].children.push({ id: v.id, limiUnit: v.limit_unit_name, unit: v.unit_management_id, count: v.count, price: divide(v.unit_price), netContentAlias: `${v.limit_unit_name}*${v.net_content}${v.net_content_unit}`, isRepeat: false })
            } else {
              specificationsObj[`${v.limit_unit_id}`] = {
                id: v.limit_unit_id,
                limiUnit: v.limit_unit_name,
                limiUnitId: '',
                // net_content: v.net_content,
                // net_content_unit: v.net_content_unit,
                // net_content_unit_alias: v.net_content_unit_alias,
                // unit_type: v.unit_type,
                // unit_type_alias: v.unit_type_alias,
                netContentAlias: `${v.limit_unit_name}*${v.net_content}${v.net_content_unit}`,
                isRepeat: false,
                children: [ // id用于删除的
                  { id: v.id, limiUnit: v.limit_unit_name, unit: v.unit_management_id, count: v.count, price: divide(v.unit_price), netContentAlias: `${v.limit_unit_name}*${v.net_content}${v.net_content_unit}`, isRepeat: false }
                ]
              }
            }
          }
        })
        Object.values(limitUnitObj).forEach(v => {
          if (!v.initialize) {
            specificationsObj[`${v.id}`] = {
              id: v.id,
              limiUnit: v.unit_management_name,
              limiUnitId: v.unit_management,
              // net_content: v.net_content,
              // net_content_unit: v.net_content_unit,
              // net_content_unit_alias: v.net_content_unit_alias,
              // unit_type: v.unit_type,
              // unit_type_alias: v.unit_type_alias,
              netContentAlias: `${v.unit_management_name}*${v.net_content}${v.net_content_unit}`,
              children: [
                { limiUnit: v.unit_management_name, unit: '', count: '', price: '', netContentAlias: `${v.unit_management_name}*${v.net_content}${v.net_content_unit}`, isRepeat: false }
              ]
            }
          }
        })
        this.formData.specificationsList = Object.values(specificationsObj)
      }
    },
    checkFormData() {
      let pass = true
      if (this.formData.specificationsList.some(v => v.isRepeat)) {
        pass = false
        this.$message.error('请检查规格是否重复!')
      }

      return pass
    },
    // 确认事件
    clickConfirmHandle() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          if (!this.checkFormData()) return
          if (this.isLoading) return
          let params = {
            id: +this.$route.query.id,
            materials_id: this.formData.materials_id
            // ref_unit_price: times(this.formData.price),
            // weight: this.formData.weight
          }
          if (this.formData.specificationsList.length > 0) {
            let addList = []
            let modifyList = []
            this.formData.specificationsList.forEach(v => {
              if (v.children.length > 0) {
                v.children.forEach(item => {
                  if (item.unit && item.count && item.price) {
                    if (item.id) {
                      modifyList.push({
                        id: item.id,
                        limit_unit_management_id: v.id,
                        unit_management_id: item.unit,
                        count: item.count,
                        unit_price: times(item.price)
                      })
                    } else {
                      addList.push({
                        limit_unit_management_id: v.id,
                        unit_management_id: item.unit,
                        count: item.count,
                        unit_price: times(item.price)
                      })
                    }
                  }
                })
              }
            })
            if (addList.length > 0) {
              params.add_supplier_specification = addList
            }
            if (modifyList.length > 0) {
              params.modify_supplier_specification = modifyList
            }
            if (this.deleteSpecsList.length > 0) {
              params.delete_supplier_specification = this.deleteSpecsList
            }
          }
          // params.supplier_specification
          this.isLoading = true
          this.sendFormData(params)
        } else {
        }
      })
    },
    // 发送数据
    async sendFormData(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpSupplierManageContactMaterialsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('成功')
        this.confirm && this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 取消事件
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    // 关闭弹窗
    handlerClose(e) {
      this.formData = {
        id: '',
        materials_id: '',
        unit: '',
        unitName: '',
        weight: '',
        price: '',
        category: '',
        categoryAttribute: '',
        limitUnitAlias: '',
        specificationsList: []
      }
      this.deleteSpecsList = []
      if (this.$refs.formData) {
        this.$refs.formData.resetFields()
      }
      this.isLoading = false
      // this.visible = false
      // this.$emit('close')
    },
    // 获取物资列表数据
    async getMaterialList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpMaterialsListPost({
        page: 1,
        page_size: 999999
      }))
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.materialList = res.data.results
      } else {
        // this.$message.error(res.msg)
      }
    },
    changeMateriaHandle(e) {
      let current = this.materialList.find(v => v.id === this.formData.materials_id)
      this.formData.unit = current.unit_id
      this.formData.unitName = current.unit_name
      this.formData.category = current.materail_classification_name
      this.formData.categoryAttribute = current.attribute

      // 存在最小单位则需要初始化基础规格数据
      if (current.limit_unit_management && current.limit_unit_management.length > 0) {
        this.formData.specificationsList = []
        let limitUnitAlias = ''
        current.limit_unit_management.forEach(v => {
          if (v.status !== 'delete') { // 先手动过滤下，目前后端没过滤已删除的数据
            limitUnitAlias = limitUnitAlias ? limitUnitAlias + `，${v.unit_management_name}*${v.net_content}${v.net_content_unit}` : `${v.unit_management_name}*${v.net_content}${v.net_content_unit}`
            let defaultUnitId = this.unitList.find(item => item.name === v.unit_management_name).id
            this.formData.specificationsList.push({
              id: v.id,
              limiUnit: v.unit_management_name,
              limiUnitId: v.unit_management,
              // net_content: v.net_content,
              // net_content_unit: v.net_content_unit,
              // net_content_unit_alias: v.net_content_unit_alias,
              // unit_type: v.unit_type,
              // unit_type_alias: v.unit_type_alias,
              netContentAlias: `${v.unit_management_name}*${v.net_content}${v.net_content_unit}`,
              children: [
                { limiUnit: v.unit_management_name, unit: defaultUnitId, count: 1, price: '', netContentAlias: `${v.unit_management_name}*${v.net_content}${v.net_content_unit}`, isRepeat: false }
              ]
            })
          }
        })
        this.formData.limitUnitAlias = limitUnitAlias
      } else {
        this.formData.specificationsList = []
        this.formData.limitUnitAlias = ''
      }
    },
    // 获取单位列表数据
    async getUnitManagementList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpUnitManagementListPost({
        page: 1,
        page_size: 999999
        // organization_id: this.$store.getters.organization
      }))
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.unitList = res.data.results
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 合并单元格，只合并第一列
    mergeSpanHandle({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0 && columnIndex === 0) {
        return [999, 1];
      }
      if (rowIndex > 0 && columnIndex === 0) {
        return [0, 0];
      }
    },
    clickToolIcon(type, index, specs, row) {
      if (type === 'add') {
        specs.children.push({ limiUnit: specs.limiUnit, unit: '', count: '', price: '', netContentAlias: specs.netContentAlias })
      }
      if (type === 'remove') {
        if (row.id) {
          this.deleteSpecsList.push(row.id)
        }
        specs.children.splice(index, 1)
      }
    }
  }
}
</script>

<style lang="scss">
.dialog-form {
  .m-l-4 {
    margin-left: 4px;
  }
  .m-r-4 {
    margin-right: 4px;
  }
  .w-220{
    width: 220px;
  }
  .w-260 {
    width: 260px;
  }
  .w-80 {
    width: 80px;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .el-upload__tip {
    color: red;
  }
  .ps-table-header-row {
    line-height: 31px;
  }
  .specifications {
    margin-right: 20px;
    margin-bottom: 22px;
    padding: 12px 14px;
    border-radius: 8px;
    background-color: #D7D7D7;
    .tool-icon {
      vertical-align: middle;
      cursor: pointer;
      font-size: 16px;
      &+.tool-icon {
        margin-left: 6px;
      }
      &:hover{
        color: #ff9b45;
      }
    }
    .specs-unit {
      display: inline-block;
      vertical-align: middle;
    }
    .el-form-item__error {
      top: 106%;
    }
    .fixed-tip {
      position: absolute;
      bottom: -28px;
      line-height: 1;
    }
  }
}
</style>
