<template>
  <div class="container-wrapper">
    <div class="food-inspection">
      <h2 class="table-title">
        <span>
          <el-select v-model="selectedYear" placeholder="年份" size="small" style="width: 100px">
            <el-option v-for="year in years" :key="year" :label="`${year}年`" :value="year"></el-option>
          </el-select>
          <el-select v-model="selectedMonth" placeholder="月份" size="small" style="width: 80px">
            <el-option v-for="month in 12" :key="month" :label="`${month}月`" :value="month"></el-option>
          </el-select>
          食品出品质量安全及中心温度检查表
        </span>
      </h2>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column label="日期" prop="date" width="150" align="center"></el-table-column>
        <el-table-column label="餐次" width="220" align="center">
          <template slot-scope="scope">
            <el-checkbox-group v-model="scope.row.mealTypes" size="mini">
              <el-checkbox label="早餐">早餐</el-checkbox>
              <el-checkbox label="中餐">中餐</el-checkbox>
              <el-checkbox label="晚餐">晚餐</el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>
        <el-table-column label="菜品名称" prop="dishName"  align="center"></el-table-column>
        <el-table-column label="色泽" prop="color" width="150" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.color" size="mini"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="外观" prop="appearance" width="150" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.appearance" size="mini"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="口感" prop="taste" width="150" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.taste" size="mini"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="中心温度" prop="temperature" width="150" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.temperature" size="mini"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="判定" align="center">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.result" size="mini">
              <el-radio label="符合">符合</el-radio>
              <el-radio label="不符合">不符合</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="检查人" prop="inspector" width="150" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.inspector" size="mini"></el-input>
          </template>
        </el-table-column>
      </el-table>

      <div class="note-section">
        <div class="note-title">说明：</div>
        <div class="note-content">
          <p>1. 色泽: 评价菜肴是否具色泽正常"正常（着勾）"√"，不能有此菜肴不应有的颜色，焦黑等情况（X）；</p>
          <p>2. 外观: 评价此菜肴是否具备该出品应有的形态正常（√），不正常（X）；</p>
          <p>3. 口感: 评价此菜肴是否 香、脆、嫩、酥等是否具备特色；</p>
          <p>4. 中心温度（CCP点）: 中心温度＞75℃, 在上述判定栏内, 符合打"√", 不符合打"×"。</p>
        </div>
      </div>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShipinZhongxinWenduManagementLedger',
  data() {
    // Generate current year and next 5 years
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 6 }, (_, i) => currentYear + i);

    // Generate sample data
    const generateData = () => {
      const data = [];

      for (let i = 1; i <= 26; i++) {
        data.push({
          id: i,
          date: `2023-06-${String(i).padStart(2, '0')}`,
          mealTypes: [],
          dishName: '',
          color: '',
          appearance: '',
          taste: '',
          temperature: '',
          result: '符合',
          inspector: ''
        });
      }
      return data;
    };

    return {
      tableData: generateData(),
      currentPage: 1,
      pageSize: 15,
      totalItems: 26,
      selectedYear: currentYear,
      selectedMonth: new Date().getMonth() + 1,
      years: years
    };
  },
  computed: {
    displayData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    },
    titleText() {
      return `${this.selectedYear}年${this.selectedMonth}月食品出品质量安全及中心温度检查表`;
    }
  },
  watch: {
    selectedYear(newVal) {
      console.log(`Year changed to: ${newVal}`);
      // Here you can implement logic to refresh data based on selected year
    },
    selectedMonth(newVal) {
      console.log(`Month changed to: ${newVal}`);
      // Here you can implement logic to refresh data based on selected month
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0'
      };
    },
    updateDates() {
      // This method can be used to update the dates based on selected year and month
      this.tableData.forEach((item, index) => {
        // Calculate days in month
        const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate();
        // Only update if day exists in selected month
        if (index + 1 <= daysInMonth) {
          item.date = `${this.selectedYear}-${String(this.selectedMonth).padStart(2, '0')}-${String(index + 1).padStart(2, '0')}`;
        }
      });
      this.totalItems = this.tableData.length;
    }
  }
};
</script>

<style lang="scss" scoped>
.food-inspection {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;

    .el-select {
      margin: 0 5px;
    }
  }

  ::v-deep .el-input__inner {
    padding: 0 5px;
    height: 28px;
    line-height: 28px;
    text-align: center;
  }

  ::v-deep .el-radio {
    margin-right: 5px;
    margin-left: 0;

    &.is-bordered {
      padding: 8px 15px 8px 10px;
      height: auto;
    }
  }

  ::v-deep .el-checkbox-group {
    display: flex;
    justify-content: space-around;

    .el-checkbox {
      margin-right: 5px;
      margin-left: 0;
    }
  }

  .note-section {
    margin-top: 20px;
    border: 1px solid #EBEEF5;
    padding: 15px;

    .note-title {
      font-weight: bold;
      margin-bottom: 10px;
    }

    .note-content {
      font-size: 14px;

      p {
        margin: 5px 0;
        line-height: 1.5;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
