<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        label-width="120px"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-button size="mini" @click="gotoExport" v-permission="['background_report_center.data_report.third_order_list_export']">导出Excel</el-button>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row" />
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { THIRD_RECONCILIATION } from './constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { deepClone } from '@/utils'

export default {
  name: 'ThirdReconciliation',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        // { label: '序号', key: 'index', type: 'index' },
        { label: '总单号', key: 'trade_no' },
        { label: '创建时间', key: 'create_time' },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        { label: '优惠金额', key: 'discount_fee', type: 'money' },
        { label: '动账金额', key: 'real_fee', type: 'money' },
        { label: '订单类型', key: 'order_type_alias' },
        { label: '第三方名称', key: 'third_name' },
        { label: '第三方订单', key: 'out_trade_no' },
        { label: '动账时间', key: 'pay_time' },
        { label: '第三方动账金额', key: 'third_fee', type: 'money' },
        { label: '对账状态', key: 'settle_status_alias' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页

      searchFormSetting: deepClone(THIRD_RECONCILIATION),
      collect: [ // 统计
        { key: 'real_fee', value: 0, label: '动账总金额:￥', type: 'money' },
        { key: 'third_fee', value: 0, label: '第三方动账总金额:￥', type: 'money' },
        { key: 'settle_success', value: 0, label: '已对账:', unit: "笔" },
        { key: 'settle_pending', value: 0, label: '未对账:', unit: "笔" },
        { key: 'receive_amount', value: 0, label: '实收金额:￥', type: 'money' },
        { key: 'settle_fail', value: 0, label: '对账失败:', unit: "笔" },
        {
          key: 'text',
          value: '实收金额=(消费+充值+缴费)-退款-提现',
          label: '',
          block: true
        }
      ],
      printType: 'ThirdReconciliation',
      isFirstSearch: true
    }
  },
  created () {
    this.initLoad(true)
  },
  mounted() {
    this.getSearchQuery()
    this.initPrintSetting()
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        // this.currentTableSetting = this.tableSetting
        this.getWithdrawList()
      }
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.getWithdrawList()
      this.isFirstSearch = true
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getWithdrawList()
        this.isFirstSearch = false
      }
    },
    // 获取搜索的数据
    async getSearchQuery() {
      const res = await this.$apis.apiBackgroundReportCenterSelectDataSelectListPost({
        request_report_path: 'background_report_center/data_report/third_order_list'
      })
      if (res.code === 0) {
        if (res.data && res.data.OrderType) {
          this.searchFormSetting.order_type.dataList = res.data.OrderType
        }
      } else {}
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getWithdrawList() {
      const params = this.formatQueryParams(this.searchFormSetting)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportThirdOrderListPost({
        ...params,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.result
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getWithdrawList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'ExportThirdReconciliation',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '第三方对账表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportThirdOrderListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
