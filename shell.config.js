const shell = require('shelljs')

// 配置参数
const options = {
  api_url: 'https://cashier-v4.debug.packertec.com/api/client_codegenerator/jscode', // 请求api的地址
  // api_url: 'http://192.168.51.63:8005/api/client_codegenerator/jscode', // 梓健本地
  write_path: 'src/api/modules/asyncapi.js' // 保存的地址
}

/*
* 返回一个对象
* 可以根据 code 值来判断当前命令是否执行成功
* code === 0 代表成功
* */

// 执行下载api文件，写入到src/api/modules/asyncapi.js文件中
function downloadApis() {
  return new Promise((resolve, reject) => {
    // -G 表示get请求
    // -o 小写指定目录文件
    // -d 表示参数
    // other...
    // curl -d xx=xx -d aa=aa -o ${file} ${url}
    const downCommand = `curl -G -d apps=booking,background_logistics,background_logistics_manage,client -d is_exclude=1 -o ${options.write_path} ${options.api_url}`
    // 异步回调
    shell.exec(downCommand, function(code, stdout, stderr) {
      // console.log('Exit code:', code);
      // console.log('Program output:', stdout);
      // console.log('Program stderr:', stderr);
      if (code === 0) {
        console.log('download api success !')
        resolve(code)
      } else {
        shell.exit(1);
        reject(code)
      }
    })
  })
}

// 格式化下
function formatApi() {
  return new Promise((resolve, reject) => {
    const formatCommand = `prettier --config --write ${options.write_path}`
    // 异步回调
    shell.exec(formatCommand, function(code, stdout, stderr) {
      if (code === 0) {
        console.log('format api success !')
        resolve(code)
      } else {
        shell.exit(1);
        reject(code)
      }
    })
  })
}

// 执行，先下载，再格式化
downloadApis().then(() => {
  // 给个延时
  setTimeout(() => {
    formatApi()
  }, 500)
})
