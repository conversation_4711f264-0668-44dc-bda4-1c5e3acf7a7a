import FileSaver from 'file-saver'
import XLSX from 'xlsx'

/**
 * @description 下载excel文件
 * @param {*} url
 */
export function downloadExcelUrl(url) {
  // window.open(this.templateUrl);
  let spliturl = url.split('/')
  let filsename = spliturl[spliturl.length - 1]
  FileSaver.saveAs(url, filsename)
}

/**
 * @description json数据转xlsx blob，但表
 * @param {*} json
 * @param {*} merges
 * @returns xlsxblob
 */
export function jsonToXlsxBlob(json, merges) {
  let wsname = 'Sheet1'
  let wb = XLSX.utils.book_new()
  let ws = XLSX.utils.aoa_to_sheet(json)
  XLSX.utils.book_append_sheet(wb, ws, wsname)
  if (merges) ws['!merges'] = merges
  let opts = {
    bookType: 'xlsx', // 要生成的文件类型
    bookSST: false,
    type: 'binary' // 二进制格式
  }
  let wbout = XLSX.write(wb, opts) // 生成xlsx格式的数据
  let xlsxblob = new Blob([s2ab(wbout)], {
    // 生成数据流格式
    type: 'application/octet-stream'
  })
  // 字符串转ArrayBuffer
  return xlsxblob
}

export function s2ab(s) {
  var buf = new ArrayBuffer(s.length)
  var view = new Uint8Array(buf)
  for (var i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
  return buf
}

/**
 * @description 下载文件
 * @param {*} json
 * @param {*} merges
 * @param {*} filename
 */
export function downloadJsonExcel(json, merges, filename) {
  let fileBlob = jsonToXlsxBlob(json, merges)
  if (!filename) {
    filename = uuid2(16, 16) + '-' + Date.now() + '.xlsx'
  }
  FileSaver.saveAs(fileBlob, filename)
}

/**
 * @description 生成uuid
 * @returns string
 */
export function uuid2(len, radix) {
  var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  var uuid = []; var i;
  radix = radix || chars.length;

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
  } else {
    // rfc4122, version 4 form
    var r;

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | Math.random() * 16;
        uuid[i] = chars[(i === 19) ? (r & 0x3) | 0x8 : r];
      }
    }
  }

  return uuid.join('');
}
