<template>
  <el-dialog
    class="print-ticket"
    :visible.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    custom-class="ps-dialog"
    :width="width"
    :destroy-on-close="false"
    top="25vh"
  >
    <el-form
      v-if="visible"
      :model="dialogForm"
      @submit.native.prevent
      status-icon
      ref="dialogForm"
      :rules="dialogFormRules"
      label-width="120px"
    >
      <div>
        <el-form-item v-if="type === 'address' || type === 'addressArea' || type === 'printForOrgOrder'" label="配送日期" prop="printDate">
          <el-date-picker
            v-model="dialogForm.printDate"
            type="date"
            :clearable="true"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            align="left"
            class="w-250 ps-picker"
            popper-class="ps-poper-picker"
            placeholder="请选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item
          v-if="type === 'address' || type === 'addressArea' || type === 'order' || type === 'cateringArea' || type === 'onScence' || type === 'printForOrgOrder'"
          label="打印组织"
          prop="printOrg"
          ref="organization"
          >
          <organization-select
            class="search-item-w ps-input w-250"
            placeholder="请选择打印组织"
            :isLazy="false"
            :multiple="false"
            :check-strictly="true"
            v-model="dialogForm.printOrg"
            @change="getOrganization"
            :append-to-body="true"
            >
            <!-- @change="getPrinterList" -->
            <!-- @change="getOrganization($event)" -->
          </organization-select>
        </el-form-item>
        <el-form-item label="打印设备" prop="printDevice">
          <el-select class="w-250 ps-select" v-model="dialogForm.printDevice" placeholder="请选择打印设备">
            <el-option v-for="item in printDeviceList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="type === 'address' || type === 'addressArea' || type === 'printForOrgOrder'">
          <el-select class="w-250 ps-select" v-model="dialogForm.printMeal" :multiple="true" placeholder="请选择打印餐段">
            <el-option v-for="item in mealTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="type === 'onScence' " label="打印类型" prop="printType">
          <el-select class="w-250 ps-select" v-model="dialogForm.printType"  placeholder="请选择打印类型">
            <el-option v-for="item in printTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="打印份数" prop="printCount">
          <el-input-number class="w-250 ps-input-number" v-model="dialogForm.printCount" :min="1"></el-input-number>
        </el-form-item>

        <el-form-item v-if="isShowSerialNumberRange" label="序号" prop="serialNumberRange">
          <div class="flex col-center" style="text-align: center;">
            <el-input class="w-120 ps-input-number" placeholder="开始序号" v-model="dialogForm.serialNumberRange.start"></el-input>
            <span style="width: 10px;">~</span>
            <el-input class="w-120 ps-input-number" placeholder="结束序号" v-model="dialogForm.serialNumberRange.end"></el-input>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: right;">
      <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">取消</el-button>
      <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">确定</el-button>
    </div>
  </el-dialog>
  <!-- end -->
</template>

<script>
import OrganizationSelect from '@/components/OrganizationSelect'
import { MEAL_TYPES } from '@/utils/constants'
import { parseTime } from '@/utils'
export default {
  name: 'PrintTicket',
  components: {
    OrganizationSelect
  },
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    // 卡务打印类型
    cardType: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '小票打印'
    },
    width: {
      type: String,
      default: '500px'
    },
    isshow: Boolean,
    confirm: Function,
    printInfo: {
      type: Object,
      default: () => {}
    },
    selectListId: {
      type: Array,
      default() {
        return []
      }
    },
    // 打印类型
    printType: {
      type: Array,
      default() {
        return []
      }
    },
    // 序号范围
    serialNumberRange: {
      type: Object,
      default: () => ({})
    },
    // 是否显示序号
    isShowSerialNumberRange: {
      type: Boolean,
      default: false
    }
  },
  data() {
    var checkSerialNumberRange = (rule, value, callback) => {
      if (this.isShowSerialNumberRange) {
        if (!value.start || !value.end) {
          return callback(new Error('开始和结束序号不能为空'))
        }
        if (!/^\d+$/.test(value.start)) {
          return callback(new Error('请输入正确的开始序号'))
        }
        if (!/^\d+$/.test(value.end)) {
          return callback(new Error('请输入正确的结束序号'))
        }
        if (parseInt(value.start) < 1) {
          return callback(new Error('开始序号必须大于等于1'))
        }
        if (parseInt(value.end) < 1) {
          return callback(new Error('结束序号必须大于等于1'))
        }
        if (parseInt(value.start) > parseInt(value.end)) {
          return callback(new Error('开始序号必须小于等于结束序号'))
        }
      }
      callback()
    }
    return {
      isLoading: false,
      dialogForm: {
        printDate: parseTime(new Date(), '{y}-{m}-{d}'),
        printOrg: '',
        printMeal: [],
        printDevice: '',
        printCount: 1,
        printType: '',
        serialNumberRange: { start: '', end: '' }
      },
      dialogFormRules: {
        printDate: [{ required: true, message: '请选择日期', trigger: 'change' }],
        printOrg: [{ required: true, message: '请选择打印组织', trigger: ['change', 'blur'] }],
        printDevice: [{ required: true, message: '请选择打印设备', trigger: 'change' }],
        printMeal: [{ required: true, message: '请选择打印餐段', trigger: 'blur' }],
        printType: [{ required: true, message: '请选择打印类型', trigger: 'blur' }],
        printCount: [{ required: true, message: '请选择打印份数', trigger: 'blur' }],
        serialNumberRange: [{ required: true, validator: checkSerialNumberRange, trigger: ['blur'] }]
      },
      mealTypeList: MEAL_TYPES,
      printDeviceList: [],
      printTypeList: this.printType
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (!this.visible) return
      this.getPrinterList()
      // 设置序号信息
      if (this.type === 'newPrintAgain' && this.isShowSerialNumberRange) {
        this.getSerialNumber()
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    clickConfirmHandle() {
      this.$refs.dialogForm.validate(valid => {
        if (valid) {
          let api
          let params = {
            printer: this.dialogForm.printDevice,
            print_num: this.dialogForm.printCount
          }
          // 新增打印管理-打印历史列表的打印
          if (this.type === 'newPrintAgain') {
            if (this.isShowSerialNumberRange) {
              params.start_index_no = Number(this.dialogForm.serialNumberRange.start)
              params.end_index_no = Number(this.dialogForm.serialNumberRange.end)
            }
            params.ids = this.selectListId
            api = this.$apis.apiBackgroundPrinterPrintTaskManagerPrintAgainPost(params)
          } else if (this.type === 'printAgain') {
            params.ids = this.selectListId
            api = this.$apis.apiBackgroundPrinterPrintTaskManagerPrintAgainPost(params)
          } else if (this.type === 'printAgainTask') {
            params.ids = this.selectListId
            api = this.$apis.apiBackgroundPrinterPrintTaskPrintAgainPost(params)
          } else if (this.type === 'order') {
            params.order_ids = this.selectListId
            params.org_ids = [this.dialogForm.printOrg]
            api = this.$apis.apiBackgroundPrinterPrintTaskManagerPrintForStallOrderPost(params)
          } else if (this.type === 'address') {
            params.adders_center_ids = this.selectListId
            params.org_ids = [this.dialogForm.printOrg]
            params.meal_types = this.dialogForm.printMeal
            params.reservation_date = this.dialogForm.printDate
            api = this.$apis.apiBackgroundPrinterPrintTaskManagerPrintForAddersOrderPost(params)
          } else if (this.type === 'addressArea') {
            params.addr_area_ids = this.selectListId
            params.org_ids = [this.dialogForm.printOrg]
            params.meal_types = this.dialogForm.printMeal
            params.reservation_date = this.dialogForm.printDate
            api = this.$apis.apiBackgroundPrinterPrintTaskManagerPrintForAreaOrderPost(params)
          } else if (this.type === 'cateringArea') {
            params.area_ids = this.selectListId
            params.org_ids = [this.dialogForm.printOrg]
            params.reservation_date_start = this.printInfo.reservation_date_start
            params.reservation_date_end = this.printInfo.reservation_date_end
            params.take_meal_time = this.printInfo.take_meal_time
            api = this.$apis.apiBackgroundPrinterPrintTaskManagerPrintForAreaCateringPost(params)
          } else if (this.type === 'card') {
            params.obj_ids = this.selectListId
            params.card_operate_type = this.cardType
            api = this.$apis.apiBackgroundPrinterPrintTaskManagerPrintForCardOperatePost(params)
          } else if (this.type === 'onScence') { // 新增堂食订单打印
            params.order_ids = this.selectListId
            params.org_ids = [this.dialogForm.printOrg]
            params.printer_type = this.dialogForm.printType
            api = this.$apis.apiBackgroundPrinterPrintTaskManagerPrintForOnSceneOrderPost(params)
          } else if (this.type === 'printForOrgOrder') { // 新增消费点配餐打印
            params.area_ids = this.selectListId
            params.org_ids = [this.dialogForm.printOrg]
            params.meal_types = this.dialogForm.printMeal
            params.reservation_date = this.dialogForm.printDate
            api = this.$apis.apiBackgroundPrinterPrintTaskManagerPrintForOrgOrder(params)
          }
          this.PrintTicket(api)
        } else {
        }
      })
    },
    async PrintTicket(api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        // this.confirm()
        this.$emit('confirm', 'search')
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    async getPrinterList() {
      this.dialogForm.printDevice = ''
      this.isLoading = true
      let params = {
        page: 1,
        page_size: 9999
      }
      if (this.dialogForm.printOrg) {
        params.org_ids = [this.dialogForm.printOrg]
      }
      const res = await this.$apis.apiBackgroundPrinterPrinterListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.printDeviceList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
      this.dialogForm.serialNumberRange = { start: '', end: '' }
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      // this.$refs.dialogForm.resetFields()
      // this.$emit('close')
      this.dialogForm.serialNumberRange = { start: '', end: '' }
    },
    getOrganization() {
      this.getPrinterList()
      this.$refs.organization.clearValidate()
    },
    //  设置打印类型
    setPrintType(type) {
      this.$set(this.dialogForm, "printType", type)
      console.log("this.dialogForm", this.dialogForm);
    },
    // 设置打印类型列表，如果如果动态改动这个列表数据的时候才调用此方法，平时直接在prop 传 printType就行
    setPrintTypeList(list) {
      this.printTypeList = list || []
    },
    // 获取打印类型列表序号
    getPrintNo() {
      return new Promise(resolve => {
        this.$apis.apiBackgroundPrinterPrintTaskManagerGetMaxIndexNoPost({
          id: this.selectListId[0]
        }).then(res => {
          if (res && res.code === 0) {
            let data = res.data || {}
            let maxIndexNo = data.max_index_no || 1
            let minIndexNo = data.min_index_no || 1
            resolve({ maxIndexNo, minIndexNo })
          } else {
            resolve({ maxIndexNo: 1, minIndexNo: 1 })
          }
        }).catch(error => {
          console.log("error", error);
          resolve({ maxIndexNo: 1, minIndexNo: 1 })
        })
      })
    },
    // 获取流水号序列号
    async getSerialNumber() {
      const { minIndexNo, maxIndexNo } = await this.getPrintNo()
      // const minNum = 1
      // const maxNum = await this.getPrintNo()
      this.$set(this.dialogForm.serialNumberRange, 'start', minIndexNo)
      this.$set(this.dialogForm.serialNumberRange, 'end', maxIndexNo)
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
.print-ticket{
}
</style>
