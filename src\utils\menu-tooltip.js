/**
 * Menu tooltip functionality to show tooltips for truncated menu items
 */

// Function to add title attributes to menu items
export function initMenuTooltips() {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupTooltips);
  } else {
    setupTooltips();
  }
}

// Setup the tooltips for menu items
function setupTooltips() {
  // Find all menu item and submenu title spans
  const menuSpans = document.querySelectorAll('.el-menu-item span, .el-submenu__title span');

  // Check each span to see if it's truncated
  menuSpans.forEach(span => {
    // Set the title attribute to the full text
    span.setAttribute('title', span.textContent);
  });

  // Observe for changes in the menu
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.type === 'childList') {
        // Re-check spans when menu changes
        document.querySelectorAll('.el-menu-item span, .el-submenu__title span').forEach(span => {
          span.setAttribute('title', span.textContent);
        });
      }
    });
  });

  // Start observing
  const menu = document.querySelector('.el-menu');
  if (menu) {
    observer.observe(menu, { childList: true, subtree: true });
  }
}

// Auto-initialize when this module is imported
initMenuTooltips();
