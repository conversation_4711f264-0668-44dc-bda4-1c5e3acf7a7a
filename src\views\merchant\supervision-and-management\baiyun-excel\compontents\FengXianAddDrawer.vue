<template>
  <customDrawer :show.sync="visible" cancelText="关闭" :confirmShow="type==='add'" :loading="isLoading" :title="title"
    :size="size" :wrapperClosable="false" :showClose="false" @confirm="confirmDialog" @cancel="closeDialog"
    :printShow="true" :printText="打印" @print="clickPrintHandle" :exportShow="type==='detail'" :exportText="导出" @export="clickExportHandle" :exportPermission="['background_fund_supervision.ledger.risk_control_list_export']">
    <div class="table-content m-t-20">
      <!-- 表单包装表格 start -->
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="0">
        <el-table v-loading="isLoading" :data="formData.tableData" ref="historytableData" style="width: 100%" stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #control_measures="{ row }">
            <div class="multiline-text">{{  row.control_measures }}</div>
            </template>
          </table-column>
          <el-table-column  label="责任人"  align="center" width="150px">
            <template slot-scope="scope">
              <el-form-item :prop="`tableData.${scope.$index}.responsible_person`" :rules="responsiblePersonRules">
                <el-input v-model.trim="scope.row.responsible_person" placeholder="请输入责任人"  v-if="type==='add'"/>
                <div class="penson-txt" v-if="type==='detail'">{{  scope.row.responsible_person }}</div>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <!-- 表单包装表格 end -->
    </div>
    <!-- 分页 start -->
    <div class="block ps-pagination" style="text-align: right; padding-top: 20px; display: none">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100, 500]" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount" background class="ps-text" popper-class="ps-popper-select"></el-pagination>
    </div>
  </customDrawer>
  <!-- end -->
</template>

<script>
import { to, deepClone, setLocalStorage } from '@/utils'
import dayjs from 'dayjs'
import { TABLE_HEAD_DATA_FENG_XIAN_LEDGER_DETAIL } from '../constants'
import { FENG_XIAN_ADD_DRAWER_DEFAULT_DATA } from './defaultData'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report'
export default {
  name: 'FengXianAddDrawer',
  props: {
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'add'
    },
    size: {
      type: String,
      default: '90%'
    },
    isshow: Boolean
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchDate: [],
      datePickerOptions: {
        // 禁用超过最近30天之前的日期
        disabledDate: time => {
          const today = dayjs()
          const thirtyDaysAgo = today.subtract(90, 'days')
          return time.getTime() < thirtyDaysAgo || time.getTime() > today.endOf('day')
        }
      },
      tableSetting: deepClone(TABLE_HEAD_DATA_FENG_XIAN_LEDGER_DETAIL),
      // 表单数据
      formData: {
        tableData: []
      },
      // 责任人验证规则
      responsiblePersonRules: [
        { required: true, message: '请输入责任人', trigger: 'blur' },
        { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
      ],
      // 表单验证规则
      formRules: {},
      printType: 'fengXianLedgerDetail'
    }
  },
  mixins: [exportExcel, report],
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    isshow(val) {
      if (val) {
        // 初始化数据
        this.initData()
      }
    }
  },
  created() {
  },
  mounted() { },
  methods: {
    // 初始化数据
    initData() {
      // 使用默认数据初始化表格
      let tableData = this.type === "add" ? deepClone(FENG_XIAN_ADD_DRAWER_DEFAULT_DATA) : (this.drawerData.extra ? this.drawerData.extra : [])
      // 添加序号
      tableData = tableData.map((item, index) => {
        return {
          ...item,
          index: index + 1
        }
      })
      this.totalCount = tableData.length
      // 设置足够大的页面大小以显示所有数据
      this.pageSize = this.totalCount
      this.currentPage = 1
      this.$set(this.formData, 'tableData', tableData)
      console.log('this.formData.tableData', this.formData.tableData)
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate()
        }
      })
    },

    async confirmDialog(e) {
      console.log('confirmDialog', this.formData.tableData)
      try {
        // 先进行表单验证
        this.$refs.formRef.validate((valid) => {
          if (valid) {
            // 验证通过
            this.saveData()
            // this.visible = false
            // this.$emit('confirm', this.formData.tableData)
          } else {
            // 再手动检查所有责任人字段
            const emptyFields = this.formData.tableData.filter((item, index) => {
              return !item.responsible_person || item.responsible_person.trim() === ''
            })
            if (emptyFields.length > 0) {
              this.$message.error('序号' + emptyFields[0].index + '风险项未填写负责人')
              return
            }
            return
          }
        })
      } catch (error) {
        // 验证失败，显示错误信息
        console.log('error', error)
        this.$message.error('请填写所有责任人信息')
        return
      }
    },
    // 保存数据
    async saveData() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerRiskControlAddPost({
          extra: this.formData.tableData
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '保存失败')
        return
      }
      if (res && res.code === 0) {
        this.$message.success('保存成功')
        this.visible = false
        this.$emit('confirm', this.formData.tableData)
      } else {
        this.$message.error(res.msg || '保存失败')
      }
    },

    // 获取历史记录
    async getSampleOperateLogList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreRetentionRecordFoodReservedSampleOperateLogListPost({
          start_time: this.searchDate.length ? this.searchDate[0] + ' 00:00:00' : '',
          end_time: this.searchDate.length ? this.searchDate[1] + ' 23:59:59' : '',
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.formData.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
    },
    // 不再需要更新表格数据的方法，因为我们显示所有数据
    updateTableData() {
      // 保留空方法以防其他地方调用
    },
    closeDialog() {
      this.$emit('close')
    },
    // 打印事件
    clickPrintHandle() {
      console.log('clickPrintHandle')
      const params = {}
      let tableSetting = deepClone(this.tableSetting)
      tableSetting.push({
        label: '责任人',
        key: 'responsible_person',
        width: '150px'
      })
      tableSetting = tableSetting.filter(item => item.key !== 'operation')
      setLocalStorage(this.printType, JSON.stringify(this.formData.tableData))
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: false,
          print_type: this.printType,
          print_title: '风险管控清单管理台账',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerRiskControlListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          is_local: true,
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    },
    // 导出按钮点击
    gotoExport() {
      console.log('gotoExport', this.drawerData)
      let params = {
        r_id: this.drawerData.id
      }
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionLedgerRiskControlExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    // 导出按钮点击
    clickExportHandle() {
      this.gotoExport()
    }
  }
}
</script>

<style lang="scss" scoped>
// 自定义表格中表单项的样式
.multiline-text {
  white-space: pre-wrap;
  text-align: left;
}
.penson-txt{
  text-align: center;
}
</style>
