<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-auto-dialog"
    :width="width"
  >
    <el-form
      :model="formData"
      @submit.native.prevent
      status-icon
      ref="formData"
      :rules="formRules"
      label-width="100px"
      class="dialog-form"
      v-loading="isLoading"
    >
      <el-form-item label="标签名称" prop="label_name">
        <div class="w-auto">
          <el-input v-model="formData.label_name" :maxlength="10"></el-input>
          <!-- <tree-select
            :multiple="false"
            :options="foodlabelList"
            :normalizer="paySettingNormalizer"
            placeholder="请选择"
            :default-expand-level="1"
            v-model="formData.label_id"
            :disable-branch-nodes="true"
            :show-count="true"
            :append-to-body="true"
            :z-index="3000"
            no-results-text="暂无数据"
            no-children-text="暂无更多"
          ></tree-select> -->
        </div>
      </el-form-item>
      <el-form-item label="选择元素" prop="nutrition_key">
        <el-select
          v-model="formData.nutrition_key"
          placeholder="请选择"
          filterable
          class="ps-select w-auto"
          popper-class="ps-popper-select"
          :disabled="type === 'modify'"
        >
          <el-option
            v-for="(item, index) in nutrition_key"
            :key="index"
            :label="item.name"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <div class="ps-flex no-item-bottom">
        <el-form-item label="标签规则" prop="comparison" class="">
          <el-select
            v-model="formData.comparison"
            placeholder="请选择"
            filterable
            class="ps-select"
            popper-class="ps-popper-select"
            style="width: 140px"
          >
            <el-option
              v-for="(item, index) in comparisonList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <div class="ps-flex flex-align-c" v-if="formData.comparison">
          <el-form-item label="" prop="operator_number_one" label-width="0" class="p-l-10 p-r-10">
            <el-input
              v-model="formData.operator_number_one"
              style="width: 95px"
              class="ps-input"
            ></el-input>
          </el-form-item>

          <div v-if="formData.comparison === 'between'" class="ps-flex flex-align-c">
            <div>~</div>
            <el-form-item label="" prop="operator_number_two" label-width="0">
              <el-input
                v-model="formData.operator_number_two"
                style="width: 95px"
                class="ps-input m-l-10"
              ></el-input>
            </el-form-item>
          </div>
        </div>
      </div>
      <el-form-item label="" prop="">
        每100g含量
      </el-form-item>
    </el-form>

    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 60px; text-align: right">
        <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">
          取消
        </el-button>
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">
          保存
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { NUTRITION_KEY } from '@/constants'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { to, deepClone } from '@/utils'
export default {
  name: 'trayDialog',
  components: {},
  props: {
    loading: Boolean,
    isshow: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '600px'
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    },
    confirm: Function
  },
  // mixins: [activatedLoadData],
  data() {
    var validatorOperatorNumberOne = (rule, value, callback) => {
      if (!this.reg.test(value) || Number(value) > 10000) {
        callback(new Error(' '))
      } else {
        callback()
      }
    }
    var validatorOperatorNumberTow = (rule, value, callback) => {
      if (
        !this.reg.test(value) ||
        Number(value) > 10000 ||
        Number(value) < Number(this.formData.operator_number_one)
      ) {
        callback(new Error(' '))
      } else {
        callback()
      }
    }
    return {
      reg: /^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/,
      isLoading: false,
      formData: {
        label_name: '',
        comparison: '',
        nutrition_key: '',
        operator_number_one: '',
        operator_number_two: ''
      },
      formRules: {
        label_name: [{ required: true, message: '请选择输入名称', trigger: ['blur', 'change'] }],
        nutrition_key: [{ required: true, message: ' ', trigger: ['blur', 'change'] }],
        comparison: [{ required: true, message: ' ', trigger: ['blur', 'change'] }],
        operator_number_one: [
          {
            required: true,
            validator: validatorOperatorNumberOne,
            message: ' ',
            trigger: ['blur', 'change']
          }
        ],
        operator_number_two: [
          {
            required: true,
            validator: validatorOperatorNumberTow,
            message: ' ',
            trigger: ['blur', 'change']
          }
        ]
      },
      foodlabelList: [], // 分组
      nutrition_key: deepClone(NUTRITION_KEY),
      comparisonList: [
        {
          label: '大于',
          value: '>'
        },
        {
          label: '大于等于',
          value: '>='
        },
        {
          label: '小于',
          value: '<'
        },
        {
          label: '小于等于',
          value: '<='
        },
        {
          label: '区间',
          value: 'between'
        }
      ]
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {},
  async created() {
    // await this.getLabelGroupList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    // paySetting option
    paySettingNormalizer(node) {
      if (!node) {
        return
      }
      return {
        id: node.id,
        label: node.name,
        children: node.label_list
      }
    },
    async getLabelGroupList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyLabelGroupListPost({
          type: 'food',
          page: 1,
          page_size: 999999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodlabelList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    initLoad() {
      this.formData = {
        label_name: this.type === 'modify' ? this.infoData.label_name : '',
        comparison: this.type === 'modify' ? this.infoData.comparison : '',
        nutrition_key: this.type === 'modify' ? this.infoData.nutrition_key : '',
        operator_number_one: this.type === 'modify' ? this.infoData.operator_number_one : '',
        operator_number_two: this.type === 'modify' ? this.infoData.operator_number_two : ''
      }
    },
    clickConfirmHandle() {
      this.$refs.formData.validate(async valid => {
        if (valid) {
          let params = {
            label_name: this.formData.label_name,
            comparison: this.formData.comparison,
            nutrition_key: this.formData.nutrition_key,
            operator_number_one: Number(this.formData.operator_number_one),
            operator_number_two: Number(this.formData.operator_number_two)
          }
          this.isLoading = true
          let [err, res] = ''
          if (this.type === 'add') {
            ;[err, res] = await to(this.$apis.apiBackgroundHealthyAdminAutoLabelAddPost(params))
          } else {
            params.id = this.infoData.id
            ;[err, res] = await to(this.$apis.apiBackgroundHealthyAdminAutoLabelModifyPost(params))
          }
          this.isLoading = false
          if (err) {
            this.$message.error(err.message)
            return
          }
          if (res.code === 0) {
            this.visible = false
            // this.confirm()
            this.$emit('confirm', 'search')
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
        } else {
          this.$message.error('参数格式错误，请认真检查！')
        }
      })
    },
    clickCancleHandle() {
      this.visible = false
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.ps-auto-dialog {
  .w-auto{
    width: 80%;
  }
  .no-item-bottom {
    margin-bottom: 22px;
    .el-form-item{
      margin-bottom: 0;
    }
  }
}
</style>
