<template>
  <div class="store-goods container-wrapper">
    <div class="top-btn">
      <el-radio-group v-model="tabType" class="ps-radio-btn" @change="changeTab">
        <el-radio-button v-for="item in tabTypeList" :key="item.value" :label="item.value" v-permission="[`${item.permission}`]">
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
      <!-- <img @click="refreshHandle" class="refresh" src="@/assets/img/refresh.png" alt="" /> -->
    </div>
    <store-stock ref="StoreStock" v-if="tabType === 'store_stock'"></store-stock>
    <category-stock ref="StoreStock" v-if="tabType === 'category_stock'"></category-stock>
  </div>
</template>

<script>
// import { debounce } from '@/utils'
import StoreStock from './components/StoreStock.vue'
import CategoryStock from './components/CategoryStock.vue'
export default {
  name: 'StoreStockAdmin',
  components: { CategoryStock, StoreStock },
  data() {
    return {
      tabType: 'store_stock',
      tabTypeList: [
        {
          value: 'store_stock',
          label: '商品库存',
          permission: 'background_store.goods_stock.goods_stock_list'
        },
        {
          value: 'category_stock',
          label: '分类库存',
          permission: 'background_store.goods_stock.category_stock_list'
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    changeTab() {},
    refreshHandle() {
      // this.$refs.deliverChild.resetSearchHandle()
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.store-goods {
  .top-btn {
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
  }
  .refresh {
    cursor: pointer;
    padding: 14px 0;
  }
}
</style>
