<template>
  <div class="add_meal_month_recipes container-wrapper">
    <div class="table-wrapper" v-loading="isLoading">
      <div class="tab m-b-20">
        <div
          :class="['tab-item', foodMenuType === 'foodSeting' ? 'active' : '']"
          @click="tabClick('foodSeting')"
        >
          菜品配置
        </div>
        <div
          v-if="isNutritionGuidance"
          :class="['tab-item', foodMenuType === 'nutritionAnalysis' ? 'active' : '']"
          @click="tabClick('nutritionAnalysis')"
        >
          营养分析
        </div>
      </div>
      <!-- 餐段 -->
      <div v-if="foodMenuType === 'foodSeting'">
        <div class="meal-wrapper" v-for="(item, index) in mealList" :key="index">
          <div class="meal-content p-b-10">
            <div class="meal-text">
              {{ item.label }}（{{
                item.dataList
                  ? item.dataList.food_data.length || item.dataList.set_meal_data.length
                  : 0
              }}）
            </div>
            <div class="operate">
              <span
                class="catering-text"
                v-if="
                  item.dataList &&
                    (item.dataList.food_data.length || item.dataList.set_meal_data.length)
                "
                @click="clickMenuCatering(item)"
              >
                配餐
              </span>
              <span class="catering-no-text" v-else @click="clickMenuCatering(item)">未配餐</span>
              <span
                class="cope-text"
                v-if="
                  item.dataList &&
                    (item.dataList.food_data.length || item.dataList.set_meal_data.length)
                "
                @click="copyMeal(item)"
              >
                复制
              </span>
            </div>
          </div>
          <div
            class="ps-flex-align-c flex-align-c p-b-10"
            v-if="
              isNutritionGuidance &&
                (item.value === 'breakfast' || item.value === 'lunch' || item.value === 'dinner')
            "
          >
            <div class="progress-box">
              <span class="progress-title">能量摄入</span>
              <el-progress
                class="progress-content"
                :show-text="false"
                :color="
                  percentageColor(item.dataList ? item.dataList.total_nutrition.energy_kcal : '')
                "
                :percentage="
                  percentageTotal(item.dataList ? item.dataList.total_nutrition.energy_kcal : 0)
                "
              ></el-progress>
            </div>
            <div class="progress-box">
              <span class="progress-title">三大营养元素</span>
              <el-progress
                class="progress-content"
                :show-text="false"
                :color="
                  percentageColor(
                    item.dataList
                      ? item.dataList.total_nutrition.total_axunge_protein_carbohydrate
                      : ''
                  )
                "
                :percentage="
                  percentageTotal(
                    item.dataList
                      ? item.dataList.total_nutrition.total_axunge_protein_carbohydrate
                      : 0
                  )
                "
              ></el-progress>
            </div>
            <div class="progress-box">
              <span class="progress-title">食物多样性</span>
              <el-progress
                class="progress-content"
                :show-text="false"
                :color="
                  percentageColor(
                    item.dataList
                      ? item.dataList.total_nutrition.total_cereals_eggsandmeat_fruit_vegetable
                      : ''
                  )
                "
                :percentage="
                  percentageTotal(
                    item.dataList
                      ? item.dataList.total_nutrition.total_cereals_eggsandmeat_fruit_vegetable
                      : 0
                  )
                "
              ></el-progress>
            </div>
          </div>
          <el-table
            :data="item.dataList ? item.dataList.food_data : []"
            ref="progressTableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
            class="ps-table"
          >
            <el-table-column prop="food_name" label="菜品名称" align="center"></el-table-column>
            <el-table-column
              prop="ingredient_group"
              key="ingredient_group"
              label="食材组成"
              align="center"
              width="200"
            >
              <template slot-scope="scope">
                <div
                  v-if="scope.row.ingredient_group && scope.row.ingredient_group.length"
                  class="ps-flex-align-c flex-wrap"
                >
                  <div v-for="(item, index) in scope.row.ingredient_group" :key="index">
                    <span class="p-t-10">
                      {{
                        (index === scope.row.ingredient_group.length - 1 &&
                          item.name + ':' + item.number) ||
                          item.name + ':' + item.number + '、'
                      }}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="" label="营养元素" align="center" width="200">
              <template slot-scope="scope">
                <div class="ps-flex-align-c flex-wrap">
                  {{ scope.row.ingredient_category_count + '类' }}/
                  <div v-for="(item, index) in mainNutritionList" :key="index">
                    {{
                      (index === mainNutritionList.length - 1 &&
                        item.name + ':' + scope.row.main_nutrition[item.key] + item.unit) ||
                        item.name + ':' + scope.row.main_nutrition[item.key] + item.unit + '/'
                    }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="setting_stocks" label="库存" align="center"></el-table-column>
            <el-table-column
              prop="food_buy_limit"
              label="限制可点数量"
              align="center"
            ></el-table-column>
            <el-table-column label="操作" align="center" width="100" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  @click="clickMenuFoodDelete(item, scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <menu-nutrition-analysis
        :menuType="menu_type"
        :menuId="menu_id"
        v-if="foodMenuType === 'nutritionAnalysis'"
      />
    </div>
    <!-- 复制某一天的餐段 -->
    <el-dialog
      title="复制到"
      :visible.sync="showMealCopyDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      width="600px"
    >
      <div>
        日期：
        <el-date-picker v-model="copyMealDate" placeholder="选择日期" size="small" :default-value="monthDate" :picker-options="pickerOptions" popper-class="el-picker-box"></el-date-picker>
      </div>
      <div style="margin-top: 12px">
        餐段：
        <el-select size="small" v-model="copyMealType">
          <el-option
            v-for="opt in mealTypeList"
            :label="opt.label"
            :key="opt.value"
            :value="opt.value"
          ></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showMealCopyDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="handleCopyMeal">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { MEAL_TYPES } from '@/utils/constants'
import * as dayjs from 'dayjs'
import { to } from '@/utils'
import menuNutritionAnalysis from '../components/menu/menuNutritionAnalysis'

export default {
  components: {
    menuNutritionAnalysis
  },
  data() {
    return {
      pickerOptions: this.disabledDate(),
      isLoading: false,
      menu_type: '',
      menu_id: '',
      monthDate: '',
      foodMenuType: 'foodSeting',
      mealList: [],
      showMealCopyDialog: false,
      copyMealDate: '',
      copyMealType: '',
      mealTypeList: [{ label: '全部', value: 'all' }, ...MEAL_TYPES],
      dataInfoDialog: {}, // cope 打开的单条数据
      isNutritionGuidance: false,
      mainNutritionList: [
        {
          name: '能量',
          key: 'energy_kcal',
          value: 0,
          unit: 'kcal'
        },
        {
          name: '脂肪',
          key: 'axunge',
          value: 0,
          unit: 'g'
        },
        {
          name: '碳水化物',
          key: 'carbohydrate',
          value: 0,
          unit: 'g'
        },
        {
          name: '蛋白质',
          key: 'protein',
          value: 0,
          unit: 'g'
        }
      ]
    }
  },
  created() {
    this.initLoad()
    if (this.$route.query.isNutritionGuidance === 'guidance') {
      this.isNutritionGuidance = true
    }
    this.mealList = MEAL_TYPES
  },
  mounted() {},
  methods: {
    disabledDate() {
      let that = this;
      return {
        disabledDate(time) {
          let month = new Date(that.monthDate).getMonth()
          let timeMonth = new Date(time).getMonth()
          return month !== timeMonth
        }
      };
    },
    initLoad() {
      this.menu_type = this.$route.query.menuType
      this.menu_id = String(this.$route.query.menuId)
      this.monthDate = this.$route.query.monthDate
      this.getMenuDateNutrition()
    },
    tabClick(type) {
      // tab 栏点击事件
      this.foodMenuType = type
    },
    copyMeal(data) {
      this.dataInfoDialog = data.dataList
      this.showMealCopyDialog = true
    },
    // 确定复制某一个餐段
    async handleCopyMeal(id, date) {
      if (!this.copyMealDate) {
        return this.$message.error('请选择日期')
      }
      if (!this.copyMealType) {
        return this.$message.error('请选择餐段')
      }
      this.showMealCopyDialog = false
      const params = {
        id: this.dataInfoDialog.id,
        target_data: []
      }
      params.target_data.push({
        date: dayjs(this.copyMealDate).format('YYYY-MM-DD'),
        meal_type: this.copyMealType
      })
      const res = await this.$apis.apiBackgroundFoodMenuMonthlyCopyMealTypeFoodPost(params)
      if (res.code === 0) {
        this.$message.success('操作成功！')
        this.showMealCopyDialog = false
        // 要刷新接口
        this.getMenuDateNutrition()
      } else {
        this.$message.error(res.msg)
      }
    },
    async getMenuDateNutrition() {
      let params = {
        id: this.menu_id, // id
        menu_type: this.menu_type, // 菜谱 周月
        date: this.monthDate
        // nutrition_type: 'carbohydrate'
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodMenuMenuDateNutritionPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.mealList = MEAL_TYPES.map(v => {
          let dataItem = res.data.result[v.value]
          if (dataItem) {
            v.dataList = dataItem
            v.dataList.food_data.forEach(item => {
              item.setting_stocks = dataItem.setting_stocks[item.id] === -1 ? '无数量限制' : dataItem.setting_stocks[item.id]
              item.food_buy_limit = dataItem.food_buy_limit[item.id]
            })
          } else {
            v.dataList = null
          }
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除单个菜品
    clickMenuFoodDelete(data, row) {
      this.getMenuMenuFoodDelete(row.id, data.value)
    },
    async getMenuMenuFoodDelete(id, mealType) {
      let params = {
        id: this.menu_id, // id
        menu_type: this.menu_type, // 菜谱 周月
        date: this.monthDate,
        meal_type: mealType,
        food_id: id
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodMenuMenuFoodDeletePost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.getMenuDateNutrition()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 计算营养
    percentageTotal(nutritionData) {
      let totle = 0
      if (nutritionData) {
        if (parseInt(nutritionData) >= 100) {
          totle = 100
        } else {
          totle = parseInt((nutritionData / 120) * 100)
        }
      }
      return totle
    },
    //  营养进度条的样式
    percentageColor(nutritionData) {
      let color = ''
      if (parseInt(nutritionData) >= 120) {
        //  红色
        color = '#ea5b55'
      } else if (parseInt(nutritionData) >= 80) {
        color = '#5dbf6e'
      } else if (parseInt(nutritionData) < 80) {
        color = '#e89e42'
      }
      return color
    },
    clickMenuCatering(data) {
      if (data.value === 'afternoon' || data.value === 'supper' || data.value === 'morning') {
        this.isNutritionGuidance = false
      }
      this.$router.push({
        name: 'MerchantMenuCatering',
        query: {
          isNutritionGuidance: this.isNutritionGuidance ? 'true' : 'false',
          menuId: this.$route.query.menuId, // id
          menuType: this.$route.query.menuType, // 菜谱 周月
          currentEditMealType: data.value, // 早午晚
          currentEditDate: this.$route.query.monthDate, // 日期
          data: data.dataList ? this.$encodeQuery(data.dataList) : this.$encodeQuery([])
        }
      })
    }
  }
}
</script>

<style lang="scss">
.add_meal_month_recipes {
  .tab {
    padding: 20px 20px 0 20px;
    .tab-item {
      display: inline-block;
      width: 90px;
      height: 28px;
      line-height: 28px;
      margin-right: 10px;
      text-align: center;
      border-radius: 14px;
      border: solid 1px #dae1ea;
      font-size: 16px;
      color: #7b7c82;
      vertical-align: middle;
      cursor: pointer;
      &.active {
        color: #ffffff;
        background-color: #fd953c;
      }
    }
  }
  .meal-wrapper {
    padding: 20px;
    .meal-content {
      display: flex;
      .meal-text {
        font-size: 16px;
        font-weight: bold;
      }
      .operate {
        .catering-text {
          color: #5dbf6e;
          cursor: pointer;
        }
        .cope-text {
          color: #fd953c;
          cursor: pointer;
        }
        .catering-no-text {
          color: #6d9df7;
          cursor: pointer;
        }
      }
    }
    .progress-box {
      display: flex;
      align-items: center;
      padding-right: 30px;
      .progress-title {
        // flex: 1;
        padding-right: 10px;
        font-size: 13px;
      }
      .progress-content {
        width: 200px;
      }
    }
  }
}
.el-picker-box{
  .el-picker-panel__icon-btn{
    display: none;
  }
  .el-date-picker__header{
    span:nth-child(3) { /*第三个标签是span的标签，把它隐藏*/
      display: none;
    }
  }
  .el-date-table td.selected span{
    color: #fff !important;
  }
}
</style>
