<template>
  <div class="inventory-flow-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" :show-refresh="false" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表
          <span class="inline-block m-l-20 font-size-16">当前仓库：<span style="color: 000; font-weight: 700;">{{$route.query.warehouse_name}}</span></span>
        </div>
        <div class="align-r">
          <!-- <button-icon color="origin" @click="showImportDialog=true">导入</button-icon> -->
          <button-icon color="origin" @click="openDialog">新增盘点</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- content start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoDetail(row)" >查看详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- content end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <dialog-message :show.sync="showDialog" title="新建盘点" width="435px" @close="closeDialogHandle" @confirm="clickConfirmHandle">
      <el-form v-loading="dialogLoading" ref="dialogFormRef" :model="dialogForm" :rules="dialogrules" label-position="left" label-width="80px" size="medium">
        <el-form-item label="盘点名称" label-width="80px" prop="name">
          <el-input v-model="dialogForm.name" :maxlength="20" class="ps-input"></el-input>
        </el-form-item>
      </el-form>
    </dialog-message>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
export default {
  name: 'InventoryStock',
  mixins: [exportExcel],
  components: {
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      tableSettings: [
        { label: '盘点时间', key: 'inventory_time' },
        { label: '盘点名称', key: 'name' },
        { label: '操作人', key: 'account_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      tableData: [], // table数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '盘点时间',
          format: 'yyyy-MM-dd',
          clearable: false,
          value: getSevenDateRange(7)
        },
        account_name: {
          type: 'input',
          value: '',
          label: '操作人',
          placeholder: '请输入'
        }
      },
      showDialog: false, // 是否开启弹窗
      dialogLoading: false, // 弹窗loading
      dialogType: '', // 弹窗的状态，add/modify
      dialogForm: {
        name: ''
      }, // 弹窗数据
      dialogrules: {
        name: [{ required: true, message: '请输入盘点名称', trigger: 'change' }]
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getInventoryStock()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getInventoryStock() {
      if (!this.$route.query.warehouse_id) return this.$message.error('获取仓库参数失败，请重新进入！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: Number(this.$route.query.warehouse_id),
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundDrpMaterialInventoryListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getInventoryStock()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 导出
    handleExport(row) {
      const option = {
        type: 'InquiryList',
        url: 'apiBackgroundDrpInquiryExportInquiryPost',
        params: {
          // id: row.id
        }
      }
      this.exportHandle(option)
    },
    gotoDetail(row) {
      this.$router.push({
        name: 'InventoryStockDetail',
        query: {
          id: row.id
        }
      })
    },
    addInventoryStockHandle(type) {
      this.$router.push({
        name: 'AddInventoryStock',
        params: {
          type
        },
        query: {
          ...this.$route.query,
          name: this.dialogForm.name
        }
      })
      this.$refs.dialogFormRef.resetFields()
    },
    openDialog() {
      this.showDialog = true
    },
    // 弹窗关闭事件
    closeDialogHandle() {
      this.$refs.dialogFormRef.resetFields()
    },
    // 弹窗确定事件
    clickConfirmHandle(e) {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          this.addInventoryStockHandle('add')
          this.showDialog = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.inventory-flow-wrapper {
  .ps-pagination {
    padding-top: 0px !important;
  }
}
</style>
