<template>
  <div class="AddTransferOrder p-t-20">
    <h3 class="">{{ titleText }}</h3>
    <div class="form-container">
      <el-form
        v-loading="isLoading"
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        size="small"
        class="m-l-20 m-t-10"
      >
        <el-form-item label="当前仓库" prop="">
          {{ $route.query.warehouse_name }}
          <div class="float-r">
            <el-button class="ps-cancel-btn" size="medium" @click="closeHandler">取消</el-button>
            <el-button class="ps-origin-btn" size="medium" @click="submitFormHandle">保存提交</el-button>
          </div>
        </el-form-item>
        <el-form-item label-width="0">
          <el-form-item label="调拨仓库" prop="transfer_warehouse_id" class="inline-block">
            <el-select v-model="formData.transfer_warehouse_id" clearable class="ps-select" popper-class="ps-popper-select" placeholder="请选择">
              <el-option v-for="option in warehouseList" :key="option.id" :label="option.name" :value="option.id" ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="预计归还日期" prop="expected_return_date" class="inline-block m-l-30" label-width="110px">
            <el-date-picker
              v-model="formData.expected_return_date"
              type="date"
              placeholder="选择日期时间"
              value-format="yyyy-MM-dd"
              :picker-options="pickerPurchaseTimeOptions"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="" prop="autoContactTrade" class="inline-block" label-width="0">
            <el-checkbox v-model="formData.autoContactTrade" class="ps-checkbox m-l-30 m-r-6">自动关联入库单据</el-checkbox>
          </el-form-item>
        </el-form-item>
        <el-form-item label="" prop="" label-width="10px">
          <el-button class="ps-origin-btn w-100" @click="addMaterials('transfer_order')">新增物资</el-button>
        </el-form-item>
        <el-form-item label="" label-width="10px">
          <div style="width: 92%; margin-bottom: 10px">
            <el-table
              :data="currentMaterialsTableData"
              ref="tableRef"
              stripe
              size="small"
              border
              header-row-class-name="ps-table-header-row"
            >
              <table-column v-for="item in materialsTableSettings" :key="item.key" :col="item">
                <template #unitName="{ row }">
                  <span>
                    {{ row.limit_unit_item.unit_management_name }}*
                    {{row.limit_unit_item.net_content}}{{row.limit_unit_item.net_content_unit}}
                  </span>
                </template>
                <template #transferCount="{ row, index }">
                  <el-form-item
                    label=""
                    label-width="0"
                    class="m-b-0"
                    :rules="formRules.count"
                    :prop="'materialsTableData.' + getMaterialsIndex(index) + '.count'"
                    :error="row.error"
                  >
                    <el-input
                      v-model="row.count"
                      placeholder="请输入"
                      :maxlength="6"
                      class="ps-input"
                      @input="changeCountHandle($event, row, index)"
                    ></el-input>
                  </el-form-item>
                </template>
                <!-- 关联采购单 -->
              <template #associated="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.associated"
                  :prop="'materialsTableData[' + getMaterialsIndex(index) + '].contact_trade_list'"
                >
                  <el-button
                    type="text"
                    size="small"
                    class="ps-text"
                    @click="addMaterials('associated_purchase', row, index)"
                  >
                    {{ row.contact_trade_list && row.contact_trade_list.length > 0 ? `已关联${row.contact_trade_list.length}` : '选择' }}
                  </el-button>
                </el-form-item>
              </template>
                <template #operation="{ index }">
                  <el-button type="text" size="small" class="ps-origin" @click.stop="deleteMaterials(index)">
                    删除
                  </el-button>
                </template>
              </table-column>
            </el-table>
            <div v-if="errorTableData" class="red">{{ errorTableData }}</div>
            <pagination
              v-if="formData.materialsTableData.length > materialsPageSize"
              :onPaginationChange="onMaterialsPaginationChange"
              :current-page.sync="materialsPage"
              :page-size.sync="materialsPageSize"
              :layout="'total, prev, pager, next, jumper'"
              :total="formData.materialsTableData.length"
            ></pagination>
          </div>
        </el-form-item>
        <el-form-item label="单据备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            class="ps-textarea w-280"
            :rows="3"
            :maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="上传附件">
          <file-upload
            ref="fileUploadRef"
            :fileList="formData.fileLists"
            type="enclosure"
            prefix="inventory"
            :show-file-list="false"
            accept=".jpeg,.jpg,.png,.bmp"
            :rename="false"
            :multiple="true"
            :limit="9"
            :before-upload="beforeUpload"
            @fileLists="getFileLists"
          >
            <template v-slot="scope">
              <!-- {{ scope }} -->
              <el-button :loading="scope.loading" class="ps-origin" size="small" type="text">
                上传{{ scope.loading ? '中' : '' }}
              </el-button>
            </template>
          </file-upload>
          <!-- <p style="color:#a5a5a5; line-height: 1.5;">附件不超过20M</p> -->
        </el-form-item>
        <el-form-item v-if="previewList.length">
          <el-collapse v-model="activeCollapse" style="max-width: 60%">
            <el-collapse-item :title="collapseTitle" name="1">
              <div class="img-item" v-for="(img, index) in previewList" :key="img + index">
                <el-image
                  :preview-src-list="previewList"
                  :initial-index="index"
                  class="upload-img m-r-6"
                  :src="img"
                  fit="contain"
                ></el-image>
                <span class="img-tools">
                  <i class="el-icon-delete" @click.stop="deleteUploadImg(index)"></i>
                </span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
      </el-form>
    </div>
    <!-- 添加物资 -->
    <choose-list-dialog
      :showdialog.sync="showChooseDialog"
      :title="dialogChooseTitle"
      :type="dialogChooseType"
      :api="dialogChooseApi"
      :search-setting="dialogChooseSearchSetting"
      :table-settings="dialogChooseTableSettings"
      :params="dialogChooseParams"
      :defaultSelect="dialogSelect"
      :rowKey="dialogRowKey"
      :showSelectLen="showSelectLen"
      @confirmChoose="confirmChooseHandle"
    >
      <div slot="tip" style="font-size: 12px;">调拨数量不可大于物资当前库存</div>
    </choose-list-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSuffix } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import { integer, oneDecimal } from '@/utils/validata'
import { validateNumber, validateOneDecimalCount } from '@/utils/form-validata'
import * as dayjs from 'dayjs'
import NP from 'number-precision'
import ChooseListDialog from '../../components/ChooseListDialog'

export default {
  name: 'AddTransferOrder',
  mixins: [exportExcel],
  components: {
    ChooseListDialog
  },
  data() {
    return {
      type: 'add',
      titleText: '仓库管理/调拨单/新增调拨单',
      isLoading: false, // 刷新数据
      queryData: this.$route.query,
      // form表单数据
      formData: {
        transfer_warehouse_id: '', // 供应商
        warehouse_id: '',
        expected_return_date: '', // 采购日期
        materialsTableData: [],
        remark: '',
        fileLists: [],
        autoContactTrade: true // 自动关联入库单据
      },
      formRules: {
        transfer_warehouse_id: [{ required: true, message: '请选择调拨仓库', trigger: 'change' }],
        expected_return_date: [{ required: true, message: '请选择日期', trigger: 'change' }],
        warehouse_id: [{ required: true, message: '请选择入库仓库', trigger: 'change' }],
        count: [{ required: true, validator: this.validateEmptyCount, trigger: 'change' }]
      },
      pickerPurchaseTimeOptions: {
        disabledDate(time) {
          return time.getTime() < dayjs().subtract(1, 'day').valueOf()
        }
      },
      // 入库物资
      materialsTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '当前库存', key: 'limit_unit_stock' },
        { label: '调拨数量', key: 'count', type: 'slot', slotName: 'transferCount' },
        { label: '最小单位', key: 'unit_name', type: 'slot', slotName: 'unitName' },
        { label: '关联入库单据', key: 'contact_trades', type: 'slot', slotName: 'associated' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      // 物资数据
      materialsTableData: [],
      currentMaterialsTableData: [],
      materialsPage: 1,
      materialsPageSize: 10,
      errorTableData: '',
      warehouseList: [], // 供应商列表
      unitList: [], // 单位列表
      // 选择物资/模板/询价单等弹窗
      showChooseDialog: false, // 是否开启弹窗
      dialogChooseLoading: false, // 弹窗loading
      dialogChooseTitle: '选择物资',
      dialogChooseType: '', // 弹窗的状态，add/modify
      dialogChooseData: {}, // 弹窗数据
      remoteChooseLoading: false,
      dialogChooseTableSettings: [],
      dialogChooseSearchSetting: {},
      dialogChooseParams: {
        warehouse_id: +this.$route.query.warehouse_id
      },
      dialogChooseApi: '1', // 请求的接口
      collapseTitle: '查看附件',
      dialogSelect: [],
      showSelectLen: false,
      dialogRowKey: 'materials_id',
      dialogData: {}
    }
  },
  computed: {
    // 花销大，因为每次materialsTableData数据变化都会触发它重新计算
    // currentMaterialsTableData() {
    //   return this.materialsTableData.slice((this.materialsPage - 1) * this.materialsPageSize, this.materialsPage * this.materialsPageSize)
    // }
    previewList() {
      const result = this.formData.fileLists.map(v => v.url)
      this.setPreviewListTitle(result)
      return result
    }
  },
  created() {
    this.type = this.$route.params.type || 'add'
    if (this.$route.query.warehouse_id) {
      this.formData.warehouse_id = this.$route.query.warehouse_id
    }
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getWarehouseList()
      if (this.type === 'midify') {
        // try {
        //   let data = JSON.parse(this.$route.query.data)
        // } catch (error) {
        // }
      }
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取仓库数据列表
    async getWarehouseList() {
      const res = await this.$apis.apiBackgroundDrpWarehouseListPost({
        status: 'enable',
        page: 1,
        page_size: 999999
      })
      if (res.code === 0) {
        // 过滤掉当前的仓库，调拨单不允许选自己
        this.warehouseList = res.data.results.filter(v => v.id !== Number(this.queryData.warehouse_id))
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化Materials分页数据
    initCurrentTableMaterials() {
      this.currentMaterialsTableData = this.formData.materialsTableData.slice(
        (this.materialsPage - 1) * this.materialsPageSize,
        this.materialsPage * this.materialsPageSize
      )
    },
    // 获取实际materialsTableData的index，这样做就不用重新遍历一次数据了
    getMaterialsIndex(index) {
      return (this.materialsPage - 1) * this.materialsPageSize + index
    },
    onMaterialsPaginationChange(val) {
      this.materialsPage = val.current
      this.materialsPageSize = val.pageSize
      this.initCurrentTableMaterials()
    },
    // 校验输入
    validateEmptyCount(rule, value, callback, source, options) {
      // 校验走到这应该都会有这数据的
      // field：tableData.0.changeCount，tableData.1.changeCount，还有tableData[0].changeCount这种（先不做这种的兼容吧，记住prop的设置不能用这种）
      // 切割拿值做校验
      try {
        const fieldArr = rule.field.split('.')
        console.log(rule, value, fieldArr)
        const row = this.formData[fieldArr[0]][Number(fieldArr[1])]
        // console.log(111, row)
        if (value) {
          if (!oneDecimal(value)) {
            callback(new Error('格式错误'))
          } else {
            if (row && Number(row.current_num) < Number(value)) {
              callback(new Error('不能大于当前库存'))
            } else {
              callback()
            }
          }
        } else {
          callback(new Error('请检查调拨数量'))
        }
      } catch (error) {
        console.log(error)
        callback(new Error('校验出错了'))
      }
    },
    // 添加物资
    addMaterials(type, data, index) {
      this.dialogChooseType = type
      if (data) {
        this.dialogData = data
        this.dialogData.index = index
      }
      // 初始化分页数据
      // this.initCurrentTableMaterials()
      if (type === 'transfer_order') {
        this.showSelectLen = true
        this.dialogChooseTitle = '添加物资'
        this.dialogChooseApi = 'apiBackgroundDrpTransferInfoGetMaterialsListPost'
        this.dialogChooseParams = {
          warehouse_id: +this.$route.query.warehouse_id
        }
        this.dialogChooseSearchSetting = {}
        this.dialogChooseTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '物资名称', key: 'materials_name' },
          { label: '最小单位', key: 'limit_unit', type: 'slot', slotName: 'limitUnit' },
          { label: '当前库存', key: 'limit_unit_stock' },
          { label: '调拨数量', key: 'changeCount', type: 'slot', slotName: 'inventory' }
        ]
      }
      this.dialogSelect = []
      // 选择采购单
      if (type === 'associated_purchase') {
        this.dialogChooseTitle = '关联入库单据'
        this.showSelectLen = false
        this.dialogChooseApi = 'apiBackgroundDrpEntryInfoGetMaterialsTradePost'
        this.dialogChooseParams = {
          warehouse_id: +this.$route.query.warehouse_id,
          materials_id: data.materials_id,
          materials_name: data.materials_name, // 这个是字段是为了传过去，再设置到返回的数据里面，其实接口不需要
          limit_unit_id: data.limit_unit_id,
          limit_unit_item: data.limit_unit_item, // 这个是字段是为了传过去，再设置到返回的数据里面，其实接口不需要
          total_limit_unit_stock: data.limit_unit_stock // 这个是字段是为了传过去，再设置到返回的数据里面，拿到当前最小单位的总库存，其实接口不需要
        }
        // 自定义id{trade_no_物资id_供应商id}，物资id可能会重复，因为同一个物资可能关联多个不同的供应商
        this.dialogRowKey = 'custom_id'
        this.dialogChooseSearchSetting = {}
        this.dialogChooseTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '单据编号', key: 'trade_no' },
          { label: '供应商名称', key: 'supplier_manage_name' },
          { label: '入库时间', key: 'entry_time' },
          { label: '当前库存', key: 'current_count' },
          { label: '输入库存', key: 'changeCount', type: 'slot', slotName: 'inventory' }
        ]
        // 还原下已选的入库单数据吧
        if (data.contact_trade_list.length > 0) {
          this.dialogSelect = data.contact_trade_list
        }
      }
      this.showChooseDialog = true
    },
    // 修改数量时如果是自动关联的需要调接口更新关联单号数据
    changeCountHandle: debounce(function(e, row, index) {
      this.getAutoMaterialsTrade([row])
    }, 300),
    // 删除物资
    deleteMaterials(index) {
      this.formData.materialsTableData.splice(this.getMaterialsIndex(index), 1)
      if (this.formData.materialsTableData.length !== 0) {
        this.materialsPage = this.$computedTotalPageSize(
          this.formData.materialsTableData.length,
          this.materialsPageSize
        )
      } else {
        this.materialsPage = 1
      }
      // 初始化分页数据
      this.initCurrentTableMaterials()
    },
    // 检查表单数据
    checkedFormData() {
      let nopass = this.formData.materialsTableData.some(v => {
        return !v.count || (Number(v.count) > v.current_num)
      })
      if (nopass) {
        this.errorTableData = '请检查调拨数量!'
      } else {
        this.errorTableData = ''
      }
      // nopass = false
      return !nopass
    },
    // 格式华参数
    formatParams() {
      let params = {
        warehouse_id: this.formData.warehouse_id,
        transfer_warehouse_id: this.formData.transfer_warehouse_id,
        expected_return_date: this.formData.expected_return_date,
        remark: this.formData.remark,
        extra: {
          images: this.previewList
        }
      }
      params.materials_data = this.formData.materialsTableData.map(v => {
        return {
          ...v,
          contact_trade_list: v.contact_trade_list.map(v => {
            return {
              trade_no: v.trade_no,
              count: v.changeCount,
              supplier_manage_id: v.supplier_manage_id
            }
          })
        }
      })
      return params
    },
    validateForm(type) {
      if (this.checkedFormData()) {
        let params = this.formatParams()
        if (!this.formData.materialsTableData.length) return this.$message.error('请先选择物资！')
        this.sendFormdata(params)
      } else {
        this.$message.error('请认真检查表单数据！')
        this.checkedFormData()
      }
    },
    // 页面按钮点击
    async submitFormHandle(type) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.validateForm(type)
        } else {
          this.checkedFormData()
        }
      })
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            // this.$backVisitedViewsPath(this.$route.path, 'ProcureList')
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 发送数据
    async sendFormdata(params) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDrpTransferInfoAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        this.$backVisitedViewsPath(this.$route.path, 'InquiryOrderList')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 选择物资确定回调事件
    async confirmChooseHandle(res) {
      console.log(1212, res.data)
      this.showChooseDialog = false
      let result = []
      // 选择关联入库单据确定
      if (res.type === 'associated_purchase') {
        // 选择关联入库单据需要反选
        // this.dialogSelect = res.select.map(v => v[this.dialogRowKey])
        // this.formData.materialsTableData.forEach(v => {
        //   if (v.materials_id === this.dialogData.materials_id) {
        //     this.$set(v, 'contact_trade_list', res.data)
        //     let changeCountSum = res.data.reduce((prev, next) => {
        //       return NP.plus(prev, next.changeCount||0)
        //     }, 0)
        //     this.$set(v, 'count', changeCountSum)
        //   }
        // })
        // 处理关联单据合并到表格
        // 同数据同单号直接覆盖/同数据不同单号新增/不同的数据直接push
        res.data.map(v => {
          v.key = `${v.materials_id}_${v.supplier_manage_id}_${v.limit_unit_id}_${v.entry_price}`
          let tableindex = this.formData.materialsTableData.findIndex(tableitem => tableitem.key === v.key)
          if (tableindex !== -1) { // 同类型数据
            let tradeindex = this.formData.materialsTableData[tableindex].contact_trade_list.findIndex(trade => trade.trade_no === v.trade_no)
            if (tradeindex !== -1) { // 同数据同单号直接覆盖
              this.formData.materialsTableData[tableindex].contact_trade_list[tradeindex].changeCount = v.changeCount
              this.formData.materialsTableData[tableindex].contact_trade_list[tradeindex].count = v.changeCount
            } else { // 同数据不同单号新增
              v.count = v.changeCount
              this.formData.materialsTableData[tableindex].contact_trade_list.push(v)
            }
          } else { // 不同的数据直接push
            v.limit_unit_stock = v.total_limit_unit_stock // 当前单位最小库存=当前最小单位的总库存
            v.count = v.changeCount // 出库数量
            v.contact_trade_list = [v] // 关联单据
            console.log(989898, v)
            this.formData.materialsTableData.push(v)
          }
        })
        // 不自动关联的时候，点击确定关联，要把原来的那条数据删掉
        if (!this.formData.autoContactTrade && !this.dialogData.contact_trade_list.length) {
          let index = this.formData.materialsTableData.findIndex(tableitem => tableitem === this.dialogData)
          this.formData.materialsTableData.splice(index, 1)
        }
        this.formData.materialsTableData.map(item => {
          if (item.contact_trade_list.length) { // 关联了单据
            item.count = item.contact_trade_list.reduce((prev, next) => {
              return NP.plus(prev, next.count || 0)
            }, 0)
          }
        })
      } else {
        // 出来新添加的数据
        res.data.forEach(v => {
          let item = {
            materials_name: v.materials_name,
            materials_id: v.materials_id,
            current_num: v.current_num,
            count: v.changeCount,
            unit_name: v.unit_name,
            unit_id: v.unit_id,
            error: '',
            contact_trade_list: [],
            // 出库需要最小单位列表
            limit_unit_management: v.limit_unit_management,
            limit_unit_id: v.limit_unit_id,
            limit_unit_item: v.limit_unit_item,
            limit_unit_stock: v.limit_unit_stock
          }
          // 因为供应商是多选，但在外层列表中是单选的情况，得拆分
          result.push(item)
        })
        // 自动关联物资直接调用接口
        if (this.formData.autoContactTrade) {
          await this.getAutoMaterialsTrade(result)
        } else {
          if (this.formData.materialsTableData.length > 0) {
            this.formData.materialsTableData = this.mergeArrays(this.formData.materialsTableData, result)
          } else {
            this.formData.materialsTableData = result
          }
        }
      }

      this.initCurrentTableMaterials()
    },
    // 自动关联入库单
    async getAutoMaterialsTrade(materials) {
      let params = {
        warehouse_id: +this.queryData.warehouse_id,
        materials_data: materials.map(v => {
          return {
            materials_id: v.materials_id,
            count: +v.count,
            limit_unit_id: v.limit_unit_id
          }
        })
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDrpEntryInfoAutoGetMaterialsTradePost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showImportDialog = false
        this.setAutoMaterialsTrade(materials, res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置自动关联的入库单数据
    setAutoMaterialsTrade(materialsList, autoMaterials) {
      // 相同的物资处理
      let autoMaterialsObj = {}
      if (autoMaterials && autoMaterials.length > 0) {
        autoMaterials.forEach(v => {
          // 同步下数据字段
          v.count = v.use_count
          // 这个要同步下，因为提交时会用到，这是关联物资里面的数量
          v.changeCount = v.use_count
          let current = v
          // if (autoMaterialsObj[v.materials_id]) {
          //   autoMaterialsObj[v.materials_id].push(current)
          // } else {
          //   autoMaterialsObj[v.materials_id] = [current]
          // }
          // 区分不同单价/供应商/单位的物资
          if (autoMaterialsObj[`${v.materials_id}_${v.supplier_manage_id}_${v.limit_unit_id}_${v.entry_price}`]) {
            autoMaterialsObj[`${v.materials_id}_${v.supplier_manage_id}_${v.limit_unit_id}_${v.entry_price}`].push(current)
          } else {
            autoMaterialsObj[`${v.materials_id}_${v.supplier_manage_id}_${v.limit_unit_id}_${v.entry_price}`] = [current]
          }
        })
      }
      let materials_list = [] // 不同单价/供应商/单位的物资，要分成多条，重新搞个数组保存
      for (let key in autoMaterialsObj) {
        let keyList = key.split('_')
        // 从弹窗来的物资信息
        let materials = materialsList.find(current => current.materials_id === Number(keyList[0]))
        // 库存数量
        let count = autoMaterialsObj[key].reduce((prev, next) => {
          return NP.plus(prev, next.count || 0)
        }, 0)
        // 成本价
        let entry_price = autoMaterialsObj[key].length ? autoMaterialsObj[key][0].entry_price : 0
        materials_list.push({
          ...materials,
          key: key,
          contact_trade_list: autoMaterialsObj[key],
          count: count,
          entry_price: entry_price
        })
      }
      // materials.forEach(v => {
      //   // 往添加的物资里添加关联的入库单数据
      //   if (autoMaterialsObj[v.materials_id]) {
      //     // 关联入库单据
      //     v.contact_trade_list = autoMaterialsObj[v.materials_id]
      //     // 同步下自动关联的数量，因为有库存限制
      //     v.count = autoMaterialsObj[v.materials_id].reduce((prev, next) => {
      //       return NP.plus(prev, next.count || 0)
      //     }, 0)
      //   }
      // })
      if (this.formData.materialsTableData.length > 0) {
        this.formData.materialsTableData = this.mergeArrays(this.formData.materialsTableData, materials_list, true)
      } else {
        // 自动关联的话如果
        this.formData.materialsTableData = materials_list
      }
    },
    // 合并新旧数据，以供应商id和物资id作为唯一值，相同的数据需要合并，数量这些需要累加起来
    // 当已添加的物资列表中存在关联过入库单数据的，但新增增物资时又是选了同一个物资，则需要清除旧的物资数据中的关联入库单数据，自动关联的物资是替换的形式，其它的为累加
    mergeArrays(tableData, newData, notAccumulation) {
      // 创建一个空对象用于存储合并的结果
      let merged = {}
      // 遍历 tableData 数组
      for (let i = 0; i < tableData.length; i++) {
        let current = tableData[i]
        merged[current.key] = current
      }
      newData.forEach(item => {
        // 存在相同数据，合并数量并重新计算合计
        let key = item.key
        if (merged[key]) {
          merged[key].contact_trade_list = item.contact_trade_list
          // notAccumulation 是否不需要累加出库数量
          // 自动关联是不做累加的
          // 非自动关联新增物资时如果是同一物资时需要累加
          if (notAccumulation) {
            merged[key].count = item.count
          } else {
            merged[key].count = NP.plus(merged[key].count, item.count)
          }
        } else {
        // 不存在直接往merged里面新增
          merged[key] = item
        }
      })
      return Object.values(merged)
    },
    getFileLists(fileLists) {
      this.formData.fileLists = fileLists
    },
    // 上传图片前钩子
    beforeUpload(file) {
      let uploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      if (!uploadType.includes(getSuffix(file.name))) {
        this.$message.error('请检查上传文件格式！')
        return false
      }
      const isLt20M = file.size / 1024 / 1024 <= 20
      if (!isLt20M) {
        this.$message.error('上传附件大小不能超过 20M')
      }
      return isLt20M
    },
    //
    setPreviewListTitle(result) {
      this.collapseTitle = '查看附件(' + result.length + ')'
    },
    // 删除图片
    deleteUploadImg(index) {
      const fileUploadRef = this.$refs.fileUploadRef
      if (this.formData.fileLists[index]) {
        fileUploadRef && fileUploadRef.spliceFileData(this.formData.fileLists[index].uid)
      }
      this.formData.fileLists.splice(index, 1)
    }
  }
}
</script>

<style lang="scss">
.AddTransferOrder {
  position: relative;
  h3 {
    margin: 0;
  }
  .m-b-0 {
    margin-bottom: 0;
  }
  .w-280 {
    width: 280px;
  }
  .w-160 {
    width: 160px !important;
  }
  .w-auto {
    width: 300px;
  }
  .error {
    ::v-deep .el-input__inner {
      border-color: red;
    }
  }
  .red {
    color: red;
    .ps-origin {
      color: red !important;
    }
  }
  .upload-img {
    width: 90px;
    height: 90px;
  }
  .img-item {
    display: inline-block;
    position: relative;
    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
    .img-tools {
      display: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #ffffff;
      font-size: 16px;
      transition: 0.3s;
      i {
        cursor: pointer;
        color: #ff9b45;
      }
    }
    &:hover {
      .img-tools {
        display: inline-block;
      }
    }
  }
}
.el-date-table td.selected span {
  color: #fff !important;
}
.right-btn {
  position: absolute;
  right: 20px;
  top: 20px;
}
</style>
