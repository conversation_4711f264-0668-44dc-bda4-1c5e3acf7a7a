<template>
  <!-- 排班管理-->
  <div class="custom-calendar">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- 头部-->
    <div class="custom-header">
      <div class="ps-flex flex-center">
        <span class="pre-btn">
          <i class="el-icon-arrow-left" @click="selectDate('prev')"></i>
        </span>
        <div class="m-l-10 m-r-10">{{ getCurrentMonth() }}</div>
        <span class="next-btn">
          <i class="el-icon-arrow-right" @click="selectDate('next')"></i>
        </span>
        <div class="m-l-20">排班管理表</div>
      </div>
      <div class="btn-layout">
        <button-icon color="plain" @click="showRecord">历史记录</button-icon>
        <!-- <button-icon color="plain" @click="gotoPrint">打印</button-icon> -->
        <!-- <button-icon color="origin" @click="saveAll">保存</button-icon> -->
      </div>
    </div>
    <el-calendar ref="calendar" v-model="currentDate" :first-day-of-week="7" value-format="YYYY-MM"
      v-loading="isLoading">
      <template slot="dateCell" slot-scope="{date, data}">
        <div @click="handlerShowChooseUser(data)">
          <div class="date-content-header">
            <p :class="data.isSelected ? 'is-selected' : ''">
              {{ data.day.split('-').slice(1).join('-') }}
            </p>
            <div class="copy" @click.stop="copyFun(data.day)">复制</div>
          </div>
          <div class="date-content-body">
            <div :class="['name', index > 0 ? 'm-l-5' : '']" v-for="(item, index) in dateRangePersonInfo[data.day]"
              :key="index">
              {{ item.name }}
            </div>
          </div>
        </div>
      </template>
    </el-calendar>
    <!--选择人员弹窗-->
    <add-workforce-dialog ref="chooseMainUserDialog" :isshow.sync="isShowMainUserDialog"
      @confirm="confirmMainUserDialog" dialog-type="default" :use-date="chooseDate" @close="closeMainUserDialog" />
    <copy-workforce-dialog ref="copyDialog" :isshow.sync="isShowCopyDialog" @confirm="confirmCopyDialog"
      dialog-type="default" :use-date="chooseDate" @close="closeCopyDialog" />
    <record-workforce-dialog ref="recordDialog" :isshow.sync="isShowRecordDialog"
      dialog-type="default"  @close="closeRecordDialog" />
  </div>
</template>

<script>
import { deepClone, parseTime, to } from '@/utils';
import AddWorkforceDialog from './compontents/AddWorkforceDialog.vue'
import CopyWorkforceDialog from './compontents/CopyWorkforceDialog.vue'
import RecordWorkforceDialog from './compontents/RecordWorkforceDialog.vue'

export default {
  name: 'WorkforceManagement',
  data() {
    return {
      currentDate: new Date(),
      isLoading: false,
      dateRangePersonInfo: {}, // 日期范围人员信息信息
      isShowMainUserDialog: false,
      chooseDate: '',
      isShowCopyDialog: false, // 是否显示复制弹窗
      isShowRecordDialog: false // 是否显示复制弹窗
    }
  },
  components: {
    AddWorkforceDialog,
    CopyWorkforceDialog,
    RecordWorkforceDialog
  },
  mounted() {
    this.getDateRangeInfo()
    console.log(this.$refs.calendar);
  },
  methods: {
    selectDate(type) {
      switch (type) {
        case 'prev':
          this.currentDate = new Date(this.currentDate.setMonth(this.currentDate.getMonth() - 1))
          break;
        case 'next':
          this.currentDate = new Date(this.currentDate.setMonth(this.currentDate.getMonth() + 1))
          break;
        default:
          break;
      }
      this.getDateRangeInfo()
    },
    getCurrentMonth() {
      return parseTime(this.currentDate, '{y}年{m}月')
    },
    refreshHandle() {
      this.currentDate = new Date()
      this.getDateRangeInfo()
    },
    // 获取范围日期排班记录
    async getDateRangeInfo() {
      this.isLoading = true
      // 设置当前月份月初日期
      let startDate = parseTime(this.currentDate, '{y}-{m}') + '-01'
      // 设置当前月份月末日期
      let endDate = parseTime(this.currentDate, '{y}-{m}') + "-" + new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0).getDate();
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionCanteenSafetyManagementGetPersonSchedulePost({
        start_date: startDate,
        end_date: endDate
      }))
      this.isLoading = false
      if (err) {
        return
      }
      if (res && res.code === 0) {
        let data = res.data || []
        if (data && data.length > 0) {
          data.forEach(item => {
            this.$set(this.dateRangePersonInfo, item.use_date, deepClone(item.job_person_list))
          })
          console.log("this.dateRangePersonInfo[day]", this.dateRangePersonInfo);
        }
      }
    },
    // 显示历史记录
    showRecord() {
      this.isShowRecordDialog = true
    },
    // 打印
    gotoPrint() {

    },
    // 保存全部
    saveAll() {
      console.log("保存");
    },
    // 确认选择人员
    confirmMainUserDialog(list, date) {
      this.isShowMainUserDialog = false
      this.$set(this.dateRangePersonInfo, date, deepClone(list))
    },
    // 关闭选择人员弹窗
    closeMainUserDialog() {
      this.isShowMainUserDialog = false
    },
    // 显示选择人员弹窗
    handlerShowChooseUser(row) {
      console.log("handlerShowChooseUser", row);
      this.chooseDate = row.day
      let selectDate = new Date(row.day)
      selectDate.setHours(0, 0, 0, 0)
      let currentDate = new Date()
      currentDate.setHours(0, 0, 0, 0)
      if (selectDate.getTime() < currentDate.getTime()) {
        return this.$message.error("无法修改已过期日期")
      }
      if (this.$refs.chooseMainUserDialog) {
        let list = Reflect.has(this.dateRangePersonInfo, row.day) ? this.dateRangePersonInfo[row.day] : []
        this.$refs.chooseMainUserDialog.setPersonList(list)
      }
      this.isShowMainUserDialog = true
    },
    // 确认复制弹窗
    confirmCopyDialog() {
      this.isShowCopyDialog = false
      this.getDateRangeInfo()
    },
    // 关闭复制弹窗
    closeCopyDialog() {
      this.isShowCopyDialog = false
    },
    // 显示复制弹窗
    copyFun(value) {
      let day = value
      let list = Reflect.has(this.dateRangePersonInfo, day) ? this.dateRangePersonInfo[day] : []
      if (!list || list.length === 0) {
        return this.$message.error("无排班人员进行复制")
      }
      if (this.$refs.chooseMainUserDialog) {
        this.$refs.copyDialog.setPersonList(list)
      }
      this.isShowCopyDialog = true
    },
    // 关闭历史记录弹窗
    closeRecordDialog() {
      this.isShowRecordDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-calendar {
  padding: 20px;
  border-radius: 10px;

  .custom-header {
    background: #fff;
    height: 40px;
    display: flex;
    justify-content: center;
    position: relative;

    .pre-btn {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }

    .next-btn {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }

    .btn-layout {
      position: absolute;
      right: 0;
    }
  }

  ::v-deep .el-calendar__header {
    height: 0;
    padding: 0;

    .el-calendar__title {
      display: none !important;
    }

    .el-calendar__button-group {
      display: none !important;
    }
  }

  ::v-deep .el-calendar-table .el-calendar-day {
    height: fit-content;
  }

  .flex-center {
    align-items: center;
  }

  .date-content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    height: 40px;
    margin-bottom: 10px;
    position: relative;

    .copy {
      position: absolute;
      right: 10px;
      cursor: pointer;
      color: #409eff !important;
    }
  }

  .date-content-body {
    min-height: 100px;
    display: flex;
    flex-wrap: wrap;

    .name {
      min-width: 50px;
      height: 30px;
      border-radius: 5px;
      line-height: 30px;
      text-align: center;
      padding: 0 5px;
      background: #efefef;
    }
  }
}
</style>
