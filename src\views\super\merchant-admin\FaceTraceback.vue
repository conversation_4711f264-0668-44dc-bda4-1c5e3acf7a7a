<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" :isShowSearchBtn="isShowSearchBtn" :isShowResetBtn="isShowResetBtn" :isShowCollapse="isShowCollapse">
      <div slot="customBtn" >
        <el-button :disabled="isLoading" class="ps-origin-btn search-h-r-btn" type="primary" @click="clickConfirmHandle" size="mini">开始查询</el-button>
        <el-button :disabled="isLoading"  @click="resetFormFace" class="search-h-r-btn ps-plain-btn" size="mini">重置</el-button>
      </div>
      <div slot="append" class="face_trace">
        <el-form-item label="查询人脸" >
        <file-upload
          ref="faceFileRef"
          :fileList="fileListsUpload"
          type="enclosure"
          :limit="1"
          :before-upload="beforeUpload"
          @fileLists="getFileLists"
          :class="['avatar-uploader',fileListsUpload.length==0?'border-gray-1':'']"
          :show-file-list="false"
          prefix="tmp"
          v-loading="isUploading"
          @uploadError="uploadError"
        >
          <img v-if="fileListsUpload.length" :src="fileListsUpload[0].url" class="avatar" @click="clearFileHandle">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </file-upload>

      </el-form-item>
      <el-form-item >
          <div>
           <div class="m-t-25 color-gray">
            {{ uploadTipRightTop }}
           </div>
           <div class="color-gray">
            {{ uploadTipRightBottom }}
           </div>
          </div>
      </el-form-item>
      </div>
    </search-form>
    <el-card class="box-card" v-if="isShowPopImg" v-drag>
      <div  class="flex-between m-b-10" >
        <span>人脸照片</span>
        <i class="el-icon-close" style="float: right;" @click="closeCard"></i>
      </div>
      <img :src="faceImgUrl" class="face-img-big"  />
    </el-card>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          height="460"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #face="{row}">
            <div>
              <img :src="row.face_url" alt class="face-img"  @click="handlerImgClick(row.face_url)"/>
            </div>
            </template>
            <template #sex="{row}">
              {{ getSexName(row.gender) }}
            </template>
            <template #score="{row}">
              {{  row.score | formatScoreNotRounding}}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showDialogHandle(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->

    </div>
  </div>
</template>

<script>
import { deepClone, to } from '@/utils'

export default {
  name: "FaceTraceback",
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 5, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableDataClone: [],
      tableSettings: [
        { label: '排序', key: 'index' },
        { label: '近似人脸', key: 'face_url', type: "slot", slotName: "face" },
        { label: '人员编号', key: 'member_card' },
        { label: '姓名', key: 'card_name' },
        { label: '手机号', key: 'card_phone' },
        { label: '性别', key: 'gender', type: "slot", slotName: "sex" },
        { label: '识别分数', key: 'score', type: "slot", slotName: "score" },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
      ],
      searchFormSetting: {
        company_id: {
          type: 'CompanySelect',
          value: [],
          label: '组织名称',
          dataList: [],
          multiple: false,
          checkStrictly: true,
          clearable: true,
          companyOpts:
          {
            label: 'name',
            value: 'company'
          },
          role: 'super'
        },
        count: {
          type: 'input',
          value: '',
          label: '人脸回溯数量',
          placeholder: '请输入查询数量（上限50条）'
        }

      },
      fileListsUpload: [],
      isShowSearchBtn: false,
      isShowResetBtn: false,
      uploadTipRightTop: '上传：查询人脸',
      uploadTipRightBottom: '仅支持jpg、png、bmp格式，大小不超过10M',
      isShowPopImg: false, // 是否显示大图
      faceImgUrl: '', // face图片
      isUploading: false,
      isShowCollapse: false
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.resetFormFace()
      this.tableData = []
      this.initLoad()
    },
    /**
     * 初始化数据
     */
    initLoad() {
      console.log("faceTrace初始化");
    },
    /**
     * 搜索点击
     */
    clickConfirmHandle() {
      console.log("searchHandle", this.searchFormSetting, parseInt(this.searchFormSetting.count.value));
      if (this.searchFormSetting.company_id.value.length === 0) {
        this.$message.error('请选择组织名称');
        return
      }
      if (this.searchFormSetting.count.value.length === 0 || this.searchFormSetting.count.value === "0") {
        this.$message.error('请输入人脸回溯数量');
        return
      }

      let numberReg = /^\d+$/
      if (this.searchFormSetting.count.value.length > 0 && !numberReg.test(this.searchFormSetting.count.value)) {
        this.$message.error("人脸回溯数量必须为整数");
        return
      }
      if (parseInt(this.searchFormSetting.count.value) > 50) {
        this.$message.error("人脸回溯数量一次查询的数量必须低于50");
        return
      }

      if (this.fileListsUpload.length === 0) {
        this.$message.error('请选择查询人脸');
        return
      }
      this.getFaceList()
    },
    /**
     * 查看详情
     * @param {*} e
     */
    showDialogHandle(e) {
      var personId = e.member_card
      this.$router.push({
        name: "FaceTracebackDetail",
        query: {
          id: personId
        }
      });
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      var moreList = this.tableDataClone.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
      this.tableData = deepClone(moreList)
    },
    /**
     * 上传前检测
     * @param {} file
     */
    beforeUpload(file) {
      let unUploadType = ['.jpg', '.png', '.bmp']
      if (!unUploadType.includes(this.getSuffix(file.name))) {
        this.$message.error('上传图片只能是 jpg/png 格式')
        return false
      }
      const isLt10M = (file.size / 1024 / 1024) < 10;
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!');
        return false
      }
      this.isUploading = true
    },
    /**
     * 获取文件后缀名
     *
     * */
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    },
    /**
     * 上传成功获取文件
     * @param {*} fileLists
     */
    getFileLists(fileLists) {
      this.isUploading = false
      this.fileListsUpload = deepClone(fileLists)
      console.log('fileLists', fileLists)
    },
    /**
     * 获取自定义人脸回溯列表
     */
    async getFaceList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        face_url: this.fileListsUpload[0].url
        // page: this.currentPage,
        // page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminFaceSearchFaceTracebackPost(params))
      console.log("apiBackgroundAdminFaceSearchFaceTracebackPost", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        var resultList = res.data || []
        if (Array.isArray(resultList) && resultList.length > 0) {
          // 给列表增加序号
          resultList.map((item, index) => {
            item.index = index + 1
            return item
          })
        }
        this.tableData = deepClone(resultList.slice(0, this.pageSize))
        this.tableDataClone = deepClone(resultList)
        this.totalCount = resultList.length
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'count') {
            params[key] = parseInt(data[key].value)
          } else
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    /**
     * 删除图片
     */
    clearFileHandle() {
      console.log('clear')
      this.$refs.faceFileRef.clearHandle()
      this.fileListsUpload = []
    },
    /**
     * 获取性别名称
     * @param {*} sexCode
     */
    getSexName(sexCode) {
      switch (sexCode) {
        case "MAN":
          return "男"
        case "WOMEN":
          return "女"
        case "OTHER":
          return "其他"
        default:
          break;
      }
    },
    /**
     *重置
     */
    resetFormFace() {
      if (this.$refs.searchRef && Reflect.has(this.$refs.searchRef, "resetForm")) {
        this.$refs.searchRef.resetForm()
      }
      this.clearFileHandle()
    },
    /**
     * 图片点击
     */
    handlerImgClick(imgUrl) {
      console.log("handlerImgClick", this.isShowPopImg, imgUrl);
      if (this.faceImgUrl !== imgUrl) {
        this.faceImgUrl = imgUrl
        this.isShowPopImg = true
      } else {
        this.isShowPopImg = !this.isShowPopImg
      }
    },
    /**
     * 上传失败
     * @param {*} error
     */
    uploadError(error) {
      console.log("error", error);
      this.isUploading = false
    },
    /**
     * 关闭
     */
    closeCard() {
      console.log("closeCard");
      this.isShowPopImg = false
    }

  }
}

</script>

<style lang="scss" scoped>
  .avatar-uploader {
    vertical-align: middle;
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .el-upload:hover {
      border-color: #409EFF;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 80px;
      height: 80px;
      line-height: 80px;
      text-align: center;
    }
    .avatar {
      max-width: 200px;
      max-height: 160px;
      display: block;
    }
  }
  .border-gray-1{
    border: 1px solid #ccc
  }
  .face-img {
    max-width: 40px;
    max-height: 50px;
  }
  .face-img-big{
    max-width: 400px;
    max-height: 400px;
    margin: 0 auto;
  }
  .color-gray{
    color: #8c939d;
  }
  .box-card{
    position: fixed;
    left: 35%;
    top: 20%;
    min-width: 300px;
    min-height: 430px;
    z-index: 1000;
  }
</style>
