<template>
  <el-drawer :visible.sync="visible" :title="title" :loading.sync="isLoading" @close="handleClose" size="678px" append-to-body modal-append-to-body
    :wrapperClosable="false" class="ps-el-drawer">
    <div class="dialog-content m-t-20 m-l-20">
      <el-form ref="cardruleForm" label-width="50px" class="demo-ruleForm" v-if="dialogType === 'default'">
        <div style="display:flex;">
          <el-form-item label="姓名">
            <el-input class="ps-input" @input="changePersonNo" v-model="memberOpts.person_name"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="分组">
            <user-group-select :multiple="true" :collapse-tags="true" class="search-item-w ps-input w-180"
              v-model="memberOpts.selectGroup" placeholder="请下拉选择" @change="handlerChangeGroup"></user-group-select>
          </el-form-item>
          <div class="m-l-20"><el-button class="ps-origin-btn" @click="handlerSearch">搜索</el-button></div>
        </div>
      </el-form>
      <div class="table-wrap">
        <el-table ref="userListRef" :data="memberOpts.tableData" tooltip-effect="dark"
          header-row-class-name="table-header-row" v-loading="isLoading" @selection-change="handleSelectionChange">
          <el-table-column class-name="ps-checkbox" type="selection" width="37"
            :selectable="selectableHandle"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
          <el-table-column prop="card_user_group_alias" label="分组" align="center"></el-table-column>
        </el-table>
      </div>
      <div class="block ps-pagination">
        <!-- <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize" :layout="'total, prev, pager, next, jumper'" :total="totalCount"></pagination> -->
      </div>
    </div>
    <div class="ps-el-drawer-footer m-l-20">
      <el-button :disabled="isBtnLoading" class="ps-cancel-btn" @click="clickCancleHandle">
        取消
      </el-button>
      <el-button class="ps-btn" type="primary" @click="clickConfirmHandle"
        :disabled="isBtnLoading || !selectListId || selectListId.length <= 0" v-loading="isBtnLoading">
        确定
      </el-button>
    </div>
  </el-drawer>
  <!-- end -->
</template>

<script>
import { debounce, to, deepClone, uniqueArrKey } from '@/utils'
import UserGroupSelect from '@/components/UserGroupSelect'

export default {
  name: 'chooseUser',
  props: {
    loading: Boolean,
    isshow: Boolean,
    title: {
      type: String,
      default: '选择人员'
    },
    limitId: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: 'default'
    },
    confirm: Function
  },
  components: {
    UserGroupSelect
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      groupOpts: {
        value: 'id',
        label: 'group_name',
        children: 'children_list',
        checkStrictly: true
      },
      memberOpts: {
        tableData: [],
        person_name: '',
        selectGroup: [],
        departmentList: []
      },
      selectListId: [],
      personList: [], // 已选择人员列表
      pageSize: 999999, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      isBtnLoading: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        // this.getCardUserList()
        console.log("this.visible", this.personList)
        if (this.personList && this.personList.length > 0) {
          let groupIds = []
          this.personList.forEach(item => {
            let groupId = item.card_user_groups || []
            groupIds = groupIds.concat(groupId)
          })
          // 去重
          groupIds = [...new Set(groupIds)]
          this.memberOpts.selectGroup = groupIds
          this.getCardUserList()
        } else if (this.dialogType === 'limit') {
          this.getCardUserList()
        }
      }
    }
  },
  mounted() { },
  methods: {
    // 获取人员
    async getCardUserList() {
      this.isLoading = true
      let params = {
        page_size: this.pageSize,
        page: this.currentPage
      }
      if (this.memberOpts.selectGroup && this.memberOpts.selectGroup.length > 0) {
        params.card_user_group_ids = this.memberOpts.selectGroup
      }
      if (this.dialogType !== 'default') {
        params.discount_limit_id = this.limitId
      } else {
        params.is_self_org = true
      }
      if (this.memberOpts.person_name) { params.person_name = this.memberOpts.person_name }
      const [err, res] = this.dialogType === 'default' ? await to(this.$apis.apiCardServiceCardUserListPost(params)) : await to(this.$apis.apiBackgroundMarketingDiscountLimitCardInfoChosenListPost(params))
      if (err) {
        this.isLoading = false
        return this.$message.error('获取人员信息失败')
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        let list = []
        if (this.dialogType === 'default') {
          list = list.concat(deepClone(this.personList))
        }
        list = list.concat(data.results)
        list = uniqueArrKey(list, 'id')
        list = await this.getChoosePersonList(list)
        this.isLoading = false
        this.totalCount = data.count || 0
        this.memberOpts.tableData = list
        this.memberOpts.tableData = this.memberOpts.tableData.map(user => {
          user.card_user_group_alias = user.card_user_group_alias && Array.isArray(user.card_user_group_alias) ? user.card_user_group_alias.join('，') : user.card_user_group_alias
          if (this.dialogType === 'default') {
            this.personList.forEach(selectId => {
              if (user.id === selectId.id && this.$refs.userListRef) {
                this.$nextTick(() => {
                  console.log("user 1111", user.id);
                  this.$refs.userListRef.toggleRowSelection(user, true);
                });
              }
            })
          } else {
            this.$nextTick(() => {
              if (user.is_enable && this.$refs.userListRef) {
                this.$refs.userListRef.toggleRowSelection(user, true);
              }
            });
          }
          return user
        })
        console.log("this.memberOpts.tableData", this.memberOpts.tableData);
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分组选择监听
    handlerChangeGroup(list) {
      // this.currentPage = 1
      // this.getCardUserList()
    },
    // 输入监听
    changePersonNo: debounce(function () {
      // this.currentPage = 1
      // this.getCardUserList()
    }, 300),
    // 人员选择dialog 多选框选中事件
    handleSelectionChange(val) {
      console.log("val", val);
      this.selectListId = []
      if (this.dialogType === 'default') {
        let data = Object.freeze(val) // 解除下监听吧，节约点资源
        data.map(item => { this.selectListId.push(item.id) })
      } else {
        let ids = val.map(item => item.id)
        this.memberOpts.tableData.forEach(item => {
          if (!ids.includes(item.id)) {
            this.selectListId.push(item.id)
          }
        })
      }
    },
    clickCancleHandle() {
      this.handleClose()
    },
    // 确认选择
    async clickConfirmHandle() {
      if (!this.selectListId.length) {
        return this.$message.error(this.dialogType === 'default' ? "请选择用户" : "请反选要取消的人员")
      }
      if (this.dialogType === 'default') {
        this.updatePersonList()
        this.$emit('confirm', this.personList)
      } else {
        this.isBtnLoading = true
        let params = {
          ids: this.selectListId
        }
        const [err, res] = await to(this.$apis.apiBackgroundMarketingDiscountLimitCardCancelPost(params))
        this.isBtnLoading = false
        if (err) {
          return this.$message.error('取消失败')
        }
        if (res && res.code === 0) {
          this.$message.success('取消成功')
          this.$emit('confirm', res.data)
        } else {
          this.$message.error(res.msg)
        }
      }
    },
    updatePersonList() {
      this.memberOpts.tableData.forEach(item => {
        if (this.selectListId.includes(item.id)) {
          this.personList.push(item)
        }
      })
    },
    // 关闭
    handleClose(e) {
      this.isLoading = false
      this.memberOpts = {
        tableData: [],
        person_name: '',
        selectGroup: []
      }
      this.visible = false
      this.$emit('close', false)
    },
    // 设置人员列表
    setPersonList(list) {
      this.personList = deepClone(list)
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getCardUserList()
    },
    handlerSearch() {
      this.updatePersonList()
      this.currentPage = 1
      this.getCardUserList()
    },
    selectableHandle(row) {
      if (this.dialogType === 'default') {
        return true
      } else {
        return row.is_enable
      }
    },
    getChoosePersonList(list) {
      return new Promise((resolve) => {
        this.$apis.apiBackgroundMarketingDiscountLimitChosenCardInfoIdsPost().then(res => {
          if (res && res.code === 0) {
            let data = res.data || {}
            let cardInfoIds = data.card_info_ids || []
            let ids = []
            if (cardInfoIds) {
              cardInfoIds.forEach(item => {
                ids = ids.concat(item || [])
              })
            }
            // 去除存ids id的数据
            list = list.filter(item => !ids.includes(item.id))
            resolve(list)
          } else {
            resolve(list)
          }
        }).catch(error => {
          console.log(error);
          resolve(list)
        })
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.table-wrap {
  max-height: 550px;
  overflow: auto;
}
</style>
