<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="origin" type="add" @click="addAndEditCommodity('add')">
              新建
            </button-icon>
            <!-- <button-icon color="plain" type="export" @click="gotoExport">新建</button-icon> -->
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <el-table
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
            class="ps-table-tree"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item">
              <template #image="{ row }">
                <el-image
                  style="width: 65px; height: 62px"
                  :src="row.images_url[0]"
                  :preview-src-list="[row.images_url[0]]"
                ></el-image>
              </template>
              <template #commodityType="{ row }">
                <div>
                  <div>{{ row.commodity_type_alias }}</div>
                  <div v-if="row.commodity_type === 'virtual'">
                    <span>{{ row.virtual_commodity_type_alias }}</span>
                    <span
                      v-if="row.virtual_commodity_type === 'ai_nutritionist' && row.commodity_extra"
                    >
                      {{ row.commodity_extra.count }}次
                    </span>
                    <span v-if="row.virtual_commodity_type === 'member' && row.commodity_extra">
                      {{ row.commodity_extra.day }}天
                    </span>
                  </div>
                  <div v-if="row.commodity_type === 'physical'">
                    <span>商品编码：{{ row.physical_code }}</span>
                  </div>
                </div>
              </template>

              <template #grounding="{ row }">
                <div v-if="!row.is_permanent">
                  <div>{{ row.start_date }}至</div>
                  <div>{{ row.end_date }}</div>
                </div>
                <div v-else>永久</div>
              </template>
              <template #points="{ row }">
                <span>{{ row.commodity_price_type === 'money' ? '--' : row.points }}</span>
              </template>
              <template #buy_stock_num="{ row }">
                <div v-if="row.buy_stock_num === -1">
                  <div>不限制</div>
                </div>
                <div v-else>{{ row.buy_stock_num }}</div>
              </template>
              <template #buy_limit="{ row }">
                <div>
                  <span>{{ row.buy_limit_type_alias }}</span>
                  <span v-if="row.buy_limit_type !== 'non'">{{ row.buy_limit_num }}次</span>
                </div>
              </template>
              <template #status="{ row }">
                <el-switch
                  v-model="row.is_enable"
                  @change="switchStatus(row)"
                  active-color="#ff9b45"
                  inactive-color="#ffcda2"
                ></el-switch>
              </template>
              <template #operation="{ row }">
                <el-button
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="clickDetailsCommodity(row)"
                >
                  查看
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="addAndEditCommodity('modify', row)"
                  :disabled="row.is_enable"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  @click="deleteHandler(row)"
                  :disabled="row.is_enable"
                >
                  删除
                </el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- end -->
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <commodiy-modify-drawer
      v-if="commodiyModifyDrawerVisible"
      :isshow.sync="commodiyModifyDrawerVisible"
      @clickSaveDrawer="clickSaveDrawer"
      :drawerType="drawerType"
      :drawerModifyData="drawerModifyData"
       :collectData="collectData"
    ></commodiy-modify-drawer>
    <commodiy-details-drawer
      v-if="commodiyDetailsDrawerVisible"
      :isshow.sync="commodiyDetailsDrawerVisible"
      :commodiyId="drawerModifyData.id"
    ></commodiy-details-drawer>
  </div>
</template>

<script>
import { to, deepClone, debounce } from '@/utils'
import { POINTS_COMMODITY } from './components/constantsConfig'
import CommodiyModifyDrawer from './components/commodiyModifyDrawer'
import CommodiyDetailsDrawer from './components/commodiyDetailsDrawer.vue'
export default {
  name: 'PointsCommodity',
  components: { CommodiyModifyDrawer, CommodiyDetailsDrawer },
  data() {
    return {
      isLoading: false,
      tableSettings: [
        { label: '商品图片', key: 'image', type: 'slot', slotName: 'image', width: '150px' },
        { label: '商品名称', key: 'name' },
        {
          label: '商品类型',
          key: 'commodity_type_alias',
          type: 'slot',
          slotName: 'commodityType',
          width: '150px'
        },
        { label: '上架时间', key: 'grounding', type: 'slot', slotName: 'grounding' },
        { label: '优先级', key: 'priority' },
        { label: '兑换积分', key: 'points', type: 'slot', slotName: 'points' },
        { label: '支付金额（元）', key: 'fee', type: 'money' },
        { label: '销售数量', key: 'stock_num' },
        { label: '当前库存', key: 'buy_stock_num', type: 'slot', slotName: 'buy_stock_num' },
        { label: '可兑换数', key: 'buy_limit', type: 'slot', slotName: 'buy_limit' },
        { label: '创建时间', key: 'create_time' },
        { label: '修改时间', key: 'update_time' },
        { label: '操作人', key: 'operator_name' },
        { label: '状态', key: 'is_enable', type: 'slot', slotName: 'status' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      total: 0,
      searchFormSetting: deepClone(POINTS_COMMODITY),
      commodiyModifyDrawerVisible: false,
      commodiyDetailsDrawerVisible: false,
      drawerType: '',
      drawerModifyData: {}, // 编辑的数据
      collectData: {}
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getPointsPointsCommodityList()
    },
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getPointsPointsCommodityList()
    }, 300),
    clickSaveDrawer() {
      this.getPointsPointsCommodityList()
    },
    // 获取列表数据
    async getPointsPointsCommodityList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMemberPointsPointsCommodityListPost({
          page: this.currentPage,
          page_size: this.pageSize,
          ...this.formatQueryParams(this.searchFormSetting)
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.total = res.data.count
        this.totalCount = res.data.count
        this.tableData = res.data.results
        this.collectData = res.data.collect
      } else {
        this.$message.error(res.msg)
      }
    },
    addAndEditCommodity(type, row) {
      this.drawerType = type
      if (type === 'modify') {
        this.drawerModifyData = deepClone(row)
      }
      this.commodiyModifyDrawerVisible = true
    },
    clickDetailsCommodity(row) {
      this.drawerModifyData = deepClone(row)
      this.commodiyDetailsDrawerVisible = true
    },
    // 修改状态
    switchStatus(data) {
      this.$confirm(`确定要${data.is_enable ? '上架' : '下架'}该商品？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundMemberPointsPointsCommodityModifyPost(data)
            )
            instance.confirmButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getPointsPointsCommodityList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              data.is_enable = !data.is_enable
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    deleteHandler(row) {
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundMemberPointsPointsCommodityDeletePost({
                ids: [row.id]
              })
            )
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                }
              }
              this.getPointsPointsCommodityList()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getPointsPointsCommodityList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPointsPointsCommodityList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>
<style lang="scss" scoped></style>
