<template>
  <custom-drawer
    title="详情"
    :show.sync="showDrawer"
    :direction="direction"
    :wrapperClosable="wrapperClosable"
    class="drawer-wrapper"
    v-bind="$attrs"
    v-on="$listeners"
    confirm-text="确定"
    @cancel="handlerClose"
  >
    <div class="p-l-20 p-r-20 p-b-20" v-loading="isLoading">
      <div class="tab-box m-b-20">
        <el-radio-group v-model="tabType" size="small" @input="changeTabType">
          <el-radio-button v-for="item in tabList" :key="item.value" :label="item.value" >{{ item.name }}</el-radio-button>
        </el-radio-group>
      </div>
      <ul v-if="tabType === 0" class="list-box">
        <li class="list-item ps-flex-align-c flex-align-c m-b-16">
          <div class="supper-label">供应商名称：</div>
          <div class="supper-value">{{ detailData.name }}</div>
        </li>
        <li class="list-item ps-flex-align-c flex-align-c m-b-16">
          <div class="supper-label">统一社会信用代码：</div>
          <div class="supper-value">{{ detailData.credit_code }}</div>
        </li>
        <li class="list-item ps-flex-align-c flex-align-c m-b-16">
          <div class="supper-label">创建时间：</div>
          <div class="supper-value">{{ detailData.create_time }}</div>
        </li>
        <li class="list-item ps-flex-align-c flex-align-c m-b-16">
          <div class="supper-label">供应类别：</div>
          <div class="supper-value">{{ detailData.supply_category }}</div>
        </li>
        <li class="list-item m-b-16">
          <div class="supper-label">资质信息</div>
          <div class="supper-value-img p-20">
            <div class="img-box inline-block m-r-20" v-for="(img, index) in detailData.imgList" :key="index">
              <el-image
                class="img-item"
                :src="img.url"
                :preview-src-list="computedImgList(detailData.imgList)"
                :initial-index="index"
                fit="cover"
              ></el-image>
              <div class="img-text text-center">{{ computedType(img.name) }}</div>
              <div class="img-time text-center">{{ img.time }}</div>
            </div>
          </div>
        </li>
      </ul>
      <div v-else class="">
        <!-- <el-divider></el-divider>
        <h3 class="m-b-10">应用组织</h3> -->
        <el-table :data="tableData" ref="tableData" size="small" style="width: 100%" stripe header-row-class-name="ps-table-header-row">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button :disabled="row.img_list ? false : true" type="text" size="small" class="ps-text" @click="clickHandler(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <image-viewer  v-model="showViewer" :initial-index="0" :z-index="3000" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
    </div>
  </custom-drawer>
</template>

<script>
import { deepClone } from '@/utils'
import dayjs from 'dayjs'
let prevOverflow = '';

export default {
  name: 'SupplierManagementDetail',
  components: {},
  props: {
    isShow: {
      required: true,
      type: Boolean
    },
    wrapperClosable: {
      type: Boolean,
      default: true
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      tabType: 0,
      tabList: [
        {
          name: '基本信息',
          value: 0
        },
        // {
        //   name: '合同信息',
        //   value: 1
        // },
        {
          name: '车辆信息',
          value: 2
        },
        {
          name: '司机信息',
          value: 3
        },
        {
          name: '应用组织',
          value: 4
        }
      ],
      tableSettingList: {
        '1': [
          { label: '组织名', key: 'organization_name' },
          { label: '合同期限', key: 'contact_name' },
          { label: '合同有效期', key: 'contact_phone' },
          { label: '附件', key: 'operation', type: "slot", slotName: "operation" }
        ],
        '2': [
          { label: '车牌号', key: 'plate_number' },
          { label: '车辆类型', key: 'car_type_alias' },
          { label: '图片', key: 'img_list', type: "slot", slotName: "operation" }
        ],
        '3': [
          { label: '司机姓名', key: 'name' },
          { label: '联系方式', key: 'number' },
          { label: '证件信息', key: 'img_list', type: "slot", slotName: "operation" }
        ],
        '4': [
          { label: '所属项目点', key: 'company_name' },
          { label: '所属组织', key: 'organization_name' },
          { label: '联系人姓名', key: 'contact_name' },
          { label: '联系电话', key: 'contact_phone' },
          { label: '供应商地址', key: 'address' }
        ]
      },
      // tableSettings: [
      //   { label: '所属项目点', key: 'company_name' },
      //   { label: '所属组织', key: 'organization_name' },
      //   { label: '联系人姓名', key: 'contact_name' },
      //   { label: '联系电话', key: 'contact_phone' },
      //   { label: '供应商地址', key: 'address' },
      //   { label: '附件信息', key: 'operation', type: "slot", slotName: "operation" }
      // ],
      detailData: {},
      tableData: [], // table数据
      previewSrcList: [],
      showViewer: false
    }
  },
  computed: {
    showDrawer: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    },
    imageIndex() {
      let previewIndex = 0;
      const initialIndex = this.initialIndex;
      if (initialIndex >= 0) {
        previewIndex = initialIndex;
        return previewIndex;
      }
      const srcIndex = this.previewSrcList.indexOf(this.src);
      if (srcIndex >= 0) {
        previewIndex = srcIndex;
        return previewIndex;
      }
      return previewIndex;
    },
    preview() {
      const { previewSrcList } = this;
      return Array.isArray(previewSrcList) && previewSrcList.length > 0;
    },
    tableSettings() {
      return this.tableSettingList[`${this.tabType}`]
    },
    computedImgList() {
      return d => {
        return d.map(item => item.url)
      }
    },
    computedType() {
      return d => {
        switch (d) {
          case '1':
            return '营业执照'
          case '2':
            return '食品经营许可证'
          case '3':
            return '食品生产许可证'
        }
      }
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    init() {
      console.log(this.infoData)
      this.detailData = deepClone(this.infoData)
      this.tableData = this.detailData.same_supper_info ? this.detailData.same_supper_info : []
      this.detailData.imgList = this.infoData.certification_info && this.infoData.certification_info.length ? this.infoData.certification_info.map(item => {
        let obj = {
          name: item.aptitude,
          time: item.expiry_date ? `${dayjs(item.expiry_date[0]).format('YYYY-MM-DD')} 至 ${dayjs(item.expiry_date[1]).format('YYYY-MM-DD')}` : '--',
          url: item.imgUrl[0]
        }
        return obj
      }) : []
    },
    // 取消
    clickCancelHandle() {
      this.showDrawer = false
    },
    // 确定
    clickConfirmHandle() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.$emit('confirm', this.formData)
          this.resetHandle()
          // 也不需要提交数据直接在这关闭算了
          this.showDrawer = false
        } else {

        }
      })
    },
    // 重置
    resetHandle() {
    },
    clickHandler(row) {
      // don't show viewer when preview is false
      this.previewSrcList = row.img_list || []
      this.showViewer = true;
    },
    closeViewer() {
      this.showViewer = false;
    },
    changeTabType(e) {
      this.tableData = []
      switch (e) {
        case 0:
          this.init()
          break
        case 1:
          this.getContractInformation()
          break
        case 2:
          this.getCarInfo()
          break
        case 3:
          this.getDriverInfo()
          break
        case 4:
          this.getAppOrgInfo()
          break
      }
    },
    getContractInformation() {
      this.$apis.apiBackgroundAdminSupplierManageVendorContractListPost({
        id: this.infoData.id
      }).then(res => {
        if (res.code === 0) {

        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getCarInfo() {
      this.$apis.apiBackgroundAdminSupplierManageVendorVehicleInformationListPost({
        id: this.infoData.id
      }).then(res => {
        if (res.code === 0) {
          this.tableData = res.data.results.map(item => {
            Object.assign(item, {
              img_list: item.car_img
            })
            return item
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getDriverInfo() {
      this.$apis.apiBackgroundAdminSupplierManageVendorDriverInformationListPost({
        id: this.infoData.id
      }).then(res => {
        if (res.code === 0) {
          this.tableData = res.data.results.map(item => {
            Object.assign(item, {
              img_list: [
                ...item.driving_licence,
                ...item.health_certificate
              ]
            })
            return item
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getAppOrgInfo() {
      this.$apis.apiBackgroundAdminSupplierManageVendorOrganizationInformationListPost({
        id: this.infoData.id
      }).then(res => {
        if (res.code === 0) {
          this.tableData = deepClone(res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handlerClose() {
      this.tabType = 0
      this.showDrawer = false
    }
  }
};
</script>

<style lang="scss">
.drawer-wrapper {
  font-size: 14px;
  .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
  .min-btn-w {
    min-width: 80px;
  }
  .el-button+.el-button {
      margin-left: 20px;
  }
  .tab-box {
    .el-radio-group{
      border-radius: 6px;
      &, .el-radio-button__inner{
        color: #fff;
        background-color: #aaaaaa;
      }
    }
    .el-radio-button__inner {
      border: none;
      &:hover {
        background-color: #555555;
        opacity: .6;
      }
    }
    .el-radio-button__orig-radio:checked+.el-radio-button__inner{
      background-color: #555555;
      box-shadow: -1px 0 0 0 #555555;
    }
  }
  .supper-value-img {
    font-size: 13px;
  }
  .img-box {
    .img-item {
      width: 160px;
      height: 92px;
    }
  }
}
</style>
