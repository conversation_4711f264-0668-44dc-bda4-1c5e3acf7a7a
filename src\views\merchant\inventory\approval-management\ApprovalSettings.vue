<template>
  <div class="ApprovalSettings container-wrapper">
    <div class="m-t-20">采购审批/审批设置</div>
    <div v-loading="isLoading" class="table-wrapper content p-20">
      <div class="approval-setting-header">
        <div class="tab-box m-b-20">
          <div class="inline-block">
            <button v-for="tab in tabList" :key="tab.value" :class="['custom-btn', tabType === tab.value ? 'active' : '']" @click="changeTabHandle(tab.value)">{{ tab.name }}</button>
          </div>
          <div class="float-r">
            <button :disabled="isLoading" class="custom-btn active" @click="submitHandle">保存</button>
          </div>
        </div>
        <el-form
          ref="approvalFormRef"
          :model="approvalForm"
          :rules="approvalFormRules"
          label-width="80px"
          size="mini"
        >
          <el-form-item label="审批类型" prop="needApprove">
            <el-radio-group v-model="approvalForm.needApprove" class="ps-radio m-l-20">
              <el-radio :label="1">人工审批</el-radio>
              <el-radio :label="0">无需审批</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-show="approvalForm.needApprove === 1" class="approval-list clearfix" style="width: 100%;">
            <div ref="approvalDrop" class="clearfix float-l">
              <div v-for="(approval, pIndex) in approvalForm.approveList" :key="approval.customId" class="approval-item m-b-20">
                <i class="fixed-drop el-icon-s-operation"></i>
                <div class="approval-right float-r">
                  <div class="approval-top m-b-10">审批方式</div>
                  <div class="">
                    <div class="font-size-12">审批人员有两个/两个以上时，请选择审批方式</div>
                    <el-form-item label="" :prop="`approveList[${pIndex}].approveMethod`" :rules="approvalFormRules.approveMethod" label-width="0">
                      <el-radio-group v-model="approval.approveMethod" class="ps-radio" @change="setApprovalFlow">
                        <div class="m-t-16">
                          <el-radio label="and_approve">会签（需要所有人员审批）</el-radio>
                        </div>
                        <div class="m-t-16">
                          <el-radio :disabled="approval.accountInfo.length <= 1" label="or_approve">或签（多名中的其中一名审批即可）</el-radio>
                        </div>
                      </el-radio-group>
                    </el-form-item>
                  </div>
                </div>
                <div class="approval-left">
                  <div class="approval-top m-b-10">
                    <span class="m-r-10">审批人设置</span>
                    <el-button size="mini" class="ps-btn mini-btn" @click="showDialogHandle('addApprovalSettings', pIndex)">添加</el-button>
                  </div>
                  <div class="account-table">
                    <el-form-item label="" :prop="`approveList[${pIndex}].accountInfo`" :rules="approvalFormRules.accountInfo" label-width="0">
                      <el-table
                        :data="approval.accountInfo"
                        ref="tableData"
                        style="width: 100%"
                        stripe
                        size="small"
                        header-row-class-name="ps-table-header-row"
                        max-height="200px"
                      >
                        <table-column v-for="item in tableSettings" :key="item.key" :col="item">
                          <template #operation="{ row, index }">
                            <el-button type="text" size="small" class="ps-warn" @click="deleteAccountHandle(row, index, pIndex)">删除</el-button>
                          </template>
                        </table-column>
                      </el-table>
                    </el-form-item>
                  </div>
                </div>
                <div class="approval-tool">
                  <i v-if="approvalForm.approveList.length > 1" class="tool-icon el-icon-remove-outline" @click="deleteApprovalHandle(pIndex)"></i>
                  <i class="tool-icon el-icon-circle-plus-outline" @click="addApprovalHandle(pIndex)"></i>
                </div>
              </div>
            </div>
            <div ref="approvalFlow" class="approval-flow float-l p-10">
              <div class="flow-title m-b-10">审批流程预览</div>
              <div class="flow-content m-l-16">
                <div v-for="(item, index) in approvalFlowList" :key="index" class="flow-item m-b-6">
                  <div class="flow-step m-b-6">
                    <span class="flow-step-title">审批{{index + 1}}：</span>
                    <span class="flow-step-name">{{ item.name + item.mode }}</span>
                  </div>
                  <div v-if="index < approvalFlowList.length - 1" class="flow-line"></div>
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <!-- dialog start -->
    <choose-list-dialog
      :showdialog.sync="showChooseDialog"
      :title="dialogChooseTitle"
      :type="dialogChooseType"
      :showSelectLen="false"
      :api="dialogChooseApi"
      :detailApi="dialogChooseDetailApi"
      :search-setting="dialogChooseSearchSetting"
      :table-settings="dialogChooseTableSettings"
      :params="dialogChooseParams"
      @confirmChoose="confirmChooseHandle"
    ></choose-list-dialog>
    <!-- dialog end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import Sortable from 'sortablejs'
import ChooseListDialog from '../components/ChooseListDialog'

export default {
  name: 'ApprovalSettings',
  mixins: [exportExcel],
  components: {
    ChooseListDialog
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      tabType: 'purchase_info',
      tabList: [
        { name: '采购单', value: 'purchase_info' },
        { name: '入库单', value: 'entry_info' },
        { name: '出库单', value: 'exit_info' },
        { name: '退货', value: 'return_info' },
        { name: '申购单', value: 'subscribe_info' }
      ],
      approvalForm: {
        id: '',
        needApprove: 0, // 是否需要审批，1需要，0不需要
        approveList: [ // 审批
          // {
          //   accountInfo: [],
          //   approveMethod: ''
          // }
        ]
      },
      approvalFormRules: {
        needApprove: [{ required: true, message: '请选择审批类型', trigger: 'change' }],
        accountInfo: [{ required: true, message: '请选择审批人', trigger: 'change' }],
        approveMethod: [{ required: true, message: '请选择审批方式', trigger: 'change' }]
      },
      tableSettings: [
        { label: '角色名称', key: 'role_name' },
        { label: '账号名', key: 'member_name' },
        { label: '账号', key: 'username' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      // 审批流程预览
      approvalFlowList: [
        // { name: '', mode: '' }
      ],
      // 选择账号
      showChooseDialog: false, // 是否开启弹窗
      dialogChooseLoading: false, // 弹窗loading
      dialogChooseTitle: '选择审批人',
      dialogChooseType: '', // 弹窗的状态，add/modify
      dialogChooseData: {}, // 弹窗数据
      dialogChooseTableSettings: [],
      dialogChooseSearchSetting: {},
      dialogChooseParams: {},
      dialogChooseApi: '1', // 请求的接口
      dialogChooseDetailApi: '',
      dialogChooseDisabledList: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    async initLoad() {
      if (this.$route.query.tabType) {
        this.changeTabHandle(this.$route.query.tabType, false)
      }
      this.getApprovalSettings()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.getApprovalSettings()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key.indexOf('time') < 0) {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 切换tab
    changeTabHandle(e, changeHash = true) {
      this.tabType = e
      this.searchHandle()
      if (changeHash) {
        this.replaceHash()
      }
    },
    // 保存下参数
    replaceHash() {
      this.$router.replace({
        name: "ApprovalSettings",
        query: {
          tabType: this.tabType
        }
      })
    },
    // 获取列表数据
    async getApprovalSettings() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        approve_type: this.tabType
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpApproveInventoryRuleInfoPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        this.resetForm()
        return
      }
      if (res.code === 0) {
        if (res.data && res.data.id) {
          this.approvalForm.id = res.data.id
          this.approvalForm.needApprove = res.data.need_approve
          if (res.data.approve_json.approve_list.length > 0) {
            this.approvalForm.approveList = res.data.approve_json.approve_list.map((v, index) => {
              return {
                customId: index + 1,
                accountInfo: v.account_info,
                approveMethod: v.approve_method
              }
            })
          } else {
            this.approvalForm.approveList = [
              {
                customId: 1,
                accountInfo: [],
                approveMethod: ''
              }
            ]
          }
        } else {
          this.resetForm()
        }
        this.setApprovalFlow()
        if (this.approvalForm.approveList.length > 0) {
          setTimeout(() => {
            this.initSortable()
          }, 500)
        }
      } else {
        this.$message.error(res.msg)
        this.resetForm()
      }
    },
    resetForm() {
      this.approvalForm.id = ''
      this.approvalForm.needApprove = 0
      this.approvalForm.approveList = [
        {
          customId: 1,
          accountInfo: [],
          approveMethod: ''
        }
      ]
    },
    // 初始化拖拽排序
    initSortable(type) {
      this.$nextTick(_ => {
        const dropEl = this.$refs.approvalDrop
        this.sortWrap = Sortable.create(dropEl, {
          handle: '.fixed-drop',
          animation: 300,
          setData: function(dataTransfer) {
            dataTransfer.setData('Text', '')
          },
          onEnd: evt => {
            const currentRow = this.approvalForm.approveList.splice(evt.oldIndex, 1)[0]
            this.approvalForm.approveList.splice(evt.newIndex, 0, currentRow)
            console.log(232323, this.approvalForm.approveList)
          }
        })
      })
    },
    // 同步下右侧审批流程的高度
    setFlowHeight() {
      this.$nextTick(_ => {
        let approvalDropRect = this.$refs.approvalDrop.getBoundingClientRect()
        this.$refs.approvalFlow.setAttribute('style', `height: ${parseInt(approvalDropRect.height - 20)}px;`)
      })
    },
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = ''
      switch (type) {
        case 'confirmReturn':
          title = '确认归还吗？'
          apiUrl = 'apiBackgroundDrpTransferInfoConfirmReturnPost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break;
        case 'delete':
          title = '确定删除吗？'
          apiUrl = 'apiBackgroundDrpInquiryDeletePost'
          this.showOperationDialog(title, apiUrl, { ids: [data.id] })
          break;
        case 'sales':
          title = '确定销单吗？'
          apiUrl = 'apiBackgroundDrpInquirySubmitPost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break;
        case 'receipt':
          title = '确定收货吗？'
          apiUrl = 'apiBackgroundDrpTransferInfoConfirmReceiptPost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break;
      }
    },
    // 显示操作弹窗
    showOperationDialog(title, apiUrl, params) {
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis[apiUrl](params)
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getApprovalSettings()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 添加账号弹窗
    showDialogHandle(type, index) {
      this.dialogChooseData = {
        index: index
      }
      this.dialogChooseType = type
      this.dialogChooseTitle = '选择审批人'
      this.dialogChooseApi = 'apiBackgroundOrganizationAccountListPost'
      this.dialogChooseParams = {
        status: 1,
        page: 1,
        page_size: 999999
      }
      this.dialogChooseDisabledList = []
      this.approvalForm.approveList.forEach(approve => {
        approve.accountInfo.forEach(v => {
          this.dialogChooseDisabledList.push(v.account_id)
        })
      })
      this.dialogChooseSearchSetting = {
        role_id: {
          type: 'lazySelect',
          label: '角色名称',
          clearable: true,
          value: '',
          apiUrl: 'apiBackgroundOrganizationRoleListPost',
          params: {},
          isLazy: true,
          multiple: false
        },
        username: {
          type: 'input',
          value: '',
          label: '账号',
          placeholder: '请输入'
        }
      }
      this.dialogChooseTableSettings = [
        { label: '', key: 'selection', type: 'selection', reserveSelection: true, selectable: this.selectableHandle },
        { label: '角色名称', key: 'role_alias' },
        { label: '账号名', key: 'member_name' },
        { label: '账号', key: 'username' }
      ]
      this.showChooseDialog = true
    },
    // 添加物资禁止选择当前已有的物资
    selectableHandle(row, index) {
      if (this.dialogChooseDisabledList.includes(row.id)) {
        return false
      } else {
        return true
      }
    },
    closeDialogHandle() {
      this.showChooseDialog = false
    },
    confirmChooseHandle(res) {
      // this.showChooseDialog = false
      console.log(res)
      let result = res.data.map(v => {
        return {
          account_id: v.id,
          member_name: v.member_name,
          username: v.username,
          role_name: v.role_alias
        }
      })
      this.approvalForm.approveList[this.dialogChooseData.index].accountInfo.push(...result)
      this.$refs.approvalFormRef.validateField(`approveList[${this.dialogChooseData.index}].accountInfo`)
      this.dialogChooseData = {}
      this.showChooseDialog = false
      this.setApprovalFlow()
    },
    // 删除
    deleteAccountHandle(row, index, pIndex) {
      this.approvalForm.approveList[pIndex].accountInfo.splice(index, 1)
      if (this.approvalForm.approveList[pIndex].accountInfo.length <= 1) {
        this.$set(this.approvalForm.approveList[pIndex], 'approveMethod', 'and_approve')
        this.setApprovalFlow()
      }
    },
    // 删除审批规则
    deleteApprovalHandle(index) {
      this.approvalForm.approveList.splice(index, 1)
      this.setApprovalFlow()
    },
    // 添加审批规则
    addApprovalHandle(index) {
      this.approvalForm.approveList.splice(index + 1, 0, {
        customId: this.approvalForm.approveList.length + 1,
        accountInfo: [],
        approveMethod: ''
      })
      this.setApprovalFlow()
    },
    // 设置审批流程预览
    setApprovalFlow() {
      this.approvalFlowList = []
      this.approvalForm.approveList.forEach(v => {
        let item = {
          name: v.accountInfo.reduce((prev, next) => {
            if (prev) {
              return prev + '、' + next.member_name
            } else {
              return next.member_name
            }
          }, ''),
          mode: v.approveMethod === '' ? '' : (v.approveMethod === 'and_approve' ? '（会签）' : '（或签）')
        }
        this.approvalFlowList.push(item)
      })
      this.setFlowHeight()
    },
    submitHandle(type, row) {
      this.$refs.approvalFormRef.validate(valid => {
        if (valid) {
          this.sendFormdata(this.formatParams())
        }
      })
    },
    formatParams() {
      console.log(1111, this.tabList.filter(v => v.value === this.tabType))
      let params = {
        name: this.tabList.find(v => v.value === this.tabType).name,
        approve_type: this.tabType,
        need_approve: this.approvalForm.needApprove
      }
      if (this.approvalForm.id) {
        params.id = this.approvalForm.id
      }
      if (this.approvalForm.needApprove) {
        params.approve_json = {
          approve_list: this.approvalForm.approveList.map(approve => {
            return {
              account_ids: approve.accountInfo.map(v => v.account_id),
              approve_method: approve.approveMethod
            }
          })
        }
      } else {
        params.approve_json = {
          approve_list: []
        }
      }
      return params
    },
    // 发送数据
    async sendFormdata(params) {
      let api = 'apiBackgroundDrpApproveModifyPost'
      if (!this.approvalForm.id) {
        api = 'apiBackgroundDrpApproveAddPost'
      }
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis[api](params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        this.getApprovalSettings()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ApprovalSettings{
  .w-70{
    width: 70px;
    &.m-r-20{
      margin-right: 20px;
    }
  }
  .tab-box {
    .custom-btn {
      width: 70px;
      display: inline-block;
      line-height: 1;
      white-space: nowrap;
      cursor: pointer;
      background: #fff;
      border: 1px solid #dcdfe6;
      color: #606266;
      text-align: center;
      box-sizing: border-box;
      outline: none;
      margin: 0;
      transition: .1s;
      font-weight: 500;
      padding: 7px 15px;
      font-size: 12px;
      border-radius: 4px;
      &+.custom-btn {
        margin-left: 10px
      }
      &:hover {
        color: #ff9b45;
        border-color: #ffe1c7;
        background-color: #fff5ec;
      }
      &.active {
        background-color: #ff9b45;
        border-color: #ff9b45;
        color: #fff;
      }
    }
  }
  .approval-list {
    .approval-item {
      position: relative;
      padding: 10px;
      min-width: 768px;
      margin-right: 140px;
      background-color: #d7d7d7;
      border-radius: 4px;
      .fixed-drop {
        position: absolute;
        top: 4px;
        right: 6px;
        font-size: 20px;
        cursor: move;
      }
      .approval-top {
        line-height: 26px;
        overflow: hidden;
      }
      .approval-left {
        width: 60%;
        margin-right: 340px;
        .mini-btn {
          padding: 6px 12px;
        }
      }
      .approval-right {
        width: 260px;
      }
      .approval-tool {
        position: absolute;
        width: 56px;
        right: -60px;
        bottom: 2px;
        .tool-icon {
          cursor: pointer;
          font-size: 24px;
          &+.tool-icon {
            margin-left: 6px;
          }
          &:hover{
            color: #ff9b45;
          }
        }
      }
    }
    .approval-flow {
      // margin-left: 140px;
      width: 300px;
      min-height: 100px;
      background-color: #f2f2f2;
      border-radius: 4px;
      .flow-content {
        .flow-step {
          display: flex;
          .flow-step-title {
            min-width: 60px;
          }
          .flow-step-name {
            flex: 1;
          }
        }
        .flow-line {
          position: relative;
          height: 40px;
          &::after{
            content: '';
            position: absolute;
            left: 16px;
            top: 0;
            bottom: 0;
            border-left: 2px dashed #797979;
          }
        }
      }
    }
  }
  .ps-radio {
    ::v-deep.el-radio__input.is-disabled+span.el-radio__label{
      color: #606266;
    }
  }
}
@media only screen and (max-width: 1260px) {
  .ApprovalSettings {
    .approval-list {
      .approval-item {
        min-width: 568px;
        .approval-left {
          width: 50%;
        }
      }
    }
  }
}
</style>
