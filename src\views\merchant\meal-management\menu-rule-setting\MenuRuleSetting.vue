<template>
  <div class="menu-rule-setting container-wrapper">
    <el-form v-loading="isLoading" :rules="formRuls" :model="formData" ref="formRules" size="small">
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">规则设置</div>
          <div class="p-r-20">
            <span class="p-r-10" style="font-size:13px">菜品提醒规则</span>
            <el-switch v-model="formData.food_tips" active-color="#ff9b45"></el-switch>
          </div>
        </div>
        <div style="padding: 0 20px" v-if="formData.food_tips">
          <div class="ps-flex">
            <el-form-item label="">
              <span class="p-r-10">添加的菜品销量在指定时间</span>
            </el-form-item>
            <el-form-item label="" prop="food_tips_time">
              <el-select
                v-model="formData.food_tips_time"
                class="ps-select w-100 p-r-10"
                popper-class="ps-popper-select"
              >
                <el-option label="近7天" :value="7"></el-option>
                <el-option label="近30天" :value="30"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <span class="m-r-10">内，销售排行位于后</span>
            </el-form-item>
            <el-form-item label="" prop="food_tips_rank">
              <el-select
                v-model="formData.food_tips_rank"
                class="ps-select m-r-10"
                style="width:120px"
                popper-class="ps-popper-select"
              >
                <el-option :label="1" :value="1"></el-option>
                <el-option :label="2" :value="2"></el-option>
                <el-option :label="3" :value="3"></el-option>
                <el-option :label="4" :value="4"></el-option>
                <el-option :label="5" :value="5"></el-option>
                <el-option :label="6" :value="6"></el-option>
                <el-option :label="7" :value="7"></el-option>
                <el-option :label="8" :value="8"></el-option>
                <el-option :label="9" :value="9"></el-option>
                <el-option :label="10" :value="10"></el-option>
              </el-select>
              <span>名进行提醒</span>
            </el-form-item>
          </div>
          <div class="ps-flex">
            <el-form-item label="">
              <span class="p-r-10">添加的菜品在</span>
            </el-form-item>
            <el-form-item label="" prop="food_tips_menu_type">
              <el-select
                v-model="formData.food_tips_menu_type"
                class="ps-select w-100 p-r-10"
                popper-class="ps-popper-select"
              >
                <el-option label="周菜谱" value="week"></el-option>
                <el-option label="月菜谱" value="month"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <span class="m-r-10">菜谱内，重复</span>
            </el-form-item>
            <el-form-item label="" prop="food_tips_repeat_number">
              <el-input
                placeholder=""
                v-model="formData.food_tips_repeat_number"
                class="ps-input w-100 m-r-10"
              ></el-input>
              <span>次，进行提醒</span>
            </el-form-item>
          </div>
          <div v-for="(item, index) in formData.menu_food_category_limit_rule" :key="index + '_seasonal'">
            <div class="ps-flex flex-align-c">
                <el-form-item label="">
                  <span class="p-r-10"> 智能推荐的菜品属性</span>
                </el-form-item>
                <el-form-item class="m-r-10" label="" :prop="'menu_food_category_limit_rule['+index+'].category_id'" :rules="formRuls.category_id">
                  <el-select
                    v-model="item.category_id"
                    class="ps-select w-100 p-r-10"
                    popper-class="ps-popper-select"
                    filterable
                    @change="changeIntAttrId(item)"
                  >
                  <el-option label="全选" :value="0"></el-option>
                  <el-option
                    v-for="food in foodAttributeList"
                    :key="food.key"
                    :label="food.label"
                    :value="food.key"
                  ></el-option>
                  </el-select>
                  <span>在</span>
                </el-form-item>
                <el-form-item label="" :prop="'menu_food_category_limit_rule['+index+'].menu_type'" :rules="formRuls.menu_type">
                  <el-select
                    v-model="item.menu_type"
                    class="ps-select w-100 p-r-10"
                    popper-class="ps-popper-select"
                    :disabled="item.category_id === ''"
                  >
                  <el-option
                    v-for="menu in menuTypeListData"
                    :key="menu.value"
                    :label="menu.name"
                    :value="menu.value"
                    :disabled="disabledMenuType(item.category_id, menu.value)"
                  ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" :prop="'menu_food_category_limit_rule['+index+'].days'" :rules="formRuls.days">
                  <el-input
                    placeholder=""
                    v-model="item.days"
                    class="ps-input w-100 m-r-10"
                  ></el-input>
                  <span>天内，</span>
                </el-form-item>
                <el-form-item label="" :prop="'menu_food_category_limit_rule['+index+'].food_tips_repeat_number'" :rules="formRuls.food_tips_repeat_number">
                  <span class="p-r-10">允许重复</span>
                  <el-input
                    placeholder=""
                    v-model="item.food_tips_repeat_number"
                    class="ps-input w-100 m-r-10"
                  ></el-input>
                  <span>次</span>
                </el-form-item>
                <el-form-item label="" prop="">
                  <el-button class="p-l-20" style="font-size: 25px;" icon="el-icon-circle-plus" type="text" @click="clickAddMenuFoodLimitRule('menu_food_category_limit_rule')"></el-button>
                  <el-button  v-if="formData.menu_food_category_limit_rule.length>1" style="font-size: 25px;" icon="el-icon-remove" type="text" @click="clickDelectMenuFoodLimitRule(index,'menu_food_category_limit_rule')"></el-button>
                </el-form-item>
           </div>
          </div>
          <!-- <div v-for="(item, index) in formData.menu_seasonal_food_limit_rule" :key="index">
            <div class="ps-flex flex-align-c">
                <el-form-item label="">
                  <span class="p-r-10"> 智能推荐标有 <span class="red">应季标识</span> 的菜品在</span>
                </el-form-item>
                <el-form-item label="" :prop="'menu_seasonal_food_limit_rule['+index+'].menu_type'" :rules="formRuls.menu_type">
                  <el-select
                    v-model="item.menu_type"
                    class="ps-select w-100 p-r-10"
                    popper-class="ps-popper-select"
                  >
                  <el-option
                    v-for="menu in menuTypeListData"
                    :key="menu.value"
                    :label="menu.name"
                    :value="menu.value"
                    :disabled="disabledSeasonalMenu(menu.value)"
                  ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" :prop="'menu_seasonal_food_limit_rule['+index+'].days'" :rules="formRuls.days">
                  <el-input
                    placeholder=""
                    v-model="item.days"
                    class="ps-input w-100 m-r-10"
                  ></el-input>
                  <span>天内，</span>
                </el-form-item>
                <el-form-item label="" :prop="'menu_seasonal_food_limit_rule['+index+'].food_tips_repeat_number'" :rules="formRuls.food_tips_repeat_number">
                  <span class="p-r-10">允许重复</span>
                  <el-input
                    placeholder=""
                    v-model="item.food_tips_repeat_number"
                    class="ps-input w-100 m-r-10"
                  ></el-input>
                  <span>次</span>
                </el-form-item>
                <el-form-item label="" prop="">
                  <el-button class="p-l-20" style="font-size: 25px;" icon="el-icon-circle-plus" type="text" @click="clickAddSeasonalFoodLimitRule('menu_seasonal_food_limit_rule')"></el-button>
                  <el-button v-if="formData.menu_seasonal_food_limit_rule.length>1" style="font-size: 25px;" icon="el-icon-remove" type="text" @click="clickDelectSeasonalFoodLimitRule(index,'menu_seasonal_food_limit_rule')"></el-button>
                </el-form-item>
           </div>
          </div> -->
          <div style="max-width: 900px;" v-for="(meal, index) in ruleAttrMeal" :key="meal.key">
            <div class="m-b-20">{{meal.label}}：智能推荐的菜品属性数量：<span class="red">共{{ meal.count?meal.count:0 }}道</span></div>
            <div class="inline-form-item">
              <el-form-item label-width="120px" v-for="item in meal.data" :key="item.key" :label="nameFormat(item.label,5)" :rules="formRuls.arr_number"  :prop="meal.key+'_food_data.'+item.key">
                <el-input
                  placeholder=""
                  v-model="formData[meal.key+'_food_data'][item.key]"
                  class="ps-input w-100 m-l-10 m-r-10"
                  @input="inputChangeRuleHandle($event, meal, index)"
                ></el-input>
                <span class="m-r-10">道</span>
              </el-form-item>
            </div>
          </div>
          <!-- <div class="ps-flex">
            <el-form-item label="">
              菜谱餐标（每人每天多少钱）：
              <el-switch v-model="formData.food_menu_price_switch" active-color="#ff9b45"></el-switch>
            </el-form-item>
            <el-form-item v-if="formData.food_menu_price_switch" label="" prop="food_menu_price">
              <el-input
                placeholder=""
                v-model="formData.food_menu_price"
                class="ps-input w-100 m-l-10 m-r-10"
              ></el-input>
              <span>元</span>
            </el-form-item>
          </div> -->
        </div>
      </div>
      <div class="footer" style="margin-top: 20px">
        <!-- <el-button style="width: 120px" @click="closeHandler">取消</el-button> -->
        <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitHandler">
          保存
        </el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import { validateStock } from '@/assets/js/validata'
import { to, deepClone, debounce } from '@/utils'
import NP from 'number-precision'
import { validataPositiveMoney } from '@/utils/form-validata'
import { MENU_TYPE_LIST_DATA } from './constants'
import {  integer } from '@/utils/validata'

export default {
  data() {
    let validateDays = (rule, value, callback) => {
      let number = /^\d+$/
      if (!number.test(value) && value) {
        callback(new Error('请输入正确数字'))
      } else {
        callback()
      }
    }
    const validateStockNum = (rule, value, callback) => {
      if (value) {
        if (!integer(value)) {
          callback(new Error('请输入数字'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      menuTypeListData: deepClone(MENU_TYPE_LIST_DATA),
      isLoading: false,
      formData: {
        food_tips: false,
        food_tips_time: '',
        food_tips_rank: '',
        food_tips_menu_type: '',
        food_tips_repeat_number: '',
        breakfast_food_data: {},
        lunch_food_data: {},
        dinner_food_data: {},
        menu_food_category_limit_rule: [
          {
            category_id: "",
            menu_type: "",
            days: 0,
            food_tips_repeat_number: 0
          }
        ]
        // menu_seasonal_food_limit_rule: [
        //   {
        //     menu_type: "",
        //     days: 0,
        //     food_tips_repeat_number: 0
        //   }
        // ],
        // food_menu_price_switch: '',
        // food_menu_price: ''
      },
      formRuls: {
        food_tips_time: [
          {
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ],
        food_tips_rank: [
          {
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ],
        food_tips_menu_type: [
          {
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ],
        food_tips_repeat_number: [
          {
            required: true,
            message: '请输入整数',
            validator: validateStock,
            trigger: 'blur'
          }
        ],
        arr_number: [
          {
            validator: validateStockNum,
            trigger: 'blur'
          }
        ],
        food_menu_price: [
          {
            validator: validataPositiveMoney,
            trigger: 'change'
          }
        ],
        category_id: [{ required: true, message: "请选择菜品属性", trigger: ['blur', 'change'] }],
        menu_type: [{ required: true, message: "请选择菜谱", trigger: ['blur', 'change'] }],
        days: [{ required: false, validator: validateDays, message: "请输入整数", trigger: ['blur', 'change'] }]
      },
      ruleAttrMeal: [
        { label: '早餐', key: 'breakfast', count: 0, data: [] },
        { label: '午餐', key: 'lunch', count: 0, data: [] },
        { label: '下午茶', key: 'afternoon', count: 0, data: [] },
        { label: '晚餐', key: 'dinner', count: 0, data: [] },
        { label: '夜宵', key: 'supper', count: 0, data: [] },
        { label: '凌晨餐', key: 'morning', count: 0, data: [] }
      ],
      foodAttributeList: []
      // foodList: []
    }
  },
  async created() {
    // this.getFoodList()
    await this.getFoodAttributeList()
    this.getMenuSettingInfoRuleDetail()
  },
  computed: {
    disabledMenuType() {
      return (select, menuType) => {
        const allSelectMeal = this.formData.menu_food_category_limit_rule.reduce((prev, current) => {
          if (select === 0 || select === current.category_id || current.category_id === 0) {
            return prev.concat(current.menu_type)
          } else {
            return prev
          }
        }, [])
        let disabled = false
        if (allSelectMeal.includes(menuType)) {
          disabled = true
        }
        return disabled
      }
    },
    disabledSeasonalMenu() {
      // 获取所有选中
      const allSelectMeal = this.formData.menu_seasonal_food_limit_rule.reduce((prev, current) => {
        return prev.concat(current.menu_type)
      }, [])
      return (current) => {
        let disabled = false
        if (allSelectMeal.includes(current)) {
          disabled = true
        }
        return disabled
      }
    }
  },
  mounted() {},
  methods: {
    // 获取菜品列表
    // async getFoodList() {
    //   let params = {
    //     page: 1,
    //     page_size: 99999
    //   }
    //   const res = await this.$apis.apiBackgroundFoodFoodListPost(params)
    //   if (res.code === 0) {
    //     this.foodList = res.data.results
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    async getFoodAttributeList() {
      this.isLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodCategoryListPost(
          {
            page: 1,
            page_size: 99999
          }
        )
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodAttributeList = res.data.results.map(v => {
          let obj = {
            label: v.name,
            key: v.id
          }
          return obj
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    async getMenuSettingInfoRuleDetail() {
      this.isLoading = true
      let [err, res] = await to(this.$apis.apiBackgroundFoodMenuSettingInfoRuleDetailPost())
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // res.data.food_menu_price = NP.divide(res.data.food_menu_price, 100)
        // 对数据初始化
        let result = deepClone(this.formData)
        if (res.data) {
          for (const key in res.data) {
            const item = res.data[key];
            switch (key) {
              case 'menu_food_category_limit_rule':
                if (item.length > 0) { // 数组类型不能直接赋值
                  result[key] = item
                }
                break;
              case 'menu_seasonal_food_limit_rule':
                if (item.length > 0) { // 数组类型不能直接赋值
                  result[key] = item
                }
                break;
              default: // 其它的用接口返回的默认数据吧
                result[key] = item
                break;
            }
          }
          // this.formData = result
        }
        // this.formData.breakfast_food_data = this.processFoodData(this.formData.breakfast_food_data)
        // this.formData.lunch_food_data = this.processFoodData(this.formData.lunch_food_data)
        // this.formData.dinner_food_data = this.processFoodData(this.formData.dinner_food_data)
        this.setFoodRuleAttr(result)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理一下分类没有数据
    processFoodData(foodData) {
      let processedData = {};
      if (foodData && Object.keys(foodData).length) {
        for (const key in foodData) {
          this.foodAttributeList.forEach(v => {
            if (Number(v.key) === Number(key)) {
              processedData[key] = foodData[key] ? foodData[key] : 0;
            }
          });
        }
      } else {
        this.foodAttributeList.forEach(v => {
          processedData[v.key] = 0
        })
      }
      return processedData
    },
    changeIntAttrId(item) {
      // 先清一遍
      item.menu_type = ''
    },
    // 初始化规则
    setFoodRuleAttr(result) {
      let currentFoodCategoryList = this.foodAttributeList.map(v => v.key)
      this.ruleAttrMeal.forEach((v, index) => {
        this.$set(v, 'data', deepClone(this.foodAttributeList))
        // 过滤下已删除的FoodCategory数据
        let currentMealKey = v.key+'_food_data'
        if (result[currentMealKey]) {
          let categoryFoodData = {}
          Object.keys(result[currentMealKey]).forEach(category => {
            if (currentFoodCategoryList.includes(Number(category))) {
              categoryFoodData[category] = result[currentMealKey][category]
            }
          })
          result[currentMealKey] = categoryFoodData
        }
      })
      this.formData = result
      // 设置了数据再初始化下
      this.ruleAttrMeal.forEach((v, index) => {
        this.changeMealRuleHandle('', v, index)
      })
    },
    inputChangeRuleHandle: debounce(function(e, meal, index) {
      this.changeMealRuleHandle(e, meal, index)
    }, 300),
    // 修改
    changeMealRuleHandle(e, meal, index) {
      const key = meal.key + '_food_data'
      let result = 0
      Object.keys(this.formData[key]).forEach(v => {
        if (this.formData[key][v]) {
          result = NP.plus(result, this.formData[key][v])
        }
      })
      this.$set(this.ruleAttrMeal[index], 'count', result)
    },
    formatParams() {
      let params = deepClone(this.formData)
      // 再转一遍数字类型吧，旧数据可能又字符串的
      params.food_tips_rank = Number(params.food_tips_rank)
      // for (const key in this.formData.breakfast_food_data) {
      //   params.breakfast_food_data[key] = Number(this.formData.breakfast_food_data[key])
      // }
      // for (const key in this.formData.lunch_food_data) {
      //   params.lunch_food_data[key] = Number(this.formData.lunch_food_data[key])
      // }
      // for (const key in this.formData.dinner_food_data) {
      //   params.dinner_food_data[key] = Number(this.formData.dinner_food_data[key])
      // }
      // params.food_menu_price = NP.times(params.food_menu_price, 100)
      this.ruleAttrMeal.forEach((meal, index) => {
        const mealKey = meal.key + '_food_data'
        const item = params[mealKey]
        // console.log(112323, item, this.ruleAttrMeal)
        // for (const key in item) {
        //   if (Number(item[key])) {
        //     item[key] = Number(item[key])
        //   }
        // }
        // 所有菜品属性都给个默认值0
        meal.data.forEach(v => {
          if (Number(item[v.key])) {
            item[v.key] = Number(item[v.key])
          } else {
            item[v.key] = 0
          }
        })
      })
      params.menu_food_category_limit_rule = this.formData.menu_food_category_limit_rule.map(v => {
        let obj = {
          category_id: v.category_id,
          menu_type: v.menu_type,
          days: Number(v.days),
          food_tips_repeat_number: Number(v.food_tips_repeat_number)
        }
        return obj
      })
      // params.menu_seasonal_food_limit_rule = this.formData.menu_seasonal_food_limit_rule.map(v => {
      //   let obj = {
      //     menu_type: v.menu_type,
      //     days: Number(v.days),
      //     food_tips_repeat_number: Number(v.food_tips_repeat_number)
      //   }
      //   return obj
      // })
      return params
    },
    async getMenuSettingInfoModify() {
      this.isLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundFoodMenuSettingInfoModifyPost(this.formatParams())
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getMenuSettingInfoRuleDetail()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickAddMenuFoodLimitRule(type) {
      this.formData.menu_food_category_limit_rule.push(
        {
          category_id: "",
          menu_type: "",
          days: 0,
          food_tips_repeat_number: 0
        }
      )
    },
    clickDelectMenuFoodLimitRule(index, type) {
      this.formData.menu_food_category_limit_rule.splice(index, 1)
    },
    clickAddSeasonalFoodLimitRule(type) {
      this.formData.menu_seasonal_food_limit_rule.push(
        {
          menu_type: "",
          days: 0,
          food_tips_repeat_number: 0
        }
      )
    },
    clickDelectSeasonalFoodLimitRule(index, type) {
      this.formData.menu_seasonal_food_limit_rule.splice(index, 1)
    },
    // 格式化文字 超过多少显示...
    nameFormat(name, number) {
      if (!name) return
      let subStr = name.slice(0, number)
      subStr = subStr + (name.length > number ? '...' : '')
      return subStr
    },
    // 提交数据
    submitHandler() {
      this.$refs.formRules.validate(valid => {
        if (valid) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          this.getMenuSettingInfoModify()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.menu-rule-setting {
}
.el-textarea .el-input__count {
  background: none;
}
.inline-form-item{
  .el-form-item {
    display: inline-block;
    // width: 220px;
    // text-align: right;
  }
}
</style>
