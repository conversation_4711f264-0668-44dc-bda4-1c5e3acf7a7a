<template>
  <div id="login-main">
    <div class="login-wrapper">
      <div class="login-welcome">
        <img class="welcome-logo" src="@/assets/img/login-bg2.png" alt="login-welcome" />
      </div>
      <div class="login-form" v-loading="loginFormState.loading">
        <div class="form-wrapper">
          <div class="verify-pop">
              <verify-code ref="verifyCode" :visible="isShowVerify" @success="verifySuccess" :is-number="true" @refresh="verifyRefresh"></verify-code>
            </div>
          <!-- <div class="form-logo">
            <img src="@/assets/img/logo.png" alt="" />
          </div> -->
          <div>
            <div class="form-top" v-if="loginType === 'qrcode'">
              <div class="welcome-text" style="margin-left: 100px;">扫码登录</div>
              <img
                class="code-img"
                @click="loginType = 'account'"
                src="@/assets/img/login-pc-code.png"
                alt="login-wechat-code"
              />
            </div>
            <div class="form-top" v-else-if="loginType === 'forgetPwd'">
              <div class="welcome-text">找回密码</div>
            </div>
            <div class="form-top" v-else-if="loginType === 'changePwd'">
              <div class="welcome-text">修改密码</div>
            </div>
            <div class="form-top" v-else>
              <div class="welcome-text">欢迎登录</div>
              <img
                class="code-img"
                @click="loginType = 'qrcode'"
                src="@/assets/img/login-wechat-code.png"
                alt="login-wechat-code"
              />
            </div>
          </div>
          <div v-if="loginType === 'qrcode'" style="height: 270px;margin-top: 20px;position: relative; overflow: hidden;">
            <wx-login
              :appid="appid"
              scope="snsapi_login"
              :redirect_uri="redirect_uri"
              href="data:text/css;base64,LmltcG93ZXJCb3h7CiB3aWR0aDphdXRvOwp9Ci5pbXBvd2VyQm94IC50aXRsZSB7CiBkaXNwbGF5OiBub25lOwp9Ci5pbXBvd2VyQm94IC5xcmNvZGUgewogYm9yZGVyOiBub25lOwogd2lkdGg6IDIwMHB4Owp9Ci5pbXBvd2VyQm94IC5zdGF0dXMgewp3aWR0aDoyMDBweDsKbWFyZ2luOiBhdXRvOwp0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KLmltcG93ZXJCb3ggLnN0YXR1c19icm93c2VyIHA6bnRoLWNoaWxkKDIpewogZGlzcGxheTogbm9uZTsKfQouaW1wb3dlckJveCAuc3RhdHVzX3N1Y2MgLnN0YXR1c190eHQgcHsKIGRpc3BsYXk6IG5vbmU7Cn0KLmltcG93ZXJCb3ggLnN0YXR1c19mYWlsIC5zdGF0dXNfdHh0IHB7CiBkaXNwbGF5OiBub25lOwp9Cg=="
            ></wx-login>
            <!-- <div style="font-size:14px;color:#c0c4cc;position:absolute;width:100%;text-align:center;bottom:20px;">
              打开微信扫一扫登录
            </div> -->
          </div>
          <div v-else-if="loginType === 'changePwd'">
            <el-form
              :model="loginFormState"
              :rules="rules"
              ref="changePwdFormRef"
              class="form"
              label-width="0"
            >
              <div style="margin-top:40px;">
                <el-form-item prop="oldPassword">
                  <el-input
                    v-model="loginFormState.oldPassword"
                    show-password
                    maxlength="46"
                    :placeholder="$t('placeholder.oldPassword')"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item prop="newPassword">
                  <el-input
                    v-model="loginFormState.newPassword"
                    show-password
                    maxlength="46"
                    :placeholder="$t('placeholder.changeNewPassword')"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item prop="checkPassword">
                  <el-input
                    v-model="loginFormState.checkPassword"
                    show-password
                    maxlength="46"
                    :placeholder="$t('placeholder.checkPassword')"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button
                    class="login-btn"
                    style="margin-top:20px;"
                    :loading="loginFormState.loading"
                    @click="checkChangePwdValidate"
                  >
                    确定
                  </el-button>
                </el-form-item>
                <el-form-item>
                  <el-button class="cancel-btn ps-plain-btn" @click="gotoQrcodeLogin">返回</el-button>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <div v-else>
            <el-tabs v-model="loginType" v-if="loginType != 'forgetPwd'" @tab-click="tabHandleClick">
              <el-tab-pane label="密码" name="account"></el-tab-pane>
              <el-tab-pane label="验证码" name="sms"></el-tab-pane>
            </el-tabs>
            <el-form
              :model="loginFormState"
              :rules="rules"
              ref="loginFormRef"
              class="form"
              label-width="0"
            >
              <div v-if="loginType === 'account'">
                <el-form-item prop="account" class="account">
                  <el-input
                    v-model="loginFormState.account"
                    maxlength="48"
                    :placeholder="$t('placeholder.account')"
                    clearable
                    prefix-icon="el-icon-user"
                    @input="changeAccountHandle"
                  >
                    <!-- <i slot="prefix" class="el-input__icon el-icon-user"></i> -->
                    <!-- <img slot="prefix" class="ps-icon icon-user" src="@/assets/img/icon-user.png" alt=""> -->
                  </el-input>
                </el-form-item>
                <el-form-item prop="password" class="password">
                  <el-input
                    type="password"
                    v-model.trim="loginFormState.password"
                    maxlength="46"
                    :placeholder="$t('placeholder.password')"
                    clearable
                    prefix-icon="el-icon-lock"
                  >
                    <!-- <i slot="prefix" class="el-input__icon el-icon-lock"></i> -->
                    <!-- <img slot="prefix" class="ps-icon icon-lock" src="@/assets/img/icon-lock.png" alt=""> -->
                  </el-input>
                </el-form-item>
                <el-form-item class="verify-code-box" label="" prop="verifyCode">
                  <el-input
                    class="verify-code"
                    v-model="loginFormState.verifyCode"
                    placeholder="请输入图形验证码"
                    autocomplete="off"
                  ></el-input>
                  <el-image
                    @click="getVerifyCodeHandle"
                    class="verify-code-img"
                    :src="verifyUrl"
                    fit="contain">
                    <div slot="error" class="el-image__error" @click="getVerifyCodeHandle">加载失败</div>
                    </el-image>
                </el-form-item>
                <span v-if="isExpire" style="color: red;" class="font-size-12">{{ tipText(0) }}<span style="color: #2694F1;text-decoration: underline;">{{ tipText(1) }}</span></span>
                <el-form-item>
                  <div class="password-tools">
                    <el-checkbox v-model="isRemember" class="remember">
                      {{ $t('login.rememberPassword') }}
                    </el-checkbox>
                    <span class="forget-password" @click="forgetHandle">
                      {{ $t('login.forgetPassword') }}
                    </span>
                  </div>
                </el-form-item>
                <el-form-item class="login">
                  <el-button
                    class="login-btn"
                    :loading="loginFormState.loading"
                    @click="checkLoginFormHandle"
                  >
                    {{ $t('login.loginBtn') }}
                  </el-button>
                </el-form-item>
                <div v-if="userErrorLogin" style="text-align:center; font-size: 14px; color: red;">{{ errorLoginMsg }}<a v-if="showChangePwdTip" src="javascript:void" class="change-pwd-a" @click="loginType='changePwd'">修改密码</a></div>
              </div>
              <div v-if="loginType === 'sms'">
                <el-form-item prop="phone" class="phone">
                  <el-input
                    v-model="loginFormState.phone"
                    maxlength="48"
                    :placeholder="$t('placeholder.phone')"
                    clearable
                    prefix-icon="el-icon-phone"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="smsCode" class="phone-code">
                  <verification-code :sendAuthCode="sendAuthCode" :disabled="sendCodeDisabled" @click="getVerCode" :reset-handle="resetHandle" v-model="loginFormState.smsCode"></verification-code>
                </el-form-item>
                <el-form-item class="login">
                  <el-button
                    class="login-btn"
                    :loading="loginFormState.loading"
                    @click="checkLoginFormHandle"
                  >
                    {{ $t('login.loginBtn') }}
                  </el-button>
                </el-form-item>
              </div>
              <div v-if="loginType === 'forgetPwd' && loginFormState.forgetStatus === 1" style="margin-top:40px;">
                <el-form-item prop="phone" class="phone">
                  <el-input
                    v-model="loginFormState.phone"
                    maxlength="48"
                    :placeholder="$t('placeholder.phone')"
                    clearable
                    prefix-icon="el-icon-phone"
                    @input="changePhoneHandle"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="smsCode" class="phone-code" style="margin-bottom:5px;">
                  <verification-code :sendAuthCode="sendAuthCode" :disabled="sendCodeDisabled" :reset-handle="resetHandle" @click="getVerCode" v-model="loginFormState.smsCode"></verification-code>
                </el-form-item>
                <div style="font-size: 12px;color: #c0c4cc;margin: 20px 0;">
                  无法获取短信验证码请联系管理员
                </div>
                <el-form-item>
                  <el-button
                    class="login-btn"
                    style="margin-top:20px;"
                    :loading="loginFormState.loading"
                    @click="checkPwdValidate(0)"
                  >
                    下一步
                  </el-button>
                </el-form-item>
                <el-form-item>
                  <el-button class="cancel-btn ps-plain-btn" @click="gotoQrcodeLogin">返回</el-button>
                </el-form-item>
              </div>
              <div v-if="loginType === 'forgetPwd' && loginFormState.forgetStatus === 0" style="margin-top:40px;">
                <el-form-item prop="newPassword">
                  <el-input
                    v-model="loginFormState.newPassword"
                    show-password
                    :placeholder="$t('placeholder.newPassword')"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item prop="checkPassword">
                  <el-input
                    v-model="loginFormState.checkPassword"
                    show-password
                    :placeholder="$t('placeholder.checkPassword')"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button
                    class="login-btn"
                    style="margin-top:20px;"
                    :loading="loginFormState.loading"
                    @click="checkPwdValidate(1)"
                  >
                    完成
                  </el-button>
                </el-form-item>
                <el-form-item>
                  <el-button class="cancel-btn ps-plain-btn" @click="gotoQrcodeLogin">取消</el-button>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <div class="agreement m-t-20" v-if="agreementList.length">登录即代表你同意
          <span v-for="agreement in agreementList" :key="agreement.id">《<span class="blue pointer" @click="gotoAgreement(agreement)">{{ agreement.agreement_type_alias }}</span>》</span>
        </div>
        <!-- 备案号 -->
        <!-- <filings class="fixed-record" /> -->
      </div>
    </div>
    <select-company :show="showCompany" :type="loginType" :company-list="dialogData" :confirmhandle="dialogConfirmHandle" :closehandle="dialogClosehandle" />
    <double-factor v-if="userInfo.is_double_factor" :userInfo="userInfo" @doubleConfirm="doubleConfirmHandle" @doubleCancel="doubleCancelHandle" />
  </div>
</template>

<script>
import md5 from 'js-md5';
import { setLocalStorage, getLocalStorage, removeLocalStorage, to, parseTime, setSessionStorage, removeSessionStorage, debounce, isMobile } from '@/utils'
import apis from '../../api'
// import wxLogin from 'vue-wxlogin';
import Filings from "@/components/Filings"
import { LOGINAGREEMENT } from '@/constants/agreement'
import SelectCompany from "./components/SelectCompany"
import DoubleFactor from './components/DoubleFactor/index.vue'
import VerifyCode from '@/components/VerifyCode/VerifyCode.vue'
import { Base64 } from 'js-base64';

export default {
  name: 'Login',
  // eslint-disable-next-line vue/no-unused-components
  components: { Filings, SelectCompany, DoubleFactor, VerifyCode },
  data() {
    let validateAccount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("账号不能为空"));
      } else {
        // let regNum = /(^\d{3,12}$)|(^[a-zA-Z]{3,12}$)/;
        let regNum = /^\w{5,20}$/;
        if (!regNum.test(value)) {
          callback(
            new Error("账号长度5到20位，只支持数字、大小写英文或下划线组合")
          );
        } else {
          callback();
        }
      }
    };
    let validatePass = (rule, value, callback) => {
      let regPass = /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/
      // let regPass = /(^\w{6,32}$)/;
      if (!value) {
        return callback(new Error("密码不能为空"));
      } else {
        if (!regPass.test(value)) {
          callback(new Error("密码长度8到20位，字母和数组组合"));
        } else {
          callback();
        }
      }
    };
    let validatePassAgain = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("请再次输入密码"));
      } else {
        let regPass = /^[0-9A-Za-z]{8,20}$/;
        if (!regPass.test(value)) {
          callback(new Error("密码长度8~20位，英文加数字"));
        } else {
          callback();
        }
      }
    };
    let validateTelphone = (rule, value, callback) => {
      if (!value) {
        this.sendCodeDisabled = true
        return callback(new Error("手机号不能为空"));
      } else {
        let regTelphone = /^1[3456789]\d{9}$/
        if (!regTelphone.test(value)) {
          this.sendCodeDisabled = true
          callback(new Error("手机号格式错误"));
        } else {
          this.sendCodeDisabled = false
          callback();
        }
      }
    };
    return {
      isRemember: false,
      loginFormState: {
        account: '',
        password: '',
        phone: '', // 手机号
        smsCode: '', // 验证码
        forgetStatus: 1, // 忘记密码状态（切换页面）1:输入手机号验证码;0:输入密码
        newPassword: '', // 新密码
        checkPassword: '', // 再次输入新密码
        loading: false,
        verifyCode: '' // 图片验证码
      },
      redirect_uri: '',
      appid: '',
      rules: {
        account: [{ validator: validateAccount, trigger: "change" }],
        password: [{ validator: validatePass, trigger: "change" }],
        oldPassword: [{ validator: validatePass, trigger: "change" }],
        newPassword: [{ validator: validatePass, trigger: "change" }],
        checkPassword: [{ validator: validatePassAgain, trigger: "change" }],
        phone: [{ validator: validateTelphone, trigger: "change" }],
        smsCode: [{ required: true, message: '请输入验证码', trigger: 'change' }],
        verifyCode: [{ required: true, message: '请输入验证码', trigger: 'change' }]
      },
      redirect: undefined,
      otherQuery: {},
      messageHandle: null,
      loginType: 'qrcode',
      sendCodeDisabled: true,
      sendAuthCode: true,
      userErrorLogin: false, // 是否启用
      errorLoginMsg: '您还有5次尝试机会',
      errorLoginData: { // 只记录账号登录和手机登录的
        // xxx: { count: 0, time: 0 } // xxx登录的手机/账号, count当前登录失败的次数, time满足5次的时间记录
      }, // 记录登录失败的账号和失败次数
      showChangePwdTip: false,
      token: '',
      verifyUrl: '', // 图片验证码
      agreementList: LOGINAGREEMENT, // 协议列表
      showCompany: false,
      dialogData: [],
      companyId: '',
      expireText: '',
      isExpire: false,
      loginData: {},
      userInfo: {},
      isShowVerify: false,
      answerList: [],
      answer: ''
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
        if (query.code) {
          this.loginType = 'qrcode'
          this.loginFormState.qrcode = query.code
          this.loginHanlde()
        }
        if (query.token) {
          this.loginHanlde(this.$route.query.token)
        }
      },
      immediate: true
    },
    loginType() {
      if (this.loginType === 'account') {
        this.getVerifyCodeHandle()
      }
    }
  },
  computed: {
    tipText() {
      return d => {
        let text = this.expireText.slice(0, 18)
        let num = this.expireText.slice(-10)
        if (d === 0) {
          return text
        } else {
          return num
        }
      }
    }
  },
  async created () {
    // 添加判断浏览器
    this.determineBrowser()

    if (this.$route.query.token) {
      this.loginHanlde(this.$route.query.token);
    }
    let v4Login = getLocalStorage('v4_login')
    if (v4Login) {
      try {
        v4Login = JSON.parse(decodeURIComponent(v4Login))
        this.loginFormState.account = v4Login.account
        this.loginFormState.password = v4Login.password
        this.isRemember = true
      } catch (error) {}
    }
    let errorLogin = getLocalStorage('errorLoginData')
    if (errorLogin) {
      try {
        this.errorLoginData = JSON.parse(decodeURIComponent(errorLogin))
      } catch (error) {}
    }
    this.getWxLoginData()
    this.getVerifyCodeHandle()
  },
  mounted () {
    document.addEventListener('keydown', this.enterKeydowHandle)
    // 关闭this.$confirm等类型的弹窗
    try {
      this.$msgbox && this.$msgbox.close && this.$msgbox.close()
    } catch (error) {
      console.warn('$msgbox close error', error)
    }
  },
  methods: {
    // 监听键盘事件
    enterKeydowHandle(e) {
      if (e.keyCode === 13) {
        this.checkLoginFormHandle()
      }
    },
    // 输入框校验
    checkLoginFormHandle(e) {
      if (this.loginFormState.account === 'pushi1') {
        this.userErrorLogin = true
      } else {
        this.userErrorLogin = false
      }
      // if (this.userErrorLogin && this.checkLoginError(this.loginFormState.account)) {
      //   return
      // }
      this.$refs.loginFormRef.validate(valid => {
        if (valid) {
          // 多项目点共用同一个手机登录
          if (this.loginType === 'sms') {
            this.getPhoneCompanyInfo()
          } else {
            this.loginHanlde();
          }
        } else {
          if (this.messageHandle) return;
          this.messageHandle = this.$message({
            type: 'error',
            message: '请检查登录信息！',
            onClose: () => {
              this.messageHandle = null
            }
          });
        }
      })
    },
    async loginHanlde(token) {
      if (this.loginFormState.loading) return;
      this.loginFormState.loading = true
      if (this.isRemember) {
        setLocalStorage('v4_login', encodeURIComponent(JSON.stringify({
          account: this.loginFormState.account,
          password: this.loginFormState.password
        })))
      } else {
        removeLocalStorage('v4_login')
      }
      let params = {
        token,
        mode: this.loginType,
        company_id: this.companyId
      }
      // 协议添加默认同意参数
      if (this.agreementList.length) {
        params.agreement_types = this.agreementList.map(v => v.agreement_type)
      }
      // 有token不再走里面判断
      if (!token) {
        if (this.loginType === 'account') {
          params.username = this.loginFormState.account
          params.password = this.loginFormState.password ? md5(this.loginFormState.password) : ''
          params.verify_code = this.loginFormState.verifyCode
        } else if (this.loginType === 'sms') {
          params.phone = this.loginFormState.phone
          params.sms_code = this.loginFormState.smsCode
          params.code = this.answer
        } else if (this.loginType === 'qrcode') {
          params.code = this.loginFormState.qrcode
        }
      }
      // 登录前先去清楚sessionStorage，防止通过window.open或者a标签打开新窗口时sessionStorage的共享问题 https://github.com/lmk123/blog/issues/66
      // await this.$store.dispatch('user/logout')
      const [err, res] = await to(apis.apiBackgroundLoginPost(params))
      if (err) {
        this.loginFormState.loading = false
        return this.$message.error(err.message)
      }
      this.getVerifyCodeHandle()
      this.loginData = res
      if (res.code === 0 || res.code === 5) {
        if (res.code === 5) {
          let stopServiceMsg = {
            code: res.code,
            status: res.data.project_status,
            endTime: res.data.service_end_time || '',
            data: res.data
          }
          this.$store.dispatch('user/setStopServiceMsg', stopServiceMsg)
        } else {
          this.$store.dispatch('user/setStopServiceMsg', {})
        }
        // 是否开启双因子登录，如果是验证码登录的话不需要二次进行验证
        if (this.loginType !== 'sms' && res.data.is_double_factor) {
          this.userInfo = res.data
          setSessionStorage('PWDV4TOKEN', res.data.token)
        } else {
          this.setLoginData(res)
        }
        // this.setLoginData(res)
        // this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
      } else if (res.code === 3) { // 到期修改密码 允许跳过本次
        res.data.msg = res.msg
        // res.data.is_expire_change_pwd = true
        setSessionStorage('ISEXPIRECHANGEPWD', '1')
        this.setLoginData(res)
      } else if (res.code === 4) { // 到期修改密码 允许跳过本次
        this.loginFormState.loading = false
        setSessionStorage('PWDV4TOKEN', res.data.token)
        this.token = res.data.token
        this.errorLoginMsg = '登录密码已过期，请前往'
        this.showChangePwdTip = true
        this.userErrorLogin = true
        this.$message.error(res.msg);
      } else if (res.code === 1) {
        if (res.data && res.data.login_error_count !== undefined) { // 登录失败记录下当前登录的信息
          // if (this.loginType === 'account') {
          //   this.setLoginErrorCount(this.loginFormState.account)
          // } else if (this.loginType === 'sms') {
          //   this.setLoginErrorCount(this.loginFormState.phone)
          // }
          this.userErrorLogin = true
          this.errorLoginMsg = `您还有${5 - res.data.login_error_count}次尝试机会`
        }
        this.expireText = res.msg
        this.isExpire = true
        this.loginFormState.loading = false
        return
      } else {
        this.loginFormState.loading = false
        this.$message.error(res.msg)
        if (this.loginType === 'qrcode') {
          this.$router.replace({
            name: 'Login'
          })
        }
      }
    },
    doubleConfirmHandle() {
      this.setLoginData(this.loginData)
    },
    doubleCancelHandle() {
      this.loginData = {}
      this.userInfo = {}
      this.isLoading = false
      this.loginFormState.loading = false
    },
    async setLoginData(res) {
      console.log("setLoginData", res.data);
      await this.$store.dispatch('user/login', res.data)
      // this.$message.success('登录成功')
      removeLocalStorage('errorLoginData')
      if (this.loginType === 'sms') { // 如果是验证码登录的话不需要二次进行验证
        setSessionStorage('CHECKDOUBLEFACTOR', '1')
      }
      this.$message({
        type: 'success',
        message: '登录成功',
        duration: 1000
      })
      if (Reflect.has(res.data, 'is_channel_account' && res.data.is_channel_account)) {
        // 是渠道用户，跳转到渠道首页
        console.log("跳转渠道首页");
        this.$router.push({ path: '/homechannel' })
      } else {
        console.log("跳转原来首页");
        this.$router.push({
          path: '/'
        })
        // if (res.data.account_type !== 3) {
        //   this.$router.push({
        //     path: '/',
        //     name: 'HomePage',
        //     params: {
        //       code: this.stopServiceMsg.code,
        //       msg: this.stopServiceMsg.msg
        //     }
        //   })
        // } else {

        // }
      }
    },
    // 记录下登录失败的次数, 等保需求
    setLoginErrorCount(key) {
      // 首先判断是否有这字段吧,没有则初始化下
      if (this.errorLoginData[key] === undefined) {
        this.errorLoginData[key] = {
          count: 0,
          time: 0
        }
      }
      this.errorLoginData[key].count += 1
      this.errorLoginData[key].time = new Date().getTime() // 记录下时间
      if (this.errorLoginData[key].count < 5) {
        this.errorLoginMsg = `您还有${5 - this.errorLoginData[key].count}次尝试机会`
      } else {
        this.errorLoginMsg = ''
      }
      setLocalStorage('errorLoginData', encodeURIComponent(JSON.stringify(this.errorLoginData)))
    },
    changeAccountHandle(key) {
      this.userErrorLogin = false
      // if (this.errorLoginData[key]) {
      //   this.errorLoginMsg = `您还有${5 - this.errorLoginData[key].count}次尝试机会`
      // } else {
      //   this.errorLoginMsg = ''
      // }
    },
    // 检查下登录失败的次数
    checkLoginError(account) {
      let isExceed = false
      // 如果失败次数大于5次锁定账号10分钟
      if (this.errorLoginData[account] && this.errorLoginData[account].count > 5) {
        let diffTime = new Date().getTime() - this.errorLoginData[account].time
        if (diffTime < 6e5) {
          isExceed = true
          this.errorLoginMsg = ''
          this.$message.error('账号已锁定10分钟! 请于' + parseTime(this.errorLoginData[account].time + 6e5) + '后再尝试登录')
        }
      }
      return isExceed
    },
    async getPhoneCode() {
      let choices = 0
      if (this.loginType === 'forgetPwd') { choices = 2 }
      let params = {
        phone: this.loginFormState.phone,
        choices: choices,
        code: this.answer
      }
      const [err, res] = await to(this.$apis.apiBackgroundVerificationCodePost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.sendAuthCode = false
        this.$message.success('发送成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重置验证码倒计时
    resetHandle(e) {
      this.sendAuthCode = true
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    forgetHandle(e) {
      // if (this.messageHandle) return;
      // this.messageHandle = this.$message({
      //   type: 'warning',
      //   message: '请联系管理员进行修改！',
      //   onClose: () => {
      //     this.messageHandle = null
      //   }
      // });
      this.loginFormState.phone = ''
      this.loginFormState.forgetStatus = 1
      this.loginType = 'forgetPwd'
      this.sendAuthCode = true
      this.sendCodeDisabled = true
    },
    tabHandleClick() {
      this.$nextTick(() => {
        this.sendAuthCode = true
        this.sendCodeDisabled = true
        this.isShowVerify = false
        this.loginFormState.verifyCode = ''
        this.loginFormState.phone = ''
        // this.answerList = []
        this.$refs.loginFormRef.clearValidate();
      })
    },
    gotoQrcodeLogin() {
      if (this.loginType === 'changePwd' && this.$refs.changePwdFormRef) {
        this.$refs.changePwdFormRef.resetFields()
        removeSessionStorage('PWDV4TOKEN')
      } else
      if (this.$refs.loginFormRef) {
        this.$refs.loginFormRef.resetFields()
      }
      this.$nextTick(() => {
        this.loginType = 'qrcode'
        this.loginFormState.forgetStatus = 1
      })
    },
    checkPwdValidate(choices) {
      this.$refs.loginFormRef.validate(valid => {
        if (valid) {
          if (choices === 0) { // 忘记密码获取也需要选择公司
            this.getPhoneCompanyInfo()
          } else {
            this.changePassword(choices);
          }
        } else {
          this.$message.error('请输入正确的信息！')
        }
      })
    },
    async changePassword(choices) {
      let params = {
        sms_code: this.loginFormState.smsCode,
        phone: this.loginFormState.phone,
        choices,
        company_id: this.companyId,
        code: this.answer
      }
      if (choices) {
        if (this.loginFormState.newPassword === this.loginFormState.checkPassword) {
          params.new_password = md5(this.loginFormState.newPassword)
        } else {
          return this.$message.error('密码输入不一致')
        }
      }
      this.loginFormState.loading = true
      const [err, res] = await to(this.$apis.apiBackgroundFindPasswordPost(params))
      this.loginFormState.loading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.loginFormState.forgetStatus = choices
        this.loginFormState.newPassword = ''
        this.loginFormState.checkPassword = ''
        if (choices) {
          this.loginFormState.phone = ''
          this.loginType = 'account'
          this.companyId = ''
          this.$message.success('修改成功')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    async getWxLoginData() {
      const [err, res] = await to(this.$apis.apiBackgroundWechatValidatePost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.appid = res.data.appid
        this.redirect_uri = res.data.redirect_uri ? encodeURIComponent(res.data.redirect_uri + '/#/login') : ''
      } else {
        this.$message.error(res.msg)
      }
    },
    checkChangePwdValidate() {
      this.$refs.changePwdFormRef.validate(valid => {
        if (valid) {
          this.changePwdHandle();
        } else {
          this.$message.error('请输入正确的信息！')
        }
      })
    },
    async changePwdHandle(e) {
      let params = {
        choices: 2, // 固定2为修改密码
        password: md5(this.loginFormState.oldPassword)
      }
      if (this.loginFormState.oldPassword === this.loginFormState.newPassword) {
        return this.$message.error('新密码与旧密码不可重复')
      }
      if (this.loginFormState.newPassword === this.loginFormState.checkPassword) {
        params.new_password = md5(this.loginFormState.newPassword)
      } else {
        return this.$message.error('新密码两次输入不一致')
      }
      this.loginFormState.loading = true
      const res = await this.$apis.apiBackgroundModifyUserinfoPost(params)
      this.loginFormState.loading = false

      if (res.code === 0) {
        this.loginFormState.oldPassword = ''
        this.loginFormState.newPassword = ''
        this.loginFormState.checkPassword = ''
        this.loginFormState.password = ''
        this.loginFormState.phone = ''
        this.loginType = 'account'
        this.showChangePwdTip = false
        this.userErrorLogin = false
        this.errorLoginMsg = ''
        removeSessionStorage('PWDV4TOKEN')
        this.$message.success('修改成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 节下流咯
    getVerifyCodeHandle: debounce(function() {
      this.getVerifyCode()
    }, 300),
    async getVerifyCode() {
      const res = await this.$apis.apiBackgroundGetLoginVerifyCodePost();
      if (res.code === 0) {
        this.verifyUrl = res.data
      } else {
        this.$message.error(res.msg);
      }
    },
    gotoAgreement(row) {
      let url = window.location.origin + '/#/agreement?type=' + row.agreement_type + '&key=AGREEMENTLIST'
      window.open(url, '_blank')
    },
    // 判断浏览器类型
    determineBrowser() {
      let developUrl = "http://sh-v4.debug.packertec.com/"
      let stagingUrl = 'http://sh-v4-staging.packertec.com/'
      let productUrl = "http://sh-v4.packertec.com/"
      // 如果移动端浏览器就重新跳转到移动端的网页
      if (isMobile()) {
        this.$confirm("检测您当前在手机环境，是否前往商户移动端？", '提示', {
          confirmButtonText: '立即前往',
          cancelButtonText: '留在后台',
          center: true,
          customClass: 'myMsgDialog',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              let h5Url = ""
              switch (process.env.NODE_ENV) {
                case "development":
                  h5Url = developUrl
                  break;
                case "staging":
                  h5Url = stagingUrl
                  break;
                case "production":
                  h5Url = productUrl
                  break;
                default:
                  break;
              }
              window.location.href = h5Url
              console.log("h5Url", h5Url);
            }
            done()
          }
        }).then(() => {
        }).catch(() => {
        })
      }
    },
    // 不同项目点可以用相同手机登录
    // 获取手机登录的项目点信息
    async getPhoneCompanyInfo() {
      let choices = 0
      if (this.loginType === 'forgetPwd') { choices = 2 } // 忘记密码
      const [err, res] = await this.$to(this.$apis.apiBackgroundGetPhoneCompanyInfoPost({
        phone: this.loginFormState.phone,
        sms_code: this.loginFormState.smsCode,
        sms_type: choices,
        code: this.answer
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data.length > 1) {
          this.showCompany = true
          this.dialogData = res.data
        }
        if (res.data.length === 1) {
          this.dialogConfirmHandle(res.data[0])
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 选择项目点确定事件
    dialogConfirmHandle(e) {
      this.companyId = e.company_id
      this.showCompany = false
      if (this.loginType === 'forgetPwd') {
        this.changePassword(0); // 忘记密码
      } else {
        this.loginHanlde(); // 正常登录
      }
    },
    dialogClosehandle() {
      this.showCompany = false
    },
    async getVerCode(flag) {
      if (!this.loginFormState.phone || !/^1[3456789]\d{9}$/.test(this.loginFormState.phone)) {
        return
      }
      const res = await this.$apis.apiBackgroundGetSmsVerifyCodePost({
        phone: this.loginFormState.phone
      });
      if (res.code === 0) {
        let data = res.data || {}
        this.answer = data.key || ''
        let keys = Base64.decode(data.key) ? JSON.parse(Base64.decode(data.key)) : ''
        console.log("getLoginVerifyCode", keys);
        this.answerList = []
        if (keys && typeof keys === 'object') {
          for (let keyName in keys) {
            this.answerList.push(keys[keyName])
          }
        }
        if (this.$refs.verifyCode) {
          this.$refs.verifyCode.setAnswerList(this.answerList)
          if (flag) {
            this.$refs.verifyCode.reset()
          }
        }
        console.log("result", this.answerList)
        this.isShowVerify = true
      } else {
        this.$message.error(res.msg);
      }
    },
    // 验证成功
    verifySuccess(value) {
      console.log("verifySuccess", value);
      this.isShowVerify = false
      this.getPhoneCode()
    },
    verifyRefresh() {
      this.getVerCode(true)
    }
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.enterKeydowHandle)
  }

}
</script>

<style lang="scss" scope>
$origin: #ff9b45;
$origin-hover: #ffa558;
$origin-active: #e58b3e;

@mixin origin-border {
  border-color: $origin;
  input {
    &:focus {
      border-color: $origin;
    }
  }
  .el-input.is-focus .el-input__inner {
    border-color: $origin;
  }
}
#login-main {
  width: 100%;
  height: 100%;
  min-width: 772px;
  position: relative;
  // display: flex;
  // align-items: center;
  // justify-content: center;
  background-color: #ffe3ce;
  .login-wrapper {
    display: flex;
    height: 100%;
    .login-welcome {
      // width: 50%;
      height: 100%;
      img {
        display: inline-block;
        // width: 100%;
        height: 100%;
      }
    }
    .login-form {
      position: relative;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      background-color: #ffffff;
      .form-wrapper {
        width: 350px;
        box-shadow: 0px 0px 9px #c9c8c8;
        border-radius: 10px;
        padding: 30px;
        position: relative;
        .form-logo {
          margin-bottom: 60px;
          text-align: center;
        }
        .form-top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .welcome-text {
            font-weight: bold;
            font-size: 22px;
          }
          .code-img {
            cursor: pointer;
          }
        }
        .el-tabs {
          margin: 8px 5px;
        }
        .el-tabs__nav-wrap::after {
          background-color: transparent;
        }
        .el-tabs__item.is-active,
        .el-tabs__item:hover {
          color: $origin;
        }
        .el-tabs__active-bar {
          background-color: $origin;
        }
      }
      .form {
        margin: 0 5px;
        .ps-icon {
          display: inline-block;
          width: 19px;
          margin-left: 3px;
          vertical-align: middle;
        }
        .el-input {
          @include origin-border;
        }
        .account {
          &.is-error {
            margin-bottom: 30px;
          }
        }
        .password {
          margin-bottom: 16px;
        }
        .password-tools {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #c9d0d9;
          line-height: 1.5;
          .remember {
            .el-checkbox__label {
              color: #7f7e86;
            }
            .el-checkbox__input.is-checked .el-checkbox__inner {
              color: #c9d0d9;
              border-color: #ff9b45;
              background: #ff9b45;
            }
            .el-checkbox__input.is-focus .el-checkbox__inner {
              border-color: #ff9b45;
            }
          }
          .forget-password {
            color: #ff9b45;
            cursor: pointer;
          }
        }
        .phone-code {
          // .phone-code-btn {
          //   &:hover {
          //     color: $origin;
          //     border-color: $origin;
          //     background-color: #fff;
          //   }
          //   &:focus {
          //     color: $origin;
          //     border-color: $origin;
          //     background-color: #fff;
          //   }
          //   &:active {
          //     color: $origin-active;
          //     border-color: $origin-active;
          //     background-color: #fff;
          //   }
          // }
        }
        .login {
          margin-top: 40px;
        }
        .login-btn {
          width: 100%;
          display: block;
          color: #ffffff;
          background-image: linear-gradient(90deg, #ffa545 0%, #ff8346 100%),
            linear-gradient(#ff9b45, #ff9b45);
          background-blend-mode: normal, normal;
          box-shadow: 0px 5px 7px 0px rgba(255, 155, 69, 0.5);
          border-radius: 4px;
          border: none;
          &:hover {
            opacity: 0.8;
          }
          &:active {
            opacity: 0.9;
          }
        }
        .cancel-btn {
          width: 100%;
          display: block;
          color: $origin;
          border-color: $origin;
          box-shadow: 0px 5px 7px 0px rgba(255, 230, 158, 0.5);
          border-radius: 4px;
        }
        .change-pwd-a{
          color: #2694EE;
          text-decoration-line: underline;
          cursor: pointer;
        }
      }
      .verify-code-box{
        .verify-code{
          width: 170px;
        }
        .verify-code-img{
          float: right;
          width: 100px;
          height: 40px;
          margin-left: 5px;
          margin-right: 0;
          vertical-align: middle;
          cursor: pointer;
        }
      }
      .agreement {
        font-size: 12px;
        .blue{
          color: #169BD5;
        }
        .pointer {
          cursor: pointer;
        }
      }
    }
  }

}
.myMsgDialog {
  width: 220px;
}
.verify-pop {
  position: absolute;
  left: 14px;
  top: 83px;
}

</style>
