<template>
  <div class="container-wrapper">
    <h2>{{ reportData.name || '自定义报表' }}</h2>
    <div v-if="reportData" class="table-wrapper" v-loading="isLoading">
      <div class="table-content">
        <iframe :src="reportData.url" frameborder="0" class="report-iframe" style="width: 100%; height: calc(100vh - 235px);"></iframe>
      </div>
    </div>
    <div v-else>
      <p>正在加载报表数据...</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "CustomReport",
  data() {
    return {
      reportData: null,
      isLoading: false
    }
  },
  created() {
    // 从路由 meta 中获取自定义报表数据
    this.reportData = this.$route.meta.customReportData
  },
  watch: {
    // 监听路由变化，更新报表数据
    $route(to, from) {
      this.isLoading = true
      this.reportData = to.meta.customReportData
      this.isLoading = false
    }
  },
  methods: {
    // 这里可以添加具体的报表渲染方法
  }
}
</script>

<style lang="scss" scoped>
.custom-report-container {
  padding: 20px;

  h2 {
    color: #333;
    margin-bottom: 20px;
  }
}
</style>
