<template>
  <div id="HealthSystem">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="followAll(true)" v-permission="['background_healthy.healthy_info.follow_user']">批量关注</button-icon>
          <button-icon color="origin" @click="followAll(false)" v-permission="['background_healthy.healthy_info.follow_user']">批量取关</button-icon>
          <button-icon color="origin" @click="addOrEditHandle('add', '')" v-permission="['background_healthy.healthy_info.add']">创建档案</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table ref="tableView" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row" row-key="key" @selection-change="setSelectList">
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #isFollow="{ row }">
              {{ row.is_follow ? '是' : '否' }}
            </template>
            <template #diseaseList="{ row }">
              <div v-for="(item, index) in row.disease_list" :key="index">
                {{ item.name }}
              </div>
            </template>
            <template #creatorName="{ row }">
              {{ row.creator_name || '--' }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" @click="addOrEditHandle('edit', row)" v-permission="['background_healthy.healthy_info.modify']">编辑</el-button>
              <el-button type="text" size="small" @click="isFollow([row.user_id], !row.is_follow)" v-permission="['background_healthy.healthy_info.follow_user']">{{row.is_follow ? '取消关注' : '关注'}}</el-button>
              <el-button type="text" size="small" @click="showDetail(row)">详情</el-button>
              <el-button type="text" size="small" @click="showAnalysisResult(row)">营养分析</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
      <!-- 创建或编辑档案 -->
      <CreateOrEditArchives :drawerType="createOrEditDrawerType" :isShow.sync="createOrEditDrawerShow" :fileData="fileData" @refresh="refreshHandle" :foodList="foodList" :diseaseList="diseaseList" />
      <!-- 档案详情 -->
      <ArchivesDetail :isShow.sync="archivesDetailShow" :archivesDetailParams="archivesDetailParams" />
      <!-- 营养分析 -->
      <AnalysisResult :isShow.sync="analysisResultShow" :selectedId="selectedId" />
    </div>
  </div>
</template>

<script>
import { debounce, deepClone } from '@/utils'
import CreateOrEditArchives from './components/CreateOrEditArchives.vue'
import ArchivesDetail from './components/ArchivesDetail.vue'
import AnalysisResult from './components/AnalysisResult.vue'
export default {
  name: 'HealthSystem',
  components: {
    CreateOrEditArchives,
    ArchivesDetail,
    AnalysisResult
  },
  data() {
    return {
      isLoading: false,
      searchForm: {
        select_time: {
          type: 'datetimerange',
          label: '创建时间',
          value: [],
          clearable: false
        },
        name: {
          label: '姓名',
          type: 'input',
          value: '',
          placeholder: '请输入',
          labelWidth: '100px'
        },
        person_no: {
          label: '人员编号',
          type: 'input',
          value: '',
          placeholder: '请输入',
          labelWidth: '100px'
        },
        phone: {
          label: '手机号',
          type: 'input',
          value: '',
          placeholder: '请输入',
          labelWidth: '100px'
        },
        is_follow: {
          label: '重点关注',
          type: 'select',
          value: '',
          placeholder: '全部',
          clearable: true,
          dataList: [
            {
              value: '',
              label: '全部'
            },
            {
              value: 1,
              label: '是'
            },
            {
              value: 0,
              label: '否'
            }
          ]
        }
      },
      tableData: [],
      currentTableSetting: [
        { label: '', type: 'selection', reserveSelection: true },
        { label: '人员编号', key: 'person_no' },
        { label: '姓名', key: 'name' },
        { label: '手机号', key: 'phone' },
        { label: '性别', key: 'gender' },
        { label: '个人特征', key: 'disease_list', type: "slot", slotName: 'diseaseList' },
        { label: '重点关注', key: 'is_follow', type: "slot", slotName: "isFollow" },
        { label: '创建时间', key: 'create_time' },
        { label: '操作人', key: 'creator_name ', type: "slot", slotName: "creatorName" },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "240" }
      ],
      createOrEditDrawerType: '',
      createOrEditDrawerShow: false,
      archivesDetailShow: false,
      analysisResultShow: false,
      selectedList: [],
      fileData: {},
      archivesDetailParams: {},
      selectedId: 0,
      diseaseList: [],
      foodList: [],
      page: 1,
      pageSize: 10,
      totalCount: 0
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.isLoading = true
      await this.getDataList()
      await this.getDiseaseList()
      await this.getFoodList()
      this.isLoading = false
    },
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.page = 1
        this.isLoading = true
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.page = 1
      this.initLoad()
    },
    // 获取个人特征
    getDiseaseList() {
      this.$apis.apiBackgroundHealthyDiseaseListPost({
        page_size: 9999
      }).then(res => {
        if (res.code === 0) {
          this.diseaseList = deepClone(res.data.results) || []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取食材
    getFoodList() {
      let params = {
        page: 1,
        page_size: 9999
      }
      this.$apis.apiBackgroundHealthyHealthyInfoIngredientTabooListPost(params).then(res => {
        if (res.code === 0) {
          this.foodList = deepClone(res.data) || []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取健康档案
    getDataList() {
      let params = {
        page: this.page,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchForm)
      }
      this.$apis.apiBackgroundHealthyHealthyInfoListPost(params).then(res => {
        if (res.code === 0) {
          this.tableData = res.data.results
          this.totalCount = res.data.count
          this.isLoading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (key === 'select_time' && data[key].value.length) {
          params.start_time = data[key].value[0]
          params.end_time = data[key].value[1]
        } else {
          params[key] = data[key].value || undefined
        }
      }
      return params
    },
    setSelectList(arr) {
      let newArr = arr.map(item => {
        return item.user_id
      })
      this.selectedList = this.removeDuplicates(newArr)
    },
    // 数组去重
    removeDuplicates(arr) {
      return [...new Set(arr)]
    },
    // 批量关注
    followAll(status) {
      if (this.selectedList.length) {
        this.isFollow(this.selectedList, status)
      } else {
        return this.$message.error('请先选择档案')
      }
    },
    // 关注该用户
    isFollow(ids, status) {
      this.$apis.apiBackgroundHealthyHealthyInfoFollowUserPost({
        user_ids: ids,
        is_follow: status
      }).then(res => {
        if (res.code === 0) {
          this.isLoading = true
          this.$message.success(status ? '关注成功' : '取关成功')
          this.$refs.tableView.clearSelection()
          this.getDataList()
          this.isLoading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    showDetail(data) {
      this.archivesDetailParams = {
        id: data.id,
        user_id: data.user_id
      }
      this.archivesDetailShow = true
    },
    addOrEditHandle(type, data) {
      this.createOrEditDrawerType = type
      if (type === 'add') {
        let obj = {
          name: '',
          person_no: '',
          phone: '',
          card_info_id: '',
          gender: '',
          height: '',
          weight: '',
          targe_weight: '',
          birthday: '',
          job: '',
          disease: '',
          taboo_food: '',
          is_follow: ''
        }
        this.fileData = deepClone(obj)
      } else {
        this.fileData = deepClone(data)
      }
      this.createOrEditDrawerShow = true
    },
    // 获取档案详情
    showAnalysisResult(data) {
      this.selectedId = data.id
      this.analysisResultShow = true
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.isLoading = true
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.isLoading = true
      this.getDataList()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
