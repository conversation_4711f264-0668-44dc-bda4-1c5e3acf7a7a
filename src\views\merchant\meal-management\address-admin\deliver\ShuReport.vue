<template>
  <div class="ShuReport container-wrapper">
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <el-checkbox v-model="isSelectAll" @change="selectAllChange" :disabled="tableData.length <=0">全部选择</el-checkbox>
          <button-icon color="plain" type="export" @click="handleExport" v-permission="['background_order.order_reservation.get_delivery_collect_by_y_export']">导出报表</button-icon>
          <button-icon color="plain" @click="openDialog">小票打印</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          :span-method="objectSpanMethod"
          @selection-change="handleSelectionChange"
          @select="selectSelection"
          @select-all="selectSelectionAll"
        >
          <el-table-column type="selection" prop="selection" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column prop="area_name" label="区域" align="center"></el-table-column>
          <el-table-column prop="l1_addr" label="一级" align="center"></el-table-column>
          <el-table-column prop="l2_addr" label="二级" align="center"></el-table-column>
          <el-table-column prop="l3_addr" label="三级" align="center"></el-table-column>
          <el-table-column prop="l4_addr" label="四级" align="center"></el-table-column>
          <el-table-column prop="l5_addr" label="五级" align="center"></el-table-column>
          <el-table-column prop="user_name" label="取餐人" align="center"></el-table-column>
          <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
          <el-table-column prop="remark" label="备注" align="center"></el-table-column>
          <el-table-column prop="food_name" label="菜品" align="center"></el-table-column>
          <el-table-column prop="food_count" label="数量" align="center"></el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <print-ticket
      :isshow.sync="dialogPrintVisible"
      type="order"
      :select-list-id="selectOrderId"
      @confirm="searchHandle"
    ></print-ticket>
  </div>
</template>

<script>
import { debounce, getSevenDateRange, deepClone } from '@/utils'
import { MEAL_TYPES } from '@/utils/constants'
import { mergeHandle, mergeRowAction } from '@/utils/table'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import PrintTicket from '@/components/PrintTicket'
export default {
  name: 'ShuReport',
  components: { PrintTicket },
  mixins: [exportExcel],
  data() {
    return {
      tableData: [],
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      isLoading: false, // 刷新数据
      searchFormSetting: {
        select_date: {
          clearable: false,
          label: '取餐时间',
          type: 'daterange',
          value: getSevenDateRange(1)
        },
        area_ids: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '配送区域',
          dataList: []
        },
        l1_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '一级地址',
          dataList: []
        },
        l2_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '二级地址',
          dataList: []
        },
        l3_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '三级地址',
          dataList: []
        },
        l4_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '四级地址',
          dataList: []
        },
        l5_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '五级地址',
          dataList: []
        },
        take_meal_time: {
          type: 'select',
          value: '',
          clearable: true,
          label: '取餐餐段',
          dataList: MEAL_TYPES
        },
        name: {
          type: 'input',
          value: '',
          label: '取餐人',
          placeholder: '请输入'
        },
        phone: {
          type: 'input',
          value: '',
          label: '手机号码',
          placeholder: '请输入手机号码'
        },
        org_ids: {
          type: 'organizationSelect',
          value: [this.$store.getters.organization],
          label: '消费点',
          clearable: true,
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        is_visitor: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看游客',
          value: false
        }
      },
      mergeOpts: {
        useKeyList: {
          id: [
            'selection',
            'l1_addr',
            'l2_addr',
            'l3_addr',
            'l4_addr',
            'l5_addr',
            'user_name',
            'phone',
            'remark'
          ]
        }, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        mergeKeyList: [
          'area_name'
        ] // 通用的合并字段，根據值合并
      },
      dialogPrintVisible: false,
      selectOrderId: [],
      isSelectAll: false, // 是否全部选择
      selectListIdCount: 0
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    'searchFormSetting.org_ids.value': function() {
      this.getAddressAreaList()
    },
    'searchFormSetting.area_ids.value': function() {
      this.searchFormSetting.l1_addr.dataList = []
      this.searchFormSetting.l1_addr.value = []
      this.loadAddress(1, this.searchFormSetting.area_ids.value)
    },
    'searchFormSetting.l1_addr.value': function() {
      this.searchFormSetting.l2_addr.dataList = []
      this.searchFormSetting.l2_addr.value = ''
      if (this.searchFormSetting.l1_addr.value.length) {
        this.loadAddress(2)
      }
    },
    'searchFormSetting.l2_addr.value': function() {
      this.searchFormSetting.l3_addr.dataList = []
      this.searchFormSetting.l3_addr.value = ''
      if (this.searchFormSetting.l2_addr.value.length) {
        this.loadAddress(3)
      }
    },
    'searchFormSetting.l3_addr.value': function() {
      this.searchFormSetting.l4_addr.dataList = []
      this.searchFormSetting.l4_addr.value = ''
      if (this.searchFormSetting.l3_addr.value.length) {
        this.loadAddress(4)
      }
    },
    'searchFormSetting.l4_addr.value': function() {
      this.searchFormSetting.l5_addr.dataList = []
      this.searchFormSetting.l5_addr.value = ''
      if (this.searchFormSetting.l4_addr.value.length) {
        this.loadAddress(5)
      }
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getShuList()
      this.getAddressAreaList()
      this.loadAddress(1)
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.dialogPrintVisible = false
        this.currentPage = 1
        this.resetSelectAll()
        this.getShuList()
      }
    }, 300),
    resetSearchHandle() {
      this.$refs.searchRef.resetForm()
      this.searchHandle()
    },
    resetHandle() {
      this.resetSelectAll()
      this.searchFormSetting.org_ids.value = [this.$store.getters.organization]
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' || data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.reservation_date_start = data[key].value[0]
            params.reservation_date_end = data[key].value[1]
          }
        }
      }
      return params
    },
    async getShuList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderReservationGetDeliveryCollectByXPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = []
        res.data.results.map((item, index) => {
          item.foods_st.map(food => {
            this.tableData.push({
              id: res.data.other_info[index].order_payment_id,
              ...item,
              ...food,
              index
            })
          })
        })
        // if (this.isSelectAll) {
        //   this.$refs.tableData.toggleAllSelection()
        // }
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    // 分页页数change事件
    async handleSizeChange(val) {
      this.pageSize = val
      await this.getShuList()
      this.changeTableSelection()
    },
    // 分页页码change事件
    async handleCurrentChange(val) {
      this.currentPage = val
      await this.getShuList()
      this.changeTableSelection()
    },
    // 当选择项发生变化时会触发该事件
    // handleSelectionChange(val) {
    //   this.selectOrderId = val.map(item => {
    //     return item.id
    //   })
    // },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    selectSelection(row) {
      this.changeSelectSelection(row)
    },
    selectSelectionAll(row) {
      this.changeSelectSelection(row)
    },
    changeSelectSelection (row) {
      let rowIdS = row.map(v => { return v.id })
      this.tableData.forEach((item, indx) => {
        if (!rowIdS.includes(item.id)) {
          var selectListIdIndex = this.selectOrderId.indexOf(item.id)
          if (selectListIdIndex !== -1) {
            this.selectOrderId.splice(selectListIdIndex, 1)
          }
        } else {
          this.selectOrderId.push(...rowIdS)
          this.selectOrderId = [...new Set(this.selectOrderId)]
        }
      })
      if (this.selectOrderId.length) {
        this.isSelectAll = this.selectListIdCount === this.selectOrderId.length ? true : 0
      }
    },
    changeTableSelection() {
      if (this.tableData.length) {
        this.tableData.forEach((item, index) => {
          // 匹配勾选上
          if (this.selectOrderId.includes(item.id)) {
            this.$nextTick(() => {
              this.$refs.tableData.toggleRowSelection(item);
            })
          }
        })
      }
    },
    async getAddressAreaList() {
      const res = await this.$apis.apiAddressAddersAreaAllPost({
        used_org_ids: this.searchFormSetting.org_ids.value
      })
      if (res.code === 0) {
        this.searchFormSetting.area_ids.dataList = [
          {
            name: '未命名区域',
            id: 0
          },
          ...res.data
        ]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态配送点数据
    async loadAddress(level, areaIds) {
      // 这里的level是这样的：一级组织的level=1，传给后端需要-1
      let params = {
        page: 1,
        page_size: 99999,
        level: level - 1,
        used_for_web: true
      }
      if (level === 2) {
        params.parent_id = this.searchFormSetting.l1_addr.value
      } else if (level === 3) {
        params.parent_id = this.searchFormSetting.l2_addr.value
      } else if (level === 4) {
        params.parent_id = this.searchFormSetting.l3_addr.value
      } else if (level === 5) {
        params.parent_id = this.searchFormSetting.l4_addr.value
      }
      if (areaIds) {
        params.area_id = areaIds
      }
      const res = await this.$apis.apiAddressAddersCenterListPost(params)
      if (res.code === 0) {
        if (level === 1) {
          this.searchFormSetting.l1_addr.dataList = res.data.results
        } else if (level === 2) {
          this.searchFormSetting.l2_addr.dataList = res.data.results
        } else if (level === 3) {
          this.searchFormSetting.l3_addr.dataList = res.data.results
        } else if (level === 4) {
          this.searchFormSetting.l4_addr.dataList = res.data.results
        } else if (level === 5) {
          this.searchFormSetting.l5_addr.dataList = res.data.results
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出报表
    handleExport() {
      const option = {
        type: 'ShuReport',
        url: 'apiBackgroundOrderOrderReservationGetDeliveryCollectByXPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          is_export: true
        }
      }
      this.exportHandle(option)
    },
    openDialog() {
      if (!this.selectOrderId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.dialogPrintVisible = true
    },
    /**
     * 全选监听
     */
    async selectAllChange(value) {
      this.selectOrderId = []
      this.$refs.tableData.clearSelection();
      if (value) {
        // 全选
        if (Reflect.has(this.$parent, 'getDeliveryCollectByXyIds')) {
          this.isLoading = true
          var list = await this.$parent.getDeliveryCollectByXyIds({
            ...this.formatQueryParams(this.searchFormSetting),
            page: this.currentPage,
            page_size: this.pageSize
          })
          this.isLoading = false
          if (list && list.length > 0) {
            this.selectOrderId = deepClone(list)
            this.selectListIdCount = list.length
            this.changeTableSelection()
          } else {
            this.isSelectAll = false
          }
        }
      } else {
        // 反选列表数据
        this.$refs.tableData.clearSelection()
        this.selectOrderId = []
      }
    },
    /**
     * 重置全选
     */
    resetSelectAll() {
      // if (this.isSelectAll) {
      this.isSelectAll = false
      this.selectOrderId = []
      this.$refs.tableData.clearSelection()
      // }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
</style>
