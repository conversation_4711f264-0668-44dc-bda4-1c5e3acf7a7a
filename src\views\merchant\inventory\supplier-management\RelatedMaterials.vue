<template>
  <div class="relationSupplierIngredient container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" label-width="100px" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="handleExport">导出编码</button-icon>
          <button-icon color="origin" @click="importHandler('import')">导入物资</button-icon>
          <button-icon color="origin" @click="addOrEditMaterials('add')">关联物资</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="materials_id"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #specs="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="openSpecsHandle(row)">查看</el-button>
            </template>
            <template #qycode="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="openQycodeHandle(row)">查看</el-button>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="addOrEditMaterials('modify', row)">
                编辑
              </el-button>
              <el-button type="text" size="small" class="ps-text" @click="deleteHandle('delete', row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- import start -->
    <import-page-dialog
      :show.sync="showImportDialog"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :url="importApi"
      :paramsData="importParamsData"
    ></import-page-dialog>
    <!-- import end -->
    <add-related-materials
      :showdialog.sync="dialogVisible"
      :type="dialogType"
      :title="dialogTitle"
      :info-data="dialogInfo"
      :confirm="searchHandle"
    />
    <dialog-message :show.sync="showQycode" top="22vh" title="查看" center :show-footer="false">
      <qrcode :value="qycodeValue" :options="qrcodeOptions" tag="img" :margin="10" alt class="face-img" />
      <div class="text-center">{{ dialogQrCodeInfo.materials_name }}</div>
    </dialog-message>
    <!-- 规格显示 -->
    <specs-dialog
      :showdialog.sync="dialogSpecsVisible"
      :info-data="dialogSpecsData"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, divide } from '@/utils'
import AddRelatedMaterials from './AddRelatedMaterials'
import qrcode from '@chenfengyuan/vue-qrcode'
import SpecsDialog from './SpecsDialog'

export default {
  name: 'RelatedMaterials',
  mixins: [exportExcel],
  components: { AddRelatedMaterials, qrcode, SpecsDialog },
  data() {
    return {
      importLink: '',
      supplierId: '',
      isLoading: false, // 刷新数据
      searchFormSetting: {
        name: {
          type: 'input',
          value: '',
          label: '物资名称',
          placeholder: '请输入物资名称'
        }
      },
      tableSettings: [
        { label: '', key: 'selection', type: 'selection', reserveSelection: true },
        { label: '物资名称', key: 'materials_name' },
        // { label: '参考单价', key: 'ref_unit_price', type: 'money' },
        { label: '最小单位', key: 'limit_unit_alias' },
        { label: '规格', key: 'specs', type: 'slot', slotName: 'specs' },
        { label: '物资编码', type: 'slot', slotName: 'qycode', key: 'supplier_manage_name_list' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right' }
      ],
      tableData: [],
      ingredientDialogVisible: false,
      ingredientInfo: {},
      ingredientDialogType: '',
      ingredientDialogTitle: '关联食材',
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      dialogType: '', // 弹窗类型
      dialogVisible: false,
      dialogTitle: '关联物资',
      dialogWidth: '',
      dialogInfo: {},
      showQycode: false,
      qycodeValue: '供应商：xxx供应商\n\n物资名称：生牛肉\n\n参考单价：18元\n\n单位：斤',
      qrcodeOptions: {
        width: 256,
        height: 256
        // errorCorrectionLevel: 'H' // L M Q H
      },
      dialogQrCodeInfo: {},
      // 导入的弹窗数据
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/供应商-导入物资.xlsx',
      importHeaderLen: 2,
      importApi: 'apiBackgroundDrpSupplierManageImportContactMaterialsPost',
      importParamsData: {
        id: +this.$route.query.id
      },
      selectionList: [],
      dialogSpecsVisible: false,
      dialogSpecsData: {}
    }
  },
  created() {
    this.supplierId = this.$route.query.id
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getRelatedMaterialsList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getRelatedMaterialsList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getRelatedMaterialsList() {
      if (!this.supplierId) return this.$message.error('参数格式错误')
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDrpMaterialsSupplierManageDetailsPost({
        supplier_manage_id: +this.$route.query.id,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results.map(v => {
          v.limit_unit_alias = v.limit_unit_management.reduce((prev, current) => {
            if (current.status !== 'delete') {
              if (prev) {
                return `${prev}，${current.unit_management_name}`
              } else {
                return current.unit_management_name
              }
            } else {
              return prev
            }
          }, '')
          return v
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    deleteHandle(type, data) {
      this.$confirm('确定删除该条物资数据？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.deleteSupplierIngredient({
              id: +this.$route.query.id,
              materials_id: data.materials_id
            })
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    // 删除事件
    async deleteSupplierIngredient(params) {
      const res = await this.$apis.apiBackgroundDrpSupplierManageContactMaterialsDeletePost(params)
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getRelatedMaterialsList()
      } else {
        this.$message.error(res.msg)
      }
    },
    dialogHandleClose() {
      this.dialogInfo = {}
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getRelatedMaterialsList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectionList = val
    },
    addOrEditMaterials(type, data) {
      this.dialogType = type
      if (type === 'modify') {
        this.dialogInfo = data
      }
      this.dialogVisible = true
    },
    openQycodeHandle(data) {
      this.dialogQrCodeInfo = data
      this.qycodeValue = `供应商：${data.qr_code.supplier_manage_name}\n\n物资名称：${
        data.qr_code.materials_name
      }\n\n参考单价：${divide(data.qr_code.ref_unit_price)}元\n\n单位：${data.unit_name}`
      this.showQycode = true
    },
    importHandler(type) {
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    handleExport() {
      if (this.selectionList.length === 0) {
        this.$message.error('请先选择需要导出的数据！')
        return
      }
      const params = {
        supplier_manage_id: +this.$route.query.id,
        materials_ids: this.selectionList.map(v => v.materials_id)
      }
      const option = {
        type: 'RelatedMaterials',
        url: 'apiBackgroundDrpSupplierManageMaterialsQrcodeExportPost',
        params
      }
      this.exportHandle(option)
    },
    openSpecsHandle(data) {
      this.dialogSpecsData = data
      this.dialogSpecsVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.ps-dialog {
  .nutrition-item {
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
}
</style>
