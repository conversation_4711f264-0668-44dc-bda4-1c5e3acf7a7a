<template>
  <div class="carbinding container-wrapper">
    <!--车辆绑定 -->
    <!--头部-->
    <refresh-tool @refreshPage="refreshHandler(true)" />
    <!--筛选 -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler" @reset="resetHandler">
      <div slot="append" class="ps-flex">
        <div class="el-form-item__label w-80 m-b-20">菜品分类</div>
        <food-category ref='foodCateGory' @input="changeCategory" style='width:200px' />
      </div>
    </search-form>
    <!--数据列表 -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="handlerExport()" v-permission="['background_order.manage_report.food_payment_weight_ranking_list_export']">导出报表</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" height="460" stripe
          header-row-class-name="ps-table-header-row" empty-text="暂无数据，请查询">
          <el-table-column label="序号" align="center" header-align="center" width="80px">
            <template slot-scope="scope">
              {{ (scope.$index + 1) + (currentPage - 1) * pageSize }}
            </template>
          </el-table-column>
          <table-column v-for="(item, index) in tableSettings" :key="index" :col="item">
            <template #foodWeight="{ row }">
              {{ row.food_weight | 0 }} g
            </template>
            <template #averageWeight="{ row }">
              {{ row.average_weight | 0 }} g
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination @size-change="handleSizeChange" @current-change="onPaginationChange" :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount" background class="ps-text" popper-class="ps-popper-select"></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { TABLE_HEAD_DATA_DISHES_TAKEN, SEARCH_FORM_SET_DATA_DISHES_TAKEN } from './constantsConfig.js'
import { deepClone, to, debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import FoodCategory from '../../home-page/components/FoodCategory.vue'
export default {
  name: "DishesTakenRanking",
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSettings: deepClone(TABLE_HEAD_DATA_DISHES_TAKEN), // 表格头部设置内容
      searchFormSetting: deepClone(SEARCH_FORM_SET_DATA_DISHES_TAKEN), // 搜索设置内容
      // tableSettingImport: deepClone(TABLE_HEAD_DATA_IMPORT_CAR), // 导入表格头部设置
      chooseCategory: []
    }
  },
  components: {
    FoodCategory
  },
  mixins: [exportExcel, report],
  created() {
    // this.initLoad()
  },
  methods: {
    /**
     * 刷新页面
     */
    refreshHandler(flag) {
      // 搜索重置
      this.currentPage = 1;
      if (flag) {
        this.$refs.searchRef.resetForm()
      }
      if (this.$refs.foodCateGory) {
        this.chooseCategory = []
        this.$refs.foodCateGory.reset()
      }
      this.tableData = []
      this.initLoad()
    },
    /**
     * 初始化数据
     */
    initLoad() {
      console.log("初始化");
      // 获取字典，现在从本地进行获取
      // 获取数据列表
      this.getFoodSaleRanking()
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    onPaginationChange(page) {
      console.log("onPaginationChange", page);
      this.currentPage = page
      this.getFoodSaleRanking()
    },
    /**
     * 显示条数改变
     * @param {*} pageSize
     */
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getFoodSaleRanking()
    },
    /**
     * 获取车辆绑定列表
     */
    async getFoodSaleRanking() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundReportCenterManageReportFoodPaymentWeightRankingListPost(params))
      console.log("apiBackgroundReportCenterManageReportFoodPaymentWeightRankingList", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        var resultList = res.data.results || []
        this.tableData = deepClone(resultList)
        this.totalCount = res.data.count || -1
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'select_time') {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          } else {
            params[key] = data[key].value
          }
        }
      }
      if (this.chooseCategory && this.chooseCategory.length > 0) {
        params.category = this.chooseCategory
      }
      return params
    },
    /**
     * 筛选
     */
    searchHandler: debounce(function (isSearch) {
      console.log("isSearch", isSearch);
      if (isSearch && isSearch === 'search') {
        if (this.searchFormSetting.select_time.value.length === 0) {
          return this.$message.error('请选择就餐时间')
        }
        this.currentPage = 1;
        this.initLoad()
      }
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.currentPage = 1;
      this.tableData = []
      if (this.$refs.foodCateGory) {
        this.chooseCategory = []
        this.$refs.foodCateGory.reset()
      }
    },
    // 导出
    handlerExport() {
      if (!this.tableData || this.tableData.length === 0) {
        return this.$message.error("没有可导出的数据")
      }
      const option = {
        type: 'ExportDishesTakenRanking',
        params: {
          payment_order_type: this.currentType,
          ...this.sort,
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 菜品选择修改
    changeCategory(value) {
      console.log("value", value);
      this.chooseCategory = value
    }
  }
}

</script>

<style lang="scss" scoped>
.carbinding {
  .el-table {
    border: 1px solid #fff;
  }
  .el-form-item__label {
    font-weight: 700;
  }
}
</style>
