<template>
  <div class="container-wrapper">
    <div class="waste-disposal-record">
      <h2 class="table-title">食堂餐厨废弃物处置记录本({{ currentMonth }}月)</h2>

      <el-table
        :data="displayData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <!-- 收货单位 -->
        <el-table-column label="收货单位" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.receiver" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>

        <!-- 时间 -->
        <el-table-column label="时间" align="center">
          <template slot-scope="scope">
            <el-date-picker v-model="scope.row.time" type="date" placeholder="选择日期" size="mini"></el-date-picker>
          </template>
        </el-table-column>

        <!-- 每次收取量 -->
        <el-table-column label="每次收取量" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.amount" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>

        <!-- 收货人 -->
        <el-table-column label="收货人" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.receiverPerson" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>

        <!-- 去向 -->
        <el-table-column label="去向" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.destination" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>

        <!-- 操作者 -->
        <el-table-column label="操作者" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.operator" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CanshuLajiManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 30,
      tableData: this.generateTableData(),
      currentMonth: new Date().getMonth() + 1
    };
  },
  computed: {
    displayData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  methods: {
    generateTableData() {
      const data = [];

      // 生成31行数据，每天数据
      for (let i = 1; i <= 31; i++) {
        data.push({
          receiver: '',
          time: '',
          amount: '',
          receiverPerson: '',
          destination: '',
          operator: ''
        });
      }

      return data;
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0',
        textAlign: 'center'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.waste-disposal-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  ::v-deep .el-table .cell {
    padding: 5px;
  }

  ::v-deep .el-input__inner {
    border: none;
    border-bottom: 1px solid #DCDFE6;
    border-radius: 0;
    text-align: center;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
