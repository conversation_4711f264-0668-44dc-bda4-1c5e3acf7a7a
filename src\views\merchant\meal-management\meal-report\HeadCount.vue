<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="export" @click="handleExport" v-permission="['background_order.order_report_meal.not_report_list_export']">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column
            v-for="(item, index) in table_column"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            align="center"
          >
            <template #default="{ row }" v-if="item.prop === 'card_user_groups'">
              <div>
                <span v-for="(item, index) in row.card_user_groups" :key="index">{{ item }}{{ index !== (row.card_user_groups.length -1) ? '、' : '' }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="display: flex; justify-content: space-between; align-items: center; padding-top:20px;">
        <div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: flex-end; width: 250px;">
          <!-- <table-statistics v-loading="isLoading" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText" :statistics="collect" /> -->
          <div class="bottom-statistics" v-loading="isLoading" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText">未报餐人数： {{ count }}</div>
          <div class="bottom-statistics">说明：当天无人报餐则不显示</div>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce, getSevenDateRange } from '@/utils'
import { HEADCOUNT_TABLE_CLOUMN } from '../meal-report/component/declaration'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import dayjs from 'dayjs'

export default {
  mixins: [exportExcel],
  data() {
    const defaultDate = getSevenDateRange(7);
    return {
      searchFormSetting: {
        select_date: {
          clearable: false,
          pickerOptions: {},
          label: '报餐时间',
          type: 'daterange',
          value: [defaultDate[0], defaultDate[1]]
        },
        name: {
          type: 'input',
          value: '',
          label: '姓名',
          placeholder: '请输入姓名'
        },
        person_no: {
          type: 'input',
          value: '',
          label: '人员编号',
          placeholder: '请输入人员编号'
        },
        card_department_group: {
          type: 'departmentSelect',
          multiple: true,
          flat: false,
          label: '部门',
          value: [],
          placeholder: '请选择部门',
          checkStrictly: true,
          dataList: [],
          collapseTags: true,
          limit: 1,
          level: 1
        },
        card_user_group_ids: {
          type: 'groupSelect',
          label: '适用分组',
          value: [],
          placeholder: '请选择',
          multiple: true
        }
      },
      tableData: [],
      table_column: HEADCOUNT_TABLE_CLOUMN,
      elementLoadingText: "正在加载中...",
      count: 0,
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getHeadCountList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getHeadCountList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderReportMealNotReportListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.count = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        // 判断报餐时间是否只有七天
        let startDate = dayjs(this.searchFormSetting.select_date.value[0])
        let endDate = dayjs(this.searchFormSetting.select_date.value[1]).format('YYYY-MM-DD')
        console.log('时间', startDate, dayjs(this.searchFormSetting.select_date.value[0]).format('YYYY-MM-DD'), endDate, startDate.diff(endDate, 'day'))
        if (Math.abs(startDate.diff(endDate, 'day')) > 7) {
          console.log('选择的时间超过七天')
          return this.$message.error('所选择的时间超过七天，请缩小时间范围后重试')
        }
        this.getHeadCountList()
      }
    }, 300),
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getHeadCountList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getHeadCountList()
    },
    handleExport() {
      const option = {
        url: 'apiBackgroundOrderOrderReportMealNotReportListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.bottom-statistics {
  // width: 250px;
  font-weight: bold;
  font-size: 14px;
  color: #606266;
}
</style>
