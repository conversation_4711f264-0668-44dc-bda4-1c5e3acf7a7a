<template>
  <div class="Warehousing">
    <h3 class="m-t-20">仓库管理/入库单/入库</h3>
    <!-- <refresh-tool :showRefresh="false" /> -->
    <div class="form-container">
      <el-form
        v-loading="isLoading"
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        size="small"
        class="m-l-20 m-t-10"
      >
        <el-form-item label="" prop="" label-width="0" class="vertical-middle clearfix">
          <span class="m-l-12 vertical-middle">
            <span class="label-text">当前仓库：</span>
            {{ detailData.warehouse_name }}
          </span>
          <span class="m-l-12 vertical-middle">
            <span class="label-text m-l-60">单据编号：</span>
            {{ detailData.trade_no }}
          </span>
          <el-form-item label="入库时间：" prop="entryTime" label-width="100px" class="inline-block m-b-0 m-l-60">
            <el-date-picker
              v-model="formData.entryTime"
              type="datetime"
              placeholder="选择日期"
              class="ps-picker"
              style="width: auto"
              popper-class="ps-poper-picker"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
            ></el-date-picker>
          </el-form-item>
          <span class="float-r">
            <el-button class="ps-btn" @click="addMaterials('entry_info')">添加物资</el-button>
          </span>
        </el-form-item>
        <div class="red inbound-tips">相同物资存在多条数据，物资名称、入库价、有效期、供应商名称如一致，将在保存时进行数据合并</div>
        <div class="red inbound-tips">相同物资，入库价和有效期必须一致，否则将无法保存入库</div>
        <el-form-item label="" label-width="20px">
          <el-table
            :data="formData.tableData"
            ref="tableRef"
            stripe
            size="small"
            border
            max-height="600"
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in materialsTableSettings" :key="item.key" :col="item">
              <template #count="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.number"
                  :prop="'tableData.' + index + '.count'"
                >
                   <!-- @input="inputHandle($event, row, index, 'count')" -->
                  <el-input v-model="row.count" placeholder="请输入" :maxlength="6" class="ps-input"></el-input>
                </el-form-item>
              </template>
              <template #daterange="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.valid_date"
                  :prop="'tableData.' + index + '.valid_date'"
                >
                  <span class="inline-block ps-btn-span pointer">
                    {{
                      row.valid_date && row.valid_date.length > 1
                        ? `${row.valid_date[0]}至${row.valid_date[1]}`
                        : '请选择'
                    }}
                    <el-date-picker
                      v-model="row.valid_date"
                      type="daterange"
                      placeholder="选择日期"
                      class="ps-picker"
                      style="width: auto"
                      popper-class="ps-poper-picker"
                      value-format="yyyy-MM-dd"
                      @change="changeValidateHandle"
                    ></el-date-picker>
                  </span>
                </el-form-item>
              </template>
              <template #supplier="{ row, index }">
                <span v-if="row.select_purchase">{{ row.supplier_manage_name }}</span>
                <el-form-item
                  v-else
                  label=""
                  :prop="'tableData.' + index + '.supplier_manage_id'"
                  :rules="formRules.supplier_manage_id"
                  class="m-b-0"
                >
                  <el-select
                    v-model="row.supplier_manage_id"
                    filterable
                    class="ps-select"
                    popper-class="ps-popper-select"
                    placeholder="请选择"
                    @change="changeSupplier($event, index)"
                  >
                    <el-option
                      v-for="option in row.supplier_list"
                      :key="option.supplier_manage_id"
                      :label="option.supplier_manage_name"
                      :value="option.supplier_manage_id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template #specs="{ row, index }">
                <el-form-item
                  label=""
                  :prop="'tableData.' + index + '.specs'"
                  :rules="formRules.specs"
                  class="m-b-0"
                >
                  <el-select v-model="row.material_specification_id" :disabled="!row.supplier_manage_id" clearable filterable class="ps-select" popper-class="ps-popper-select" placeholder="请选择" @change="changeSpecs($event, index)">
                    <el-option v-for="option in row.specification_list" :key="option.id" :label="`1${option.unit_management_name}*${option.count}${option.limit_unit_name}*${option.net_content}${option.net_content_unit}`" :value="option.id" ></el-option>
                  </el-select>
                </el-form-item>
              </template>
              <template #entryPrice="{ row, index }">
                <el-form-item
                  label=""
                  label-width="0"
                  class="m-b-0"
                  :rules="formRules.entry_price"
                  :prop="'tableData.' + index + '.entry_price'"
                >
                  <el-input v-model="row.entry_price" :maxlength="5" placeholder="请输入" class="ps-input" @change="changeValidateHandle"></el-input>
                </el-form-item>
              </template>
              <template #operation="{ row, index }">
                <el-button type="text" size="small" class="ps-text" @click.stop="addMaterials('replace_materials', row, index)">
                  替换物资
                </el-button>
                <el-button type="text" size="small" class="ps-warn" @click.stop="deleteMaterials(index, row)">
                  删除
                </el-button>
              </template>
            </table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="合计" prop="remark">{{ totlePrice }}元</el-form-item>
        <el-form-item label="入库备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            class="ps-textarea w-280"
            :rows="3"
            :maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="上传附件">
          <file-upload
            ref="fileUploadRef"
            :fileList="formData.fileLists"
            type="enclosure"
            prefix="inventory"
            :show-file-list="false"
            accept=".jpeg,.jpg,.png,.bmp"
            :rename="false"
            :multiple="true"
            :limit="9"
            :before-upload="beforeUpload"
            @fileLists="getFileLists"
          >
            <template v-slot="scope">
              <!-- {{ scope }} -->
              <el-button :loading="scope.loading" class="ps-origin" size="small" type="text">
                上传{{ scope.loading ? '中' : '' }}
              </el-button>
            </template>
          </file-upload>
          <!-- <p style="color:#a5a5a5; line-height: 1.5;">附件不超过20M</p> -->
        </el-form-item>
        <el-form-item v-if="previewList.length">
          <el-collapse v-model="activeCollapse" style="max-width: 60%">
            <el-collapse-item :title="collapseTitle" name="1">
              <div class="img-item" v-for="(img, index) in previewList" :key="img + index">
                <el-image
                  :preview-src-list="previewList"
                  :initial-index="index"
                  class="upload-img m-r-6"
                  :src="img"
                  fit="contain"
                ></el-image>
                <span class="img-tools">
                  <!-- <i class="el-icon-zoom-in m-r-10"></i> -->
                  <i class="el-icon-delete" @click.stop="deleteUploadImg(index)"></i>
                </span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
        <el-form-item>
          <el-button class="ps-origin-btn" size="medium" @click="backHandle">取 消</el-button>
          <el-button class="ps-origin-btn" size="medium" @click="submitHandle">确 认</el-button>
          <!-- <el-button class="ps-origin-btn" size="medium" @click="pickingInventory">领料出入库</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <!-- 弹窗 -->
    <choose-list-dialog
      :showdialog.sync="showDialog"
      :title="dialogTitle"
      :type="dialogType"
      :api="dialogApi"
      :detailApi="dialogDetailApi"
      :search-setting="dialogSearchSetting"
      :table-settings="dialogTableSettings"
      :params="dialogParams"
      :defaultSelect="dialogSelect"
      :rowKey="dialogRowKey"
      :showSelectLen="showSelectLen"
      @confirmChoose="confirmChooseHandle"
    ></choose-list-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSuffix, getUrlFilename, times, deepClone, parseTime } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import * as dayjs from 'dayjs'
import { validateNumber, validateOneDecimalCount } from '@/utils/form-validata'
import ChooseListDialog from '../../components/ChooseListDialog'
import { twoDecimal } from '@/utils/validata'
import NP from 'number-precision'

export default {
  name: 'Warehousing',
  mixins: [exportExcel],
  components: { ChooseListDialog },
  data() {
    let validataPrice = (rule, value, callback) => {
      if (value !== '') {
        if (!oneDecimal(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    return {
      isLoading: false, // 刷新数据
      warehouseId: '',
      warehouseName: '',
      detailData: {},
      // form表单数据
      formData: {
        entryType: '',
        entryTime: '',
        remark: '',
        fileLists: [],
        tableData: []
      },
      formRules: {
        name: [{ required: true, message: '请选择', trigger: 'change' }],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        // entryTime: [{ required: true, message: '请选择入库时间', trigger: 'change' }],
        valid_date: [{ validator: this.validateEntryValidDate, trigger: 'blur' }],
        entryType: [{ required: true, message: '请选择入库类型', trigger: 'change' }],
        number: [{ validator: validateOneDecimalCount, trigger: 'change' }],
        entry_price: [{ validator: this.validateEntryPrice, trigger: 'change' }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < dayjs().subtract(1, 'day').valueOf()
        }
      },
      // 入库物资
      materialsTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '供应商名称', key: 'supplier_manage_id', type: 'slot', slotName: 'supplier', minWidth: '100px' },
        { label: '规格', key: 'material_specification_id', type: 'slot', slotName: 'specs' },
        { label: '最小单位', key: 'unit_name' },
        { label: '预计入库数量', key: 'expected_entry_count' },
        { label: '实际入库数量', key: 'count', type: 'slot', slotName: 'count' },
        { label: '参考单价', key: 'ref_unit_price', type: 'money' },
        { label: '入库价', key: 'entry_price', type: 'slot', slotName: 'entryPrice' },
        { label: '有效期', key: 'valid_date', type: 'slot', slotName: 'daterange', minWidth: '80px' },
        // { label: '期限（天）', key: 'deadline', type: "slot", slotName: "day" },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      activeCollapse: [],
      showDialog: false, // 是否开启弹窗
      dialogLoading: false, // 弹窗loading
      dialogTitle: '选择物资',
      dialogType: '', // 弹窗的状态，add/modify
      dialogData: {}, // 弹窗数据
      remoteLoading: false,
      dialogTableSettings: [],
      dialogSearchSetting: {},
      dialogParams: {
        warehouse_id: this.$route.query.warehouse_id
      },
      dialogApi: '',
      dialogDetailApi: '',
      dialogSelect: [],
      dialogRowKey: 'materials_id',
      showSelectLen: false,
      validateFieldList: [] // 要手动触发的校验项
    }
  },
  computed: {
    previewList() {
      const result = this.formData.fileLists.map(v => v.url)
      this.setPreviewListTitle(result)
      return result
    },
    // 合计
    totlePrice() {
      let price = this.formData.tableData.reduce((prev, next) => {
        return NP.plus(NP.times(next.count, next.entry_price || 0), prev)
      }, 0)
      return price
    }
  },
  created() {
    this.warehouseId = this.$route.query.warehouse_id
    this.warehouseName = this.$route.query.warehouse_name
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getInboundOrderDetail()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.initLoad()
    }, 300),
    // 获取详情数据
    async getInboundOrderDetail() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: +this.$route.query.id,
        warehouse_id: this.warehouseId
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpEntryInfoDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.detailData = res.data
        // this.tableData = res.data.results
        this.formData.entryType = res.data.inventory_entry_type
        this.formData.entryTime = res.data.entry_time
        this.formData.remark = res.data.remark
        if (res.data.image_json && res.data.image_json.length > 0) {
          let fileImages = []
          res.data.image_json.forEach((v, index) => {
            fileImages.push({
              url: v,
              name: getUrlFilename(v),
              status: 'success',
              uid: new Date().getTime() + index
            })
          })
          this.formData.fileLists = fileImages
        } else {
          this.formData.fileLists = []
        }
        if (res.data.entry_data) {
          this.formData.tableData = res.data.entry_data.map(v => {
            if (v.start_valid_date && v.end_valid_date) {
              v.valid_date = [v.start_valid_date, v.end_valid_date]
            } else {
              v.valid_date = []
            }
            v.count = v.expected_entry_count
            v.entry_price = v.entry_price / 100
            v.supplier_list = v.price_info
            v.image_json = v.image_json || []
            v.is_origin = true
            // 规格的
            v.specification_list = v.price_info.find(current => current.supplier_manage_id === v.supplier_manage_id).specification
            v.specs_item = v.specification_list.find(sp => sp.id === v.material_specification_id)
            // 为什么要拷贝一层呢，这是防止与detailData中的数据相互影响，去掉可能会导致输入框失效
            return deepClone(v)
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 选择供应商
    changeSupplier(e, currentIndex) {
      let current = this.formData.tableData[currentIndex]
      let currentSupplier = current.supplier_list.find(v => v.supplier_manage_id === e)
      // 选择不同供应商的物资单价是不同的，需要对应切换
      // this.$set(current, 'ref_unit_price', currentSupplier.ref_unit_price)
      this.$set(current, 'supplier_manage_name', currentSupplier.supplier_manage_name)
      // 设置下规格列表数据
      this.$set(current, 'specification_list', currentSupplier.specification || [])
      // 修改供应商得重置规格和单价等数据
      this.$set(current, 'specs_item', '')
      this.$set(current, 'material_specification_id', '')
      this.$set(current, 'unit_name', '')
      this.$set(current, 'ref_unit_price', '')
      // 合并相同物资和相同供应商的数据，做完规格再看去重
      this.formData.tableData = this.uniqueMaterials(this.formData.tableData, true)
    },
    // 修改规格
    changeSpecs(e, currentIndex) {
      let current = this.formData.tableData[currentIndex]
      let currentSpecs = current.specification_list.find(v => v.id === e)
      this.$set(current, 'specs_item', currentSpecs)
      this.$set(current, 'unit_name', `${currentSpecs.limit_unit_name}*${currentSpecs.net_content}${currentSpecs.net_content_unit}`) // 最小单位：例：瓶*330ml
      this.$set(current, 'ref_unit_price', currentSpecs.unit_price)
      // // // 合并相同物资和相同供应商的数据
      this.formData.tableData = this.uniqueMaterials(this.formData.tableData, true)
    },
    inputHandle(e, row, index) {
      if (!row.is_origin) {
        this.$set(row, 'expected_entry_count', e)
      }
    },
    // 添加物资
    addMaterials(type, data, index) {
      this.dialogType = type
      if (data) {
        this.dialogData = data
        this.dialogData.index = index
      } else {
        this.dialogData = {}
      }
      // 添加物资
      if (type === 'entry_info') {
        this.dialogTitle = '添加物资'
        this.dialogApi = 'apiBackgroundDrpMaterialsGetMaterialsListPost'
        this.showSelectLen = true
        this.dialogSearchSetting = {
          name: {
            type: 'input',
            value: '',
            label: '物资名称',
            placeholder: '请输入'
          }
        }
        this.dialogTableSettings = [
          { label: '', key: 'selection', type: 'selection', reserveSelection: true },
          { label: '物资名称', key: 'materials_name' },
          { label: '当前库存', key: 'current_num' },
          { label: '供应商', key: 'supplier', type: 'slot', slotName: 'supplier' },
          { label: '规格', key: 'specs', type: 'slot', slotName: 'specs' },
          { label: '单价', key: 'unit_price', type: 'slot', slotName: 'unitPrice' },
          { label: '入库数量', key: 'count', type: 'slot', slotName: 'count' },
          { label: '有效期', key: 'date', type: 'slot', slotName: 'date' }
        ]
        this.dialogRowKey = 'materials_id'
      }
      if (type === 'replace_materials') {
        this.dialogTitle = '替换物资'
        this.dialogApi = 'apiBackgroundDrpMaterialsGetMaterialsListPost'
        this.showSelectLen = false
        this.dialogSearchSetting = {
          name: {
            type: 'input',
            value: '',
            label: '物资名称',
            placeholder: '请输入'
          }
        }
        this.dialogTableSettings = [
          { label: '', key: 'id', type: 'slot', slotName: 'radio', width: '80px' },
          { label: '物资名称', key: 'materials_name' },
          { label: '当前库存', key: 'current_num' }
        ]
        this.dialogRowKey = 'materials_id'
        this.dialogSelect = data.materials_id
      }
      this.showDialog = true
    },
    // 弹窗确定事件
    confirmChooseHandle(res) {
      this.showDialog = false
      console.log(11231, res)
      let errorMaterials = []
      let result = []
      res.data.forEach(v => {
        if (v.price_info && v.price_info.length > 0) {
          let item = {
            materials_name: v.materials_name,
            materials_id: v.materials_id,
            current_num: v.current_num,
            count: v.count,
            expected_entry_count: v.count,
            unit_name: `${v.specs_item.limit_unit_name}*${v.specs_item.net_content}${v.specs_item.net_content_unit}`, // 最小单位：例：瓶*330ml
            unit_id: v.unit_id,
            ref_unit_price: v.specs_item.unit_price, // 参考价
            entry_price: '',
            supplier_manage_id: v.supplier,
            supplier_manage_name: v.supplier_manage_name,
            supplier_list: v.price_info,
            valid_date: v.date,
            image_json: [],
            //  加了规格以后新增的
            material_specification_id: v.specs,
            specs_item: v.specs_item,
            specification_list: v.specification_list
          }
          // let currentSupplier = null
          // // 选择物资时如果物资绑定多个供应商的时候，价格取供应商中最低的
          // v.price_info.forEach(supplier => {
          //   if (currentSupplier) {
          //     if (supplier.ref_unit_price < currentSupplier.ref_unit_price) {
          //       currentSupplier = supplier
          //     }
          //   } else {
          //     currentSupplier = supplier
          //   }
          // })
          // if (currentSupplier) {
          //   item.supplier_manage_id = currentSupplier.supplier_manage_id
          //   item.supplier_manage_name = currentSupplier.supplier_manage_name
          //   item.ref_unit_price = currentSupplier.ref_unit_price || 0
          // }
          result.push(item)
        } else {
          errorMaterials.push(v.materials_name)
        }
      })
      if (res.type === 'entry_info') {
        if (result.length > 0) {
          if (this.formData.tableData.length > 0) {
            this.formData.tableData = this.mergeArrays(this.formData.tableData, result)
          } else {
            this.formData.tableData = result
          }
        }
      }
      if (errorMaterials.length > 0) {
        this.$notify.error({
          title: '错误',
          message: `物资：${errorMaterials.join('，')}未关联供应商！`
        });
      }
      // 替换物资
      if (res.type === 'replace_materials') {
        if (result.length > 0) {
          let materials = result[0]
          let current = this.formData.tableData[this.dialogData.index]
          if (materials.materials_id !== current.materials_id) {
            this.$set(current, 'materials_name', materials.materials_name)
            this.$set(current, 'materials_id', materials.materials_id)
            this.$set(current, 'current_num', materials.current_num)
            this.$set(current, 'unit_id', materials.unit_id)
            this.$set(current, 'unit_name', materials.unit_name)
            this.$set(current, 'supplier_manage_id', materials.supplier_manage_id)
            this.$set(current, 'supplier_manage_name', materials.supplier_manage_name)
            this.$set(current, 'supplier_list', materials.supplier_list)
            this.$set(current, 'ref_unit_price', materials.ref_unit_price)
          }
          this.formData.tableData = this.mergeArrays(this.formData.tableData, [])
        }
      }
    },
    // 合并新旧数据，以同一供应商、相同物资、规格、相同入库价、相同有效期的数据为前提合并，数量这些需要累加起来
    mergeArrays(tableData, newData) {
      // 创建一个空对象用于存储合并的结果
      let merged = {}
      // 遍历 tableData 数组
      for (let i = 0; i < tableData.length; i++) {
        let current = tableData[i]
        let key = `${current.materials_id}_${current.supplier_manage_id}_${current.material_specification_id}_${current.entry_price}_${JSON.stringify(current.valid_date)}`
        // 当存在则证明数据重复，需合并
        if (merged[key]) {
          // 合并入库数量
          merged[key].count = NP.plus(merged[key].count, current.count)
        } else {
          merged[key] = current
        }
      }
      newData.forEach(item => {
        // 存在相同数据，合并数量并重新计算合计
        let key = `${item.materials_id}_${item.supplier_manage_id}_${item.material_specification_id}_${item.entry_price}_${JSON.stringify(item.valid_date)}`
        if (merged[key]) {
          // 合并默认以添加物资为主，当采购单和添加物资的物资重复时
          if (merged[key].select_purchase) {
            merged[key].select_purchase = false
          }
          merged[key].count = NP.plus(merged[key].count, item.count)
          merged[key].expected_entry_count = merged[key].count
          // merged[key] = Object.assign({}, merged[key], item)
          if (merged[key].valid_date.length === 0) {
            merged[key].valid_date = item.valid_date
          }
          // 原有的物资图片要默认携带上去
          if (item.image_json && item.image_json.length > 0) {
            merged[key].image_json = item.image_json
          }
        } else {
          // 不存在直接往merged里面新增
          merged[key] = item
        }
      })
      return Object.values(merged)
    },
    // 删除物资
    deleteMaterials(index) {
      this.formData.tableData.splice(index, 1)
      // 手动触发下校验
      this.validateFieldList = []
      this.formData.tableData.forEach((v, index) => {
        this.validateFieldList.push(`tableData.${index}.entry_price`, `tableData.${index}.valid_date`)
      })
      this.changeValidateHandle()
    },
    getFileLists(fileLists) {
      this.formData.fileLists = fileLists
    },
    // 上传图片前钩子
    beforeUpload(file) {
      let uploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      if (!uploadType.includes(getSuffix(file.name))) {
        this.$message.error('请检查上传文件格式！')
        return false
      }
      const isLt20M = file.size / 1024 / 1024 <= 2
      if (!isLt20M) {
        this.$message.error('上传附件大小不能超过 2M')
      }
      return isLt20M
    },
    //
    setPreviewListTitle(result) {
      this.collapseTitle = '查看附件(' + result.length + ')'
    },
    // 删除图片
    deleteUploadImg(index) {
      const fileUploadRef = this.$refs.fileUploadRef
      if (this.formData.fileLists[index]) {
        fileUploadRef && fileUploadRef.spliceFileData(this.formData.fileLists[index].uid)
      }
      this.formData.fileLists.splice(index, 1)
    },
    // 格式化数据
    formatParams() {
      let params = {
        inventory_entry_type: this.formData.entryType,
        // entry_time: this.formData.entryTime,
        warehouse_id: this.warehouseId,
        remark: this.formData.remark,
        is_warehousing: true
      }
      if (this.formData.entryTime) {
        params.entry_time = this.formData.entryTime
      }
      params.entry_data = this.uniqueMaterials(this.formData.tableData).map(v => {
        return {
          materials_id: v.materials_id,
          name: v.name,
          count: v.count,
          unit_id: v.unit_id,
          unit_name: v.unit_name,
          entry_price: times(v.entry_price),
          start_valid_date: v.valid_date[0],
          end_valid_date: v.valid_date[1],
          supplier_manage_id: v.supplier_manage_id,
          image_json: v.image_json,
          limit_unit_id: v.specs_item.limit_unit_id
        }
      })
      params.image_json = this.previewList
      return params
    },
    // 物资去重，根据供应商、物资、规格（新的）、入库价、有效期，都相同的则合并，数量累加
    uniqueMaterials(data) {
      const arr = deepClone(data)
      const newArray = []
      const tmp = {}
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        const key = `${item.materials_id}_${item.supplier_manage_id}_${item.material_specification_id}_${item.entry_price}_${JSON.stringify(item.valid_date)}`
        if (!tmp[key]) {
          tmp[key] = item
        } else { // 数量累加
          tmp[key].count = NP.plus(tmp[key].count, item.count)
          tmp[key].expected_entry_count = tmp[key].count
          // 原有的物资图片要默认携带上去
          if (item.image_json && item.image_json.length > 0) {
            tmp[key].image_json = item.image_json
          }
        }
      }
      return Object.values(tmp)
    },
    // 当列表已存在相同供应商的物资时，弹窗显示是否合并
    mergeSupplierMaterials() {
      this.$confirm(`同一供应商、相同物资、相同入库价、相同有效期的数据已合并`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        showCancelButton: false,
        center: true,
        beforeClose: (action, instance, done) => {
          done()
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 确定
    submitHandle() {
      this.$refs.formRef.validate(valid => {
        console.log(valid)
        if (valid) {
          if (!this.formData.tableData.length) return this.$message.error('请先选择物资！')
          let api = 'apiBackgroundDrpEntryInfoModifyPost'
          let params = this.formatParams()
          params.id = +this.$route.query.id
          this.showConfirmDialog(api, params)
        }
      })
    },
    showConfirmDialog(api, params) {
      this.$confirm(`同一供应商、相同物资、相同入库价、相同有效期的数据将会合并`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          done()
          if (action === 'confirm') {
            this.sendDataHandle(api, params)
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 发送
    async sendDataHandle(api, params) {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(this.$apis[api](params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.mgs || '成功')
        this.$backVisitedViewsPath(this.$route.path, 'InboundOrder')
      } else {
        this.$message.error(res.msg)
      }
    },
    backHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'InboundOrder')
    },
    // 校验输入入库价，当相同存在相同物质相同供应商时入库价不同的不允许保存，规避其它地方自动管理入库单重复的问题
    validateEntryPrice(rule, value, callback, source, options) {
      // 校验走到这应该都会有这数据的
      // field：tableData.0.changeCount，tableData.1.changeCount，还有tableData[0].changeCount这种（先不做这种的兼容吧，记住prop的设置不能用这种）
      // 切割拿值做校验
      try {
        const fieldArr = rule.field.split('.')
        const row = this.formData[fieldArr[0]][Number(fieldArr[1])]
        if (value) {
          if (!twoDecimal(value)) {
            callback(new Error('格式错误'))
          } else {
            // callback()
            // 是否相同
            let isDifferent = true
            let fieldList = [] // 定义个变量存下需要手动校验的prop
            // 校验通过后再做是否又相同物质和供应商的校验
            for (let index = 0; index < this.formData.tableData.length; index++) {
              const item = this.formData.tableData[index];
              // 当存在物质id和供应商id相同时，判断入库价是否相同，不同则抛出错误
              if ((row.materials_id === item.materials_id) && (row.supplier_manage_id === item.supplier_manage_id) && item.entry_price) {
                // 存下需要校验的字段prop，用于后面手动触发相同物质和供应商的数据校验
                fieldList.push(`tableData.${index}.entry_price`)
                if (Number(value) !== Number(item.entry_price)) {
                  // callback(new Error('入库价不一致'))
                  // this.validateFieldList.push(`tableData.${index}.entry_price`)
                  isDifferent = false
                  // 已存在相关手动的校验规则直接跳出循环
                  if (this.validateFieldList.length > 0) {
                    break;
                  }
                }
              }
            }
            // 当不存在需要手动触发的校验prop才添加
            if (this.validateFieldList.length === 0) {
              this.validateFieldList = fieldList
            }
            if (isDifferent) {
              callback()
            } else {
              callback(new Error('入库价不一致'))
            }
          }
        } else {
          callback(new Error('请输入'))
        }
      } catch (error) {
        console.log(error)
        callback(new Error('校验出错了'))
      }
    },
    // 校验有效期
    validateEntryValidDate(rule, value, callback, source, options) {
      // 校验走到这应该都会有这数据的
      // field：tableData.0.changeCount，tableData.1.changeCount，还有tableData[0].changeCount这种（先不做这种的兼容吧，记住prop的设置不能用这种）
      // 切割拿值做校验
      try {
        const fieldArr = rule.field.split('.')
        const row = this.formData[fieldArr[0]][Number(fieldArr[1])]
        let isDifferent = true
        let fieldList = [] // 定义个变量存下需要手动校验的prop
        // 校验通过后再做是否又相同物质和供应商的校验
        for (let index = 0; index < this.formData.tableData.length; index++) {
          const item = this.formData.tableData[index];
          // 当存在物质id和供应商id相同时，判断有效期是否相同，不同则抛出错误
          if ((row.materials_id === item.materials_id) && (row.supplier_manage_id === item.supplier_manage_id)) {
            // 存下需要校验的字段prop，用于后面手动触发相同物质和供应商的数据校验
            fieldList.push(`tableData.${index}.valid_date`)
            if (JSON.stringify(value) !== JSON.stringify(item.valid_date)) {
              isDifferent = false
              // 已存在相关手动的校验规则直接跳出循环
              if (this.validateFieldList.length > 0) {
                break;
              }
            }
          }
        }
        // 当不存在需要手动触发的校验prop才添加
        if (this.validateFieldList.length === 0) {
          this.validateFieldList = fieldList
        }
        if (isDifferent) {
          callback()
        } else {
          callback(new Error('有效期不一致'))
        }
      } catch (error) {
        console.log(error)
        callback(new Error('校验出错了'))
      }
    },
    // 数据修改时校验下其它相同物质相同供应商的数据
    changeValidateHandle: debounce(function(e) {
      if (this.validateFieldList.length > 0) {
        const formRef = this.$refs.formRef
        let validateLen = this.validateFieldList.length
        formRef.validateField(this.validateFieldList.slice(0), (e) => {
          validateLen--
          // 重置，很重要
          if (validateLen === 0) {
            this.validateFieldList = []
          }
        })
      }
    }, 200),
    // 领料出入库
    pickingInventory() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (!this.formData.tableData.length) {
            return this.$message.error('请先选择物资！')
          }
          this.$confirm("领料出入库将对物资进行入库和出库，确认进行出入库？", '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            center: true,
            customClass: 'myPickerDialog',
            beforeClose: async (action, instance, done) => {
              if (action === 'confirm') {
                instance.confirmButtonLoading = true
                // 先调用入库
                let inFlag = await this.setInBoundOrder()
                if (inFlag) {
                  // 入库成功先查关联单据
                  console.log(this.formData.tableData, 'this.formData.tableData')
                  let getMaterialFlag = await this.getAutoMaterialsTrade()
                  if (getMaterialFlag) {
                    // 关联单据成功并设置好以后就出库拉
                    let outBoundFlag = await this.setOutboundOrder()
                    instance.confirmButtonLoading = false
                    if (outBoundFlag) {
                      this.$backVisitedViewsPath(this.$route.path, 'DocumentManagement')
                    }
                    console.log("outBoundFlag", outBoundFlag)
                    done()
                  } else {
                    instance.confirmButtonLoading = false
                    done()
                  }
                } else {
                  instance.confirmButtonLoading = false
                  done()
                }
              }
              done()
            }
          }).then(() => {
          }).catch(() => {
          })
        }
      })
    },
    // 格式化数据
    formatParamsOutbound() {
      console.log("formatParamsOutbound", this.formData.tableData)
      let params = {
        inventory_exit_type: "RECEIVE_EXIT",
        warehouse_id: this.warehouseId,
        exit_time: parseTime(new Date(), '{y}-{m}-{d} {h}:{i}'),
        remark: this.formData.remark
      }
      if (this.$route.query.type === 'to_exit_info') {
        params.sg_trade_no = this.$route.query.sg_trade_no
      }
      params.exit_data = this.formData.tableData.map(v => {
        return {
          materials_id: v.materials_id,
          name: v.name,
          count: v.count * v.specs_item.count,
          unit_id: v.unit_id,
          // 这个啥？
          current_num: v.current_num,
          limit_unit_id: v.specs_item.limit_unit_id,
          unit_name: v.unit_name,
          entry_fee: Number((times(v.entry_price) / v.specs_item.count).toFixed(0)),
          contact_trade_list: v.contact_trade_list && v.contact_trade_list.map(v => {
            return {
              trade_no: v.trade_no,
              count: v.use_count,
              supplier_manage_id: v.supplier_manage_id
            }
          }),
          key: v.key
        }
      })
      params.image_json = this.previewList
      return params
    },
    // 设置入库
    setInBoundOrder() {
      return new Promise((resolve) => {
        let params = this.formatParams(true)
        params.skip_approve = true
        this.$apis.apiBackgroundDrpEntryInfoAddPost(params).then(res => {
          if (res && res.code === 0) {
            resolve(true)
          } else {
            this.$message.error(res.msg || '入库失败！')
            resolve(false)
          }
        }).catch(error => {
          console.log("error", error.msg);
          this.$message.error(error.msg || '入库失败！')
          resolve(false)
        })
      })
    },
    // 获取自动关联的入库单数据
    getAutoMaterialsTrade() {
      return new Promise((resolve) => {
        let inParams = {
          is_new: true,
          warehouse_id: this.warehouseId,
          materials_data: this.formData.tableData.map(v => {
            return {
              materials_id: v.materials_id,
              count: v.count * v.specs_item.count,
              limit_unit_id: v.specs_item.limit_unit_id
            }
          })
        }
        this.$apis.apiBackgroundDrpEntryInfoAutoGetMaterialsTradePost(inParams).then(res => {
          if (res && res.code === 0) {
            this.setAutoMaterialsTrade(this.formData.tableData, res.data)
            resolve(true)
          } else {
            this.$message.error(res.msg || '关联单据失败！')
            resolve(false)
          }
        }).catch(error => {
          this.$message.error(error.msg || '关联单据失败！')
          resolve(false)
        })
      })
    },
    // 出库
    setOutboundOrder() {
      return new Promise((resolve) => {
        let outParams = this.formatParamsOutbound()
        outParams.skip_approve = true
        this.$apis.apiBackgroundDrpExitInfoAddPost(outParams).then(res => {
          if (res && res.code === 0) {
            this.$message.success('出入库成功！')
            resolve(true)
          } else {
            this.$message.error(res.msg || '入库成功，出库失败！')
            resolve(false)
          }
        }).catch(error => {
          this.$message.error(error.msg || '入库成功，出库失败！')
          resolve(false)
        })
      })
    },
    // 设置自动关联的入库单数据
    setAutoMaterialsTrade(materialsList, autoMaterials) {
      if (!autoMaterials || autoMaterials.length === 0) {
        return
      }
      // 先给后台返回的关联单号生成唯一的id 每条数据的唯一标识，不同物资、供应商、单位、入库价，分为不同数据
      autoMaterials = autoMaterials.map(item => {
        item.key = `${item.materials_id}_${item.supplier_manage_id}_${item.limit_unit_id}_${item.entry_price}`
        return item
      })
      // 给列表每个项，加上匹配的关联单据
      if (materialsList && materialsList.length > 0) {
        materialsList = materialsList.map(item => {
          console.log("item.key", item.entry_price, item.specs_item.count, Number((times(item.entry_price) / item.specs_item.count).toFixed(0)))
          item.key = `${item.materials_id}_${item.supplier_manage_id}_${item.specs_item.limit_unit_id}_${Number((times(item.entry_price) / item.specs_item.count).toFixed(0))}`
          console.log("item.key", item.key);
          item.contact_trade_list = autoMaterials.filter(subItem => {
            return subItem.key === item.key
          })
          return item
        })
      }
      this.formData.tableData = deepClone(materialsList)
      console.log(this.formData.tableData, 'this.formData.tableData')
    }
  }
}
</script>

<style lang="scss" scoped>
.Warehousing {
  .m-b-0 {
    margin-bottom: 0 !important;
  }
  .m-0 {
    margin: 0 !important;
  }
  .vertical-middle {
    vertical-align: middle;
  }
  .inbound-tips {
    margin-left: 20px;
    margin-bottom: 4px;
    font-size: 13px;
  }
  ::v-deep.el-form-item__label {
    font-weight: 500;
  }
  .form-container {
    margin-top: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    .ps-btn-span {
      position: relative;
      color: #ff9b45;
      font-size: 12px;
      text-decoration: underline;
      // text-underline-offset: 5px; /* 设置下划线与文本基线之间的距离 */
      line-height: 1.2;
      vertical-align: middle;
      &:hover {
        color: #e58b3e;
      }
      .ps-picker {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        opacity: 0;
      }
    }
    .upload-img {
      width: 90px;
      height: 90px;
    }
    .img-item {
      display: inline-block;
      position: relative;
      transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
      .img-tools {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ffffff;
        font-size: 16px;
        transition: 0.3s;
        i {
          cursor: pointer;
          color: #ff9b45;
        }
      }
      &:hover {
        .img-tools {
          display: inline-block;
        }
      }
    }
  }
  .label-text {
    font-size: 14px;
    // font-weight: 600;
    color: #606266;
  }
  .w-220 {
    width: 220px;
  }
  .w-280 {
    width: 280px;
  }
  .w-auto {
    width: 300px;
  }
  .m-b-0 {
    margin-bottom: 0;
    &.is-error {
      margin-bottom: 20px;
    }
  }
  .is-error {
    .ps-btn-span {
      color: red;
      text-decoration: underline;
    }
  }
}
</style>
