<template>
  <el-drawer
    :title="title"
    :visible.sync="drawerVisible"
    size="60%"
    :before-close="handleClose"
    class="ps-el-drawer history-record-drawer"
    :wrapperClosable="false"
    append-to-body
    modal-append-to-body
  >
    <!-- 时间筛选 -->
    <div class="filter-section">
      <el-form :inline="true" size="small">
        <el-form-item label="时间：">
          <el-date-picker
            v-model="searchDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="searchHandle"
            :picker-options="pickerOptions"
            class="date-picker"
            :clearable="false"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 历史记录表格 -->
    <div class="table-content">
      <el-table
        :data="historytableData"
        v-loading="isLoading"
        stripe
        border
        max-height="600"
        header-row-class-name="ps-table-header-row"
      >
        <el-table-column prop="operation_time" label="操作时间"  width="200"/>
        <el-table-column prop="operator" label="操作人" width="150" />
        <el-table-column prop="operation_type_alias" label="操作" width="70" />
        <el-table-column label="操作前内容" align="center">
          <template slot-scope="scope">
            <slot name="beforeContent" :row="scope.row" :index="scope.$index">
              <div v-for="(item,index) in getBeforeContent(scope.row)" :key="index">{{ item }}</div>
            </slot>
          </template>
        </el-table-column>
        <el-table-column label="操作后内容" align="center">
          <template slot-scope="scope">
            <slot name="afterContent" :row="scope.row" :index="scope.$index">
              <div v-for="(item,index) in getAfterContent(scope.row)" :key="index">{{ item }}</div>
            </slot>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <pagination
      :onPaginationChange="onPaginationChange"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :pageSizes="[5, 10, 20, 50, 100, 500]"
      :layout="'total, prev, pager, next, sizes, jumper'"
      :total="totalCount"
    ></pagination>

    <!-- 底部操作区 -->
    <div class="ps-el-drawer-footer">
      <el-button size="small" class="w-100" @click="drawerVisible = false">关闭</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { getDateRang, debounce, to } from '@/utils'

export default {
  name: 'HistoryRecordDialog',
  props: {
    // 弹窗标题
    title: {
      type: String,
      default: '历史记录'
    },
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 获取列表数据接口
    api: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      searchDate: getDateRang(-3, { format: '{y}-{m}-{d}' }), // 时间范围，默认三天
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      historytableData: [],
      tempStartDate: null, // 临时存储选择的开始时间
      pickerOptions: {
        disabledDate: (time) => {
          const now = new Date()

          // 基本限制：不能选择未来时间
          if (time.getTime() > now.getTime()) {
            return true
          }

          // 如果已经选择了开始时间，限制结束时间
          if (this.tempStartDate) {
            const oneMonthLater = new Date(this.tempStartDate.getTime() + 30 * 24 * 60 * 60 * 1000)
            // 结束时间不能超过开始时间后30天，也不能早于开始时间
            return time.getTime() > Math.min(oneMonthLater.getTime(), now.getTime()) ||
                   time.getTime() < this.tempStartDate.getTime()
          }

          return false
        },
        onPick: (obj) => {
          // 当选择了开始时间后，限制结束时间最多为开始时间后一个月
          if (obj.minDate && !obj.maxDate) {
            // 临时存储选择的开始时间，用于限制结束时间
            this.tempStartDate = obj.minDate
          } else {
            // 清除临时存储
            this.tempStartDate = null
          }
        },
        shortcuts: [
          {
            text: '最近3天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3 * 24 * 60 * 60 * 1000)
              start.setHours(0, 0, 0, 0)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 7 * 24 * 60 * 60 * 1000)
              start.setHours(0, 0, 0, 0)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000)
              start.setHours(0, 0, 0, 0)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    tableData() {
      return this.data || []
    }
  },
  methods: {
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getOperateLogList()
    }, 300),

    // 重置
    handleReset() {
      this.searchDate = this.getDefaultDateRange()
      this.currentPage = 1
      this.getOperateLogList()
    },

    // 获取历史记录
    async getOperateLogList() {
      if (!this.api || !this.$apis[this.api]) {
        return this.$message.error('获取接口地址失败！')
      }
      this.isLoading = true
      let params = {
        start_time: this.searchDate.length ? this.searchDate[0] + ' 00:00:00' : '',
        end_time: this.searchDate.length ? this.searchDate[1] + ' 23:59:59' : '',
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (this.type) {
        params.ledger_data_type = this.type
      }
      const [err, res] = await to(
        this.$apis[this.api](params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        let results = data.results || []
        this.totalCount = data.count
        this.historytableData = results
      } else {
        this.$message.error(res.msg)
      }
    },

    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getOperateLogList()
    },
    // 关闭抽屉
    handleClose() {
      this.drawerVisible = false
      this.$emit('close')
    },
    // 获取操作前内容
    getBeforeContent(row) {
      let extra = row.extra || {}
      let beforeData = extra.before || []
      return beforeData
    },
    // 获取操作后内容
    getAfterContent(row) {
      let extra = row.extra || {}
      let afterData = extra.after || []
      return afterData
    }
  },
  watch: {
    // 监听抽屉显示状态，打开时自动搜索
    drawerVisible(newVal) {
      if (newVal) {
        this.getOperateLogList()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.history-record-drawer {
  // 内容区域样式
  .filter-section {
    margin: 0 20px 20px 20px;
    padding: 15px;
    border-radius: 4px;

    .date-picker {
      width: 420px;
    }
  }

  .table-content {
    margin: 0 20px 20px 20px;
  }

  .pagination-wrapper {
    text-align: right;
    padding: 0 20px 20px 20px;
  }
}

// 底部操作区样式
.ps-el-drawer-footer {
  position: sticky;
  bottom: 0;
  padding: 20px;
  background-color: #fff;
  z-index: 10;
}
</style>
