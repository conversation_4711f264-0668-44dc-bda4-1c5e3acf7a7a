<template>
  <div class="acticity-recharges container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="80px" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="gotoAddDiscountCoupon">新增优惠券</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column prop="coupon_no" label="活动编号" align="center"></el-table-column>
          <el-table-column prop="name" label="活动名称" align="center"></el-table-column>
          <el-table-column prop="start_time" label="开始时间" align="center"></el-table-column>
          <el-table-column prop="end_time" label="结束时间" align="center"></el-table-column>
          <!-- <el-table-column prop="check_price" label="额度" align="center"></el-table-column> -->
          <el-table-column prop="coupon_type_desc" label="优惠类型" align="center">
            <!-- eslint-disable vue/no-unused-vars -->
            <template slot-scope="scope">
              充值赠送
            </template>
          </el-table-column>
          <el-table-column prop="limit_type_alias" label="限额" align="center">
            <template slot-scope="scope">
              {{scope.row.limit_type_alias}}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="140" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="gotoModifyRandomReduce(scope.row)">编辑</el-button>
              <!-- <el-button type="text" size="small">活动情况查看</el-button>
              <el-button type="text" size="small">规则查看</el-button>
              <el-button type="text" size="small">停用</el-button>-->
              <el-button type="text" class="ps-warn-text" size="small" @click="deleteDiscountCouponHandle(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'

export default {
  name: 'DiscountCoupon',
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '名称',
          value: '',
          placeholder: '请输入活动名称'
        },
        type: {
          type: 'select',
          label: '类型',
          value: '',
          placeholder: '请选择活动类型',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '启用',
            value: 'enable'
          }, {
            label: '停用',
            value: 'disable'
          }, {
            label: '过期',
            value: 'expire'
          }, {
            label: '删除',
            value: 'delete'
          }]
        },
        status: {
          type: 'select',
          label: '状态',
          value: '',
          placeholder: '请选择活动状态',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '启用',
            value: 'enable'
          }, {
            label: '停用',
            value: 'disable'
          }, {
            label: '过期',
            value: 'expire'
          }, {
            label: '删除',
            value: 'delete'
          }]
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDiscountCouponList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getDiscountCouponList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 获取重置活动列表
    async getDiscountCouponList() {
      this.isLoading = true
      let params = {
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (this.searchFormSetting.name.value) {
        params.name = this.searchFormSetting.name.value
      }
      if (this.searchFormSetting.status.value) {
        params.status = this.searchFormSetting.status.value
      }
      const [err, res] = await to(this.$apis.apiBackgroundMarketingRechargeListPost(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          if (v.status === 'enable') {
            v.isOpen = true
          } else {
            v.isOpen = false
          }
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    async deleteDiscountCouponHandle(row) {
      let params = {
        coupon_no: row.coupon_no,
        status: 'delete'
      }
      this.$confirm(`确定删除吗?`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            // await this.$sleep(2222)
            const [err, res] = await to(this.$apis.apiBackgroundMarketingRechargeChangePost(params))
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getDiscountCouponList()
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
      // const [err, res] = await to(this.$apis.apiBackgroundMarketingRechargeChangePost(params));
      // this.isLoading = false
      // if (err) {
      //   this.$message.error(err.message)
      //   return
      // }
      // if (res.code === 0) {
      //   this.$message.success(res.msg)
      //   this.getDiscountCouponList()
      // } else {
      //   this.$message.error(res.msg)
      // }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDiscountCouponList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDiscountCouponList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    // 添加充值活动
    gotoAddDiscountCoupon() {
      this.$router.push({
        name: "MerchantAddDiscountCoupon",
        params: { type: "add" }
      });
    },
    gotoModifyRandomReduce(data) {
      this.$router.push({
        name: "MerchantAddDiscountCoupon",
        params: {
          type: "modify"
        },
        query: {
          id: data.coupon_no
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.acticity-recharges {}
</style>
