// 是否是邮箱
export const isEmail = (value) => {
  return /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value);
}

// 是否包含中文
export const haveCNChars = (value) => {
  return /[\u4e00-\u9fa5]/.test(value);
}

// 是否是手机号
export const isPhone = (value) => {
  return /^1[3,4,5,6,7,8,9][0-9]{9}$/.test(value.toString());
}

// 是否是座机
export const isTel = (value) => {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(value.toString());
}

export const isUserLength5To20 = (value) => {
  return /^\w{5,20}$/.test(value)
}

export const isPassLength8To20 = (value) => {
  return /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(value)
}
// [~!#@$%^*&()_+{}\[\]|\\;:'",<.>\/?]

// 是否是座机或手机号
export const isTelOrPhone = (value) => {
  return /(^([0-9]{3,4}-)?[0-9]{7,8}$)|(^1[3,4,5,6,7,8,9][0-9]{9}$)/.test(value.toString());
}

// 是否是金额，允许写 0    小数字精确到2位
export const isPrice = (value) => {
  return /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(value.toString());
}
// 是否数字或者长期
export const isNum = (value) => {
  return /\d/.test(value);
}

// 校验正数并可为0，可包含2位小数
export const positiveMoney = (value) => {
  return /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(value)
}

// 校验数字最多2位整数，可包含1位小数，可输入格式1，1.0 11.0等
export const onePositiveValue = (value) => {
  return /^(([1-9]?[0-9])|([1-9]?[0-9]\.[1-9])|([1-9]?[1-9]\.[0]))$/.test(value)
}

// 校验数字最多1位整数，可包含3位小数，当前正则没法判断0.000这种情况
export const threePositiveValue = (value) => {
  return /^(([0-9])|([0-9]\.\d{1,3})|([1-9]\.[0]))$/.test(value)
}

// 可检验负数
export const money = (value) => {
  return /^-?(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(value)
}

// 检验正整数
export const integer = (value) => {
  // /^\d+$/
  return /^[0-9]+$/.test(value)
}

// 校验>=0的数字并且最多只能保留一位小数
export const oneDecimal = (value) => {
  return /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/.test(value)
}

// 校验>0的数字并且最多只能保留2位小数(金额)
export const twoDecimal = (value) => {
  return /^([1-9]\d*|0?\.\d{1,2})(\.\d{1,2})?$/.test(value)
}

// 匹配数字或字母。不区分大小写
export const defaultNo = (value) => {
  return /^[a-zA-Z0-9]+$/.test(value)
}
