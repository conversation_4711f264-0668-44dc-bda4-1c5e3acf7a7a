<template>
  <div class="ScaleRecognitionStatistics container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
        </div>
      </div>

      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="organization_name" label="所属组织" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="material_name" label="物资名称" align="center"></el-table-column>
          <el-table-column prop="url" label="识别图片" align="center">
            <template slot-scope="scope">
              <el-image class="img-item" :src="scope.row.url" fit="fit" :preview-src-list="getPreViewList(scope.row)">
                <div slot="error" class="image-slot m-t-20">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="results1" label="识别1" align="center"></el-table-column>
          <el-table-column prop="results2" label="识别2" align="center"></el-table-column>
          <el-table-column prop="results3" label="识别3" align="center"></el-table-column>
          <el-table-column prop="results4" label="识别4" align="center"></el-table-column>
          <el-table-column prop="results5" label="识别5" align="center"></el-table-column>
          <el-table-column prop="recognize_result" label="识别结果" align="center">
            <template slot-scope="scope">
              <span v-if="!scope.row.recognize_data || scope.row.recognize_data.length === 0 ">--</span>
              <span v-else-if="scope.row.recognize_result" class="results-color-green">正确</span>
              <span v-else class="results-color-red">错误</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <div style="display: flex; justify-content: space-between; align-items: center">
        <div class="font-size-14" style="padding-left: 20px">
          <span>识别数量：{{ totalData.total_count }}</span>
          <span style="margin-left: 50px">识别率：{{ totalData.success_rate }}%</span>
          <span style="margin-left: 50px" class="ps-red">未识别数量：{{ totalData.failed_count || 0 }}</span>
        </div>
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'ScaleRecognitionStatistics',
  mixins: [exportExcel],
  data() {
    return {
      totalData: {
        total_count: 0,
        success_rate: 0,
        failed_count: 0
      },
      isLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '创建时间',
          clearable: false,
          value: [
            dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD')
          ]
        },
        organization_ids: {
          type: 'organizationSelect',
          multiple: true,
          checkStrictly: true,
          isLazy: true,
          label: '所属组织',
          value: [],
          placeholder: '请选择消费点',
          role: 'super'
        },
        // trade_no: {
        //   label: '单据编号',
        //   type: 'input',
        //   value: '',
        //   placeholder: '请输入',
        //   clearable: true,
        //   maxlength: 20
        // },
        recognize_results: {
          type: 'select',
          label: '识别结果',
          value: '',
          clearable: true,
          multiple: true,
          collapseTags: true,
          placeholder: '',
          dataList: [
            {
              label: '正确',
              value: 1
            },
            {
              label: '错误',
              value: 0
            }
          ]
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getApproveRulesList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getApproveRulesList()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getApproveRulesList() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundAdminRecognitionRateStatisticsListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )

      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }

      if (res.code === 0) {
        this.tableData = res.data.results.map((item) => {
          item.url = item.image_json.length ? item.image_json[0] : ''
          if (item.recognize_data && item.recognize_data.length > 0) {
            item.recognize_data.map((iiiitem, index) => {
              item['results' + (index + 1)] = iiiitem
            })
          } else {
            for (let i = 1; i <= 5; i++) {
              item['results' + i] = '--'
            }
          }
          return item
        })
        this.totalCount = res.data.count || 0
        this.totalData.total_count = res.data.recognition_count
        this.totalData.success_rate = res.data.success_rate
        this.totalData.failed_count = res.data.failed_count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getApproveRulesList()
    },
    // 图片预览
    getPreViewList(row) {
      var url = row.url || ''
      if (url) {
        return [url]
      }
      return []
    }
  }
}
</script>
<style lang="scss" scoped>
.ScaleRecognitionStatistics{
  .results-color-red{
    color: red;
  }
  .results-color-green{
    color: green;
  }
}
</style>
