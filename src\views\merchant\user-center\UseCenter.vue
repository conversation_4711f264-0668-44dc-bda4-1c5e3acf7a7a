<template>
  <div class="user-center container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting.sync="searchFormSetting" label-width="105px" @search="searchHandle" :autoSearch="false"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          <span>数据列表</span>
          <el-checkbox
            class="ps-checkbox m-l-20"
            v-model="userCheckedAll"
            @change="changeCheckedAll"
          >
            全选
          </el-checkbox>
        </div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openUserDialog('add')" v-permission="['card_service.card_user.add']">新增用户</button-icon>
          <button-icon color="plain" type="Import" @click="openImport('ImportUser')" v-permission="['card_service.card_user.batch_import']">导入用户</button-icon>
          <button-icon color="plain" type="export" @click="agreementGotoExport" v-permission="['card_service.card_user.list_export']">导出用户</button-icon>
          <button-icon color="plain" type="Import" @click="openImport('ImportEditUser')" v-permission="['card_service.card_user.import_modify_card_user']">导入编辑</button-icon>
          <button-icon color="plain" type="Import" @click="gotoImportFace" v-permission="['card_service.card_face.batch_import']">导入人脸</button-icon>
          <button-icon color="origin" type="mul" @click="mulOperation('delFaces')" v-permission="['card_service.card_face.batch_delete']">批量删除人脸</button-icon>
          <button-icon color="plain" type="open" @click="mulOperation('openFacesPay')" v-permission="['card_service.card_user_face.switch']">开启人脸支付</button-icon>
          <button-icon color="plain" type="close" @click="mulOperation('closeFacesPay')" v-permission="['card_service.card_user_face.switch']">关闭人脸支付</button-icon>
          <button-icon color="plain" type="Import" @click="openImport('ImportGroup')" v-permission="['card_service.card_user.import_set_card_user_group']">导入分组</button-icon>
          <button-icon color="origin" type="mul" @click="openGroupDialog()" v-permission="['card_service.card_user.batch_set_card_user_group']">批量分组</button-icon>
          <button-icon color="plain" type="Import" @click="openImport('ImportReturnCard')" v-permission="['card_service.card_user.import_card_user_quit']">导入退卡</button-icon>
          <button-icon color="origin" type="mul" @click="mulOperation('freeze','mul')" v-permission="['card_service.card_user.batch_freeze_card_user']">批量冻结</button-icon>
          <button-icon color="origin" type="mul" @click="clickSyncUser" v-permission="['card_service.card_user.user_sync_to_org']">用户同步</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
          @select="selectSelection"
          @select-all="selectSelectionAll"
        >
          <el-table-column type="selection" width="50" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column label="人脸" align="center">
            <!-- 新增代码 -->
            <template slot-scope="scope">
              <img v-if="scope.row.face_url" :src="scope.row.face_url" alt class="face-img" @click="openFaceDialog(scope.row)"/>
              <el-button v-else type="text" class="ps-text" @click="openFaceDialog(scope.row)">上传</el-button>
            </template>
          </el-table-column>
          <el-table-column label="人脸支付" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.facepay" :disabled="!isCurrentOrgs(scope.row.organization) || scope.row.person_status_alias === '退户'" @change="switchFacePay('oneFacesPay',scope.row,  scope.row.facepay)" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="姓名"></el-table-column>
          <el-table-column key="person_no" prop="person_no" label="人员编号" align="center">
            <template slot-scope="scope">
              <span>{{sensitiveSetting.person_no ? scope.row.person_no: '****'}}</span>
            </template>
          </el-table-column>
          <el-table-column  key="phone" label="手机号" align="center">
            <template slot-scope="scope">
              <div>
                <div>{{sensitiveSetting.phone ? scope.row.phone: '****'}}</div>
                <!-- <el-popover placement="top-start" title="" width="150" trigger="hover">
                  <template>
                    <div class="popover-box">{{ scope.row.phone }}</div>
                  </template>
                  <el-button type="text" size="mini" style="padding:0;" class="ps-text" slot="reference">关联手机号</el-button>
                </el-popover> -->
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="sensitiveSetting.id_number" prop="id_number" label="身份证号" align="center">
            <template slot-scope="scope">
              <span>{{getIdCardNo(scope.row)}}
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="card_group_verbose" label="人群" align="center"></el-table-column> -->
          <el-table-column prop="organization_alias" label="来源" align="center"></el-table-column>
          <el-table-column prop="card_user_group_alias" label="分组" align="center"></el-table-column>
          <el-table-column prop="gender_alias" label="性别" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="card_department_group_alias" label="部门" align="center"></el-table-column>
          <el-table-column prop="balance_total" label="储值钱包余额" align="center">
            <template slot-scope="scope">
              <div :style="{ color: isDisplayString(scope.row.display_balance) ? '#ccc' : '' }">{{ scope.row.balance_total }}</div>
              <el-popover placement="top-start" title="" width="180"  trigger="hover">
                <template>
                  <div class="popover-box" v-for="(item,index) in scope.row.wallet_balance" :key="index">
                    <span>{{item.source_organization}}</span>
                    <span>￥{{ item.balance}}</span>
                  </div>
                </template>
                <el-button type="text" size="mini" style="padding:0;" class="ps-text" slot="reference"
                  >查看更多</el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="subsidy_balance_total" label="补贴钱包余额" align="center">
            <template slot-scope="scope">
              <div :style="{ color: isDisplayString(scope.row.display_subsidy_balance) ? '#ccc' : '' }">{{ scope.row.subsidy_balance_total }}</div>
              <el-popover placement="top-start" title="" width="180" trigger="hover">
                <template>
                  <div class="popover-box" v-for="(item,index) in scope.row.wallet_subsidy_balance" :key="index">
                    <span>{{item.source_organization}}</span>
                    <span>￥{{ item.subsidy_balance }}</span>
                  </div>
                </template>
                <el-button type="text" size="mini" style="padding:0;" class="ps-text" slot="reference"
                  >查看更多</el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="complimentary_balance" label="赠送钱包余额" align="center">
            <template slot-scope="scope">
              <div :style="{ color: isDisplayString(scope.row.display_complimentary_balance) ? '#ccc' : '' }">{{ scope.row.complimentary_balance}}</div>
              <el-popover placement="top-start" title="" width="180" trigger="hover">
                <template>
                  <div class="popover-box" v-for="(item,index) in scope.row.wallet_complimentary_balance" :key="index">
                    <span>{{item.source_organization}}</span>
                    <span>￥{{ item.complimentary_balance}}</span>
                  </div>
                </template>
                <el-button type="text" size="mini" style="padding:0;" class="ps-text" slot="reference"
                  >查看更多</el-button
                >
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="account_status_alias" label="账户状态" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
               <span :style="{color:(scope.row.person_status =='ENABLE'?'#56ba58':'#02a7f0')}">{{ scope.row.account_status_alias}}</span>
            </template>
          </el-table-column>
          <el-table-column  key="card_no" prop="card_no" label="卡号" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{sensitiveSetting.card_no ? scope.row.card_no :'****'}}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="card_status_alias"
            label="卡状态"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span :class="setCloumnTextColor(scope.row)">{{scope.row.card_status_alias}}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间" width="220" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <div>创建时间：{{scope.row.create_time}}</div>
              <!-- 如果新增生效时间 list表生效时间默认获取创建时间 create_time-->
              <div>生效时间：{{formatTime(scope.row, 'effective')}}</div>
              <div>失效时间：{{formatTime(scope.row, 'expiration')}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="spare_field" width="150" align="center">
            <template slot="header" slot-scope="/* eslint-disable vue/no-unused-vars */ scope">
              <div v-if="isModifyField">
                <el-input
                  v-model.trim="spareField"
                  size="mini"
                  placeholder="请输入"
                  id="el-table-column-header-input"
                  maxlength="10"
                  @blur="saveSetting($event, 1)"
                ></el-input>
              </div>
              <div v-else class="el-table-column-header-label">
                <span>{{ spareField }}</span>
                <i @click.stop="isModifyField = true"
                 v-if="isTopLevel"
                 v-permission="['card_service.card_operate.modify_spare_field_alias']"
                 class="el-icon-edit-outline m-l-3">
                </i>
              </div>
            </template>
            <template slot-scope="scope">
              {{ scope.row.spare_field }}
            </template>
          </el-table-column>
          <el-table-column prop="spare_field2" width="150" align="center">
            <template slot="header" slot-scope="/* eslint-disable vue/no-unused-vars */ scope">
              <div v-if="isModifyField2">
                <el-input
                  v-model.trim="spareField2"
                  size="mini"
                  placeholder="请输入"
                  id="el-table-column-header-input"
                  maxlength="10"
                  @blur="saveSetting($event, 2)"
                ></el-input>
              </div>
              <div v-else class="el-table-column-header-label">
                <span>{{ spareField2 }}</span>
                <i @click.stop="isModifyField2 = true"
                  v-if="isTopLevel"
                  v-permission="['card_service.card_operate.modify_spare_field_alias']"
                  class="el-icon-edit-outline m-l-3">
                </i>
              </div>
            </template>
            <template slot-scope="scope">
              {{ scope.row.spare_field2 }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                :disabled="!isCurrentOrgs(scope.row.organization) || scope.row.person_status_alias === '退户'"
                @click="openUserDialog('edit', scope.row)"
                v-permission="['card_service.card_user.modify']"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="small"
                v-show="scope.row.person_status !== 'FREEZE'"
                :disabled="!isCurrentOrgs(scope.row.organization) || scope.row.person_status_alias === '退户'"
                @click="mulOperation('freeze', 'one', scope.row)"
                v-permission="['card_service.card_user.batch_freeze_card_user']"
                >冻结</el-button
              >
              <el-button
                type="text"
                size="small"
                class="ps-red"
                :disabled="scope.row.person_status_alias === '退户'"
                v-show="scope.row.card_status == 'ENABLE'"
                @click="mulOperation('loss','',scope.row.id)"
                v-permission="['card_service.card_operate.loss']"
                >挂失</el-button
              >
              <el-button
                :disabled="scope.row.person_status_alias === '退户'"
                v-show="scope.row.card_status === 'LOSS'"
                type="text"
                size="small"
                class="ps-green-text"
                @click="mulOperation('cancelLoss','',scope.row.id)"
                v-permission="['card_service.card_operate.cancel_loss']"
                >取消挂失</el-button>
              <el-button
                :disabled="scope.row.person_status_alias === '退户'"
                v-show="scope.row.card_status == 'LOSS' "
                type="text"
                size="small"
                class="ps-green-text"
                @click="openCardOperation('repair', scope.row)"
                v-permission="['card_service.card_operate.change']"
                >补卡</el-button
              >
              <!-- 使用中才有 挂失不能点 -->
              <el-button
                type="text"
                size="small"
                class="ps-green-text"
                :disabled="!isCurrentOrgs(scope.row.organization) || scope.row.person_status_alias === '退户'"
                v-show="scope.row.card_status=='UNUSED'||scope.row.card_status=='QUIT' "
                @click="openCardOperation('publish', scope.row)"
                v-permission="['card_service.card_operate.publish']"
                >发卡</el-button
              >
              <el-button
                type="text"
                size="small"
                class="ps-red"
                :disabled="!isCurrentOrgs(scope.row.organization) || scope.row.person_status_alias === '退户'"
                v-show="scope.row.card_status=='ENABLE'"
                @click="isFlatReturn(scope.row)"
                v-permission="['card_service.card_operate.card_quit']"
                >退卡</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
        <div class="table-total">
          <span>已录入人脸：{{ totalData.has_face_counts }}</span>
          <span>未录入人脸：{{ totalData.non_face_counts }}</span>
          <span v-if="serviceType === 1 && $store.state.user.userInfo.level_tag === 0">用户规模：{{ totalData.user_scale }}</span>
          <span v-if="serviceType === 1 && $store.state.user.userInfo.level_tag === 0">用户使用率：{{ totalData.use_user_rate }}%</span>
        </div>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 添加/编辑用户 -->
    <user-drawer
      v-if="userDialogVisible"
      :loading="dialogLoading"
      :isshow.sync="userDialogVisible"
      :type="userDialogType"
      :title="userDialogTitle"
      :user-info="userInfo"
      :groupList="groupList"
      :departmentList="searchFormSetting.card_department_group_id.dataList"
      :sensitiveSetting="sensitiveSetting"
      @confirm="searchHandle"
      @showServiceDialog="showServiceDialog"
    />
    <!-- end -->
    <!-- 用户即将达上限弹窗 -->
    <dialog-message
      width="500px"
      title="提示"
      :show.sync="serviceDialogShow"
      customClass="expire-dialog"
      :showFooter="false"
      @close="serviceDialogClose">
      <div>
        <span>{{ msgShow(0) }}：<span style="text-decoration: underline; color: blue;">{{ msgShow(1) }}</span>）</span>
      </div>
      <template slot="tool">
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
          <el-button
            :disabled="isLoading"
            class="ps-cancel-btn"
            @click="goUpgrade"
          >
            扩容
          </el-button>
          <el-button
            :disabled="isLoading"
            class="ps-btn"
            type="primary"
            @click="serviceDialogClose"
          >
            暂不处理
          </el-button>
        </div>
      </template>
    </dialog-message>
    <!-- 操作弹窗 -->
    <card-operation
      :loading="dialogLoading"
      :isshow.sync="cardOperationVisible"
      :type="cardOperationType"
      :title="cardOperationTitle"
      :user-info="userInfo"
      @confirm="searchHandle"
    />
    <!-- end -->
    <!-- 导入数据的弹窗 start -->
    <import-dialog-drawer
      :templateUrl="templateUrl"
      :tableSetting="tableSetting"
      :show.sync="importShowDialog"
      :title="importDialogTitle"
      :openExcelType="openExcelType"
    ></import-dialog-drawer>
    <!-- 导入数据的弹窗 end -->
    <!-- 批量分组 start -->
    <el-dialog
      title="批量分组"
      :visible.sync="mulGroupDialog"
      width="400px"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false">
      <el-form class="dialog-form" inline label-width="110px" v-loading="isLoading">
        <el-form-item label="修改后的分组" prop="group" label-width="100px">
          <user-group-select
            class="search-item-w ps-input w-180"
            v-model="setGroup"
            :multiple="false"
            placeholder="请下拉选择"
          ></user-group-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="isLoading" class="ps-cancel-btn" @click="mulGroupDialog = false">取 消</el-button>
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="submitMulGroup">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 批量分组 end -->
    <!-- 用户同步 -->
    <el-dialog
      v-if="syncUserDialog"
      :title="syncUserTitleDialog"
      :visible.sync="syncUserDialog"
      width="400px"
      top="20vh"
      custom-class="ps-dialog"
      :before-close="beforeCloseDialogUserSync"
      :show-close="false"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      center
      >
      <div>
        <div class="text-size text-center" v-if="userSyncType === 'initial'">是否复制用户分组</div>
        <div class="text-size text-center" v-if="userSyncType === 'initial'">同步后钱包为禁用状态</div>
        <!-- 进度 -->
        <div v-if="userSyncType === 'progress'">
            <el-progress :percentage="syncProgress" color="#ff9b45" :stroke-width="8" :show-text="false"></el-progress>
            <div class="text-right m-t-10">
              <span style="color: #ff9b45;">{{ currentSyncNumber }}</span>
              <span>/{{ this.selectListId.length }}</span>
            </div>
        </div>
        <div class="text-size text-center" v-if="userSyncType === 'closeSync'">是否结束同步进程</div>
        <div class="text-center"  v-if="userSyncType === 'success'">
          <div class="text-size m-b-10"> <span class="sync-success-text">同步成功：</span> {{ currentSyncNumber }}</div>
          <div class="text-size"> <span class="sync-success-text">未同步：</span>{{ this.selectListId.length - currentSyncNumber }}</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button  v-if="userSyncType === 'initial'" :disabled="isSyncBtnLoading" size="small" class="ps-cancel-btn" @click="clickCloseSync('closeDialog')">取 消</el-button>
        <el-button  v-if="userSyncType === 'initial'" :disabled="isSyncBtnLoading" size="small" class="ps-origin-plain-btn"  plain @click="clickSync(true)">复制并同步</el-button>
        <el-button  v-if="userSyncType === 'initial'" :disabled="isSyncBtnLoading" size="small" class="ps-btn" type="primary" @click="clickSync(false)">仅同步用户</el-button>
        <el-button  v-if="userSyncType === 'progress'" :disabled="isSyncBtnLoading" size="small" class="ps-cancel-btn" @click="clickCloseSync('closeSync')">取 消</el-button>
        <el-button  v-if="userSyncType === 'closeSync'" :disabled="isSyncBtnLoading" size="small" class="ps-cancel-btn" @click="clickCloseSync('continue')">取 消</el-button>
        <el-button  v-if="userSyncType === 'closeSync'" :disabled="isSyncBtnLoading" size="small" class="ps-btn" type="primary" @click="clickDetermineSyncUser">确 定</el-button>
        <el-button  v-if="userSyncType === 'success'" :disabled="isSyncBtnLoading" size="small" class="ps-btn" type="primary"  @click="clickCloseSync('closeSyncDialog')">关 闭</el-button>

      </span>
    </el-dialog>
    <!-- 用户同步 end -->
    <!-- 上传人脸对话框组件 start -->
    <UploadFaceDialog :faceDetailInfo="faceDetailInfo" @uploadSuccess="uploadSuccess" ref="uploadFaceDialogRef"></UploadFaceDialog>
    <!-- 上传人脸对话框组件 end -->
    <!-- 协议弹窗 -->
    <dialog-message center width="600px" title="提示" :show.sync="agreementDialog" customClass="expire-dialog">
      <div style="margin-bottom: 20px;">是否导出当前列表？</div>
      <div class="flex flex-align-center">
        <el-radio v-model="isAgreement" label="agreement" style="margin-right: 0; display: flex;align-items: center;">
          已阅读并同意
        </el-radio>
        <el-link type="primary" @click="gotoAgreement" style="margin-left: 5px;">《{{agreementInfo.agreement_name}}》</el-link>
      </div>
      <template slot="tool">
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right">
          <el-button class="ps-cancel-btn" @click="agreementDialog = false">取消</el-button>
          <el-button :disabled="!isAgreement" :loading="exportLoading" class="ps-btn" type="primary" @click="confirmDialogAgreement">确定</el-button>
        </div>
      </template>
    </dialog-message>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, parseTime, formatWallet } from '@/utils'
import { isCurrentOrgs } from './utils'
// import userDialog from './components/userDialog.vue'
import userDrawer from './components/userDrawer.vue'
import CardOperation from './components/CardOperation.vue'
import UserGroupSelect from '@/components/UserGroupSelect'
// 新增代码 --- 导入上传人脸对话框组件
import UploadFaceDialog from './components/uploadFaceDialog.vue'
import { decrypted } from '@/utils/aesUtil'
import { mapGetters } from 'vuex'

export default {
  name: 'UserCenter',
  components: { userDrawer, CardOperation, UserGroupSelect, UploadFaceDialog },
  props: {},
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  data() {
    return {
      isModifyField: false, // 是否修改备用字段1
      isModifyField2: false, // 是否修改备用字段2
      spareField: '备用字段1', // 备用字段1
      spareField2: '备用字段2', // 备用字段2
      time: new Date().getTime(),
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        card_no: {
          type: 'input',
          label: '卡号',
          value: '',
          placeholder: '请输入卡号'
        },
        org_ids: {
          type: 'organizationSelect',
          multiple: true,
          checkStrictly: true,
          isLazy: false,
          label: '来源',
          value: [],
          placeholder: '请选择来源'
        },
        person_name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        phone: {
          type: 'input',
          label: '手机号',
          value: '',
          placeholder: '请输入手机号'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        },
        card_user_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: '',
          multiple: true,
          filterable: true,
          placeholder: '请选择分组'
        },
        card_department_group_id: {
          type: 'departmentSelect',
          multiple: false,
          checkStrictly: true,
          isLazy: false,
          label: '部门',
          value: '',
          placeholder: '请选择部门'
        },
        card_status: {
          type: 'select',
          label: '卡状态',
          value: [],
          placeholder: '请选择卡状态',
          multiple: true,
          collapseTags: true,
          dataList: [{
            label: '挂失',
            value: 'LOSS'
          }, {
            label: '退卡',
            value: 'QUIT'
          }, {
            label: '使用中',
            value: 'ENABLE'
          }, {
            label: '未发卡',
            value: 'UNUSED'
          }]
        },
        person_status: {
          type: 'select',
          label: '账户状态',
          value: '',
          placeholder: '请选择账户状态',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '使用中',
            value: 'ENABLE'
          },
          // {
          //   label: '退户',
          //   value: 'PERSON_QUIT'
          // },
          {
            label: '冻结中',
            value: 'FREEZE'
          }]
        },
        facepay: {
          type: 'select',
          label: '人脸支付状态',
          value: '',
          placeholder: '请选择人脸支付状态',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '启用',
            value: true
          }, {
            label: '禁用',
            value: false
          }]
        },
        is_self_org: {
          type: 'checkbox',
          label: ' ',
          checkboxLabel: '本组织创建',
          value: false
        },
        has_face: {
          type: 'select',
          label: '录入人脸',
          value: '',
          placeholder: '请选择',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '已录入',
            value: true
          }, {
            label: '未录入',
            value: false
          }]
        }
      },
      selectListId: [], // 选中的数据ID，注意这里这里的数据不需要响应，使用freeze了
      dialogLoading: false,
      userDialogVisible: false, // 用户弹窗
      userDialogType: '',
      userDialogTitle: '',
      userInfo: {}, // 编辑时的数据
      cardOperationVisible: false, // 操作卡弹窗（补卡、发卡、退卡）
      cardOperationType: '', // 操作卡弹窗类型
      cardOperationTitle: '', // 操作卡弹窗标题
      // 导入的弹窗数据
      importDialogTitle: '',
      importShowDialog: false,
      templateUrl: '',
      openExcelType: '',
      tableSetting: [],
      mulGroupDialog: false,
      setGroup: '',
      groupList: [],
      totalData: {
        has_face_counts: 0,
        non_face_counts: 0,
        user_scale: 0,
        use_user_rate: 0
      },
      sensitiveSetting: {}, // 隐藏后台配置的信息
      userCheckedAll: false, // 全选数据
      selectListIdCount: 0, // 全选总数量
      syncUserTitleDialog: "提示", // 用户同步弹框title
      syncUserDialog: false,
      userSyncType: 'initial', // 初始值 同步状态进行中 成功 是否结束
      timer: null, // 定时器
      syncProgress: 0, // 同步进度
      isSyncBtnLoading: false,
      userSyncQueryId: '', // 进度的id
      // 新增代码 --- 保存表格详情信息，用于传值给faceDialog
      faceDetailInfo: {},
      serviceDialogShow: false,
      serviceMsg: '',
      // 保存收费模式
      serviceType: 0,
      agreementDialog: false, // 协议弹窗
      isAgreement: false, // 是否同意
      exportLoading: false,
      agreementInfo: { // 协议对象
        id: '',
        agreement_name: ''
      }
    }
  },
  created() {
    this.initLoad()
    // console.log('当前层级', this.$store.state.user.userInfo)
  },
  mounted() {
    window.addEventListener('click', this.watchTableCustomFieldsClick)
  },
  computed: {
    ...mapGetters({
      userInfoFromStore: 'userInfo',
      organization: 'organization'
    }),
    // 只有一级组织才能修改备用字段
    isTopLevel() {
      let show = false
      console.log('用户信息', this.userInfoFromStore)
      if (this.userInfoFromStore.orgs_level && this.userInfoFromStore.orgs_level[this.organization] === 0) {
        show = true
      }
      return show
    },
    currentSyncNumber: function() {
      // Math.trunc 用于去掉它的参数的小数部分，而只返回整数部分
      return Math.trunc((this.syncProgress / 100) * this.selectListId.length);
    },
    msgShow() {
      return d => {
        let text = this.serviceMsg ? this.serviceMsg.split('：') : ''
        if (d === 0) {
          return text[0]
        } else {
          return text ? text[1].slice(0, -2) : ''
        }
      }
    }
  },
  methods: {
    initLoad() {
      this.getMerchantInfo()
      this.getCardUserList()
      this.getSensitiveSetting()
      this.getCardUserSpareFieldAlias()
      this.getAgreementMessage()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.userDialogVisible = false
        this.cardOperationVisible = false
        this.currentPage = 1
        this.selectListId = []
        this.userCheckedAll = false
        this.$refs.tableData.clearSelection()
        this.$nextTick(() => {
          this.getCardUserList()
        })
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      // 取消勾选列表
      this.selectListId = []
      this.userCheckedAll = false
      this.$refs.tableData.clearSelection();
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      console.log(data)
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getCardUserList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        console.log('用户列表', res);
        this.tableData = []
        res.data.results.map(item => {
          item.card_user_group_alias = item.card_user_group_alias.join('，')
          // 格式化下钱包
          item = formatWallet(item)
          // 退户的不显示
          if (item.account_status_alias !== '退户') {
            this.tableData.push(item)
          }
          try {
            item.organization_alias = item.organization_alias.join('，')
          } catch (error) {
            console.warn(error)
          }
        })
        this.totalCount = res.data.count
        this.totalData.has_face_counts = res.data.has_face_counts
        this.totalData.non_face_counts = res.data.non_face_counts
        this.totalData.user_scale = res.data.user_scale
        this.totalData.use_user_rate = res.data.use_user_rate
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取分组信息
    async userGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        page: 1,
        page_size: ***********
      })
      this.isLoading = false
      if (res.code === 0) {
        this.groupList = res.data.results
        this.searchFormSetting.card_user_group_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置卡状态文字颜色
    setCloumnTextColor(row) {
      let style = [];
      switch (row.card_status) {
        case "LOSS": // 挂失
          style = ["loss"];
          break;
        case "FREEZE": // 冻结中
          style = ["blue"];
          break;
        case "QUIT": // 退卡
          style = ["warn"];
          break;
        case "ENABLE": // 使用中
          style = ["success"];
          break;
        case "UNUSED": // 未发卡
          style = ["warn"];
          break;
        default:
          style = ["success"];
          break;
      }
      return style;
    },
    // 如果新增生效时间 list表生效时间默认获取创建时间 create_time
    formatTime(row, type) {
      if (type === 'effective') {
        return row.effective_time ? parseTime(new Date(row.effective_time), '{y}-{m}-{d} {h}:{i}:{s}') : parseTime(new Date(row.create_time), '{y}-{m}-{d} {h}:{i}:{s}')
      } else {
        return row.expiration_time ? parseTime(new Date(row.expiration_time), '{y}-{m}-{d} {h}:{i}:{s}') : '-'
      }
    },
    // 分页页数change事件
    async handleSizeChange(val) {
      this.pageSize = val;
      await this.getCardUserList()
      this.changeTableSelection()
    },
    // 分页页码change事件
    async handleCurrentChange(val) {
      this.currentPage = val;
      await this.getCardUserList()
      this.changeTableSelection()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    selectSelection(row) {
      this.changeSelectSelection(row)
    },
    selectSelectionAll(row) {
      this.changeSelectSelection(row)
    },
    changeSelectSelection (row) {
      let rowIdS = row.map(v => { return v.id })
      this.tableData.forEach((item, indx) => {
        if (!rowIdS.includes(item.id)) {
          var selectListIdIndex = this.selectListId.indexOf(item.id)
          if (selectListIdIndex !== -1) {
            this.selectListId.splice(selectListIdIndex, 1)
          }
        } else {
          this.selectListId.push(...rowIdS)
          this.selectListId = [...new Set(this.selectListId)]
        }
      })
      if (this.selectListId.length) {
        this.userCheckedAll = this.selectListIdCount === this.selectListId.length ? true : 0
      }
    },
    // 人脸开关/批量
    async switchFacePay(type, data, isOpen) {
      let facepay
      if (type === 'oneFacesPay') {
        facepay = isOpen
      } else if (type === 'openFacesPay') {
        facepay = true
      } else if (type === 'closeFacesPay') {
        facepay = false
      }
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserFaceSwitchPost({
        ids: type === 'oneFacesPay' ? [data.id] : this.selectListId,
        facepay
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('设置成功')
        this.getCardUserList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 批量删除人脸
    async mulDelFaces() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserFaceBatchDeletePost({
        ids: this.selectListId
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('删除人脸成功')
        this.getCardUserList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 批量上传人脸
    gotoImportFace() {
      this.$router.push({
        name: 'MerchantMulImportFace'
      })
    },
    // 获取获取登录组织设置的隐藏信息相关的
    async getSensitiveSetting() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetSensitiveSettingPost()
      this.isLoading = false
      if (res.code === 0) {
        this.sensitiveSetting = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 批量、单个冻结
    async cardFreeze(type, data) {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserBatchFreezeCardUserPost({
        card_user_ids: type === 'one' ? [data.id] : this.selectListId
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('冻结成功')
        this.getCardUserList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取到所有列表的id全选
    async getCardUserAllList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        only_id: true
      })
      this.isLoading = false
      if (res.code === 0) {
        this.selectListId = res.data.results
        this.selectListIdCount = res.data.count
        this.changeTableSelection()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 批量操作 type表示类型，text是弹窗文字，num表示批量或者单个操作，data是单个操作的时候带过去的数据
    mulOperation(type, num, data) {
      if (!data && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let title = '提示'
      let content = ''
      switch (type) {
        case "openFacesPay":
          content = '确定开启人脸支付？'
          break;
        case "closeFacesPay":
          content = '确定关闭人脸支付？'
          break;
        case "delFaces":
          content = '确定批量删除人脸？'
          break;
        case "freeze":
          content = '确认用户是否还有待支付/离线订单,冻结后，该用户无法进行消费/充值/取款操作，直到解除冻结状态！'
          break;
        case "loss":
          content = '确认把选中的卡进行挂失吗？挂失后，该卡将无法在进行刷卡消费！直到解除挂失状态！'
          break;
        case "cancelLoss":
          content = '确认把选中的卡取消挂失吗？'
          break;
        case "quit":
          content = '注意：该操作需谨慎！退卡后，该卡片将不再与账户关联，该卡将为无主卡。'
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            switch (type) {
              case "freeze": // 冻结
                if (num === 'mul') { this.cardFreeze('mul') } else { this.cardFreeze('one', data) }
                break;
              case "delFaces": // 删除人脸
                this.mulDelFaces()
                break;
              case "loss": // 挂失
                this.setCardLoss(data)
                break;
              case "cancelLoss": // 取消挂失
                this.cancelCardLoss(data)
                break;
              case "quit": // 退卡
                this.quitCard(data)
                break;
              case "openFacesPay": // 开启人脸
                this.switchFacePay(type)
                break;
              case "closeFacesPay": // 关闭人脸
                this.switchFacePay(type)
                break;
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 打开批量分组弹窗
    openGroupDialog() {
      if (!this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.setGroup = ''
      this.mulGroupDialogTitle = '批量分组'
      this.mulGroupDialog = true
    },
    // 批量设置分组
    async submitMulGroup() {
      if (!this.setGroup) {
        return this.$message.error('请先选择分组！')
      }
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserBatchSetCardUserGroupPost({
        card_user_ids: this.selectListId,
        card_user_group_id: this.setGroup
      })
      this.isLoading = false
      if (res.code === 0) {
        this.mulGroupDialog = false
        this.$message.success('编辑分组成功')
        this.getCardUserList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 退卡
    isFlatReturn(data) {
      if (data.is_flat_return) {
        this.openCardOperation('quit', data)
      } else {
        this.mulOperation('quit', '', data.id)
      }
    },
    // 新增or编辑用户
    openUserDialog(type, item) {
      this.userInfo = {}
      this.userDialogType = type
      this.userDialogTitle = '新增用户'
      this.userDialogVisible = true
      if (type === 'edit') {
        this.userInfo = item
        this.userDialogTitle = '编辑用户'
      }
    },
    // 补卡、发卡
    openCardOperation(type, item) {
      this.cardOperationType = type
      this.cardOperationVisible = true
      this.userInfo = item
      if (type === 'publish') {
        this.cardOperationTitle = '发卡'
      } else if (type === 'repair') {
        this.cardOperationTitle = '补卡'
      } else if (type === 'quit') {
        this.cardOperationTitle = '退卡'
      }
    },
    // 挂失卡
    async setCardLoss(id) {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardOperateLossPost({
        card_user_id: id
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('挂失成功')
        this.getCardUserList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 取消挂失
    async cancelCardLoss(id) {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardOperateCancelLossPost({
        card_user_id: id
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('取消挂失成功')
        this.getCardUserList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 用户同步
    async getUserSyncToOrg(status) {
      this.isSyncBtnLoading = true
      const res = await this.$apis.apiCardServiceCardUserUserSyncToOrgPost({
        ids: this.selectListId,
        is_create_group: status
      })
      this.isSyncBtnLoading = false
      if (res.code === 0) {
        this.userSyncQueryId = res.data.query_id
        this.syncUserTitleDialog = '用户同步中，请耐心等待...'
        this.userSyncType = 'progress'
        this.getUserSyncProgress()
        this.timer = setInterval(() => {
          this.getUserSyncProgress()
        }, 3000)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 查询进度
    async getUserSyncProgress() {
      try {
        const res = await this.$apis.apiBackgroundBaseTasksExportQueryPost({
          query_id: this.userSyncQueryId
        })
        if (res.code === 0) {
          if (res.data.status === 'success') {
            this.syncProgress = res.data.progress
            clearInterval(this.timer)
            await this.$sleep(1000)
            this.userSyncType = 'success'
            this.syncUserTitleDialog = '提示'
          } else if (res.data.status === 'processing') {
            this.syncProgress = res.data.progress
          } else if (res.data.status === 'failure') {
            clearInterval(this.timer)
            this.$message.error('同步结果失败！')
          }
        } else {
          this.$message.error(res.msg)
          clearInterval(this.timer)
        }
      } catch (error) {
        this.$message.error('出错啦')
        clearInterval(this.timer)
      }
    },
    // 终止查询进度
    async getStopTasksProgress() {
      try {
        const res = await this.$apis.apiCardServiceCardUserStopUserSyncToOrgPost({
          query_id: this.userSyncQueryId
        })
        if (res.code === 0) {
          clearInterval(this.timer)
          this.userSyncType = 'success'
          this.syncUserTitleDialog = '提示'
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error('出错啦')
      }
    },
    async quitCard(id) {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardOperateCardQuitPost({
        card_user_id: id
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('退卡成功')
        this.getCardUserList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳转链接
    gotoAgreement() {
      let url = window.location.origin + '/#/agreement?type=' + 'USER' + '&key=AGREEMENTLIST'
      window.open(url, '_blank')
    },
    // 协议弹窗
    async confirmDialogAgreement() {
      try {
        const option = {
          type: 'ExportUser',
          immediate: true,
          params: {
            ...this.formatQueryParams(this.searchFormSetting),
            agreement_id: this.agreementInfo.id,
            page: 1,
            page_size: 999999999
          }
        }
        this.exportLoading = true
        await this.exportHandle(option)
        this.exportLoading = false
      } catch (error) {
        this.exportLoading = false
      }
    },
    // 协议弹窗
    agreementGotoExport() {
      this.agreementDialog = true
    },
    // 获取协议详情
    async getAgreementMessage() {
      const params = {
        agreement_type: 'USER'
      }
      const [err, res] = await this.$to(this.$apis.getAgreementMessage(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        if (res.data) {
          this.agreementInfo = res.data
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出
    gotoExport(type) {
      const option = {
        type: type,
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: 1,
          page_size: 999999999
        }
      }
      this.exportHandle(option)
    },
    openImport(type) {
      switch (type) {
        case "ImportUser":
          this.importDialogTitle = '导入用户'
          if (this.sensitiveSetting.id_number) {
            this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入用户.xlsx'
          } else {
            this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入用户v2.xlsx'
          }
          this.openExcelType = type
          this.tableSetting = [
            { key: 'name', label: '姓名' },
            { key: 'person_no', label: '人员编号' },
            { key: 'card_no', label: '卡号' },
            { key: 'phone', label: '手机号' },
            // { key: 'is_number', label: '身份证号' },
            { key: 'age', label: '年龄' },
            { key: 'is_auto_group', label: '是否自动分组' },
            { key: 'card_user_group_ids', label: '用户分组' },
            { key: 'card_department_group_id', label: '部门分组' },
            { key: 'gender', label: '性别' },
            { key: 'crowd', label: '人群' },
            { key: 'effective_time', label: '生效时间' },
            { key: 'expiration_time', label: '失效时间' },
            { key: 'spare_field', label: '备用字段1' },
            { key: 'spare_field2', label: '备用字段2' }
          ]
          if (this.sensitiveSetting.id_number) {
            this.tableSetting.splice(4, 0, { key: 'is_number', label: '身份证号' })
          }
          break;
        case "ImportEditUser":
          this.importDialogTitle = '导入编辑'
          if (this.sensitiveSetting.id_number) {
            this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入编辑.xls'
          } else {
            this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入编辑v2.xlsx'
          }
          this.openExcelType = type
          this.tableSetting = [
            { key: 'name', label: '姓名' },
            { key: 'person_no', label: '人员编号' },
            { key: 'card_no', label: '卡号' },
            { key: 'phone', label: '手机号' },
            // { key: 'is_number', label: '身份证号' },
            { key: 'age', label: '年龄' },
            { key: 'is_auto_group', label: '是否自动分组' },
            { key: 'card_user_group_ids', label: '用户分组' },
            { key: 'card_department_group_id', label: '部门分组' },
            { key: 'gender', label: '性别' },
            { key: 'crowd', label: '人群' },
            { key: 'effective_time', label: '生效时间' },
            { key: 'expiration_time', label: '失效时间' },
            { key: 'spare_field', label: '备用字段1' },
            { key: 'spare_field2', label: '备用字段2' }
          ]
          if (this.sensitiveSetting.id_number) {
            this.tableSetting.splice(4, 0, { key: 'is_number', label: '身份证号' })
          }
          break;
        case "ImportGroup":
          this.importDialogTitle = '导入分组'
          this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入设置用户分组.xls'
          this.openExcelType = type
          this.tableSetting = [
            { key: 'name', label: '姓名' },
            { key: 'person_no', label: '人员编号' },
            { key: 'card_user_group_ids', label: '用户分组' }
          ]
          break;
        case "ImportReturnCard":
          this.importDialogTitle = '导入退卡'
          this.templateUrl = location.origin + '/api/temporary/template_excel/卡务模板/导入退卡.xls'
          this.openExcelType = type
          this.tableSetting = [
            { key: 'name', label: '姓名' },
            { key: 'person_no', label: '人员编号' }
          ]
          break;
      }
      this.importShowDialog = true
    },
    changeTableSelection() {
      if (this.tableData.length) {
        this.tableData.forEach((item, index) => {
          // 匹配勾选上
          if (this.selectListId.includes(item.id)) {
            this.$nextTick(() => {
              this.$refs.tableData.toggleRowSelection(item);
            })
          }
        })
      }
    },
    changeCheckedAll() {
      this.selectListId = []
      this.$refs.tableData.clearSelection();
      if (this.userCheckedAll) {
        this.getCardUserAllList()
      }
    },
    clickSyncUser() {
      if (!this.selectListId.length) {
        return this.$message.error('未选择用户')
      }
      this.syncProgress = 0 // 打开设为0
      this.syncUserDialog = true
    },
    clickSync(status) {
      this.getUserSyncToOrg(status)
    },
    beforeCloseDialogUserSync() {
      this.syncUserDialog = false
      this.userSyncType = 'initial'
      this.syncUserTitleDialog = '提示'
    },
    clickCloseSync(type) {
      switch (type) {
        case 'closeDialog':
          this.syncUserDialog = false
          this.userSyncType = 'initial'
          break;
        case 'closeSyncDialog':
          this.syncUserDialog = false
          this.userSyncType = 'initial'
          this.getCardUserList()
          break;
        case 'closeSync':
          this.userSyncType = 'closeSync'
          this.syncUserTitleDialog = '提示'
          break;
        case 'continue':
          this.userSyncType = 'progress'
          this.syncUserTitleDialog = '用户同步中，请耐心等待...'
          break;
        default:
          break;
      }
    },
    clickDetermineSyncUser() {
      this.getStopTasksProgress()
    },
    departmentNode(node) {
      return {
        id: node.id,
        label: node.group_name,
        children: node.children_list
      }
    },
    // 是否是当前组织
    isCurrentOrgs,
    // 新增代码 --- 打开上传人脸对话框
    openFaceDialog(val) {
      this.faceDetailInfo = val
      this.$nextTick(() => {
        this.$refs.uploadFaceDialogRef.openDialog()
      })
    },
    // 新增代码 --- 监听对话框上传成功的emit事件
    uploadSuccess() {
      this.getCardUserList()
    },
    showServiceDialog(msg) {
      this.getCardUserList()
      this.serviceDialogShow = true
      this.serviceMsg = msg
    },
    serviceDialogClose() {
      this.serviceDialogShow = false
      this.serviceMsg = false
    },
    goUpgrade() {
      this.serviceDialogClose()
      this.$router.push({
        path: '/upgrade/service'
      })
    },
    // 获取当前组织的收费模式
    getMerchantInfo() {
      this.isLoading = true
      this.$apis.apiBackgroundTollBackgroundTollGetSettingsPost()
        .then(res => {
          if (res.code === 0) {
            this.serviceType = res.data.toll_type
            console.log('this.serviceType', this.serviceType, typeof this.serviceTypes)
          } else {
            this.$message.error(res.msg)
          }
        })
    },
    isDisplayString (value) {
      return typeof value === 'string';
    },
    // 获取身份证号
    getIdCardNo(row) {
      let idCard = row.id_number || ''
      if (idCard) {
        idCard = decrypted(idCard)
      }
      return this.sensitiveSetting.id_number ? idCard : '****'
    },
    // 备用字段方法如下：
    async getCardUserSpareFieldAlias() {
      const { data, msg, code } = await this.$apis.apiCardServiceCardOperateGetSpareFieldAliasPost()
      if (code === 0) {
        let result = data || {}
        this.spareField = result.spare_field
        this.spareField2 = result.spare_field2
      } else {
        this.$message.error(msg)
      }
    },

    async ModifyCardUserSpareFieldAlias() {
      const { msg, code } = await this.$apis.apiCardServiceCardOperateModifySpareFieldAliasPost({
        spare_field: this.spareField,
        spare_field2: this.spareField2
      })
      if (code === 0) {
        this.$message.success('修改成功')
      } else {
        this.$message.error(msg)
      }
    },
    // 保存自定义字段
    async saveSetting(e, type) {
      let val = e.target.value
      if (val) {
        await this.ModifyCardUserSpareFieldAlias()
      }
      await this.getCardUserSpareFieldAlias()
      if (type === 1) this.isModifyField = false
      else this.isModifyField2 = false
    },
    watchTableCustomFieldsClick () {
      document.addEventListener('click', (e) => {
        let thisClickElementId = e.target.id
        if (thisClickElementId !== 'el-table-column-header-input') {
          this.isModifyField = false
          this.isModifyField2 = false
        }
      })
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    document.removeEventListener('click', this.watchTableCustomFieldsClick)
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.user-center{
  .face-img {
    max-width: 40px;
    max-height: 50px;
  }
  .sync-success-text {
    text-align: right;
    display: inline-block;
    width: 90px;
  }
  .text-size {
    font-size: 18px;
    color: #303133;
    font-weight:bold;
  }
  .el-table-column-header-label{
    cursor: pointer;
    i:hover{
      color: #FF6600;
    }
  }
}
</style>
