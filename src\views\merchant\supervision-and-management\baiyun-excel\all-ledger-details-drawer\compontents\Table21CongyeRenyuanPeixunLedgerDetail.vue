<template>
  <!-- 表13-食品留样记录表-详情 -->
  <div class="content-21">
    <div class="item-info">
      <div class="item-info-wrap flex">
        <div class="color-999 item-title">主讲人：</div>
        <div>{{ detailInfo.speaker }}</div>
      </div>
      <div class="item-info-wrap flex">
        <div class="color-999 item-title">培训日期：</div>
        <div class="m-t-5">{{ detailInfo.operate_date }}</div>
      </div>
    </div>
    <div class="item-info">
      <div class="item-info-wrap flex">
        <div class="color-999 item-title">培训时长：</div>
        <div>{{ detailInfo.duration }}分钟</div>
      </div>
      <div class="item-info-wrap flex">
        <div class="color-999 item-title">评估结果：</div>
        <div>{{ detailInfo.evaluation_result_verbose }}</div>
      </div>
    </div>
    <div class="item-info">
      <div class="item-info-wrap flex">
        <div class="color-999 item-title">记录人：</div>
        <div>{{ detailInfo.username }}</div>
      </div>
      <div class="item-info-wrap flex">
        <div class="color-999 item-title">复核人：</div>
        <div>{{ detailInfo.confirmer }}</div>
      </div>
    </div>
    <div class="flex flex-align-c m-b-15">
      <div class="color-999 item-title">培训主题：</div>
      <div class="food-item">{{ detailInfo.training_theme_alias }}</div>
    </div>
    <div class="flex flex-align-c m-b-15">
      <div class="color-999 item-title">培训内容：</div>
      <div class="food-item">{{ detailInfo.training_content }}</div>
    </div>
    <div class="flex flex-align-c m-b-15">
      <div class="color-999 item-title">培训方式：</div>
      <div class="food-item">{{ detailInfo.training_method_alias }}</div>
    </div>
    <div class="flex flex-align-c m-b-15">
      <div class="color-999 item-title">应到人员：</div>
      <div class="food-item">{{ detailInfo.expected_person }}</div>
    </div>
    <div class="flex flex-align-c m-b-15">
      <div class="color-999 item-title">实到人员：</div>
      <div class="food-item">{{ detailInfo.actual_person }}</div>
    </div>
    <div class="flex flex-align-c m-b-15">
      <div class="color-999 item-title">考核方式：</div>
      <div class="food-item">{{ detailInfo.assessment_method_alias }}</div>
    </div>
    <div class="detault-img-wrap flex m-b-15">
      <div class="color-999 item-title">培训照片：</div>
      <div class="img-wrap m-t-5">
        <el-image
          v-for="(url, urlIndex) in detailInfo.record_img"
          :key="urlIndex"
          class="detault-img m-r-10 pointer"
          :src="url"
          fit="cover"
          @click="clickViewerHandler(detailInfo.record_img, urlIndex)"
        ></el-image>
      </div>
    </div>
    <!-- 预览图片 -->
    <image-viewer
      v-model="showViewer"
      :initial-index="imgIndex"
      :z-index="3000"
      :on-close="showViewer = false"
      :preview-src-list="previewSrcList"
    />
  </div>
</template>
<script>
import { to } from '@/utils'
export default {
  name: 'Table11ShipinLiuyangDetail',
  data() {
    return {
      isLoading: false,
      detailInfo: null,
      // 预览图片
      imgIndex: 0,
      previewSrcList: [],
      showViewer: false
    }
  },
  props: {
    ledgerId: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    // 监听 ledgerId 变化
    ledgerId: {
      handler(newVal) {
        if (newVal) {
          this.getLedgerReviewDetail(newVal)
        }
      },
      immediate: true // 立即执行
    }
  },
  methods: {
    // 获取详情通用接口
    async getLedgerReviewDetail(id) {
      this.isLoading = true
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyLedgerReviewDetail({ id }))
      if (err) {
        this.$message.error(err.message)
        this.isLoading = false
        return
      }
      if (res.code === 0) {
        this.detailInfo = res.data || null
      } else {
        this.$message.error(res.msg)
      }
      this.isLoading = false
    },
    // 时间处理
    formatTime(timeStr) {
      // 分割日期和时间部分
      const [datePart, timePart] = timeStr.split(' ')
      // 分割日期的年、月、日
      const [year, month, day] = datePart.split('-')
      // 分割时间的时、分
      const [hour, minute] = timePart.split(':')

      // 格式化日期和时间
      const dateStr = `${year}年${month}月${day}日`
      const timeStrFormatted = `${hour}时${minute}分`

      return [dateStr, timeStrFormatted]
    },
    // 预览图片
    clickViewerHandler(row, index) {
      // don't show viewer when preview is false
      this.previewSrcList = row || []
      this.imgIndex = index
      this.showViewer = true
    }
  }
}
</script>
<style lang="scss" scoped>
.content-21 {
  padding: 20px 0 0 3%;
  .color-999 {
    color: #999;
  }
  .item-info {
    display: flex;
    justify-content: space-between;
    margin-right: 30%;
    margin-bottom: 10px;
    width: 100%;
    .item-info-wrap {
      width: 50%;
    }
  }
  .item-title{
    width: 90px;
  }
  .food-item {
    width: 80%;
    padding: 10px;
    background-color: #f2f2f2;
    border-radius: 8px;
  }
  .detault-img-wrap {
    // flex-wrap: wrap;
  }
  .detault-img {
    width: 160px;
    height: 92px;
    border: 1px dashed #e1e1e1;
    border-radius: 2px;
  }
}
</style>
