<template>
  <div>
    <div class="container-wrapper">
      <feeback :form-setting="searchFormSetting" type="Merchant" :api-url="apiUrl" :autoSearch="false"/>
    </div>
  </div>
</template>

<script>
import { FEEBACK_LIST } from './constants'
import { deepClone } from '@/utils'
import feeback from "@/components/feeback"

export default {
  name: 'EvaluateList',
  components: { feeback },
  data() {
    return {
      searchFormSetting: deepClone(FEEBACK_LIST),
      apiUrl: 'apiBackgroundFeedbackFeedbackRecordListPost'
    }
  },
  mounted() {
    // this.initLoad()
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
</style>
