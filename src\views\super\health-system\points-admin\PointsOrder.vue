<template>
  <div class="PointsOrder container-wrapper">
    <div class="top-btn">
      <el-radio-group v-model="tabType" class="ps-radio-btn" @change="changeTab">
        <el-radio-button v-for="item in tabTypeList" :key="item.value" :label="item.value">
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
      <!-- <img @click="refreshHandle" class="refresh" src="@/assets/img/refresh.png" alt="" /> -->
    </div>
    <payment-order ref="paymentOrder" v-if="tabType === 'payment'"></payment-order>
    <refund-order ref="refundOrder" v-if="tabType === 'refund'"></refund-order>
  </div>
</template>

<script>
// import { debounce } from '@/utils'
import PaymentOrder from './components/paymentOrder.vue'
import RefundOrder from './components/refundOrder.vue'
export default {
  name: 'PointsOrder',
  components: { PaymentOrder, RefundOrder },
  data() {
    return {
      tabType: 'payment',
      tabTypeList: [
        {
          value: 'payment',
          label: '支付订单'
        },
        {
          value: 'refund',
          label: '退款订单'
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    changeTab() {},
    refreshHandle() {
      // this.$refs.deliverChild.resetSearchHandle()
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.PointsOrder {
  .top-btn {
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
  }
  .refresh {
    cursor: pointer;
    padding: 14px 0;
  }
}
</style>
