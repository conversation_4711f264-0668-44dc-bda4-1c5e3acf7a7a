import axios from 'axios'
import store from '@/store'
import router from '@/router'

// import qs from 'qs'
import { getToken, getSessionStorage, setSessionStorage } from '@/utils'

let baseURL = getSessionStorage('baseUrl') || process.env.VUE_APP_BASE_URL
// 动态拿下
// if (!baseURL && location.href.indexOf('/#') !== -1) {
//   baseURL = location.href.split('/#')[0]
// }

const cancelDuplicated = true // 是否开启重复接口限制
// const cancel_white_list = [] // 白名单
const cancelList = ['/api/background/login'] // 需拦截的重复请求接口名单
// 重复请求限制，目前统一所有接口统一时间段内当前一个没收到请求返回都不会继续触发之后的请求
const ajaxPending = new Map() // 存储每个ajax请求的队列
const cancelToken = axios.CancelToken // 初始化取消请求的构造函数

/**
 * @description 添加请求队列
 * @param {Object} config
 * @param {function} cancel
 */
const addPending = (config) => {
  if (cancelDuplicated && cancelList.includes(replaceBaseurl(config.url, '/'))) {
    const cancelKey = getCancelKey(config)
    // eslint-disable-next-line new-cap
    config.cancelToken = config.cancelToken || new cancelToken(cancel => {
      if (!ajaxPending.has(cancelKey)) {
        ajaxPending.set(cancelKey, cancel)
      } else {
        if (cancel) {
          cancel({ code: 3001, message: 'Request canceled' });
          // Notify({ type: 'warning', message: 'Request canceled' });
        }
      }
    })
  }
}

/**
 * @description 立即停止接口调用
 * @param {*} config
 */
// const cancelXhrNow = (config) => {
//   // eslint-disable-next-line new-cap
//   config.cancelToken = config.cancelToken || new cancelToken(cancel => {
//     if (cancel) {
//       cancel({ code: 3001, message: 'Request canceled' });
//     }
//   })
// }

/**
 *  @description 移除请求队列
 * @param {Object} config
 * @param {function} cancel
 */
const removePending = (config, cancel) => {
  if (cancelDuplicated && cancelList.includes(replaceBaseurl(config.url, '/'))) {
    const cancelKey = getCancelKey(config)
    // 当前请求存在队列中，取消第二次请求
    if (ajaxPending.has(cancelKey)) {
      // const cancel = ajaxPending.get(cancelKey)
      // cancel&&cancel('Request canceled');
      ajaxPending.delete(cancelKey);
    }
  }
}

/**
 * @description 生成队列唯一key
 * @param {Object} config
 * @returns
 */
const getCancelKey = (config) => {
  // 移除baseURL，应为发现有些接口没有baseURL，有些又有
  let url = replaceBaseurl(config.url, '/')
  return `${config.method}:${url}`
}

/**
 *
 * @param {String} url
 * @param {string} re
 * @returns
 */
const replaceBaseurl = (url, re = '') => {
  return url ? url.replace(baseURL, re) : url
}

// create an axios instance
const service = axios.create({
  baseURL: baseURL, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 120000 // request timeout
})
// console.log(process.env)

// request interceptor
service.interceptors.request.use(
  config => {
    if (store.getters.baseUrl) {
      config.baseURL = store.getters.baseUrl
    }
    // do something before request is sent
    // if (store.getters.token) {
    //   // please modify it according to the actual situation
    //   config.headers.Authorization = 'JWT' + store.getters.token
    // }
    // if (getToken()) {
    addPending(config);
    // } else {
    //   if (config.url.indexOf('login') < 0) {
    //     cancelXhrNow(config)
    //   }
    // }
    // 设置下请求的时间
    setSessionStorage('REQUESTTIME', +new Date())
    // 判断是不是file，
    let checkedMethods = ['post', 'put', 'patch', 'get']
    if (checkedMethods.includes(config.method)) {
      config.data = config.data || {}
      let hasFiles = false
      for (let i in config.data) {
        if (config.data[i] instanceof Array) {
          for (let j in config.data[i]) {
            if (config.data[i][j] instanceof File || config.data[i][j] instanceof Blob) {
              hasFiles = true
              break
            }
          }
        }
        if (config.data[i] instanceof File || config.data[i] instanceof Blob) {
          hasFiles = true
          break
        }
      }

      if (hasFiles) {
        const formdata = new FormData()
        for (let i in config.data) {
          if (config.data[i] instanceof Array) {
            for (let j in config.data[i]) {
              formdata.append(i, config.data[i][j])
            }
          } else {
            formdata.append(i, config.data[i])
          }
        }
        config.data = formdata
      } else {
        // 不能序列化，后端不做兼容。。。
        // config.data = qs.stringify(config.data)
      }
    }
    // 为了兼容首页修改密码
    let token = getToken() || getSessionStorage('PWDV4TOKEN')
    config.headers.TOKEN = token

    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  response => {
    removePending(response.config)
    // do something with response success
    // 与后端约定一套返回格式，自定义code状态码
    const res = response.data
    switch (res.code) {
      case -1:
        // 退出登录，跳转在permission.js里面做，不能在此进行路由跳转
        store.dispatch('user/logout')
        router.push({
          path: '/login'
        })
        break
      case -3: // 异地登录
        store.dispatch('user/logout')
        router.push({
          path: '/login'
        })
        break
      // case 3: //
      //   break
      // case 4: //
      //   break
      // // ...忽略
      // default:
    }
    return res
  },
  error => {
    const config = error.config || {};
    removePending(config)
    console.log('err' + error) // for debug
    // error
    // do some with response error
    // if (error.response !== undefined) {
    //   switch (error.response.status) {
    //     case 400:
    //       break
    //     case 401: // 会话已失效! 请重新登录
    //       break
    //     case 402: // 登陆超时 ！
    //       break
    //     case 403: // 没有权限！
    //       break
    //       // ...忽略
    //     default:
    //   }
    // }
    return Promise.reject(error)
  }
)

// 验证params
function sanitizeParams(value) {
  const pattern = /[<>]|[\t ]|script|&#/gi
  return value.replace(pattern, '')
}
// 循环params并对每个key验证
function sanitizeObject(obj) {
  const sanitizedObj = {}
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitizedObj[key] = sanitizeObject(obj[key])
      } else if (typeof obj[key] === 'string') {
        sanitizedObj[key] = sanitizeParams(obj[key])
      } else {
        sanitizedObj[key] = obj[key]
      }
    }
  }
  return sanitizedObj
}

export default {
  request: service,
  // 以下封装了一些常用的请求
  // get请求加上时间戳参数，避免从缓存中拿数据
  get_t(url, param) {
    if (param !== undefined) {
      Object.assign(param, { _t: (new Date()).getTime() })
    } else {
      param = { _t: (new Date()).getTime() }
    }
    return service({ method: 'get', url, params: param })
  },
  getData(url, param) {
    return service({ method: 'get', url, params: param })
  },
  post(url, param, config) {
    console.log('param', param, typeof param, url)
    if (url === '/api/background/verification_code') {
      // 对param内的每个key进行验证
      const realParam = sanitizeObject(param)
      console.log('验证后的params', realParam)
      return service.post(url, realParam, config)
    } else {
      return service.post(url, param, config)
    }
  },
  get(url, config) {
    return service.get(url, config)
  },
  put: service.put,
  _delete: service.delete
}
