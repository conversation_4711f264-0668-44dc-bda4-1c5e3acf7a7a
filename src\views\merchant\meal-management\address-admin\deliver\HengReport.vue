<template>
  <div class="HengReport container-wrapper">
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      @reset="resetHandle"
      :autoSearch="false"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <el-checkbox v-model="isSelectAll" @change="selectAllChange" :disabled="tableData.length <=0">全部选择</el-checkbox>
          <button-icon color="plain" type="export" @click="handleExportBefor" v-permission="['background_order.order_reservation.get_delivery_collect_by_x_export']">导出报表</button-icon>
          <button-icon color="plain" @click="openDialog">小票打印</button-icon>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          show-summary
          :summary-method="getSummaries"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
          @select="selectSelection"
          @select-all="selectSelectionAll"
          >
          <el-table-column type="selection" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column v-for="item in currentTableSetting" :key="item.key" :label="item.label" :prop="item.key" align="center">
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <print-ticket
      :isshow.sync="printVisible"
      type="order"
      title="小票打印"
      :select-list-id="selectOrderId"
      @confirm="searchHandle"
    ></print-ticket>
    <!-- 报表设置 -->
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="fixedTableSetting"
      :defaultCheckedSetting="defaultfixedTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
    <!-- 导出提示弹窗 -->
    <dialog-message
      width="470px"
      title="导出报表"
      :show.sync="printDialogShow"
      customClass="expire-dialog"
      cancelText="否"
      confirmText="是"
      :loading="isExportLoading"
      @cancel="onPrintDialogCancel"
      @confirm="onPrintDialogConfirm"
    >
      <template class="expire-dialog-content">
        <span>是否同步导出消费点配餐表？</span>
      </template>
    </dialog-message>
  </div>
</template>

<script>
import { debounce, deepClone, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { MEAL_TYPES } from '@/utils/constants'
import PrintTicket from '@/components/PrintTicket'
import searchInfoMixin from '@/mixins/searchMixin'
import { SEARCH_FORM_SETTING_HENG_REPORT } from './constants.js'
import report from '@/mixins/report' // 混入
export default {
  name: 'HengReport',
  components: { PrintTicket },
  mixins: [exportExcel, searchInfoMixin, report],
  data() {
    return {
      printDialogShow: false, // 报表导出弹窗
      // 报表设置
      dialogPrintVisible: false,
      printType: 'HengReport',
      currentTableSetting: [''],
      fixedTableSetting: [], // 固定表头
      defaultfixedTableSetting: [],
      noFixedTableSetting: [], // 不固定表头
      FIXED_HEADER_COUNT: 11, // 固定列数常量
      isTableSettingInitialized: false, // 标记是否已初始化表格设置
      tableData: [],
      totalData: {},
      tableSetting: [''],
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      isLoading: false, // 刷新数据
      searchFormSetting: this.initSearchFormSetting(),
      printVisible: false,
      selectOrderId: [],
      otherInfo: [],
      isSelectAll: false, // 是否全部选择
      selectListIdCount: 0,
      organizationList: [] // 组织列表
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    // 监听父组件的tabType变化，当切换到当前组件时恢复搜索信息
    '$parent.tabType': {
      handler(newVal) {
        if (newVal === 'heng') {
          // 切换到heng tab时恢复搜索信息
          this._restore_searchInfo('heng')
        }
      },
      immediate: true
    },
    'searchFormSetting.org_ids.value': function() {
      this.getAddressAreaList()
    },
    // // 配送区域
    'searchFormSetting.area_ids.value': {
      handler() {
        this.searchFormSetting.l1_addr.dataList = []
        // this.searchFormSetting.l1_addr.value = []
        this.loadAddress(1, this.searchFormSetting.area_ids.value)
      },
      immediate: true,
      deep: true
    },
    // 一级地址
    'searchFormSetting.l1_addr.value': {
      handler() {
        this.searchFormSetting.l2_addr.dataList = []
        // this.searchFormSetting.l2_addr.value = []
        if (this.searchFormSetting.l1_addr.value.length) {
          this.loadAddress(2)
        }
      },
      immediate: true,
      deep: true
    },
    'searchFormSetting.l2_addr.value': {
      handler() {
        this.searchFormSetting.l3_addr.dataList = []
        // this.searchFormSetting.l3_addr.value = ''
        if (this.searchFormSetting.l2_addr.value.length) {
          this.loadAddress(3)
        }
      },
      immediate: true,
      deep: true
    },
    'searchFormSetting.l3_addr.value': {
      handler() {
        this.searchFormSetting.l4_addr.dataList = []
        // this.searchFormSetting.l4_addr.value = ''
        if (this.searchFormSetting.l3_addr.value.length) {
          this.loadAddress(4)
        }
      },
      immediate: true,
      deep: true
    },
    'searchFormSetting.l4_addr.value': {
      handler() {
        this.searchFormSetting.l5_addr.dataList = []
        // this.searchFormSetting.l5_addr.value = ''
        if (this.searchFormSetting.l4_addr.value.length) {
          this.loadAddress(5)
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.getOrganizationList()
  },
  beforeDestroy() {
    // 组件销毁前保存搜索信息
    this._save_searchInfo('heng')
  },
  methods: {
    /**
     * 初始化搜索表单配置
     */
    initSearchFormSetting() {
      const searchFormSetting = deepClone(SEARCH_FORM_SETTING_HENG_REPORT)

      // 设置日期范围
      searchFormSetting.select_date.value = getSevenDateRange(1)

      // 设置餐段数据
      searchFormSetting.take_meal_time.dataList = MEAL_TYPES

      // 设置组织ID
      searchFormSetting.org_ids.value = [this.$store.getters.organization]
      return searchFormSetting
    },
    // 导出报表弹窗
    handleExportBefor() {
      this.printDialogShow = true
    },
    onPrintDialogCancel() {
      this.handleExport(false)
    },
    onPrintDialogConfirm() {
      this.handleExport(true)
      this.printDialogShow = false
    },
    // 打印
    async gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = [] // 打印动态表头
      let fromMartData = []
      const res = await this.$apis.apiBackgroundOrderOrderReservationGetDeliveryCollectByYPost({
        ...params,
        page: this.currentPage,
        page_size: this.totalCount || 10
      })
      if (res.code === 0) {
        if (res.data.results.length) {
          let tableHeader = res.data.results[1] || []
          const last10TableSetting = tableHeader.length ? tableHeader.slice(11) : []
          last10TableSetting.map((item, index) => {
            fromMartData.push({
              label: item,
              key: (11 + index) + ''
            })
          })
          tableSetting = this.defaultfixedTableSetting.concat(fromMartData)
        } else {
          this.$message.warning('暂无数据不支持打印')
          tableSetting = []
          return
        }
      } else {
        this.$message.error(res.msg)
        return
      }
      // console.log(tableSetting, 'tableSetting');
      // return
      const organizationStrs = this.findNamesByIds(this.organizationList, params.org_ids).join('、')
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          isUstomTableHeaders: true,
          print_type: this.printType,
          print_title: '配送汇总表-横表',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundOrderOrderReservationGetDeliveryCollectByYPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          // collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          show_current_page: true, // 显示底部当前页码
          params: JSON.stringify({
            ...params,
            start_date: params.reservation_date_start,
            end_date: params.reservation_date_end,
            page: 1,
            mealType: MEAL_TYPES.find(item => item.value === params.take_meal_time)?.label || '',
            organizationStrs,
            page_size: this.totalCount ? this.totalCount : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    initLoad() {
      this.getHengList()
      this.getAddressAreaList()
      this.loadAddress(1)
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.printVisible = false
        this.currentPage = 1
        this.resetSelectAll()
        // 保存搜索信息
        this._save_searchInfo('heng')
        this.getHengList()
      }
      // 特殊情况处理  不能放在watch中,watch做切换回显时会触发 所以放在这里
      this.clearSubAddress(e.label)
    }, 300),
    // 清空下级地址
    clearSubAddress(label) {
      // 定义地址层级映射关系
      const ADDRESS_LEVEL_MAP = {
        '配送区域': ['l1_addr', 'l2_addr', 'l3_addr', 'l4_addr', 'l5_addr'],
        '一级地址': ['l2_addr', 'l3_addr', 'l4_addr', 'l5_addr'],
        '二级地址': ['l3_addr', 'l4_addr', 'l5_addr'],
        '三级地址': ['l4_addr', 'l5_addr'],
        '四级地址': ['l5_addr']
      }
      const levelsToClear = ADDRESS_LEVEL_MAP[label]
      if (levelsToClear) {
        levelsToClear.forEach(level => {
          this.searchFormSetting[level].value = []
        })
      }
    },
    resetSearchHandle() {
      this.$refs.searchRef.resetForm()
      this.searchHandle()
    },
    resetHandle() {
      this.resetSelectAll()
      this.searchFormSetting.org_ids.value = [this.$store.getters.organization]
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' || data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.reservation_date_start = data[key].value[0]
            params.reservation_date_end = data[key].value[1]
          }
        }
      }
      return params
    },
    async getHengList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderReservationGetDeliveryCollectByYPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        if (res.data.results.length) {
          this.totalData = ['', ...res.data.results[0]] // 有个勾选框，合计往后退一个
          this.tableSetting = []
          for (let i = 0; i < res.data.results[1].length; i++) {
            let labelValue = res.data.results[1][i]
            this.tableSetting.push({
              label: labelValue,
              key: i + ''
            })
          }
          // 动态设置表头
          this.setTableHeader(res.data.results)
          this.tableData = res.data.results.splice(2, res.data.results.length - 2) // 用数据的index做prop
          this.tableData.unshift(res.data.results[0])
          this.otherInfo = res.data.other_info
        } else {
          this.tableData = []
        }
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态设置固定表头
    setTableHeader(list) {
      const headerData = list[1] || [];
      // 只在首次初始化时设置固定表头
      if (!this.isTableSettingInitialized) {
        this.fixedTableSetting = this.createTableHeaders(headerData.slice(0, this.FIXED_HEADER_COUNT), 0);
        this.defaultfixedTableSetting = [...this.fixedTableSetting];
      }
      // 生成可滚动部分的表头
      this.noFixedTableSetting = this.createTableHeaders(
        headerData.slice(this.FIXED_HEADER_COUNT),
        this.FIXED_HEADER_COUNT
      );
      // 合并固定和可滚动表头
      this.currentTableSetting = [...this.defaultfixedTableSetting, ...this.noFixedTableSetting];
      this.isTableSettingInitialized = true;
    },

    // 创建表头配置的辅助方法
    createTableHeaders(headers, startIndex) {
      return headers.map((label, index) => ({
        label,
        key: (startIndex + index).toString()
      }));
    },
    // 打印设置弹窗
    confirmPrintDialog(confirmTableSetting) {
      this.defaultfixedTableSetting = confirmTableSetting
      this.currentTableSetting = [...confirmTableSetting, ...this.noFixedTableSetting]
    },
    // 分页页数change事件
    async handleSizeChange(val) {
      this.pageSize = val
      await this.getHengList()
      this.changeTableSelection()
    },
    // 分页页码change事件
    async handleCurrentChange(val) {
      this.currentPage = val
      await this.getHengList()
      this.changeTableSelection()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    selectSelection(row) {
      this.changeSelectSelection(row)
    },
    selectSelectionAll(row) {
      this.changeSelectSelection(row)
    },
    changeSelectSelection (row) {
      let rowIdS = row.map(item => {
        let index = this.tableData.indexOf(item)
        return this.otherInfo[index].order_payment_id
      })
      this.tableData.forEach((item, index) => {
        let id = this.otherInfo[index].order_payment_id
        if (!rowIdS.includes(id)) {
          var selectListIdIndex = this.selectOrderId.indexOf(id)
          if (selectListIdIndex !== -1) {
            this.selectOrderId.splice(selectListIdIndex, 1)
          }
        } else {
          this.selectOrderId.push(...rowIdS)
          this.selectOrderId = [...new Set(this.selectOrderId)]
        }
      })
      if (this.selectOrderId.length) {
        this.isSelectAll = this.selectListIdCount === this.selectOrderId.length ? true : 0
      }
    },
    changeTableSelection() {
      if (this.tableData.length) {
        this.tableData.forEach((item, index) => {
          let id = this.otherInfo[index] ? this.otherInfo[index].order_payment_id : ''
          // 匹配勾选上
          if (this.selectOrderId.includes(id)) {
            this.$nextTick(() => {
              this.$refs.tableData.toggleRowSelection(item);
            })
          }
        })
      }
    },
    async getAddressAreaList() {
      const res = await this.$apis.apiAddressAddersAreaAllPost({
        used_org_ids: this.searchFormSetting.org_ids.value
      })
      if (res.code === 0) {
        this.searchFormSetting.area_ids.dataList = [
          {
            name: '未命名区域',
            id: 0
          },
          ...res.data
        ]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态配送点数据
    async loadAddress(level, areaIds) {
      // 这里的level是这样的：一级组织的level=1，传给后端需要-1
      let params = {
        page: 1,
        page_size: 99999,
        level: level - 1,
        used_for_web: true
      }
      if (level === 2) {
        params.parent_id = this.searchFormSetting.l1_addr.value
      } else if (level === 3) {
        params.parent_id = this.searchFormSetting.l2_addr.value
      } else if (level === 4) {
        params.parent_id = this.searchFormSetting.l3_addr.value
      } else if (level === 5) {
        params.parent_id = this.searchFormSetting.l4_addr.value
      }
      if (areaIds) {
        params.area_id = areaIds
      }
      const res = await this.$apis.apiAddressAddersCenterListPost(params)
      if (res.code === 0) {
        if (level === 1) {
          this.searchFormSetting.l1_addr.dataList = res.data.results
        } else if (level === 2) {
          this.searchFormSetting.l2_addr.dataList = res.data.results
        } else if (level === 3) {
          this.searchFormSetting.l3_addr.dataList = res.data.results
        } else if (level === 4) {
          this.searchFormSetting.l4_addr.dataList = res.data.results
        } else if (level === 5) {
          this.searchFormSetting.l5_addr.dataList = res.data.results
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 合计
    getSummaries(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        sums[index] = this.totalData[index]
      });
      return sums;
    },
    // 导出报表
    handleExport(isOrgCollectExport) {
      const option = {
        type: 'HengReport',
        url: 'apiBackgroundOrderOrderReservationGetDeliveryCollectByYPost',
        immediate: true,
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          is_export: true,
          is_org_collect_export: isOrgCollectExport
        }
      }
      this.exportHandle(option)
    },
    openDialog() {
      if (!this.selectOrderId.length) {
        return this.$message.error('请先选择数据！')
      }
      console.log('selectOrderId', this.selectOrderId);
      this.printVisible = true
    },
    /**
     * 全选监听
     */
    async selectAllChange(value) {
      this.$refs.tableData.clearSelection()
      this.selectOrderId = []
      if (value) {
        // 全选
        if (Reflect.has(this.$parent, 'getDeliveryCollectByXyIds')) {
          this.isLoading = true
          var list = await this.$parent.getDeliveryCollectByXyIds({
            ...this.formatQueryParams(this.searchFormSetting),
            page: this.currentPage,
            page_size: this.pageSize
          })
          this.isLoading = false
          if (list && list.length > 0) {
            this.selectOrderId = deepClone(list)
            this.selectListIdCount = list.length
            this.changeTableSelection()
          } else {
            this.isSelectAll = false
          }
        }
      } else {
        // 反选列表数据
        this.$refs.tableData.clearSelection()
        this.selectOrderId = []
      }
    },
    /**
     * 重置全选
     */
    resetSelectAll() {
      // if (this.isSelectAll) {
      this.isSelectAll = false
      this.selectOrderId = []
      this.$refs.tableData.clearSelection()
      // }
    },
    // 通过组织id获取组织名称
    findNamesByIds(treeData, selectedIds) {
      // 创建映射表：id -> name
      const idToNameMap = new Map();
      // 递归遍历树形结构
      function traverse(nodes) {
        nodes.forEach(node => {
          // 将当前节点的id和name存入映射表
          idToNameMap.set(node.id, node.name);
          // 如果存在子节点，递归遍历子节点
          if (node.children_list && node.children_list.length > 0) {
            traverse(node.children_list);
          }
        });
      }
      // 从根节点开始遍历
      traverse(treeData);
      // 根据选中的id列表获取对应的名称
      return selectedIds.map(id => idToNameMap.get(id) || null);
    },
    // 获取下级组织数据
    async getOrganizationList(id) {
      const res = await this.$apis.apiBackgroundOrganizationOrganizationTreeListPost({
        org_id: id
      })
      if (res.code === 0) {
        this.organizationList = res.data
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';

.HengReport {
  // 将合计放到表格最上方
  // .el-table {
  //   display: flex;
  //   flex-direction: column;
  // }
  // order默认值为0，order越大，位置越往后，这样合计行就上移到表格上方
  ::v-deep .el-table__footer-wrapper{
    display: none;
    // order: 0;
    // overflow-y: scroll;
  }
  ::v-deep .el-table tr:nth-child(1) {
    // 样式应用于第一个tr
    position: sticky;
    top: 0;
    z-index: 10;
    background: #fff;
  }
  ::v-deep .el-table tr:nth-child(1) {
    td:nth-child(1) > div:nth-child(1){
      display: none;
    }
  }
  ::v-deep .el-table__header-wrapper{
    order: 1;
    // overflow-y: scroll;
  }
  ::v-deep .el-table__body-wrapper {
    order: 2;
    overflow-y: scroll;
    max-height: 500px;
  }
}
</style>
