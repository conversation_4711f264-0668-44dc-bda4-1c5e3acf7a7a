<template>
  <!-- 添加/编辑 -->
  <custom-drawer
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :wrapperClosable="false"
    :size="760"
    destroy-on-close
    v-bind="$attrs"
    v-on="$listeners"
    :confirmShow="false"
    cancelText="关闭"
    @close="handlerClose"
    @cancel="clickCancleHandle"
  >
    <div class="p-10">
      <el-table
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        size="small"
        stripe
        header-row-class-name="ps-table-header-row"
        tooltip-effect="dark property-info-history-drawer-tooltips"
        row-key="id"
        border
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item">
          <template #operator_name="{ row }">
            <span v-if="row.operator_member_name">{{ row.operator_member_name }}（{{ row.operator_name }}）</span>
            <span v-else>--</span>
          </template>
        </table-column>
      </el-table>
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :page-sizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
    </div>
  </custom-drawer>
  <!-- end -->
</template>

<script>
export default {
  inheritAttrs: false,
  name: 'PropertyInfoHistoryDrawer',
  props: {
    isShow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '历史记录'
    },
    width: {
      type: String,
      default: '460px'
    },
    showFooter: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSettings: [
        { label: '创建时间', key: 'create_time' },
        { label: '文件名称', key: 'asset_info_name' },
        { label: '操作内容', key: 'remark', showTooltip: true },
        { label: '操作员', key: 'operator_name', type: 'slot', slotName: 'operator_name' }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getDataList()
    },
    async getDataList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundFundSupervisionAssetAssetInfoHistoryListPost({
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        this.tableData = data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 取消事件
    clickCancleHandle() {
      this.visible = false
    },
    // 关闭弹窗
    handlerClose(e) {
      this.visible = false
    }
  }
}
</script>

<style lang="scss">
.dialog-form {
}
.property-info-history-drawer-tooltips {
  white-space: normal; /* 允许换行 */
  word-break: break-all; /* 长单词自动换行 */
  max-width: 200px; /* 设置最大宽度，超出后换行 */
}
</style>
