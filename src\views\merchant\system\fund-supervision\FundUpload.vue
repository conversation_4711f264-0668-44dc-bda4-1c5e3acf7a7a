<template>
  <div class="to-do-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false" @linkage="setAccountType" @reset="resetHandler"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="showUpLoadDrawer('add')" v-permission="['background_fund_supervision.fund_upload.add_fund']">新建</button-icon>
          <button-icon color="plain" @click="gotoExport" v-permission="['background_fund_supervision.fund_upload.list_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #accountFee="{ row }">
              <div>{{ computedPrice(row.account_fee) }}</div>
            </template>
            <template #operation="{ row }">
              <div>
                <el-button type="text" size="small" class="ps-text" @click="getDetails(row)">详情</el-button>
                <el-button v-if="row.approval_status !== 'agreed'" type="text" size="small" class="ps-text" v-show="row.approval_status === 'pending'" :disabled="row.approval_status !== 'pending' || computedIsOrg(row)" @click="cancelUpload(row)" v-permission="['background_fund_supervision.fund_upload.revoke_approve_fund']">撤销申请</el-button>
                <el-button v-if="row.approval_status !== 'agreed'" type="text" size="small" class="ps-text" v-show="row.approval_status !== 'pending'" :disabled="row.approval_status === 'pending' || computedIsOrg(row)" @click="showUpLoadDrawer('edit', row)" v-permission="['background_fund_supervision.fund_upload.add_fund']">重新申请</el-button>
              </div>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="type === 'add' ? '新建资金上传' : '重新申请'"
        :visible="upLoadDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="upLoadDrawerFormRef" :model="upLoadDrawerForm" label-width="80px" label-position="right">
            <el-form-item :label="'名称'" prop="name" :rules="{ required: true, message: '请输入名称', trigger: ['change', 'blur'] }">
              <el-input v-model="upLoadDrawerForm.name" class="w-300" placeholder="请输入名称，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item :label="'流水类别'" prop="flowType" :rules="{ required: true, message: '请选择流水类别', trigger: ['change', 'blur'] }">
              <el-radio-group v-model="upLoadDrawerForm.flowType" @input="setFlowType">
                <el-radio :label="'operating_income'">营业收入</el-radio>
                <el-radio :label="'non_operating_income'">非营业收入</el-radio>
                <el-radio :label="'operating_cost'">营业成本</el-radio>
                <el-radio :label="'non_operating_cost'">非营业成本</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-show="upLoadDrawerForm.flowType" :label="'流水类型'" prop="accountType" :rules="{ required: true, message: '请选择流水类型', trigger: ['change', 'blur'] }">
              <el-radio-group v-model="upLoadDrawerForm.accountType">
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_income'" :label="'store_consume'" class="m-t-10 m-b-10">储值消费</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_income'" :label="'teacher_store_consume'" class="m-t-10 m-b-10">教师储值消费</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_income'" :label="'subsidy_consume'" class="m-t-10 m-b-10">补贴消费</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_income'" :label="'teacher_subsidy_consume'" class="m-t-10 m-b-10">教师补贴消费</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_income'" :label="'third_consume'" class="m-t-10 m-b-10">第三方消费</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'non_operating_income'" :label="'government_subsidy'" class="m-t-10 m-b-10">政府补助</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'non_operating_income'" :label="'public_welfare_donation'" class="m-t-10 m-b-10">公益捐赠</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_income' || upLoadDrawerForm.flowType === 'non_operating_income'" :label="'other_income'" class="m-t-10 m-b-10">其他收入</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_cost'" :label="'raw_material_cost'" class="m-t-10 m-b-10">原材料成本</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_cost'" :label="'utilities'" class="m-t-10 m-b-10">水电气成本</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_cost'" :label="'labor_cost'" class="m-t-10 m-b-10">人工成本</el-radio>
                <el-radio v-show="upLoadDrawerForm.flowType === 'operating_cost' || upLoadDrawerForm.flowType === 'non_operating_cost'" :label="'other_costs'" class="m-t-10 m-b-10">其他成本</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="'记账日期'" prop="date" :rules="{ required: true, message: '请选择记账日期', trigger: ['change', 'blur'] }">
              <el-date-picker
                v-model="upLoadDrawerForm.date"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item :label="'记账金额'" prop="account" :rules="accountRules">
              <el-input v-model="upLoadDrawerForm.account" class="w-300" placeholder="请输入记账金额，保留小数点后两位"></el-input>
            </el-form-item>
            <el-form-item :label="'备注'" prop="remark">
              <el-input type="textarea" v-model="upLoadDrawerForm.remark" :show-word-limit="true" maxlength="100" :autosize="{ minRows: 6, maxRows: 8}" resize="none"></el-input>
            </el-form-item>
            <el-form-item :label="'附件'" prop="file">
              <el-upload
                v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                :action="serverUrl" :data="uploadParams" :file-list="fileLists" :on-success="uploadSuccess"
                :before-upload="beforeImgUpload" :limit="5" :multiple="false" :show-file-list="true"
                :headers="headersOpts">
                <div class="flex-center">
                  <el-button class="m-r-20" size="small" icon="el-icon-plus">添加文件</el-button>
                  <div slot="tip" class="el-upload__tip">不超过20M</div>
                </div>
              </el-upload>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="cancelHandle('upload')">取消</el-button>
            <el-button class="w-100 ps-btn" @click="upLoadRequest">提交申请</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'详情'"
        :visible="detailDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="detailDrawerFormRef" :model="detailDrawerForm" label-width="80px" label-position="right">
            <el-form-item :label="'名称'">
              <div class="m-l-5">{{ detailDrawerForm.name }}</div>
            </el-form-item>
            <el-form-item :label="'申请单号'">
              <div class="m-l-5">{{ detailDrawerForm.application_no }}</div>
            </el-form-item>
            <el-form-item :label="'创建人'">
              <div class="m-l-5">{{ detailDrawerForm.founder }}</div>
            </el-form-item>
            <el-form-item :label="'所属组织'">
              <div class="m-l-5">{{ detailDrawerForm.org }}</div>
            </el-form-item>
            <el-form-item :label="'流水类别'">
              <div class="m-l-5">{{ detailDrawerForm.flow_type }}</div>
            </el-form-item>
            <el-form-item :label="'流水类型'">
              <div class="m-l-5">{{ detailDrawerForm.account_type }}</div>
            </el-form-item>
            <el-form-item :label="'记账日期'">
              <div class="m-l-5">{{ detailDrawerForm.account_date }}</div>
            </el-form-item>
            <el-form-item :label="'记账金额'">
              <div class="m-l-5">{{ computedPrice(detailDrawerForm.account_price) }}</div>
            </el-form-item>
            <el-form-item :label="'备注'">
              <div class="m-l-5">{{ detailDrawerForm.remark }}</div>
            </el-form-item>
            <el-form-item :label="'附件'">
              <!-- {{ detailDrawerForm.fileList }} -->
              <div v-for="(item, index) in detailDrawerForm.fileList" :key="index" class="m-l-5">
                <div class="w-350 flex-b-c">
                  <div>
                    <i class="el-icon-document"></i>
                    <span>{{ item.name }}</span>
                  </div>
                  <el-button type="text" size="small" class="ps-text" @click="downloadUrl(item)">下载</el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item :label="'审批状态'">
              <el-timeline class="p-t-10 m-l-5">
                <el-timeline-item
                  :icon="item.icon"
                  :color="item.color"
                  :size="'large'"
                  v-for="(item, index) in detailDrawerForm.status"
                  :key="index"
                  :timestamp="item.status_alias"
                  :placement="'top'">
                  <div v-for="(itemIn, indexIn) in item.data" :key="indexIn" :class="[approveMethod === 'and_approve' && index !== 0 ? 'bg-grey' : '', 'm-b-10']">
                    <!--这里做个区别，会签和其他两个区别显示-->
                    <div v-if="approveMethod !== 'and_approve'" class="flex-col">
                      <div class="w-350 flex-b-c">
                        <div>{{ itemIn.operator }}</div>
                        <div class="w-150 flex-b-c" v-show="itemIn.status !== 'PENDING'">
                          <div v-show="itemIn.status !== 'PENDING'">{{ itemIn.timestamp }}</div>
                          <i :class="itemIn.icon" :style="{'color': itemIn.color, 'fontSize': '18px'}"></i>
                        </div>
                      </div>
                      <div v-show="itemIn.status === 'REJECT'" class="red">
                          拒绝原因：{{ item.reason }}
                        </div>
                    </div>
                    <div v-else>
                      <div v-for="(childItem, childIndex) in itemIn" :key="childIndex" class="flex-col">
                        <div class="w-350 flex-b-c">
                          <div>{{ childItem.operator }}</div>
                          <div class="w-150 flex-b-c" v-show="childItem.status !== 'PENDING'">
                            <div v-show="childItem.status !== 'PENDING'">{{ childItem.timestamp }}</div>
                            <i :class="[childItem.icon, 'icon']" :style="{'color': childItem.color, 'fontSize': '18px'}"></i>
                          </div>
                        </div>
                        <div v-show="childItem.status === 'REJECT'" class="red">
                          拒绝原因：{{ item.reason }}
                        </div>
                      </div>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="cancelHandle('detail')">关闭</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { debounce, deepClone, getRequestParams, getToken, divide, times } from '@/utils'
import dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { mapGetters } from 'vuex';
export default {
  mixins: [exportExcel],
  data() {
    const RECENTSEVEN = [
      dayjs()
        .subtract(7, 'day')
        .format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ]
    return {
      isLoading: false,
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '申请时间',
          clearable: false,
          value: RECENTSEVEN
        },
        application_no: {
          type: 'input',
          label: '申请单号',
          value: '',
          placeholder: '请输入申请单号'
        },
        name: {
          type: 'input',
          label: '名称',
          value: '',
          placeholder: '请输入申请单号名称'
        },
        organization_ids: {
          type: 'organizationSelect',
          value: '',
          label: '所属组织',
          placeholder: '请选择',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: [],
          multiple: true,
          checkStrictly: true,
          collapseTags: true,
          clearable: true
        },
        flow_type: {
          type: 'select',
          label: '流水类别',
          value: '',
          placeholder: '请选择流水类别',
          isLinkage: true,
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '营业收入',
              value: 'operating_income'
            },
            {
              label: '非营业收入',
              value: 'non_operating_income'
            },
            {
              label: '营业成本',
              value: 'operating_cost'
            },
            {
              label: '非营业成本',
              value: 'non_operating_cost'
            }
          ]
        },
        account_type: {
          type: 'select',
          label: '流水类型',
          value: '',
          placeholder: '请选择流水类型',
          dataList: [],
          multiple: true,
          collapseTags: true,
          clearable: true,
          disabled: true
        },
        approval_status: {
          type: 'select',
          label: '审批状态',
          value: '',
          placeholder: '请选择',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '审批中',
              value: 'pending'
            },
            {
              label: '已同意',
              value: 'agreed'
            },
            {
              label: '已拒绝',
              value: 'rejected'
            },
            {
              label: '已撤销',
              value: 'rescinded'
            }
          ]
        }
      },
      tableData: [],
      tableSetting: [
        { label: '名称', key: 'name' },
        { label: '申请单号', key: 'application_no' },
        { label: '申请时间', key: 'create_time' },
        { label: '所属组织', key: 'organization' },
        { label: '流水类别', key: 'flow_type_alias' },
        { label: '流水类型', key: 'account_type_alias' },
        { label: '记账日期', key: 'account_date' },
        { label: '记账金额', key: 'account_fee', type: "slot", slotName: "accountFee" },
        { label: '备注', key: 'remark', showTooltip: true },
        { label: '审批状态', key: 'approval_status_alias' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      type: 'add',
      upLoadDrawerShow: false,
      upLoadDrawerForm: {
        name: '',
        flowType: 'operating_income',
        accountType: 'store_consume',
        date: '',
        account: '',
        remark: '',
        file: ''
      },
      accountRules: [
        { required: true, message: '请输入金额', trigger: ['change', 'blur'] },
        { pattern: /^(?:0|[1-9]\d{0,5})(?:\.\d{1,2})?$/, message: '请输入正确的金额', trigger: ['change', 'blur'] }
      ],
      uploading: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'super_food_img'
      },
      fileLists: [],
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      approveMethod: '',
      detailDrawerShow: false,
      detailDrawerForm: {
        name: '',
        application_no: '',
        founder: '',
        org: '',
        flow_type: '',
        account_type: '',
        account_date: '',
        account_price: '',
        remark: '',
        fileList: [],
        status: []
      }
    }
  },
  computed: {
    ...mapGetters(['organization']),
    computedPrice() {
      return d => {
        return '￥' + divide(d, 100)
      }
    },
    computedIsOrg() {
      return d => {
        return d.organization_id !== this.organization
      }
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.getDataList()
    },
    resetHandler() {
      this.searchFormSetting.account_type.disabled = true
    },
    getDataList() {
      this.isLoading = true
      let params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      this.$apis.apiBackgroundFundSupervisionFundUploadFundUploadListPost(params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.tableData = deepClone(res.data.results || [])
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList()
    },
    // select搜素联动（目前只做了select的，后续还有别的要在原组件上改动）
    setAccountType(e) {
      this.searchFormSetting.account_type.value = []
      if (e) {
        let newDataList = []
        switch (e) {
          case 'operating_income':
            newDataList = [
              {
                label: '储值消费',
                value: 'store_consume'
              },
              {
                label: '教师储值消费',
                value: 'teacher_store_consume'
              },
              {
                label: '补贴消费',
                value: 'subsidy_consume'
              },
              {
                label: '教师补贴消费',
                value: 'teacher_subsidy_consume'
              },
              {
                label: '第三方消费',
                value: 'third_consume'
              },
              {
                label: '其他收入',
                value: 'other_income'
              }
            ]
            break;
          case 'non_operating_income':
            newDataList = [
              {
                label: '政府补助',
                value: 'government_subsidy'
              },
              {
                label: '公益捐赠',
                value: 'public_welfare_donation'
              },
              {
                label: '其他收入',
                value: 'other_income'
              }
            ]
            break;
          case 'operating_cost':
            newDataList = [
              {
                label: '原材料成本',
                value: 'raw_material_cost'
              },
              {
                label: '水电气成本',
                value: 'utilities'
              },
              {
                label: '人工成本',
                value: 'labor_cost'
              },
              {
                label: '其他成本',
                value: 'other_costs'
              }
            ]
            break;
          case 'non_operating_cost':
            newDataList = [
              {
                label: '其他成本',
                value: 'other_costs'
              }
            ]
            break;
        }
        this.searchFormSetting.account_type.dataList = deepClone(newDataList)
        this.searchFormSetting.account_type.disabled = false
      } else {
        this.searchFormSetting.account_type.disabled = true
      }
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        console.log('file', file, fileList)
        let arr = []
        fileList.forEach(item => {
          arr.push(item.response.data.public_url)
        })
        this.fileLists = deepClone(fileList)
        this.upLoadDrawerForm.file = deepClone(arr)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 图片上传前检测
    beforeImgUpload(file) {
      console.log('file', file)
      // 文件校验
      const fileType = ['jpeg', 'jpg', 'xls', 'xlsx', 'png', 'txt', 'zip', 'docx', 'doc', 'bmp', 'tiff', "JPEG", "PNG", "BMP", "TIFF", "WEBP", "HEIF", "JPG", "exe", "rar", "ZIP", "RAR"]
      const isLt2M = file.size / 1024 / 1024 <= 20
      let result = file.type.split("/")[1]
      if (!fileType.includes(result)) {
        this.$message.error('请上传正确的文件')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },
    showUpLoadDrawer(type, data) {
      this.type = type
      if (type === 'edit') {
        this.upLoadDrawerForm.name = data.name
        this.upLoadDrawerForm.flowType = data.flow_type
        this.upLoadDrawerForm.accountType = data.account_type
        this.upLoadDrawerForm.date = data.account_date
        this.upLoadDrawerForm.account = divide(data.account_fee, 100)
        this.upLoadDrawerForm.remark = data.remark
        // 处理文件
        if (data.image_json && data.image_json.length) {
          let time = new Date().getTime()
          this.fileLists = data.image_json.map(item => {
            time += 1
            let fileName = item.substring(item.lastIndexOf('/') + 1)
            let isSplit = fileName.indexOf('?')
            if (isSplit > -1) {
              fileName = fileName.substring(0, isSplit)
            }
            return {
              name: fileName,
              status: 'success',
              uid: time,
              url: item
            }
          })
          this.upLoadDrawerForm.file = deepClone(data.image_json)
        } else {
          this.fileLists = []
          this.upLoadDrawerForm.file = []
        }
      } else {
        this.upLoadDrawerForm.name = ''
        this.upLoadDrawerForm.flowType = 'operating_income'
        this.upLoadDrawerForm.accountType = 'store_consume'
        this.upLoadDrawerForm.date = ''
        this.upLoadDrawerForm.account = ''
        this.upLoadDrawerForm.remark = ''
        // 处理文件
        this.upLoadDrawerForm.file = []
        this.fileLists = []
      }
      this.upLoadDrawerShow = true
      setTimeout(() => {
        this.$refs.upLoadDrawerFormRef.clearValidate()
      }, 10)
    },
    cancelHandle(type) {
      if (type === 'upload') {
        this.$refs.upLoadDrawerFormRef.resetFields()
        this.fileLists = []
        this.upLoadDrawerShow = false
      } else {
        this.$refs.detailDrawerFormRef.resetFields()
        this.fileLists = []
        this.detailDrawerForm.status = []
        this.detailDrawerShow = false
      }
    },
    upLoadRequest() {
      this.$refs.upLoadDrawerFormRef.validate((valid) => {
        if (valid) {
          let params = {
            account_date: this.upLoadDrawerForm.date,
            name: this.upLoadDrawerForm.name,
            flow_type: this.upLoadDrawerForm.flowType,
            account_type: this.upLoadDrawerForm.accountType,
            remark: this.upLoadDrawerForm.remark || undefined,
            image_json: this.upLoadDrawerForm.file || undefined,
            account_fee: times(this.upLoadDrawerForm.account, 100)
          }
          this.$apis.apiBackgroundFundSupervisionFundUploadAddFundPost(params).then(res => {
            if (res.code === 0) {
              this.$message.success('申请成功')
            } else {
              this.$message.error(res.msg)
            }
            this.fileLists = []
            this.$refs.upLoadDrawerFormRef.resetFields()
            this.upLoadDrawerShow = false
            this.getDataList()
          })
        } else {
          this.$message.error('请检查提交内容是否正确')
        }
      })
    },
    cancelUpload(data) {
      this.$confirm(`已撤销的申请单可重新申请，确定撤销申请。`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确认撤销',
        cancelButtonText: '取消'
      }).then(() => {
        this.$apis.apiBackgroundFundSupervisionFundUploadRevokeApproveFundPost({
          id: data.id
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('撤销成功')
          } else {
            this.$message.error(res.msg)
          }
          this.getDataList()
        })
      }).catch(action => {
        this.$message('取消撤销')
      })
    },
    gotoExport() {
      let params = getRequestParams(this.searchFormSetting, this.currentPage, this.totalCount)
      const option = {
        url: 'apiBackgroundFundSupervisionFundUploadListExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    // 获取详情
    getDetails(data) {
      this.$apis.apiBackgroundFundSupervisionFundUploadFundDetailsPost({
        id: data.id
      }).then(res => {
        if (res.code === 0) {
          // 这里要区别依次审批与其他审批
          // 这里仅凭数组的长度去判断其实是很不合理的，但和陈老师商讨后确实是不知道该如何处理了，目前只能先这么判断着……
          let result = []
          if (res.data.length > 1) {
            // 大于一则是依次审批的
            result = res.data[res.data.length - 1]
          } else {
            result = res.data[0]
          }
          this.detailDrawerForm.name = result.name
          this.detailDrawerForm.application_no = result.application_no
          this.detailDrawerForm.org = result.org_name
          this.detailDrawerForm.founder = result.creator
          this.detailDrawerForm.flow_type = result.flow_type_alias
          this.detailDrawerForm.account_type = result.account_type_alias
          this.detailDrawerForm.account_date = result.account_date
          this.detailDrawerForm.account_price = result.account_fee
          this.detailDrawerForm.remark = result.remark || '--'
          this.approveMethod = result.approve_method
          // 处理文件
          if (result.image_json && result.image_json.length) {
            this.detailDrawerForm.fileList = result.image_json.map(item => {
              let fileName = item.substring(item.lastIndexOf('/') + 1)
              let isSplit = fileName.indexOf('?')
              if (isSplit > -1) {
                fileName = fileName.substring(0, isSplit)
              }
              return {
                name: fileName,
                url: item
              }
            })
          }
          // 处理状态得将之前的一起打包丢进去
          this.detailDrawerForm.status = [
            {
              icon: 'el-icon-check',
              color: '#14ce84',
              status_alias: '提交申请',
              status: 'pending',
              data: [
                {
                  icon: 'el-icon-success',
                  color: '#14ce84',
                  status_alias: '提交申请',
                  status: 'pending',
                  account_id: '',
                  timestamp: result.create_time,
                  operator: `${result.creator} (${result.username})`
                }
              ]
            }
          ]
          let newStatus = []
          // 先判断一下是不是或签，或签的话全部账号显示在一个流程里就可以了
          switch (result.approve_method) {
            case 'one_by_one_approve': {
              // 依次审批还是拿回approve_account_info组成数组显示吧
              // 先循环res.data
              res.data.forEach(item => {
                let obj = {
                  icon: 'el-icon-check',
                  color: '#ff9b45',
                  status_alias: '待审批',
                  status: 'pending',
                  reason: result.reject_reason || '',
                  data: []
                }
                let statusList = []
                if (item.approve_account_info && item.approve_account_info.length) {
                  // 将这个审批账号里的全部账号拉出来组成目标数据丢尽obj.data
                  item.approve_account_info.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name} (${itemIn.username})`
                    }
                    statusList.push(itemIn.approve_status)
                    obj.data.push(child)
                  })
                  let agreeFlag = statusList.some(item => item === 'AGREE')
                  let rejectFlag = statusList.some(item => item === 'REJECT')
                  // 把上传的obj根据里面的内容重新赋值一下
                  obj.icon = agreeFlag ? 'el-icon-check' : (rejectFlag ? 'el-icon-close' : 'el-icon-more')
                  obj.color = agreeFlag ? '#14ce84' : (rejectFlag ? '#fd594e' : '#ff9b45')
                  obj.status_alias = agreeFlag ? '审批通过' : (rejectFlag ? '拒绝审批' : '待审批')
                  obj.status = agreeFlag ? 'AGREE' : (rejectFlag ? 'REJECT' : 'PENDING')
                }
                newStatus.push(obj)
              })
              break
            }
            case 'and_approve': {
              // 如果是会签，将每个审批账号做成一个数组塞到data里面
              let obj = {
                icon: result.approval_status === 'AGREE' ? 'el-icon-check' : (result.approval_status === 'PENDING' ? 'el-icon-more' : 'el-icon-close'),
                color: this.switchColor(result.approval_status),
                status_alias: result.approval_status_alias,
                status: result.approval_status,
                reason: result.reject_reason || '',
                data: []
              }
              if (result.approve_account_info && result.approve_account_info.length) {
                result.extra.forEach(item => {
                  if (item.length) {
                    let arr = []
                    item.forEach(itemIn => {
                      let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                      let child = {
                        icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                        color: this.switchColor(itemIn.approve_status),
                        status_alias: itemIn.approve_status_alias,
                        status: itemIn.approve_status,
                        account_id: itemIn.account_id,
                        timestamp: itemIn.approve_time,
                        operator: `${itemIn.account_name} (${itemIn.username})`
                      }
                      arr.push(child)
                    })
                    obj.data.push(arr)
                  }
                })
                newStatus.push(obj)
              }
              break
            }
            case 'or_approve': {
              // 如果是或签，将所有账号放在一个流程内
              let obj = {
                icon: result.approval_status === 'AGREE' ? 'el-icon-check' : (result.approval_status === 'PENDING' ? 'el-icon-more' : 'el-icon-close'),
                color: this.switchColor(result.approval_status),
                status_alias: result.approval_status_alias,
                status: result.approval_status,
                reason: result.reject_reason || '',
                data: []
              }
              if (result.approve_account_info && result.approve_account_info.length) {
                result.extra.forEach(item => {
                  if (item.length) {
                    item.forEach(itemIn => {
                      let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                      let child = {
                        icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                        color: this.switchColor(itemIn.approve_status),
                        status_alias: itemIn.approve_status_alias,
                        status: itemIn.approve_status,
                        account_id: itemIn.account_id,
                        timestamp: itemIn.approve_time,
                        operator: `${itemIn.account_name} (${itemIn.username})`
                      }
                      obj.data.push(child)
                    })
                  }
                })
                newStatus.push(obj)
              }
              break
            }
          }
          this.addRejectStatus(result, newStatus)
          if (this.approveMethod !== 'and_approve') {
            this.detailDrawerForm.status.push(...newStatus)
          } else {
            let obj = deepClone(this.detailDrawerForm.status[0])
            obj.data = [[obj.data[0]]]
            this.detailDrawerForm.status = [obj, ...newStatus]
          }
          console.log('this.detailDrawerForm.status', this.detailDrawerForm.status)
          this.detailDrawerShow = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    addRejectStatus(data, statusArr) {
      console.log(data, statusArr)
      // 处理状态
      if (data.approval_status === 'REVOKE') {
        let obj = {
          icon: 'el-icon-error',
          color: '#909399',
          status_alias: '撤销申请',
          status: 'REVOKE',
          timestamp: data.create_time,
          operator: `${data.creator} (${data.username})`
        }
        let status = {
          icon: 'el-icon-close',
          color: '#909399',
          status_alias: '撤销申请',
          status: 'REVOKE',
          data: []
        }
        // 用历史操作处理旧数据
        let record = []
        if (data.approve_record && data.approve_record.record && data.approve_record.record.length) {
          record = deepClone(data.approve_record.record)
        }
        // 如果是撤销的，直接塞
        switch (data.approve_method) {
          case 'one_by_one_approve': {
            // 先把最后一个干掉
            statusArr.pop()
            statusArr.forEach(item => {
              let approvalStatusArr = []
              item.data.forEach(itemIn => {
                let obj = record.filter(recordItem => recordItem.account_id === itemIn.account_id)
                if (obj.length) {
                  // 如果有就改
                  let childStatus = obj[0].status === 'PENDING' || obj[0].status === 'AGREE'
                  itemIn.icon = childStatus ? 'el-icon-success' : 'el-icon-error'
                  itemIn.color = this.switchColor(obj[0].status)
                  itemIn.status_alias = obj[0].content
                  itemIn.status = obj[0].status
                  itemIn.timestamp = obj[0].time
                } else {
                  // 没有就置空
                  itemIn.icon = ''
                  itemIn.timestamp = ''
                }
                approvalStatusArr.push(itemIn.status)
              })
              // 根据statusArr里的状态去判断
              let flag = approvalStatusArr.some(item => item === 'REJECT')
              // 审批账号里面的改好了，轮到该审批账号本身的状态了
              item.icon = flag ? 'el-icon-close' : 'el-icon-check'
              item.color = flag ? this.switchColor('') : this.switchColor('AGREE')
              item.status_alias = flag ? '' : '审批通过'
              item.status = flag ? '' : 'AGREE'
            })
            // 依次审批的话因为最后一个审批账号没审就撤销了，把最后那个干掉
            status.data = [{ ...obj }]
            statusArr.push(status)
            break
          }
          case 'and_approve': {
            statusArr[0].data.forEach(item => {
              item.forEach(itemIn => {
                console.log(itemIn, record)
                let obj = record.filter(recordItem => recordItem.account_id === itemIn.account_id)
                if (obj.length) {
                  // 如果有就改
                  itemIn.icon = obj[0].status === 'AGREE' ? 'el-icon-success' : 'el-icon-error'
                  itemIn.color = this.switchColor(obj[0].status)
                  itemIn.status_alias = obj[0].content
                  itemIn.status = obj[0].status
                  itemIn.timestamp = obj[0].time
                } else {
                  // 没有就置空
                  itemIn.icon = ''
                  itemIn.timestamp = ''
                }
              })
            })
            // 审批账号里面的改好了，轮到该审批账号本身的状态了
            statusArr[0].icon = 'el-icon-more'
            statusArr[0].color = this.switchColor('PENDING')
            statusArr[0].status_alias = '待审批'
            statusArr[0].status = 'PENDING'
            status.data = [[{ ...obj }]]
            statusArr.push(status)
            break
          }
          case 'or_approve':
            // 先把最后一个干掉
            statusArr.pop()
            status.data = [{ ...obj }]
            statusArr.push(status)
            break
        }
      }
    },
    switchColor(status) {
      let color = ''
      switch (status) {
        case 'PENDING':
          color = '#ff9b45'
          break
        case 'AGREE':
          color = '#14ce84'
          break
        case 'REJECT':
          color = '#fd594e'
          break
        case 'pending':
          color = '#ff9b45'
          break
        case 'agreed':
          color = '#14ce84'
          break
        case 'rejected':
          color = '#fd594e'
          break
        default:
          color = '#909399'
      }
      return color
    },
    async downloadUrl(data) {
      try {
        const response = await axios({
          url: data.url,
          method: 'GET',
          responseType: 'blob' // 重要：设置响应类型为 blob
        })
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a')
        link.href = url
        link.download = `${data.name}` // 设置下载的文件名
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (error) {
        console.error('下载失败:', error)
      }
    },
    // 监听流水类型确认流水类别
    setFlowType(e) {
      switch (e) {
        case 'operating_income':
          this.upLoadDrawerForm.accountType = 'store_consume'
          break;
        case 'non_operating_income':
          this.upLoadDrawerForm.accountType = 'government_subsidy'
          break;
        case 'operating_cost':
          this.upLoadDrawerForm.accountType = 'raw_material_cost'
          break;
        case 'non_operating_cost':
          this.upLoadDrawerForm.accountType = 'other_costs'
          break;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-grey {
  padding: 10px 20px;
  border: 1px solid #E7E9EF;
  border-radius: 4px;
}
.icon {
  font-size: 18px;
}
::v-deep .el-timeline-item__node--large {
  left: -4px;
  width: 18px !important;
  height: 18px !important;
}
</style>
