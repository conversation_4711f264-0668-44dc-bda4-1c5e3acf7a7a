<template>
  <div class="GoodsWarehousingDialog">
    <el-dialog
      title="商品入库"
      :visible.sync="visible"
      top="20vh"
      custom-class="ps-dialog"
      show-close
      :close-on-click-modal="false"
      width="1200px"
    >
      <div>
        <el-table
          :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
          style="width: 100%"
          border
          header-row-class-name="ps-table-header-row"
          class="ps-table"
        >
          <el-table-column prop="index" label="序号" width="70" align="center"></el-table-column>
          <el-table-column prop="stock_name" label="商品名称" align="center" width="280">
            <template slot-scope="scope">
              <!--ref="el_auto"  为了解决关闭后不匹配 -->
              <el-autocomplete
                ref="el_auto"
                v-model="scope.row.stock_name"
                :fetch-suggestions="querySearch"
                :trigger-on-focus="false"
                :clearable="true"
                :popper-class="noData ? 'platform-auto-complete' : ''"
                value-key="stock_name"
                size="mini"
                style="width: 250px;"
                class="inline-input"
                placeholder="请输入内容"
                @input="changeHandleGoods($event, scope.row.index)"
                @select="handleSubmit($event, scope.row.index)"
                @clear="clearSelect(scope.row.index)"
              >
                <template v-if="noData" slot-scope="{ item }">
                  <div class="default">{{ item.default }}</div>
                </template>
              </el-autocomplete>
            </template>
          </el-table-column>
          <el-table-column prop="spec" label="规格" align="center"></el-table-column>
          <el-table-column prop="barcode" label="条码" align="center" width="180">
            <template slot-scope="scope">
              <div class="ps-flex flex-align-c flex-justify-c">
                <el-input
                  class="ps-input"
                  size="mini"
                  v-model="scope.row.barcode"
                  @input="changeBarcode(scope.row, scope.row.index)"
                ></el-input>
                <el-button
                  type="text"
                  size="small"
                  class="ps-text-blue m-l-10"
                  @click="clickStockSearch(scope.row, scope.row.index)"
                >
                  搜索
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="stock_num" label="当前库存" align="center"></el-table-column>
          <el-table-column prop="current_stock_num" label="当前入库量" align="center" width="100">
            <template slot-scope="scope">
              <el-input
                class="ps-input"
                size="mini"
                onkeyup="value=value.replace(/[^\d]/g,'')"
                maxlength="4"
                v-model="scope.row.current_stock_num"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="small" class="ps-text" @click="clickAddStock">
                新增
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                @click="clickDelectStock(scope.row)"
                v-if="tableData.length > 1"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pageSizeItem ps-pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-size="pageSize"
            layout="prev, pager, next, total, jumper"
            :total="tableData.length"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="canceDialogHandle">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          @click="clickDetermineWarehousing"
          v-loading="isLoading"
        >
          确定入库
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { to } from '@/utils'
export default {
  props: {
    isshow: Boolean,
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      dialogVisible: false, // 是否显示隐藏弹窗
      allGoodsInfos: [],
      tableData: [
        {
          index: 1,
          id: '',
          stock_name: '',
          spec: '',
          barcode: '',
          stock_num: '',
          current_stock_num: ''
        }
      ],
      currentPage: 1,
      pageSize: 10,
      noData: false // 是否匹配到数据了
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    this.initGoodsList()
  },
  methods: {
    getGoodsList(params) {
      return new Promise(resolve => {
        this.isLoading = true
        this.$apis
          .apiBackgroundStoreGoodsListPost({
            ...params,
            page: 1,
            page_size: 99999
          })
          .then(res => {
            this.isLoading = false
            if (Reflect.has(res, 'code') && res.code === 0) {
              console.log('res.data', res.data)
              var data = res.data || {}
              resolve(data.results)
            }
            resolve([])
          })
          .catch(error => {
            this.isLoading = false
            console.log('error', error)
            resolve([])
          })
      })
    },
    async initGoodsList() {
      let goodsList = await this.getGoodsList()
      if (!goodsList.length) return
      this.allGoodsInfos = goodsList.map(v => {
        v.stock_name = v.name
        // + '--' + v.spec
        return v
      })
    },
    changeHandleGoods(item, index) {
      let stockData = {
        stock_name: item
      }
      this.initStockData(index, stockData)
    },
    handleSubmit(item, index) {
      if (item.default) return
      for (let tableDataIndex = 0; tableDataIndex < this.tableData.length; tableDataIndex++) {
        if (this.tableData[tableDataIndex].id === item.id) {
          this.initStockData(index)
          return this.$message.error('当前选择的商品已存在')
        }
      }
      let stockData = {
        id: item.id,
        stock_name: item.stock_name,
        spec: item.spec,
        barcode: item.barcode,
        stock_num: item.stock_num
      }
      this.initStockData(index, stockData)
    },
    // 输入框获取焦点时调用的方法
    querySearch(queryString, cb) {
      // allInfos是怎么来，是从父组件传过来的还是在自己组件调用api接口拿到的还是其他
      // 我这里的allInfos是从父组件传过来的，在这里也举例子组件从父组件传值
      // let results = this.allGoodsInfos
      let results = queryString
        ? this.allGoodsInfos.filter(this.createFilter(queryString))
        : this.allGoodsInfos
      this.noData = false
      if (results.length === 0) {
        results = [{ default: '无匹配数据' }]
        this.noData = true
      }
      // results = queryString ? results.filter(this.createFilter(queryString)) : results
      // 控制展示条数，防止数据太大加载时间过长，影响体验
      if (results.length > 100) {
        results.splice(100, results.length - 1)
      }
      // 返回筛选出的结果数据到输入框下面的输入列表
      cb(results)
    },
    // 当输入数据时触发的，筛选出和输入数据匹配的建议输入。
    // 调用match方法，是模糊匹配；官方调用的是indexOf，是精确匹配，看自身情况选择
    createFilter(queryString) {
      return item => {
        return item.stock_name.toUpperCase().match(queryString.toUpperCase())
      }
    },
    // 看element源码发现，在点击关闭时，发现把activated 置为false了
    clearSelect(index) {
      this.$refs.el_auto.activated = true
      // 如果输入了 就空值
      this.initStockData(index)
      // 方法二:让输入框失去焦点
      // document.activeElement.blur()
    },
    changeBarcode(row, index) {
      // 如果输入了 就空值
      let stockData = {
        barcode: row.barcode
      }
      this.initStockData(index, stockData)
    },
    async clickStockSearch(row, index) {
      if (!row.barcode) return
      let params = { full_barcode: row.barcode }
      let list = await this.getGoodsList(params)
      if (!list.length) {
        this.$confirm('未找到该条码对应商品，请重新输入', '提示', {
          cancelButtonText: '关闭',
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          showConfirmButton: false,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              done()
            } else {
              if (!instance.confirmButtonLoading) {
                done()
              }
            }
          }
        })
          .then(e => {})
          .catch(e => {})
        return
      }
      for (let tableDataIndex = 0; tableDataIndex < this.tableData.length; tableDataIndex++) {
        if (this.tableData[tableDataIndex].id === list[0].id && index !== tableDataIndex) {
          this.initStockData(index)
          return this.$message.error('当前选择的商品已存在')
        }
      }
      // + '--' + list[0].spec
      let stockData = {
        id: list[0].id,
        stock_name: list[0].name,
        spec: list[0].spec,
        barcode: list[0].barcode,
        stock_num: list[0].stock_num
      }
      // 初始化table内容
      this.initStockData(index, stockData)
    },
    initStockData(index, currentData) {
      let stockData = currentData || {}
      let data = {
        index: index,
        id: stockData.id ? stockData.id : '',
        stock_name: stockData.stock_name ? stockData.stock_name : '',
        spec: stockData.spec ? stockData.spec : '',
        barcode: stockData.barcode ? stockData.barcode : '',
        stock_num: stockData.stock_num ? stockData.stock_num : '',
        current_stock_num: stockData.current_stock_num ? stockData.current_stock_num : ''
      }
      this.$set(this.tableData, index - 1, data)
    },
    // 入库
    async setGoodsAddStock(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsAddStockPost({
          stock_list: params
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$emit('determineStock', res.data)
        this.visible = false
      } else {
        this.$message.error(res.msg)
      }
    },
    clickDetermineWarehousing() {
      for (let index = 0; index < this.tableData.length; index++) {
        let currentData = this.tableData[index]
        if (!currentData.stock_name || !currentData.barcode || !currentData.current_stock_num) {
          return this.$message.error(`第${currentData.index}条，请完善信息`)
        }
      }
      let stockList = this.tableData.map(v => {
        return { id: v.id, stock_num: Number(v.current_stock_num) }
      })
      this.setGoodsAddStock(stockList)
    },

    clickAddStock() {
      this.tableData.push({
        index: this.tableData.length + 1,
        id: '',
        stock_name: '',
        spec: '',
        barcode: '',
        stock_num: '',
        current_stock_num: ''
      })
    },
    clickDelectStock(row) {
      let index = row.index - 1
      this.tableData.splice(index, 1)
      this.tableData.forEach((item, goodsIndex) => {
        item.index = goodsIndex + 1
      })
    },
    canceDialogHandle() {
      this.visible = false
    },
    handleSizeChange(val) {
      this.pageSize = val
      // this.currentPageLoad = 1; // 改变页面大小后，跳转回第一页
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style lang="scss" scoped>
.GoodsWarehousingDialog {
  .ps-text-blue {
    color: #229bff;
  }
  .platform-auto-complete {
    .el-autocomplete-suggestion__wrap {
      padding: 5px 0;
      ul li {
        pointer-events: none; // 阻止可点击事件
        .default {
          text-align: center;
          color: #999;
        }
        &:hover {
          background-color: #fff;
        }
      }
    }
  }
}
</style>
