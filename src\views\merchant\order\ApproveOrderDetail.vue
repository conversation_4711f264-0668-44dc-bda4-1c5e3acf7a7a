<template>
  <!-- eslint-disable vue/no-unused-vars -->
  <div class="appeal-order-detail container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- 基本信息 -->
    <div class="box-wrapper" v-loading="isLoading">
      <div class="box-header">
        <div class="box-title">基本信息</div>
      </div>
      <!-- table start -->
      <div class="box-content">
        <div class="user-info-wrapper">
        <div class="user-img">
          <!-- <el-image :src="approveOrderDetail.img_url" lazy></el-image> -->
          <el-avatar v-if="approveOrderDetail.img_url" :size="60" fit="cover" shape="square" :src="approveOrderDetail.img_url"></el-avatar>
          <el-avatar v-else :size="60" fit="cover" shape="square" :src="require('@/assets/img/account-img.png')"></el-avatar>
        </div>
        <div class="user-info-r clearfix" style="min-width: 700px;">
          <!-- 左右布局吧，上下宽度没保障 -->
          <div class="float-l clearfix">
            <div class="info-item">
              <div class="label">姓名：</div>
              <div class="value">{{ approveOrderDetail.name ? approveOrderDetail.name : '--' }}</div>
            </div>
            <div class="info-item">
              <div class="label">卡号：</div>
              <div class="value">{{ approveOrderDetail.card_no ? approveOrderDetail.card_no : '--' }}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">手机号：</div>
              <div class="value">{{ approveOrderDetail.phone ? approveOrderDetail.phone : '--' }}</div>
            </div>
            <div class="info-item">
              <div class="label">部门：</div>
              <div class="value">{{ approveOrderDetail.payer_department_group_name ? approveOrderDetail.payer_department_group_name : '--' }}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">有关分组：</div>
              <div class="value">{{ approveOrderDetail.payer_group_name ? approveOrderDetail.payer_group_name : '--' }}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">人员编号：</div>
              <div class="value">{{ approveOrderDetail.person_no ? approveOrderDetail.person_no : '--' }}</div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
    <!-- 订单信息 -->
    <div class="box-wrapper margin-t-20">
      <div class="box-header">
        <div class="box-title">审核详情</div>
      </div>
      <!-- table start -->
      <div class="box-content">
        <div class="user-info-wrapper">
          <div class="user-info-r clearfix">
            <el-scrollbar>
            <!-- 左右布局吧，上下宽度没保障 -->
            <div class="float-l clearfix">
              <div class="info-item">
                <div class="label">审核编号：</div>
                <div class="value">{{ approveOrderDetail.review_no ? approveOrderDetail.review_no : '--' }}</div>
              </div>
              <div class="info-item">
                <div class="label">申请原因：</div>
                <div class="value">{{ approveOrderDetail.review_reason_alias ? approveOrderDetail.review_reason_alias : '--' }}</div>
              </div>
            </div>
            <div class="float-l">
              <div class="info-item">
                <div class="label">申请时间：</div>
                <div class="value">{{ approveOrderDetail.apply_time ? approveOrderDetail.apply_time : '--' }}</div>
              </div>
              <div class="info-item">
                <div class="label">备注：</div>
                <div class="value">{{ approveOrderDetail.remark ? approveOrderDetail.remark : '--' }}</div>
              </div>
            </div>
            </el-scrollbar>
          </div>
        </div>
        <div class="order-price-box clearfix">
          <div class="order-p-item">
            <p class="order-label">订单金额：</p>
            <p class="order-price">￥{{ approveOrderDetail.origin_fee | formatMoney }}</p>
          </div>
          <div class="order-p-item">
            <p class="order-label">优惠金额：</p>
            <p class="order-price red">￥{{ approveOrderDetail.discount_fee | formatMoney }}</p>
          </div>
          <div class="order-p-item">
            <p class="order-label">补贴消费：</p>
            <p class="order-price">￥{{ approveOrderDetail.subsidy_fee | formatMoney }}</p>
          </div>
          <div class="order-p-item">
            <p class="order-label">服务费：</p>
            <p class="order-price">￥{{ approveOrderDetail.fuwu_fee | formatMoney }}</p>
          </div>
          <div class="order-p-item">
            <p class="order-label">实收金额：</p>
            <p class="order-price">￥{{ approveOrderDetail.real_fee | formatMoney }}</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 申请取消订单 -->
    <div class="box-wrapper margin-t-20">
      <div class="box-header">
        <div class="box-title">申请取消订单</div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <custom-table
          border
          :table-data="foodData"
          :table-setting="tableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          />
      </div>
    </div>
    <!-- 申诉信息 -->
    <div v-if="approveOrderDetail.review_status === 'reject'" class="box-wrapper margin-t-20">
      <div class="box-header">
        <div class="box-title">
          拒绝原因
        </div>
      </div>
      <!-- table start -->
      <div class="box-content">
        <div class="appeal-item">
          <span>{{ approveOrderDetail.reject_reason }}</span>
        </div>
      </div>
    </div>
    <div class="appeal-footer" v-if="approveOrderDetail.review_status === 'wait'">
      <el-button size="small" style="width: 100px;" @click="openDialogHandle('reject')" v-permission="['background_order.order_review.order_review_operate_no']">拒绝</el-button>
      <el-button class="ps-origin-btn" style="width: 100px;" size="small" @click="openDialogHandle" v-permission="['background_order.order_review.order_review_operate_yes']">同意</el-button>
    </div>
    <!-- 弹窗 -->
    <el-dialog :visible.sync="dialogVisible" :width="dialogWidth" :top="dialogTop" :title="dialogTitle" :close-on-press-escape="false" :close-on-click-modal="false" @closed="closeDialogHandle" custom-class="appeal-dialog">
      <div class="">
        <el-input
          type="textarea"
          :rows="4"
          :maxlength="100"
          show-word-limit
          placeholder="请输入内容"
          v-model="dialogForm.rejectReason">
        </el-input>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button size="small" class="ps-cancel-btn" :disabled="isLoading" @click="cancelDialogHandle">取 消</el-button>
        <el-button size="small" type="primary" class="ps-origin-btn" :disabled="isLoading" @click="submitDialogHaldle('reject', 'reject')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { debounce, to } from '@/utils'

export default {
  name: 'AppealOrder',
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      type: '',
      tabType: 1,
      // 数据列表
      foodData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      walletOrgsList: [], // 动账钱包
      dealStatusAlias: { // 处理状态
        PENDING: '待处理',
        HANG_OUT: '挂起中',
        TIMEOUT: '已逾期',
        CHANGE_FEE: '修改金额',
        ORDER_FREE: '整单不扣费',
        ORDER_REJECT: '整单驳回',
        ORDER_REFUND: '退款'
      },
      approveOrderDetail: {},
      deviceList: [], // 设备信息
      dialogVisible: false,
      dialogTitle: '提示',
      dialogTop: '30vh',
      dialogWidth: '400px',
      dialogType: '',
      dialogForm: {
        rejectReason: ''
      },
      dialogTableData: {},
      tableSetting: [
        { label: '序号', key: 'index', type: 'index' },
        { label: '总单号', key: 'unified_out_trade_no' },
        { label: '订单号', key: 'trade_no' },
        { label: '第三方订单号', key: 'out_trade_no' },
        { label: '餐段', key: 'meal_type_alias' },
        { label: '创建时间', key: 'create_time' },
        { label: '支付时间', key: 'pay_time' },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        { label: '优惠金额', key: 'discount_fee', type: 'money' },
        { label: '优惠类型', key: 'discount_type_alias' },
        { label: '实收金额', key: 'pay_fee', type: 'money' },
        { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
        { label: '储值动账', key: 'wallet_fee', type: 'money' },
        { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
        { label: '支付方式', key: 'sub_payway_alias' },
        { label: '支付类型', key: 'payway_alias' },
        { label: '支付状态', key: 'order_status_alias' },
        { label: '对账状态', key: 'settle_status_alias' },
        { label: '动账组织', key: 'wallet_org' }
      ]
    }
  },
  created() {
    this.type = this.$route.query.type
    this.getLevelName()
    this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
      this.getAppealDetail()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // level name
    async getLevelName() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetLevelNamePost()
      if (res.code === 0) {
        if (res.data) {
          let levelList = res.data.map(v => {
            return {
              key: v.level,
              label: v.name
            }
          })
          this.tableSetting.push(...levelList)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取详情
    async getAppealDetail() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderReviewOrderReviewInfoPost({
        id: this.$route.query.id
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('getAppealDetail', res)
      if (res.code === 0) {
        this.approveOrderDetail = res.data
        this.foodData = res.data.order_payments
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getBusinessList()
    },
    // 打开弹窗
    openDialogHandle(type, data) {
      this.dialogType = type
      if (type === 'reject') {
        this.dialogTitle = "请填写拒绝原因"
        this.dialogVisible = true
      } else {
        this.dialogTitle = "提示"
        this.submitDialogHaldle(type, 'success')
      }
    },
    cancelDialogHandle() {
      this.dialogVisible = false
    },
    closeDialogHandle() {
      this.dialogType = ''
      this.dialogForm = {
        rejectReason: ''
      }
    },
    submitDialogHaldle(type, status) {
      let params = {
        id: this.approveOrderDetail.id,
        review_status: status
      }
      if (type !== 'reject') {
        this.$confirm('<p>确定同意该笔订单的审批吗？</p><p style="color: red;">确定后不可撤销</p>', '提示', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              await this.sendRequestHandle(params)
              done()
              instance.confirmButtonLoading = false
            } else {
              if (!instance.confirmButtonLoading) {
                done()
              }
            }
          }
        })
          .then(e => {})
          .catch(e => {})
      } else {
        params.reject_reason = this.dialogForm.rejectReason
        this.sendRequestHandle(params)
      }
    },
    async sendRequestHandle(params) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderReviewOrderReviewOperatePost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogVisible = false
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    }
  },
  beforeDestroy() {
  }
}
</script>

<style lang="scss" scoped>
.appeal-order-detail {
  margin-bottom: 60px;
  .user-info-wrapper {
    overflow-x: auto;
    display: flex;
    align-items: center;
    font-size: 14px;
    .user-img {
      width: 70px;
      margin-right: 20px;
    }
    .user-info-r {
      .float-l {
        float: left;
      }
      .info-item {
        display: flex;
        align-items: center;
        margin-right: 40px;
        padding: 5px 0;
      }
    }
  }
  .order-price-box{
    margin-top: 10px;
    display: inline-block;
    border: 1px solid #e2e8f0;
    min-width: 750px;
    .order-p-item{
      position: relative;
      padding: 5px 20px;
      min-width: 140px;
      float: left;
      text-align: center;
      color: #23282d;
      &:not(:last-child) {
        &:after{
         position: absolute;
          content: '';
          top: 20px;
          right: 0;
          bottom: 20px;
          width: 1px;
          background-color: #e2e8f0;
        }
      }
      .order-label{
        font-size: 14px;
        opacity: .7;
      }
    }
    .red{
      color: red;
    }
  }
  .appeal-item{
    margin-bottom: 10px;
    .appeal-item-label{
      font-size: #23282d;
      opacity: .7;
    }
  }
  .appeal-footer{
    width: 100%;
    padding: 15px;
    padding-right: 60px;
    background-color: #fff;
    text-align: center;
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 9999;

  }
}
</style>
<style lang="scss">
.appeal-dialog {
  border-radius: 5px;
  .el-dialog__body{
    padding: 10px 20px;
    max-height: 82vh;
    overflow-y: auto;
    .price-box{
      .price-p{
        margin-bottom: 10px;
        .price-label{
          margin-right: 15px;
        }
      }
    }
    .nutrition-item{
      // display: flex;
      // justify-content: space-around;
      // flex-wrap: wrap;
      display: inline-block;
      width: 200px;
      .nutrition-label{
        margin-bottom: 3px;
        font-size: 14px;
        letter-spacing: 1px;
        color: #23282d;
      }
    }
  }
  .dialog-content-text{
    margin-bottom: 10px;
  }
}
</style>
