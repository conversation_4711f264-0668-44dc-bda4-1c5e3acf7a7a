<template>
  <div class="PrintSetting container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="gotoAddPrintDevice('add')">新建打印设备</button-icon>
          <button-icon color="plain" type="del" @click="mulOperation('mulDel')">批量删除</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column prop="consume_addr" label="所属消费点" align="center"></el-table-column>
          <el-table-column prop="device_type_alias" label="设备类型" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="gotoAddPrintDevice('edit', scope.row.device_no)"
                >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="mulOperation('del', scope.row.device_no)"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'

export default {
  name: 'PrintSetting',
  components: {},
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        org_ids: {
          type: 'consumeSelect',
          label: '消费点',
          value: '',
          placeholder: '请选择消费点'
        },
        device_type: {
          type: 'select',
          label: '设备类型',
          value: [],
          placeholder: '请选择设备类型',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        }
      },
      selectListId: []
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    $route: {
      handler: function(route) {
        if (route.params.type === 'change') {
          this.initLoad()
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getPrintSettingList()
      this.getDeviceType()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getPrintSettingList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      // this.currentPage = 1;
      // this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取设备设置列表
    async getPrintSettingList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDevicePrintConfListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getPrintSettingList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getPrintSettingList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => { this.selectListId.push(item.device_no) })
    },
    // 批量操作 type表示类型，text是弹窗文字，num表示批量或者单个操作，data是单个操作的时候带过去的数据
    mulOperation(type, data) {
      if (!data && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let title = '提示'
      let content = ''
      switch (type) {
        case "mulDel":
          content = '确定批量删除所选打印设备吗？'
          break;
        case "del":
          content = '确定删除该打印设备吗？'
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {}
            switch (type) {
              case "mulDel":
                params.device_nos = this.selectListId
                break;
              case "del":
                params.device_nos = [data]
                break;
            }
            this.delPrintDevice(params)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 删除打印设备
    async delPrintDevice(params) {
      const res = await this.$apis.apiBackgroundDeviceDevicePrintConfBatchDeletePost(params)
      if (res.code === 0) {
        this.$message.success('删除成功')
        this.getPrintSettingList()
      } else {
        this.$message.error(res.msg)
      }
    },
    gotoAddPrintDevice(type, id) {
      this.$router.push({
        name: 'MerchantAddPrintDevice',
        params: {
          type
        },
        query: {
          type,
          id
        }
      })
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        this.searchFormSetting.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
