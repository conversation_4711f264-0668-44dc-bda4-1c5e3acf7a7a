<template>
  <!-- 表25-学校校园食品安全工作管理清单-详情 -->
  <div class="content">
    <div class="top-info" v-if="baseInfo">
      <div class="m-b-10"><span class="color-999">检查人: </span>{{ baseInfo.username || '--' }}</div>
      <div class="m-b-10"><span class="color-999">检查时间: </span>{{ baseInfo.create_time || '--' }}</div>
      <div class="m-b-10"><span class="color-999">联系电话: </span>{{ baseInfo.phone || '--' }}</div>
    </div>
    <!-- max-height="650" -->
    <custom-table
      border
      v-loading="isLoading"
      :table-data="tableData"
      :table-setting="tableSetting"
      ref="tableData"
      style="width: 100%"
      stripe
      height="calc(100vh - 300px)"
      :span-method="objectSpanMethod"
      header-row-class-name="ps-table-header-row"
    />
  </div>
</template>
<script>
import { to } from '@/utils'
import { mergeHandle, mergeRowAction } from '@/utils/table'
import { convertMockFormDataToTableList } from '@/views/merchant/supervision-and-management/baiyun-excel/utils/utils'
export default {
  name: 'Table25WorkManagementFoodSafety',
  data() {
    return {
      isLoading: false,
      tableData: [],
      baseInfo: null,
      tableSetting: [
        { label: '检查项目', key: 'inspection_items', showOverflowTooltip: true },
        { label: '检查内容 (二级指标)', key: 'inspection_contents_secondary_indicators', showOverflowTooltip: true },
        { label: '序号', key: 'serial_number' },
        { label: '检查要点 (三级指标)', key: 'key_points_inspection_third', showOverflowTooltip: true },
        { label: '检查结果', key: 'inspection_results' },
        { label: '情况备注', key: 'inspection_remarks', showOverflowTooltip: true }
      ], // 当前表格配置
      mergeOpts: {
        useKeyList: {}, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        mergeKeyList: ['inspection_items', 'inspection_contents_secondary_indicators'] // 通用的合并字段，根據值合并
      }
    }
  },
  props: {
    ledgerId: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    // 监听 ledgerId 变化
    ledgerId: {
      handler(newVal) {
        console.log(newVal, 'newVal');
        if (newVal) {
          this.getLedgerReviewDetail(newVal)
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取详情通用接口
    async getLedgerReviewDetail(id) {
      this.isLoading = true
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyLedgerReviewDetail({ id }))
      if (err) {
        this.$message.error(err.message)
        this.isLoading = false
        return
      }
      if (res.code === 0) {
        const dataJson = res.data && res.data.form
        const dataJsonParse = JSON.parse(dataJson)
        this.baseInfo = res.data
        this.tableData = convertMockFormDataToTableList(dataJsonParse)
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
      } else {
        this.$message.error(res.msg)
      }
      this.isLoading = false
    },
    // 表格合并逻辑
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 0 10px;
}
.color-999{
  color: #999;
}
.top-info {
  display: flex;
  flex-direction: column;
}
</style>
