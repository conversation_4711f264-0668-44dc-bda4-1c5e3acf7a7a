<template>
  <div class="container-wrapper" v-loading="tableLoading">
    <div class="food-sample-record">
      <h2 class="table-title">
        <span>食品留样记录本</span>
      </h2>

      <el-table :data="tableData" border style="width: 100%" :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column label="留样日期" align="center" width="200">
          <template slot-scope="scope">
            <div class="date-selector">
              <el-select v-model="scope.row.month" placeholder="月" size="mini" style="width: 80px">
                <el-option v-for="i in 12" :key="`month-${i}`" :label="`${i}月`" :value="i"></el-option>
              </el-select>
              <el-select v-model="scope.row.day" placeholder="日" size="mini" style="width: 80px">
                <el-option v-for="i in 31" :key="`day-${i}`" :label="`${i}日`" :value="i"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="餐次" prop="mealType" align="center">
          <template slot-scope="scope">
            <el-select v-model="scope.row.mealType" placeholder="选择餐次" size="mini">
              <el-option label="早餐" value="早餐"></el-option>
              <el-option label="中餐" value="中餐"></el-option>
              <el-option label="晚餐" value="晚餐"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="样品名称" prop="sampleName" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.sampleName" size="mini"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="留样重量(g)" prop="weight" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.weight" size="mini"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="留样时间" align="center" width="200">
          <template slot-scope="scope">
            <div class="time-selector">
              <el-select v-model="scope.row.sampleHour" placeholder="时" size="mini" style="width: 80px">
                <el-option v-for="i in 24" :key="`hour-${i}`" :label="`${i - 1}时`" :value="i - 1"></el-option>
              </el-select>
              <el-select v-model="scope.row.sampleMinute" placeholder="分" size="mini" style="width: 80px">
                <el-option v-for="i in 12" :key="`minute-${i * 5}`" :label="`${i * 5 - 5}分`" :value="i * 5 - 5"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="留样人签名" prop="samplePerson" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.samplePerson" size="mini"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="样品处理日期" align="center" width="260">
          <template slot-scope="scope">
            <div class="date-selector">
              <el-select v-model="scope.row.processYear" placeholder="年" size="mini" style="width: 80px">
                <el-option v-for="i in 5" :key="`year-${i}`" :label="`${2023 + i}年`" :value="2023 + i"></el-option>
              </el-select>
              <el-select v-model="scope.row.processMonth" placeholder="月" size="mini" style="width: 80px">
                <el-option v-for="i in 12" :key="`proc-month-${i}`" :label="`${i}月`" :value="i"></el-option>
              </el-select>
              <el-select v-model="scope.row.processDay" placeholder="日" size="mini" style="width: 80px">
                <el-option v-for="i in 31" :key="`proc-day-${i}`" :label="`${i}日`" :value="i"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="处理时间" align="center" width="200">
          <template slot-scope="scope">
            <div class="time-selector">
              <el-select v-model="scope.row.processHour" placeholder="时" size="mini" style="width: 80px">
                <el-option v-for="i in 24" :key="`proc-hour-${i}`" :label="`${i - 1}时`" :value="i - 1"></el-option>
              </el-select>
              <el-select v-model="scope.row.processMinute" placeholder="分" size="mini" style="width: 80px">
                <el-option v-for="i in 12" :key="`proc-minute-${i * 5}`" :label="`${i * 5 - 5}分`" :value="i * 5 - 5"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="清倒人" prop="disposePerson" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.disposePerson" size="mini"></el-input>
          </template>
        </el-table-column>
      </el-table>

      <div class="note-section">
        <div class="note-content">
          <p>备注：</p>
          <p>1、学校食堂、集体用餐配送单位、规大宗餐饮服务和量过100人的一次性聚餐要将规定量<span class="highlight">留样至少48小时</span>样品存放，留样时间<span
              class="highlight">最低不少于48小时</span>；</p>
          <p>2、留由专人填写表格后写明留样食物（菜肴）名称，提示应<span class="highlight">于2小时内</span>，时间为<span
              class="highlight">10点和14点上</span>，设有专用冰箱，储存容器（样样盒）<span class="highlight">要贴标签</span>。</p>
        </div>
      </div>

      <div class="pagination-container">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[15, 30, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShipinLiuyangManagementLedger',
  data() {
    return {
      tableData: [],
      currentPage: 1,
      pageSize: 12,
      totalItems: 12,
      tableLoading: false
    };
  },
  created() {
    this.tableLoading = true
    const data = [];

    for (let i = 1; i <= 12; i++) {
      data.push({
        id: i,
        month: null,
        day: null,
        mealType: '',
        sampleName: '',
        weight: '',
        sampleHour: null,
        sampleMinute: null,
        samplePerson: '',
        processYear: null,
        processMonth: null,
        processDay: null,
        processHour: null,
        processMinute: null,
        disposePerson: ''
      });
      this.tableData = data
      this.tableLoading = false
    }
  },
  computed: {
    displayData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.food-sample-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .date-selector,
  .time-selector {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  ::v-deep .el-input__inner,
  ::v-deep .el-select .el-input__inner {
    padding: 0 5px;
    height: 28px;
    line-height: 28px;
    text-align: center;
  }

  .note-section {
    margin-top: 20px;
    border: 1px solid #EBEEF5;
    padding: 15px;

    .note-content {
      font-size: 14px;

      p {
        margin: 5px 0;
        line-height: 1.5;
      }

      .highlight {
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
