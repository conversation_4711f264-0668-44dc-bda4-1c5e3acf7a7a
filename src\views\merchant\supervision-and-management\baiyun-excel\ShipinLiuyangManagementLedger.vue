<template>
  <!--表13 食品留样记录表 -列表 -->
  <div class="assignment-ledger-permission container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandler"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon @click="gotoPrint" color="origin">打印</button-icon>
          <el-button size="mini" @click="openPrintSetting" type="primary">报表设置</el-button>
          <button-icon color="origin" @click="handleExport" v-permission="['background_fund_supervision.ledger_food_safety.get_food_reserved_sample_ledger_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content" style="padding-bottom: 0">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          height="calc(100vh - 570px)"
          :max-height="600"
        >
          <table-column v-for="(item,index) in currentTableSetting" :key="item.key + index" :col="item">
            <template #reserved_sign="{ row }">
              <el-button type="text" size="small" @click="viewSignature(row, 'reserved_sign')">查看</el-button>
            </template>
            <template #liquidator_sign="{ row }">
              <el-button type="text" size="small" @click="viewSignature(row, 'liquidator_sign')">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[5, 10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->

      <!-- 报表设置 -->
      <print-setting
        v-if="dialogPrintVisible"
        :extraParams="{ printType: printType }"
        :tableSetting="tableSetting"
        :defaultCheckedSetting="currentTableSetting"
        :show.sync="dialogPrintVisible"
        @confirm="confirmPrintDialog"
      ></print-setting>

      <!-- 详情 -->
      <details-drawer
        :visible.sync="isShowDrawer"
        ledgerSerialNumber="24"
        :confirmShow="false"
        :showFooter="true"
        :printShow="true"
        cancelText="关 闭"
        @print="clickPrint"
      ></details-drawer>

      <!-- 查看签名 -->
      <el-dialog :title="viewDialogTitle" :visible.sync="dialogVisible" width="440px" custom-class="notice-dialog">
        <div class="content">
          <el-image
            style="width: 100%; height: 100%"
            :src="url"
            :preview-src-list="previewSrcList"
          ></el-image>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: right">
          <el-button type="primary" size="small" @click="dialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
// import dayjs from 'dayjs'
import {
  SEARCH_SETTING_FOOD_SAMPLE_RETENT_RECORD_BOOK,
  TABLE_HEAD_DATA_FOOD_SAMPLE_RETENT_RECORD_BOOK
} from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import detailsDrawer from '@/views/merchant/supervision-and-management/baiyun-excel/all-ledger-details-drawer/index.vue'
export default {
  name: 'ShipinLiuyangManagementLedger',
  mixins: [exportExcel, report],
  data() {
    return {
      isShowDrawer: false, // 详情抽屉
      dialogVisible: false,
      dialogPrintVisible: false, // 报表设置
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      printType: 'FoodReservedSample',
      viewDialogTitle: '', // 查体签名标题
      url: '',
      previewSrcList: [],
      tableSetting: deepClone(TABLE_HEAD_DATA_FOOD_SAMPLE_RETENT_RECORD_BOOK), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_FOOD_SAMPLE_RETENT_RECORD_BOOK), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_FOOD_SAMPLE_RETENT_RECORD_BOOK) // 查询表单配置
    }
  },
  created() {
    const date = this.$route.query.date
    if (date) {
      this.searchFormSetting.select_time.value = [date, date]
    }
    this.initLoad()
  },
  components: {
    detailsDrawer
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          // if (key !== 'select_time' && key !== 'select_time_operate') {
          //   const value = data[key].value
          //   if (value) {
          //     params[key] = data[key].value
          //   }
          // } else if (data[key].value && data[key].value.length > 0) {
          //   params.start_date = data[key].value[0]
          //   params.end_date = data[key].value[1]
          // }
          if (key === 'select_time_operate' && data[key].value && data[key].value.length > 0) {
            params.operate_start_date = data[key].value[0]
            params.operate_end_date = data[key].value[1]
          } else if (key === 'select_time' && data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          } else {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodReservedSampleLedger(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 查看详情
    openDetail(data) {
      this.isShowDrawer = true
    },
    // 获取提交操作员
    getOperatorUsername(row) {
      const operatorUsername = row.operator_username || ''
      const operatorMemberName = row.operator_member_name || ''
      return operatorMemberName + (operatorUsername ? `(${operatorUsername})` : '')
    },
    // 获取复核人一
    getConfirmerUsername(row) {
      const confirmerUsername = row.confirmer_username || ''
      const confirmerMemberName = row.confirmer_member_name || ''
      return confirmerMemberName + (confirmerUsername ? `(${confirmerUsername})` : '')
    },
    // 获取复核人二
    getReviewersList(reviewersList) {
      let list = []
      if (reviewersList && reviewersList.length > 0) {
        reviewersList.forEach(item => {
          list.push(item.member_name + '(' + item.username + ' )')
        })
      }
      return list.join(',')
    },
    // 打印
    clickPrint() {
      console.log('打印')
    },
    // 查看签名
    viewSignature(row, key) {
      console.log(row);
      switch (key) {
        case 'reserved_sign':
          this.viewDialogTitle = '留样人签名'
          this.url = row.sign_info[0]
          this.previewSrcList = [row.sign_info[0]]
          break;

        case 'liquidator_sign':
          this.viewDialogTitle = '清倒人签名'
          this.url = row.sign_info[1]
          this.previewSrcList = [row.sign_info[1]]
          break;
      }
      this.dialogVisible = true
    },
    // 导出
    handleExport() {
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodReservedSampleLedgerExport',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.totalCount ? this.totalCount : 10
        }
      }
      this.exportHandle(option)
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.currentTableSetting)
      tableSetting = tableSetting.filter(item => item.key !== 'sign_info_reserved' && item.key !== 'sign_info_liquidator')
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '食品留样记录表',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodReservedSampleLedger', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped></style>
