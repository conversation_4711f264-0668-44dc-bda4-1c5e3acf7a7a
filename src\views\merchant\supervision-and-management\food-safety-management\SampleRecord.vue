<template>
  <div class="AiRetentionInstrument-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="plain" @click="clickSampleTime">销样时间</button-icon>
          <button-icon color="origin" @click="clickHistoricalRecords">历史记录</button-icon>
          <button-icon color="origin" type="export" @click="gotoExport">导出</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #entryCupboard="{ row }">
              {{ row.entry_cupboard ? '是' : '否' }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-red" @click="mulOperation(row)">删除</el-button>
              <el-button type="text" size="small" @click="viewPicDetail(row.food_image)">查看</el-button>
            </template>
            <template #reservedUserName="{ row }">
              <div class="person-tag line-1" @click="viewPicDetail(row.reserved_user_image)">
                {{ row.reserved_user_name }}
              </div>
            </template>
            <template #sampleEntryUser="{ row }">
              <div class="person-tag line-1" @click="handlerShowUser(row.sample_entry_user)">
                {{ getNameByList(row.sample_entry_user) }}
              </div>
            </template>
            <template #sampleExitUser="{ row }">
              <div class="person-tag line-1" @click="handlerShowUser(row.sample_exit_user)">
                {{ getNameByList(row.sample_exit_user) }}
              </div>
            </template>
            <template #notReservedReason="{ row }">
              <div
                class="person-tag color-red line-1"
                @click="handlerShowModifyReason(row.not_reserved_reason, 'notReserved', row.id)"
              >
                {{ row.not_reserved_reason ? row.not_reserved_reason : '编辑' }}
              </div>
            </template>
            <template #notEntryReason="{ row }">
              <div
                class="person-tag color-red line-1"
                @click="handlerShowModifyReason(row.not_entry_reason, 'notEntry', row.id)"
                v-if="!row.entry_cupboard"
              >
                {{ row.not_entry_reason ? row.not_entry_reason : '编辑' }}
              </div>
            </template>
            <template #temperature="{ row }">
              {{ row.temperature ? row.temperature + '°C' : '' }}
            </template>
            <template #foodWeight="{ row }">
              {{ row.food_weight ? row.food_weight + 'g' : '' }}
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
    <!--查看大图弹窗 这里可以用一个空的图片进行替代-->
    <el-image ref="imagePre" src="" :preview-src-list="imgUrlList">
      <div slot="error"></div>
    </el-image>
    <image-view-preview
      :isshow.sync="isShowPreViewDialog"
      :title="dialogTitle"
      :picList="imgUrlList"
      @close="closePreviewDialog"
    ></image-view-preview>
    <edit-reason-dialog
      :isshow.sync="editReasonDialogVisible"
      :title="editReasonDialogTitle"
      :dialogType="editReasonDialogType"
      :content="editReasonDialogContent"
      :id="editReasonDialogId"
      @close="closeEditReasonDialog"
      @confirm="confirmEditReasonDialog"
    ></edit-reason-dialog>
    <!-- 删除的 -->
    <del-sample-record-drawer
      v-if="delDrawerVisible"
      :isshow.sync="delDrawerVisible"
      :drawerData="drawerData"
      @clickSaveDrawer="clickSaveDrawer"
    ></del-sample-record-drawer>
    <!-- 销样时间 -->
    <sample-time-drawer v-if="isShowSampleTime" :isshow.sync="isShowSampleTime"></sample-time-drawer>
    <!-- 历史记录 -->
    <history-sample-record-drawer
      v-if="isShowRecordDialog"
      :isshow.sync="isShowRecordDialog"
    ></history-sample-record-drawer>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' //
import { SEARCH_SETTING_MORNING_SAMPLE_RECORD, TABLE_HEAD_DATA_SAMPLE_RECORD } from './constants'
import ImageViewPreview from '@/components/ImageViewPreview/index.vue'
import EditReasonDialog from './compontents/EditReasonDialog.vue'
import DelSampleRecordDrawer from './compontents/DelSampleRecordDrawer.vue'
import HistorySampleRecordDrawer from './compontents/HistorySampleRecordDrawer.vue'
import SampleTimeDrawer from './compontents/SampleTimeDrawer.vue'
export default {
  name: 'MerchantSampleRecord',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_SAMPLE_RECORD), // 表格配置
      currentTableSetting: [], // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_MORNING_SAMPLE_RECORD), // 查询表单配置
      printType: 'SupervisionCanteenSampleRecord', // 类型
      imgUrlList: [], // 图片预览列表
      isShowPreViewDialog: false, // 图片预览弹窗
      dialogTitle: '', // 图片预览弹窗标题
      editReasonDialogVisible: false, // 编辑原因弹窗
      editReasonDialogTitle: '原因', // 编辑原因弹窗标题
      editReasonDialogType: '', // 编辑原因弹窗类型
      editReasonDialogContent: '', // 编辑原因弹窗内容
      editReasonDialogId: -1, // 编辑原因弹窗id
      menuList: [], // 菜单列表
      delDrawerVisible: false,
      drawerData: {},
      isShowRecordDialog: false, // 历史记录
      isShowSampleTime: false // 销样时间
    }
  },
  created() {
    this.initLoad()
  },
  components: {
    ImageViewPreview,
    EditReasonDialog,
    DelSampleRecordDrawer,
    HistorySampleRecordDrawer,
    SampleTimeDrawer
  },
  mounted() {},
  watch: {
    'searchFormSetting.select_time': {
      handler: function (val) {
        console.log('searchFormSetting.select_time', val)
        this.getMenuList()
      },
      deep: true
    },
    'searchFormSetting.org_id': {
      handler: function (val) {
        console.log('searchFormSetting.org_id', val)
        this.getMenuList()
      },
      deep: true
    }
  },
  methods: {
    initLoad() {
      this.getSupervisionCanteenSampleRecord()
      this.initPrintSetting()
      this.getDevicelist()
      this.getMenuList()
      this.foodFoodCategoryList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getSupervisionCanteenSampleRecord()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key === 'entry_cupboard') {
            params.entry_cupboard = data[key].value
          } else if (key === 'meal_type') {
            params.meal_type = data[key].value
          } else if (key === 'menu_type' && data[key].value) {
            params.menu_id = data[key].value
            let findItem = this.menuList.find(item => item.id === data[key].value)
            params.menu_type = findItem ? findItem.menu_type : ''
          } else if (key !== 'select_time') {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取留样记录
    async getSupervisionCanteenSampleRecord() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSupervisionCanteenSampleRecord()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 查看详情
    gotoDetail(data) {
      this.$router.push({
        name: 'MerchantAiRetentionInstrumentDetail',
        query: {
          id: data.id,
          data: this.$encodeQuery(data)
        }
      })
    },
    // 导出弹窗
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tabbleSetting = deepClone(this.tableSetting)
      tabbleSetting = tabbleSetting.filter(item => item.key !== 'operation')
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '留样记录',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tabbleSetting),
          current_table_setting: JSON.stringify(tabbleSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      })
      window.open(href, '_blank')
    },
    /**
     * 查看图片大图
     * @param {*} imgUrl
     */
    viewPicDetail(imgUrl) {
      this.dialogTitle = '查看详情'
      if (imgUrl && imgUrl.length > 0) {
        var picUrl = imgUrl
        // 预览图片
        this.$set(this, 'imgUrlList', [picUrl])
        this.isShowPreViewDialog = true
      } else {
        this.$message.error('亲，没有图片喔！')
      }
      console.log('viewPicDetail', imgUrl)
    },
    // 关闭图片预览
    closePreviewDialog() {
      this.isShowPreViewDialog = false
    },
    // 编辑原因
    handlerShowModifyReason(content, type, id) {
      console.log('handlerEditReason', content, type, id)
      this.editReasonDialogTitle = type === 'notEntry' ? '未入柜原因' : '未留样原因'
      this.editReasonDialogContent = content
      this.editReasonDialogType = type
      this.editReasonDialogId = id || -1
      this.editReasonDialogVisible = true
    },
    // 关闭编辑原因弹窗
    closeEditReasonDialog() {
      this.editReasonDialogVisible = false
    },
    // 确认编辑原因
    confirmEditReasonDialog() {
      this.editReasonDialogVisible = false
      this.getSupervisionCanteenSampleRecord()
    },
    // 获取列表名字
    getNameByList(list) {
      console.log('getNameByList', list)
      if (!list || list.length === 0) {
        return ''
      }
      if (typeof list === 'object') {
        let newList = []
        for (let key in list) {
          newList.push(list[key])
        }
        return newList.join('、')
      } else if (Array.isArray(list) && list.length > 0) {
        let newList = list.map(item => item.name)
        return newList.join('、')
      }
    },
    // 显示用户信息
    async handlerShowUser(list) {
      console.log('handlerShowUser', list)
      if (!list || list.length === 0) {
        return '暂未图片'
      }
      if (typeof list === 'object') {
        let newList = []
        let currentIndex = 0
        let count = Object.keys(list).length
        for (let key in list) {
          this.getUserPicInfo(key, img => {
            if (img) {
              newList.push(img)
            }
            currentIndex++
            if (currentIndex === count) {
              if (!newList || newList.length === 0) {
                return this.$message.error('图片不存在')
              }
              this.$set(this, 'imgUrlList', newList)
              this.isShowPreViewDialog = true
            }
          })
        }
      }
    },
    // 获取用户图片
    getUserPicInfo(id, callBack) {
      this.isLoading = true
      this.$apis
        .apiBackgroundFundSupervisionChannelCanteenManagementGetAccountFaceUrlPost({
          id
        })
        .then(res => {
          this.isLoading = false
          if (res && res.code === 0) {
            callBack(res.data)
          }
        })
        .catch(error => {
          this.isLoading = false
          console.log('error', error)
        })
    },
    // 获取设备列表
    getDevicelist() {
      this.isLoading = true
      this.$apis
        .apiBackgroundDeviceDeviceListPost({ device_type: 'LYG', page: 1, page_size: 9999 })
        .then(res => {
          this.isLoading = false
          if (res && res.code === 0) {
            let data = res.data || {}
            let results = data.results || []
            console.log('results', results)
            this.$set(this.searchFormSetting.entry_device_ids, 'dataList', results)
          }
        })
        .catch(error => {
          this.isLoading = false
          console.log('error', error)
        })
    },
    // 获取菜谱列表
    getMenuList() {
      this.isLoading = true
      let params = {
        start_date:
          this.searchFormSetting.select_time && this.searchFormSetting.select_time.value.length
            ? this.searchFormSetting.select_time.value[0]
            : '',
        end_date:
          this.searchFormSetting.select_time && this.searchFormSetting.select_time.value.length
            ? this.searchFormSetting.select_time.value[1]
            : ''
      }
      if (this.searchFormSetting.org_id.value) {
        Reflect.set(params, 'organization_ids', [this.searchFormSetting.org_id.value])
      }
      this.$apis
        .apiBackgroundStoreRetentionRecordMenuList(params)
        .then(res => {
          this.isLoading = false
          if (res && res.code === 0) {
            let data = res.data || []
            this.menuList = deepClone(data)
            console.log('results', data)
            data = data.map(item => {
              return {
                label: item.name,
                value: item.id
              }
            })
            this.$set(this.searchFormSetting.menu_type, 'value', '')
            this.$set(this.searchFormSetting.menu_type, 'dataList', data)
          }
        })
        .catch(error => {
          this.isLoading = false
          console.log('error', error)
        })
    },
    async foodFoodCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodCategoryAllListPost({
          page: 1,
          page_size: 9999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        this.searchFormSetting.category_ids.dataList = data.results || []
      } else {
        this.$message.error(res.msg)
      }
    },
    mulOperation(row) {
      this.drawerData = row
      this.delDrawerVisible = true
    },
    clickSaveDrawer() {
      if (this.currentPage > 1) {
        if (this.tableData.length === 1) {
          this.currentPage--
        }
      }
      this.getSupervisionCanteenSampleRecord()
    },
    clickHistoricalRecords() {
      this.isShowRecordDialog = true
    },
    clickSampleTime() {
      this.isShowSampleTime = true
    }
  }
}
</script>

<style lang="scss">
.person-tag {
  cursor: pointer;
  color: #ff9b45;
  text-decoration: underline;
  width: '130px';
}

.color-red {
  color: #fd594e !important;
}
</style>
