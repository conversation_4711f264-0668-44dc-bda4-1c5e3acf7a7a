<template>
  <div class="withdraw-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="105px" :loading="isLoading" :form-setting="searchSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" type="export" @click="handleExport" v-permission="['background_order.order_refund.report_meal_pack_order_refund_list_export']">导出</button-icon>
          <button-icon color="origin" @click="mulResumeConfirm('mul')">批量恢复</button-icon>
          <button-icon color="origin" @click="mulRefundConfirm('mul')">批量退款</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row"  @selection-change="handleOrderSelectionChange" :empty-text="isFirstSearch ? '暂无数据，请查询' : ''">
          <table-column v-for="(item, i) in currentTableSetting" :key="item.key + i" :col="item">
            <template #operation="{ row }">
              <el-button :disabled="row.refund_status!=='ORDER_REFUNDING'" @click="mulResumeConfirm('one', row)" type="text" size="small" class="ps-text">恢复供餐</el-button>
              <el-button :disabled="row.refund_status!=='ORDER_REFUNDING'" @click="mulRefundConfirm('one', row)" type="text" size="small" class="ps-red">立即退款</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
       <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="page"
        :page-size.sync="pageSize"
        :page-sizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, to, camelToUnderline, divide, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { REFUND_PENDING_ORDER_SEARCH } from './constants'
import report from '@/mixins/report' // 混入
import { confirm } from '@/utils/message'
export default {
  name: 'RefundPendingOrder',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      searchSetting: deepClone(REFUND_PENDING_ORDER_SEARCH),
      // 数据列表
      tableData: [],

      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      page: 1, // 第几页

      // 报表设置相关
      tableSetting: [
        { label: '', key: 'selection', type: 'selection' },
        { label: '创建时间', key: 'create_time', width: "150" },
        { label: '恢复就餐时间', key: 'resume_time', width: "150" },
        { label: '退款到账时间', key: 'finish_time', width: "150" },
        { label: '餐包名称', key: 'report_meal_pack_settings_name' },
        // { label: '审核编号', key: 'withdraw_fee' },
        { label: '退款单号', key: 'refund_no', width: "190" },
        { label: '订单号', key: 'trade_no', width: "190" },
        { label: '用户姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '分组', key: 'payer_group_name' },
        { label: '部门', key: 'payer_department_group_name' },
        { label: '就餐日期', key: 'report_date', width: "120" },
        { label: '退款餐段', key: 'meal_type_alias' },
        { label: '退费金额', key: 'refund_fee', type: 'money' },
        { label: '状态', key: 'refund_status_alias' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "180" }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'RefundPendingOrder',
      selectList: [],
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
    this.initPrintSetting()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        this.getRefundPendingOrderList()
      }
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.page = 1
        this.initLoad()
        this.isFirstSearch = false
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.page = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        let k = camelToUnderline(key)
        if (data[key].value !== '') {
          if (k !== 'select_time') {
            params[k] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取数据列表
    async getRefundPendingOrderList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderRefundReportMealPackOrderRefundListPost({
        ...this.formatQueryParams(this.searchSetting),
        page: this.page,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        this.tableData = []
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        // this.tableData = res.data.results.map(row => {
        //   row.withdraw_fee = divide(row.withdraw_fee)
        //   row.wallet_fee = divide(row.wallet_fee)
        //   return row
        // })
        this.totalCount = res.data.count
      } else {
        this.tableData = []
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.page = val.current
      this.pageSize = val.pageSize
      this.getRefundPendingOrderList()
    },
    handleOrderSelectionChange(val) {
      this.selectList = val.map(item => {
        return item
      })
    },
    // 恢复
    mulResumeConfirm(type, data) {
      if (type === 'one') {
        confirm({ content: `确认恢复${data.report_date} ${data.meal_type_alias}？`, title: `恢复供餐` }).then(_ => {
          this.setMealOrderResume([data.id])
        }).catch(e => {
          if (e === 'cancel') {
          }
        })
      } else if (type === 'mul') {
        let ids = []
        this.selectList.map(item => {
          if (item.refund_status === 'ORDER_REFUNDING') {
            ids.push(item.id)
          }
        })
        confirm({ content: `确认恢复供餐？共${ids.length}单`, title: `恢复供餐` }).then(_ => {
          this.setMealOrderResume(ids)
        }).catch(e => {
          if (e === 'cancel') {
          }
        })
      }
    },
    async setMealOrderResume(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderRefundReportMealOrderRefundResumePost({
        ids: params
      }))
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getRefundPendingOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 退款
    mulRefundConfirm(type, data) {
      if (type === 'one') {
        confirm({ content: `确认对${data.report_date} ${data.meal_type_alias}进行立即退款？退款后，不可在原餐包进行预定，请谨慎操作！`, title: `立即退款` }).then(_ => {
          this.setMealOrderRefund([data.trade_no])
        }).catch(e => {
          if (e === 'cancel') {
          }
        })
      } else if (type === 'mul') {
        let trades = []
        this.selectList.map(item => {
          if (item.refund_status === 'ORDER_REFUNDING') {
            trades.push(item.trade_no)
          }
        })
        confirm({ content: `确认进行立即退款？退款后，不可在原餐包进行预定，请谨慎操作！共${trades.length}单`, title: `立即退款` }).then(_ => {
          this.setMealOrderRefund(trades)
        }).catch(e => {
          if (e === 'cancel') {
          }
        })
      }
    },
    async setMealOrderRefund(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderRefundReportMealOrderRefundPost({
        trade_nos: params
      }))
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getRefundPendingOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出报表
    handleExport() {
      let ReserParams = this.formatQueryParams(this.searchSetting)
      const option = {
        type: 'RefundPendingOrder',
        url: 'apiBackgroundOrderOrderRefundReportMealPackOrderRefundListExportPost',
        params: {
          page: this.page,
          page_size: this.pageSize,
          ...ReserParams
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      // const params = this.formatQueryParams(this.searchFormSetting)
      let tableSettingList = this.tableSetting.filter(item => item.type !== "slot" && item.type !== "selection")
      let currentTableSettingList = this.currentTableSetting.filter(item => item.type !== "slot" && item.type !== "selection")
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_type: this.printType,
          print_title: '待退费订单',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundOrderOrderRefundReportMealPackOrderRefundListPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSettingList),
          current_table_setting: JSON.stringify(currentTableSettingList),
          params: JSON.stringify({
            ...this.formatQueryParams(this.searchSetting),
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.withdraw-order {
  .sumWrapper{
    padding-left: 20px;
    padding-bottom: 20px;
    ul, li { list-style: none; }
    li{
      display: inline-block;
      margin-right: 20px;
      font-size: 13px;
    }
  }
}
</style>
