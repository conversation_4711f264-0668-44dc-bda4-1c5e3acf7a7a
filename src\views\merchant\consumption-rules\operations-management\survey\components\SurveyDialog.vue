<template>
  <!-- 添加/编辑 -->
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    width="500px"
  >
    <el-form
      :model="dialogForm"
      @submit.native.prevent
      status-icon
      ref="dialogForm"
      :rules="dialogFormRules"
      label-width="80px"
      class="dialog-form SurveyDialog"
    >
      <el-form-item label="题目类型">
        <el-radio-group class="ps-radio" v-model="dialogForm.type" @change="changeType">
          <el-radio label="CHOICE">选择题</el-radio>
          <el-radio label="ANSWER">简答题</el-radio>
          <el-radio label="SCORE">评分</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="dialogForm.type === 'CHOICE'">
        <el-checkbox v-model="dialogForm.multiple_choice" class="ps-checkbox">可多选</el-checkbox>
      </el-form-item>
      <el-form-item label="题目内容" prop="title">
        <el-input v-model="dialogForm.title" max="20" class="ps-input w-300"></el-input>
      </el-form-item>
      <div v-if="dialogForm.type === 'CHOICE'" class="m-l-80 m-b-8">
        <div v-for="(optionsItem, optionsIndex) in dialogForm.options_data" :key="optionsIndex" class="options-wrap">
          <el-form-item class="form-inline option-form" :label="optionsItem.options" :prop="'options_data.'+optionsIndex+'.content'" :rules="dialogFormRules.content" label-width="35px">
            <el-input v-model="optionsItem.content" max="20" class="ps-input w-300"></el-input>
            <el-button v-if="dialogForm.options_data.length > 2" type="text" size="small" class="ps-red m-l-10" @click="delOptions(optionsIndex)">删除</el-button>
          </el-form-item>
        </div>
        <el-button v-if="dialogForm.options_data.length < 7" type="text" size="small" @click="addoption">添加选项</el-button>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { deepClone } from '@/utils'
const keyList = ['A', 'B', 'C', 'D', 'E', 'F', 'G']
export default {
  name: 'SurveyDialog',
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    isshow: Boolean,
    confirm: Function
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      dialogForm: {
        type: 'CHOICE',
        title: '',
        multiple_choice: false,
        options_data: [{
          options: 'A',
          content: ''
        }, {
          options: 'B',
          content: ''
        }]
      },
      dialogFormRules: {
        title: [{ required: true, message: '内容不能为空', trigger: "blur" }],
        content: [{ required: true, message: '内容不能为空', trigger: "blur" }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.dialogForm = {
          type: 'CHOICE',
          title: '',
          multiple_choice: false,
          options_data: [{
            options: 'A',
            content: ''
          }, {
            options: 'B',
            content: ''
          }]
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
    },
    clickConfirmHandle() {
      this.$refs.dialogForm.validate(valid => {
        if (valid) {
          let data = {
            type: this.dialogForm.type,
            content: this.dialogForm.title,
            multiple_choice: this.dialogForm.multiple_choice
          }
          if (this.dialogForm.type === 'CHOICE') {
            data.options_data = deepClone(this.dialogForm.options_data)
          }
          this.confirm(data)
        } else {
        }
      })
    },
    addoption() {
      let lastOptKey = this.dialogForm.options_data[this.dialogForm.options_data.length - 1].options
      let key = keyList[keyList.indexOf(lastOptKey) + 1]
      this.dialogForm.options_data.push({
        options: key,
        content: ''
      })
    },
    delOptions(index) {
      this.dialogForm.options_data.splice(index, 1)
      this.dialogForm.options_data.map((opt, optindex) => {
        opt.options = keyList[optindex]
      })
    },
    changeType() {
      this.$refs.dialogForm.resetFields()
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.dialogForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.SurveyDialog {
  .option-form{
    margin-bottom: 18px;
  }
}
</style>
