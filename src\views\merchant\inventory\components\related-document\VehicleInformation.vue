<template>
  <div class="vehicleInformation-box">
    <div v-for="(vehicle, index) in vehicleList" :key="index" class="vehicle-item">
      <div class="form-item">
        <span class="form-label">车辆类型：</span>{{ vehicle.car_type_alias }}
      </div>
      <div class="form-item">
        <span class="form-label">车牌号：</span>{{ vehicle.plate_number }}
      </div>
      <div class="form-item">
        <span class="form-label">车辆图片：</span>
        <div class="form-img-box">
          <el-image
            v-for="(img, k) in vehicle.car_img"
            :key="img"
            class="detault-img m-r-6 pointer"
            :src="img"
            fit="contain"
            @click="clickViewerHandler(vehicle.car_img, k)"
          ></el-image>
        </div>
      </div>
      <el-divider v-if="showDivider"></el-divider>
    </div>
    <image-viewer v-model="showViewer" :initial-index="imgIndex" :z-index="3000" :on-close="closeViewer" :preview-src-list="previewSrcList"/>
  </div>
</template>

<script>
// 车辆信息
export default {
  name: 'VehicleInformation',
  props: {
    vehicleList: {
      type: Array,
      default: () => []
    },
    showDivider: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      imgIndex: 0,
      previewSrcList: [],
      showViewer: false
    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {
    clickViewerHandler(row, index) {
      // don't show viewer when preview is false
      this.previewSrcList = row || []
      this.imgIndex = index
      this.showViewer = true;
    },
    closeViewer() {
      this.showViewer = false;
    }
  }
};
</script>

<style scoped lang="scss">
.vehicleInformation-box {
  .form-item {
    display: flex;
    font-size: 14px;
    line-height: 30px;
  }
  .form-img-box {
    flex: 1;
  }
  .detault-img {
    width: 160px;
    height: 92px;
    border: 1px dashed #e1e1e1;
    border-radius: 2px;
  }
}
</style>
