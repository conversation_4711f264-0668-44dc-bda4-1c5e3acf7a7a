<template>
  <el-drawer :visible.sync="visible" :title="title" :loading.sync="isLoading" @close="clickCancleHandle" size="720px"
    :wrapperClosable="false" class="ps-el-drawer">
    <div v-loading="isLoading" class="related-document p-t-20 p-l-20 p-r-20">
      <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
        <el-radio-button v-for="tab in tabTypeList" :key="tab.value" :label="tab.value">{{ tab.label }}</el-radio-button>
      </el-radio-group>
      <div class="info-wrap">
        <div class="title">基本信息</div>
        <div class="form-item"><span class="form-label">单据编号：</span>{{ infoData.trade_no }}</div>
        <div class="form-item"><span class="form-label">创建时间：</span>{{ infoData.create_time }}</div>

        <div v-if="tabType === 'deliveryOrder'" class="form-item"><span class="form-label">开始配送时间：</span>{{ infoData.delivery_date }}</div>
        <div v-if="tabType === 'deliveryOrder'" class="form-item"><span class="form-label">预计送达时间：</span>{{ infoData.expect_arrival_date }}</div>
        <!-- <div v-if="tabType === 'deliveryOrder'" class="form-item"><span class="form-label">送达时间：</span>{{  }}</div> -->

        <div v-if="tabType === 'receiptOrder'" class="form-item"><span class="form-label">收货时间：</span>{{ infoData.create_time }}</div>

        <div v-if="tabType === 'settlementOrder'" class="form-item"><span class="form-label">结算时间：</span>{{ infoData.expect_arrival_date }}</div>
        <div v-if="tabType === 'settlementOrder'" class="form-item"><span class="form-label">结算状态：</span>{{ infoData.expect_arrival_date }}</div>
        <div v-if="tabType === 'settlementOrder'" class="form-item"><span class="form-label">结算金额：</span>{{ infoData.expect_arrival_date }}</div>
        <div v-if="tabType === 'settlementOrder'" class="form-item"><span class="form-label">审批人：</span>{{ infoData.expect_arrival_date }}</div>
        <div v-if="tabType === 'settlementOrder'" class="form-item"><span class="form-label">审批时间：</span>{{ infoData.expect_arrival_date }}</div>
      </div>
      <div class="info-wrap" v-if="tabType !== 'settlementOrder'">
        <div class="title">配送信息</div>
        <div class="form-item"><span class="form-label">配送温度：</span>{{ infoData.delivery_temperature }}°c</div>
        <div v-if="tabType === 'receiptOrder'" class="form-item">
          <span class="form-label">单据凭证：</span>
          <div v-if="infoData.purchase_certificate && infoData.purchase_certificate.length > 0"  class="form-img-box">
            <el-image
              v-for="(img, k) in infoData.purchase_certificate"
              :key="img"
              class="detault-img m-r-6 pointer"
              :src="img"
              fit="contain"
              :preview-src-list="infoData.purchase_certificate"
              :initial-index="k"
            ></el-image>
          </div>
        </div>
      </div>
      <!-- 司机信息 -->
      <DriverInformation v-if="tabType !== 'settlementOrder'" :driver-list="infoData.driver_info"></DriverInformation>
      <!-- 车辆信息 -->
      <VehicleInformation v-if="tabType !== 'settlementOrder'" :vehicle-list="infoData.vehicle_info"></VehicleInformation>

      <div class="info-wrap" v-if="tabType === 'receiptOrder'">
        <div class="title">核验人员信息</div>
        <ul v-if="infoData.check_accounts && infoData.check_accounts.length > 0" class="check-box flex flex-wrap">
          <li v-for="(checkItem, index) in infoData.check_accounts" :key="index" class="check-item m-r-20">
            <div class="flex">
              <span>{{ checkItem.role_name }}：</span>
              <span>{{ checkItem.username }}</span>
            </div>
            <div class="flex">
              <span>收货实况人脸：</span>
              <span>
                <el-image
                  :src="checkItem.face_url"
                  fit="contain"
                  class="face-img"
                  :preview-src-list="[checkItem.face_url]"
                ></el-image>
              </span>
            </div>
          </li>
        </ul>
        <div v-else>无</div>
      </div>

      <div class="info-wrap" v-if="tabType !== 'settlementOrder'">
        <div class="title">物资信息</div>
        <div>合计金额：￥{{ totalPrice }}</div>
        <el-table
          :data="infoData.ingredient_data"
          ref="tableData"
          style="width: 100%"
          stripe
          size="mini"
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #img="{ row }">
              <el-button v-if="row.check_file && row.check_file.length > 0" type="text" size="small" class="ps-text" @click="clickViewerHandler(row)">查看</el-button>
              <span v-else>--</span>
            </template>
          </table-column>
        </el-table>
      </div>
    </div>
    <div class="ps-el-drawer-footer p-l-20">
      <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">
        取消
      </el-button>
      <el-button class="ps-btn" type="primary" @click="clickConfirmHandle" v-loading="isLoading">
        确定
      </el-button>
    </div>
    <image-viewer
      v-model="showViewer"
      :initial-index="imgIndex"
      :z-index="3000"
      :on-close="closeViewer"
      :preview-src-list="previewSrcList"
    />
  </el-drawer>
  <!-- end -->
</template>

<script>
import { divide } from '@/utils'
import VehicleInformation from './VehicleInformation'
import DriverInformation from './DriverInformation'
import NP from 'number-precision'

export default {
  name: 'RelatedDocument',
  props: {
    loading: Boolean,
    isshow: Boolean,
    title: {
      type: String,
      default: '关联单据'
    },
    confirm: Function,
    id: [Number, String]
  },
  components: {
    VehicleInformation,
    DriverInformation
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      tabType: 'deliveryOrder',
      tabTypeList: [
        {
          label: '配送单',
          value: 'deliveryOrder'
        },
        {
          label: '收货单',
          value: 'receiptOrder'
        },
        {
          label: '结算单',
          value: 'settlementOrder'
        }
      ],
      tableData: [],
      tableSettings: [
        { label: '物资名称', key: 'name' },
        { label: '收货数量', key: 'name1' },
        { label: '单价', key: 'name2' },
        { label: '合计金额', key: 'name3' },
        { label: '保质期', key: 'name4' }
      ],
      vehicleList: [],
      infoData: {}, // dd
      totalPrice: 0, // 合计金额
      imgIndex: 0,
      previewSrcList: [],
      showViewer: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    isshow(val) {
      if (val) {
        this.changeTabHandle(this.tabType)
      }
    }
  },
  mounted() {

  },
  methods: {
    changeTabHandle(val) {
      switch (val) {
        case 'deliveryOrder':
          this.tableSettings = [
            { label: '物资名称', key: 'materials_name' },
            { label: '配送数量', key: 'purchase_count' },
            { label: '单价', key: 'unit_price' },
            { label: '合计金额', key: 'total_price' },
            { label: '保质期', key: 'valid_date' }
          ]
          this.getDeliveryNoteInfo()
          break;
        case 'receiptOrder':
          this.tableSettings = [
            { label: '物资名称', key: 'materials_name' },
            { label: '收货数量', key: 'purchase_count' },
            { label: '单价', key: 'unit_price' },
            { label: '合计金额', key: 'total_price' },
            { label: '保质期', key: 'valid_date' },
            { label: '核验附件', key: 'check_file', type: 'slot', slotName: "img" }
          ]
          this.getReceivingNoteInfo()
          break;
        case 'settlementOrder':
          this.getFinalStatementInfo()
          break;
      }
    },
    clickConfirmHandle() {
      this.visible = false
    },
    clickCancleHandle() {
      this.visible = false
    },
    initLoad() {
      // this.getDeliveryNoteInfo()
      // this.getFinalStatementInfo()
      // this.getReceivingNoteInfo()
    },
    async getDeliveryNoteInfo() {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpPurchaseInfoPurchaseDeliveryNoteInfoPost({
        id: this.id
      }))
      this.isLoading = false
      this.infoData = {}
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        this.totalPrice = 0
        // eslint-disable-next-line camelcase
        if (data?.ingredient_data) {
          data.ingredient_data = data.ingredient_data.map((v) => {
            this.totalPrice = NP.plus(this.totalPrice, v.total_price)
            v.purchase_count = (v.purchase_count || 0) + (v.purchase_unit || '')
            v.unit_price = "￥" + divide(v.unit_price) + (v.purchase_unit ? ("/" + v.purchase_unit) : '')
            v.total_price = "￥" + divide(v.total_price)
            v.valid_date = v.start_valid_date + "-" + v.end_valid_date
            return v
          })
        }
        this.totalPrice = divide(this.totalPrice)
        this.infoData = data || {}
      } else {
        this.$message.error(res.msg)
      }
    },
    async getReceivingNoteInfo() {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpPurchaseInfoPurchaseReceivingNoteInfoPost({
        id: this.id
      }))
      this.isLoading = false
      this.infoData = {}
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        this.totalPrice = 0
        // eslint-disable-next-line camelcase
        if (data?.ingredient_data) {
          data.ingredient_data = data.ingredient_data.map((v) => {
            this.totalPrice = NP.plus(this.totalPrice, v.total_price)
            v.purchase_count = (v.receive_count || 0) + (v.purchase_unit || '')
            v.unit_price = "￥" + divide(v.unit_price) + (v.purchase_unit ? ("/" + v.purchase_unit) : '')
            v.total_price = "￥" + divide(v.total_price)
            v.valid_date = v.start_valid_date + "-" + v.end_valid_date
            return v
          })
        }
        this.totalPrice = divide(this.totalPrice)
        this.infoData = data || {}
      } else {
        this.$message.error(res.msg)
      }
    },
    async getFinalStatementInfo() {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpPurchaseInfoPurchaseFinalStatementInfoPost({
        id: this.id
      }))
      this.isLoading = false
      this.infoData = {}
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.infoData = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    clickViewerHandler(row, index) {
      // don't show viewer when preview is false
      this.previewSrcList = row.check_file || []
      this.imgIndex = index || 0
      this.showViewer = true;
    },
    closeViewer() {
      this.showViewer = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.related-document{
  .flex{
    display: flex;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .info-wrap{
    font-size: 14px;
    line-height: 30px;
    .title{
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0 10px;
    }
    .form-item {
      display: flex;
      font-size: 14px;
      line-height: 30px;
    }
    .form-img-box {
      flex: 1;
    }
    .detault-img {
      width: 160px;
      height: 92px;
      border: 1px dashed #e1e1e1;
      border-radius: 2px;
    }
    .face-img {
      margin-top: 10px;
      width: 80px;
      height: 100px;
    }
  }
}
</style>
