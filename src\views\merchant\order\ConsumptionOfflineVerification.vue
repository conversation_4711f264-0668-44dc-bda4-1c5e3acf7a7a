<template>
  <div class="consumption-failure-offline container-wrapper">
      <search-form ref="searchRef" label-width="105px" :loading="isLoading" :form-setting="searchSetting" @search="searchHandle" :autoSearch="false" @reset="resetHandler"></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表<span class="m-l-40 ps-text font-size-14">当前异常订单表记录的数据：离线上传核销失败订单</span></div>
          <div class="align-r">
            <!-- <button-icon color="plain" @click="mulRefundHandler" v-permission="['background_order.order_offline.bulk_order_pay']">批量核销</button-icon> -->
            <button-icon  @click="mulRefundHandler" v-permission="['background_order.order_offline.cancel_bulk_order_pay']" :disabled="!orderIds || orderIds.length === 0">批量取消</button-icon>
            <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_order.order_offline.list_export']">导出</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
            <!-- <button-icon color="plain" type="export" @click="handleExport" style="margin-right:20px">
              导出报表
            </button-icon>
            <el-button size="small">全部重新扣款</el-button> -->
          </div>
        </div>
        <div class="table-content">
          <!-- table start -->
          <el-table  style="width: 100%" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row" use-virtual :row-height="30"  :big-data-checkbox="true" :max-height="800"
          class="table-data" @selection-change="handleSelectionChange" row-key="id" :empty-text="isFirstSearch ? '暂无数据，请查询' : ''">
            <el-table-column type="selection" width="50" class-name="ps-checkbox" :selectable="selectDisabled" ></el-table-column>
            <el-table-column :index="index" v-for="(item,index) in currentTableSetting" :key="index"
            :prop="item.key" :label="item.label" :width="item.width" align="center">
            <template slot-scope="scope">
              <span v-if="item.type === 'index'">{{ (scope.$index+1)+(currentPage-1)*pageSize }}</span>
              <span v-if="item.type === 'money'"> ¥{{ scope.row[item.key] | formatMoney }}</span>
              <span v-if="!item.type">{{ scope.row[item.key] }}</span>
              <span v-if="item.type === 'slot' && item.slotName === 'operation'">
                <!-- <el-button type="text" size="small" @click="gotoDetail(scope.row)">详情</el-button> -->
                <!-- <el-button type="text" size="small" @click="clickBtnHandle('repay', scope.row)" v-permission="['background_order.order_offline.order_pay']">重新扣款</el-button> -->
                <!-- <el-button type="text" size="small" @click="clickBtnHandle('origin', scope.row)" v-permission="['background_order.order_offline.order_pay']">原价扣款</el-button> -->
                <el-button type="text" size="small" class="ps-text" @click="clickBtnHandle('cancel', scope.row)" v-permission="['background_order.order_offline.cancel_bulk_order_pay']" :disabled="scope.row.order_status">取消订单</el-button>
              </span>
            </template>
            </el-table-column>
          </el-table>
          <!-- table end -->
        </div>
        <!-- <table-statistics v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText" :statistics="collect" /> -->
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 200, 500, 1000, 2000]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
      <print-setting
        v-if="dialogPrintVisible"
        :extraParams="{ printType: printType }"
        :tableSetting="tableSetting"
        :defaultCheckedSetting="currentTableSetting"
        :show.sync="dialogPrintVisible"
        @confirm="confirmPrintDialog"
      ></print-setting>
      <!-- 扣款弹窗 -->
      <el-dialog
          width="30%"
          custom-class="el-dialog__body"
          :title="dialogType == 'refund'?'批量扣款':'提示'"
          :visible.sync="isShowRefundDialog"
          :close-on-press-escape="false"
          :close-on-click-modal="false"
          lock-scroll
          append-to-body
          :show-close="false"
          @close="dialogClose"
        >
        <!--选择扣款模式-->
        <div v-if="dialogType == 'refund'" class="flex-btn-center">
        <el-radio text-color="#FF9B45" v-model="flagRefundMoney" label="1">重新扣款</el-radio>
        <el-radio text-color="#FF9B45" v-model="flagRefundMoney" label="2">原价扣款</el-radio>
        </div>
        <!--扣款中-->
        <div v-else-if="dialogType == 'refunding'" class="">
          <div>后台执行批量扣款中，请稍后...</div>
          <el-progress :text-inside="true" :stroke-width="24" :percentage="percentage" status="success"></el-progress>
        </div>
        <!--扣款结果-->
        <div v-else>
          <p style="color:black;text-align: center;">扣款成功：{{numberRefundSuccess}}单</p>
          <p style="color:#E0364C;text-align: center;">扣款失败：{{numberRefundFail}}单</p>
        </div>
        <div slot="footer" class="flex-btn-center">
          <el-button size="small" class="ps-cancel-btn" @click="handlerCancleRefund"  :disabled="isCancleBtnDisable" v-loading="dialogCancleLoading" v-if="dialogType != 'success'">取 消</el-button>
          <el-button size="small" class="ps-btn" @click="handlerConfirmRefund" v-loading="dialogLoading" type="primary" :disabled="isConfirmBtnDisable" v-if="dialogType == 'refund'">确认扣款</el-button>
          <el-button size="small" class="ps-btn" @click="handlerConfirmClose" v-loading="dialogLoading" type="primary" :disabled="isConfirmBtnDisable" v-if="dialogType == 'success'">关闭</el-button>
        </div>
      </el-dialog>
  </div>
</template>

<script>
import { debounce, to, deepClone } from '@/utils'
import { MEALTYPE, PICKEROPTIONS, PAYMENTSTATE } from './component/order'
import { CONSUMPTION_OFFLINE_VERIFICATION_SEARCH_SETTING, CONSUMPTION_OFFLINE_VERIFICATION_TABLE_SETTING } from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { mapState } from 'vuex'
export default {
  name: 'ConsumptionOfflineVerification',
  mixins: [report, exportExcel],
  components: {
  },
  data() {
    return {
      meal_type: deepClone(MEALTYPE), // 餐段
      paymentstate: PAYMENTSTATE, // 支付状态
      searchSetting: deepClone(CONSUMPTION_OFFLINE_VERIFICATION_SEARCH_SETTING),
      pickerOptions: PICKEROPTIONS,
      tableData: [],
      // 报表设置相关
      tableSetting: deepClone(CONSUMPTION_OFFLINE_VERIFICATION_TABLE_SETTING),
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'OrderVerificationAbnormal',
      columns: [], // 动态获取组织的层级
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      totalData: {
        total_count: 0,
        total_amount: 0,
        total_pay_amount: 0
      },
      orgKey: [], // 区分下组织和其它
      collect: [ // 统计
        { key: 'total_count', value: 0, label: '合计笔数' },
        { key: 'total_amount', value: 0, label: '合计订单金额：￥', type: 'money' },
        { key: 'total_pay_amount', value: 0, label: '合计实收金额：￥', type: 'money' }
      ],
      elementLoadingText: "数据正在加载，请耐心等待...",
      isLoadingCollect: false,
      orderIds: [], // 批量选择的订单
      flagRefundMoney: '1', // 重新扣款还是原价扣款
      dialogType: 'refunding', // 弹窗类型
      numberRefundSuccess: 0, // 扣款成功笔数
      numberRefundFail: 0, // 扣款失败笔数
      isShowRefundDialog: false, // 弹窗是否显示
      dialogLoading: false, // 弹窗按钮loading
      timeCount: 3, // 关闭倒计时
      timeThread: null, // 倒计时线程
      isCancleBtnDisable: false,
      isConfirmBtnDisable: false,
      timer: null, // 重复轮训线程
      percentage: 0, // 进度，最高百分白
      dialogCancleLoading: false, // 取消loading
      queryId: null, // 线程ID
      isFirstSearch: true
    }
  },
  async created() {
    // await this.getLevelNameList()
    this.isLoading = true
    await this.initPrintSetting()
    this.isLoading = false
  },
  mounted() {
  },
  computed: {
    ...mapState('navTabs', ['navMenuList'])
  },
  destroyed() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    initLoad() {
      this.getFailureOrderList()
      // this.getFailureOrderStatisticalDataList()  // 没有汇总
    },
    // 重置
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = false
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.initLoad()
        this.isFirstSearch = false
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 拉取核销失败订单
    async getFailureOrderList() {
      this.isLoading = true
      let params = this.formatQueryParams(this.searchSetting)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderVerificationAbnormalListPost({
        ...params,
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count || 0
        this.tableData = data.results || []
        console.log(this.tableData)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表合计数据 后端说先不用了
    async getFailureOrderStatisticalDataList() {
      const params = this.formatQueryParams(this.searchSetting)
      this.isLoadingCollect = true
      const res = await this.$apis.apiBackgroundOrderOrderVerificationAbnormalListCollectPost(params)
      if (res.code === 0) {
        this.elementLoadingText = '数据正在加载，请耐心等待...'
        this.isLoadingCollect = false
        console.log(res)
        // 统计
        this.collect.forEach(item => {
          for (let i in res.data) {
            if (item.key === i) {
              item.value = res.data[i]
            }
          }
        })
      } else {
        this.elementLoadingText = '汇总数据加载失败，请重试。'
        this.$message.error('汇总数据加载失败，请重试。')
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getFailureOrderList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getFailureOrderList()
    },
    // 导出报表
    handleExport() {},
    gotoDetail(row) {
      this.$router.push({
        name: 'MerchantConsumptionFailureDetail',
        query: {
          id: row.p_id,
          trade_no: row.trade_no
          // data: JSON.stringify(row)
        }
      })
    },
    // 列表序号 补0
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundGetlevelNameListPost()
      let list = res.data.map(v => {
        this.orgKey.push(v.level)
        return {
          label: v.name,
          key: 'org_' + v.level
        }
      })
      this.tableSetting.splice(6, 0, ...list)
      console.log(this.tableSetting)
      this.initPrintSetting()
    },
    // 按钮事件
    clickBtnHandle(type, row, refuseType) {
      let tipsText = `确定${type === 'repay' ? '重新发起扣款吗' : type === 'cancel' ? '取消订单吗' : '原价扣款'}？`
      this.$confirm(tipsText, '提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-origin-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            if (type === 'repay') {
              await this.repayOrder(row.p_id)
            }
            if (type === 'cancel') {
              let id = refuseType && refuseType === 'multi' ? this.orderIds : row.id
              await this.closeOrder(id, refuseType || 'single')
            }
            if (type === 'origin') {
              await this.repayOrder(row.p_id, true) // 第二个参数为是否原价扣款
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 重新发起订单支付, isOrigin表示是否原价扣款
    async repayOrder(id, isOrigin) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      let params = {
        order_payment_id: id
      }
      if (isOrigin) params.is_original_price = isOrigin
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineOrderPayPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getFailureOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重新发起订单支付
    async closeOrder(id, type) {
      let idsList = type === 'single' ? [id] : id
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderVerificationAbnormalOrderClosePost({
        ids: idsList
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getFailureOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log("handleSelectionChange", val);
      var selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map((item, i) => {
        selectListId.push(item.id)
      })
      this.orderIds = deepClone(selectListId)
    },
    // 批量扣款
    mulRefundHandler() {
      if (this.dialogLoading) return
      if (!this.orderIds.length) return this.$message.error('请选择要批量处理的订单！')
      this.clickBtnHandle('cancel', {}, 'multi')
      console.log("mulRefundHandler", this.orderIds);
    },
    // 扣款成功
    async handlerConfirmRefund() {
      console.log("handlerConfirmRefund");
      this.dialogLoading = true
      var params = {
        order_payment_ids: this.orderIds,
        is_original_price: this.flagRefundMoney === '2'
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineBulkOrderPayPost(params))
      this.dialogLoading = false
      if (err) {
        this.dialogType = "refund"
        this.$message.error(err.message || '扣款失败')
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        this.queryId = data.query_id || ''
        this.startQueryHandle()
      } else {
        this.startQueryHandle()
        this.$message.error(res.msg || '扣款失败')
      }
    },
    // 设置倒数 不要了这个产品说不要倒计时关闭弹窗了
    setTimeBackward() {
      var that = this
      this.timeThread = setInterval(() => {
        that.timeCount--
        if (that.timeCount === 0) {
          that.isShowRefundDialog = false
          clearInterval(this.timeThread)
          that.timeThread = null
          that.timeCount = 3
          that.currentPage = 1
          that.getFailureOrderList()
        }
      }, 1000);
    },
    // 弹窗关闭
    dialogClose() {
      console.log("dialogClose");
      // this.handlerConfirmClose()
      this.handlerCancleRefund()
    },
    // 开始轮询
    startQueryHandle() {
      this.percentage = 0
      this.dialogType = 'refunding'
      this.getResultUrl(this.queryId)
      this.timer = setInterval(() => {
        this.getResultUrl(this.queryId)
      }, 3000)
    },
    // 轮询查看结果
    async getResultUrl(queryId) {
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineBulkOrderPayTaskQueryPost({
        query_id: queryId
      }))
      if (err) {
        this.showErrorMsg(res.msg)
        return
      }
      if (res.code === 0) {
        this.percentage = res.data.progress || 0
        if (res.data && res.data.status === 'success') {
          this.numberRefundSuccess = res.data.success || 0
          this.numberRefundFail = res.data.fail || 0
          clearInterval(this.timer)
          setTimeout(() => {
            this.dialogType = "success"
          }, 1000);
        } else if (res.data.status === 'failure') {
          this.showErrorMsg(res.msg)
        }
      } else {
        this.showErrorMsg(res.msg)
      }
    },
    // 显示错误信息
    showErrorMsg(msg) {
      this.dialogType = 'refund'
      this.$message.error(msg)
      clearInterval(this.timer)
    },
    // 取消批量扣款
    async handlerCancleRefund() {
      if (this.dialogType === 'refunding') {
        clearInterval(this.timer)
        this.timer = null
        this.dialogCancleLoading = true
        const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineCancelBulkOrderPayPost(({
          query_id: this.queryId
        })))
        this.dialogCancleLoading = false
        if (err) {
          this.showErrorMsg(res.msg)
          return
        }
        if (res && res.code === 0) {
          this.numberRefundSuccess = res.data.success || 0
          this.numberRefundFail = res.data.fail || 0
          this.dialogType = "success"
        } else {
          this.$message.error(res.msg || '取消失败')
        }
      } else if (this.dialogType === 'refund') {
        this.isShowRefundDialog = false
      } else {
        this.handlerConfirmClose()
      }
    },
    // 关闭弹窗
    handlerConfirmClose() {
      this.isShowRefundDialog = false
      this.dialogType = 'refund'
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.getFailureOrderList()
    },
    // 导出
    gotoExport() {
      let params = this.formatQueryParams(this.searchSetting)
      const option = {
        url: 'apiBackgroundOrderOrderVerificationAbnormalListExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    // 列表里面是否可以批量选择
    selectDisabled(row) {
      return row.order_status === 'WAIT_CONFIRM'
    }
  }
}
</script>

<style lang="scss" scoped>
.consumption-failure-offline {

  .table-data{
    ::v-deep .el-table__row td{
      border-bottom: 1px solid #EBEEF5;
      .umy-table-beyond{
      text-align: center;
    }
    }
    ::v-deep .el-table__fixed-right {
      height :100% !important;
    }
    ::v-deep .el-table__fixed {
      height :100% !important;
    }
  }
  .el-input {
    width: 180px;
  }
  .el-select {
    width: 180px;
  }
  //数据报表头部
  .table-wrapper {
    .table-header {
      display: flex;
      justify-content: space-between;
      .el-button {
        margin-right: 10px;
        &:nth-of-type(3) {
          margin-right: 20px;
        }
      }
    }
  }
  .el-table {
    text-align: center;
    font-size: 12px;
  }
  .total {
    // padding: 0 20px;
    margin-top: 20px;
    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 14px;
    }
  }
  .el-loading-wrapp{
    .el-loading-spinner{
      margin-top:0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
::v-deep .el-dialog__body {
  padding: 10px 20px  10px 20px!important;
  position: relative;
  border-radius: 10px;
}
.flex-btn-center {
  display: flex;
  justify-content: center;
}
.time-count {
   text-align:right;
}
::v-deep .ps-table-header-row{
  height: 60px !important;
}
.close-icon {
  position: absolute;
  top:-30px;
  right: 20px;
}

</style>
