import * as dayjs from 'dayjs'
import { URL_MANUFACTURER } from '../user-center/constants/cardManageConstants'
export const INITNUMBER = function(val) {
  let str = ''
  let arr = val.toString().split('')
  if (val >= 1000000 && val <= 9999999) {
    arr.splice(1, 0, ',')
    arr.splice(5, 0, ',')
    str = arr.join('')
  } else if (val >= 100000) {
    arr.splice(3, 0, ',')
    str = arr.join('')
  } else if (val >= 10000) {
    arr.splice(2, 0, ',')
    str = arr.join('')
  } else if (val >= 1000) {
    arr.splice(1, 0, ',')
    str = arr.join('')
  } else if (val < 1000 && val >= 0) {
    return val
  }
  return str
}
export const RECENTSEVENTDAYARR = function() {
  let arr2 = [dayjs().format('YYYY-MM-DD')]
  for (let i = 1; i < 7; i++) {
    arr2.unshift(
      dayjs()
        .subtract(i, 'day')
        .format('YYYY-MM-DD')
    )
  }
  return arr2
}
export const TIMEARR = [
  {
    value: 'today',
    label: '今天'
  },
  {
    value: 'week',
    label: '最近7天'
  },
  {
    value: 'month',
    label: '最近30天'
  }
]
// 拓展功能
export const EXPAND = [
  { id: 9, label: '洗衣管理', disabled: true, src: require('@/assets/img/washing_system_orange.png'), type: "ZK_LAUNDRY" },
  { id: 6, label: '车辆管理', disabled: true, src: require('@/assets/img/icon-car.png'), url: URL_MANUFACTURER, type: "car_management" },
  { id: 1, label: '考勤管理', disabled: true, src: require('@/assets/img/icon1.png') },
  { id: 2, label: '水控管理', disabled: true, src: require('@/assets/img/icon2.png') },
  { id: 13, label: '智慧校园', disabled: true, src: require('@/assets/img/icon13.png'), type: 'YDZHXY' },
  { id: 3, label: '电控管理', disabled: true, src: require('@/assets/img/icon3.png') },
  { id: 10, label: '督贝管理', disabled: true, src: require('@/assets/img/icon_dbgl.png'), type: "DoBay" },
  { id: 11, label: '智慧门店', disabled: true, src: require('@/assets/img/icon_zhmd.png'), type: "DoBay" },
  { id: 4, label: '监控管理', disabled: true, src: require('@/assets/img/icon4.png') },
  { id: 5, label: '门禁管理', disabled: true, src: require('@/assets/img/icon5.png') }
]
// 消息公告
export const NOTICE = [
  { id: 1, label: '大声的呐喊大卡司打开大声的呐喊大卡司打开', time: '2022-03-03 15:08' },
  { id: 2, label: '大声的呐喊大卡司打开', time: '2022-03-01 12:20' },
  { id: 3, label: '大声的呐喊大卡司打开', time: '2022-02-28 10:00' },
  { id: 4, label: '大声的呐喊大卡司打开', time: '2022-02-01 12:15' }
]

export const NAVTYPE = [
  { id: 1, fee: 89536, label: '营业额' },
  { id: 2, fee: 73124, label: '实收金额' },
  { id: 3, fee: 3560, label: '消费订单笔数' },
  { id: 4, fee: 26707.5, label: '充值金额' },
  { id: 5, fee: 3006.58, label: '退款金额' }
]

export const OPTIONLINE = {
  xAxis: {
    type: 'category',
    data: RECENTSEVENTDAYARR(), // 默认显示最近7天
    axisLine: { show: false },
    axisTick: {
      show: false
    },
    splitLine: {
      show: false
    },
    axisLabel: {
      textStyle: {
        fontSize: 14,
        color: '#666',
        lineHeight: 50
      }
      // interval: function(index) {} // 横坐标之间 间隙
    }
  },
  yAxis: {
    type: 'value',
    axisTick: {
      show: false
    },
    axisLine: {
      show: false
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#dfe5ec'
      }
    },
    splitNumber: 4,
    axisLabel: {
      textStyle: {
        fontSize: 16,
        color: '#666'
      },
      formatter: function(value, index) {
        if (value >= 1000) {
          value = value / 1000 + 'k'
        }
        return value
      }
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      // type: 'cross',
      lineStyle: {
        type: 'dashed'
      }
    },
    transitionDuration: 0,
    borderColor: '#FCA155',
    textStyle: {
      color: '#000'
    },
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;',
    formatter:
      '<div style="padding:5px;font-size:16px;font-weight: 540;">{b0}<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">营业额</span>￥{c0}</div>'
  },
  grid: {
    left: '40',
    top: '20'
  },
  series: [
    {
      data: [14820, 15932, 12437, 13934, 11900, 20213, 11010],
      type: 'line',
      smooth: true,
      symbolSize: 10,
      showSymbol: false, // 隐藏点
      lineStyle: {
        color: '#FCAD6A',
        width: 4
      },
      itemStyle: {
        borderColor: '#FCAD6A',
        borderWidth: 3
      }
    }
  ]
}
export const OPTIONYUAN = {
  tooltip: {
    trigger: 'item',
    borderColor: '#FCA155',
    textStyle: {
      color: '#000',
      fontWeight: 500
    },
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;',
    formatter: function(params) {
      let marker = params.marker
      let percent = params.percent
      return marker + params.name + '&nbsp;&nbsp;&nbsp;' + percent + '%'
    }
  },
  title: {
    text: 0,
    subtext: '订单总笔数',
    left: 'center',
    top: '30%',
    textStyle: {
      color: '#000',
      fontSize: 40,
      align: 'center'
    },
    subtextStyle: {
      color: '#999',
      fontSize: 16,
      align: 'center'
    }
  },
  legend: [
    {
      bottom: '18%',
      left: '20',
      itemWidth: 8, // 设置宽度
      itemHeight: 8, // 设置高度
      icon: 'circle',
      data: ['早餐'],
      width: 400,
      textStyle: {
        fontSize: '14'
      },
      // selectedMode: false, // 取消图例上的点击事件
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.2)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      },
      itemGap: 120
    },
    {
      bottom: '18%',
      left: '60%',
      // left: 'right',
      itemWidth: 8, // 设置宽度
      itemHeight: 8, // 设置高度
      icon: 'circle',
      data: ['午餐'],
      width: 400,
      textStyle: {
        fontSize: '14'
      },
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.3)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      },
      itemGap: 120
    },
    {
      bottom: '10%',
      left: '20',
      itemWidth: 8, // 设置宽度
      itemHeight: 8, // 设置高度
      icon: 'circle',
      data: ['下午茶'],
      width: 400,
      textStyle: {
        fontSize: '14'
      },
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.1)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      },
      itemGap: 118
    },
    {
      bottom: '10%',
      left: '60%',
      // left: 'right',
      itemWidth: 8, // 设置宽度
      itemHeight: 8, // 设置高度
      icon: 'circle',
      data: ['晚餐'],
      width: 400,
      textStyle: {
        fontSize: '14'
      },
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.25)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      },
      itemGap: 118
    },
    {
      bottom: '2%',
      left: '20',
      itemWidth: 8, // 设置宽度
      itemHeight: 8, // 设置高度
      icon: 'circle',
      data: ['宵夜'],
      width: 400,
      textStyle: {
        fontSize: '14'
      },
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.09)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      }
      // itemGap: 125
    },
    {
      bottom: '2%',
      left: '60%',
      // left: 'right',
      itemWidth: 8, // 设置宽度
      itemHeight: 8, // 设置高度
      icon: 'circle',
      data: ['凌晨餐'],
      width: 400,
      textStyle: {
        fontSize: '14'
      },
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.06)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      }
      // itemGap: 125
    }
  ],

  series: [
    {
      // name: 'Access From',
      type: 'pie',
      radius: ['70%', '60%'],
      avoidLabelOverlap: false,
      top: '-25%',
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 5
      },
      hoverAnimation: false,
      label: {
        show: false,
        position: 'center'
        // normal: {
        //   formatter: '{{b},{d}%}'
        // }
      },
      emphasis: {
        label: {
          show: false,
          fontSize: '20',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 1048, name: '早餐' },
        { value: 1235, name: '午餐' },
        { value: 780, name: '下午茶' },
        { value: 684, name: '晚餐' },
        { value: 400, name: '宵夜' },
        { value: 200, name: '凌晨餐' }
      ]
    }
  ],
  color: ['#07DED0', '#FE985F', '#9E92F7', '#F97C95', '#58AFFE', '#F8C345']
}
export const OPTIONHUAN = {
  tooltip: {
    trigger: 'item',
    borderColor: '#FCA155',
    textStyle: {
      color: '#000'
    },
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;',
    formatter: function(params) {
      let marker = params.marker
      let percent = params.percent
      return marker + params.name + '&nbsp;&nbsp;&nbsp;' + percent + '%'
    }
  },
  legend: [
    {
      bottom: '40',
      left: '5%',
      icon: 'circle',
      data: ['堂食'],
      textStyle: {
        fontSize: '14'
      },
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.3)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      }
      // itemGap: 15
    },
    {
      bottom: '40',
      left: '50%',
      icon: 'circle',
      data: ['外卖配送'],
      textStyle: {
        fontSize: '14'
      },
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.2)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      }
      // itemGap: 15
    },
    {
      bottom: '10',
      left: '5%',
      icon: 'circle',
      data: ['食堂自提'],
      textStyle: {
        fontSize: '14'
      },
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.1)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      }
      // itemGap: 15
    },
    {
      bottom: '10',
      left: '50%',
      icon: 'circle',
      data: ['取餐柜自提'],
      textStyle: {
        fontSize: '14'
      },
      formatter: function(name) {
        let number = Number(OPTIONYUAN.title.text.replace(/,/g, ''))
        let clientcounts = [Math.round(number * 0.4)]
        return name + '    ' + INITNUMBER(clientcounts[0]) + '笔'
      }
      // itemGap: 15
    }
  ],
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      top: '-20%',
      left: '-5%',
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 1198, name: '堂食' },
        { value: 835, name: '外卖配送' },
        { value: 680, name: '食堂自提' },
        { value: 634, name: '取餐柜自提' }
      ]
    }
  ],
  color: ['#4D95FA', '#07D7D7', '#727AFF', '#4AD96D']
}
export const COLUMNS = [
  { label: '餐段', column: 'meal_type' },
  { label: '营业额', column: 'turnover' },
  { label: '占比', column: 'Proportion' }
]
export const OPRIONBAR = {
  xAxis: {
    type: 'category',
    data: [
      '琶洲食堂',
      '万胜围食堂',
      '新港东食堂',
      '棠东食堂',
      '保利食堂',
      '天河中心食堂',
      '太古汇食堂',
      '黄村食堂'
    ],
    axisLine: { show: false },
    axisTick: {
      show: false
    },
    splitLine: {
      show: false
    },
    axisLabel: {
      textStyle: {
        fontSize: 16,
        color: '#666',
        lineHeight: 40
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: { show: false },
    axisTick: {
      show: false
    },
    splitNumber: 4,
    axisLabel: {
      textStyle: {
        fontSize: 16,
        color: '#666'
      },
      formatter: function(value, index) {
        if (value >= 1000) {
          value = value / 1000 + 'k'
        }
        return value
      }
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#dfe5ec'
      }
    }
  },
  grid: {
    left: '40',
    top: '50'
  },
  tooltip: {
    borderColor: '#FCA155',
    textStyle: {
      color: '#000'
    },
    transitionDuration: 0,
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;',
    formatter: function(params) {
      let value = params.value
      let name = params.name
      // return `${dayjs(`${new Date()}`).format(
      //   'YYYY-MM-DD'
      // )}<br/><span style=" color: #888;margin-right: 15px;">营业额</span>￥${INITNUMBER(value)}`
      return `<div style="padding:5px;font-size:16px;font-weight: 540;">${name}<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">营业额</span>￥${INITNUMBER(
        value
      )}`
    }
  },
  series: [
    {
      barWidth: 30, // 柱图宽度
      color: '#FE943C',
      data: [11000, 7500, 9000, 5645.5, 5900, 6100, 11800, 7000, 8500],
      type: 'bar'
    }
  ]
}

export const PICKEROPTIONS = {
  shortcuts: [
    {
      text: '最近一周',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        picker.$emit('pick', [start, end])
      }
    }
  ],
  disabledDate(time) {
    let start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
    return time.getTime() > Date.now() - 8.64e6 || time.getTime() < start //
  }
}
export const RECENTSEVENTDAY = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

//  东贝跳转链接 ，默认用后台返回的URL 管理系统
export const ULR_DO_BEI_SYSTEM_DEV = 'https://dobayadmin.anasit.com/login'
export const ULR_DO_BEI_SYSTEM_STAGING = 'https://dobayadmin.anasit.com/login'
//  东贝跳转链接 ，默认用后台返回的URL 门店
export const ULR_DO_BEI_SHOP_DEV = 'https://shopadmin.anasit.com/login'
export const ULR_DO_BEI_SHOP_STAGING = 'https://shopadmin.anasit.com/login'
// 洗衣系统跳转URL
export const URL_WASHING_SYSTEM_DEV = 'https://web-xhf.lxt6.cn:8089/#/login'
export const URL_WASHING_SYSTEM_STAGING = 'https://web-xhf.lxt6.cn:8089/#/login'
