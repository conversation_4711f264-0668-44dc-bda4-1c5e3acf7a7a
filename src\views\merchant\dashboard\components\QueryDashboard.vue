<template>
  <div class="smartCanteenNutrition">
    <div class="header">
      <div class="title">查询大屏</div>
      <div class="time">2024年01月08日 09:55:11 星期一</div>
    </div>
    <div class="chart">
      <div class="center">
        <div class="block h-770">
          <div class="title">餐段</div>
          <div>
            <el-button type="text" class="ps-bule block-btn" @click="openDialog('menu')">
              编辑
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="选择组织" :visible.sync="showDialog" width="500px" custom-class="ps-dialog">
      <el-form
        :model="dialogForm"
        ref="dialogFormRef"
        label-width="120px"
        class="dialog-form"
        :rules="dialogFormRules"
      >
        <el-form-item label="显示组织：" prop="orgId">
          <organization-select
            class="search-item-w ps-input w-250"
            placeholder="请选择所属组织"
            :isLazy="false"
            :multiple="false"
            :check-strictly="true"
            v-model="dialogForm.orgId"
            :append-to-body="true"
            @change="changeOrg"
          ></organization-select>
        </el-form-item>
        <el-form-item label="菜谱：">
          <el-radio-group class="ps-radio" v-model="dialogForm.menuType" @change="changeMenuType">
            <el-radio label="week">周菜谱</el-radio>
            <el-radio label="month">月菜谱</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="设备类型：" prop="deviceType">
          <el-select
            multiple
            v-model="dialogForm.deviceType"
            placeholder="请选择设备类型"
            class="ps-select w-250"
            popper-class="ps-popper-select"
            @change="deviceTypeChange"
          >
            <el-option
              v-for="item in deviceList"
              :key="item.key"
              :label="item.name"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备型号：" :prop="isDisabledModel ? '' : 'deviceModel'">
          <el-select
            multiple
            v-model="dialogForm.deviceModel"
            placeholder="请选择设备型号"
            class="ps-select w-250"
            popper-class="ps-popper-select"
            :disabled="isDisabledModel"
            @change="deviceModelChange"
          >
            <el-option
              v-for="item in deviceModelList"
              :key="item.key"
              :label="item.key"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="菜谱：" prop="menuId">
          <el-select v-model="dialogForm.menuId" class="ps-select w-250">
            <el-option
              v-for="item in menuList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="showDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="confirmDialog">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import OrganizationSelect from '@/components/OrganizationSelect'
export default {
  name: 'management',
  components: { OrganizationSelect },
  props: {
    type: String,
    cameraList: Array,
    templateInfo: [Object, Array]
  },
  data() {
    return {
      dialogType: '',
      showDialog: false,
      dialogForm: {
        orgId: '',
        menuType: '',
        deviceType: [],
        deviceModel: [],
        menuId: ''
      },
      dialogFormRules: {
        orgId: [{ required: true, message: '请选择显示组织', trigger: 'blur' }],
        deviceType: [{ required: true, message: '请选择设备类型', trigger: 'blur' }],
        deviceModel: [{ required: true, message: '请选择设备型号', trigger: 'blur' }],
        menuId: [{ required: true, message: '请选择菜谱', trigger: 'blur' }]
      },
      deviceList: [],
      deviceModelList: [],
      isDisabledModel: false,
      menuList: [],
      dataInfo: {
        menuType: 'week',
        deviceType: '',
        deviceModel: '',
        menuId: '',
        orgId: ''
      }
    }
  },
  created() {
    if (this.type === 'edit') {
      this.dataInfo = this.templateInfo
    }
    this.getOrgDeviceList()
  },
  methods: {
    openDialog(dialogType) {
      this.dialogType = dialogType
      this.showDialog = true
      this.dialogForm.orgId = this.dataInfo.orgId
      this.dialogForm.menuType = this.dataInfo.menuType
      this.dialogForm.deviceType = this.dataInfo.deviceType
      this.dialogForm.deviceModel = this.dataInfo.deviceModel
      this.dialogForm.menuId = this.dataInfo.menuId
      if (this.dialogForm.deviceType.length) {
        this.getDeviceModel()
      } else {
        this.isDisabledModel = false
      }
      this.checkGetMemuList()
    },
    confirmDialog() {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          this.dataInfo.orgId = this.dialogForm.orgId
          this.dataInfo.menuType = this.dialogForm.menuType
          this.dataInfo.deviceType = this.dialogForm.deviceType
          this.dataInfo.deviceModel = this.dialogForm.deviceModel
          this.dataInfo.menuId = this.dialogForm.menuId
          this.$refs.dialogFormRef.clearValidate()
          this.dialogForm = {
            orgId: '',
            menuType: '',
            deviceType: [],
            deviceModel: [],
            menuId: ''
          }
          this.showDialog = false
          this.$emit('comfirm', this.dataInfo)
        }
      })
    },
    changeOrg() {
      this.dialogForm.menuId = ''
      this.menuList = []
      this.checkGetMemuList()
    },
    changeMenuType() {
      this.dialogForm.menuId = ''
      this.menuList = []
      this.checkGetMemuList()
    },
    deviceTypeChange() {
      this.dialogForm.menuId = ''
      this.menuList = []
      this.dialogForm.deviceModel = []
      this.deviceModelList = []
      if (this.dialogForm.deviceType.length) {
        this.getDeviceModel()
      } else {
        this.isDisabledModel = false
      }
      this.checkGetMemuList()
    },
    deviceModelChange() {
      this.dialogForm.menuId = ''
      this.menuList = []
      this.checkGetMemuList()
    },
    checkGetMemuList() {
      // let deviceModel = []
      // if (this.dialogForm.deviceType.indexOf('H5') !== -1) {
      //   deviceModel.push('H5')
      // }
      // if (this.dialogForm.deviceType.indexOf('MAPP') !== -1) {
      //   deviceModel.push('MAPP')
      // }
      // deviceModel = deviceModel.concat(this.dialogForm.deviceModel)
      // if (this.dialogForm.deviceType.length && deviceModel.length && this.dialogForm.orgId) {
      //   this.getMenuList(deviceModel)
      // }
      if (this.dialogForm.deviceType.length && this.dialogForm.orgId) {
        if (
          this.dialogForm.deviceType.indexOf('H5') !== -1 ||
          this.dialogForm.deviceType.indexOf('MAPP') !== -1
        ) {
          this.getMenuList()
        } else {
          if (this.dialogForm.deviceModel.length) this.getMenuList()
        }
      }
    },
    // 获取菜谱
    async getMenuList() {
      const res = await this.$apis.apiBackgroundDatascreenKanbanTempGetKanbanSettingPost({
        organization_id: [this.dialogForm.orgId],
        device_type: this.dialogForm.deviceType,
        device_model: this.dialogForm.deviceModel,
        menu_type: this.dialogForm.menuType
      })
      if (res.code === 0) {
        this.menuList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取当前组织设备类型
    async getOrgDeviceList() {
      const res = await this.$apis.apiBackgroundAdminDeviceDeviceTypePost({
        source: 'self'
      })
      if (res.code === 0) {
        this.deviceList = [{ name: 'H5', key: 'H5' }, { name: '小程序', key: 'MAPP' }, ...res.data]
      } else {
        this.$message.error(res.msg)
      }
    },
    async getDeviceModel() {
      // H5和小程序不需要选择设备型号（置灰），也不需要获取设备型号
      let deviceTypes = this.dialogForm.deviceType.filter(t => {
        return t !== 'H5' && t !== 'MAPP'
      })
      if (!deviceTypes.length) {
        this.isDisabledModel = true
        return
      } else {
        this.isDisabledModel = false
      }
      const res = await this.$apis.apiBackgroundAdminDeviceDeviceModelPost({
        device_types: deviceTypes
      })
      if (res.code === 0) {
        this.deviceModelList = res.data
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss">
.smartCanteenNutrition {
  padding: 20px;
  background-color: #fff;
  min-width: 1200px;
  .header {
    position: relative;
    height: 40px;
    margin-bottom: 15px;
    .title {
      text-align: center;
      font-size: 30px;
      line-height: 40px;
      font-weight: bold;
    }
    .time {
      font-size: 22px;
      position: absolute;
      top: 0;
      left: 50px;
      line-height: 40px;
    }
  }
  .center {
    width: 100%;
  }
  .chart {
    display: flex;
    justify-content: space-around;
  }
  .block {
    display: flex;

    justify-content: space-between;
    border: 1px #a1a1a1 solid;
    padding: 10px;
    .block-btn {
      padding: 10px;
      font-size: 22px;
    }
    .title {
      font-size: 22px;
    }
  }
  .h-770 {
    height: 710px;
  }
}
</style>
