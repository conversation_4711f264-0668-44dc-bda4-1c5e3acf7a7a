import * as dayjs from 'dayjs'

export const recentSevenDay = [
  dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]
export const PAYMENT_ORDER = {
  time_type: {
    type: 'select',
    value: 'create_time',
    maxWidth: '120px',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      }
    ]
  },
  select_date: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    labelWidth: '100px',
    placeholder: '请输入订单号',
    clearable: true
  },
  commodity_name: {
    type: 'input',
    value: '',
    label: '商品名称',
    labelWidth: '100px',
    placeholder: '请输入商品名称',
    clearable: true
  },
  commodity_type: {
    type: 'select',
    value: '',
    label: '商品类型',
    clearable: true,
    dataList: [
      {
        label: '虚拟商品',
        value: 'virtual'
      },
      {
        label: '实物商品',
        value: 'physical'
      }
    ]
  },
  is_enable: {
    type: 'select',
    value: '',
    label: '状态',
    clearable: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '上架中',
        value: true
      },
      {
        label: '已下架',
        value: false
      }
    ]
  },
  user_name: {
    type: 'input',
    value: '',
    label: '用户名称',
    labelWidth: '100px',
    placeholder: '请输入用户名称',
    clearable: true
  },
  user_phone: {
    type: 'input',
    value: '',
    label: '手机号',
    labelWidth: '100px',
    placeholder: '请输入手机号',
    clearable: true
  }
}
export const REFUND_ORDER = {
  time_type: {
    type: 'select',
    value: 'create_time',
    maxWidth: '120px',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '退款时间',
        value: 'finish_time'
      }
    ]
  },
  select_date: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    labelWidth: '100px',
    placeholder: '请输入订单号',
    clearable: true
  },
  commodity_name: {
    type: 'input',
    value: '',
    label: '商品名称',
    labelWidth: '100px',
    placeholder: '请输入商品名称',
    clearable: true
  },
  commodity_type: {
    type: 'select',
    value: '',
    label: '商品类型',
    clearable: true,
    dataList: [
      {
        label: '虚拟商品',
        value: 'virtual'
      },
      {
        label: '实物商品',
        value: 'physical'
      }
    ]
  },
  user_name: {
    type: 'input',
    value: '',
    label: '用户名称',
    labelWidth: '100px',
    placeholder: '请输入用户名称',
    clearable: true
  },
  origin_trade_no: {
    type: 'input',
    value: '',
    label: '原订单',
    labelWidth: '100px',
    placeholder: '请输入原订单',
    clearable: true
  }
}

export const POINTS_COMMODITY = {
  time_type: {
    type: 'select',
    value: 'create_time',
    maxWidth: '120px',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '修改时间',
        value: 'update_time'
      }
    ]
  },
  select_date: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '商品名称',
    labelWidth: '100px',
    placeholder: '请输入商品名称',
    clearable: true
  },
  commodity_type: {
    type: 'select',
    value: '',
    label: '商品类型',
    clearable: true,
    dataList: [
      {
        label: '虚拟商品',
        value: 'virtual'
      },
      {
        label: '实物商品',
        value: 'physical'
      }
    ]
  },
  is_enable: {
    type: 'select',
    value: '',
    label: '状态',
    clearable: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '上架中',
        value: true
      },
      {
        label: '已下架',
        value: false
      }
    ]
  }
}

export const POINTS_TASK = {
  select_date: {
    type: 'daterange',
    label: '创建时间',
    value: [],
    format: 'yyyy-MM-dd',
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '任务名称',
    labelWidth: '100px',
    placeholder: '请输入任务名称',
    clearable: true
  }
  // specify_page_type: {
  //   type: 'select',
  //   value: '',
  //   label: '所在页面',
  //   clearable: true,
  //   dataList: [
  //     {
  //       label: '首页',
  //       value: 'front_page'
  //     },
  //     {
  //       label: '营养分析',
  //       value: 'nutritional_analysis'
  //     },
  //     {
  //       label: '营养周报',
  //       value: 'nutrition_weekly'
  //     }
  //   ]
  // }
}
