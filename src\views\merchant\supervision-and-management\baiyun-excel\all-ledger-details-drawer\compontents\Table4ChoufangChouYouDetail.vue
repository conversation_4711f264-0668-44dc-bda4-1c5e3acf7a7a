<template>
  <div>
    <div class="table-content m-t-20">
      <div class="table-header-title">厨房油烟机清洗表</div>
      <!-- 表单包装表格 start -->
        <el-table v-loading="isLoading" :data="tableData" ref="historytableData" style="width: 100%" stripe
          header-row-class-name="ps-table-header-row">
        <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
        </table-column>
        </el-table>
      <!-- 表单包装表格 end -->
    </div>
    <!-- 分页 start -->
    <div class="block ps-pagination" style="text-align: right; padding-top: 20px; display: none">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100, 500]" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount" background class="ps-text" popper-class="ps-popper-select"></el-pagination>
    </div>
  </div>
  <!-- end -->
</template>

<script>
import { to, deepClone } from '@/utils'
import { TABLE_HEAD_DATA_CHUFANG_CHOUYOU_LEDGER_DETAIL } from '../../constants'

export default {
  name: 'Table4ChoufangChouYouDetail',
  props: {
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    },
    ledgerNo: {
      type: String,
      default: ''
    },
    reviewPerson: {
      type: String,
      default: ''
    }
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableSetting: deepClone(TABLE_HEAD_DATA_CHUFANG_CHOUYOU_LEDGER_DETAIL),
      tableData: [
        {
          clean_content: '烟罩油、尘清除是否干净',
          clean_situation: '',
          clean_person: '',
          review_name: ''
        },
        {
          clean_content: '管道内油垢清洗',
          clean_situation: '',
          clean_person: '',
          review_name: ''
        },
        {
          clean_content: '排风机风扇上的油污铲干净',
          clean_situation: '',
          clean_person: '',
          review_name: ''
        },
        {
          clean_content: '排风机过滤器上的油污清洗干净',
          clean_situation: '',
          clean_person: '',
          review_name: ''
        }
      ] // 表格数据
    }
  },
  watch: {
    ledgerNo(val) {
      if (val) {
        this.initData()
      }
    }
  },
  created() {
  },
  mounted() {
    console.log('ledgerNo', this.ledgerNo)
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      // 使用默认数据初始化表格
      this.getDataList()
    },

    // 获取历史记录
    async getDataList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerGetKitchenRangeHoodCleanPost({
          ledger_no: this.ledgerNo
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        let newList = deepClone(this.tableData)
        newList.forEach((item, index) => {
          switch (index) {
            case 0:
              item.clean_situation = data.yzj_is_clean ? '是' : '否'
              item.clean_person = data.yzj_job_person_name
              item.review_name = this.reviewPerson
              break;
            case 1:
              item.clean_situation = data.gd_is_clean ? '是' : '否'
              item.clean_person = data.gd_job_person_name
              item.review_name = this.reviewPerson
              break;
            case 2:
              item.clean_situation = data.fs_is_clean ? '是' : '否'
              item.clean_person = data.fs_job_person_name
              item.review_name = this.reviewPerson
              break;
            case 3:
              item.clean_situation = data.glq_is_clean ? '是' : '否'
              item.clean_person = data.glq_job_person_name
              item.review_name = this.reviewPerson
              break;
            default:
              break;
          }
        })
        this.tableData = deepClone(newList)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
    },
    closeDialog() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.table-header-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
  background-color: #fff;
}
</style>
