<template>
  <div class="carbinding container-wrapper">
    <!--车辆绑定 -->
    <!--头部-->
    <refresh-tool @refreshPage="refreshHandler(true)" />
    <!--筛选 -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler" @reset="resetHandler" :autoSearch="false"></search-form>
    <!--数据列表 -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon  color="origin"  @click="handlerCarImport()" v-permission="['card_service.third_card_user.batch_import_sk_bind']">导入关联</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          height="460"
          stripe
          header-row-class-name="ps-table-header-row"
        >
        <el-table-column label="序号" align="center" header-align="center" width="80px">
          <template slot-scope="scope" >
             {{ (scope.$index+1) + (currentPage-1)*pageSize }}
          </template>
        </el-table-column>
          <table-column v-for="(item,index) in tableSettings" :key="index" :col="item">
            <template #cardDepartmentGroupAlias="{ row }">
              {{ row.card_department_group_alias | listToString }}
            </template>
            <template #cardUserGroupAlias="{ row }">
              {{ row.card_user_group_alias | listToString }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text-blue" @click="handlerEdit(row)" v-permission="['card_service.third_card_user.sk_modify']">编辑</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="onPaginationChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!--导入弹窗-->
    <import-dialog-drawer
      :templateUrl="templateUrl"
      :tableSetting="tableSettingImport"
      :show.sync="isShowImportDialog"
      :title="'导入关联'"
      :openExcelType="openExcelType"
    >
    </import-dialog-drawer>
    <!--编辑弹窗-->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="isShowEditDialog"
      width="450px"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      show-close
      >
      <el-form ref="dialogForm"  label-width="100px">
        <el-form-item label="水控账号" prop='username' >
            <el-input v-model="dialogEditData.username" placeholder="请输入水控账号" type="text" class="w-250" @input="dialogInputChange"></el-input>
        </el-form-item>
      </el-form>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button  class="ps-cancel-btn" @click="isShowEditDialog = false" >取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="submitEditDialog" v-loading="isBindLoading">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { TABLE_HEAD_DATA_USER_BINDING, SEARCH_FORM_SET_DATA_USER_BINDING, URL_TEMPLATE_MODEL, TABLE_HEAD_DATA_IMPORT_USER_BINGING } from './constants/userBandingConstants.js'
import { deepClone, to, debounce } from '@/utils'
export default {
  name: "UserBandingWaterList",
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSettings: deepClone(TABLE_HEAD_DATA_USER_BINDING), // 表格头部设置内容
      searchFormSetting: deepClone(SEARCH_FORM_SET_DATA_USER_BINDING), // 搜索设置内容
      dialogTitle: '编辑', // 弹窗标题
      isShowEditDialog: false, // 是否显示编辑dialog
      isShowImportDialog: false, // 是否显示导入dialog
      templateUrl: '', // 导入模板的链接 to do debug
      tableSettingImport: deepClone(TABLE_HEAD_DATA_IMPORT_USER_BINGING), // 导入表格头部设置
      dialogEditData: {}, // 编辑弹窗数据存放
      isLoadingPerson: false, // 是否显示loading
      isBindLoading: false, // 是否显示确认键loading
      openExcelType: 'UserBandingImport', // 是否显示确认键loading
      isHasPersonInfo: false // 是否存在改用户
    }
  },
  components: { },
  created() {
    this.initLoad()
  },
  filters: {
    listToString(value) {
      if (!value || value === "" || value.length === 0) {
        return '--'
      }
      if (typeof value === 'string') {
        return value
      }
      if (Array.isArray(value) && value.length > 0) {
        return value.join(",")
      }
      return '--'
    }

  },
  destroyed() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
  },
  methods: {
    /**
     * 刷新页面
     */
    refreshHandler(flag) {
      // 搜索重置
      this.currentPage = 1;
      if (flag) {
        this.$refs.searchRef.resetForm()
      }
      this.tableData = []
      this.initLoad()
    },
    /**
     * 初始化数据
     */
    initLoad() {
      console.log("initLoad");
      // 获取数据列表
      this.getUserBindingList()
      // 获取模板链接
      this.templateUrl = this.getTempUrl()
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    onPaginationChange(page) {
      console.log("onPaginationChange", page);
      this.currentPage = page
      this.getUserBindingList()
    },
    /**
     * 显示条数改变
     * @param {*} pageSize
     */
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getUserBindingList()
    },
    /**
     * 获取车辆绑定列表
     */
    async getUserBindingList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiCardServiceThirdCardUserSkListPost(params))
      console.log("getUserBindingList", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        var resultList = res.data.results || []
        this.tableData = deepClone(resultList)
        this.totalCount = res.data.count || -1
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'select_date') {
            params[key] = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    /**
     * 筛选
     */
    searchHandler: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.initLoad()
      }
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.refreshHandler(false)
    },
    /**
     * 导入按钮点击
     */
    handlerCarImport() {
      this.isShowImportDialog = true
    },
    /**
     * 编辑
     * @param {*} itemData  每行数据
     */
    handlerEdit(itemData) {
      console.log("itemData", itemData);
      this.dialogEditData = deepClone(itemData)
      this.isHasPersonInfo = !!itemData.username
      this.isShowEditDialog = true
    },
    /**
     * 导入确认
     * @param {*} data
     */
    async confirmImportData(data) {
      var dataList = data.allData || []
      var currentPage = data.currentPage
      console.log("confirmImportData", data, currentPage)
      if (Array.isArray(dataList) && dataList.length > 0) {
        this.isShowImportDialog = false

        const [err, res] = await to(this.$apis.apiCardServiceThirdCardUserBatchImportSkBindPost({ url: dataList }))
        if (err) {
          this.$message.error('导入失败 ' + err.message)
          return
        }
        if (res.code === 0) {
          this.$message.success('导入成功')
          this.getUserBindingList()
        } else {
          this.$message.error('导入失败 ' + res.msg)
        }
      } else {
        this.$message.error('请先导入数据')
      }
    },
    /**
     * 编辑弹窗确认
     */
    submitEditDialog() {
      if (!this.dialogEditData.person_no) {
        this.$message.error('水控账号不能为空')
        return
      }
      this.bindUserInfo()
    },
    /**
     * 编辑绑定信息
     */
    async bindUserInfo() {
      this.isBindLoading = true
      let params = {
        id: this.dialogEditData.id,
        username: this.dialogEditData.username
      }
      const [err, res] = await to(this.$apis.apiCardServiceThirdCardUserSkModifyPost(params))
      this.isBindLoading = false

      if (err) {
        this.$message.error('修改失败 ' + err.message)
        return
      }

      if (res.code === 0) {
        this.$message.success('修改成功')
        this.isShowEditDialog = false
        this.getUserBindingList()
      } else {
        this.$message.error('修改失败 ' + res.msg)
      }
    },
    /**
     * 获取模板链接
     */
    getTempUrl() {
      var url = URL_TEMPLATE_MODEL
      url = process.env.NODE_ENV === 'development' ? "https://cashier-v4.debug.packertec.com" + url : location.origin + url
      console.log("url", url);
      return url
    }
  }
}

</script>

<style lang="scss" scoped>
.carbinding {
  .el-table{
    border: 1px solid #fff;
  }
  ::v-deep .el-table td,
  ::v-deep .el-table th{
      border: 1px solid #fff !important;
  }

  .dialog-person-info {
    width: 250px;
    height: 100px;
    background-color: #F2F2F2;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

}

</style>
