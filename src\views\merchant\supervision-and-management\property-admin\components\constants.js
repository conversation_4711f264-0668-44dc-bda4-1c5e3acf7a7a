import { getSevenDateRange, getMonthDateRange } from '@/utils'
export const DEBT_TYPE_LIST = [
  {
    label: '长期借款',
    value: 'long_borrowing'
  },
  {
    label: '长期应付款',
    value: 'long_payable'
  },
  {
    label: '短期借款',
    value: 'short_borrowing'
  },
  {
    label: '应付账款',
    value: 'payable'
  },
  {
    label: '预收账款',
    value: 'pre_harvest_payable'
  },
  {
    label: '其他负债',
    value: 'other_liability'
  }
]

export const SEARCH_PROPERTY_INFO = {
  upload_start: {
    type: 'monthrange',
    label: '日期归属',
    value: getMonthDateRange(1),
    clearable: false
  },
  select_time: {
    type: 'daterange',
    label: '创建时间',
    value: getSevenDateRange(7),
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '文件名称',
    placeholder: '请输入文件名称'
  }
}
export const TABLE_PROPERTY_INFO = [
  { label: '创建时间', key: 'create_time' },
  { label: '文件名称', key: 'name' },
  { label: '日期归属', key: 'upload_date' },
  { label: '资产金额', key: 'asset_fee', type: 'money' },
  { label: '备注', key: 'remark', showTooltip: true },
  { label: '操作员', key: 'operator_name', type: 'slot', slotName: 'operator_name' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
]
// 系统详情
export const INFO_SYSTEM_DETAILS_TABLE = [
  { label: '资产编号', key: 'number_no' },
  { label: '资产类别', key: 'asset_category_alias' },
  { label: '资产类型', key: 'asset_type_alias' },
  { label: '资产分类', key: 'asset_classify_name', showTooltip: true },
  { label: '资产名称', key: 'name', showTooltip: true },
  { label: '原值', key: 'original_fee', type: 'money' },
  { label: '入库金额', key: 'warehouse_fee', type: 'money' },
  { label: '出库金额', key: 'out_of_stock_fee', type: 'money' },
  { label: '净值', key: 'net_worth_fee', type: 'money' }
]
// 手动上传详情
export const INFO_HAND_DETAILS_TABLE = [
  { label: '资产编号', key: 'number_no' },
  { label: '资产类别', key: 'asset_category_alias' },
  { label: '资产类型', key: 'asset_type_alias' },
  { label: '资产分类', key: 'asset_classify_name', showTooltip: true },
  { label: '资产名称', key: 'name', showTooltip: true },
  { label: '原值', key: 'original_fee', type: 'money' },
  { label: '累计折旧', key: 'depreciation_fee', type: 'money' },
  { label: '净值', key: 'net_worth_fee', type: 'money' },
  { label: '数量', key: 'quantity' },
  { label: '购置日期', key: 'purchase_date' },
  { label: '变更日期', key: 'change_date' },
  { label: '单位', key: 'unit', showTooltip: true },
  { label: '存放地点', key: 'place', showTooltip: true },
  { label: '在用单位', key: 'use_unit', showTooltip: true },
  { label: '备注', key: 'remark', showTooltip: true }
]

export const SEARCH_PROPERTY_DETAILS = {
  upload_start: {
    type: 'monthrange',
    label: '日期归属',
    value: getMonthDateRange(1),
    clearable: false
  },
  select_time: {
    type: 'daterange',
    label: '购置日期',
    value: getSevenDateRange(7),
    clearable: true
  },
  asset_categorys: {
    type: 'select',
    label: '资产类别',
    value: '',
    multiple: true,
    collapseTags: true,
    placeholder: '请选择资产类别',
    dataList: [
      {
        label: '固定资产',
        value: 'fixed'
      },
      {
        label: '流动资产',
        value: 'flow'
      }
    ]
  },
  asset_types: {
    type: 'select',
    label: '资产类型',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择资产类型',
    dataList: []
  },
  asset_classify_ids: {
    type: 'select',
    label: '资产分类',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择状态',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: []
  },
  name: {
    type: 'input',
    value: '',
    label: '资产名称',
    placeholder: '请输入资产名称'
  }
}
export const TABLE_PROPERTY_DETAILS = [
  { label: '资产编号', key: 'number_no' },
  { label: '日期归属', key: 'upload_date' },
  { label: '资产类别', key: 'asset_category_alias' },
  { label: '资产类型', key: 'asset_type_alias' },
  { label: '资产分类', key: 'asset_classify_name', showTooltip: true },
  { label: '资产名称', key: 'name', showTooltip: true },
  { label: '原值', key: 'original_fee', type: 'money' },
  { label: '累计折旧', key: 'depreciation_fee', type: 'money' },
  { label: '净值', key: 'net_worth_fee', type: 'money' },
  { label: '数量', key: 'quantity' },
  { label: '购置日期', key: 'purchase_date' },
  { label: '变更日期', key: 'change_date' },
  { label: '单位', key: 'unit', showTooltip: true },
  { label: '存放地点', key: 'place', showTooltip: true },
  { label: '在用单位', key: 'use_unit', showTooltip: true },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
]

export const SEARCH_PROPERTY_STATISTICS = {
  select_time: {
    type: 'monthrange',
    label: '统计月份',
    value: getMonthDateRange(1),
    clearable: false
  }
}
export const TABLE_PROPERTY_STATISTICS = [
  { label: '月份', key: 'statistics_date' },
  { label: '组织名称', key: 'organization_name' },
  { label: '资产总额', key: 'total_fee', type: 'money' },
  {
    key: 'gd',
    label: '固定资产',
    children: [
      {
        key: 'device_fee',
        label: '设备',
        type: 'money'
      },
      {
        key: 'long_term_fee',
        label: '长期投资',
        type: 'money'
      }
    ]
  },
  {
    key: 'ld',
    label: '流动资产',
    children: [
      {
        key: 'monetary_funds_fee',
        label: '货币资金',
        type: 'money'
      },
      {
        key: 'prepayment_fee',
        label: '预付账款',
        type: 'money'
      },
      {
        key: 'accounts_fee',
        label: '应收账款',
        type: 'money'
      },
      {
        key: 'stock_fee',
        label: '存货',
        type: 'money'
      },
      {
        key: 'short_term_fee',
        label: '短期投资',
        type: 'money'
      }
    ]
  }
]

export const SEARCH_DEBT_INFO = {
  select_time: {
    type: 'daterange',
    label: '日期筛选',
    value: getSevenDateRange(7),
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '负债名称',
    placeholder: '请输入负债名称'
  },
  liability_category: {
    type: 'select',
    label: '负债类别',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择负债类别',
    dataList: [
      {
        label: '非流动负债',
        value: 'non_flowing_liability'
      },
      {
        label: '流动负债',
        value: 'flowing_liability'
      }
    ]
  },
  liability_type: {
    type: 'select',
    label: '负债类型',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择负债类型',
    dataList: []
  },
  liability_state: {
    type: 'select',
    label: '状态',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择状态',
    dataList: [
      {
        label: '未还款',
        value: 'not_repayment'
      },
      {
        label: '还款中',
        value: 'part_repayment'
      },
      {
        label: '已还款',
        value: 'already_repayment'
      },
      {
        label: '已冲销',
        value: 'reversed'
      }
    ]
  },
  data_source: {
    type: 'select',
    label: '来源',
    value: '',
    clearable: true,
    multiple: false,
    collapseTags: false,
    placeholder: '请选择来源',
    dataList: [
      {
        label: '人工上传',
        value: 'manual'
      },
      {
        label: '系统记录',
        value: 'system'
      }
    ]
  }
}
export const TABLE_DEBT_INFO = [
  { label: '归属时间', key: 'belong_date' },
  { label: '更新时间', key: 'update_time' },
  { label: '负债名称', key: 'name' },
  { label: '负债类别', key: 'liability_category_alias' },
  { label: '负债类型', key: 'liability_type_alias' },
  { label: '债权人', key: 'person' },
  { label: '负债金额', key: 'price', type: 'money' },
  { label: '已还金额', key: 'repayment_price', type: 'money' },
  { label: '状态', key: 'liability_state_alias' },
  { label: '来源', key: 'data_source_alias' },
  { label: '附件', key: '', type: 'slot', slotName: 'images' },
  { label: '备注', key: 'remark', showTooltip: true },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
]

export const SEARCH_REPAYMENT_INFO = {
  select_time: {
    type: 'daterange',
    label: '日期筛选',
    value: getSevenDateRange(7),
    clearable: false
  },
  liability_name: {
    type: 'input',
    value: '',
    label: '还款名称',
    placeholder: '请输入还款名称'
  },
  repayment_name: {
    type: 'input',
    value: '',
    label: '负债名称',
    placeholder: '请输入负债名称'
  },
  operate: {
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入操作员'
  }
}
export const TABLE_REPAYMENT_INFO = [
  { label: '创建时间', key: 'create_time' },
  { label: '还款名称', key: 'name' },
  { label: '负债名称', key: 'liability_name' },
  { label: '还款金额', key: 'price', type: 'money' },
  { label: '附件', key: '', type: 'slot', slotName: 'images' },
  { label: '备注', key: 'remark', showTooltip: true },
  { label: '操作员', key: 'operate' }
]

export const SEARCH_DEBT_STATISTICS = {
  select_time: {
    type: 'monthrange',
    label: '统计月份',
    value: getMonthDateRange(1),
    clearable: false
  }
}
export const TABLE_DEBT_STATISTICS = [
  { label: '月份', key: 'date_str' },
  { label: '组织名称', key: 'org_name' },
  { label: '负债总额', key: 'all_liability_price', type: 'money' },
  {
    key: 'ld',
    label: '流动负债',
    children: [
      {
        key: 'short_borrowing_price',
        type: 'money',
        label: '短期借款'
      },
      {
        key: 'payable_price',
        type: 'money',
        label: '应付账款'
      },
      {
        key: 'pre_harvest_payable_price',
        type: 'money',
        label: '预收账款'
      },
      {
        key: 'other_liability_price',
        type: 'money',
        label: '其他'
      }
    ]
  },
  {
    key: 'fld',
    label: '非流动负债',
    children: [
      {
        key: 'long_payable_price',
        type: 'money',
        label: '长期应付款'
      },
      {
        key: 'long_borrowing_price',
        type: 'money',
        label: '长期借款'
      },
      {
        key: 'non_other_liability_price',
        type: 'money',
        label: '其他'
      }
    ]
  }
]
