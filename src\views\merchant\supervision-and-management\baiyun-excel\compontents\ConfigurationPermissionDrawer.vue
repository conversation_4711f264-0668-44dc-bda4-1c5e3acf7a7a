<template>
  <custom-drawer :show.sync="localVisible" :title="dialogTitle" :loading.sync="loading" :wrapperClosable="false"
    :size="size" :showClose="false" @confirm="handleSave" @cancel="handleClose">
    <div class="m-l-30 ps-origin" v-if="dialogType === 'multi'">
      <div class="ps-red">注：不选择时，则不会修改原管理台账对应的操作员/复核人。</div>
    </div>
    <el-form :model="form" :rules="rules" ref="formRef" label-position="right" label-width="120px" class="m-t-20">
      <el-form-item label="管理台账名称" prop="platformName" v-if="dialogType === 'add' || dialogType === 'edit'">
        <el-select v-model="form.platformName" placeholder="请选择" multiple filterable>
          <el-option v-for="item in platformOptions" :key="item.name" :label="item.value"
            :value="item.name"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="指定操作员" prop="operationPermission">
        <el-select v-model="form.operationPermission" placeholder="请选择" multiple filterable
          @change="handleChange($event, 'opearation')">
          <el-option v-for="item in permissionOptions" :key="item.id" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="复核人">
        <el-select v-model="form.reviewer" placeholder="请选择" multiple filterable
          @change="handleChange($event, 'reviewer')">
          <el-option v-for="item in reviewerOptions" :key="item.id" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>

    </el-form>
  </custom-drawer>
</template>

<script>
import CustomDrawer from '@/components/global/customDrawer'
import { deepClone } from '@/utils'

export default {
  name: 'ConfigurationPermissionDrawer',
  components: {
    CustomDrawer
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: '配置权限'
    },
    dialogType: {
      type: String,
      default: 'add'
    },
    permissionData: {
      type: Object,
      default: () => ({})
    },
    chooseData: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      localVisible: false,
      loading: false,
      form: {
        platformName: [],
        operationPermission: [],
        reviewer: []
      },
      rules: {
        platformName: [
          { required: true, message: '请选择管理台账名称', trigger: 'change' }
        ],
        operationPermission: [
          { required: true, message: '请选择指定操作员', trigger: 'change' }
        ]
      },
      userList: [], // 用户列表
      platformOptions: [],
      permissionOptions: [],
      reviewerOptions: [],
      size: '40%'
    }
  },
  created() {
    this.getUserList()
    this.getLedgerTypeList()
  },
  watch: {
    visible: {
      handler(val) {
        console.log('visible', val)
        this.localVisible = val
        if (val) {
          this.initFormData()
        } else if (!val) {
          this.form = {
            platformName: [],
            operationPermission: [],
            reviewer: []
          }
          this.$refs.formRef && this.$refs.formRef.clearValidate()
        }
        if (this.dialogType === 'multi') {
          this.rules.operationPermission[0].required = false
        } else {
          this.rules.operationPermission[0].required = true
        }
      },
      immediate: true
    },
    localVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    initFormData() {
      if (this.permissionData && Object.keys(this.permissionData).length > 0) {
        let permissIds = []
        if (this.permissionData.ledger_type_list && this.permissionData.ledger_type_list.length > 0) {
          this.platformOptions.forEach(item => {
            if (this.permissionData.ledger_type_list.includes(item.name)) {
              permissIds.push(item.name)
            }
          })
        }
        let operationPermission = this.permissionData.operators_list && this.permissionData.operators_list.map(item => item.id)
        let reviewer = this.permissionData.reviewers_list && this.permissionData.reviewers_list.map(item => item.id)
        this.form = {
          platformName: permissIds || [],
          operationPermission: operationPermission || [],
          reviewer: reviewer || []
        }
      } else {
        this.form = {
          platformName: [],
          operationPermission: [],
          reviewer: []
        }
      }
      this.$refs.formRef && this.$refs.formRef.clearValidate()
    },
    handleClose() {
      this.$emit('close')
    },
    handleSave() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.saveConfigPermission()
        }
      })
    },
    async getUserList() {
      let parmas = {
        status: 1,
        page: 1,
        page_size: 999999,
        organization_ids: [this.$store.getters.organization]
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrganizationAccountListPost(parmas))
      console.log('res', res)
      if (err) {
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        let results = data.results || []
        results = results.map(item => {
          item.label = item.member_name + "(" + item.username + " )"
          return item
        })
        this.userList = deepClone(results)
        this.permissionOptions = deepClone(results)
        this.reviewerOptions = deepClone(results)
      }
    },
    // 获取台账类型列表
    async getLedgerTypeList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundFundSupervisionLedgerLedgerTypeListPost({}))
      console.log('res', res)
      if (err) {
        return
      }
      if (res && res.code === 0) {
        const data = res.data || []
        this.platformOptions = deepClone(data)
      }
    },
    // 保存配置权限
    async saveConfigPermission() {
      let params = {
        operators_ids: this.form.operationPermission,
        reviewers_ids: this.form.reviewer
      }
      if (this.dialogType === 'multi') {
        params.ids = this.chooseData.map(item => item.id)
      } else {
        params.ledger_type_list = this.form.platformName
        if (this.dialogType === 'edit') {
          params.id = this.permissionData.id
        }
      }
      this.loading = true
      let api = this.dialogType === 'multi' ? this.$apis.apiBackgroundFundSupervisionLedgerLedgerPermissionBulkSavePost : this.$apis.apiBackgroundFundSupervisionLedgerLedgerPermissionSavePost
      const [err, res] = await this.$to(api(params))
      this.loading = false
      console.log('res', res)
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        this.$message.success('保存成功')
        this.$emit('confirm', { ...this.form })
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  .el-select {
    width: 100%;
  }
}

.tip-icon {
  margin-left: 10px;
}

.txt-gray {
  color: #3d3b3b;
}
</style>
