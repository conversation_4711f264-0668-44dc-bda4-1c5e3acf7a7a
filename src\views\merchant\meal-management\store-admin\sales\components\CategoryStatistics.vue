<template>
  <div class="CategoryStatistics container-wrapper">
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_store.goods.category_sales_summary_export']">导出EXCEL</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @sort-change="sortChange"
        >
          <table-column
            v-for="item in tableSetting"
            :key="item.key"
            :col="item"
            :sortable="item.sortable"
          >
            <template #index="{ row }">
              <div v-if="row.index === 'slot'">{{ row.sumsName }}</div>
              <div v-else>{{ row.index }}</div>
            </template>
            <template #cost_money="{ row }">
              <span>{{ row.cost_money | formatDate }}</span>
            </template>
            <template #sale_money="{ row }">
              <span>{{ row.sale_money | formatDate }}</span>
            </template>
            <template #sale_profit="{ row }">
              <span>{{ row.sale_profit | formatDate }}</span>
            </template>
            <template #count="{ row }">
              <div>{{ row.count }}</div>
            </template>
          </table-column>
        </el-table>
        <div class="tips p-t-10">注：默认按照销售数量降序</div>
      </div>
      <!-- <div class="tips p-l-20">注：默认按照销售数量升序</div> -->

      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>
<script>
import { debounce, to } from '@/utils'
import { CATEGORY_STATISTICS } from '../../components/constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'CategoryStock',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '销售排行', type: 'slot', slotName: 'index' },
        { label: '分类', key: 'category_name' },
        {
          label: '已销售成本总额',
          key: 'cost_money',
          type: 'money',
          slotName: 'cost_money',
          sortable: 'custom'
        },
        {
          label: '已销售总额',
          key: 'sale_money',
          type: 'money',
          slotName: 'sale_money',
          sortable: 'custom'
        },
        {
          label: '已销售利润',
          key: 'sale_profit',
          type: 'money',
          slotName: 'sale_profit',
          sortable: 'custom'
        },
        { label: '已销售数量', key: 'count', slotName: 'count', sortable: 'custom' }
      ],
      searchFormSetting: CATEGORY_STATISTICS,
      sortData: {}
    }
  },
  created() {
    this.getGoodsCategoryList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getCategorySalesSummary()
    },
    // 监听表头点击获取排序参数
    sortChange(column) {
      let sortType = ''
      if (column.order === 'ascending') {
        sortType = 'asc'
      } else if (column.order === 'descending') {
        sortType = 'desc'
      }
      if (column.order) {
        this.sortData = {
          sort_name: column.prop,
          sort_type: sortType
        }
      } else {
        this.sortData = {}
      }
      this.getCategorySalesSummary()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getCategorySalesSummary()
      }
    }, 300),
    async getGoodsCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategoryListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.goods_category_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getCategorySalesSummary() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategorySalesSummaryPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize,
          ...this.sortData
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = []
        let summaryData = res.data.summary_data
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 如果有值直接push 两个合计进去
        if (this.tableData.length) {
          this.tableData.push(
            {
              index: 'slot',
              sumsName: '当页合计',
              cost_money: summaryData.cost_money,
              sale_money: summaryData.sale_money,
              sale_profit: summaryData.sale_profit,
              count: summaryData.count
            },
            {
              index: 'slot',
              sumsName: '全部合计',
              cost_money: summaryData.total_cost_money,
              sale_money: summaryData.total_sale_money,
              sale_profit: summaryData.total_sale_profit,
              count: summaryData.total_count
            }
          )
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getCategorySalesSummary()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getCategorySalesSummary()
    },
    gotoExport() {
      const option = {
        type: 'ExportCategoryStatistics',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss" scoped>
.CategoryStatistics {
  .tips {
    color: #ff9b45;
  }
}
</style>
