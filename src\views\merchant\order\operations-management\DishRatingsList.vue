<template>
  <div class="AiRetentionInstrument-list container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon v-permission="['background_food.food.food_evaluation_list_export']" color="origin" type="export" @click="gotoExport">导出数据</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoDetail(row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-warn" @click="gotoDetail(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入

export default {
  name: 'DishRatingsList',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '所属组织', key: 'organization_name' },
        { label: '菜品名称', key: 'food_name' },
        { label: '评价数量', key: 'food_count' },
        { label: '整体评分', key: 'evaluation_score' }
        // { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '评价日期',
          format: 'yyyy-MM-dd',
          value: getSevenDateRange(7)
        },
        name: {
          type: 'input',
          value: '',
          label: '菜品名称',
          placeholder: ''
        },
        organization_ids: {
          type: 'organizationSelect',
          value: [],
          label: '所属组织',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        }
      },
      showDialog: false
    }
  },
  created() {
    // this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDishRatingsList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getDishRatingsList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      let [err, res] = await to(this.$apis.apiBackgroundFoodFoodFoodEvaluationListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDishRatingsList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    addTemplateHandle() {
      this.showDialog = true
    },
    // 查看详情
    gotoDetail(data) {
      this.$router.push({
        name: 'MerchantAiRetentionInstrumentDetail',
        query: {
          id: data.id,
          data: this.$encodeQuery(data)
        }
      })
    },
    // 删除
    async deleteHandle(type, data) {
      let params = {}
      if (type === 'single') {
        params.ids = [data.id]
      } else {
      } // 删除多个，原型没有
      if (this.isLoading) return this.$message.error('请勿重复提交！')
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          if (action === 'confirm') {
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundFoodIngredientDeletePost(params))
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.foodIngredientList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 导出弹窗
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      const option = {
        type: 'DishRatingsList',
        url: 'apiBackgroundFoodFoodFoodEvaluationListExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
.AiRetentionInstrument-list {}
</style>
