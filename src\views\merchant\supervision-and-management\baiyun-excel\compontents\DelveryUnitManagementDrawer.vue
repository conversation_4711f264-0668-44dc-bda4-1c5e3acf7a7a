<template>
  <!-- 表26 配送单位管理抽屉 -->
  <el-drawer
    :title="title"
    :visible.sync="drawerVisible"
    size="50%"
    class="ps-el-drawer history-record-drawer"
    :wrapperClosable="false"
    append-to-body
    v-loading="isLoading"
    modal-append-to-body
    @open="handleOpen"
  >
    <div class="delivery-units-form">
      <!-- 配送单位表单列表 -->
      <el-form ref="deliveryForm" :model="form" label-width="auto" class="form-container" style="margin-top: 0">
        <!-- 循环渲染多个配送单位 -->
        <div v-for="(unit, index) in form.deliveryUnits" :key="index" class="delivery-unit">
          <!-- 标题 + 删除按钮 -->
          <div class="unit-header">
            <h3>配送单位{{ index + 1 }}信息</h3>
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(index)"
              v-if="form.deliveryUnits.length > 1"
            >
              删除
            </el-button>
          </div>

          <!-- 表单内容 -->
          <el-row :gutter="20">
            <!-- 配送单位名称 -->
            <el-col :span="11">
              <el-form-item
                label="配送单位名称"
                :rules="rules.delivery_unit_name"
                :prop="getProp(index, 'delivery_unit_name')"
              >
                <el-input v-model="unit.delivery_unit_name" maxlength="40" clearable placeholder="请输入" />
              </el-form-item>
            </el-col>

            <!-- 营业执照号码 -->
            <el-col :span="11">
              <el-form-item
                label="营业执照号码"
                :rules="rules.business_license"
                :prop="getProp(index, 'business_license')"
              >
                <el-input v-model="unit.business_license" maxlength="20" clearable placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 食品经营许可证 -->
            <el-col :span="11">
              <el-form-item label="食品经营许可证" :rules="rules.aptitude_name" :prop="getProp(index, 'aptitude_name')">
                <el-input v-model="unit.aptitude_name" maxlength="20" clearable placeholder="请输入" />
              </el-form-item>
            </el-col>

            <!-- 许可证有效期至 -->
            <el-col :span="11">
              <el-form-item label="许可证有效期至" :rules="rules.end_date" :prop="getProp(index, 'end_date')">
                <el-date-picker
                  v-model="unit.end_date"
                  type="date"
                  placeholder="请选择有效期截止时间"
                  value-format="yyyy-MM-dd"
                  style="width: 270px"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 配送合同起止时间 -->
            <el-col :span="11">
              <el-form-item
                label="配送合同起止时间"
                :rules="rules.delivery_contract_time"
                :prop="getProp(index, 'delivery_contract_time')"
              >
                <el-date-picker
                  v-model="unit.delivery_contract_time"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  style="width: 270px"
                />
              </el-form-item>
            </el-col>

            <!-- 经营地址 -->
            <el-col :span="11">
              <el-form-item label="经营地址" :rules="rules.address" :prop="getProp(index, 'address')">
                <el-input v-model="unit.address" maxlength="20" clearable placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 法定代表人 -->
            <el-col :span="11">
              <el-form-item label="法定代表人" :rules="rules.contact_name" :prop="getProp(index, 'contact_name')">
                <el-input v-model="unit.contact_name" maxlength="20" clearable placeholder="请输入" />
              </el-form-item>
            </el-col>

            <!-- 法定代表人电话 -->
            <el-col :span="11">
              <el-form-item label="联系电话" :rules="rules.contact_Phone" :prop="getProp(index, 'contact_Phone')">
                <el-input
                  v-model="unit.contact_Phone"
                  maxlength="20"
                  clearable
                  placeholder="请输入法定代表人联系号码"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 食品安全管理员 -->
            <el-col :span="11">
              <el-form-item
                label="食品安全管理员"
                :rules="rules.food_safety_admin"
                :prop="getProp(index, 'food_safety_admin')"
              >
                <el-input v-model="unit.food_safety_admin" maxlength="20" clearable placeholder="请输入" />
              </el-form-item>
            </el-col>

            <!-- 食品安全管理员电话 -->
            <el-col :span="11">
              <el-form-item
                label="联系电话"
                :rules="rules.food_safety_admin_Phone"
                :prop="getProp(index, 'food_safety_admin_Phone')"
              >
                <el-input
                  v-model="unit.food_safety_admin_Phone"
                  maxlength="20"
                  clearable
                  placeholder="请输入食品安全管理员联系号码"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 配送负责人 -->
            <el-col :span="11">
              <el-form-item
                label="配送负责人"
                :rules="rules.delivery_manager"
                :prop="getProp(index, 'delivery_manager')"
              >
                <el-input v-model="unit.delivery_manager" maxlength="20" clearable placeholder="请输入" />
              </el-form-item>
            </el-col>

            <!-- 配送负责人电话 -->
            <el-col :span="11">
              <el-form-item
                label="联系电话"
                :rules="rules.delivery_manager_Phone"
                :prop="getProp(index, 'delivery_manager_Phone')"
              >
                <el-input
                  v-model="unit.delivery_manager_Phone"
                  maxlength="20"
                  clearable
                  placeholder="请输入配送负责人联系号码"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 新增按钮 -->
        <el-row style="margin-top: 20px">
          <el-col :span="24" class="text-center">
            <el-button plain @click="handleAdd" class="add-but" style="color: #ff9b45; border: 2rpx dashed #f59a23">
              添 加
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 底部操作区 -->
    <div class="ps-el-drawer-footer">
      <el-button size="small" class="w-100" @click="drawerVisible = false">关闭</el-button>
      <el-button size="small" class="w-100" type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { to } from '@/utils'
export default {
  name: 'DelveryUnitManagementDrawer',
  props: {
    // 弹窗标题
    title: {
      type: String,
      default: '配送单位管理'
    },
    // 弹窗显示状态
    visible: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  data() {
    return {
      isLoading: false,
      historyDialogVisible: false,
      form: {
        // 配送单位列表，默认至少 1 个
        deliveryUnits: [
          {
            delivery_unit_name: '', // 配送单位名称
            business_license: '', // 营业执照号码
            aptitude_name: '', // 食品经营许可证
            end_date: null, // 许可证有效期至
            delivery_contract_time: null, // 配送合同起止时间
            address: '', // 经营地址
            contact_name: '', // 法定代表人
            contact_Phone: '', // 联系电话
            food_safety_admin: '', // 食品安全管理员
            food_safety_admin_Phone: '', // 联系电话
            delivery_manager: '', // 配送负责人
            delivery_manager_Phone: '' // 联系电话
          }
        ]
      },
      rules: {
        delivery_unit_name: [{ required: true, message: '请输入配送单位名称', trigger: 'blur' }],
        business_license: [{ required: true, message: '请输入营业执照号码', trigger: 'blur' }],
        aptitude_name: [{ required: true, message: '请输入食品经营许可证', trigger: 'blur' }],
        end_date: [{ required: true, message: '请选择许可证有效期', trigger: 'change' }],
        delivery_contract_time: [{ required: true, message: '请选择配送合同起止时间', trigger: 'change' }],
        address: [{ required: true, message: '请输入经营地址', trigger: 'blur' }],
        contact_name: [{ required: true, message: '请输入法定代表人', trigger: 'blur' }],
        contact_Phone: [
          { required: true, message: '请输入法定代表人电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        food_safety_admin: [{ required: true, message: '请输入食品安全管理员', trigger: 'blur' }],
        food_safety_admin_Phone: [
          { required: true, message: '请输入食品安全管理员电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        delivery_manager: [{ required: true, message: '请输入配送负责人', trigger: 'blur' }],
        delivery_manager_Phone: [
          { required: true, message: '请输入配送负责人电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    // 抽屉打开
    handleOpen() {
      this.$nextTick(async () => {
        await this.getConfigList()
        this.$refs.deliveryForm.resetFields()
      })
    },
    // 新增配送单位
    handleAdd() {
      this.form.deliveryUnits.push({
        delivery_unit_name: '', // 配送单位名称
        business_license: '', // 营业执照号码
        aptitude_name: '', // 食品经营许可证
        end_date: null, // 许可证有效期至
        delivery_contract_time: null, // 配送合同起止时间
        address: '', // 经营地址
        contact_name: '', // 法定代表人
        contact_Phone: '', // 联系电话
        food_safety_admin: '', // 食品安全管理员
        food_safety_admin_Phone: '', // 联系电话
        delivery_manager: '', // 配送负责人
        delivery_manager_Phone: '' // 联系电话
      })
    },
    // 删除配送单位
    handleDelete(index) {
      this.form.deliveryUnits.splice(index, 1)
    },
    // 保存表单
    handleSubmit() {
      this.$refs.deliveryForm.validate(valid => {
        if (valid) {
          // 验证通过，检查是否有重复项
          const isDuplicate = this.checkDuplicateFields()
          if (isDuplicate) {
            return false
          }
          this.saveData()
          // this.drawerVisible = false
        } else {
          // 验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    // 检查配送单位名称、营业执照号码、食品经营许可证是否重复
    checkDuplicateFields() {
      const units = this.form.deliveryUnits

      // 检查配送单位名称是否重复
      const deliveryUnitNames = units.map(unit => unit.delivery_unit_name).filter(name => name)
      const uniqueDeliveryUnitNames = [...new Set(deliveryUnitNames)]
      if (deliveryUnitNames.length !== uniqueDeliveryUnitNames.length) {
        this.$message.error('配送单位名称不能重复')
        return true
      }

      // 检查营业执照号码是否重复
      const businessLicenses = units.map(unit => unit.business_license).filter(license => license)
      const uniqueBusinessLicenses = [...new Set(businessLicenses)]
      if (businessLicenses.length !== uniqueBusinessLicenses.length) {
        this.$message.error('营业执照号码不能重复')
        return true
      }

      // 检查食品经营许可证是否重复
      const aptitudeNames = units.map(unit => unit.aptitude_name).filter(name => name)
      const uniqueAptitudeNames = [...new Set(aptitudeNames)]
      if (aptitudeNames.length !== uniqueAptitudeNames.length) {
        this.$message.error('食品经营许可证不能重复')
        return true
      }

      return false
    },
    // 保存数据
    async saveData() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyModifyLedgerItemConfig({
          ledger_data_type: 'DeliveryUnit',
          ledger_item_config: this.form.deliveryUnits
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '保存失败')
        return
      }
      if (res && res.code === 0) {
        this.$message.success('保存成功')
        this.drawerVisible = false
        // this.$emit('confirm', this.formData.infoData)
      } else {
        this.$message.error(res.msg || '保存失败')
      }
    },
    getProp(index, key) {
      return 'deliveryUnits.' + index + `.${key}`
    },
    // 获取配置项
    getConfigList() {
      let params = {
        ledger_data_type: 'DeliveryUnit'
      }
      this.$apis
        .apiBackgroundFundSupervisionLedgerFoodSafetyGetLedgerItemConfig(params)
        .then(res => {
          if (res && res.code === 0) {
            let data = res.data || []
            if (data && data.length > 0) {
              const tag = data[0] || {}
              const ledgerItemConfig = tag.ledger_item_config || []
              if (ledgerItemConfig && ledgerItemConfig.length > 0) {
                // this.form.deliveryUnits = ledgerItemConfig
                this.form.deliveryUnits = []
                ledgerItemConfig.map(item => {
                  this.form.deliveryUnits.push({
                    delivery_unit_name: item.delivery_unit_name,
                    business_license: item.business_license,
                    aptitude_name: item.aptitude_name,
                    end_date: item.end_date,
                    delivery_contract_time: item.delivery_contract_time,
                    address: item.address,
                    contact_name: item.contact_name,
                    contact_Phone: item.contact_Phone,
                    food_safety_admin: item.food_safety_admin,
                    food_safety_admin_Phone: item.food_safety_admin_Phone,
                    delivery_manager: item.delivery_manager,
                    delivery_manager_Phone: item.delivery_manager_Phone
                  })
                })
              }
            }
          } else {
            this.$message.error(res.msg || '获取配置项失败')
          }
        })
        .catch(err => {
          this.$message.error(err.message || '获取配置项失败')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
/* 表单整体容器 */
.delivery-units-form {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 单个配送单位容器 */
.delivery-unit {
  background: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 20px;
}

/* 标题 + 删除按钮 */
.unit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.unit-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 按钮样式微调 */
.el-button--text {
  color: #f56c6c;
}

/* 表单间距优化 */
.el-form-item {
  margin-bottom: 16px;
}

/* 居中对齐 */
.text-center {
  text-align: center;
}
.add-but {
  width: 100%;
}
// 底部操作区样式
.ps-el-drawer-footer {
  position: sticky;
  bottom: 0;
  padding: 20px;
  background-color: #fff;
  z-index: 10;
}
</style>
