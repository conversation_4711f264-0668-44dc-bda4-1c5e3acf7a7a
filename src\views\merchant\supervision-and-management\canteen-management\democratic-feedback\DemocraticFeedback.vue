<template>
  <!-- 晨检记录-->
  <div class="morning-wrapper container-wrapper">
    <div class="tab-box">
      <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
        <el-radio-button v-for="tab in tabTypeList" :key="tab.value" :label="tab.value"
          v-permission="[tab.permissions]">{{ tab.label }}</el-radio-button>
      </el-radio-group>
    </div>
    <div class="tab-item m-t-20">
      <search-form ref="searchRef" :loading="isLoading" @search="searchHandle" :form-setting="searchFormSetting"
        @reset="resetHandler" label-width="120px" :autoSearch="false"></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <div v-if="this.tabType !== 'survey'">
              <el-button size="mini" @click="showQrCode">反馈二维码</el-button>
              <button-icon color="plain" @click="gotoExport('')" v-permission="['background_fund_supervision.channel_canteen_management.democratic_feedback_list_export']">导出</button-icon>
              <button-icon color="plain" @click="gotoPrint('')">打印</button-icon>
            </div>
            <div v-else>
              <button-icon color="origin" @click="openDrawer('add')">新增问卷</button-icon>
              <button-icon color="plain" @click="openDrawer('history')">历史记录</button-icon>
              <button-icon color="plain" @click="gotoExport('survey')">导出</button-icon>
              <button-icon color="plain" @click="gotoPrint('survey')">打印</button-icon>
            </div>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe
            header-row-class-name="ps-table-header-row" empty-text="暂无数据，请查询">
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #images="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="handleClick(row)" :disabled="!row.images.length">查看</el-button>
              </template>
              <template #operation="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="openDrawer('recover', row)" :disabled="!allPermissions.includes('background_fund_supervision.channel_canteen_management.democratic_feedback_reply')">回复</el-button>
              </template>
              <template #enable="{ row }">
                <el-switch
                  v-model="row.enable" @change="handleEnableChange(row)">
                </el-switch>
              </template>
              <template #operationForSurvey="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="openDrawer('justShow', row)">详情</el-button>
                <el-button type="text" size="small" class="ps-text" v-if="row.visible_user.includes(2)" @click="handleLink(row)">链接</el-button>
                <el-button type="text" size="small" class="ps-text" v-if="row.visible_user.includes(2)" @click="handleQrCode(row)">二维码</el-button>
                <el-button type="text" size="small" class="ps-text" v-if="!row.enable && !row.answered_num" @click="openDrawer('edit', row)">编辑</el-button>
                <el-button type="text" size="small" class="ps-text" @click="gotoPath(row.id)">数据明细</el-button>
                <el-button type="text" size="small" class="ps-text" v-if="!row.answered_num && !row.enable" @click="deleteHandle(row)">删除</el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize" :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount"></pagination>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'回复'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="drawerFormRef" :model="drawerForm" label-width="100px" label-position="right">
            <el-form-item label="回复内容：" :rules="{ required: true, message: '请输入回复内容', trigger: ['blur', 'change'] }">
              <el-input v-model="drawerForm.replyContent" type="textarea" :autosize="{ minRows: 4, maxRows: 8}" maxlength="50" show-word-limit></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="cancelHandle('drawerShow')">取消</el-button>
            <el-button type="primary" class="w-100" @click="confirmHandle('drawerShow')">确认</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="computedDrawerTitle"
        :visible="surveyDrawerShow"
        :show-close="false"
        size="45%"
        trap-focus>
        <div class="p-20">
          <el-form class="survey-drawer-form" v-if="surveyDrawerShow" ref="surveyDrawerFormRef" :model="surveyDrawerForm" label-position="top" :rules="surveyDrawerFormRules" v-loading="surveyDrawerFromLoading">
            <el-form-item prop="name">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-10">
                  <span class="red m-r-5">*</span>
                  <span class="f-w-700">问卷名称</span>
                </div>
                <el-input v-model="surveyDrawerForm.name" maxlength="30" :disabled="notToEdit"></el-input>
              </div>
            </el-form-item>
            <el-form-item prop="overview">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-10">
                  <span class="red m-r-5">*</span>
                  <span class="f-w-700">概述</span>
                </div>
                <el-input v-model="surveyDrawerForm.overview" maxlength="50" :disabled="notToEdit"></el-input>
              </div>
            </el-form-item>
            <el-form-item prop="visible_user">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-25">
                  <span class="red m-r-5">*</span>
                  <span class="f-w-700">可见用户</span>
                </div>
                <div class="flex flex-left flex-align-c">
                  <el-checkbox-group v-model="surveyDrawerForm.visible_user" :disabled="notToEdit" @change="handleVisibleUserChange">
                    <el-checkbox :label="0">内部用户</el-checkbox>
                    <el-checkbox :label="1"> 商户管理员</el-checkbox>
                    <el-checkbox :label="2">其他（仅支持匿名提交）</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="issuing_org_ids" v-if="!(surveyDrawerForm.visible_user.length === 1 && surveyDrawerForm.visible_user.includes(2))">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-10">
                  <span class="red m-r-5">*</span>
                  <span class="f-w-700">下发组织</span>
                </div>
                <organization-select
                  placeholder="请选择下发组织"
                  class="w-100-p"
                  :isLazy="false"
                  :multiple="true"
                  :check-strictly="true"
                  v-model="surveyDrawerForm.issuing_org_ids"
                  :defaultExpandAll="true"
                  :disabled="notToEdit"
                ></organization-select>
              </div>
            </el-form-item>
            <el-form-item prop="role_ids" v-if="surveyDrawerForm.visible_user.includes(1)">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-10">
                  <span class="red m-r-5">*</span>
                  <span class="f-w-700">可见角色</span>
                </div>
                <el-select v-model="surveyDrawerForm.role_ids" multiple collapse-tags placeholder="请选择" class="w-100-p" :disabled="notToEdit || !surveyDrawerForm.issuing_org_ids.length">
                  <el-option
                    v-for="(item, index) in roleList"
                    :key="index"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item prop="card_user_group_ids" v-if="surveyDrawerForm.visible_user.includes(0)">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-10">
                  <span class="red m-r-5">*</span>
                  <span class="f-w-700">可见分组</span>
                </div>
                <el-select v-model="surveyDrawerForm.card_user_group_ids" multiple collapse-tags placeholder="请选择" class="w-100-p" :disabled="notToEdit || !surveyDrawerForm.issuing_org_ids.length">
                  <el-option
                    v-for="(item, index) in userGroupList"
                    :key="index"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item prop="validity">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-10">
                  <span style="width: 1em;"></span>
                  <span class="f-w-700">有效期</span>
                </div>
                <el-date-picker
                  v-model="surveyDrawerForm.validity"
                  :disabled="notToEdit"
                  class="w-100-p"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </div>
            </el-form-item>
            <el-form-item prop="write_limit">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-25">
                  <span class="red m-r-5">*</span>
                  <span class="f-w-700">填写限制</span>
                </div>
                <!-- <el-radio-group v-model="surveyDrawerForm.write_limit" :disabled="notToEdit" v-removeAriaHidden>
                  <el-radio :label="0">无限制</el-radio>
                  <el-radio :label="1">每个用户仅支持填写一次</el-radio>
                </el-radio-group> -->
                <div class="flex flex-align-c">
                  <span style="width: 7em">同个用户可填写</span>
                  <div class="w-80 m-l-5 m-r-5">
                    <el-input
                      v-model="surveyDrawerForm.write_limit"
                      placeholder="请填写"
                      maxlength="5"
                      :disabled="notToEdit"
                    />
                  </div>
                  <span>次</span>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="commit_type">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-25">
                  <span class="red m-r-5">*</span>
                  <span class="f-w-700">提交方式</span>
                </div>
                <el-radio-group v-model="surveyDrawerForm.commit_type" :disabled="notToEdit" v-removeAriaHidden>
                  <el-radio :label="'anonymous'">匿名提交</el-radio>
                  <el-radio :label="'real_name'" v-if="surveyDrawerForm.visible_user.length === 0 || (surveyDrawerForm.visible_user.includes(0) || surveyDrawerForm.visible_user.includes(1))">实名提交</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
            <el-form-item prop="upper_limit">
              <div class="flex flex-left flex-align-c">
                <div class="flex flex-right flex-align-c w-120 p-r-10">
                  <span class="red m-r-5">*</span>
                  <span class="f-w-700">问卷收集上限</span>
                </div>
                <el-input v-model="surveyDrawerForm.upper_limit" maxlength="5" :disabled="notToEdit">
                  <template #append>
                    <span>份</span>
                  </template>
                </el-input>
              </div>
            </el-form-item>
            <el-form-item prop="questions">
              <Questionnaire class="questionnaire-form" ref="questionnaireRef" :drawerShow.sync="surveyDrawerShow" :questionsList.sync="surveyDrawerForm.questions" :disabled.sync="notToEdit"></Questionnaire>
            </el-form-item>
          </el-form>
          <!-- <el-divider></el-divider> -->
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="cancelHandle('surveyDrawerShow')">取消</el-button>
            <el-button type="primary" class="w-100" @click="gotoPreview">预览</el-button>
            <el-button type="primary" class="w-100" v-if="!notToEdit" @click="confirmHandle('surveyDrawerShow', false)">保存</el-button>
            <el-button type="primary" class="w-100" v-if="!notToEdit" @click="confirmHandle('surveyDrawerShow', true)">保存并发布</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'历史记录'"
        :visible="historyDrawerShow"
        :show-close="false"
        size="80%">
        <div class="p-20">
          <history-data ref="historyDrawerRef" :show.sync="historyDrawerShow" />
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="cancelHandle('historyDrawerShow')">关闭</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogShow"
      width="20%"
      center>
      <div class="w-100-p flex-center">
        <qrcode :value="qrUrl" :options="{ width: 250 }" :margin="10" alt />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary"  @click="dialogShow = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { SEARCH_SETTING_CANTEEN, SEARCH_SETTING_SUPPLIER, TABLE_HEAD_DATA_CANTEEN, TABLE_HEAD_DATA_SUPPLIER, SEARCH_SETTING_SURVEY, TABLE_HEAD_DATA_SURVEY } from './constants'
import OrganizationSelect from '@/components/OrganizationSelect'
import Questionnaire from '@/components/Questionnaire'
import qrcode from '@chenfengyuan/vue-qrcode'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { mapGetters } from 'vuex'
import { deepClone, setSessionStorage } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import HistoryData from './HistoryData.vue'
import dayjs from 'dayjs'

export default {
  components: {
    ElImageViewer,
    qrcode,
    OrganizationSelect,
    Questionnaire,
    HistoryData
  },
  mixins: [exportExcel],
  name: 'DemocraticFeedback',
  data() {
    return {
      isLoading: false,
      tabType: 'survey',
      tabTypeList: [
        { label: '民主反馈-食堂', value: 'canteen', permissions: 'merchant_supervision' },
        { label: '民主反馈-供应商', value: 'supplier', permissions: 'merchant_supervision' },
        { label: '问卷测评', value: 'survey', permissions: 'merchant_supervision' }
      ],
      supplierList: [],
      tableSetting: deepClone(TABLE_HEAD_DATA_CANTEEN),
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: deepClone(SEARCH_SETTING_CANTEEN),
      drawerShow: false,
      selectId: '',
      drawerForm: {
        replyContent: ''
      },
      approveMethod: '',
      showImagePreview: false,
      previewList: [],
      printType: 'DemocraticFeedback',
      qrUrl: '',
      dialogShow: false,
      surveyDrawerShow: false,
      surveyDrawerType: '',
      historyDrawerShow: false,
      selectedId: '',
      visibleTypeList: [
        {
          key: 'NEIBU',
          label: '内部用户',
          value: 0
        },
        {
          key: 'SHANGHU',
          label: '商户管理员',
          value: 1
        },
        {
          key: 'QITA',
          label: '其他（仅支持匿名提交）',
          value: 2
        }
      ],
      surveyDrawerForm: {
        name: '',
        overview: '',
        validity: [],
        visible_user: [],
        issuing_org_ids: [],
        role_ids: [],
        card_user_group_ids: [],
        write_limit: '',
        upper_limit: '',
        commit_type: 'real_name',
        questions: [],
        neibu: false,
        shanghu: false,
        qita: false
      },
      surveyDrawerFormRules: {
        name: [
          { required: true, message: '请填写', trigger: ['blur', 'change'] },
          { pattern: /^.*\S+.*$/, message: "不能输入空格", trigger: ["blur", "change"] }
        ],
        overview: [
          { required: true, message: '请填写', trigger: ['blur', 'change'] },
          { pattern: /^.*\S+.*$/, message: "不能输入空格", trigger: ["blur", "change"] }
        ],
        visible_user: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        issuing_org_ids: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        role_ids: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        card_user_group_ids: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        write_limit: [
          { required: true, message: '请填写', trigger: ['blur', 'change'] },
          { pattern: /^[1-9]\d*$/, message: "请填写正整数", trigger: ['blur', 'change'] }
        ],
        upper_limit: [
          { required: true, message: '请填写', trigger: ['blur', 'change'] },
          { pattern: /^-?\d+$/, message: '只能输入整数', trigger: ['blur', 'change'] }
        ],
        commit_type: [{ required: true, message: '请填写', trigger: ['blur', 'change'] }],
        questions: [{ required: true, message: '请填写', trigger: ['blur', 'change'] }]
      },
      surveyDrawerFromLoading: false,
      roleList: [],
      userGroupList: [],
      notToEdit: false,
      dialogTitle: ""
    }
  },
  watch: {
    // 下发组织
    // 下发组织
    "surveyDrawerForm.issuing_org_ids": {
      handler: function (newVal) {
        console.log('surveyDrawerForm.issuing_org_ids', newVal)
        if ((newVal && newVal.length) || newVal.length === 0) {
          // 以下这样写能清空  但失去了响应式 换$set
          // this.surveyDrawerForm.role_ids = []
          // this.surveyDrawerForm.card_user_group_ids = []
          this.$set(this.surveyDrawerForm, 'role_ids', [])
          this.$set(this.surveyDrawerForm, 'card_user_group_ids', [])
          this.$nextTick(() => {
            this.getRoleList(newVal)
            this.getUserGroupList(newVal)
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'organization', 'allPermissions']),
    // 判断是不是日期是同一天，同一天才展示，不是同一天不展示
    getIsDateRange() {
      try {
        let startDate = this.searchFormSetting.select_time.value[0]
        let endDate = this.searchFormSetting.select_time.value[1]
        return startDate === endDate
      } catch (error) {
        return false
      }
    },
    computedDrawerTitle() {
      let str = ''
      switch (this.surveyDrawerType) {
        case 'add':
          str = '新建'
          break
        case 'edit':
          str = '编辑'
          break
        case 'detail':
          str = '详情'
          break
      }
      return str
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getSupplierList()
      this.initTabList()
    },
    // 初始页面权限
    initTabList() {
      let result = []
      this.tabTypeList.forEach(v => {
        if (this.allPermissions.includes(v.permissions)) {
          result.push(v)
        }
      })
      this.tabTypeList = result
      this.tabType = this.tabTypeList.length ? this.tabTypeList[0].value : ''
      this.setTabDataHandle(this.tabType)
    },
    resetHandler() {
      this.currentPage = 1
      this.setTabDataHandle(this.tabType)
    },
    async refreshHandle() {
      this.currentPage = 1
      this.initTabList()
    },
    // 筛选
    async searchHandle(e) {
      this.currentPage = 1
      this.getDataList(this.tabType)
    },
    // 切换tab
    changeTabHandle(e) {
      this.setTabDataHandle(e)
    },
    // 设置tab数据
    setTabDataHandle(e) {
      switch (e) {
        case 'canteen': {
          this.searchFormSetting = deepClone(SEARCH_SETTING_CANTEEN)
          this.tableSetting = deepClone(TABLE_HEAD_DATA_CANTEEN)
          break
        }
        case 'supplier': {
          this.searchFormSetting = deepClone(SEARCH_SETTING_SUPPLIER)
          this.tableSetting = deepClone(TABLE_HEAD_DATA_SUPPLIER)
          this.searchFormSetting.supplier_manage_ids.dataList = deepClone(this.supplierList)
          break
        }
        case 'survey': {
          this.searchFormSetting = deepClone(SEARCH_SETTING_SURVEY)
          this.tableSetting = deepClone(TABLE_HEAD_DATA_SURVEY)
          break
        }
      }
      this.tableData = []
      this.getDataList(e)
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value !== '全部' && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        } else {
          params[key] = undefined
        }
      }
      return params
    },
    // 获取供应商列表
    async getSupplierList() {
      let params = {
        page: 1,
        page_size: 9999
      }
      const res = await this.$apis.apiBackgroundDrpSupplierManageListPost(params)
      if (res.code === 0) {
        this.supplierList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取角色列表
    async getRoleList(idList) {
      this.roleList = []
      const res = await this.$apis.apiBackgroundOrganizationRoleListPost({
        organization__in: idList,
        page: 1,
        page_size: 9999
      })
      if (res.code === 0) {
        let newList = []
        let results = res.data?.results || []
        results.forEach(item => {
          if (item.status === 1) {
            let obj = {
              label: item.name,
              value: item.id
            }
            newList.push(obj)
          }
        })
        this.roleList = deepClone(newList)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取用户分组
    async getUserGroupList(idList) {
      this.userGroupList = []
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        org_ids: idList,
        page: 1,
        page_size: 9999
      })
      if (res.code === 0) {
        let newList = []
        let results = res.data?.results || []
        results.forEach(item => {
          if (item.status === 'enable') {
            let obj = {
              label: item.group_name,
              value: item.id
            }
            newList.push(obj)
          }
        })
        this.userGroupList = deepClone(newList)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 请求列表数据
    async getDataList(type) {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        feedback_type: '',
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (type !== 'survey') {
        params.feedback_type = this.tabType === 'canteen' ? 'org' : 'supplier'
      } else {
        params.feedback_type = undefined
      }
      let res = {}
      if (type !== 'survey') {
        res = await this.$apis.apiBackgroundFundSupervisionChannelCanteenManagementDemocraticFeedbackListPost(params)
      } else {
        res = await this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireListPost(params)
      }
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        this.tableData = deepClone(data.results)
        if (this.$refs.tableData) {
          this.$refs.tableData.doLayout()
        }
      } else {
        this.$message.error(res.msg)
        this.tableData = [{
          key: 1
        }]
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList(this.tabType)
    },
    async openDrawer(type, data) {
      switch (type) {
        case 'recover': {
          this.selectId = data.id
          this.drawerShow = true
          break
        }
        case 'history': {
          this.historyDrawerShow = true
          break
        }
        default : {
          this.surveyDrawerType = type
          if (this.surveyDrawerType === 'edit') {
            this.notToEdit = false
            await this.getQuestions(data.id)
          } else if (this.surveyDrawerType === 'justShow') {
            this.notToEdit = true
            await this.getQuestions(data.id)
          } else {
            this.notToEdit = false
            this.surveyDrawerForm.name = ''
            this.surveyDrawerForm.overview = ''
            this.surveyDrawerForm.validity = []
            this.surveyDrawerForm.visible_user = []
            this.surveyDrawerForm.neibu = false
            this.surveyDrawerForm.shanghu = false
            this.surveyDrawerForm.qita = false
            this.surveyDrawerForm.issuing_org_ids = []
            this.surveyDrawerForm.role_ids = []
            this.surveyDrawerForm.card_user_group_ids = []
            this.surveyDrawerForm.write_limit = ''
            this.surveyDrawerForm.upper_limit = ''
            this.surveyDrawerForm.commit_type = 'real_name'
            this.surveyDrawerForm.questions = []
          }
          setTimeout(() => {
            this.$refs.surveyDrawerFormRef.clearValidate()
          }, 10)
          this.surveyDrawerShow = true
          break
        }
      }
    },
    async getQuestions(id) {
      this.surveyDrawerFromLoading = true
      const res = await this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireDetailPost({
        id: id
      })
      if (res.code === 0) {
        let data = res.data
        this.selectedId = data.id
        this.surveyDrawerForm.name = data.name
        this.surveyDrawerForm.overview = data.overview
        this.surveyDrawerForm.validity = data.effective_time && data.expiration_time ? [dayjs(data.effective_time).format('YYYY-MM-DD'), dayjs(data.expiration_time).format('YYYY-MM-DD')] : []
        // this.surveyDrawerForm.visible_user = data.visible_user
        // 处理一下visible_user
        this.surveyDrawerForm.visible_user = data.visible_user || []
        // data.visible_user.forEach(item => {
        //   switch (item) {
        //     case 0:
        //       this.surveyDrawerForm.neibu = true
        //       break
        //     case 1:
        //       this.surveyDrawerForm.shanghu = true
        //       break
        //     case 2:
        //       this.surveyDrawerForm.qita = true
        //       break
        //   }
        // })
        this.surveyDrawerForm.issuing_org_ids = data.issuing_org_ids
        this.surveyDrawerForm.write_limit = data.write_limit
        this.surveyDrawerForm.upper_limit = data.upper_limit
        this.surveyDrawerForm.commit_type = data.commit_type
        // 必须加延迟，不然监听到issuing_org_ids变化后会置空
        this.$nextTick(() => {
          this.surveyDrawerForm.card_user_group_ids = data.card_user_group_ids
          this.surveyDrawerForm.role_ids = data.roles_ids
          // this.$refs.surveyDrawerFormRef.clearValidate()
        })
        this.surveyDrawerForm.questions = data.questions.map(item => {
          let obj = {
            question_type: item.question_type,
            order_in_list: item.order_in_list
          }
          console.log('item.question_type', this.surveyDrawerForm)
          switch (item.question_type) {
            case 0: {
              Object.assign(obj, {
                caption: item.caption,
                choices: [],
                required: item.required
              })
              obj.choices = item.choices.map(itemIn => {
                let obj = {
                  type: itemIn.other_content ? 'other' : 'default',
                  description: itemIn.description,
                  other_content: itemIn.other_content,
                  multi_choice: itemIn.multi_choice,
                  order_in_list: itemIn.order_in_list
                }
                return obj
              })
              break
            }
            case 1: {
              Object.assign(obj, {
                caption: item.caption,
                choices: [],
                least_choose_count: item.least_choose_count,
                required: item.required
              })
              obj.choices = item.choices.map(itemIn => {
                let obj = {
                  type: itemIn.other_content ? 'other' : 'default',
                  description: itemIn.description,
                  other_content: itemIn.other_content,
                  multi_choice: itemIn.multi_choice,
                  order_in_list: itemIn.order_in_list
                }
                return obj
              })
              break
            }
            case 2: {
              Object.assign(obj, {
                caption: item.caption,
                top_score: item.top_score,
                required: item.required
              })
              break
            }
            case 3: {
              Object.assign(obj, {
                caption: item.caption,
                choices: [],
                top_score: 5,
                required: item.required
              })
              obj.choices = item.choices.map(itemIn => {
                let obj = {
                  description: itemIn.description,
                  order_in_list: itemIn.order_in_list
                }
                return obj
              })
              break
            }
            case 4: {
              Object.assign(obj, {
                caption: item.caption,
                required: item.required
              })
              break
            }
            case 5: {
              Object.assign(obj, {
                caption: item.caption,
                upload_max_num: item.upload_max_num,
                required: item.required
              })
              break
            }
            case 6: {
              Object.assign(obj, {
                caption: item.caption,
                upload_max_num: item.upload_max_num,
                required: item.required
              })
              break
            }
          }
          return obj
        })
        console.log('surveyDrawerForm', this.surveyDrawerForm)
      } else {
        this.$message.error(res.msg)
      }
      this.surveyDrawerFromLoading = false
    },
    cancelHandle(type) {
      switch (type) {
        case 'drawerShow':
          this.drawerForm.replyContent = ''
          this.$refs.drawerFormRef.clearValidate()
          this.drawerShow = false
          break
        case 'surveyDrawerShow':
          this.$refs.surveyDrawerFormRef.resetFields()
          this.surveyDrawerShow = false
          break
        case 'historyDrawerShow':
          this.$refs.historyDrawerRef.$refs.searchRef.resetForm()
          this.historyDrawerShow = false
      }
    },
    confirmHandle(type, status) {
      switch (type) {
        case 'drawerShow':
          this.replyHandle()
          break
        case 'surveyDrawerShow':
          if (this.surveyDrawerType === 'add') {
            this.addSurveyHandle(status)
          } else {
            this.editSurveyHandle(status)
          }
      }
    },
    replyHandle() {
      this.$refs.drawerFormRef.validate(valid => {
        if (valid) {
          let params = {
            id: this.selectId,
            reply_remark: this.drawerForm.replyContent
          }
          this.$apis.apiBackgroundFundSupervisionChannelCanteenManagementDemocraticFeedbackReplyPost(params).then(res => {
            if (res.code === 0) {
              this.$message.success('操作成功')
              this.cancelHandle('drawerShow')
              this.getDataList(this.tabType)
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    async addSurveyHandle(status) {
      let arr = await this.$refs.questionnaireRef.checkQuestionnaire()
      if (arr.every(item => item)) {
        this.$refs.surveyDrawerFormRef.validate(valid => {
          if (valid) {
            let params = {
              enable: status,
              ...this.surveyDrawerForm,
              effective_time: this.surveyDrawerForm.validity.length ? this.surveyDrawerForm.validity[0] + ' 00:00:00' : undefined,
              expiration_time: this.surveyDrawerForm.validity.length ? this.surveyDrawerForm.validity[1] + ' 23:59:59' : undefined
            }
            this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireAddPost(params).then(res => {
              if (res.code === 0) {
                this.$message.success('新增成功')
                this.cancelHandle('surveyDrawerShow')
                this.getDataList(this.tabType)
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            this.$message.error('请检查表单是否填写正确')
          }
        })
      } else {
        this.$message.error('请检查问卷必填项')
      }
    },
    async editSurveyHandle(status) {
      let arr = await this.$refs.questionnaireRef.checkQuestionnaire()
      if (arr.every(item => item)) {
        this.$refs.surveyDrawerFormRef.validate(valid => {
          if (valid) {
            let params = {
              id: this.selectedId,
              enable: status,
              ...this.surveyDrawerForm,
              effective_time: this.surveyDrawerForm.validity && this.surveyDrawerForm.validity.length ? this.surveyDrawerForm.validity[0] + ' 00:00:00' : undefined,
              expiration_time: this.surveyDrawerForm.validity && this.surveyDrawerForm.validity.length ? this.surveyDrawerForm.validity[1] + ' 23:59:59' : undefined
            }
            this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireModifyPost(params).then(res => {
              if (res.code === 0) {
                this.$message.success('编辑成功')
                this.cancelHandle('surveyDrawerShow')
                this.getDataList(this.tabType)
              } else {
                this.$message.error(res.msg)
              }
            })
          } else {
            this.$message.error('请检查表单是否填写正确')
          }
        })
      } else {
        this.$message.error('请检查问卷必填项')
      }
    },
    handleEnableChange(row) {
      this.$confirm(row.enable ? '开始收集数据，是否发布？' : '停止后，问卷将无法填写，确认吗？', row.enable ? '发布' : '停止发布', {
        confirmButtonText: row.enable ? '发布' : '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          id: row.id,
          enable: row.enable
        }
        this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireModifyStatusPost(params).then(res => {
          if (res.code === 0) {
            this.$message.success(row.enable ? '发布成功' : '停止发布成功')
            this.getDataList(this.tabType)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
        row.enable = !row.enable
        this.$message.info('已取消')
      })
    },
    deleteHandle(row) {
      this.$confirm('删除后，无法查看已收集的数据，是否继续？', '删除', {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.secondConfirm(row.id)
      }).catch(() => {
        this.$message.info('已取消')
      })
    },
    secondConfirm(id) {
      this.$confirm('是否确定删除？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireDeletePost({
          id: id
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.getDataList(this.tabType)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
        this.$message.info('已取消')
      })
    },
    handleClick(data) {
      this.previewList = deepClone(data.images)
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    showQrCode() {
      const companyId = this.userInfo.company_id
      const type = this.tabType === 'canteen' ? 'canteen' : 'supplier'
      const orgId = this.organization
      switch (process.env.NODE_ENV) {
        case 'development':
          this.qrUrl = `https://h5.debug.packertec.com/pages_common/supervision/supervision_democratic?companyId=${companyId}&type=${type}&orgId=${orgId}`
          break;
        case 'staging':
          this.qrUrl = `https://h5-v4-staging.packertec.com/pages_common/supervision/supervision_democratic?companyId=${companyId}&type=${type}&orgId=${orgId}`
          break;
        case 'production':
          this.qrUrl = `https://h5-v4.packertec.com/pages_common/supervision/supervision_democratic?companyId=${companyId}&type=${type}&orgId=${orgId}`
          break;
      }
      this.dialogShow = true
    },
    setQrUrl(data) {
      switch (process.env.NODE_ENV) {
        case 'development':
          this.qrUrl = `https://h5.debug.packertec.com/pages_info/news/questionnaire?id=${data.id}&type=qrCode`
          break;
        case 'staging':
          this.qrUrl = `https://h5-v4-staging.packertec.com/pages_info/news/questionnaire?id=${data.id}&type=qrCode`
          break;
        case 'production':
          this.qrUrl = `https://h5-v4.packertec.com/pages_info/news/questionnaire?id=${data.id}&type=qrCode`
          break;
      }
    },
    handleLink(data) {
      this.qrUrl = ''
      this.setQrUrl(data)
      if (this.qrUrl) {
        // 创建一个临时input元素
        const input = document.createElement('input')
        // 设置input的值为qrUrl
        input.value = this.qrUrl
        // 将input添加到文档中
        document.body.appendChild(input)
        // 选中input中的所有内容
        input.select()
        // 执行复制命令
        document.execCommand('copy')
        // 移除临时input元素
        document.body.removeChild(input)
        // 提示用户复制成功
        this.$message.success('链接已复制到剪贴板')
      } else {
        this.$message.warning('没有可复制的链接')
      }
    },
    handleQrCode(data) {
      this.qrUrl = ''
      this.dialogTitle = data.name
      this.setQrUrl(data)
      this.$nextTick(() => {
        this.dialogShow = true
      })
    },
    gotoExport(type) {
      let option = {
        url: !type ? 'apiBackgroundFundSupervisionChannelCanteenManagementDemocraticFeedbackListExportPost' : 'apiBackgroundFundSupervisionPublicityInfoQuestionnaireListExportPost',
        params: {}
      }
      if (!type) {
        option.params = {
          ...this.formatQueryParams(this.searchFormSetting),
          feedback_type: this.tabType === 'canteen' ? 'org' : 'supplier',
          page: this.currentPage,
          page_size: this.pageSize
        }
      } else {
        option.params = {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint(type) {
      let params = {}
      if (!type) {
        params = {
          ...this.formatQueryParams(this.searchFormSetting),
          feedback_type: this.tabType === 'canteen' ? 'org' : 'supplier',
          page: this.currentPage,
          page_size: this.pageSize
        }
      } else {
        params = {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      const currentTableSetting = this.tableSetting.slice(0, -1)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: false,
          print_type: this.printType,
          print_title: !type ? '民主反馈' : '问卷测评',
          result_key: 'results', // 返回的数据处理的data keys
          api: !type ? 'apiBackgroundFundSupervisionChannelCanteenManagementDemocraticFeedbackListPost' : 'apiBackgroundFundSupervisionPublicityInfoQuestionnaireListPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params
          }),
          isMerge: "0"
        }
      });
      window.open(href, "_blank");
    },
    gotoPath(id) {
      this.$router.push({
        name: 'MerchantSurveyDetailData',
        query: {
          id: id
        }
      })
    },
    gotoPreview() {
      setSessionStorage('SurveyData', JSON.stringify(this.surveyDrawerForm))
      const { href } = this.$router.resolve({
        name: 'Preview'
      })
      window.open(href, "_blank");
    },
    // 筛选框的change事件
    handleVisibleUserChange(e) {
      console.log('handleVisibleUserChange', e)
    },
    visibleUserChange(type, flag) {
      switch (type) {
        case 'neibu':
          if (flag) {
            this.surveyDrawerForm.visible_user.push(0)
          } else {
            let arr = this.surveyDrawerForm.visible_user.filter(item => item !== 0)
            this.surveyDrawerForm.visible_user = deepClone(arr)
          }
          break
        case 'shanghu':
          if (flag) {
            this.surveyDrawerForm.visible_user.push(1)
          } else {
            let arr = this.surveyDrawerForm.visible_user.filter(item => item !== 1)
            this.surveyDrawerForm.visible_user = deepClone(arr)
          }
          break
        case 'qita':
          if (flag) {
            this.surveyDrawerForm.visible_user.push(2)
          } else {
            let arr = this.surveyDrawerForm.visible_user.filter(item => item !== 2)
            this.surveyDrawerForm.visible_user = deepClone(arr)
          }
          break
      }
      // this.$message.info('看看' + this.surveyDrawerForm.visible_user)
    }
  }
}
</script>

<style lang="scss" scoped>
.morning-wrapper {
  padding-top: 20px;
}
.survey-drawer-form {
  ::v-deep .el-form-item__error {
    margin-left: 105px;
  }
  .questionnaire-form {
    ::v-deep .el-form-item__error {
      margin-left: 0px;
    }
  }
}
</style>
