<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
      <search-form
        ref="searchRef"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon
              color="plain"
              @click="gotoExport"
              v-permission="['background_order.order_report_meal.group_summary_list_export']"
            >
              导出Excel
            </button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row"
          />
          <div class="m-t-20">注：实际单数+待退费单数+退单数=订单数</div>
        </div>
        <!-- 统计 start -->
        <!-- <table-statistics :statistics="orderAllNumberCollect" />
        <table-statistics :statistics="orderMealAllNumberCollect" />
        <table-statistics :statistics="orderMealAllRefundNumberCollect" /> -->
        <!-- end -->
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { GROUP_SUMMARY } from '../constant'
import { debounce, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'ReportMealGroupSummaryList',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      // tableSettingMeal: [],
      tableSetting: [
        { key: 'group_organization_name', label: '所属组织' },
        { key: 'name', label: '分组' },
        { key: 'collect_count', label: '总实际单数' },
        { key: 'collect_stop_count', label: '总待退费单数' },
        { key: 'collect_refund_count', label: '总退单数' },
        {
          key: 'breakfast',
          label: '早餐',
          children: [
            { key: 'breakfast_count', label: '实际单数' },
            { key: 'breakfast_stop_count', label: '待退费单数' },
            { key: 'breakfast_refund_count', label: '退单数' }
          ]
        },
        {
          key: 'lunch',
          label: '午餐',
          children: [
            { key: 'lunch_count', label: '实际单数' },
            { key: 'lunch_stop_count', label: '待退费单数' },
            { key: 'lunch_refund_count', label: '退单数' }
          ]
        },
        {
          key: 'afternoon',
          label: '下午茶',
          children: [
            { key: 'afternoon_count', label: '实际单数' },
            { key: 'afternoon_stop_count', label: '待退费单数' },
            { key: 'afternoon_refund_count', label: '退单数' }
          ]
        },
        {
          key: 'dinner',
          label: '晚餐',
          children: [
            { key: 'dinner_count', label: '实际单数' },
            { key: 'dinner_stop_count', label: '待退费单数' },
            { key: 'dinner_refund_count', label: '退单数' }
          ]
        },
        {
          key: 'supper',
          label: '宵夜',
          children: [
            { key: 'supper_count', label: '实际单数' },
            { key: 'supper_stop_count', label: '待退费单数' },
            { key: 'supper_refund_count', label: '退单数' }
          ]
        },
        {
          key: 'morning',
          label: '凌晨餐',
          children: [
            { key: 'morning_count', label: '实际单数' },
            { key: 'morning_stop_count', label: '待退费单数' },
            { key: 'morning_refund_count', label: '退单数' }
          ]
        }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: deepClone(GROUP_SUMMARY),
      orderAllNumberCollect: [
        // 统计
        { key: 'collect_total_count', value: 0, label: '总单数：' },
        { key: 'collect_total_refund_count', value: 0, label: '总退款单数：' }
      ],
      orderMealAllNumberCollect: [],
      orderMealAllNumberCollectData: [
        // 统计
        { key: 'breakfast_total_count', value: 0, label: '早餐总订单数：' },
        { key: 'lunch_total_count', value: 0, label: '午餐总订单数：' },
        { key: 'dinner_total_count', value: 0, label: '晚餐总订单数：' },
        { key: 'afternoon_total_count', value: 0, label: '下午茶总订单数：' },
        { key: 'supper_total_count', value: 0, label: '宵夜总订单数：' },
        { key: 'morning_total_count', value: 0, label: '凌晨餐总订单数：' }
      ],
      orderMealAllRefundNumberCollect: [],
      orderMealAllRefundNumberCollectData: [
        // 统计
        { key: 'breakfast_total_refund_count', value: 0, label: '早餐总退单数：' },
        { key: 'lunch_total_refund_count', value: 0, label: '午餐总退单数：' },
        { key: 'dinner_total_refund_count', value: 0, label: '晚餐总退单数：' },
        { key: 'afternoon_total_refund_count', value: 0, label: '下午茶总退单数：' },
        { key: 'supper_total_refund_count', value: 0, label: '宵夜总退单数：' },
        { key: 'morning_total_refund_count', value: 0, label: '凌晨餐总退单数：' }
      ],
      printType: 'ReportMealGroupSummaryList',
      isFirstSearch: true
    }
  },
  created() {
    this.initPrintSetting()
    this.initLoad(false)
    // this.getSettingsReportMealOrganizationCheck()
  },
  watch: {
    currentTableSetting(newVal, oldVal) {
      if (newVal) {
        this.getCollectSeting()
      }
    }
  },
  methods: {
    async initLoad(isFirst) {
      if (!isFirst) {
        // await this.getOrgWallet()
        this.getMealGroupSummaryList()
      }
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getMealGroupSummaryList()
        this.isFirstSearch = false
      }
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getMealGroupSummaryList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderReportMealGroupSummaryListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        date_type: 'report_date',
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        this.tableData.push({
          group_organization_name: "合计",
          collect_count: res.data.summary_data.collect_total_count,
          collect_stop_count: res.data.summary_data.collect_total_stop_count,
          collect_refund_count: res.data.summary_data.collect_total_refund_count,
          breakfast_count: res.data.summary_data.breakfast_total_count,
          breakfast_stop_count: res.data.summary_data.breakfast_total_stop_count,
          breakfast_refund_count: res.data.summary_data.breakfast_total_refund_count,
          lunch_count: res.data.summary_data.lunch_total_count,
          lunch_stop_count: res.data.summary_data.lunch_total_stop_count,
          lunch_refund_count: res.data.summary_data.lunch_total_refund_count,
          afternoon_count: res.data.summary_data.afternoon_total_count,
          afternoon_stop_count: res.data.summary_data.afternoon_total_stop_count,
          afternoon_refund_count: res.data.summary_data.afternoon_total_refund_count,
          dinner_count: res.data.summary_data.dinner_total_count,
          dinner_stop_count: res.data.summary_data.dinner_total_stop_count,
          dinner_refund_count: res.data.summary_data.dinner_total_refund_count,
          supper_count: res.data.summary_data.supper_total_count,
          supper_stop_count: res.data.summary_data.supper_total_stop_count,
          supper_refund_count: res.data.summary_data.supper_total_refund_count,
          morning_count: res.data.summary_data.morning_total_count,
          morning_stop_count: res.data.summary_data.morning_total_stop_count,
          morning_refund_count: res.data.summary_data.morning_total_refund_count
        })
        // this.orderAllNumberCollect.forEach(item => {
        //   item.value = res.data.summary_data[item.key] || 0
        // })
        // this.orderMealAllNumberCollect.forEach(item => {
        //   item.value = res.data.summary_data[item.key] || 0
        // })
        // this.orderMealAllRefundNumberCollect.forEach(item => {
        //   item.value = res.data.summary_data[item.key] || 0
        // })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取当前开启餐段
    // async getSettingsReportMealOrganizationCheck() {
    //   this.isLoading = true
    //   const res = await this.$apis.apiBackgroundReportMealReportMealSettingsReportMealOrganizationCheckPost()
    //   this.isLoading = false
    //   if (res.code === 0) {
    //     res.data.meal_time_enable_list.forEach(mealTime => {
    //       let mealSegment = this.tableSettingMeal.find(segment => segment.key === mealTime)
    //       if (mealSegment) {
    //         this.tableSetting.push(mealSegment)
    //       }
    //     })
    //     this.initPrintSetting()
    //     console.log(res.data)
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getMealGroupSummaryList()
    },
    gotoExport() {
      const option = {
        url: 'apiBackgroundOrderOrderReportMealGroupSummaryListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          date_type: 'report_date',
          page: this.currentPage,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let collectList = {
        orderAllNumberCollect: this.orderAllNumberCollect,
        orderMealAllNumberCollect: this.orderMealAllNumberCollect,
        orderMealAllRefundNumberCollect: this.orderMealAllRefundNumberCollect
      }
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '分组汇总',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundOrderOrderReportMealGroupSummaryListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          // collect: JSON.stringify(this.collect),
          // collectList: JSON.stringify(collectList),
          push_summary: true, // 合计添加到到table数据最后
          isMerge: 0,
          params: JSON.stringify({
            ...params,
            date_type: 'report_date',
            page: 1,
            page_size: this.totalCount ? this.totalCount : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 设置底部合并数据
    getCollectSeting() {
      this.orderMealAllNumberCollect = []
      this.orderMealAllRefundNumberCollect = []
      this.currentTableSetting.forEach(item => {
        const fullKey = `${item.key}_total_count`
        const fullRefundKey = `${item.key}_total_refund_count`
        let mealStat = this.orderMealAllNumberCollectData.find(stat => stat.key === fullKey)
        let mealRefundStat = this.orderMealAllRefundNumberCollectData.find(stat => stat.key === fullRefundKey)
        if (mealStat) {
          this.orderMealAllNumberCollect.push(mealStat)
        }
        if (mealRefundStat) {
          this.orderMealAllRefundNumberCollect.push(mealRefundStat)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-table {
  text-align: center;
  font-size: 12px;
}
</style>
