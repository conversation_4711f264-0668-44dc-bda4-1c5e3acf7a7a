<template>
  <div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #status="{ row }">
              <el-switch v-model="row.isDefault" />
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="drawerShow = true">详情</el-button>
              <el-button type="text" size="small" class="ps-warn-text" @click="editDrawerShow = true">核实</el-button>
            </template>
          </table-column>
        </el-table>
        <table-statistics v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText" :statistics="collect" />
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'预警详情'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="addChannelFormRef" :model="drawerForm" label-width="80px" label-position="right">
            <el-form-item :label="'预警时间'">
              {{ drawerForm.warningTime }}
            </el-form-item>
            <el-form-item :label="'所属组织'">
              {{ drawerForm.org }}
            </el-form-item>
            <el-form-item :label="'所属渠道'">
              {{ drawerForm.channel }}
            </el-form-item>
            <el-form-item :label="'预警类型'">
              {{ drawerForm.type }}
            </el-form-item>
            <el-form-item :label="'预警信息'">
              {{ drawerForm.info }}
            </el-form-item>
            <el-form-item :label="'状态'">
              {{ drawerForm.status }}
            </el-form-item>
            <el-form-item :label="'下发时间'">
              {{ drawerForm.time }}
            </el-form-item>
            <el-form-item :label="'操作员'">
              {{ drawerForm.operator }}
            </el-form-item>
            <el-form-item :label="'核实要求'">
              {{ drawerForm.request }}
            </el-form-item>
            <el-form-item :label="'核实人员'" v-if="infoType === 'check'">
              {{ drawerForm.verifier }}
            </el-form-item>
            <el-form-item :label="'核实时间'" v-if="infoType === 'check'">
              {{ drawerForm.verifyTime }}
            </el-form-item>
            <el-form-item :label="'核实信息'" v-if="infoType === 'check'">
              <div>{{ drawerForm.verifyInfo }}</div>
              <div v-for="(item, index) in drawerForm.attachments" :key="index" class="w-300 flex-b-c">
                <div>{{ item.name }}</div>
                <el-button type="text" size="small" class="ps-text" @click="drawerShow = true">下载</el-button>
              </div>
            </el-form-item>
            <el-form-item :label="'忽略原因'" v-if="infoType === 'noCheck'">
              {{ drawerForm.reason }}
            </el-form-item>
            <el-form-item :label="'变更时间'" v-if="infoType === 'noCheck'">
              {{ drawerForm.changeTime }}
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100 ps-origin-btn">关闭</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'编辑核实信息'"
        :visible="editDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="addChannelFormRef" :model="editDrawerForm" label-width="80px" label-position="right">
            <el-form-item :label="'预警时间'">
              {{ editDrawerForm.warningTime }}
            </el-form-item>
            <el-form-item :label="'预警类型'">
              {{ editDrawerForm.org }}
            </el-form-item>
            <el-form-item :label="'预警信息'">
              {{ editDrawerForm.channel }}
            </el-form-item>
            <el-form-item :label="'核实要求'">
              {{ editDrawerForm.type }}
            </el-form-item>
            <el-form-item :label="'核实信息'" prop="info">
              <el-input class="p-t-15" type="textarea" v-model="editDrawerForm.info" :show-word-limit="true" maxlength="500" :autosize="{ minRows: 6, maxRows: 8}" resize="none"></el-input>
            </el-form-item>
            <el-form-item :label="'附件'">
              <el-upload
                v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                :action="serverUrl" :data="uploadParams" :file-list="fileLists" :on-success="uploadSuccess"
                :before-upload="beforeFoodImgUpload" :limit="3" :multiple="false" :show-file-list="true"
                :headers="headersOpts">
                <div class="flex-center">
                  <el-button class="m-r-20" size="small" type="primary" icon="el-icon-plus">添加文件</el-button>
                  <div slot="tip" class="el-upload__tip">不超过20M</div>
                </div>
              </el-upload>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100">取消</el-button>
            <el-button size="small" class="w-100 ps-origin-btn">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { debounce, deepClone, getRequestParams, getToken } from '@/utils'

import dayjs from 'dayjs'
export default {
  data() {
    const RECENTSEVEN = [
      dayjs()
        .subtract(7, 'day')
        .format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ]
    return {
      isLoading: false,
      isLoadingCollect: false,
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'update_time',
          maxWidth: '130px',
          dataList: [
            {
              label: '更新时间',
              value: 'update_time'
            },
            {
              label: '预警时间',
              value: 'forewarning_time'
            },
            {
              label: '核实时间',
              value: 'verify_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: false,
          value: RECENTSEVEN
        },
        org_name: {
          type: 'input',
          label: '组织名称',
          value: '',
          placeholder: '请输入组织名称'
        },
        type: {
          type: 'select',
          label: '类型',
          value: '',
          placeholder: '请选择',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '利润率超标预警',
              value: '1'
            },
            {
              label: '原材料占比支出预警',
              value: '2'
            }
          ]
        },
        status: {
          type: 'select',
          label: '状态',
          value: '',
          placeholder: '请选择',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '核实中',
              value: '1'
            },
            {
              label: '已核实',
              value: '2'
            },
            {
              label: '已忽略',
              value: '3'
            }
          ]
        }
      },
      tableData: [
        {
          key: 1
        }
      ],
      tableSetting: [
        { label: '预警时间', key: 'name' },
        { label: '组织', key: 'address' },
        { label: '所属渠道', key: 'isDefault' },
        { label: '预警类型', key: 'tips' },
        { label: '预警信息', key: 'status', type: "slot", slotName: "status" },
        { label: '状态', key: 'operateTime' },
        { label: '更新时间', key: '3' },
        { label: '核实要求', key: '1' },
        { label: '核实时间', key: '2' },
        { label: '核实人员', key: 'operator' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "200" }
      ],
      elementLoadingText: '正在获取',
      collect: [ // 统计
        { key: '1', value: 0, label: '待核实' },
        { key: '2', value: 0, label: '核实中' },
        { key: '3', value: 0, label: '已核实' },
        { key: '4', value: 0, label: '已忽略' }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      drawerShow: false,
      drawerForm: {
        warningTime: '2024年10月17日 15：55：52',
        org: '田家炳实验中学',
        channel: '广州市教育局',
        type: '利润率超标预警',
        info: '9月份利润率6%',
        status: '已核实',
        time: '2024年10月17日 15：55：52',
        operator: '上山居士',
        request: '严格当月的原材料支出情况，限3天上报具体数据',
        verifier: '张三',
        verifyTime: '2024年10月17日 15：55：52',
        verifyInfo: '数据统计有误',
        attachments: [
          {
            name: '附件一.doc',
            url: ''
          },
          {
            name: '附件二.excel',
            url: ''
          }
        ],
        reason: '',
        changeTime: ''
      },
      infoType: 'check',
      editDrawerShow: false,
      editDrawerForm: {
        warningTime: '',
        org: '',
        channel: '',
        type: '',
        status: ''
      },
      uploading: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'super_food_img'
      },
      fileLists: [],
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      }
    }
  },
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    getDataList() {
      console.log('组件内获取数据的方法')
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getControlRecordList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getControlRecordList()
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        console.log('上传成功', file, fileList)
        this.fileLists = deepClone(fileList)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 图片上传前检测
    beforeFoodImgUpload(file) {
      const isLt2M = file.size / 1024 / 1024 <= 20
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
