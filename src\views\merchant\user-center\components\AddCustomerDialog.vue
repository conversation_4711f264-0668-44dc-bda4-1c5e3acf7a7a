<template>
  <el-drawer :title="title" :visible.sync="visible" width="450px" custom-class="ps-dialog-message"
    :close-on-press-escape="false" show-close :wrapperClosable="false" @close="close">
    <el-form ref="dialogForm" label-width="100px" class="m-t-20" :rules="dialogFormRules" :model="dialogEditData">
      <el-form-item label="人员编号" prop='person_no'>
        <el-input v-model="dialogEditData.person_no" placeholder="请输入人员编号" type="text" class="w-250" :disabled="type === 'edit'"
          @input="dialogInputChange"></el-input>
      </el-form-item>
      <el-form-item label="姓名" prop='name'>
        <div class="dialog-person-info" v-loading="isLoadingPerson">
          {{ dialogEditData.name }}
        </div>
      </el-form-item>
      <el-form-item label="客户号" prop='client_number'>
        <el-input v-model="dialogEditData.client_number" placeholder="请输入客户号" type="text" class="w-250"></el-input>
      </el-form-item>
    </el-form>
    <!-- 弹窗底部的按钮 -->
    <span class="dialog-footer-drawer">
      <el-button class="ps-cancel-btn" @click="clickCancleHandle">取 消</el-button>
      <el-button class="ps-btn" type="primary" @click="submitEditDialog" v-loading="isBindLoading">确 定</el-button>
    </span>
  </el-drawer>
</template>
<script >
import { to } from '@/utils';
export default {
  name: 'AddCustomerDialog',
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '新建'
    },
    show: Boolean
  },
  // mixins: [activatedLoadData],
  data() {
    var cardNoValidate = (rule, value, callback) => {
      if (value === '') {
        callback(new Error("请输入客户号"));
      } else if (value && !/^[a-zA-Z0-9_]+$/i.test(value)) {
        callback(new Error("请输入正确的客户号"));
      } else {
        callback();
      }
    };
    return {
      isLoading: false,
      dialogEditData: {
        person_no: '',
        name: '',
        phone: '',
        client_number: ''
      }, // 编辑弹窗数据存放
      isLoadingPerson: false, // 是否显示loading
      dialogFormRules: {
        person_no: [{ required: true, message: '请输入人员', trigger: 'blur' }],
        name: [{ required: false, message: '请填入人员编号查询', trigger: "blur" }],
        client_number: [{ required: true, validator: cardNoValidate, trigger: "blur" }]
      },
      isHasPersonInfo: false, // 是否有该人员信息
      isBindLoading: false // 是否显示确认键loading
    }
  },
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  watch: {
    visible(value) {
      console.log("visible");
      if (value) {
        this.initLoad()
      }
    }
  },
  created() {
  },
  mounted() { },
  methods: {
    initLoad() {

    },
    clickCancleHandle() {
      this.visible = false
    },
    /**
     * 弹窗
     * @param {*} e
     */
    dialogInputChange(e) {
      console.log("dialogInputChange", e);
      if (e.length > 0) {
        if (this.timer) {
          clearTimeout(this.timer)
        }
        this.timer = setTimeout(() => {
          this.getNameAndMobile(e)
        }, 1500)
      }
    },
    /**
     * 获取后台人员的姓名跟手机号
     */
    async getNameAndMobile(personNo) {
      console.log("personNo", personNo);
      this.isLoadingPerson = true
      const [err, res] = await to(this.$apis.apiCardServiceThirdCardUserGetCardInfoPost({ person_no: personNo }))
      this.isLoadingPerson = false
      if (err) {
        this.isHasPersonInfo = false
        this.setNameAndMobile()
        return
      }

      if (res.code === 0) {
        var resultData = res.data || {}
        this.isHasPersonInfo = true
        this.setNameAndMobile(resultData.name, resultData.id)
      } else {
        this.isHasPersonInfo = false
        this.setNameAndMobile()
      }
    },
    /**
     * 设置姓名与手机号
     * @param {*} name
     * @param {*} mobile
     */
    setNameAndMobile(name, cardInfoId) {
      this.dialogEditData.name = name || ''
      this.dialogEditData.card_info_id = cardInfoId || ''
    },
    /**
     * 编辑弹窗确认
     */
    submitEditDialog() {
      this.$refs.dialogForm.validate(valid => {
        if (valid) {
          if (!this.isHasPersonInfo && this.type === 'add') {
            this.$message.error('没有查询到该用户，请重新修改人员编号查询！')
            return
          }
          this.bindCardInfo()
        } else {
        }
      })
    },
    /**
    * 编辑绑定信息
    */
    async bindCardInfo() {
      let message = this.type === 'add' ? '新增' : '修改'
      let params = {
        client_number: this.dialogEditData.client_number,
        person_no: this.dialogEditData.person_no
      }
      // 编辑
      if (this.type === 'edit') {
        params.id = this.dialogEditData.id
        params.is_modify = true
      } else {
        // 新增
        params.card_info_id = this.dialogEditData.card_info_id
      }
      this.isBindLoading = true
      const [err, res] = await to(this.$apis.apiCardServiceThirdCardUserAbcClientModifyPost(params))
      this.isBindLoading = false
      if (err) {
        this.$message.error(message + '失败 ' + err.message)
        return
      }
      if (res && res.code === 0) {
        this.$message.success(message + '成功')
        this.$emit('confirmDialog', this.dialogEditData)
      } else {
        this.$message.error(message + '失败 ' + res.msg)
      }
    },
    // 设置弹窗数据
    setInfoData(data) {
      console.log("setInfoData", data);
      if (!data) {
        return
      }
      this.dialogEditData.person_no = data.person_no || ''
      this.dialogEditData.name = data.name || ''
      this.dialogEditData.client_number = data.client_number || ''
      this.dialogEditData.id = data.id || ''
    },
    //  弹窗关闭
    close() {
      console.log("close");
      if (this.$refs.dialogForm) {
        this.$refs.dialogForm.resetFields()
      }
      this.dialogEditData = {
        person_no: '',
        name: '',
        phone: '',
        client_number: '',
        id: ''
      }
      console.log("this.dialogEditData", this.dialogEditData);
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-drawer__header {
  margin-bottom: 0;
  padding: 23px 20px;
  background: #e7e9ef !important;
}

.ps-dialog-message {
  .dialog-footer-drawer {
    margin-left: 20px;
  }
}
</style>
