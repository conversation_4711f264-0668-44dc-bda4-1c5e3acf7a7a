<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" label-width="105px" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="gotoExport">导出</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          ref="tableData"
          :data="tableData"
          v-loading="isLoading"
          stripe header-row-class-name="ps-table-header-row"
          row-key="id"
          reserve-selection
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="showCheckRecord(row)"
              >
                查看
              </el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'学校食堂每日食品安全检查记录'"
        :visible="drawerShow"
        :show-close="false"
        size="50%">
        <div class="p-20" id="print">
          <div class="m-b-20">
            <div>
              检查日期：{{ drawerData.check_date }}
            </div>
            <div>
              复核人：{{ confirmName }}
            </div>
          </div>
          <el-table :data="drawerData.extra" stripe header-row-class-name="ps-table-header-row">
            <table-column v-for="(item, index) in drawerTableSetting" :key="index" :col="item">
              <template #checkResult="{ row }">
                {{ row.check_result ? '是' : '否' }}
              </template>
            </table-column>
          </el-table>
          <div class="flex m-t-20">
            <div>
              <div>检查人：{{ drawerData.director }}</div>
              <el-image
              :src="drawerData.director_sign"
              :fit="'fill'" style="width: 200px; height: 150px;"></el-image>
            </div>
            <div>
              <div>负责人：{{ drawerData.examiner }}</div>
              <el-image
              :src="drawerData.examiner_sign"
              :fit="'fill'" style="width: 200px; height: 150px;"></el-image>
            </div>
          </div>
          <div class="ps-el-drawer-footer noprint" v-if="!isPrint">
            <el-button class="w-100" @click="cancelHandle">关闭</el-button>
            <el-button class="w-100 ps-origin-btn" v-print="printObj" @click="toPrint">打印</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { debounce, deepClone, getRequestParams } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import print from 'vue-print-nb'

export default {
  name: 'XuexiaoShipinAnquanManagementLedger',
  mixins: [exportExcel],

  directives: {
    print
  },
  data() {
    return {
      isLoading: false,
      isPrint: false,
      searchForm: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '日期筛选',
          labelWidth: '100px',
          clearable: false,
          value: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        },
        confirmer_name: {
          type: 'input',
          value: '',
          label: '复核人',
          placeholder: '请输入'
        }
      },
      tableData: [],
      tableSetting: [
        { label: '', key: 'selection', type: 'selection', reserveSelection: true },
        { label: '创建时间', key: 'create_time' },
        { label: '台账编号', key: 'ledger_no' },
        { label: '检查人', key: 'director' },
        { label: '负责人', key: 'examiner' },
        { label: '提交操作员', key: 'submit_operator_name' },
        { label: '复核人', key: 'confirmer_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      drawerShow: false,
      drawerTableSetting: [
        { label: '检查项目', key: 'check_project' },
        { label: '类型', key: 'type' },
        { label: '检查内容', key: 'check_content' },
        { label: '检查结果', key: 'check_result', type: "slot", slotName: "checkResult" },
        { label: '不符合项说明', key: 'describe' },
        { label: '不符合项采取的防范措施', key: 'measure' }
      ],
      confirmName: '',
      drawerData: {
        check_date: '',
        extra: [],
        director: '',
        director_sign: '',
        examiner: '',
        examiner_sign: ''
      },
      printObj: {
        id: 'print', // 这里是要打印元素的ID
        popTitle: '&nbsp', // 打印的标题
        extraCss: '', // 打印可引入外部的一个 css 文件
        extraHead: '' // 打印头部文字
      },
      selectedList: [] // 已勾选的数据
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getData()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value || undefined
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    getData() {
      let params = {
        ...this.formatQueryParams(this.searchForm),
        page: this.currentPage,
        page_size: this.pageSize
      }
      this.$apis.apiBackgroundFundSupervisionLedgerKitchenHygieneDailyFoodSafetyListPost(params).then(res => {
        if (res.code === 0) {
          this.tableData = res.data.results
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.pageSize = 10
      this.tableData = []
      this.initLoad()
    }, // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.pageSize = 10
        this.initLoad()
      }
    }, 300),
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.initLoad()
    },
    gotoExport() {
      const option = {
        url: "apiBackgroundFundSupervisionLedgerKitchenHygieneBulkExportDailyFoodSafetyPost",
        params: {
          ids: this.selectedList
        }
      }
      this.$refs.tableData.clearSelection()
      this.exportHandle(option)
    },
    showCheckRecord(data) {
      this.$apis.apiBackgroundFundSupervisionLedgerKitchenHygieneDailyFoodSafetyDetailsPost({
        id: data.id
      }).then(res => {
        if (res.code === 0) {
          this.drawerData = deepClone(res.data)
          this.confirmName = data.confirmer_name
          this.drawerShow = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    cancelHandle() {
      this.confirmName = ''
      this.drawerShow = false
    },
    toPrint() {
      this.isPrint = true
      setTimeout(() => {
        this.isPrint = false
      }, 100) // 100毫秒秒后恢复 不然打印会有按钮
      // window.print()
    },
    handleSelectionChange(e) {
      this.selectedList = []
      e.forEach(item => {
        this.selectedList.push(item.id)
      })
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
