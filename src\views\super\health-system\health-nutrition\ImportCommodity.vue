<template>
  <div>
    <refresh-tool :title="title" :show-refresh="false" />
    <import-page class="importPage" :initial="initial" :url="url" :header-len="headerLen" :template-url="templateUrl" :is-url-download-result="true"/>
  </div>
</template>

<script>
export default {
  name: 'ImportIngredients',
  data() {
    return {
      type: 'import',
      title: '批量导入菜品/商品',
      headerLen: 2,
      initial: true,
      url: 'apiBackgroundAdminFoodFoodBatAddPost',
      templateUrl: '/api/temporary/template_excel/food_stock/super_foods.xlsx'
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.$route.params.type) {
      this.type = this.$route.params.type
    }
    if (this.type === 'import') {
      this.title = '批量导入菜品/商品'
      this.url = 'apiBackgroundAdminFoodFoodBatAddPost'
      // this.templateUrl = '/api/temporary/template_excel/food_stock/super_foods.xlsx'
    } else {
      this.title = '导入编辑'
      this.url = 'apiBackgroundAdminFoodFoodBatModifyPost'
      // this.templateUrl = '/api/temporary/template_excel/food_stock/super_foods.xlsx'
    }
  },
  mounted() {
  },
  methods: {}
}
</script>

<style lang="scss">
.importPage {
  // max-width: 1160px;
}
</style>
