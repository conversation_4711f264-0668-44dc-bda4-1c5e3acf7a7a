<template>
  <div class="container-wrapper DownloadCenterDetail">
    <refresh-tool :showRefresh="false" />
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <!-- <div class="table-title">数据列表</div> -->
        <div class="m-l-20 align-center">文件名称：{{ detailData.file_name }}</div>
        <div class="align-r">
          <!-- v-permission="['background_drp.inquiry.batch_add_inquiry']" -->
          <button-icon color="origin" @click="handleExport">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <div class="table-html" v-html="tableHtml"></div>
      </div>
    </div>
    <!-- dialog start -->
    <!-- dialog end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { decodeQuery, getUrlFilename } from '@/utils'
import FileSaver from 'file-saver'
import XLSX from 'xlsx'
import http from '@/utils/request'

export default {
  name: 'DownloadCenterDetail',
  data() {
    return {
      isLoading: false, // 刷新数据
      detailData: {},
      tableData: [{}],
      tableSettings: [
        { label: '用户姓名', key: 'org_name' },
        { label: '人员编号', key: 'warehouse_name' },
        { label: '分组', key: 'warehouse_name1' },
        { label: '菜品名称', key: 'warehouse_nam2e' },
        { label: '菜品克重', key: 'warehouse_nam3e' }
      ],
      tableHtml: ''
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.$route.query.data) {
        this.detailData = decodeQuery(this.$route.query.data)
        this.loadXlsx(this.detailData.file_url)
      }
    },
    // 解析xlsx文件转换为html
    loadXlsx(url) {
      this.isLoading = true
      // url = 'http://192.168.50.248:9999/%E8%8F%9C%E5%93%81%E9%94%80%E5%94%AE%E6%8E%92%E8%A1%8C_171843637316e4e86cf0d5cd0.xlsx'
      // url = 'http://192.168.50.248:8088/' + url
      http
        .get(url, { responseType: 'blob' }) // arraybuffer, blob
        .then(async res => {
          var blob = new Blob([res], { type: 'application/octet-stream;charset=utf-8' })
          await this.$sleep(500)
          this.loadFile(blob)
          this.isLoading = false
        })
        .catch(err => {
          this.isLoading = false
          console.log(err)
          this.$message.error(err.message)
        })
    },
    // 解析数据
    loadFile(obj) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = async e => {
          const reg = /<table.*?>.*?<\/table>/ig
          const data = e.target.result
          const workbook = XLSX.read(data, { type: 'array' })
          const firstSheetName = workbook.SheetNames[0]
          const sheet = workbook.Sheets[firstSheetName]
          // const results = XLSX.utils.sheet_to_json(workbook.Sheets[firstSheetName])
          // this.mergesList = workbook.Sheets[firstSheetName]['!merges'] ? workbook.Sheets[firstSheetName]['!merges'] : []
          let htmlStr = XLSX.utils.sheet_to_html(sheet)
          // console.log('htmlStr', htmlStr)
          let tableStr = htmlStr.match(reg)
          if (tableStr && tableStr.length > 0) {
            tableStr = tableStr[0]
            // console.log('tableStr', tableStr)
            // 处理下table显示
            tableStr = tableStr.replace('<table>', '<table style="width:100%;" border="1" cellspacing="0" cellpadding="2" bordercolor="#ebeef5">')
            // 居中
            tableStr = tableStr.replace(/<tr>/g, '<tr align="center">')
            this.tableHtml = tableStr
          }
          resolve(tableStr)
        }
        reader.readAsArrayBuffer(obj)
      })
    },
    // 导出
    handleExport() {
      let filsename = getUrlFilename(this.detailData.file_url)
      FileSaver.saveAs(this.detailData.file_url, filsename)
    }
  }
}
</script>

<style lang="scss">
.DownloadCenterDetail{
  .table-html {
    max-height: calc(100vh - 340px);
    overflow: auto;
    // 设置页头颜色
    tr:first-child {
      color: #fff;
      background-color: #909399;
      td {
        color: #fff;
      }
    }
    td {
      min-width: 80px;
      height: 30px;
      font-size: 14px;
      color: #606266;
    }
  }
}
</style>
