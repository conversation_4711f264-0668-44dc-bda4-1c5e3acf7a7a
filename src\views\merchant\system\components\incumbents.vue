<template>
  <div>
    <div class="table-header">
      <div class="header-left">
        <div class="header-left-title">在职人员（{{ totalCount }}人）</div>
        <el-button type="text" size="small" class="ps-text m-r-10" @click="showDrawer('add')" v-permission="['background_fund_supervision.job_person.add_job_person']">添加</el-button>
        <el-button type="text" size="small" class="ps-text m-r-10" @click="importDrawerShow = true" v-permission="['background_fund_supervision.job_person.batch_import']">导入</el-button>
        <el-button type="text" size="small" class="ps-text" @click="gotoExport" v-permission="['background_fund_supervision.job_person.list_export']">导出</el-button>
      </div>
    </div>
    <div class="table-content">
      <!-- table start -->
      <el-table
        v-loading="isLoading"
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        stripe
        header-row-class-name="ps-table-header-row"
      >
        <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
          <template #healthCertificate="{ row }">
            <el-button type="text" size="small" class="ps-text" :disabled="row.status"  @click="handleClick(row)">查看</el-button>
          </template>
          <template #operation="{ row }">
            <el-button type="text" size="small" class="ps-text" @click="showDrawer('edit', row)" v-permission="['background_fund_supervision.job_person.modify_job_person']">编辑</el-button>
            <el-button type="text" size="small" class="ps-warn-text" @click="deleteHandle(row)" v-permission="['background_fund_supervision.job_person.delete_job_person']">删除</el-button>
          </template>
        </table-column>
      </el-table>
      <!-- table end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="selectType === 'add' ? '添加人员' : '编辑人员'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="drawerFormRef" :model="drawerForm" label-width="100px" label-position="right">
            <el-form-item :label="'姓名'" prop="name" :rules="[{ required: true, message: '请输入姓名', trigger: ['change', 'blur'] }]">
              <el-input v-model="drawerForm.name" class="w-300" placeholder="请输入姓名，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item :label="'联系电话'" prop="phone" :rules="[{ required: true, message: '请输入联系电话', trigger: ['change', 'blur'] }]">
              <el-input v-model="drawerForm.phone" class="w-300" placeholder="请输入联系电话，不超过11位" maxlength="11"></el-input>
            </el-form-item>
            <el-form-item :label="'所属岗位'" prop="post" :rules="[{ required: true, message: '请输入岗位信息', trigger: ['change', 'blur'] }]">
              <el-input v-model="drawerForm.post" class="w-300" placeholder="请输入岗位信息，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item :label="'健康证件'" prop="img" :rules="[{ required: true, message: '请上传有效证件', trigger: ['change', 'blur'] }]">
              <div class="certification-info-show-tips">
                图片最大不超过2MB，仅支持jpg,png格式
              </div>
              <el-upload v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                :action="serverUrl" :file-list="fileLists" :on-success="uploadSuccess"
                :before-upload="beforeFoodImgUpload" :limit="1" :multiple="false" :show-file-list="false"
                :headers="headersOpts" accept=".jpeg,.jpg,.png,.bmp">
                <img v-if="drawerForm.img" :src="drawerForm.img" style="width: 100px; height: 100px;">
                <div v-else style="width: 100px; height: 100px; border: 1px dashed #C0C4CC; text-align: center; line-height: 100px;">
                  <i class="el-icon-plus"></i>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item :label="'证件有效期'" prop="time" :rules="[{ required: true, message: '请选择证件的过期日期', trigger: ['change', 'blur'] }]">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="drawerForm.time"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'导入在职人员'"
        :visible="importDrawerShow"
        :show-close="false"
        size="40%">
        <div>
          <import-upload-file
          @publicUrl="publicUrl"
          uploadFormItemLabel="批量添加"
          fileType="zip"
          :uploadParams="uploadParams"
          :link="downloadUrl"
          class="m-t-20"
        ></import-upload-file>

          <div class="p-l-20 ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="importDrawerShow = false">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="importFile">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { getToken, getSuffix, deepClone } from '@/utils/index'
import ImportUploadFile from '@/components/ImportUploadFile'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  mixins: [exportExcel],
  components: {
    ElImageViewer,
    ImportUploadFile
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '姓名', key: 'name' },
        { label: '联系电话', key: 'phone' },
        { label: '所属岗位', key: 'job_title' },
        { label: '健康证', key: 'img_url', type: "slot", slotName: "healthCertificate" },
        { label: '证件有效期', key: 'effective_time' },
        { label: '修改时间', key: 'update_time' },
        { label: '操作人', key: 'operator' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "200" }
      ],
      page: 1,
      pageSize: 10,
      totalCount: 0,
      selectType: '',
      selectId: '',
      drawerShow: false,
      drawerForm: {
        name: '',
        phone: '',
        post: '',
        img: '',
        time: ''
      },
      uploading: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'incumbents',
        key: 'incumbents' + new Date().getTime() + Math.floor(Math.random() * 150)
      },
      fileLists: [],
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      importDrawerShow: false,
      showImagePreview: false,
      previewList: [],
      downloadUrl: '/api/temporary/template_excel/batch_import_job_person.zip',
      importUrl: ''
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    publicUrl(url) {
      // let [before, after] = url.split('?')
      this.importUrl = url
    },
    importFile() {
      this.$apis.apiBackgroundFundSupervisionJobPersonBatchImportPost({
        face_zip_url: this.importUrl
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('导入成功')
        } else {
          this.$message.error(res.msg)
        }
        this.importDrawerShow = false
        this.isLoading = true
        // 后端说渲染太快了，加个延迟
        setTimeout(() => {
          this.getDataList()
        }, 500)
      })
    },
    gotoExport() {
      const option = {
        url: 'apiBackgroundFundSupervisionJobPersonListExportPost',
        params: {
          page: this.currentPage,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        this.fileLists = []
        this.drawerForm.img = res.data.public_url
      } else {
        this.drawerForm.img = ''
        this.$message.error(res.msg)
      }
    },
    // 图片上传前检测
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 2
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.getDataList()
    },
    // 获取数据
    getDataList() {
      this.isLoading = true
      let params = {
        page: this.page,
        page_size: this.pageSize
      }
      this.$apis.apiBackgroundFundSupervisionJobPersonJobPersonListPost(params).then(res => {
        if (res.code === 0) {
          this.isLoading = false
          this.tableData = deepClone(res.data.results || [])
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleClick(row) {
      this.previewList = [row.img_url]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    showDrawer(type, data) {
      this.selectType = type
      console.log('data', data)
      if (type === 'edit') {
        this.selectId = data.id
        this.drawerForm.phone = data.phone
        this.drawerForm.img = data.img_url
        this.drawerForm.name = data.name
        this.drawerForm.post = data.job_title
        this.drawerForm.time = data.effective_time
      }
      this.drawerShow = true
    },
    cancelHandle() {
      this.$refs.drawerFormRef.resetFields()
      this.drawerShow = false
    },
    saveHandle() {
      let params = {
        id: this.selectType === 'add' ? undefined : this.selectId,
        phone: this.drawerForm.phone,
        img_url: this.drawerForm.img,
        name: this.drawerForm.name,
        job_title: this.drawerForm.post,
        effective_time: this.drawerForm.time
      }
      if (this.selectType === 'add') {
        this.addJobPerson(params)
      } else {
        this.editJobPerson(params)
      }
    },
    addJobPerson(params) {
      this.$apis.apiBackgroundFundSupervisionJobPersonAddJobPersonPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('新增成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.drawerFormRef.resetFields()
        this.drawerShow = false
        this.getDataList()
      })
    },
    editJobPerson(params) {
      this.$apis.apiBackgroundFundSupervisionJobPersonModifyJobPersonPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('编辑成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.drawerFormRef.resetFields()
        this.drawerShow = false
        this.getDataList()
      })
    },
    deleteHandle(data) {
      this.$confirm(`确定要删除 ${data.name} 的人员信息？删除后不可恢复，请谨慎操作。`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$apis.apiBackgroundFundSupervisionJobPersonDeleteJobPersonPost({
          id: data.id
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
          } else {
            this.$message.error(res.msg)
          }
          this.getDataList()
        })
      }).catch(action => {
        this.$message('已取消删除')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header-left {
  padding-left: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  &-title {
    height: 32px;
    line-height: 32px;
    margin-right: 10px;
    font-weight: 700;
  }
}
</style>
