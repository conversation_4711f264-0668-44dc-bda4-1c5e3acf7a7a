<template>
  <!-- 晨检记录-->
  <div class="morning-wrapper container-wrapper">
    <div class="tab-item m-t-20">
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        @reset="resetHandler"
        label-width="120px"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-button size="mini" @click="gotoExport">导出Excel</el-button>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            :height="maxHeight"
            :max-height="maxHeight"
            stripe
            header-row-class-name="ps-table-header-row"
            empty-text="暂无数据，请查询"
          >
            <table-column v-for="(item, index) in currentTableSetting" :key="index" :col="item">
              <template #checkStatus="{ row }">
                <div :class="row.check_status ? '' : 'ps-red'">
                  {{ row.check_status ? '已晨检' : '未晨检' }}
                </div>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics v-if="getIsDateRange" :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
    ></print-setting>
  </div>
</template>

<script>
import { SEARCH_SETTING_MORNING_INSPECTION_SUMMARY, TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY } from '../constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { mapGetters } from 'vuex'
import { debounce, deepClone } from '@/utils'

export default {
  name: 'MorningInspectionRecordSummary',
  mixins: [exportExcel, report],
  components: {},
  data() {
    return {
      isLoading: false,
      tableSetting: deepClone(TABLE_HEAD_DATA_MORNING_INSPECTION_SUMMARY),
      currentTableSetting: [],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: deepClone(SEARCH_SETTING_MORNING_INSPECTION_SUMMARY),
      collect: [
        { key: 'total_count', value: 0, label: '本日汇总：应晨检', unit: '人' },
        { key: 'check_count', value: 0, label: '实际晨检', unit: '人' },
        { key: 'not_check', value: '', label: '未晨检', unit: '人' }
      ], // 统计
      printType: 'SupervisionCanteenSafetyCheckCollect',
      showPicDialog: false,
      imageList: {},
      picName: '',
      maxHeight: 460
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'organization']),
    // 判断是不是日期是同一天，同一天才展示，不是同一天不展示
    getIsDateRange() {
      try {
        let startDate = this.searchFormSetting.select_time.value[0]
        let endDate = this.searchFormSetting.select_time.value[1]
        return startDate === endDate
      } catch (error) {
        return false
      }
    }
  },
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.initPrintSetting()
      this.setTabDataHandle()
    },
    setTabDataHandle(e) {
      this.tableData = []
      this.getDataList()
    },
    resetHandler() {
      this.currentPage = 1
      this.setTabDataHandle()
    },
    async refreshHandle() {
      this.currentPage = 1
      this.setTabDataHandle()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value !== '全部') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getDataList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const res = await this.$apis.apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectPost(params)
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        this.tableData = deepClone(data.results)
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tabbleSetting = deepClone(this.currentTableSetting)
      tabbleSetting = tabbleSetting.filter(item => item.key !== 'images')
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '晨检汇总',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tabbleSetting),
          current_table_setting: JSON.stringify(tabbleSetting),
          collect: this.getIsDateRange ? JSON.stringify(this.collect) : null,
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          }),
          isMerge: '0'
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped></style>
