// import Layout from '@/layout'

import permission from '@/store/modules/permission'

// 注意layout一定要最后生成再替换成import的方式引入，不然会触发页面重复渲染
let Layout = 'Layout'

const system = [
  // 商户管理
  {
    path: '/super_merchant',
    component: Layout,
    redirect: '/super_merchant/organization',
    alwaysShow: true,
    name: 'SuperMerchantManagement',
    meta: {
      title: 'admin_merchant_management',
      permission: ['merchant_management_l2']
    },
    children: [
      {
        path: 'organization',
        component: () =>
          import(
            /* webpackChunkName: "super_organization" */ '@/views/super/merchant-admin/organization'
          ),
        name: 'SuperOrganizationAdmin',
        meta: {
          noCache: true,

          title: 'organizationAdmin',
          permission: ['background.admin.organization']
        }
      },
      {
        path: 'bank_merchant',
        component: () =>
          import(
            /* webpackChunkName: "super_agreement_record" */ '@/views/super/merchant-admin/BankMerchantManage'
          ),
        name: 'BankMerchantManage',
        meta: {
          noCache: true,

          title: 'bank_merchant',
          permission: ['background.sub_merchant_info']
        }
      },
      {
        path: 'merchant_channel_manager',
        component: () =>
          import(
            /* webpackChunkName: "merchant_channel_manager" */ '@/views/super/merchant-admin/ChannelManagementSuper'
          ),
        name: 'ChannelManagementSuper',
        meta: {
          noCache: true,

          title: 'super_merchant_channel',
          // permission: ['background_channel.admin_channel']
          permission: ['background.admin.channel']
        }
      },
      {
        path: 'report_verify_handle',
        component: () =>
          import(
            /* webpackChunkName: "super_agreement_record" */ '@/views/super/merchant-admin/ReportVerifyHandle'
          ),
        name: 'ReportVerifyHandle',
        meta: {
          noCache: true,

          title: 'report_verify_handle',
          permission: ['background.admin.script_report']
        }
      },
      {
        path: 'nutrition_orders',
        component: () =>
          import(
            /* webpackChunkName: "nutrition_orders" */ '@/views/super/merchant-admin/NutritionOrders'
          ),
        name: 'NutritionOrders',
        meta: {
          noCache: true,

          title: 'nutrition_orders',
          permission: ['background.admin.food_category.list']
        }
      },
      {
        path: 'merchant_banner',
        component: () =>
          import(
            /* webpackChunkName: "super_merchant_banner" */ '@/views/super/merchant-admin/merchant-banner/MerchantBanner'
          ),
        name: 'SuperMerchantBanner',
        meta: {
          noCache: true,

          title: 'super_merchant_banner',
          permission: ['background.admin.merchant_banner']
        }
      },
      {
        path: 'history_record',
        component: () =>
          import(
            /* webpackChunkName: "SuperHistoryRecord" */ '@/views/super/merchant-admin/HistoryRecord'
          ),
        name: 'SuperHistoryRecord',
        meta: {
          noCache: true,

          title: 'history_record',
          permission: ['background.log.operation_history_list']
        }
      },
      {
        path: 'add_bank_merchant/:type',
        component: () =>
          import(
            /* webpackChunkName: "super_agreement_record" */ '@/views/super/merchant-admin/BankMerchantManageDetail'
          ),
        name: 'BankMerchantManageDetail',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_merchant/bank_merchant',
          title: 'bank_merchant_detail',
          permission: ['background.sub_merchant_info']
        }
      },
      {
        path: 'add_merchant_banner/:type',
        component: () =>
          import(
            /* webpackChunkName: "super_add_merchant_management" */ '@/views/super/merchant-admin/merchant-banner/AddMerchantBanner'
          ),
        name: 'SuperAddMerchantBanner',
        hidden: true,
        meta: {
          noCache: true,

          title: 'super_merchant_banner',
          activeMenu: '/super_merchant/merchant_banner',
          permission: ['background.admin.merchant_banner']
        }
      }
    ]
  },
  // 监管平台
  {
    path: '/supervise_fund_management',
    component: Layout,
    redirect: '/supervise_fund_management/supervision_channel',
    alwaysShow: true,
    name: 'SuperLargeScreenConfiguration',
    meta: {
      title: 'supervise_fund_management',
      permission: ['supervision_platform_l2']
    },
    children: [
      {
        path: 'supervision_channel',
        component: () =>
          import(
            /* webpackChunkName: "nutrition_orders" */ '@/views/super/merchant-admin/SupervisionChannel'
          ),
        name: 'SupervisionChannel',
        meta: {
          noCache: true,

          title: 'supervision_channel',
          permission: ['background.admin.supervision_channel']
        }
      },
      {
        path: 'super_large_screen_configuration',
        component: () =>
          import(
            /* webpackChunkName: "super_organization" */ '@/views/super/merchant-admin/LargeScreenConfiguration'
          ),
        name: 'SuperLargeScreenConfigurationList',
        meta: {
          noCache: true,

          title: 'large_screen_configuration',
          permission: ['background.admin.monitoring_screen']
        }
      }
    ]
  },
  // 供应商管理
  {
    path: '/supplier',
    component: Layout,
    redirect: '/supplier/supplier_management',
    alwaysShow: true,
    name: 'AdminSuperManagement',
    meta: {
      title: 'information_management',
      permission: ['supplier_manage_l2']
    },
    children: [
      // 供应商
      {
        path: 'supplier_management',
        component: () =>
          import(
            /* webpackChunkName: "admin_supplier_management" */ '@/views/super/merchant-admin/supplier-management/SupplierManagement'
          ),
        name: 'adminSupplierManagement',
        meta: {
          noCache: true,

          activeMenu: '/super_merchant/supplier/supplier_management',
          title: 'supplier_management_info',
          permission: ['background.admin.supplier_manage.list']
        }
      },
      {
        path: 'modify_supplier_management',
        component: () =>
          import(
            /* webpackChunkName: "admin_supplier_management" */ '@/views/super/merchant-admin/supplier-management/ModifySupplierManagement'
          ),
        name: 'SuperModifySupplierManagement',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/super_merchant/supplier/supplier_management',
          title: 'modify_supplier_management',
          permission: ['background.admin.supplier_manage.list']
        }
      },
      // 团餐公司
      {
        path: 'group_meal_company_list',
        component: () =>
          import('@/views/super/information-management/group-meal-company/list.vue'),
        name: 'InformationManagement',
        meta: {
          noCache: true,
          title: 'group_meal_company_list',
          permission: ['background.admin.catering_company.list']
        }
      },
      // 配餐公司
      {
        path: 'catering_company_list',
        component: () =>
          import('@/views/super/information-management/catering-company/list.vue'),
        name: 'CateringCompanyManagement',
        meta: {
          noCache: true,
          title: 'catering_company_list',
          permission: ['background.admin.group_meal_company.list']
        }
      }
    ]
  },
  // 用户管理
  {
    path: '/super_user_admin',
    component: Layout,
    redirect: '/super_user_admin/list',
    alwaysShow: true,
    name: 'SuperUserAdmin',
    meta: {
      title: 'user_admin',
      permission: ['user_management_l2']
    },
    children: [
      {
        path: 'list',
        component: () =>
          import(/* webpackChunkName: "super_user_admin" */ '@/views/super/user-admin/UserAdmin'),
        name: 'SuperUserAdminList',
        meta: {
          noCache: true,

          title: 'user_admin_list',
          permission: ['background.admin.user.user_list']
        }
      },
      {
        path: 'list_detail/:id',
        component: () =>
          import(
            /* webpackChunkName: "super_user_detail" */ '@/views/super/user-admin/UserAdminDetail'
          ),
        name: 'SuperUserAdminDetail',
        hidden: true,
        meta: {
          noCache: true,

          title: 'user_admin_list_detail',
          activeMenu: '/super_user_admin/list',
          permission: ['background.admin.user.user_list']
        }
      },
      {
        path: 'face_traceback',
        component: () =>
          import(
            '@/views/super/merchant-admin/FaceTraceback'
          ),
        name: 'FaceTraceback',
        meta: {
          noCache: true,

          title: 'face_traceback',
          permission: ['background.admin.face_traceback.face_traceback']
        }
      },
      {
        path: 'face_traceback_detail',
        component: () =>
          import(
            '@/views/super/merchant-admin/FaceTracebackDetail'
          ),
        name: 'FaceTracebackDetail',
        hidden: true,
        meta: {
          noCache: true,

          title: 'face_traceback_detail',
          permission: ['background.admin.face_traceback.face_traceback']
        }
      }
    ]
  },
  // 公告管理
  {
    path: '/super_announcement_management',
    component: Layout,
    redirect: '/super_announcement_management/notice_admin',
    alwaysShow: true,
    name: 'SuperSystemSettings',
    meta: {
      title: 'announcement_management',
      permission: ['messages_management_l2']
    },
    children: [
      {
        path: 'notice_admin',
        component: () =>
          import(
            /* webpackChunkName: "super_notice_admin" */ '@/views/super/system-settings/NoticeAdmin'
          ),
        name: 'SuperNoticeAdmin',
        meta: {
          noCache: true,

          title: 'notice_admin',
          permission: ['background.admin.messages']
        }
      }
    ]
  },
  // 协议管理
  {
    path: '/super_agreement_management',
    component: Layout,
    redirect: '/super_agreement_management/agreement_list',
    alwaysShow: true,
    name: 'SuperAgreementManagement',
    meta: {
      title: 'agreement_list',
      permission: ['agreement_management_l2']
    },
    children: [
      {
        path: 'agreement_list',
        component: () =>
          import(
            /* webpackChunkName: "super_agreementlist" */ '@/views/super/merchant-admin/AgreementList'
          ),
        name: 'SuperAgreementList',
        meta: {
          noCache: true,

          title: 'agreement_list',
          permission: ['background.admin.agreement.get_agreement_list']
        }
      },
      {
        path: 'agreement_record',
        component: () =>
          import(
            /* webpackChunkName: "super_agreement_record" */ '@/views/super/merchant-admin/AgreementRecord'
          ),
        name: 'SuperAgreementRecord',
        meta: {
          noCache: true,

          title: 'agreement_record',
          permission: ['background.admin.agreement_record.list']
        }
      }
    ]
  },
  // 积木报表
  {
    path: '/jimu_statement',
    component: Layout,
    redirect: '/jimu_statement/index',
    alwaysShow: false,
    name: 'SuperJimuStatement',
    meta: {
      title: 'jimu_statement',
      permission: ['background.admin.custom_report_l2']
    },
    children: [
      {
        path: 'index',
        component: () =>
          import(
            /* webpackChunkName: "nutrition_orders" */ '@/views/super/jimu-statement/JiMuStatement'
          ),
        name: 'SuperJimu',
        meta: {
          noCache: true,

          title: 'jimu_statement',
          permission: ['background.admin.custom_report.jmreport']
        }
      },
      {
        path: 'report_application',
        component: () =>
          import(
            /* webpackChunkName: "nutrition_orders" */ '@/views/super/jimu-statement/ReportApplication'
          ),
        name: 'SuperReportApplication',
        meta: {
          noCache: true,

          title: 'report_application',
          permission: ['background.admin.jmreport.list']
        }
      }
    ]
  },
  // 会员中心
  {
    path: '/super_member_center',
    component: Layout,
    redirect: '/super_member_center/member_list',
    alwaysShow: true,
    name: 'SuperMemberCenter',
    meta: {
      title: 'member_center',
      permission: ['background_member_l2']
    },
    children: [
      {
        path: 'member_list',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberList'
          ),
        name: 'SuperMemberList',
        meta: {
          noCache: true,

          title: 'member_list',
          permission: ['background_member.member_user.list']
        }
      },
      {
        path: 'member_detail',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberDetail'
          ),
        name: 'SuperMemberDetail',
        hidden: true,
        meta: {
          noCache: true,

          title: 'member_detail',
          activeMenu: '/super_health_system/member_center/member_list',
          permission: ['background_member.member_user.list']
        }
      },
      {
        path: 'member_level',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberLevel'
          ),
        name: 'SuperMemberLevel',
        hidden: true,
        meta: {
          noCache: true,

          title: 'member_level'
        }
      },
      {
        path: 'member_level/:type',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/AddOrEditMemberLevel'
          ),
        name: 'SuperAddOrEditMemberLevel',
        hidden: true,
        meta: {
          noCache: true,

          title: 'member_level',
          activeMenu: '/super_health_system/member_center/member_level'
        }
      },
      {
        path: 'member_label',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberLabel'
          ),
        name: 'SuperMemberLabel',
        meta: {
          noCache: true,

          title: 'member_label',
          permission: ['background_member.member_label.list']
        }
      },
      {
        path: 'member_label/:type',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/AddOrEditMemberLabel'
          ),
        name: 'SuperAddOrEditMemberLabel',
        hidden: true,
        meta: {
          noCache: true,

          title: 'member_label',
          activeMenu: '/super_health_system/member_center/member_label',
          permission: ['background_member.member_label.list']
        }
      },
      {
        path: 'member_permission',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberPermission'
          ),
        name: 'SuperMemberPermission',
        hidden: true,
        meta: {
          noCache: true,

          title: 'member_permission'
        }
      },
      {
        path: 'member_permission/:type',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/AddOrEditMemberPermission'
          ),
        name: 'SuperAddOrEditMemberPermission',
        hidden: true,
        meta: {
          noCache: true,

          title: 'member_permission',
          activeMenu: '/super_health_system/member_center/member_permission'
        }
      },
      {
        path: 'member_charge_rule_index',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberChargeRuleIndex'
          ),
        name: 'SuperMemberChargeRule',
        meta: {
          noCache: true,

          title: 'member_charge_rule',
          permission: ['background_member.member_charge_rule.list']
        }
      },
      {
        path: 'member_charge_rule_detail',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberChargeRuleDetail'
          ),
        name: 'SuperMemberChargeRuleDetail',
        hidden: true,
        meta: {
          noCache: true,

          title: 'member_charge_rule_detail',
          permission: ['background_member.member_charge_rule.list']
        }
      },
      {
        path: 'member_receive_record',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberReceiveRecord'
          ),
        name: 'SuperMemberReceiveRecord',
        meta: {
          noCache: true,

          title: 'member_receive_record',
          permission: ['background_member.member_receive.list']
        }
      },
      {
        path: 'member_exclusive_rights',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberExclusiveRightsList'
          ),
        name: 'MemberExclusiveRightsList',
        meta: {
          noCache: true,

          title: 'member_exclusive_rights',
          permission: ['background_member.rights_receive.list']
        }
      },
      {
        path: 'member_exclusive_setting',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberExclusiveSetting'
          ),
        name: 'MemberExclusiveSetting',
        hidden: true,
        meta: {
          noCache: true,

          title: 'member_exclusive_setting',
          permission: ['background_member.rights_receive.list']
        }
      },
      {
        path: 'member_permission_manager',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberPermissionManager'
          ),
        name: 'MemberPermissionManager',
        meta: {
          noCache: true,

          title: 'member_permission_manager',
          permission: ['background_member.member_permission.list']
        }
      },
      {
        path: 'member_key_manager',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberKeyManager'
          ),
        name: 'MemberKeyManager',
        hidden: true,
        meta: {
          noCache: true,

          title: 'member_key_manager',
          permission: ['background_member.member_permission.list']
        }
      },
      {
        path: 'promotional_picture_setting_list',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/PromotionalPictureSettingList'
          ),
        name: 'PromotionalPictureSettingList',
        meta: {
          noCache: true,

          title: 'promotional_picture_setting_list',
          permission: ['background_member.promotional_picture.list']
        }
      },
      {
        path: 'promotional_picture_setting',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/PromotionalPictureSetting'
          ),
        name: 'PromotionalPictureSetting',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/member_center/promotional_picture_setting_list',
          title: 'promotional_picture_setting',
          permission: ['background_member.promotional_picture.list']
        }
      }
    ]
  },
  // 积分管理
  {
    path: '/super_points_admin',
    component: Layout,
    redirect: '/super_points_admin/points_order',
    name: 'pointsAdmin',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'points_admin',
      permission: ['points_management_l2']
    },
    children: [
      {
        path: 'points_order',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/points-admin/PointsOrder'
          ),
        name: 'pointsOrder',
        meta: {
          noCache: true,

          title: 'points_order',
          permission: ['background_member.points.points_order_list']
        }
      },
      {
        path: 'points_commodity',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/points-admin/PointsCommodity'
          ),
        name: 'pointsCommodity',
        meta: {
          noCache: true,

          title: 'points_commodity',
          permission: ['background_member.points.points_commodity_list']
        }
      },
      {
        path: 'points_task',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/points-admin/pointsTask'
          ),
        name: 'pointsTask',
        meta: {
          noCache: true,

          title: 'points_task',
          permission: ['background_member.points.points_task_list']
        }
      }
    ]
  },
  // 营销活动
  {
    path: '/super_marketing',
    component: Layout,
    redirect: '/super_marketing/banner',
    name: 'SuperIngredientsLibraryx',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'marketing',
      permission: ['marketing_activities_l2']
    },
    children: [
      {
        path: 'banner',
        component: () =>
          import(/* webpackChunkName: "super_ingredients_library" */ '@/views/super/operations-management/marketing/banner/Banner'),
        name: 'SuperBanner',
        meta: {
          noCache: true,

          title: 'banner_setting',
          permission: ['background.admin.marketing_banner']
        }
      },
      {
        path: 'add_banner/:type',
        component: () =>
          import(/* webpackChunkName: "super_add_banner" */ '@/views/super/operations-management/marketing/banner/AddBanner'),
        name: 'SuperAddBanner',
        hidden: true,
        meta: {
          noCache: true,

          title: 'banner',
          activeMenu: '/operations-system/marketing/banner',
          permission: ['background.admin.marketing_banner']
        }
      },
      {
        path: 'mobile_popup',
        component: () =>
          import(/* webpackChunkName: "super_mobile_popup" */ '@/views/super/operations-management/marketing/popup/MobilePopup'),
        name: 'SuperMobilePopup',
        meta: {
          noCache: true,

          title: 'mobile_popup',
          permission: ['background.admin.marketing_popup']
        }
      },
      {
        path: 'add_mobile_popup/:type',
        component: () =>
          import(/* webpackChunkName: "super_add_mobile_popup" */ '@/views/super/operations-management/marketing/popup/AddMobilePopup'),
        name: 'SuperAddMobilePopup',
        hidden: true,
        meta: {
          noCache: true,

          title: 'mobile_popup',
          activeMenu: '/operations-system/marketing/mobile_popup',
          permission: ['background.admin.marketing_popup']
        }
      },
      {
        path: 'super_article_push',
        component: Layout,
        redirect: '/super_marketing/super_article_push/article_admin',
        alwaysShow: true, // will always show the root menu
        name: 'SuperArticlePush',
        meta: {
          title: 'super_article_push',
          permission: ['background.admin.article']
        },
        children: [
          {
            path: 'article_admin',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/article-push/article-admin/articleAdmin'
              ),
            name: 'SuperArticleAdmin',
            meta: {
              noCache: true,

              title: 'super_article_admin',
              permission: ['background.admin.article.list']
            }
          },
          {
            path: 'add_edit_article',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/article-push/article-admin/addEditArticle'
              ),
            name: 'SuperAddEditArticle',
            hidden: true,
            meta: {
              noCache: true,

              activeMenu: '/super_article_push/article_admin',
              title: 'add_edit_article',
              permission: ['background.admin.article.list']
            }
          },
          {
            path: 'article_category',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/article-push/article-admin/ArticleCategory'
              ),
            name: 'SuperArticleCategory',
            meta: {
              noCache: true,

              title: 'super_article_category',
              permission: ['background.admin.article_category.list']
            }
          }
          // {
          //   path: 'article_label',
          //   component: () =>
          //     import(
          //       /* webpackChunkName: "" */ '@/views/super/article-push/article_label/articleLabel'
          //     ),
          //   name: 'SuperArticleLabel',
          //   meta: {
          //     noCache: true,
          //     title: 'super_article_label',
          //     permission: ['background.admin.article_tag.list']
          //   },
          //   children: []
          // }
        ]
      }
    ]
  },
  // 评价管理
  {
    path: '/super_evaluation_management',
    component: Layout,
    redirect: '/super_evaluation_management/feedback',
    alwaysShow: true,
    name: 'SuperEvaluationManagement',
    meta: {
      noCache: true,
      title: 'super_evaluation_management',
      permission: ['evaluation_management_l2']
    },
    children: [
      {
        path: 'feedback',
        component: () =>
          import(/* webpackChunkName: "feedback" */ '@/views/super/operations-management/feedback'),
        name: 'SuperOperationsManagementFeedback',
        meta: {
          noCache: true,
          title: 'feedback',
          permission: ['background_feedback.super_feedback_record.list']
        }
      }
    ]
  },
  // 短信管理
  {
    path: '/super_sms_management',
    component: Layout,
    alwaysShow: true,
    redirect: '/super_sms_management/setting',
    name: 'SmsSetting',
    meta: {
      title: 'sms_setting',
      permission: ['sms_management_l2']
    },
    children: [
      {
        path: 'sms_setting',
        component: () =>
          import(
          /* webpackChunkName: "SmsSetting" */ '@/views/super/system-settings/SmsSetting'
          ),
        name: 'SmsSetting',
        meta: {
          noCache: true,

          title: 'sms_setting',
          permission: ['background.admin.sms_info_template.get_settings']
        }
      },
      {
        path: 'guoxun_sms',
        component: () =>
          import(/* webpackChunkName: "sms_template" */ '@/views/super/operations-management/guoxun-management'),
        name: 'SuperGuoxunSmsManagement',
        meta: {
          noCache: true,

          title: 'guoxun_sms_management',
          permission: ['background.admin.sms_info_template.list']
        }
      },
      {
        path: 'sms_call_record',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/member-center/MemberSmsCallRecord'
          ),
        name: 'smsCallRecord',
        meta: {
          noCache: true,

          title: 'sms_call_record',
          permission: ['background_member.sms_push_receive.list']
        }
      }
    ]
  },
  // 激活码管理
  {
    path: '/super_device_admin',
    component: Layout,
    redirect: '/super_device_admin/active_code_admin',
    alwaysShow: true, // will always show the root menu
    name: 'SuperDevice',
    meta: {
      title: 'active_code_admin',
      permission: ['admin_device_l2']
    },
    children: [
      {
        path: 'active_code_admin',
        component: () =>
          import(
            /* webpackChunkName: "super_active_code" */ '@/views/super/device-admin/ActiveCodeAdmin'
          ),
        name: 'SuperActiveCodeAdmin',
        meta: {
          noCache: true,

          title: 'active_code_admin',
          permission: ['background.admin.device.device_info']
        }
      },
      {
        path: 'device_ceil_set',
        component: () =>
          import(
            /* webpackChunkName: "device_ceil_set" */ '@/views/super/device-admin/DeviceCeilSet'
          ),
        name: 'SuperDeviceCeilSet',
        hidden: true,
        meta: {
          noCache: true,

          title: 'device_ceil_set',
          activeMenu: '/super_device_admin/active_code_admin',
          permission: ['background.admin.disclaimer']
        }
      },
      {
        path: 'secret_key_admin',
        component: () => import(/* webpackChunkName: "secret_key_admin" */ '@/views/super/device-admin/SecretkeyAdmin'),
        name: 'SuperSecretkeyAdmin',
        meta: {
          noCache: true,

          title: 'secret_key_admin',
          activeMenu: '/super_device_admin/secret_key_admin',
          permission: ['background_device.admin.third_device']
        }
      },
      {
        path: 'import_active_code',
        component: () =>
          import(
            /* webpackChunkName: "import_active_code" */ '@/views/super/device-admin/ImportActiveCode'
          ),
        name: 'SuperImportActiveCode',
        hidden: true,
        meta: {
          noCache: true,

          title: 'import_active_code_admin',
          permission: ['background_device.admin.device_group']
        }
      },
      // 设备组管理
      {
        path: 'device_group',
        component: () => import(/* webpackChunkName: "secret_key_admin" */ '@/views/super/device-admin/DeviceGroup'),
        name: 'SuperDeviceGroup',
        meta: {
          noCache: true,

          title: 'device_group',
          // activeMenu: '/super_device_admin/active_code_admin',
          permission: ['background_device.admin.device_group']
        }
      },
      {
        path: 'super_third_party_equipment_admin',
        component: () =>
          import(
            /* webpackChunkName: "super_third_party_equipment_admin" */ '@/views/super/device-admin/ThirdPartyEquipmentAdmin'
          ),
        name: 'superThirdPartyEquipmentAdmin',
        meta: {
          noCache: true,

          title: 'third_party_equipment_admin',
          permission: ['background_device.admin.third_device']
        }
      },
      {
        path: 'import_third_party_equipment',
        component: () =>
          import(
            /* webpackChunkName: "super_add_ingredients" */ '@/views/super/device-admin/ImportThirdPartyEquipment'
          ),
        name: 'SuperImportThirdPartyEquipment',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_third_party_equipment/super_third_party_equipment_admin',
          title: 'import_third_party_equipment',
          permission: ['background_device.admin.third_device']
        }
      }
    ]
  },
  // 设备管理
  {
    path: '/super_device_management',
    component: Layout,
    redirect: '/super_device_management/super_device_version',
    alwaysShow: true, // will always show the root menu
    name: 'SuperDeviceVersion',
    meta: {
      title: 'super_device_management',
      permission: ['device_management_l2']
    },
    children: [
      {
        path: 'super_device_version',
        component: Layout,
        redirect: '/super_device_management/super_device_version/device',
        alwaysShow: false, // will always show the root menu
        name: 'SuperDeviceVersion',
        meta: {
          title: 'super_device_version',
          permission: ['background_device.admin.device_version']
        },
        children: [
          {
            path: 'device',
            component: () =>
              import(/* webpackChunkName: "super_device_version" */ '@/views/super/device-admin/ClientVersion'),
            name: 'SuperDeviceVesion',
            hidden: true,
            meta: {
              noCache: true,
              title: 'super_device_version',
              permission: ['background_device.admin.device_version']
            }
          },
          {
            path: 'device_version_list',
            component: () =>
              import(/* webpackChunkName: "super_device_version" */ '@/views/super/device-admin/ClientVersionList'),
            name: 'SuperDeviceVesionList',
            hidden: true,
            meta: {
              noCache: true,
              title: 'super_device_version_list',
              permission: ['background_device.admin.device_version']
            }
          },
          {
            path: 'add_super_device_version',
            component: () =>
              import(/* webpackChunkName: "add_super_device_version" */ '@/views/super/device-admin/AddClientVersion'),
            name: 'SuperAddClientVersion',
            hidden: true,
            meta: {
              noCache: true,
              title: 'add_super_device_version',
              permission: ['background_device.admin.device_version']
            }
          }
        ]
      },
      {
        path: 'super_device_log',
        component: () =>
          import(/* webpackChunkName: "super_device_log" */ '@/views/super/device-admin/DeviceLog'),
        name: 'SuperDeviceLog',
        meta: {
          noCache: true,

          title: 'super_device_log',
          permission: ['background.admin.device.device_info.log_list']
        }
      },
      {
        path: 'scale_recognition_statistics',
        name: 'ScaleRecognitionStatistics',
        component: () =>
          import(
            /* webpackChunkName: "super_satisfaction" */ '@/views/super/system-settings/ScaleRecognitionStatistics'
          ),
        meta: {
          noCache: true,

          title: 'scale_recognition_statistics',
          // no_permission: true
          permission: ['background.admin.recognition_rate_statistics']
        }
      }
    ]
  },
  // 档案管理
  {
    path: '/super_health_system',
    component: Layout,
    // redirect: '/super_health_system/health_nutrition/ingredients_library',
    redirect: '/super_health_system/user_health_records',
    alwaysShow: true, // will always show the root menu
    name: 'SuperHealthSystem',
    meta: {
      title: 'health_system',
      permission: ['archives_management_l2']
    },
    children: [
      {
        path: 'user_health_records',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/user-health-records/UserHealthRecords'
          ),
        name: 'SuperUserHealthRecords',
        meta: {
          noCache: true,

          title: 'user_health_records',
          permission: ['background.admin.healthy_info.list']
        },
        children: [
          {
            path: 'records_detail',
            component: () =>
              import(
                /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/user-health-records/RecordsDetail'
              ),
            name: 'SuperRecordsDetail',
            hidden: true,
            meta: {
              noCache: true,

              activeMenu: '/super_health_system/user_health_records',
              title: 'records_detail',
              permission: ['background.admin.healthy_info.list']
            }
          },
          {
            path: 'body_detail',
            component: () =>
              import(
                /* webpackChunkName: "BodyDetail" */ '@/views/super/health-system/user-health-records/detail/BodyDetail'
              ),
            name: 'SuperBodyDetail',
            hidden: true,
            meta: {
              noCache: true,

              activeMenu: '/super_health_system/user_health_records',
              title: 'body_detail',
              permission: ['background.admin.healthy_info.list']
            }
          },
          {
            path: 'body_view_report',
            component: () =>
              import(
                /* webpackChunkName: "BodyDetail" */ '@/views/super/health-system/user-health-records/detail/BodyViewReport'
              ),
            name: 'SuperBodyViewReport',
            hidden: true,
            meta: {
              noCache: true,

              activeMenu: '/super_health_system/user_health_records',
              title: 'view_report',
              permission: ['background.admin.healthy_info.list']
            }
          }
        ]
      },
      {
        path: 'nutritional_analysis',
        component: () =>
          import(
            /* webpackChunkName: "super_nutritional_analysis" */ '@/views/super/health-system/nutritional-analysis/index'
          ),
        name: 'SuperNutritionalAnalysis',
        hidden: true,
        meta: {
          noCache: true,

          title: 'nutritional_analysis',
          activeMenu: '/super_health_system/user_health_records',
          permission: ['background.admin.ingredient.list']
        }
      },
      {
        path: 'ingredients_library',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/health-nutrition/IngredientsLibrary'
          ),
        name: 'SuperIngredientsLibrary',
        meta: {
          noCache: true,

          title: 'ingredients_library',
          permission: ['background.admin.ingredient.list']
        }
      },
      {
        path: 'ingredients_detail_:type',
        component: () =>
          import(
            /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/health-nutrition/AddIngredients'
          ),
        name: 'SuperAddIngredients',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/health_nutrition/ingredients_library',
          title: 'ingredients',
          permission: ['background.admin.ingredient.list']
        }
      },
      {
        path: 'import_ingredients/:type',
        component: () =>
          import(
            /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/health-nutrition/ImportIngredients'
          ),
        name: 'SuperImportIngredients',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/health_nutrition/ingredients_library',
          title: 'import_ingredients',
          permission: ['background.admin.ingredient.list']
        }
      },
      {
        path: 'ingredients_category',
        component: () =>
          import(
            /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/health-nutrition/IngredientsCategoryNew'
          ),
        name: 'SuperIngredientsCategoryNew',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/health_nutrition/ingredients_library',
          title: 'ingredients_category',
          permission: ['background.admin.ingredient.list']
        }
      },
      {
        path: 'commodity',
        component: () =>
          import(
            /* webpackChunkName: "super_commodity" */ '@/views/super/health-system/health-nutrition/CommodityLibrary'
          ),
        name: 'SuperCommodity',
        meta: {
          noCache: true,

          title: 'super_commodity',
          permission: ['background.admin.food.list']
        }
      },
      {
        path: 'add_commodity',
        component: () =>
          import(
            /* webpackChunkName: "super_commodity" */ '@/views/super/health-system/health-nutrition/AddCommodity'
          ),
        name: 'SuperAddCommodity',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/health_nutrition/commodity',
          title: 'super_add_commodity',
          permission: ['background.admin.food.list']
        }
      },
      {
        path: 'add_commodity_to_super',
        component: () =>
          import(
            /* webpackChunkName: "super_commodity" */ '@/views/super/health-system/health-nutrition/AddMerchantCommodityToSuper'
          ),
        name: 'SuperAddMerchantCommodityToSuper',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/health_nutrition/commodity',
          title: 'super_add_commodity',
          permission: ['background.admin.food.list']
        }
      },
      {
        path: 'import_commodity/:type',
        component: () =>
          import(
            /* webpackChunkName: "super_add_ingredients" */ '@/views/super/health-system/health-nutrition/ImportCommodity'
          ),
        name: 'SuperImportCommodity',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/health_nutrition/commodity',
          title: 'import_commodity',
          permission: ['background.admin.food.list']
        }
      },
      {
        path: 'import_commodity_image',
        component: () =>
          import(
            /* webpackChunkName: "import_commodity_image" */ '@/views/super/health-system/health-nutrition/ImportCommodityImage'
          ),
        name: 'SuperImportCommodityImage',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/health_nutrition/commodity',
          title: 'import_commodity_image',
          permission: ['background.admin.food.list']
        }
      },
      {
        path: 'import_ingredient_image',
        component: () =>
          import(
            /* webpackChunkName: "super_import_ingredient_image" */ '@/views/super/health-system/health-nutrition/ImportIngredientImage'
          ),
        name: 'SuperImportIngredientImage',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/health_nutrition/ingredients_library',
          title: 'import_ingredient_image',
          permission: ['background.admin.food.list']
        }
      },
      {
        path: 'meal_food_classification',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/health-nutrition/MealFoodClassificationNew'
          ),
        name: 'SuperMealFoodClassificationNew',
        hidden: true,
        meta: {
          activeMenu: '/super_health_system/health_nutrition/commodity',
          noCache: true,

          title: 'meal_food_classification',
          permission: ['background.admin.food.list']
        }
      },
      {
        path: 'label_admin',
        redirect: '/label_admin/food_label',
        // component: () =>
        //   import(
        //     /* webpackChunkName: "" */ '@/views/super/health-system/nutrition-rules-core/GuidanceRules'
        //   ),
        name: 'SuperLabelAdmin',
        alwaysShow: true,
        meta: {
          noCache: true,

          title: 'label_admin',
          permission: ['background_healthy.admin.label_group']
        },
        children: [
          {
            path: 'food_label',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/label-admin/FoodLabel'
              ),
            name: 'SuperFoodLabel',
            meta: {
              noCache: true,

              title: 'food_label',
              permission: ['background_healthy.admin.label_group.list']
            }
          },
          {
            path: 'ingredients_label',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/label-admin/IngredientsLabel'
              ),
            name: 'SuperIngredientsLabel',
            meta: {
              noCache: true,

              title: 'ingredients_label',
              permission: ['background_healthy.admin.label_group.list']
            }
          },
          {
            path: 'user_label',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/label-admin/UserLabel'
              ),
            name: 'SuperUserLabel',
            meta: {
              noCache: true,

              title: 'user_label',
              permission: ['background_healthy.admin.label_group.list']
            }
          }
        ]
      }
    ]
  },
  // 参数配置
  {
    path: '/super_parameter_configuration',
    component: Layout,
    redirect: '/super_parameter_configuration/parameter-config',
    alwaysShow: true,
    meta: {
      title: 'parameter_configuration',
      permission: ['parameter_configuration_l2']
    },
    children: [

      { // 运动管理
        path: 'motion_admin',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/parameter-config/motion-admin/MotionAdmin'
          ),
        name: 'SuperMotionAdmin',
        meta: {
          noCache: true,

          title: 'motion_admin',
          permission: ['background.admin.sports.list']
        }
      },
      {
        path: 'auto_label',
        component: () =>
          import(
            /* webpackChunkName: "super_auto_label" */ '@/views/super/health-system/parameter-config/auto-label/AutoLabel'
          ),
        name: 'SuperAutoLabel',
        meta: {
          noCache: true,

          title: 'auto_label',
          permission: ['background_healthy.admin.auto_label.list']
        }
      },

      { // 疾病管理
        path: 'disease_admin',
        component: () =>
          import(
            /* webpackChunkName: "super_disease_admin" */ '@/views/super/health-system/parameter-config/disease-admin/DiseaseAdmin'
          ),
        name: 'SuperDiseaseAdmin',
        meta: {
          noCache: true,

          title: 'disease_admin',
          permission: ['background_healthy.admin.disease.list']
        }
      },
      {
        path: 'diet_manage',
        redirect: '/diet_manage/menu_plan_list',
        name: 'SuperDietManage',
        alwaysShow: true,
        meta: {
          noCache: true,

          title: 'diet_manage',
          permission: ['background_healthy.diet_manage']
        },
        children: [
          {
            path: 'menu_plan_list',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/MenuPlanList'
              ),
            name: 'SuperMenuPlanList',
            meta: {
              noCache: true,

              title: 'menu_plan_list',
              permission: ['background_healthy.menu_plan.list']
            }
          },
          {
            path: 'menu_plan_list/:type',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/AddOrEditMenuPlanList'
              ),
            name: 'SuperAddOrEditMenuPlanList',
            hidden: true,
            meta: {
              noCache: true,

              title: 'menu_plan_list',
              activeMenu: '/super_health_system/diet_manage/menu_plan_list',
              permission: ['background_healthy.menu_plan.list']
            }
          },
          {
            path: 'leave_message',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/LeaveMessage'
              ),
            name: 'SuperLeaveMessage',
            hidden: true,
            meta: {
              noCache: true,

              title: 'leave_message',
              activeMenu: '/super_health_system/diet_manage/menu_plan_list',
              permission: ['background_healthy.menu_plan.list']
            }
          },
          {
            path: 'three_meal_list',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/ThreeMealList'
              ),
            name: 'SuperThreeMealList',
            meta: {
              noCache: true,

              title: 'three_meal_list',
              permission: ['background_healthy.three_meal.list']
            }
          },
          {
            path: 'three_meal_list/:type',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/super/health-system/diet-manage/AddOrEditThreeMealList'
              ),
            name: 'SuperAddOrEditThreeMealList',
            hidden: true,
            meta: {
              noCache: true,

              title: 'three_meal_list',
              activeMenu: '/super_health_system/diet_manage/three_meal_list',
              permission: ['background_healthy.three_meal.list']
            }
          }
        ]
      },
      {
        path: 'habit_cultivate',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/health-habit-cultivate/HabitCultivate'
          ),
        name: 'SuperHabitCultivate',
        meta: {
          noCache: true,

          title: 'super_habit_cultivate',
          permission: ['background_healthy.admin.habit.list']
        },
        children: [
          {
            path: 'habit/:type',
            component: () =>
              import(/* webpackChunkName: "habit_cultivate" */ '@/views/super/health-system/health-habit-cultivate/AddModifyHabit'
              ),
            name: 'SuperAddModifyHabit',
            hidden: true,
            meta: {
              noCache: true,

              title: 'super_habit_cultivate',
              activeMenu: '/super_health_system/habit_cultivate',
              permission: ['background_healthy.admin.habit.list']
            }
          }
        ]
      },
      {
        path: 'crowd_admin',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/parameter-config/crowd-admin/CrowdAdmin'
          ),
        name: 'SuperCrowdAdmin',
        meta: {
          noCache: true,

          title: 'crowd_admin',
          permission: ['background_healthy.admin.crowd.list']
        }
      },
      {
        path: 'score_time',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/health-system/parameter-config/score-time/ScoreTime'
          ),
        name: 'SuperScoreTime',
        meta: {
          noCache: true,

          title: 'score_time',
          permission: ['background.admin.healthy_info.healthy_meal_time_list']
        }
      },
      {
        path: 'modify_score_time',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/parameter-config/score-time/ModifyScoreTime'
          ),
        name: 'SuperModifyScoreTime',
        hidden: true,
        meta: {
          // noCache: true,

          activeMenu: '/super_health_system/parameter-config/score_time',
          title: 'modify_score_time',
          permission: ['background.admin.healthy_info.healthy_meal_time_list']
        }
      },
      {
        path: 'add_or_modify_crowd_:type',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "ingredients_category" */ '@/views/super/health-system/parameter-config/crowd-admin/AddOrModifyCrowd'
          ),
        name: 'SuperAddOrModifyCrowd',
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/parameter-config/crowd_admin',
          title: 'add_or_modify_crowd',
          permission: ['background_healthy.admin.crowd.list']
        }
      },
      {
        path: 'guidance_rules',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/nutrition-rules-core/GuidanceRules'
          ),
        name: 'SuperNutritionGuidanceRules',
        meta: {
          noCache: true,

          title: 'nutrition_guidance_rules',
          permission: ['background_healthy.admin.nutrition_rule.list']
        }
      },
      {
        path: 'modify_guidance_rules',
        component: () =>
          import(
            /* webpackChunkName: "" */ '@/views/super/health-system/nutrition-rules-core/ModifyGuidanceRules'
          ),
        name: 'SuperModifyGuidanceRules',
        hidden: true,
        meta: {
          noCache: true,

          activeMenu: '/super_health_system/nutrition_rules_core/guidance_rules',
          title: 'modify_guidance_rules',
          permission: ['background_healthy.admin.nutrition_rule.list']
        }
      }
    ]
  },
  // 员工管理
  {
    path: '/super_employee_management',
    component: Layout,
    redirect: '/super_employee_management/department',
    alwaysShow: true,
    name: 'SuperUserManagement',
    meta: {
      title: 'user_management',
      permission: ['admin_user_management_l2']
    },
    children: [
      {
        path: 'department',
        component: () =>
          import(
            /* webpackChunkName: "super_department" */ '@/views/super/employee-admin/Department'
          ),
        name: 'SuperDepartment',
        meta: {
          noCache: true,

          title: 'department_management',
          permission: ['background.admin.department']
        }
      },
      {
        path: 'role',
        component: () =>
          import(/* webpackChunkName: "super_role" */ '@/views/super/employee-admin/RoleList'),
        name: 'SuperRoleList',
        meta: {
          noCache: true,

          title: 'role_management',
          permission: ['background.admin.role']
        }
      },
      {
        path: 'role/setting',
        component: () =>
          import(
            /* webpackChunkName: "super_roleSetting" */ '@/views/super/employee-admin/RoleSetting'
          ),
        name: 'SuperRoleSetting',
        hidden: true,
        meta: {
          noCache: true,

          title: 'role_setting',
          activeMenu: '/super_employee_management/role',
          permission: ['background.admin.role']
        }
      },
      {
        path: 'account',
        component: () =>
          import(
            /* webpackChunkName: "super_account" */ '@/views/super/employee-admin/AccountList'
          ),
        name: 'SuperAccountList',
        meta: {
          noCache: true,

          title: 'account_management',
          permission: ['background.admin.account']
        }
      }
    ]
  },
  // 收费管理
  {
    path: '/charge_management',
    redirect: '/charge_management/toll_rule',
    component: Layout,
    name: 'ChargeManagement',
    alwaysShow: true,
    meta: {
      title: 'charge_management',
      permission: ['background.admin.background_toll_l2']
    },
    children: [
      {
        path: 'toll_rule',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/charge-management/ChargingRules'
          ),
        name: 'ChargingRules',
        meta: {
          noCache: true,

          title: 'charging_rules',
          permission: ['background.admin.background_toll_rule']
        }
      },
      {
        path: 'toll',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/charge-management/ChargeTrail'
          ),
        name: 'ChargeTrail',
        meta: {
          noCache: true,

          title: 'charging_trail',
          permission: ['background.admin.background_toll.list']
        }
      },
      {
        path: 'toll_order',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/charge-management/ChargeOrder'
          ),
        name: 'ChargeOrder',
        meta: {
          noCache: true,

          title: 'charge_order',
          permission: ['background.admin.background_toll_order.list']
        }
      },
      {
        path: 'version_configuration',
        component: () =>
          import(
            /* webpackChunkName: "super_ingredients_library" */ '@/views/super/charge-management/VersionConfiguration'
          ),
        name: 'VersionConfiguration',
        meta: {
          noCache: true,

          title: 'version_configuration',
          permission: ['background.admin.background_toll_version.list']
        }
      }
    ]
  },
  // 系统管理
  {
    path: '/super_system',
    component: Layout,
    redirect: '/super_system/update_record',
    alwaysShow: true,
    name: 'SuperSystem',
    meta: {
      title: 'merchant_system',
      permission: ['admin_system_l2']
    },
    children: [
      {
        path: 'update_record',
        component: () =>
          import(
            /* webpackChunkName: "super_notice_admin" */ '@/views/super/system-settings/UpdateRecord'
          ),
        name: 'SuperUpdateRecord',
        meta: {
          noCache: true,

          title: 'update_record',
          permission: ['background.admin.sys_update_record.list']
        }
      },
      {
        path: 'loglist',
        component: () =>
          import(/* webpackChunkName: "super_loglist" */ '@/views/public/log/OperatingLogList'),
        name: 'LogList',
        meta: {
          noCache: true,

          title: 'list_log',
          permission: ['background.admin.log.list_log']
        }
      },
      {
        path: 'notice-:type',
        component: () =>
          import(
            /* webpackChunkName: "super_notice_add" */ '@/views/super/system-settings/NoticeAdd'
          ),
        name: 'SuperNoticeAdd',
        hidden: true,
        meta: {
          noCache: true,

          title: 'notice',
          activeMenu: '/super_system/notice_admin'
          // permission: ['admin_merchant_management']
        }
      },
      {
        path: 'notice_detail',
        component: () =>
          import(
            /* webpackChunkName: "super_notice_detail" */ '@/views/super/system-settings/NoticeDetail'
          ),
        name: 'SuperNoticeDetail',
        hidden: true,
        meta: {
          noCache: true,

          title: 'notice',
          activeMenu: '/super_system/notice_admin'
          // permission: ['admin_merchant_management']
        }
      },
      // 满意度
      {
        path: 'satisfaction_setting',
        name: 'MerchantSatisfactionSetting',
        component: () =>
          import(
            /* webpackChunkName: "super_satisfaction" */ '@/views/merchant/system/satisfaction/SatisfactionSetting'
          ),
        meta: {
          noCache: true,

          title: 'satisfaction_setting',
          // no_permission: true
          permission: ['background.admin.satisfaction.get_config_list']
        }
      },
      {
        path: 'satisfaction_total',
        name: 'MerchantSatisfactionTotal',
        component: () =>
          import(
            /* webpackChunkName: "super_satisfaction" */ '@/views/merchant/system/satisfaction/SatisfactionTotal'
          ),
        meta: {
          noCache: true,

          title: 'satisfaction_total',
          // no_permission: true
          permission: ['background.admin.satisfaction.statistics']
        }
      },
      {
        path: 'satisfaction_detail',
        name: 'MerchantSatisfactionDetail',
        component: () =>
          import(
            /* webpackChunkName: "super_satisfaction" */ '@/views/merchant/system/satisfaction/SatisfactionDetail'
          ),
        meta: {
          noCache: true,

          title: 'satisfaction_detail',
          // no_permission: true
          permission: ['background.admin.satisfaction.detail_list']
        }
      }
    ]
  }
]
export default system
