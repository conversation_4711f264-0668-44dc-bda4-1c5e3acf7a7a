<template>
  <dialog-message :show.sync="visible" :title="title" :showFooter="showFooter" :loading.sync="isLoading"
    @close="handleClose" customClass="ps-dialog" :width="width">
    <div v-if="images" class="ps-flex">
      <div>
        <img :src="images.scene_img" class="pic-tag2">
        <div class="m-t-10 text-center font-size-24">{{ name }}</div>
      </div>
      <img :src="images.picture_img" class="pic-tag2 m-l-10">
      <img :src="images.picture_back_img" class="pic-tag2 m-l-10">
    </div>
    <div v-else>
      <span style="color: #999;">暂无图片</span>
    </div>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickCancleHandle">
          关闭
        </el-button>
      </div>
    </template>

  </dialog-message>
</template>
<script>
export default {
  name: 'PicDetailsDialog',
  props: {
    loading: Boolean,
    showFooter: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '晨检图片'
    },
    width: {
      type: String,
      default: '1560px'
    },
    images: {
      type: Object,
      default() {
        return {}
      }
    },
    name: {
      type: String,
      default: ''
    },
    isshow: Boolean
  },
  data() {
    return {
      isLoading: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
    }
  },
  created() {
    this.initLoad()
  },
  mounted() { },
  methods: {
    initLoad() {
      console.log("initLoad", this.images)
    },
    clickCancleHandle() {
      this.visible = false
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.pic-tag {
  width: 250px;
  height: 400px;
}

.pic-tag2 {
  width: 500px;
  height: 400px;
}
.font-size-24{
  font-size: 24px;
}
</style>
