<template>
  <div class="channel-info-content">
    <el-form :label-position="'left'" label-width="100px">
      <el-form-item label="账号管理：">
        <el-button type="text" class="m-r-20" @click="showDrawer('add')">添加账号</el-button>
        <div class="table-style">
          <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #isDoubleFactor="{ row }">
                <el-switch v-model="row.is_double_factor" @change="openOrNot(row)"></el-switch>
              </template>
              <template #operation="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="showDrawer('edit', row)">编辑</el-button>
                <el-button type="text" size="small" class="ps-warn-text" @click="deleteHandle(row)">移除</el-button>
              </template>
            </table-column>
          </el-table>
          <!-- 分页 start -->
          <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="page"
              :page-sizes="[10, 20, 50, 100, 500]"
              :page-size="pageSize"
              layout="total, prev, pager, next, sizes, jumper"
              :total="totalCount"
              background
              class="ps-text"
              popper-class="ps-popper-select"
            ></el-pagination>
          </div>
          <!-- 分页 end -->
        </div>
      </el-form-item>
    </el-form>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="drawerType === 'add' ? '添加账号' : '编辑账号'"
        :visible="addAccountDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <div class="red font-size-14 m-b-20">提示：账号、手机号只能对应一个监管渠道。</div>
          <el-form ref="addAccountFormRef" :rules="addAccountFormRule" :model="addAccountForm" label-width="80px" label-position="right">
            <el-form-item :label="'用户姓名'" prop="name">
              <el-input v-model="addAccountForm.name" class="w-300" placeholder="请输入姓名" maxlength="10"></el-input>
            </el-form-item>
            <el-form-item :label="'手机号码'" prop="phone">
              <el-input v-model="addAccountForm.phone" class="w-300" placeholder="请输入手机号码" maxlength="11"></el-input>
            </el-form-item>
            <el-form-item :label="'登录账号'" prop="account">
              <el-input v-model="addAccountForm.account" class="w-300" placeholder="请输入登录账号" maxlength="12" :disabled="drawerType === 'edit'"></el-input>
            </el-form-item>
            <el-form-item :label="'登录密码'" prop="password"
              :rules="[
                { required: drawerType === 'add', message: '请输入密码', trigger: ['change', 'blur'] },
                { min: 8, message: '登录密码不能小于8位', trigger: ['change', 'blur'] },
                { pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,20}$/, message: '请输入8~20位数，需包含英文和数字的密码', trigger: ['change', 'blur'] }
              ]">
              <el-input v-model="addAccountForm.password" class="w-300" :placeholder="drawerType === 'add' ? '请输入登录密码' : '无需修改密码则不填'" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item :label="'登录校验'" prop="isCheck">
              <div class="ps-flex-align-c flex-align-c">
                <div class="m-r-10">手机验证码校验</div>
                <el-switch v-model="addAccountForm.isCheck" />
              </div>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('save')">保存</el-button>
            <el-button v-show="drawerType === 'add'" size="small" type="primary" class="w-100" @click="saveHandle('keepGoing')">保存并继续</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import md5 from 'js-md5';
export default {
  props: {
    organizationData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    tabType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '姓名', key: 'name' },
        { label: '账号', key: 'username' },
        { label: '手机号', key: 'mobile' },
        { label: '验证码校验', key: 'is_double_factor', type: "slot", slotName: "isDoubleFactor" },
        { label: '修改时间', key: 'update_time' },
        { label: '操作人', key: 'operator_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "200" }
      ],
      drawerType: '',
      selectId: '',
      addAccountDrawerShow: false,
      addAccountForm: {
        name: '',
        phone: '',
        account: '',
        password: '',
        isCheck: false
      },
      page: 1,
      pageSize: 10,
      totalCount: 0,
      addAccountFormRule: {
        name: [{ required: true, message: '请输入姓名', trigger: ['change', 'blur'] }],
        phone: [
          { required: true, message: '请输入手机号', trigger: ['change', 'blur'] },
          { min: 8, message: '请输入正确的手机号', trigger: ['change', 'blur'] }
        ],
        account: [
          { required: true, message: '请输入登录账号', trigger: ['change', 'blur'] },
          { min: 5, message: '登录账号不能小于5位', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  watch: {
    tabType: {
      handler: function(newVal, oldVal) {
        if (newVal === 'accountManagement') {
          console.log('切换了')
          this.getAccountDataList()
        }
      },
      immediate: true
    }
  },
  created() {
    this.getAccountDataList()
  },
  methods: {
    showDrawer(type, data) {
      this.drawerType = type
      if (type === 'edit') {
        this.selectId = data.id
        this.addAccountForm.name = data.name
        this.addAccountForm.phone = data.mobile
        this.addAccountForm.account = data.username
        this.addAccountForm.password = ''
        this.addAccountForm.isCheck = false
      } else {
        this.selectId = ''
        this.addAccountForm.name = ''
        this.addAccountForm.phone = ''
        this.addAccountForm.account = ''
        this.addAccountForm.password = ''
        this.addAccountForm.isCheck = false
      }
      this.addAccountDrawerShow = true
      setTimeout(() => {
        this.$refs.addAccountFormRef.clearValidate()
      }, 10)
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getAccountDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.getAccountDataList()
    },
    // 抽屉的方法
    cancelHandle() {
      this.$refs.addAccountFormRef.resetFields()
      this.addAccountDrawerShow = false
    },
    saveHandle(type) {
      this.$refs.addAccountFormRef.validate((valid) => {
        if (valid) {
          let params = {
            id: this.drawerType === 'add' ? undefined : this.selectId,
            supervision_channel_id: this.organizationData.id,
            username: this.addAccountForm.account,
            password: this.addAccountForm.password ? md5(this.addAccountForm.password) : undefined,
            mobile: this.addAccountForm.phone,
            name: this.addAccountForm.name,
            is_double_factor: this.addAccountForm.isCheck
          }
          if (this.drawerType === 'add') {
            this.addAccount(params, type)
          } else {
            this.editAccount(params)
          }
        } else {
          return this.$message.error('请检查表单填写的内容是否正确')
        }
      })
    },
    getAccountDataList() {
      this.isLoading = true
      this.$apis.apiBackgroundFundSupervisionAuditAccountListPost({
        page: this.page,
        page_size: this.pageSize,
        supervision_channel_id: this.organizationData.id
      }).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          console.log('res.data', res.data)
          this.tableData = deepClone(res.data.results || [])
          this.totalCount = res.data.count
        }
      })
    },
    addAccount(params, type) {
      this.$apis.apiBackgroundFundSupervisionAuditAccountAddPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('新增成功')
          this.$refs.addAccountFormRef.resetFields()
          if (type === 'keepGoing') {
            return this.getAccountDataList()
          } else {
            this.addAccountDrawerShow = false
            this.getAccountDataList()
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    editAccount(params) {
      this.$apis.apiBackgroundFundSupervisionAuditAccountModifyPost(params).then(res => {
        if (res.code === 0) {
          this.$refs.addAccountFormRef.resetFields()
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
        }
        this.addAccountDrawerShow = false
        this.getAccountDataList()
      })
    },
    openOrNot(data) {
      let params = {
        id: data.id,
        username: data.username,
        is_double_factor: data.is_double_factor
      }
      this.editAccount(params)
    },
    deleteHandle(data) {
      this.$confirm(`确定要删除 ${data.name} 的人员信息，删除后不可恢复，请谨慎操作。`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$apis.apiBackgroundFundSupervisionAuditAccountDeletePost({
          ids: [data.id]
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
          } else {
            this.$message.error(res.msg)
          }
          this.getAccountDataList()
        })
      }).catch(action => {
        this.$message('已取消删除')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.channel-info-content {
  padding: 10px;
}
</style>
