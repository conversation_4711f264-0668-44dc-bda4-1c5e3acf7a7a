<template>
  <div class="CouponAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting" :autoSearch="false"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="gotoAddOrEdit('add')" v-permission="['background_datascreen.kanban_temp.add']">新建模板</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="temp_name" label="模板名称" align="center"></el-table-column>
          <el-table-column prop="type" label="说明" align="center"></el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <span style="margin-right: 10px; font-size: 12px; color:#ff9b45;">
                <a :href="scope.row.link" target="_blank">链接</a>
              </span>
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="gotoAddOrEdit('edit', scope.row.id)"
                v-permission="['background_datascreen.kanban_temp.modify']"
                >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="delBoard(scope.row.id)"
                v-permission="['background_datascreen.kanban_temp.delete']"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <!-- <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination> -->
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce, getToken } from '@/utils'
export default {
  name: 'CouponAdmin',
  data() {
    return {
      organizationId: this.$store.getters.organization,
      companyId: this.$store.getters.userInfo.company_id,
      isLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: {
        name: {
          type: 'input',
          label: '名称',
          value: '',
          maxlength: 20,
          placeholder: '请输入模板名称'
        }
      }
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getBoardList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getBoardList()
      }
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getBoardList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDatascreenKanbanTempListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize,
        organization_id: this.organizationId
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.data
        console.log("this.companyId", this.companyId)
        this.tableData.map(item => {
          if (item.temp_name === '经营数据大屏') {
            item.link = process.env.VUE_APP_DASHBOARD + '/#/dashboard-smart-canteen?organization_id=' + this.organizationId + '&board_id=' + item.id
          } else if (item.temp_name === '监测屏') {
            item.link = `${process.env.VUE_APP_DASHBOARD}/#/take_meal_report?token=${getToken()}&organization_id=${this.organizationId}&board_id=${item.id}`
          } else if (item.temp_name === '菜品余量大屏') {
            item.link = `${process.env.VUE_APP_DASHBOARD}/#/food_stock?token=${getToken()}&organization_id=${this.organizationId}&board_id=${item.id}`
          } else if (item.temp_name === '叫号屏') {
            item.link = `${process.env.VUE_APP_DASHBOARD}/#/meal-queue?token=${getToken()}&organization_id=${this.organizationId}&board_id=${item.id}`
          } else if (item.temp_name === '智慧食堂营养屏') {
            item.link = `${process.env.VUE_APP_DASHBOARD}/#/smart-canteen-nutrition?token=${getToken()}&organization_id=${this.organizationId}&board_id=${item.id}`
          } else if (item.temp_name === '部队食堂大数据运行平台') {
            item.link = `${process.env.VUE_APP_DASHBOARD}/#/chart/preview/13?board_id=${item.id}&company_id=${this.companyId}&organization_id=${this.organizationId}`
          }
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getBoardList()
    },
    delBoard(id) {
      this.$confirm(`确定删除该看板吗？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const res = await this.$apis.apiBackgroundDatascreenKanbanTempDeletePost({
              ids: [id]
            })
            this.isLoading = false
            if (res.code === 0) {
              this.$message.success('删除成功！')
              this.getBoardList()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    gotoAddOrEdit(type, id) {
      let query = {}
      if (type === 'edit') {
        query = { id }
      }
      this.$router.push({
        name: 'MerchantAddDataBoard',
        params: {
          type
        },
        query
      })
    }
  }
}
</script>
