<template>
  <div class="food-evaluate container-wrapper">
    <search-form ref="searchRef" label-width="105px" :loading="isLoading" :form-setting="searchSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <!-- table start -->
      <div class="table-content">
        <div class="food-evaluate-item" v-for="item in foodEvaluteData" :key="item.id">
          <div class="food-evaluate-top flex-between m-t-20 m-b-20">
            <div class="m-b-10">
              <span class="evalute-label m-r-40">姓名: {{ item.name }}</span>
              <span class="evalute-label m-r-40">人员编号：{{ item.person_no }}</span>
              <span class="evalute-label m-r-40">手机号：{{ item.phone }}</span>
            </div>
            <div>
              <span>创建时间: {{ item.create_time }}</span>
            </div>
          </div>
          <div class="flex">
            <el-image :src="item.food_img ? item.food_img : defaultFoodImg" class="evalute-img m-r-20" :preview-src-list="[item.food_img]">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <div class="flex rate-box">
              <div class="food-name">{{ item.food_name }}</div>
              <div class="food-taste flex">
                <div v-for="(rate, k) in item.food_evaluation_score" :key="'rate1'+k" class="rate-item m-r-60 m-b-10">
                  <span class="rate-label">{{ rate.field_name }}</span>
                  <el-rate class="rate" v-model="rate.score" disabled></el-rate>
                </div>
              </div>
            </div>
          </div>
          <el-divider></el-divider>
        </div>
        <div class="evaluate-content empty" v-if="foodEvaluteData.length === 0">
          <img class="empty-img" src="@/assets/img/table-no-data.png" alt="empty">
          <p class="empty-text">暂无数据</p>
        </div>
      </div>
       <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="page"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce, to, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { FOOD_EVALUATE_LIST } from './constants'
import report from '@/mixins/report' // 混入
export default {
  name: 'FoodEvaluate',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  components: { },
  data() {
    return {
      isLoading: false,
      searchSetting: deepClone(FOOD_EVALUATE_LIST),
      // 数据列表
      foodEvaluteData: [],
      defaultFoodImg: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png',
      page: 1,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      rate: 4
    }
  },
  created() {
    // this.initLoad(true)
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad(isFirst) {
      this.getFoodEvaluate()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.page = 1
        this.initLoad()
        this.isFirstSearch = false
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.foodEvaluteData = []
      this.page = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取数据列表
    async getFoodEvaluate() {
      let searchParams = this.formatQueryParams(this.searchSetting)
      if (searchParams.is_anonymous) {
        delete searchParams.name
        delete searchParams.person_no
        delete searchParams.phone
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOperationManagementOrderEvaluationFoodEvaluationListPost({
        ...searchParams,
        page: this.page,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        this.foodEvaluteData = []
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodEvaluteData = res.data.results

        this.totalCount = res.data.count
      } else {
        this.foodEvaluteData = []
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.page = val.current
      this.pageSize = val.pageSize
      this.getFoodEvaluate()
    }
  }
}
</script>

<style lang="scss" scoped>
.food-evaluate {
  .flex {
    display: flex;
  }
  .rate-box {
    flex-direction: column;
    justify-content: space-between;
  }
  .rate-item{
    display: inline-block;
    .rate-label{
      vertical-align: middle;
    }
    .rate{
      display: inline-block;
      ::v-deep .el-rate__icon{
        font-size: 26px;
      }
    }
  }
  .evalute-img{
    width: 80px;
    height: 80px;
    .el-icon-picture-outline{
      font-size: 60px;
      opacity: .5;
    }
  }
  .evaluate-content{
    &.empty{
      padding: 40px;
      text-align: center;
      .empty-img{
        display: inline-block;
        width: 127px;
        height: 99px;
        vertical-align: middle;
      }
      .empty-text{
        color: #b2b2b2;
      }
    }
  }
}
</style>
