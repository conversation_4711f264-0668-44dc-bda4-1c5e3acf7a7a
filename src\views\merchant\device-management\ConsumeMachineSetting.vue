<template>
  <div class="AiMachineSetting container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <div class="table-wrapper" style="margin-top: 0px;margin-bottom: 50px;">
      <div class="table-header">
        <div>
          <div class="table-title" style="display: flex;align-items: center; justify-content: space-between;width: 380px;">适用组织：
            <organization-select
              class="search-item-w ps-input w-250"
              placeholder="请选择所属组织"
              :isLazy="false"
              :multiple="false"
              :check-strictly="true"
              v-model="organizationId"
              @change="changeOrganization"
              :append-to-body="true"
              >
            </organization-select>
          </div>
        </div>
        <div style="padding-right:20px;">
          <el-button size="small" type="primary" class="ps-origin-btn" @click="checkForm('self')">保存</el-button>
          <el-button size="small" type="primary" class="ps-plain-btn" @click="openOtherOrg">适用到其他组织</el-button>
        </div>
      </div>
      <div class="setting-wrap">
        <div class="tab-type">
          <div v-for="item in tabTypeList" :key="item.key" :class="['tabItem',tabType===item.key?'activeTab':'']" @click="changeType(item.key)">{{item.name}}</div>
        </div>
        <el-form
          :model="settingForm"
          :rules="settingFormRules"
          ref="settingForm"
        >
          <div v-show="tabType === 'routine'">
            <div class="title">餐段定额消费</div>
            <div class="margin-l-15">
              <div class="meal-fixed-price">
                <div v-for="item in mealList" :key="item.key" class="meal-item">
                  {{item.name}}
                  <el-form-item class="form-content-inline" :prop="'mealFixedPrice.'+item.key" :rules="settingFormRules.mealFixedPrice">
                    <el-input v-model="settingForm.mealFixedPrice[item.key]" class="margin-input w-100 ps-input"></el-input>元
                  </el-form-item>
                </div>
              </div>
              <div class="tips">注：必须开启餐段才可使用,开启餐段不设置金额则无法使用</div>
            </div>
            <div class="title">基础设置</div>
            <div class="margin-l-15">
              <el-form-item label="卡号反转">
                <el-switch v-model="settingForm.cardReversal" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <el-form-item prop="cardBase" class="form-content-inline margin-l-30">
                  <el-radio-group class="ps-radio" v-model="settingForm.cardBase">
                    <el-radio :label="10">十进制</el-radio>
                    <el-radio :label="16">十六进制</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form-item>
              <el-form-item label="订单图片保存">
                <el-switch v-model="settingForm.saveImage" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
              <el-form-item label="是否进行云端识别菜品" v-if="settingForm.saveImage">
                <el-switch v-model="settingForm.isCloud" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
              <el-form-item label="设备重启时间">
                <el-switch v-model="settingForm.isRestart" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <el-form-item v-if="settingForm.isRestart" class="form-content-inline margin-l-15" prop="restartTime">
                  <el-time-picker
                    v-model="settingForm.restartTime"
                    value-format="HH:mm"
                    format="HH:mm"
                    placeholder="选择时间"
                    class="w-180">
                  </el-time-picker>
                </el-form-item>
              </el-form-item>
              <el-form-item label="核销确认倒计时" prop="writeOffTime" label-width="120px">
                <el-input v-model.number="settingForm.writeOffTime" class="margin-input w-180 ps-input"></el-input>秒
              </el-form-item>
              <el-form-item label="核销无订单倒计时" prop="writeOffNoOrderTime" label-width="135px">
                <el-input v-model.number="settingForm.writeOffNoOrderTime" class="margin-input w-180 ps-input"></el-input>秒
              </el-form-item>
              <el-form-item label="账户显示时间" prop="accountTime" label-width="110px">
                <el-input v-model.number="settingForm.accountTime" class="margin-input w-180 ps-input"></el-input>秒
              </el-form-item>
              <el-form-item label="设备收银默认模式" prop="cashierMode" label-width="135px">
                <el-radio-group class="ps-radio" v-model="settingForm.cashierMode">
                  <el-radio label="ai_mode">AI收银</el-radio>
                  <el-radio label="custom_mode">自定义收银</el-radio>
                  <el-radio label="fixed_mode">固定收银</el-radio>
                  <el-radio label="meal_type_mode">餐段定额消费收银</el-radio>
                  <el-radio label="cash_mode">现金收银</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="是否打开副屏操作权限">
                <el-switch v-model="settingForm.secondaryScreenDisplay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
            </div>
            <div class="title">消费机2.0下载菜品来源</div>
            <div class="margin-l-15">
              <el-form-item>
                <el-radio-group class="ps-radio" v-model="settingForm.getFoodType">
                  <el-radio label="menu">菜谱下载</el-radio>
                  <el-radio label="stock">菜品库下载</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </div>
          <div v-show="tabType === 'pay'">
            <div class="title">支付限制</div>
            <div class="margin-l-15">
              <el-form-item label="支付金额限制">
                <el-switch v-model="settingForm.isLimitPayPrice" class="margin-r-20" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <el-form-item v-if="settingForm.isLimitPayPrice" label="支付价格不能超过" prop="payPriceLimit" class="form-content-inline">
                  <el-input v-model.number="settingForm.payPriceLimit" class="margin-input w-180 ps-input"></el-input>元
                </el-form-item>
              </el-form-item>
              <el-form-item label="零元支付">
                <el-switch v-model="settingForm.zeroAmountPay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
              <el-form-item label="固定金额是否支持后台消费规则">
                <el-switch v-model="settingForm.fixedPriceRule" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
              <el-form-item label="餐段定额是否支持后台消费规则">
                <el-switch v-model="settingForm.mealFixedRule" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
            </div>
            <div class="title">支付方式</div>
            <div class="margin-l-15">
              <el-form-item class="form-label">
                <span class="margin-r-5">刷卡支付</span>
                <el-switch v-model="settingForm.openCardPay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <span class="margin-l-30 margin-r-5">扫码支付</span>
                <el-switch v-model="settingForm.openCodePay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <span class="margin-l-30 margin-r-5">人脸支付</span>
                <el-switch v-model="settingForm.openFacePay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
            </div>
            <div class="title">人脸支付确认</div>
            <div class="margin-l-15">
              <el-form-item label="人脸支付确认">
                <el-switch v-model="settingForm.isFacePayConfirm" class="margin-r-20" @change="changeFaceConfirm" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <el-form-item v-if="settingForm.isFacePayConfirm" label="人脸确认弹框停留时间" prop="facePayTime" class="form-content-inline">
                  <el-input v-model.number="settingForm.facePayTime" class="margin-input w-180 ps-input"></el-input>秒
                </el-form-item>
              </el-form-item>
              <el-form-item label="选择人脸支付">
                <el-switch v-model="settingForm.choiceFacePay" class="margin-r-20" @change="changeFaceChoice" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <el-form-item v-if="settingForm.choiceFacePay" label="选择人脸支付时间" prop="choiceFacePayTime" class="form-content-inline">
                  <el-input v-model.number="settingForm.choiceFacePayTime" class="margin-input w-180 ps-input"></el-input>秒
                </el-form-item>
              </el-form-item>
              <el-form-item label="人脸roi">
                <el-switch v-model="settingForm.faceRoi" class="margin-r-20" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <div class="tips inline-box">注：开启后人脸识别必须在固定区域才可识别</div>
              </el-form-item>
            </div>
            <div class="title">支付相关</div>
            <div class="margin-l-15">
              <el-form-item label="支付信息停留时间" prop="payInfoTime" label-width="135px">
                <el-input v-model.number="settingForm.payInfoTime" class="margin-input w-180 ps-input"></el-input>秒
              </el-form-item>
              <el-form-item label="收银人脸常开">
                <el-switch v-model="settingForm.isOpenCashier" class="margin-r-20" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
              <!-- <el-form-item label="在线人脸检测分数" prop="onlineFaceScore" label-width="135px">
                <el-input v-model.number="settingForm.onlineFaceScore" class="margin-input w-180 ps-input"></el-input>分
              </el-form-item> -->
              <el-form-item label="在线人脸识别距离" prop="onlineFacePixel" label-width="135px">
                <el-input v-model.number="settingForm.onlineFacePixel" class="margin-input w-180 ps-input"></el-input>像素
              </el-form-item>
            </div>
          </div>
          <div v-show="tabType === 'offline'">
            <div class="offline-title">
              <div class="title">自动开启离线模式</div>
              <el-switch v-model="settingForm.autoOpenOfflineMode" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </div>
            <div class="margin-l-15" v-if="settingForm.autoOpenOfflineMode">
              <el-form-item prop="autoOpenOfflineModeType">
                <el-radio-group class="ps-radio" v-model="settingForm.autoOpenOfflineModeType" @change="changeOpenOfflineType">
                  <el-radio label="on_date">固定时间开启</el-radio>
                  <el-radio label="on_meal_time">固定餐段开启</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="openOfflineTime" class="margin-l-20" v-if="settingForm.autoOpenOfflineModeType === 'on_date'">
                <!-- <el-time-picker
                  v-model="settingForm.openOfflineTime"
                  value-format="HH:mm"
                  format="HH:mm"
                  placeholder="选择时间">
                </el-time-picker> -->
                <el-time-picker
                  is-range
                  v-model="settingForm.openOfflineTime"
                  value-format="HH:mm"
                  format="HH:mm"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="选择时间范围">
                </el-time-picker>
              </el-form-item>
              <el-form-item prop="openOfflineMeal" class="margin-l-20" v-if="settingForm.autoOpenOfflineModeType === 'on_meal_time'">
                <el-checkbox-group v-model="settingForm.openOfflineMeal">
                  <el-checkbox v-for="item in mealList" :key="item.key" :label="item.key" :disabled="item.disabled" class="ps-checkbox">{{item.name}}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>
            <div class="offline-title">
              <div class="title">自动上传离线订单</div>
              <el-switch v-model="settingForm.autoUploadOfflineOrder" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </div>
            <div class="margin-l-15" v-if="settingForm.autoUploadOfflineOrder">
              <el-form-item prop="uploadOfflineType">
                <el-radio-group class="ps-radio" v-model="settingForm.uploadOfflineType">
                  <el-radio label="auto">联网自动上传</el-radio>
                  <el-radio label="on_date">固定时间上传</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item prop="uploadOfflineTime" class="margin-l-20" v-if="settingForm.uploadOfflineType === 'on_date'">
                <el-time-picker
                  v-model="settingForm.uploadOfflineTime"
                  value-format="HH:mm"
                  format="HH:mm"
                  placeholder="选择时间">
                </el-time-picker>
              </el-form-item>
            </div>
            <div class="title">离线相关</div>
            <div class="margin-l-15">
              <el-form-item label="离线识别人脸在线支付">
                <el-switch v-model="settingForm.isOfflineFaceOnlinePay" class="margin-r-20" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
              <el-form-item label="离线人脸支付">
                <el-switch v-model="settingForm.isOpenOfflineFacePay" class="margin-r-20" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <el-form-item v-if="settingForm.isOpenOfflineFacePay" label="人脸检测分数" prop="offlineFaceScore" class="form-content-inline">
                  <el-input v-model.number="settingForm.offlineFaceScore" class="margin-input w-180 ps-input"></el-input>分
                </el-form-item>
              </el-form-item>
              <el-form-item label="离线活体检测">
                <el-switch v-model="settingForm.isOpenOfflineLive" class="margin-r-20" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <el-form-item v-if="settingForm.isOpenOfflineLive" label="活体检测分数" prop="offlineLiveScore" class="form-content-inline">
                  <el-input v-model.number="settingForm.offlineLiveScore" class="margin-input w-180 ps-input"></el-input>分
                </el-form-item>
              </el-form-item>
              <el-form-item label="离线人脸识别距离" prop="offlineFacePixel" label-width="135px">
                <el-input v-model.number="settingForm.offlineFacePixel" class="margin-input w-180 ps-input"></el-input>像素
              </el-form-item>
            </div>
          </div>
          <div v-show="tabType === 'algorithm'">
            <!-- <div class="title">菜品识别模型</div>
            <div class="margin-l-15">
              <el-form-item prop="foodDistinguishMode">
                <el-radio-group class="ps-radio" v-model="settingForm.foodDistinguishMode">
                  <el-radio label="e">E</el-radio>
                  <el-radio label="m">M</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div class="title">菜品检测模型</div>
            <div class="margin-l-15">
              <el-form-item prop="foodTestMode">
                <el-radio-group class="ps-radio" v-model="settingForm.foodTestMode">
                  <el-radio label="p">P</el-radio>
                  <el-radio label="y">Y</el-radio>
                </el-radio-group>
              </el-form-item>
            </div> -->
            <div class="offline-title">
              <div class="title">动静检测</div>
              <el-switch v-model="settingForm.movementTest" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </div>
            <div class="title">算法相关</div>
            <div class="margin-l-15">
              <el-form-item label="菜品检测分数" prop="foodTestScore" label-width="110px">
                <el-input v-model.number="settingForm.foodTestScore" class="margin-input w-180 ps-input"></el-input>%
              </el-form-item>
              <el-form-item label="菜品动静检测距离" prop="foodMovementPixel" label-width="135px">
                <el-input v-model.number="settingForm.foodMovementPixel" class="margin-input w-180 ps-input"></el-input>px
              </el-form-item>
              <el-form-item label="动静检测稳定帧数" prop="movementTestFrame" label-width="135px">
                <el-input v-model.number="settingForm.movementTestFrame" class="margin-input w-180 ps-input"></el-input>帧
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <setting-dialog
      :isshow.sync="dialogVisible"
      :type="dialogType"
      :title="dialogTitle"
      :confirm="dialogConfirm"
      @otherOrgConfirm="otherOrgConfirm"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import SettingDialog from './components/SettingDialog.vue'
import OrganizationSelect from '@/components/OrganizationSelect'
import { divide, times } from '@/utils'

export default {
  name: 'AiMachineSetting',
  components: {
    OrganizationSelect,
    SettingDialog
  },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    let validMoney = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('金额不能为空'))
      } else {
        let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      }
    }
    let validFaceScore = (rule, value, callback) => {
      if (value > 100) {
        callback(new Error('人脸检测分数不能高于100'))
      } else if (value < 75) {
        callback(new Error('人脸检测分数不能低于75'))
      } else {
        callback()
      }
    }
    let validFacePixel = (rule, value, callback) => {
      if (value >= 300) {
        callback(new Error('人脸识别距离不能高于等于300像素'))
      } else if (value < 100) {
        callback(new Error('人脸识别距离不能低于100像素'))
      } else {
        callback()
      }
    }
    let validLiveScore = (rule, value, callback) => {
      if (value > 100) {
        callback(new Error('活体检测分数不能高于100'))
      } else if (value < 50) {
        callback(new Error('活体检测分数不能低于50'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      organizationId: '',
      organizationIds: [],
      tabType: 'routine',
      tabTypeList: [
        {
          name: '常规设置',
          key: 'routine'
        },
        {
          name: '支付设置',
          key: 'pay'
        },
        {
          name: '离线设置',
          key: 'offline'
        },
        {
          name: '算法设置',
          key: 'algorithm'
        }
      ],
      settingForm: {
        mealFixedPrice: { // 餐段定额消费
          breakfast: '',
          lunch: '',
          afternoon: '',
          dinner: '',
          supper: '',
          morning: ''
        },
        cardReversal: false, // 卡号反转
        cardBase: '', // 进制
        saveImage: false, // 订单图片保存
        isCloud: false, // 是否开启云端识别
        isRestart: false, // 设备是否重启
        restartTime: '', // 设备重启时间
        writeOffTime: '', // 核销确认倒计时
        writeOffNoOrderTime: '', // 核销无订单倒计时
        accountTime: '', // 账户显示时间
        cashierMode: '', // 设备收银默认模式
        secondaryScreenDisplay: false, // 是否打开副屏操作权限
        getFoodType: 'menu', // 消费机2.0下载菜品来源
        isLimitPayPrice: false, // 支付是否限制金额
        payPriceLimit: '', // 支付金额限制
        zeroAmountPay: false, // 零元支付
        fixedPriceRule: false, // 固定金额是否支持后台消费规则
        mealFixedRule: false, // 餐段定额是否支持后台消费规则
        openCardPay: false, // 刷卡支付
        openCodePay: false, // 扫码支付
        openFacePay: false, // 人脸支付
        isFacePayConfirm: false, // 是否开启人脸支付确认
        facePayTime: '', // 人脸确认弹框停留时间
        choiceFacePay: false, // 选择人脸支付
        choiceFacePayTime: '', // 选择人脸支付时间
        faceRoi: false, // 人脸roi
        payInfoTime: '', // 支付信息停留时间
        isOpenCashier: false, // 收银人脸常开
        onlineFaceScore: '', // 在线人脸检测分数
        onlineFacePixel: '', // 在线人脸识别距离
        autoOpenOfflineMode: false, // 是否自动开启离线模式
        autoOpenOfflineModeType: '', // 自动开启离线模式类型
        openOfflineTime: [], // 固定时间开启
        openOfflineMeal: [], // 固定餐段开启
        autoUploadOfflineOrder: false, // 是否自动上传离线订单
        uploadOfflineType: '', // 自动上传离线订单类型
        uploadOfflineTime: '', // 固定时间上传
        isOfflineFaceOnlinePay: false, // 离线识别人脸在线支付
        isOpenOfflineFacePay: false, // 是否开启离线人脸支付
        offlineFaceScore: '', // 人脸检测分数
        isOpenOfflineLive: false, // 是否开启离线活体检测
        offlineLiveScore: '', // 活体检测分数
        offlineFacePixel: '', // 离线人脸识别距离
        // foodDistinguishMode: '', // 菜品识别模型
        // foodTestMode: '', // 菜品检测模型
        movementTest: false, // 动静检测
        foodTestScore: '', // 菜品检测分数
        foodMovementPixel: '', // 菜品动静检测距离
        movementTestFrame: '' // 动静检测稳定帧数
      },
      settingFormRules: {
        mealFixedPrice: [{ required: true, validator: validMoney, trigger: 'blur' }],
        cardBase: [{ required: true, message: '请选择', trigger: 'change' }],
        restartTime: [{ required: true, message: '请选择时间', trigger: 'change' }],
        writeOffTime: [
          { required: true, message: '请输入秒数' },
          { type: 'number', message: '请输入整数' }
        ],
        writeOffNoOrderTime: [
          { required: true, message: '请输入秒数' },
          { type: 'number', message: '请输入整数' }
        ],
        accountTime: [
          { required: true, message: '请输入秒数' },
          { type: 'number', message: '请输入整数' }
        ],
        cashierMode: [{ required: true, message: '请选择设备收银默认模式', trigger: 'change' }],
        payPriceLimit: [{ required: true, validator: validMoney, trigger: 'blur' }],
        facePayTime: [
          { required: true, message: '请输入秒数' },
          { type: 'number', message: '请输入整数' }
        ],
        choiceFacePayTime: [
          { required: true, message: '请输入秒数' },
          { type: 'number', message: '请输入整数' }
        ],
        payInfoTime: [
          { required: true, message: '请输入秒数' },
          { type: 'number', message: '请输入整数' }
        ],
        onlineFaceScore: [
          { required: true, message: '请输入在线人脸检测分数' },
          { type: 'number', message: '请输入整数' },
          { validator: validFaceScore }
        ],
        onlineFacePixel: [
          { required: true, message: '请输入在线人脸识别距离' },
          { type: 'number', message: '请输入整数' },
          { validator: validFacePixel }
        ],
        autoOpenOfflineModeType: [{ required: true, message: '请选择', trigger: 'change' }],
        openOfflineTime: [{ required: true, message: '请选择时间', trigger: 'change' }],
        openOfflineMeal: [{ type: 'array', required: true, message: '请至少选择一个餐段', trigger: 'change' }],
        uploadOfflineType: [{ required: true, message: '请选择', trigger: 'change' }],
        uploadOfflineTime: [{ required: true, message: '请选择时间', trigger: 'change' }],
        offlineFaceScore: [
          { required: true, message: '请输入离线人脸检测分数' },
          { type: 'number', message: '请输入整数' },
          { validator: validFaceScore }
        ],
        offlineLiveScore: [
          { required: true, message: '请输入离线活体检测分数' },
          { type: 'number', message: '请输入整数' },
          { validator: validLiveScore }
        ],
        offlineFacePixel: [
          { required: true, message: '请输入离线人脸识别距离' },
          { type: 'number', message: '请输入整数' },
          { validator: validFacePixel }
        ],
        // foodDistinguishMode: [{ required: true, message: '请选择菜品识别模型', trigger: 'change' }],
        // foodTestMode: [{ required: true, message: '请选择菜品检测模型', trigger: 'change' }],
        foodTestScore: [
          { required: true, message: '请输入菜品检测分数' },
          { type: 'number', message: '请输入整数' }
        ],
        foodMovementPixel: [
          { required: true, message: '请输入菜品动静检测距离' },
          { type: 'number', message: '请输入整数' }
        ],
        movementTestFrame: [
          { required: true, message: '请输入动静检测稳定帧数' },
          { type: 'number', message: '请输入整数' }
        ]
      },
      mealList: [{
        key: 'breakfast',
        name: '早餐'
      }, {
        key: 'lunch',
        name: '午餐'
      }, {
        key: 'afternoon',
        name: '下午茶'
      }, {
        key: 'dinner',
        name: '晚餐'
      }, {
        key: 'supper',
        name: '宵夜'
      }, {
        key: 'morning',
        name: '凌晨餐'
      }],
      dialogVisible: false,
      dialogTitle: '',
      dialogType: ''
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.organizationId = Number(sessionStorage.getItem('organization'))
      this.getSettingDetail()
    },
    // 刷新页面
    refreshHandle() {
    },
    formateParams() {
      let params = {
        // 餐段定额消费
        breakfast: times(this.settingForm.mealFixedPrice.breakfast),
        lunch: times(this.settingForm.mealFixedPrice.lunch),
        afternoon: times(this.settingForm.mealFixedPrice.afternoon),
        dinner: times(this.settingForm.mealFixedPrice.dinner),
        supper: times(this.settingForm.mealFixedPrice.supper),
        morning: times(this.settingForm.mealFixedPrice.morning),
        // 基础设置
        card_no_reversal: this.settingForm.cardReversal,
        card_no_base: this.settingForm.cardBase,
        order_image_save: this.settingForm.saveImage,
        is_cloud: this.settingForm.saveImage ? this.settingForm.isCloud : false,
        device_restart: this.settingForm.isRestart,
        device_restart_time: this.formateTime(this.settingForm.restartTime),
        cancellation_after_verification_confirm_time: this.settingForm.writeOffTime,
        cancellation_after_verification_no_order_time: this.settingForm.writeOffNoOrderTime,
        account_display_time: this.settingForm.accountTime,
        cashier_mode: this.settingForm.cashierMode,
        secondary_screen_display: this.settingForm.secondaryScreenDisplay,
        // 消费机2.0下载菜品来源
        get_food_type: this.settingForm.getFoodType,
        // 支付限制
        payment_limit: this.settingForm.isLimitPayPrice,
        payment_limit_amount: times(this.settingForm.payPriceLimit),
        zero_amount_pay_limit: this.settingForm.zeroAmountPay,
        fixed_amount_consume_rule: this.settingForm.fixedPriceRule,
        meal_type_amount_consume_rule: this.settingForm.mealFixedRule,
        // 支付方式
        can_micropay: this.settingForm.openCardPay,
        can_scanpay: this.settingForm.openCodePay,
        can_facepay: this.settingForm.openFacePay,
        // 人脸支付确认
        face_pay_confirm: this.settingForm.isFacePayConfirm,
        // face_pay_confirm_wait_time: this.settingForm.facePayTime,
        choice_face_pay: this.settingForm.choiceFacePay,
        face_roi: this.settingForm.faceRoi,
        // 支付相关
        payinfo_wait_time: this.settingForm.payInfoTime,
        face_pay_resident: this.settingForm.isOpenCashier,
        // online_face_recognize_score: this.settingForm.onlineFaceScore,
        online_detection_range: this.settingForm.onlineFacePixel,
        // 离线相关
        offline_face_online_pay: this.settingForm.isOfflineFaceOnlinePay,
        offline_face_pay_limit: this.settingForm.isOpenOfflineFacePay,
        // offline_face_recognize_score: this.settingForm.offlineFaceScore,
        live_detection: this.settingForm.isOpenOfflineLive,
        // live_face_recognize_score: this.settingForm.offlineLiveScore,
        offline_detection_range: this.settingForm.offlineFacePixel,
        // 菜品识别模型
        // food_identify: this.settingForm.foodDistinguishMode,
        // 菜品检测模型
        // food_inspection: this.settingForm.foodTestMode,
        // 动静检测
        dynamic_and_static_limit: this.settingForm.movementTest,
        // 算法相关
        food_inspection_score: this.settingForm.foodTestScore,
        food_dynamic_and_static_range: this.settingForm.foodMovementPixel,
        dynamic_and_static_frames: this.settingForm.movementTestFrame
      }
      // 人脸支付确认时间
      if (this.settingForm.isFacePayConfirm) {
        params.face_pay_confirm_wait_time = this.settingForm.facePayTime
      }
      // 选择人脸支付时间
      if (this.settingForm.choiceFacePay) {
        params.choice_face_pay_wait_time = this.settingForm.choiceFacePayTime
      }
      // 这个离线人脸支付检测分数
      if (this.settingForm.isOpenOfflineFacePay) {
        params.offline_face_recognize_score = this.settingForm.offlineFaceScore
      }
      // 离线活体检测分数
      if (this.settingForm.isOpenOfflineLive) {
        params.live_face_recognize_score = this.settingForm.offlineLiveScore
      }
      // 自动开启离线模式
      if (this.settingForm.autoOpenOfflineMode) {
        params.auto_switch_offline_model = this.settingForm.autoOpenOfflineModeType
        params.auto_switch_offline_model_conf = {}
        params.auto_switch_online_model_conf = {}
      } else {
        params.auto_switch_offline_model = 'off'
      }
      if (this.settingForm.autoOpenOfflineModeType === 'on_date') {
        params.auto_switch_offline_model_conf = {
          timestamp: this.formateTime(this.settingForm.openOfflineTime[0])
        }
        params.auto_switch_online_model_conf = {
          timestamp: this.formateTime(this.settingForm.openOfflineTime[1])
        }
      }
      if (this.settingForm.autoOpenOfflineModeType === 'on_meal_time') {
        params.auto_switch_offline_model_conf = {
          meal: this.settingForm.openOfflineMeal
        }
      }
      // 自动上传离线订单
      if (this.settingForm.autoUploadOfflineOrder) {
        params.auto_upload_offline_order = this.settingForm.uploadOfflineType
        params.auto_upload_offline_order_conf = {}
      } else {
        params.auto_upload_offline_order = 'off'
      }
      // if (this.settingForm.uploadOfflineType === 'on_date') {
      params.auto_upload_offline_order_conf = {
        on_date: this.formateTime(this.settingForm.uploadOfflineTime)
      }
      // }
      return params
    },
    checkForm(type) {
      this.$refs.settingForm.validate(valid => {
        if (valid) {
          let params = this.formateParams()
          if (type === 'self') {
            if (!this.organizationId) {
              this.$message.error('请选择适用组织')
              return
            }
            params.mode = 'self'
            params.org_no = this.organizationId
          } else {
            params.mode = 'other'
            params.org_nos = this.organizationIds
          }
          this.saveAiMachineSetting(params)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    async saveAiMachineSetting(params) {
      this.$confirm(`当前消费点可能正在就餐，是否保存？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundDeviceConsumeInfoAddPost(params)
            if (res.code === 0) {
              this.$message.success('保存成功')
              this.getSettingDetail()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    async getSettingDetail() {
      const res = await this.$apis.apiBackgroundDeviceConsumeInfoDetailsPost({
        org_no: this.organizationId
      })
      if (res.code === 0) {
        if (res.data) {
          let data = res.data.setting
          // 餐段定额消费
          this.settingForm.mealFixedPrice.breakfast = divide(data.breakfast)
          this.settingForm.mealFixedPrice.lunch = divide(data.lunch)
          this.settingForm.mealFixedPrice.afternoon = divide(data.afternoon)
          this.settingForm.mealFixedPrice.dinner = divide(data.dinner)
          this.settingForm.mealFixedPrice.supper = divide(data.supper)
          this.settingForm.mealFixedPrice.morning = divide(data.morning)
          // 基础设置
          this.settingForm.cardReversal = data.card_no_reversal
          this.settingForm.cardBase = data.card_no_base
          this.settingForm.saveImage = data.order_image_save
          this.settingForm.isCloud = data.is_cloud
          this.settingForm.isRestart = data.device_restart
          this.settingForm.restartTime = this.formateTimestamp(data.device_restart_time)
          this.settingForm.writeOffTime = data.cancellation_after_verification_confirm_time
          this.settingForm.writeOffNoOrderTime = data.cancellation_after_verification_no_order_time
          this.settingForm.accountTime = data.account_display_time
          this.settingForm.cashierMode = data.cashier_mode
          this.settingForm.secondaryScreenDisplay = data.secondary_screen_display
          // 消费机2.0下载菜品来源
          this.settingForm.getFoodType = data.get_food_type
          // 支付限制
          this.settingForm.isLimitPayPrice = data.payment_limit
          this.settingForm.payPriceLimit = divide(data.payment_limit_amount)
          this.settingForm.zeroAmountPay = data.zero_amount_pay_limit
          this.settingForm.fixedPriceRule = data.fixed_amount_consume_rule
          this.settingForm.mealFixedRule = data.meal_type_amount_consume_rule
          // 支付方式
          this.settingForm.openCardPay = data.can_micropay
          this.settingForm.openCodePay = data.can_scanpay
          this.settingForm.openFacePay = data.can_facepay
          // 人脸支付确认
          this.settingForm.isFacePayConfirm = data.face_pay_confirm
          this.settingForm.facePayTime = data.face_pay_confirm_wait_time
          this.settingForm.choiceFacePay = data.choice_face_pay
          this.settingForm.choiceFacePayTime = data.choice_face_pay_wait_time
          this.settingForm.faceRoi = data.face_roi
          // 支付相关
          this.settingForm.payInfoTime = data.payinfo_wait_time
          this.settingForm.isOpenCashier = data.face_pay_resident
          // this.settingForm.onlineFaceScore = data.online_face_recognize_score
          this.settingForm.onlineFacePixel = data.online_detection_range
          // 自动开启离线模式
          this.settingForm.autoOpenOfflineMode = !(data.auto_switch_offline_model === 'off')
          this.settingForm.autoOpenOfflineModeType = data.auto_switch_offline_model
          if (data.auto_switch_offline_model === 'on_date' &&
            data.auto_switch_offline_model_conf.timestamp &&
            data.auto_switch_online_model_conf.timestamp) {
            this.settingForm.openOfflineTime[0] = this.formateTimestamp(data.auto_switch_offline_model_conf.timestamp)
            this.settingForm.openOfflineTime[1] = this.formateTimestamp(data.auto_switch_online_model_conf.timestamp)
          } else {
            this.settingForm.openOfflineTime = null
          }
          if (data.auto_switch_offline_model === 'on_meal_time' && data.auto_switch_offline_model_conf.meal) {
            this.settingForm.openOfflineMeal = data.auto_switch_offline_model_conf.meal
          }
          // 自动上传离线订单
          this.settingForm.autoUploadOfflineOrder = !(data.auto_upload_offline_order === 'off')
          this.settingForm.uploadOfflineType = data.auto_upload_offline_order
          if (data.auto_upload_offline_order_conf.on_date) {
            this.settingForm.uploadOfflineTime = this.formateTimestamp(data.auto_upload_offline_order_conf.on_date)
          }
          // 离线相关
          this.settingForm.isOfflineFaceOnlinePay = data.offline_face_online_pay
          this.settingForm.isOpenOfflineFacePay = data.offline_face_pay_limit
          this.settingForm.offlineFaceScore = data.offline_face_recognize_score
          this.settingForm.isOpenOfflineLive = data.live_detection
          this.settingForm.offlineLiveScore = data.live_face_recognize_score
          this.settingForm.offlineFacePixel = data.offline_detection_range
          // 菜品识别模型
          // this.settingForm.foodDistinguishMode = data.food_identify
          // 菜品检测模型
          // this.settingForm.foodTestMode = data.food_inspection
          // 动静检测
          this.settingForm.movementTest = data.dynamic_and_static_limit
          // 算法相关
          this.settingForm.foodTestScore = data.food_inspection_score
          this.settingForm.foodMovementPixel = data.food_dynamic_and_static_range
          this.settingForm.movementTestFrame = data.dynamic_and_static_frames
        } else {
          this.resetForm()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    formateTime(timeData) {
      let time = timeData.split(':')
      let timestamp = time[0] * 60 * 60 + time[1] * 60
      return timestamp
    },
    formateTimestamp(timestamp) {
      return parseInt(timestamp / 3600) + ':' + timestamp % 3600 / 60
    },
    openOtherOrg() {
      this.dialogVisible = true
      this.dialogTitle = '请选择适用的组织'
      this.dialogType = 'other'
    },
    dialogConfirm() {
      this.dialogVisible = false
    },
    otherOrgConfirm(val) {
      this.dialogVisible = false
      this.organizationIds = val
      this.checkForm('other')
    },
    resetForm() {
      this.settingForm = {
        mealFixedPrice: { // 餐段定额消费
          breakfast: '',
          lunch: '',
          afternoon: '',
          dinner: '',
          supper: '',
          morning: ''
        },
        cardReversal: false, // 卡号反转
        cardBase: '', // 进制
        saveImage: false, // 订单图片保存
        isCloud: false, // 是否开启云端识别
        isRestart: false, // 设备是否重启
        restartTime: '', // 设备重启时间
        writeOffTime: '', // 核销确认倒计时
        writeOffNoOrderTime: '', // 核销无订单倒计时
        accountTime: '', // 账户显示时间
        cashierMode: '', // 设备收银默认模式
        secondaryScreenDisplay: false, // 是否打开副屏操作权限
        getFoodType: 'menu', // 消费机2.0下载菜品来源
        isLimitPayPrice: false, // 支付是否限制金额
        payPriceLimit: '', // 支付金额限制
        zeroAmountPay: false, // 零元支付
        fixedPriceRule: false, // 固定金额是否支持后台消费规则
        mealFixedRule: false, // 餐段定额是否支持后台消费规则
        openCardPay: false, // 刷卡支付
        openCodePay: false, // 扫码支付
        openFacePay: false, // 人脸支付
        isFacePayConfirm: false, // 是否开启人脸支付确认
        facePayTime: '', // 人脸确认弹框停留时间
        choiceFacePay: false, // 选择人脸支付
        faceRoi: false, // 人脸roi
        payInfoTime: '', // 支付信息停留时间
        isOpenCashier: false, // 收银人脸常开
        onlineFaceScore: '', // 在线人脸检测分数
        onlineFacePixel: '', // 在线人脸识别距离
        autoOpenOfflineMode: false, // 是否自动开启离线模式
        autoOpenOfflineModeType: '', // 自动开启离线模式类型
        openOfflineTime: [], // 固定时间开启
        openOfflineMeal: [], // 固定餐段开启
        autoUploadOfflineOrder: false, // 是否自动上传离线订单
        uploadOfflineType: '', // 自动上传离线订单类型
        uploadOfflineTime: '', // 固定时间上传
        isOfflineFaceOnlinePay: false, // 离线识别人脸在线支付
        isOpenOfflineFacePay: false, // 是否开启离线人脸支付
        offlineFaceScore: '', // 人脸检测分数
        isOpenOfflineLive: false, // 是否开启离线活体检测
        offlineLiveScore: '', // 活体检测分数
        offlineFacePixel: '', // 离线人脸识别距离
        // foodDistinguishMode: '', // 菜品识别模型
        // foodTestMode: '', // 菜品检测模型
        movementTest: false, // 动静检测
        foodTestScore: '', // 菜品检测分数
        foodMovementPixel: '', // 菜品动静检测距离
        movementTestFrame: '' // 动静检测稳定帧数
      }
    },
    changeType(key) {
      this.tabType = key
    },
    changeOrganization() {
      this.resetForm()
      if (this.organizationId) {
        this.getSettingDetail()
      }
    },
    changeOpenOfflineType() {
      this.$nextTick(() => {
        this.$refs.settingForm.clearValidate(['openOfflineTime', 'openOfflineMeal']);
      })
    },
    // 人脸确认和选择人脸开关只能二选一
    changeFaceConfirm() {
      if (this.settingForm.isFacePayConfirm) {
        this.settingForm.choiceFacePay = false
      }
    },
    changeFaceChoice() {
      if (this.settingForm.choiceFacePay) {
        this.settingForm.isFacePayConfirm = false
      }
    }
  }
}
</script>

<style lang="scss">
.AiMachineSetting{
  padding-top: 30px;
  .setting-wrap{
    margin: 0 20px;
    .tab-type{
      display: flex;
      margin: 20px 0;
      .tabItem{
        border: 1px #DAE1EB solid;
        border-radius: 15px;
        height: 30px;
        line-height: 30px;
        width: 90px;
        text-align: center;
        font-size: 14px;
        color: #7B7C80;
        margin-right: 20px;
        cursor: pointer;
      }
      .activeTab{
        color: #fff;
        background-color: #ff9b45;
        border-color: #ff9b45;
      }
    }
    .meal-fixed-price{
      display: flex;
      flex-wrap: wrap;
      width: 900px;
      .meal-item{
        width: 250px;
      }
    }
    .title{
      font-size: 16px;
      font-weight: bold;
      border-left: 5px #ff9b45 solid;
      padding: 0 10px;
      margin: 15px 0;
    }
    .offline-title{
      display: flex;
      align-items: center;
    }
    .form-text-size{
      font-size: 14px;
    }
    .form-label{
      font-size: 14px;
      color: #606266;
      font-weight: bold;
      margin-right: 8px;
    }
    .tips{
      color: #B12B38;
      font-size: 14px;
    }
    .margin-input{
      margin: 0 5px;
    }
    .margin-r-5{
      margin-right: 5px;
    }
    .margin-r-20{
      margin-right: 20px;
    }
    .margin-r-30{
      margin-right: 30px;
    }
    .margin-l-15{
      margin-left: 15px;
    }
    .margin-l-20{
      margin-left: 20px;
    }
    .margin-l-30{
      margin-left: 30px;
    }
    .inline-box{
      display: inline-block;
    }
    .form-content-inline{
      display: inline-block;
      .el-form-item__content{
        display: inline-block;
      }
    }
    .el-input-number__decrease:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled), .el-input-number__increase:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled){
      border-color: #ff9b45;
    }
    .el-input.is-active .el-input__inner, .el-input__inner:focus{
      border-color: #ff9b45;
    }
    .el-input-number__decrease:hover, .el-input-number__increase:hover{
      color: #ff9b45;
    }
    .detection-range-slider {
      .el-slider__bar{
        background-color: #ff9b45;
      }
      .el-slider__button{
        border: 2px solid #ff9b45;
      }
    }
  }
}
</style>
