<template>
  <div class="inventory-flow-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" :show-refresh="false" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r"></div>
      </div>
      <div class="table-content">
        <!-- content start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #imageJson="{ row }">
              <el-button :disabled="!row.image_json" type="text" size="small" class="ps-text" @click="handleClick(row)">
                查看
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- content end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import { INVENTORY_TYPE } from '../constants'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'InventoryFlow',
  mixins: [exportExcel],
  components: {
    ElImageViewer
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      tableSettings: [
        { label: '单据编号', key: 'trade_no' },
        { label: '关联业务号', key: 'inventory_no' },
        { label: '库存类型', key: 'record_type_alias' },
        { label: '库存', key: 'variation_count' },
        { label: '单位', key: 'unit_management' }, // , isComponents: true, type: 'date', format: 'YYYY-MM-DD'
        { label: '当前库存', key: 'current_materials_count' },
        { label: '附件信息', key: 'image_json', type: 'slot', slotName: 'imageJson' },
        { label: '备注', key: 'remark', showTooltip: true },
        { label: '经手人', key: 'account_name' },
        { label: '操作日期', key: 'update_time', type: 'date' },
        { label: '创建日期', key: 'create_time', type: 'date' }
      ],
      tableData: [], // table数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: {
        record_type: {
          type: 'select',
          value: '',
          label: '库存类型',
          placeholder: '请选择库存类型',
          dataList: INVENTORY_TYPE
        },
        account_name: {
          type: 'input',
          value: '',
          label: '经手人',
          placeholder: '请输入'
        },
        select_time: {
          type: 'daterange',
          label: '操作日期',
          format: 'yyyy-MM-dd',
          clearable: false,
          value: getSevenDateRange(7)
        }
      },
      showImagePreview: false,
      previewList: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getInventoryFlow()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getInventoryFlow() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: +this.$route.query.warehouse_id,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      params.materials_id = Number(this.$route.query.materials_id)
      params.inventory_info_id = Number(this.$route.query.inventory_info_id)
      params.supplier_manage_id = Number(this.$route.query.supplier_manage_id)
      const [err, res] = await to(
        this.$apis.apiBackgroundDrpInventoryInfoInventoryRecordListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getInventoryFlow()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    handleClick(row) {
      this.previewList = row.image_json
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    }
  }
}
</script>

<style lang="scss" scoped>
.inventory-flow-wrapper {
  .ps-pagination {
    padding-top: 0px !important;
  }
}
</style>
