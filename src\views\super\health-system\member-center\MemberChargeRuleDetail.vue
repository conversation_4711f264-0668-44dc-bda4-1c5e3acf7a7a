<template>
  <div class="SuperMemberList container-wrapper">
    <!--刷新页面-->
    <refresh-tool @refreshPage="refreshHandle" />
    <!--搜索层-->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler"
      @reset="resetHandler"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openDialog('add')">新建</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" height="460" stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #status="{ row }">
              <el-switch v-model="row.is_enable" @change="switchFacePay($event, row, row.index)" active-color="#ff9b45" v-loading="switchLoading"
                inactive-color="#ffcda2"></el-switch>
            </template>
            <template #label="{ row }">
              {{ getMemberLabelTxt(row) }}
            </template>
            <template #trigger="{ row }">
              {{ getMemberTriggerTxt(row) }}
            </template>
            <template #buyCount="{ row }">
              {{ row.buy_count == '-1' ? '无限制' : row.buy_count }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text-blue" @click="openDialog('edit', row)">编辑</el-button>
              <!-- <el-button type="text" size="small" class="ps-text-blue" @click="handlerDeleteRecord(row)">删除</el-button> -->
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination @size-change="handlerSizeChange" @current-change="handlerPageChange" :current-page.sync="currentPage"
          :page-size.sync="pageSize" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount"></pagination>
      </div>
    </div>
    <!-- 分页 end -->
    <!-- 弹窗 -->
    <member-charge-detail-dialog :isshow.sync="dialogVisible" :title="dialogTitle" :type="dialogType" :member-cycle="type"
      :base-price="basePrice" :select-info="selectInfo" :confirm="searchHandler" />
  </div>
</template>

<script>
import { SEARCH_FORM_CHARGE_DETAIL_DATA, TABLE_HEAD_CHARGE_DETAIL_DATA } from './constants.js'
import { debounce, deepClone, to } from '@/utils'
import MemberChargeDetailDialog from './components/MemberChargeDetailDialog.vue'

export default {
  name: 'SuperMemberChargeRuleDetail',
  components: { MemberChargeDetailDialog },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: deepClone(SEARCH_FORM_CHARGE_DETAIL_DATA),
      tableSettings: deepClone(TABLE_HEAD_CHARGE_DETAIL_DATA), // 表格配置
      dialogVisible: false,
      switchLoading: false,
      dialogTitle: '',
      dialogType: '',
      selectInfo: {},
      type: '',
      basePrice: ''
    }
  },
  created() {
    this.initLoad()
  },
  mounted() { },
  methods: {
    initLoad() {
      console.log("initLoad", this.$route.query);
      if (this.$route.query) {
        this.type = this.$route.query.type
        this.basePrice = this.$route.query.basePrice
        if (this.type === 'permanent' || this.type === 'week') {
          this.tableSettings = this.tableSettings.filter(item => {
            return item.key !== 'buy_count'
          })
        }
      }
      console.log("this.type", this.type, this.basePrice);
      this.getMemberCharge()
      this.getMemberLabel()
      this.getMemberCycle()
    },
    // 节下流咯
    searchHandler: debounce(function () {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getMemberCharge()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      if (this.$refs.searchRef) {
        this.$refs.searchRef.resetForm()
      }
      this.initLoad()
    },
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.currentPage = 1;
      this.getMemberCharge()
    },
    async getMemberCharge() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize,
        is_base: false,
        member_cycle: [this.type]
      }
      const res = await this.$apis.apiBackgroundMemberMemberChargeRuleListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results.map(item => {
          item.show_all_remark = false
          return item
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'member_permissions' || key === 'trigger_type') {
            params[key] = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    handlerPageChange(val) {
      console.log("handlerPageChange", val);
      this.currentPage = val
      this.getMemberCharge()
    },
    /**
     * 页面条数改变监听
     */
    handlerSizeChange(val) {
      console.log("handlerSizeChange", val);
      this.pageSize = val
      this.getMemberCharge()
    },
    async delMemberCharge(id) {
      this.$confirm(`确定删除该会员收费规则？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundMemberMemberChargeRuleDeletePost({
              ids: [id]
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getMemberCharge()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => { })
    },
    // 状态更新
    async switchFacePay(val, data, index) {
      console.log("switchFacePay", val, data, index);
      var currentIndex = index - 1 >= 0 ? index - 1 : 0
      this.switchLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMemberMemberChargeRuleModifyPost({
        id: data.id,
        name: data.name,
        member_labels: data.member_labels,
        member_cycle: data.member_cycle,
        is_enable: data.is_enable,
        trigger_type: data.trigger_type,
        discount: data.discount,
        origin_fee: data.origin_fee,
        buy_count: data.buy_count,
        remark: data.remark
      }))
      this.switchLoading = false
      if (err) {
        this.$set(this.tableData[currentIndex], 'is_enable', !data.is_enable)
        return this.$message.error('状态更新失败')
      }
      if (res && res.code === 0) {
        this.$message.success('成功')
      } else {
        this.$set(this.tableData[currentIndex], 'is_enable', !data.is_enable)
        this.$message.error(res.msg)
      }
    },
    openDialog(type, data) {
      console.log("openDialog", type, data);
      this.dialogType = type
      if (data && typeof data === 'object') {
        this.selectInfo = deepClone(data)
      }
      if (type === 'add') {
        this.dialogTitle = '新建'
      } else if (type === 'edit') {
        this.dialogTitle = '编辑'
        if (data.is_enable) {
          return this.$message.error("亲，启用中的规则无法编辑")
        }
      }
      this.dialogVisible = true
    },
    // 获取会员标签
    async getMemberLabel() {
      const res = await this.$apis.apiBackgroundMemberMemberLabelListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.searchFormSetting.member_labels.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取会员周期
    async getMemberCycle() {
      const res = await this.$apis.apiBackgroundMemberMemberChargeRuleGetMemberCyclePost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        let results = []
        for (let key in res.data) {
          results.push({
            value: key,
            label: res.data[key]
          })
        }
        // this.searchFormSetting.member_cycle.dataList = results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取会员标签
    getMemberLabelTxt(row) {
      var list = typeof row === 'object' && row.member_labels_list ? row.member_labels_list : []
      var label = ''
      if (list && list.length > 0) {
        list.forEach(item => {
          label = label + item.name + " "
        })
      }
      return label
    },
    // 获取会员触发类型
    getMemberTriggerTxt(row) {
      if (!row || typeof row !== 'object') {
        return ""
      }
      var msg = ''
      switch (row.trigger_type) {
        case "all":
          msg = '全部标签'
          break;
        case "any":
          msg = '任意标签'
          break;
        default:
          break;
      }
      return msg
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";
</style>
