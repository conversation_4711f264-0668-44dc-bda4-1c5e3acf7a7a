
import * as dayjs from 'dayjs'
import { MEAL_TYPES } from '@/utils/constants'

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]
export const recentThreeDay = [
  dayjs()
    .subtract(3, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const TYPE_WARNING = [
  {
    label: "全部",
    value: ""
  },
  {
    label: "利润率盈余",
    value: "surplus"
  },
  {
    label: "利润率亏损",
    value: "loss"
  },
  {
    label: "原材料占比",
    value: "raw_material_percentage"
  }
]
// 身份类型
export const IDENTITY_TYPE_LIST = [
  { value: "principal", label: "校长" },
  { value: "vice_principal", label: "副校长" },
  { value: "director", label: "主任" },
  { value: "head_teacher", label: "班主任" },
  { value: "teacher", label: "老师" },
  { value: "parent", label: "家长代表" }
]
// 备餐间(干净类型)
export const CLEAN_TYPE_LIST = [
  { value: "very_clean", label: "非常干净" },
  { value: "generally_clean", label: "一般干净" },
  { value: "not_clean", label: "不干净" },
  { value: "very_unclean", label: "非常不干净" }
]

// 备餐间(服务态度)
export const ATTITUDE_TYPE_LIST = [
  { value: "very_good_attitude", label: "态度很好" },
  { value: "average_attitude", label: "态度一般" },
  { value: "bad_attitude", label: "态度不好" },
  { value: "very_bad_attitude", label: "态度非常差" }
]
// 饮食浪费情况
export const WASTE_TYPE_LIST = [
  { value: "no_waste", label: "不存在浪费" },
  { value: "little_waste", label: "些许浪费" },
  { value: "very_wasteful", label: "非常浪费" }
]

// 操作规范类型
export const OPERATE_TYPE_LIST = [
  { value: "specifications", label: "操作规范" },
  { value: "little", label: "操作存在些许不规范" },
  { value: "improper", label: "操作不规范" },
  { value: "very_irregular", label: "操作非常不规范" }
]
// 消杀类型
export const DISINFECTION_TYPE_LIST = [
  { value: "timely", label: "及时消杀" },
  { value: "little", label: "存在一定时间延迟消杀" },
  { value: "very_untimely", label: "非常不及时消杀" },
  { value: "no_at_all", label: "完全不消杀" }
]

// 运作类型
export const OPERATION_TYPE_LIST = [
  { value: "normal", label: "运作正常" },
  { value: "little", label: "运作存在轻微卡顿" },
  { value: "not", label: "运作不正常" }
]

// 质量类型
export const QUALITY_TYPE_LIST = [
  { value: 3, name: "优秀", label: "excellent" },
  { value: 2, name: "良好", label: "good" },
  { value: 1, name: "差", label: "difference" }
]
// 分量类型
export const AMOUNT_TYPE_LIST = [
  { value: 3, name: "较多", label: "more" },
  { value: 2, name: "适中", label: "moderate" },
  { value: 1, name: "较少", label: "less" }
]
// 价格类型
export const PRICE_TYPE_LIST = [
  { value: 3, name: "实惠", label: "affordable" },
  { value: 2, name: "适中", label: "moderate" },
  { value: 1, name: "偏高", label: "high" }
]

// 经营预警筛选设置
export const SEARCH_FORM_MEAL_RECORD = {
  date_type: {
    type: 'select',
    label: '',
    value: 'create_date',
    dataList:
    [
      {
        label: "创建时间",
        value: "create_date"
      },
      {
        label: "陪餐时间",
        value: "meal_date"
      }
    ],
    clearable: false
  },
  select_time: {
    type: 'daterange',
    format: 'yyyy-MM-dd',
    label: '',
    value: recentThreeDay,
    clearable: false
  },
  organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '组织名称',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  meal_type: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  },
  identity_type: {
    type: 'select',
    label: '陪餐人身份',
    placeholder: '请选择陪餐人身份',
    listNameKey: 'label',
    listValueKey: 'value',
    dataList: IDENTITY_TYPE_LIST,
    value: [],
    multiple: true,
    collapseTags: true
  },
  person_name: {
    type: 'input',
    label: '陪餐人',
    labelWidth: '80px',
    value: '',
    placeholder: '请输入陪餐人',
    clearable: true
  },
  food_name: {
    type: 'input',
    labelWidth: '70px',
    label: '菜品',
    value: '',
    placeholder: '请输入菜品',
    clearable: true
  }
}
// 陪餐记录 表格设置
export const TABLE_HEAD_MEAL_RECORD = [
  {
    label: "创建时间",
    key: "create_time",
    width: "150px",
    align: "center"
  },
  {
    label: "陪餐时间",
    key: "meal_time",
    width: "150px",
    align: "center"
  },
  {
    label: "组织",
    key: "organization_name",
    align: "center"
  },
  {
    label: "餐段",
    key: "meal_type",
    type: "slot",
    slotName: 'meal_type',
    align: "center"
  },
  {
    label: "陪餐人",
    key: "person",
    align: "center",
    width: "150px",
    type: "slot",
    slotName: "person"
  },
  {
    label: "环境卫生、工作情况评价",
    key: "environmental_assessment",
    align: "center",
    children: [
      {
        label: "备餐间（含服务态度）",
        key: "room_clean_type",
        width: "180px",
        align: "center"
      },
      {
        label: "就餐区（含餐饮浪费情况）",
        key: "area_clean_type",
        width: "250px",
        align: "center"
      },
      {
        label: "其他加工操作区",
        key: "oa_clean_type",
        width: "150px",
        align: "center"
      },
      {
        label: "餐具消洗",
        key: "tda_clean_type",
        width: "150px",
        align: "center"
      },
      {
        label: "明厨亮灶运作",
        key: "operation_type",
        width: "150px",
        align: "center"
      }
    ]
  },
  {
    label: "菜品名称",
    key: "food_name",
    width: "200px",
    align: "center"
  },
  {
    label: "菜品直观评价",
    key: "food_assessment",
    align: "center",
    children: [
      {
        label: "感观",
        key: "gg_excellent_type_alias",
        align: "center"
      },
      {
        label: "质量",
        key: "zl_excellent_type_alias",
        align: "center"
      },
      {
        label: "份量",
        key: "quantity_type_alias",
        align: "center"
      },
      {
        label: "价格",
        key: "price_type_alias",
        align: "center"
      }
    ]
  },
  {
    label: "学生意见和建议",
    key: "food_remark",
    width: "150px",
    align: "center"
  },
  {
    label: "操作",
    key: "operation",
    type: "slot",
    slotName: "operation",
    align: "center",
    width: "100px",
    fixed: "right"
  }
]

// 陪餐记录详情 表格设置
export const TABLE_HEAD_MEAL_RECORD_DETAIL = [
  {
    label: "菜品名称",
    key: "name",
    width: "150px",
    type: 'slot',
    slotName: 'name',
    align: "center"
  },
  {
    label: "感观",
    key: "gg_excellent_type_alias",
    align: "center"
  },
  {
    label: "质量",
    key: "zl_excellent_type_alias",
    align: "center"
  },
  {
    label: "份量",
    key: "quantity_type_alias",
    align: "center"
  },
  {
    label: "价格",
    key: "price_type_alias",
    align: "center"
  },
  {
    label: "学生意见和建议",
    key: "remark",
    align: "center"
  },
  {
    label: "图片",
    key: "operation",
    align: "center",
    type: "slot",
    slotName: "operationImg"
  }
]
