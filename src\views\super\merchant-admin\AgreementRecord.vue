<template>
  <div class="agreement-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
        <button-icon color="origin" @click="gotoExport">导出EXCEL</button-icon>
      </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #name="{ row }">
              <el-button type="text" class="ps-text" v-if="!row.show_name" @click="row.show_name = true">查看</el-button>
              <div v-else @click="row.show_name = true">{{ row.name }}</div>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <div style="font-size: 12px; margin-left: 20px;">创建时间为用户初次签署协议的时间，更新时间为用户对同一协议进行重新签署的时间。</div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import dayjs from 'dayjs'
// import report from '@/mixins/report' // 混入

export default {
  name: 'AgreementRecord',
  mixins: [exportExcel],
  data() {
    const timeRange = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '手机号', key: 'phone' },
        { label: '商户组织', key: 'organization_name' },
        { label: '姓名', key: 'name', type: "slot", slotName: "name" },
        { label: 'IP', key: 'ip' },
        { label: '主体类型', key: 'major_type_alias' },
        { label: '协议类型', key: 'agreement_type_alias' },
        { label: '协议名称', key: 'agreement_name' },
        { label: '创建时间', key: 'create_time' },
        { label: '更新时间', key: 'update_time' }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 1,
          maxWidth: '130px',
          dataList: [
            {
              label: '创建时间',
              value: 1
            },
            {
              label: '更新时间',
              value: 2
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: true,
          format: 'yyyy-MM-dd',
          value: timeRange
        },
        major_type: {
          type: 'select',
          value: '',
          label: '主体类型',
          clearable: true,
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '商户',
              value: 'MERCHANT'
            },
            {
              label: '全局用户',
              value: 'GLOBAL_USER'
            },
            {
              label: '卡用户',
              value: 'CARD_USER'
            }
          ]
        },
        agreement_type: {
          type: 'select',
          value: '',
          label: '协议类型',
          clearable: true,
          dataList: []
        },
        phone: {
          type: 'input',
          value: '',
          label: '手机号',
          placeholder: '请输入'
        },
        o_id: {
          type: 'CompanySelect',
          value: '',
          multiple: false,
          label: '商户组织',
          filterable: true,
          clearable: true
        }
      }
    }
  },
  created() {
    this.getGreementType()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getAgreementRecord()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    async getGreementType() {
      const [err, res] = await to(this.$apis.apiBackgroundBaseMenuGetGreementTypePost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let arr = [
          {
            label: '全部',
            value: ''
          }
        ]
        for (const key in res.data) {
          arr.push({
            label: res.data[key],
            value: key
          })
        }
        this.searchFormSetting.agreement_type.dataList = arr
      } else {
        this.$message.error(res.msg)
      }
    },
    async getAgreementRecord() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminAgreementRecordListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          v.show_name = false
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getAgreementRecord()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    // 导出弹窗
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      const option = {
        type: 'AgreementRecord',
        url: 'apiBackgroundAdminAgreementRecordListExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.agreement-list {}
</style>
