<template>
  <div class="CardSubsidyDetail container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="mul" @click="grantSubsidy('clearMul')" v-if="isCurrentOrg(organization)" v-permission="['card_service.card_subsidy.bulk_clear_subsidy']">批量清零</button-icon>
          <button-icon color="plain" type="export" @click="handleExport" v-permission="['card_service.card_subsidy.info_list_export']">导出补贴</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :row-class-name="tableRowClassName"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column class-name="ps-checkbox" type="selection" width="37"></el-table-column>
          <el-table-column prop="trade_no" label="补贴订单编号" align="center"></el-table-column>
          <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="card_department_group_alias" label="部门" align="center"></el-table-column>
          <el-table-column prop="card_no" label="卡号" align="center"></el-table-column>
          <el-table-column prop="money" label="补贴金额" align="center"></el-table-column>
          <el-table-column prop="release_status_alias" label="发放状态" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <!-- <el-table-column prop="effect_time" label="生效时间" align="center"></el-table-column> -->
          <el-table-column prop="is_refresh" label="是否清零" align="center">
            <template slot-scope="scope">
              {{scope.row.is_refresh?'清零':'不清零'}}
            </template>
          </el-table-column>
          <el-table-column prop="clear_time_alias" label="清零时间" align="center"></el-table-column>
          <el-table-column prop="account_name" label="创建人" align="center"></el-table-column>
          <el-table-column prop="operate_name" label="发放人" align="center"></el-table-column>
          <el-table-column prop="used_money" label="已使用金额" align="center"></el-table-column>
          <el-table-column prop="unused_money" label="未使用金额" align="center"></el-table-column>
          <el-table-column prop="" label="操作" align="center" fixed="right">
            <template slot-scope="scope">
              <!-- v-if="scope.row.subsidy_status != 'NO_START' && scope.row.subsidy_status != 'CLEAR' && scope.row.subsidy_status != 'CLEARING'" -->
              <el-button
                :disabled="!isCurrentOrg(organization) || scope.row.receive_status === 'INVALID' || scope.row.receive_status === 'UNRECEIVE'"
                v-if="isClear(scope.row)"
                type="text"
                size="small"
                @click="grantSubsidy('clear', scope.row.id)"
                v-permission="['card_service.card_subsidy.clear_subsidy']">
                清零
              </el-button>
              <!-- <el-button
                :disabled="!isCurrentOrg(organization) || !isClear(scope.row)"
                type="text"
                size="small"
                @click="openDialog(scope.row)">
                冲销
              </el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <div class="total">
        总发放轮次：{{totalReleaseCount}}
      </div>
      <div class="total">
        <div class="total-item">总发放补贴金额：￥{{successMoney}}</div>
        <div class="total-item">发放成功人数：{{successCounts}}</div>
        <div class="total-item">发放失败人数：{{failCounts}}</div>
      </div>
      <div class="total">
        <div class="total-item">已使用补贴金额：￥{{totalUsedMoney}} </div>
        <div class="total-item">已使用人数：{{usedCount}}</div>
        <div class="total-item">未使用人数：{{unUsedCount}}</div>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 提示弹窗 -->
    <dialog-message :message="dialogMessage" :show.sync="showDialog" @confirm="confirmGrant" />
    <el-dialog
      title="冲销"
      :visible.sync="dialogVisible"
      width="400px"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false">
      <div>
        <el-form
          :model="dialogForm"
          ref="dialogForm"
          :rules="dialogFormRules"
        >
          <el-form-item prop="subsidyWriteOff">
            <el-input placeholder="请输入冲销金额" v-model="dialogForm.subsidyWriteOff" class="ps-input"></el-input>
          </el-form-item>
        </el-form>
        <div class="tips">可冲销金额：￥{{dialogInfo.unused_money}}</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="dialogVisible = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="confirmDialog">冲 销</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, divide, parseTime, times, getWeek } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { isCurrentOrg } from './utils'

export default {
  name: 'CardSubsidyDetail',
  components: {},
  props: {},
  mixins: [exportExcel],
  data() {
    let validatasubsidyWriteOff = (rule, value, callback) => {
      let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      if (value !== '') {
        if (Number(value) === 0) {
          callback(new Error('金额不能为0'))
        } else if (value > this.dialogInfo.unused_money) {
          callback(new Error('金额不能大于可冲销金额'))
        } else if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      subsidyId: '',
      organization: '',
      userId: '',
      searchFormSetting: {},
      selectListId: [],
      dialogMessage: '',
      showDialog: false,
      totalReleaseCount: '',
      successCounts: '',
      successMoney: '',
      failCounts: '',
      totalUsedMoney: '',
      usedCount: '',
      unUsedCount: '',
      dialogVisible: false,
      dialogInfo: {},
      dialogForm: {
        subsidyWriteOff: ''
      },
      dialogFormRules: {
        subsidyWriteOff: [{ required: true, validator: validatasubsidyWriteOff, trigger: "blur" }]
      }
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        if (route.query.id) {
          this.subsidyId = route.query.id
          this.subsidyType = this.$route.query.subsidy_type
          this.organization = Number(route.query.organization)
          this.initLoad()
        }
      },
      immediate: true
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    initLoad() {
      let info = {}
      if (this.subsidyType === 'MONTH_RELEASE') {
        info = {
          release_months: {
            type: 'month',
            label: '发放月份',
            value: parseTime(new Date(), '{y}-{m}'),
            placeholder: '请选择发放月份'
          }
        }
      } else if (this.subsidyType === 'WEEK_RELEASE') {
        info = {
          release_weeks: {
            type: 'week',
            label: '发放周',
            value: new Date(),
            placeholder: '请选择发放周',
            pickerOptions: {
              firstDayOfWeek: 1
            }
          }
        }
      } else if (this.subsidyType === 'DAY_RELEASE') {
        info = {
          release_dates: {
            type: 'date',
            label: '发放日期',
            value: parseTime(new Date(), '{y}-{m}-{d}'),
            placeholder: '请选择发放日期'
          }
        }
      }
      this.searchFormSetting = {
        name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        trade_no: {
          type: 'input',
          label: '补贴订单编号',
          labelWidth: '120px',
          value: '',
          placeholder: '请输入补贴订单编号'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        },
        card_user_group_id: {
          type: 'groupSelect',
          label: '分组',
          value: '',
          multiple: false,
          placeholder: '请选择分组'
        },
        card_department_group_id: {
          type: 'departmentSelect',
          multiple: false,
          isLazy: false,
          checkStrictly: true,
          label: '部门',
          value: '',
          placeholder: '请选择部门'
        },
        ...info
      }
      this.getSubsidyDetail()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getSubsidyDetail()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'release_months') {
            params.release_years = [data[key].value.split('-')[0]]
            params.release_months = [data[key].value.split('-')[1]]
          } else if (key === 'release_weeks') {
            params.release_years = [parseTime(data[key].value, '{y}')]
            params.release_weeks = [getWeek(data[key].value)]
          } else if (key === 'release_dates') {
            params.release_dates = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      params.subsidy_type = this.subsidyType
      return params
    },
    async getSubsidyDetail() {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardSubsidyInfoListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        id: this.subsidyId,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        res.data.results.map(item => {
          item.money = divide(item.money)
          item.used_money = divide(item.used_money)
          item.unused_money = divide(item.unused_money)
          item.clear_time_alias = item.clear_time ? parseTime(item.clear_time) : item.clear_time_alias
        })
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.totalReleaseCount = res.data.total_release_count
        this.successCounts = res.data.success_counts
        this.successMoney = divide(res.data.success_money)
        this.failCounts = res.data.fail_counts
        this.totalUsedMoney = divide(res.data.total_used_money)
        this.usedCount = res.data.used_count
        this.unUsedCount = res.data.unused_count
      } else {
        this.$message.error(res.msg)
      }
    },
    grantSubsidy(type, id) {
      this.userId = id
      this.grantType = type
      this.showDialog = true
      const content = '清零后，未领取的补贴将失效；如存在退款，其对应金额也将自动清零，是否继续？'
      switch (this.grantType) {
        case 'clear':
          this.dialogMessage = content
          break;
        case 'clearMul':
          if (this.selectListId.length > 0) {
            this.dialogMessage = content
            this.showDialog = true
          } else {
            this.showDialog = false
            this.$message.error('请先选择要操作的数据！')
          }
          break;
      }
    },
    confirmGrant() {
      let params = {
        card_subsidy_id: this.subsidyId
      }
      switch (this.grantType) {
        case 'clear':
          params.order_subsidy_ids = [this.userId]
          break;
        case 'clearMul':
          if (this.selectListId.length > 0) {
            params.order_subsidy_ids = this.selectListId
            this.showDialog = true
          } else {
            this.showDialog = false
            this.$message.error('请先选择要操作的数据！')
          }
          break;
      }
      this.operationSubsidy(params)
    },
    async operationSubsidy(params) {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardSubsidyClearUserSubsidyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.showDialog = false
        this.$message.success('成功')
        // 后台异步处理，1 秒后再更新一次看下
        setTimeout(this.getSubsidyDetail, 1000)
      } else {
        this.$message.error(res.msg)
      }
    },
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => { this.selectListId.push(item.id) })
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      // 计算新的总页数
      const newTotalPages = Math.ceil(this.totalCount / this.pageSize);
      // 如果当前页码超出了新的总页数范围，就调整到最后一页
      if (this.currentPage > newTotalPages && newTotalPages > 0) {
        this.currentPage = newTotalPages;
      }
      this.getSubsidyDetail()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getSubsidyDetail()
    },
    handleExport() {
      const option = {
        type: 'ExportSubsidyDetail',
        params: {
          page: 1,
          page_size: 9999999,
          ...this.formatQueryParams(this.searchFormSetting),
          id: this.subsidyId
        }
      }
      this.exportHandle(option)
    },
    isCurrentOrg,
    isClear(data) {
      let flag = true
      if (data.release_status === 'NO_START' || data.release_status === 'CLEAR' || data.release_status === 'CLEARING') {
        flag = false
      }
      // 有清零时间就不能操作清零了
      // 未使用的不同为0时也要显示下哦，要弹窗提示
      // eslint-disable-next-line eqeqeq
      if (data.clear_time) {
        flag = false
      }
      return flag
    },
    openDialog(info) {
      this.dialogInfo = info
      this.dialogVisible = true
      this.dialogForm.subsidyWriteOff = ''
    },
    async confirmDialog(params) {
      if (this.isLoading) return
      const res = await this.$apis.apiCardServiceCardOperateChargePost({
        card_user_id: this.dialogInfo.id,
        total_charge_off_money: times(this.dialogForm.subsidyWriteOff)
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.dialogVisible = true
        this.getSubsidyDetail()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
  .total{
    margin: 0 20px;
    display: flex;
    font-size: 14px;
    font-weight: bold;
    color: #606266;
    &-item{
      width: 250px;
    }
  }
  .tips{
    color: #606266;
    text-align: right;
    margin-top: 20px;
  }
</style>
