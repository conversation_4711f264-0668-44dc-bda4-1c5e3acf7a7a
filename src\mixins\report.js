import { getTreeDeepkeyList, findChildSetting, to, deepClone } from '@/utils'
import { mapGetters } from 'vuex'
// 打印混入
export default {
  data() {
    return {
      currentTableSetting: [],
      dialogPrintVisible: false
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  mounted () {
  },
  methods: {
    // 读取设置设置默认
    async initPrintSetting() {
      let defaultKeys = []
      defaultKeys = await this.getPrintSettingInfo()
      try {
        // defaultKeys = JSON.parse(getLocalStorage(this.$store.getters.userInfo.account_id + this.printType))
        if (!defaultKeys) { // null的情况为没设置过，设置过后默认是数组可为空数组
          // 检查是否有自定义默认隐藏字段配置
          // 再某个表中设置这个字段，可以默认不展示出来，需要报表设置钩上 defaultHiddenKeys: ['gerenweisheng', 'xiaohuidao', 'pifu', 'huxidao']
          if (this.defaultHiddenKeys && Array.isArray(this.defaultHiddenKeys)) {
            defaultKeys = this.getCustomDefaultSetting()
          } else {
            defaultKeys = getTreeDeepkeyList(this.tableSetting)
          }
        } else {
          defaultKeys = getTreeDeepkeyList(defaultKeys)
        }
      } catch (error) {
        // 检查是否有自定义默认隐藏字段配置
        if (this.defaultHiddenKeys && Array.isArray(this.defaultHiddenKeys)) {
          defaultKeys = this.getCustomDefaultSetting()
        } else {
          defaultKeys = getTreeDeepkeyList(this.tableSetting)
        }
      }
      if (defaultKeys.length < 12) {
        let setting = findChildSetting(this.tableSetting, defaultKeys)
        setting = this.deleteWidthKey(setting)
        this.currentTableSetting = setting
      } else {
        this.currentTableSetting = findChildSetting(this.tableSetting, defaultKeys)
      }
    },
    // 获取自定义默认设置，排除指定的隐藏字段
    getCustomDefaultSetting() {
      const hiddenKeys = this.defaultHiddenKeys || []
      const allKeys = getTreeDeepkeyList(this.tableSetting)
      return allKeys.filter(key => !hiddenKeys.includes(key))
    },
    // 获取设置数据
    async getPrintSettingInfo() {
      let defaultKeys = null
      const [err, res] = await to(this.$apis.apiBackgroundAdminAccountGetAccountPrintInfoPost({
        id: this.userInfo.account_id,
        print_key: this.printType
      }))
      if (err) {
        this.$message.error(err.message)
        return defaultKeys
      }
      if (res.code === 0) {
        console.log(res)
        defaultKeys = res.data
      } else {
        this.$message.error(res.msg)
      }
      return defaultKeys
    },
    // 获取设置数据
    async setPrintSettingInfo(list, params) {
      const [err, res] = await to(this.$apis.apiBackgroundAdminAccountSetAccountPrintInfoPost({
        id: this.userInfo.account_id,
        print_key: this.printType,
        print_list: list,
        ...params
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // console.log(res)
        this.$message.success('设置成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 打开打印设置
    openPrintSetting() {
      this.dialogPrintVisible = true
    },
    // 打印设置弹窗
    async confirmPrintDialog(val, params) {
      if (val) {
        // setLocalStorage(this.$store.getters.userInfo.account_id + this.printType, JSON.stringify(getTreeDeepkeyList(val)))
        let settingVal = deepClone(val)
        if (settingVal.length < 12) {
          settingVal = this.deleteWidthKey(settingVal)
        }
        await this.setPrintSettingInfo(settingVal, params)
        this.currentTableSetting = settingVal
      }
    },
    deleteWidthKey(data, child = 'children') {
      function traversal(data) {
        data.map(item => {
          if (item.width) {
            delete item.width
          }
          if (item.minWidth) {
            delete item.minWidth
          }

          if (item[child]) {
            if (item[child].length > 0) {
              traversal(item[child])
            }
          }
        })
      }
      traversal(data)
      return data
    },
    indexMethod(index) {
      return (this.page - 1) * this.pageSize + (index + 1)
    },
    // 设置统计的值
    setCollectData(res) {
      // 统计
      this.collect.forEach(item => {
        if (res.data.collect && res.data.collect[item.key] !== undefined) {
          this.$set(item, 'value', res.data.collect[item.key])
          // item.value = res.data.collect[item.key]
        }
      })
    },
    // 设置合计的值，严格依赖tableSetting，和数据的先后顺序
    setSummaryData(res) {
      let collect = res.data.collect
      collect[this.tableSetting[0].key] = '合计'
      if (this.tableData.length) {
        this.tableData.push(collect)
      }
      console.log(this.tableData, 666666)
    }
  },
  beforeDestroy() {
  }
}
