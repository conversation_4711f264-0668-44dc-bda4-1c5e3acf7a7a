<template>
  <!--表23 学校每周食品安全排查治理报告 -列表 -->
  <div class="assignment-ledger-permission container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandler"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="handleExport" v-permission="['background_fund_supervision.ledger_food_safety.get_weekly_food_safety_report_ledger_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content" style="padding-bottom: 0">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          height="calc(100vh - 570px)"
          :max-height="600"
        >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #first_director_sign="{ row }">
              <el-button type="text" size="small" @click="viewSignature(row, 'reserved_sign')">查看</el-button>
            </template>
            <template #second_director_sign="{ row }">
              <el-button type="text" size="small" @click="viewSignature(row, 'liquidator_sign')">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[5, 10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->

      <!-- 查看签名 -->
      <el-dialog :title="viewDialogTitle" :visible.sync="dialogVisible" width="440px" custom-class="notice-dialog">
        <div class="content">
          <el-image
            style="width: 100%; height: 100%"
            :src="url"
            :preview-src-list="previewSrcList"
          ></el-image>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: right">
          <el-button type="primary" size="small" @click="dialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import { SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN, TABLE_HEAD_DATA_SCHOOL_WEEKLY_FOOD_SAFETY } from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
export default {
  name: 'XuexiaoHuiyiJiyaoManagementLedger',
  mixins: [exportExcel, report],
  data() {
    return {
      printType: 'XuexiaoHuiyiJiyaoManagementLedger',
      dialogVisible: false,
      isShowDrawer: false, // 详情抽屉
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      viewDialogTitle: '', // 查体签名标题
      url: '',
      previewSrcList: [],
      tableSetting: deepClone(TABLE_HEAD_DATA_SCHOOL_WEEKLY_FOOD_SAFETY), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_SCHOOL_WEEKLY_FOOD_SAFETY), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN) // 查询表单配置
    }
  },
  created() {
    const date = this.$route.query.date
    if (date) {
      this.searchFormSetting.select_time.value = [date, date]
    }
    this.initLoad()
  },
  components: {},
  mounted() {},
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetWeeklyFoodSafetyReportLedger(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 查看签名
    viewSignature(row, key) {
      switch (key) {
        case 'reserved_sign':
          this.viewDialogTitle = '食品安全总监(1)签名'
          this.url = row.sign_info[0]
          this.previewSrcList = [row.sign_info[0]]
          break;

        case 'liquidator_sign':
          this.viewDialogTitle = '食品安全总监(2)签名'
          this.url = row.sign_info[1]
          this.previewSrcList = [row.sign_info[1]]
          break;
      }
      this.dialogVisible = true
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.currentTableSetting)
      // 去除图片
      tableSetting = tableSetting.filter(item => {
        return item.key !== 'sign_info_first' && item.key !== 'sign_info_second'
      })
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '学校每周食品安全排查治理报告',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetWeeklyFoodSafetyReportLedger', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 导出
    handleExport() {
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetWeeklyFoodSafetyReportLedgerExport',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.totalCount ? this.totalCount : 10
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss" scoped></style>
