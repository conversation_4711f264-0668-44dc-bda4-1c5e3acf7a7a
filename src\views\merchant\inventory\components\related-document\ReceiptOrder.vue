<template>
  <div v-loading="isLoading" class="receipt-order-box">
    <div v-if="type !== 'detail'" class="info-wrap">
      <div class="title">基本信息</div>
      <div class="form-item"><span class="form-label">单据编号：</span>{{ detailData.trade_no }}</div>
      <div class="form-item"><span class="form-label">创建时间：</span>{{ detailData.create_time }}</div>
      <div class="form-item"><span class="form-label">收货时间：</span>{{ detailData.create_time }}</div>
    </div>
    <div class="info-wrap">
      <div class="title">配送信息</div>
      <div class="form-item"><span class="form-label">配送温度：</span>{{ detailData.delivery_temperature }}°c</div>
      <div class="form-item">
        <span class="form-label">单据凭证：</span>
        <div v-if="detailData.purchase_certificate && detailData.purchase_certificate.length > 0" class="form-img-box">
          <el-image
            v-for="(img, k) in detailData.purchase_certificate"
            :key="img"
            class="detault-img m-r-6 pointer"
            :src="img"
            fit="contain"
            :preview-src-list="detailData.purchase_certificate"
            :initial-index="k"
          ></el-image>
        </div>
      </div>
    </div>
    <!-- 司机信息 -->
    <DriverInformation :driver-list="detailData.driver_info"></DriverInformation>
    <!-- 车辆信息 -->
    <VehicleInformation :vehicle-list="detailData.vehicle_info"></VehicleInformation>

    <div class="info-wrap" v-if="tabType === 'receiptOrder'">
      <div class="title">核验人员信息</div>
      <ul v-if="detailData.check_accounts && detailData.check_accounts.length > 0" class="check-box flex flex-wrap">
        <li v-for="(checkItem, index) in detailData.check_accounts" :key="index" class="check-item m-r-20">
          <div class="flex">
            <span>{{ checkItem.role_name }}：</span>
            <span>{{ checkItem.username }}</span>
          </div>
          <div class="flex">
            <span>收货实况人脸：</span>
            <span>
              <el-image
                :src="checkItem.face_url"
                fit="contain"
                class="face-img"
                :preview-src-list="[checkItem.face_url]"
              ></el-image>
            </span>
          </div>
        </li>
      </ul>
      <div v-else>无</div>
    </div>

    <div class="info-wrap">
      <div class="title">{{ type !== 'detail' ? '物资核验信息' : '合格物资信息' }}</div>
      <div>合计金额：￥{{ totalPrice }}</div>
      <el-table
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        stripe
        size="mini"
        max-height="600"
        header-row-class-name="ps-table-header-row"
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item">
          <template #img="{ row }">
            <el-button v-if="row.check_file && row.check_file.length > 0" type="text" size="small" class="ps-text" @click="clickViewerHandler(row)">查看</el-button>
            <span v-else>--</span>
          </template>
        </table-column>
      </el-table>
    </div>
    <div v-if="type === 'detail'" class="info-wrap">
      <div class="title">不合格物资信息</div>
      <div>合计金额：￥{{ noPassTotalPrice }}</div>
      <el-table
        :data="noPassTableData"
        ref="tableData"
        style="width: 100%"
        stripe
        size="mini"
        max-height="600"
        header-row-class-name="ps-table-header-row"
      >
        <table-column v-for="item in noPassTableSettings" :key="item.key" :col="item">
          <template #img="{ row }">
            <el-button v-if="row.check_file && row.check_file.length > 0" type="text" size="small" class="ps-text" @click="clickViewerHandler(row)">查看</el-button>
            <span v-else>--</span>
          </template>
        </table-column>
      </el-table>
    </div>
    <image-viewer
      v-model="showViewer"
      :initial-index="imgIndex"
      :z-index="3000"
      :on-close="closeViewer"
      :preview-src-list="previewSrcList"
    />
  </div>
  <!-- end -->
</template>

<script>
import { divide } from '@/utils'
import VehicleInformation from './VehicleInformation'
import DriverInformation from './DriverInformation'
import NP from 'number-precision'

export default {
  name: 'ReceiptOrderBox',
  props: {
    type: { //
      type: String,
      default: 'detail'
    },
    params: {
      type: Object,
      default: () => {}
    },
    api: {
      type: String,
      default: 'apiBackgroundDrpVendorDataVendorReceivingDetailListPost'
    },
    infoData: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    VehicleInformation,
    DriverInformation
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      tabType: 'receiptOrder',
      tableData: [],
      tableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '收货数量', key: 'receive_count' },
        { label: '规格', key: 'specification_record' },
        { label: '单价', key: 'unit_price' },
        { label: '合计金额', key: 'total_price' },
        { label: '保质期', key: 'valid_date' },
        { label: '核验附件', key: 'check_file', type: 'slot', slotName: "img" }
      ],
      noPassTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '收货数量', key: 'receive_count' },
        { label: '规格', key: 'specification_record' },
        { label: '单价', key: 'unit_price' },
        { label: '合计金额', key: 'total_price' },
        { label: '保质期', key: 'valid_date' },
        { label: '核验附件', key: 'check_file', type: 'slot', slotName: "img" }
      ],
      vehicleList: [],
      detailData: {},
      imgIndex: 0,
      previewSrcList: [],
      showViewer: false,
      totalPrice: 0, // 合计金额
      noPassTableData: [], // 不通过校验的物资
      noPassTotalPrice: 0
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getDataInfo()
    },
    async getDataInfo() {
      if (this.isLoading) return
      let currentApi = this.api
      if (!this.api && this.type === 'detail') {
        currentApi = 'apiBackgroundDrpVendorDataVendorReceivingDetailListPost'
      }
      if (!currentApi) {
        return this.$message.error('缺少参数，请检查！')
      }
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis[currentApi](this.params))
      this.isLoading = false
      this.detailData = {}
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        this.totalPrice = 0
        this.noPassTotalPrice = 0

        let passTotal = 0
        let noPassTotal = 0

        this.noPassTableData = []
        this.tableData = []
        // eslint-disable-next-line camelcase
        if (data?.ingredient_data) {
          data.ingredient_data.map((v) => {
            if (this.type === 'detail') {
              // is_pass=1为通过校验的物资
              if (v.is_pass === 1) {
                passTotal = NP.plus(passTotal, v.total_price)
                v.receive_count = (v.receive_count || 0) + (v.purchase_unit || '')
                v.unit_price = "￥" + divide(v.unit_price)
                v.total_price = "￥" + divide(v.total_price)
                v.valid_date = v.start_valid_date + "-" + v.end_valid_date
                this.tableData.push(v)
              } else {
                noPassTotal = NP.plus(noPassTotal, v.total_price)
                v.receive_count = (v.receive_count || 0) + (v.purchase_unit || '')
                v.unit_price = "￥" + divide(v.unit_price)
                v.total_price = "￥" + divide(v.total_price)
                v.valid_date = v.start_valid_date + "-" + v.end_valid_date
                this.noPassTableData.push(v)
              }
            } else {
              passTotal = NP.plus(passTotal, v.total_price)
              v.receive_count = (v.receive_count || 0) + (v.purchase_unit || '')
              v.unit_price = "￥" + divide(v.unit_price)
              v.total_price = "￥" + divide(v.total_price)
              v.valid_date = v.start_valid_date + "-" + v.end_valid_date
              this.tableData.push(v)
            }
          })
        }

        this.totalPrice = divide(passTotal)
        this.noPassTotalPrice = divide(noPassTotal)

        this.detailData = data || {}
      } else {
        this.$message.error(res.msg)
      }
    },
    clickViewerHandler(row, index) {
      // don't show viewer when preview is false
      this.previewSrcList = row.check_file || []
      this.imgIndex = index || 0
      this.showViewer = true;
    },
    closeViewer() {
      this.showViewer = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.receipt-order-box{
  .flex{
    display: flex;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .info-wrap{
    font-size: 14px;
    line-height: 30px;
    .title{
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0 10px;
    }
    .form-item {
      display: flex;
      font-size: 14px;
      line-height: 30px;
    }
    .form-img-box {
      flex: 1;
    }
    .detault-img {
      width: 160px;
      height: 92px;
      border: 1px dashed #e1e1e1;
      border-radius: 2px;
    }
    .face-img {
      margin-top: 10px;
      width: 80px;
      height: 100px;
    }
  }
}
</style>
