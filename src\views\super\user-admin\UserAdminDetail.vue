<template>
  <div class="user_admin_detail container-wrapper">
    <!-- 个人信息 -->
    <div class="user-info table-wrapper">
      <div class="table-header">
        <div class="table-title">个人信息</div>
      </div>
      <div class="user-info-wrapper">
        <div class="user-img">
          <!-- <el-image :src="userDetail.img_url" lazy></el-image> -->
          <el-avatar :size="120" fit="cover" :src="userDetail.img_url"></el-avatar>
        </div>
        <div class="user-info-r">
          <!-- 左右布局吧，上下宽度没保障 -->
          <div class="float-l clearfixt">
            <div class="info-item">
              <div class="label">姓名：</div>
              <div class="value">{{ userDetail.name }}</div>
            </div>
            <div class="info-item">
              <div class="label">性别：</div>
              <div class="value">{{ getGender(userDetail.gender) }}</div>
            </div>
            <div class="info-item">
              <div class="label">用户ID：</div>
              <div class="value">{{ userDetail.user_id }}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">来源渠道：</div>
              <div class="value">{{ userDetail.source_alias }}</div>
            </div>
            <div class="info-item">
              <div class="label">openid：</div>
              <div class="value">{{ userDetail.openid }}</div>
            </div>
            <div class="info-item">
              <div class="label">注册时间：</div>
              <div class="value">{{ userDetail.create_time }}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">微信号：</div>
              <div class="value">{{ userDetail.nickname }}</div>
            </div>
            <div class="info-item">
              <div class="label">手机号码：</div>
              <div class="value">{{ userDetail.phone }}</div>
            </div>
            <div class="info-item">
              <span style="margin-right: 20px;">
                <span class="label">关联用户：</span>
                <span class="value">11</span>
              </span>
              <span>
                <span class="label">关联企业:</span>
                <span class="value">22</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 账户信息 -->
    <div class="account-info table-wrapper">
      <div class="table-header">
        <div class="table-title">账户信息</div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="accountData"
          style="width: 100%"
          row-key="id"
          stripe
        >
          <template v-for="item in accountSetting">
            <el-table-column :key="item.key" :label="item.label" :prop="item.key" align="center">
              <template v-if="item.children">
                <template v-for="child in item.children">
                  <el-table-column
                    :key="child.key"
                    :label="child.label"
                    :prop="child.key"
                    align="center"
                  ></el-table-column>
                </template>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
    </div>
    <!-- 关联信息 -->
    <div class="account-info table-wrapper">
      <div class="table-header">
        <div class="table-title">关联信息</div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="relevanceData"
          style="width: 100%"
          row-key="id"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <template v-for="item in relevanceSetting">
            <el-table-column :key="item.key" :label="item.label" :prop="item.key" align="center">
              <template v-if="item.children">
                <template v-for="child in item.children">
                  <el-table-column
                    :key="child.key"
                    :label="child.label"
                    :prop="child.key"
                    align="center"
                  ></el-table-column>
                </template>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'

export default {
  name: 'SuperUserAdminDetail',
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      userDetail: {},
      accountData: [],
      accountSetting: [
        { key: '1', label: '人员编号' },
        { key: '2', label: '组织名称' },
        { key: '3', label: '储值钱包', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] },
        { key: '4', label: '补贴钱包', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] },
        { key: '5', label: '赠送钱包', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] },
        { key: '6', label: '农行电子钱包' },
        { key: '7', label: '建行电子钱包' },
        { key: '8', label: '第三方钱包', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] },
        { key: '9', label: '第三方钱包（卡务）', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] }
      ],
      relevanceData: [],
      relevanceSetting: [
        { key: '10', label: '姓名' },
        { key: '11', label: '用户ID' },
        { key: '12', label: '手机号' },
        { key: '13', label: '人员编号' },
        { key: '14', label: '组织名称' },
        { key: '15', label: '储值钱包', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] },
        { key: '16', label: '补贴钱包', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] },
        { key: '17', label: '赠送钱包', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] },
        { key: '18', label: '农行电子钱包' },
        { key: '19', label: '建行电子钱包' },
        { key: '20', label: '第三方钱包', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] },
        { key: '21', label: '第三方钱包（卡务）', children: [{ key: '', label: '总消费金额' }, { key: '', label: '余额' }] }
      ]
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getUserListDetail()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getUserListDetail()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.isRefresh = true
      // this.currentPage = 1;
      // this.tableData = []
      this.initLoad()
    },
    // 获取详情
    async getUserListDetail() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminUserUserInfoPost({
        id: this.$route.query.id
      })
      this.isLoading = false
      if (res.code === 0) {
        this.userDetail = res.data
        this.accountData = res.data.cardinfo_list
        this.userDetail.img_url =
          'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'
      } else {
        this.$message.error(res.msg)
      }
    },
    // 性别转换
    getGender(gender) {
      let genderText = '其它'
      switch (gender) {
        case 'WOMEN':
          genderText = '女'
          break
        case 'MAN':
          genderText = '男'
          break
      }
      return genderText
    },
    // 解绑
    unBindHandle(id) {
      this.$confirm('确定解绑？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // instance.cancelButtonLoading = true
            await this.$sleep(3000)
            // const [err, res] = await to(
            //   this.$apis.apiBackgroundOrganizationOrganizationDeletePost({
            //     ids: [delId]
            //   })
            // )
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            // if (err) {
            //   this.$message.error(err.message)
            //   return
            // }
            // if (res.code === 0) {
            //   done()
            //   this.$message.success('删除成功')
            //   this.getOrganizationList()
            //   this.changeTableTreeNode(data.parent, true, data)
            // } else {
            //   this.$message.error(res.msg)
            // }
            done()
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
ul,
li {
  padding: 0;
  margin: 0;
  list-style: none;
}
.user_admin_detail {
  // margin-top: 20px;
  // background-color: #fff;
  .user-info {
    // border-radius: 6px;
  }
  .info-title {
    padding: 10px 0 0 10px;
    font-size: 16px;
  }
  .user-info-wrapper {
    display: flex;
    align-items: center;
    padding: 20px;
    .user-img {
      width: 120px;
      margin-right: 20px;
    }
    .user-info-r {
      .float-l {
        float: left;
      }
      .info-item {
        display: flex;
        margin-right: 40px;
        padding: 5px 0;
      }
    }
  }
}
</style>
