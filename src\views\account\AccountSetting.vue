<template>
  <div class="AccountSetting container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" :show-refresh="false" />
    <div class="table-wrapper" style="padding: 15px 40px;">
      <div class="table-header">
        <div class="table-title">账号信息设置</div>
      </div>
      <div class="account-info-header">
        <img src="@/assets/img/account-img.png" alt="" srcset="">
        <div style="margin-left:20px;">
          <div style="font-weight: bold;font-size: 32px;">{{userInfo.username}}</div>
          <div class="account-role">{{userInfo.role_name}}</div>
        </div>
      </div>
      <div class="account-info">
        <div v-for="(item, index) in infoList" :key="index" :class="['account-info-items',item.key=='wechant'?'items-no-border':'']">
          <div class="item" style="display: flex;align-items: center;">
            <img :src="item.img" alt="" style="margin-right: 10px;">
            <span>{{item.label}}</span>
          </div>
          <div class="item" style="font-weight:bold;">{{item.value}}</div>
          <div v-if="item.key!=='account'">
            <el-button class="ps-plain-btn" size="mini" @click="openDialog(item.key)">{{item.key==='wechant'?userInfo.has_bind_wechat?'解绑':'绑定':item.key=='phone'&&!item.value?'绑定':'修改'}}</el-button>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="showDialog"
      ref="settingFormRef"
      width="320px"
      top="25vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      @close="closeDialog"
    >
      <div class="dialog-content ps-small-box">
        <el-form :model="dialogForm" :rules="rules" ref="ruleFormRef1" class="form" label-width="0">
          <div v-if="dialogType === 'name'">
            <div class="label">姓名</div>
            <el-form-item prop="name">
              <el-input
                v-model="dialogForm.name"
                maxlength="48"
                :placeholder="$t('placeholder.name')"
                class="ps-input"
                clearable
              >
              </el-input>
            </el-form-item>
          </div>
          <div v-if="dialogType === 'password'">
            <div class="label">旧密码</div>
            <el-form-item prop="oldPassword">
              <el-input
                v-model="dialogForm.oldPassword"
                maxlength="48"
                show-password
                :placeholder="$t('placeholder.oldPassword')"
                class="ps-input"
                clearable
              >
              </el-input>
            </el-form-item>
            <div class="label">新密码</div>
            <el-form-item prop="newPassword">
              <el-input
                v-model="dialogForm.newPassword"
                maxlength="48"
                show-password
                :placeholder="$t('placeholder.newPassword')"
                class="ps-input"
                clearable
              >
              </el-input>
            </el-form-item>
            <div class="label">确认密码</div>
            <el-form-item prop="checkPassword">
              <el-input
                v-model="dialogForm.checkPassword"
                maxlength="48"
                show-password
                :placeholder="$t('placeholder.checkPassword')"
                class="ps-input"
                clearable
              >
              </el-input>
              <div style="margin-top:3px; color: #F56C6C; line-height: 1; font-size: 12px;">密码有效期为90天，请在期限前重置密码</div>
            </el-form-item>
          </div>
          <div v-if="dialogType === 'phone'">
            <div v-if="userInfo.phone" style="color: #88898D;margin-bottom:30px;">原绑定手机 <span>{{'+86'+userInfo.phone}}</span></div>
            <div class="label">{{userInfo.phone?'新':''}}手机号码</div>
            <el-form-item prop="newPhone">
              <el-input
                v-model="dialogForm.newPhone"
                maxlength="48"
                :placeholder="$t('placeholder.newPhone')"
                class="ps-input"
                clearable
              >
              </el-input>
            </el-form-item>
            <div class="verify-pop">
              <verify-code ref="verifyCode" :visible="isShowVerify" @success="verifySuccess" :is-number="true" @refresh="verifyRefresh"></verify-code>
            </div>
            <div class="label">短信验证码</div>
            <el-form-item>
              <verification-code :sendAuthCode="sendAuthCode" :disabled="sendCodeDisabled" v-model="dialogForm.smsCode" :reset-handle="resetHandle" @click="getVerCode"></verification-code>
            </el-form-item>
          </div>
          <div v-if="dialogType === 'wechant'">
            <div v-if="userInfo.has_bind_wechat">
              解绑微信后将无法使用微信登录系统后台，请谨慎操作
            </div>
            <div v-else style="height:260px;">
              <wx-login
                :appid="appid"
                scope="snsapi_login"
                :redirect_uri="redirect_uri"
                id="iframeBind"
                href="data:text/css;base64,LmltcG93ZXJCb3h7CiB3aWR0aDphdXRvOwp9Ci5pbXBvd2VyQm94IC50aXRsZSB7CiBkaXNwbGF5OiBub25lOwp9Ci5pbXBvd2VyQm94IC5xcmNvZGUgewogYm9yZGVyOiBub25lOwogd2lkdGg6IDIwMHB4Owp9Ci5pbXBvd2VyQm94IC5zdGF0dXMgewp3aWR0aDoyMDBweDsKbWFyZ2luOiBhdXRvOwp0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KLmltcG93ZXJCb3ggLnN0YXR1c19icm93c2VyewogZGlzcGxheTogbm9uZTsKfQouaW1wb3dlckJveCAuc3RhdHVzX3N1Y2MgLnN0YXR1c190eHQgcHsKIGRpc3BsYXk6IG5vbmU7Cn0KLmltcG93ZXJCb3ggLnN0YXR1c19mYWlsIC5zdGF0dXNfdHh0IHB7CiBkaXNwbGF5OiBub25lOwp9Cg=="
              ></wx-login>
            </div>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" v-if="!(dialogType=='wechant'&&!userInfo.has_bind_wechat)">
        <el-button :disabled="isLoading" class="ps-plain-btn" type="primary" @click="showDialog = false">
          {{ dialogType=='wechant'&&userInfo.has_bind_wechat?'暂不解绑':$t('dialog.cancel_btn') }}
        </el-button>
        <el-button
          :disabled="isLoading"
          :class="[dialogType=='wechant'&&userInfo.has_bind_wechat?'ps-red-btn':'ps-btn']"
          type="primary"
          @click="checkModifyData('ruleFormRef1')">
          {{dialogType=='wechant'&&userInfo.has_bind_wechat?'解绑':'确定'}}
        </el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="身份验证"
      :visible.sync="verificationDialog"
      width="320px"
      top="25vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      @close="closeVerificationDialog"
    >
      <div class="dialog-content ps-small-box">
        <el-form :model="dialogForm" :rules="rules" ref="ruleFormRef2" class="form" label-width="0">
          <div style="color: #88898D;margin-bottom:20px;">为了保护你的账号安全，请验证身份，验证成功后进行下一步操作</div>
          <div style="margin-bottom:10px;">使用手机 <span>{{infoList.phone.value}}</span> 验证</div>
          <el-form-item prop="smsCode">
            <verification-code :sendAuthCode="sendAuthCode" :disabled="sendCodeDisabled" :reset-handle="resetHandle" v-model="dialogForm.smsCode" @click="getVerifyCode"></verification-code>
          </el-form-item>
          <div style="color: #88898D;margin-top:20px;">无法获取短信验证码请联系管理员</div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-plain-btn" type="primary" @click="verificationDialog = false">
          {{ $t('dialog.cancel_btn') }}
        </el-button>
        <el-button class="ps-btn" type="primary" @click="checkModifyData('ruleFormRef2')">验证</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone } from '@/utils'
// import wxLogin from 'vue-wxlogin';
import md5 from 'js-md5';
import { mapGetters } from "vuex"
import VerifyCode from '@/components/VerifyCode/VerifyCode.vue'
import { Base64 } from 'js-base64';
export default {
  name: 'AccountSetting',
  components: { VerifyCode },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    let validatePass = (rule, value, callback) => {
      let regPass = /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/
      // let regPass = /^[0-9A-Za-z]{8,20}$/;
      // let regPass = /(^\w{6,32}$)/;
      if (!value) {
        return callback(new Error("密码不能为空"));
      } else {
        if (!regPass.test(value)) {
          callback(new Error("密码长度8到20位，字母和数组组合"));
        } else {
          callback();
        }
      }
    };
    let validateTelphone = (rule, value, callback) => {
      console.log("validateTelphone", value);
      if (!value) {
        this.sendCodeDisabled = true
        return callback(new Error("手机号不能为空"));
      } else {
        let regTelphone = /^1[3456789]\d{9}$/
        if (!regTelphone.test(value)) {
          callback(new Error("手机号格式错误"));
          this.sendCodeDisabled = true
        } else {
          this.sendCodeDisabled = false
          callback();
        }
      }
    };
    let validateSmsCode = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("请输入验证码"));
      } else {
        if (!/^[0-9]{6}$/.test(value)) {
          callback(new Error("验证码应为六位数字"));
        } else {
          callback();
        }
      }
    };
    return {
      infoList: {
        name: {
          key: 'name',
          img: require('@/assets/img/name-icon.png'),
          label: '姓名',
          value: ''
        },
        account: {
          key: 'account',
          img: require('@/assets/img/account-icon.png'),
          label: '账号',
          value: ''
        },
        password: {
          key: 'password',
          img: require('@/assets/img/password-icon.png'),
          label: '密码',
          value: '**********'
        },
        phone: {
          key: 'phone',
          img: require('@/assets/img/phone-icon.png'),
          label: '手机号',
          value: ''
        },
        wechant: {
          key: 'wechant',
          img: require('@/assets/img/wechant-icon.png'),
          label: '微信',
          value: ''
        }
      },
      appid: '',
      redirect_uri: '',
      showDialog: false,
      dialogTitle: '',
      dialogType: '',
      dialogForm: {
        name: '',
        oldPassword: '',
        newPassword: '',
        checkPassword: '',
        smsCode: '',
        newPhone: '',
        code: ''
      },
      rules: {
        oldPassword: [{ validator: validatePass, trigger: "change" }],
        newPassword: [{ validator: validatePass, trigger: "change" }],
        checkPassword: [{ validator: validatePass, trigger: "change" }],
        newPhone: [{ validator: validateTelphone, trigger: "blur" }],
        smsCode: [{ validator: validateSmsCode, trigger: 'change' }]
      },
      verificationDialog: false,
      sendCodeDisabled: true,
      sendAuthCode: true,
      isLoading: false,
      isShowVerify: false,
      answerList: [],
      answer: ''
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ])
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query.code) {
          this.dialogForm.code = query.code
          this.modifyUserinfo()
          console.log(1222)
        }
      },
      immediate: true
    }
  },
  created() {
    this.getWxLoginData()
    this.updateData()
  },
  mounted() {},
  methods: {
    // 节下流咯
    searchHandle: debounce(function() {
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.updateData()
    },
    openDialog(type) {
      // 重置数据
      this.dialogForm.name = ''
      this.dialogForm.oldPassword = ''
      this.dialogForm.newPassword = ''
      this.dialogForm.checkPassword = ''
      this.dialogForm.newPhone = ''
      this.dialogForm.smsCode = ''
      this.verifyCode = ''
      this.verifyUrl = ''
      this.dialogType = type
      this.sendAuthCode = true
      this.sendCodeDisabled = false
      this.isShowVerCode = false
      if (type === 'name') {
        this.dialogTitle = "修改姓名"
        this.showDialog = true
      } else if (type === 'password') {
        this.dialogTitle = "修改密码"
        this.showDialog = true
      } else if (type === 'phone') {
        this.dialogTitle = "绑定新手机号"
        //  this.showDialog = true
        if (this.infoList.phone.value) {
          this.sendCodeDisabled = false
          this.verificationDialog = true
          this.dialogForm.smsCode = ''
        } else {
          this.showDialog = true
        };
      } else if (type === 'wechant') {
        if (this.userInfo.has_bind_wechat) {
          if (this.infoList.phone.value) {
            this.verificationDialog = true
            this.dialogForm.smsCode = ''
            this.dialogTitle = "解绑第三方账号"
          } else {
            this.dialogTitle = "绑定新手机号"
            this.dialogType = 'phone'
            this.showDialog = true
          }
        } else {
          this.dialogTitle = "扫码绑定"
          this.showDialog = true
        }
      }
      this.$nextTick(() => {
        this.$refs.ruleFormRef1.clearValidate()
      })
    },
    // 校验
    checkModifyData(form) {
      this.$refs[form].validate(valid => {
        if (valid) {
          if (this.dialogType === 'phone' && !this.dialogForm.smsCode) {
            return this.$message.error('请输入短信验证码')
          }
          this.modifyUserinfo();
        } else {
          this.$message.error('请输入正确的信息！')
        }
      })
    },
    async modifyUserinfo() {
      let params = {}
      // 1 - 修改姓名;  2 - 修改密码;  3 - 修改手机号;  4 - 绑定微信;  5 - 解绑微信
      let choices
      if (this.dialogType === 'name') {
        choices = 1
        params.name = this.dialogForm.name
      } else if (this.dialogType === 'password') {
        if (this.dialogForm.newPassword === this.dialogForm.checkPassword) {
          choices = 2
          params.password = md5(this.dialogForm.oldPassword)
          params.new_password = md5(this.dialogForm.newPassword)
        } else {
          return this.$message.error('新密码输入不一致')
        }
      } else if (this.dialogType === 'phone' && !this.verificationDialog) {
        choices = 3
        params.phone = this.dialogForm.newPhone
        params.sms_code = this.dialogForm.smsCode
        params.code = this.answer
      } else if (this.dialogType === 'wechant' && this.userInfo.has_bind_wechat) {
        choices = 5
        params.sms_code = this.dialogForm.smsCode
      }
      if (this.dialogForm.code) {
        choices = 4
        params.code = this.dialogForm.code
      }
      if (this.verificationDialog) {
        choices = 6
        params.sms_code = this.dialogForm.smsCode
      }
      params.choices = choices
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundModifyUserinfoPost(params))
      this.isLoading = false
      this.resetHandle()
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (choices === 4) {
          this.$message.success('绑定成功')
          // 这里不能直接更新，因为modify和login返回的数据不同
          // await this.$store.dispatch('user/login', res.data)
          this.updateData(res.data)
          this.dialogForm.code = ""
          this.$router.push({ query: {} })
          this.showDialog = false
        } else if (choices === 6) {
          this.showDialog = true
          this.verificationDialog = false
        } else {
          this.$message.success('修改成功')
          // await this.$store.dispatch('user/login', res.data)
          this.updateData(res.data)
          this.showDialog = false
        }
      } else {
        this.$message.error(res.msg)
        if (choices === 4) {
          this.showDialog = false
          this.replaceRouter()
        }
        if (this.$needCloseDialog(res.code)) {
          this.showDialog = false
        }
      }
    },
    // 需要传手机号，验证码时效五分钟
    async getPhoneCode() {
      const [err, res] = await to(this.$apis.apiBackgroundVerificationCodePost({
        phone: this.dialogForm.newPhone,
        choices: 1,
        code: this.answer
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.sendAuthCode = false
        this.$message.success('发送成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 绑定手机号后自动获取验证码
    async getVerifyCode() {
      const [err, res] = await to(this.$apis.apiBackgroundVerificationCodeAutoPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.sendAuthCode = false
        this.$message.success('发送成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重置验证码倒计时
    resetHandle(e) {
      this.sendAuthCode = true
      this.sendCodeDisabled = false
    },
    async getWxLoginData() {
      const [err, res] = await to(this.$apis.apiBackgroundWechatValidatePost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.appid = res.data.appid
        this.redirect_uri = this.redirect_uri = res.data.redirect_uri ? encodeURIComponent(res.data.redirect_uri + '/#/account/setting') : ''
      } else {
        this.$message.error(res.msg)
      }
    },
    updateData(data) {
      // this.userInfo = JSON.parse(decodeURIComponent(getSessionStorage('USERINFO')))
      if (data) {
        let newInfo = deepClone(this.userInfo)
        for (const key in data) {
          newInfo[key] = data[key]
        }
        this.$store.dispatch('user/updateUserInfo', newInfo)
      }
      this.infoList.name.value = this.userInfo.name
      this.infoList.account.value = this.userInfo.username
      this.infoList.phone.value = this.userInfo.mobile
      this.infoList.wechant.value = this.userInfo.has_bind_wechat ? this.userInfo.nickname : ''
    },
    replaceRouter() {
      console.log(***********)
      this.$router.replace({
        name: 'AccountSetting'
      })
    },
    // 获取验证码图形
    async getVerifyCodeHardle() {
      this.getVerCode(this.dialogForm.newPhone)
    },
    // 获取图形验证码
    async getVerCode(flag) {
      if (!this.dialogForm.newPhone || !/^1[3456789]\d{9}$/.test(this.dialogForm.newPhone)) {
        return this.$message.error("请输入正确的手机号")
      }
      const res = await this.$apis.apiBackgroundGetSmsVerifyCodePost({
        phone: this.dialogForm.newPhone
      });
      if (res.code === 0) {
        let data = res.data || {}
        this.answer = data.key || ''
        let keys = Base64.decode(data.key) ? JSON.parse(Base64.decode(data.key)) : ''
        console.log("getLoginVerifyCode", keys);
        this.answerList = []
        if (keys && typeof keys === 'object') {
          for (let keyName in keys) {
            this.answerList.push(keys[keyName])
          }
        }
        if (this.$refs.verifyCode) {
          this.$refs.verifyCode.setAnswerList(this.answerList)
          if (flag) {
            this.$refs.verifyCode.reset()
          }
        }
        console.log("result", this.answerList)
        this.isShowVerify = true
      } else {
        this.$message.error(res.msg);
      }
    },
    // 验证成功
    verifySuccess(value) {
      console.log("verifySuccess", value);
      this.isShowVerify = false
      this.getPhoneCode()
    },
    verifyRefresh() {
      this.getVerCode(true)
    },
    closeDialog() {
      this.isShowVerify = false
      this.showDialog = false
      this.sendCodeDisabled = false
    },
    closeVerificationDialog() {
      if (this.$refs.ruleFormRef2) {
        this.$refs.ruleFormRef2.clearValidate()
      }
      this.verificationDialog = false
      this.resetHandle()
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
  $origin: #ff9b45;
  $origin-hover: #ffa558;
  $origin-active: #e58b3e;
  @mixin origin-border {
    border-color: $origin;
    input{
      &:focus{
        border-color: $origin;
      }
    }
    .el-input.is-focus .el-input__inner {
      border-color: $origin;
    }
  }

  .account-info-header{
    display: flex;
    margin: 20px 0 30px 0;
    .account-role{
      background-color: #ff9b45;
      color: #fff;
      font-size: 14px;
      border-radius: 4px;
      padding: 2px;
      text-align: center;
      display: inline-block;
    }
  }
  .account-info{
    width: 600px;
    border: 1px #E3E8EC solid;
    border-radius: 6px;
    margin: 20px 0;
    .account-info-items{
      display: flex;
      align-items: center;
      padding: 20px;
      border-bottom: 1px #E3E8EC solid;
      .item{
        width: 250px;
      }
    }
    .items-no-border{
      border: none;
    }
  }
  .dialog-content{
    .label{
      margin-bottom: 5px;
    }
    .phone-code-btn{
      &:hover{
        color: $origin;
        border-color: $origin;
        background-color: #fff;
      }
      &:focus{
        color: $origin;
        border-color: $origin;
        background-color: #fff;
      }
      &:active{
        color: $origin-active;
        border-color: $origin-active;
        background-color: #fff;
      }
      &[disabled] {
        color: #C0C4CC !important;
        cursor: not-allowed;
        background-image: none;
        background-color: #FFF !important;
        border-color: #EBEEF5 !important;
      }
    }
  }
  .verify-code-box{
    .verify-code{
      width: 160px;
    }
    .verify-code-img{
      float: right;
      width: 100px;
      height: 40px;
      margin-left: 5px;
      margin-right: 0;
      vertical-align: middle;
      cursor: pointer;
    }
  }
  .verify-pop {
    position: absolute;
    left: 0%;
    top: 33%;
  }
</style>
