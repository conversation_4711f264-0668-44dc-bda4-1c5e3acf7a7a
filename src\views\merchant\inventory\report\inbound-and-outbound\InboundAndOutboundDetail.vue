<template>
  <div class="PurchaseListDetails container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表
          <span class="inline-block m-l-20 font-size-16">
            当前仓库：
            <span style="color: 000; font-weight: 700">{{ $route.query.warehouse_name }}</span>
          </span>
        </div>
        <div class="align-r">
          <!-- <button-icon color="origin" @click="handleExport">导出数据</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="id"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right; ">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, getSevenDateRange } from '@/utils'

export default {
  name: 'PurchaseListDetails',
  components: {
  },
  mixins: [exportExcel],
  data() {
    return {
      importLink: '',
      supplierId: '',
      tableData: [{}],
      isLoading: false, // 刷新数据
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '创建时间',
          format: 'yyyy-MM-dd',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入',
          maxlength: 20
        },
        inventory_type: {
          type: 'select',
          label: '类型',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '采购入库',
              value: 'PURCHASE_ENTRY'
            },
            // {
            //   label: '赠予入库',
            //   value: 'BESTOW_ENTRY'
            // },
            {
              label: '调拨入库',
              value: 'BORROW_ENTRY'
            },
            {
              label: '调拨出库',
              value: 'BORROW_EXIT'
            },
            {
              label: '损耗出库',
              value: 'EXPEND_EXIT'
            },
            {
              label: '过期出库',
              value: 'OVERDUE_EXIT'
            },
            {
              label: '采购退货出库',
              value: 'REFUND_EXIT'
            },
            {
              label: '领料出库',
              value: 'RECEIVE_EXIT'
            },
            {
              label: '归还入库',
              value: 'RETURN_ENTRY'
            },
            {
              label: '归还出库',
              value: 'RETURN_EXIT'
            },
            {
              label: '其他入库',
              value: 'OTHER_ENTRY'
            },
            {
              label: '其他出库',
              value: 'OTHER_EXIT'
            }
          ]
        }
      },
      tableSettings: [
        { label: '创建时间', key: 'create_time' },
        { label: '单据编号', key: 'trade_no' },
        { label: '类型', key: 'inventory_type' },
        { label: '经手人', key: 'account_name' },
        { label: '备注', key: 'remark' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', width: '160px', fixed: 'right' }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getInboundAndOutboundDetail()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getInboundAndOutboundDetail()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getInboundAndOutboundDetail() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDrpWarehouseInfoListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        warehouse_id: this.$route.query.warehouse_id,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getInboundAndOutboundDetail()
    },
    handleExport() {
      const option = {
        type: 'InquiryList',
        url: 'apiBackgroundDrpInquiryExportInquiryPost',
        params: {}
      }
      this.exportHandle(option)
    },
    // 详情
    gotoHandle(type, data) {
      this.$router.push({
        name: 'InboundAndOutboundReport',
        query: {
          ...this.$route.query,
          id: data.id,
          trade_no: data.trade_no
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
