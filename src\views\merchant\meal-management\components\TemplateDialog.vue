<template>
  <!-- dialog start -->
  <dialog-message :show.sync="visible" class="template-dialog" :loading="dialogLoading" :title="dialogTitle" top="10vh" :width="dialogWidth" :showFooter="false" @close="closeDialog">
    <div class="p-b-30 m-l-30 max-h-600">
      <div v-for="(item, key) in formSetting" :key="key">
        <h3>{{ item.label }}</h3>
        <div class="m-l-60 m-t-20">
          <span class="inline-block m-r-60 m-b-20 vertical-top" v-for="child in item.children" :key="child.key">
            <span class="inline-block max-w-100 vertical-top">{{ child.label + '：' }}</span>
            <span class="vertical-top m-l-10 m-r-6">{{ formData[item.key][child.key] }}道</span>
          </span>
        </div>
      </div>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { deepClone } from '@/utils'
import { MEAL_TYPES } from '@/utils/constants'

export default {
  name: 'TemplateDialog',
  // 重新定义下v-model的prop和触发的event
  // 为什么要重新定义的为了区别其它嵌套组件使用的v-model
  model: {
    prop: 'showDialog',
    event: 'changeShow'
  },
  props: {
    // 绑定的数据
    showDialog: {
      required: true
    },
    dialogTitle: {
      type: String,
      default: '预览'
    },
    // 类型
    type: {
      type: String,
      default: 'add'
    },
    // 弹窗数据源
    infoData: {
      type: Array,
      default: () => {
        return []
      }
    },
    dialogWidth: {
      type: String,
      default: '760px'
    },
    // 关闭的回调
    closehandle: Function,
    // 确定回调
    confirmhandle: Function
  },
  data() {
    return {
      dialogLoading: false, // loading
      formSetting: {},
      formData: {},
      mealList: MEAL_TYPES,
      templateAttributeList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.showDialog
      },
      set(val) {
        this.$emit('changeShow', val)
      }
    }
  },
  watch: {
    showDialog(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {
  },
  mounted() {
    this.getFoodAttributeList()
  },
  methods: {
    init() {
      // this.formData = this.infoData
      this.infoData.forEach(meal => {
        const categoryJson = meal.category_json_data
        for (const key in this.formData[meal.meal_type]) {
          if (categoryJson[key] !== '') {
            this.$set(this.formData[meal.meal_type], key, categoryJson[key])
          }
        }
      });
    },
    // 获取菜品分类数据
    async getFoodAttributeList() {
      let [err, res] = await this.$to(
        this.$apis.apiBackgroundFoodFoodCategoryListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.setDefaultAttrData(res.data.results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置餐段分类数据
    setDefaultAttrData(results) {
      let defaultAttr = {}
      if (results) {
        this.templateAttributeList = results.map(v => {
          let obj = {
            label: v.name,
            key: v.id
          }
          defaultAttr[v.id] = ''
          return obj
        })
        this.formSetting = this.mealList.map(v => {
          this.$set(this.formData, v.value, deepClone(defaultAttr))
          return {
            label: v.label,
            key: v.value,
            children: deepClone(this.templateAttributeList)
          }
        })
      } else {
        this.templateAttributeList.map(v => {
          defaultAttr[v.key] = ''
        })
        this.mealList.map(v => {
          this.$set(this.formData, v.value, deepClone(defaultAttr))
        })
      }
    },
    closeDialog() {
      this.resetForm()
      this.closehandle && this.closehandle()
    },
    cancleDialog() {
      this.resetForm()
      this.closehandle && this.closehandle()
      this.visible = false
    },
    confirmDialog() {
      this.resetForm()
      this.confirmhandle && this.confirmhandle()
      // this.$emit('confirmdialog')
    },
    // 重置表单
    resetForm() {
      // 重置表单数据
      this.dialogForm = {}
      this.setDefaultAttrData()
    }
  }
};
</script>

<style scoped lang="scss">
  .w-auto {
    width: 80%;
  }
  .w-100 {
    width: 100px;
  }
  .w-200 {
    width: 200px;
  }
  .max-w-100 {
    width: 100px;
  }
  .max-h-600 {
    max-height: 600px;
    overflow-y: auto;
  }
  .vertical-top {
    vertical-align: top;
  }
  // 有些地方需要二次弹窗的给个默认背景颜色
.template-dialog {
  ::v-deep.dialog-message{
    background-color: rgba(88, 88, 88, 0.3);
  }
}
</style>
