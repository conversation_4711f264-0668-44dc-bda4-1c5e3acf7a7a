<template>
  <div class="" v-loading="isLoading">
    <div class="box-header-week">
      <span>本月菜谱（{{ currentYear }}）</span>
      <!-- <header class="month-menu-head">本月菜谱 {{ currentYear }}</header> -->
      <div class="meal-type">
        <div>
          <!-- 暂时隐藏，不要删，这功能产品说等下次优化 -->
          <!-- <span>
            <el-popover placement="left" title="营养指导说明" width="300" trigger="hover">
              <div>
                <div>
                  营养指导是由朴食智能AI营养模型根据《中国居民膳食指南2022版》等作为指导，中国营养学会权威推荐，配合营养指导制定的营养规则，创建菜谱过程中，系统会根据菜谱对应的人群画像指导菜品配置过程，来满足大多数个体或集体人群的营养建议和风险预警，帮助人群合理搭配营养膳食
                </div>
                <div>1.能量摄入：中国居民膳食能量需要量。</div>
                <div>
                  2.三大营养素：指蛋白质、脂肪、碳水化合人体三大主要必需的营养素，具有重要的生理作用。
                </div>
                <div>
                  3.食物多样性：指一日三餐膳食的食物种类全、品样多，是平衡膳食的基础，具体包含谷类、鱼禽蛋肉、蔬菜、水果等四大类
                </div>
              </div>
              <i slot="reference" class="el-icon-question" style="color:#ff9b45;"></i>
            </el-popover>
            营养指导
            <el-switch
              v-model="isNutritionGuidance"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
              @change="switchGuidance"
            ></el-switch>
          </span> -->
        </div>
      </div>
    </div>
    <div>
      <div class="week-header-wrapper">
        <div v-for="d in weekDays" :key="d" class="week-day">{{ d }}</div>
      </div>
      <div class="content">
        <!-- <div v-bind:style="{ color: activeColor, fontSize: fontSize + 'px' }"></div> -->
        <div
          v-for="(dw, idx) in dayList"
          :key="`${dw}_${idx}`"
          :style="{ borderRight: idx === dayList.length - 1 ? border : '' }"
          class="item"
        >
          <div v-if="dw !== ''">{{ currentMonth }}月{{ dw.dayNumber }}日</div>
          <!-- <div v-if="dw !== ''">{{ dw.menu_date }}</div> -->
          <div
            :class="{
              'meal-cell-wrapper-bg': dw && !cateringFoodNumber(dw.data.foods)
            }"
             style="cursor: pointer;"
             @click="openEditDialog(dw)"
          >
            <div class="operate" v-if="dw !== ''">
              <span
                v-if="cateringFoodNumber(dw.data.foods)"
                style="font-size: 12px"

              >
                已配餐（{{ cateringFoodNumber(dw.data.foods) }}）
              </span>
              <span class="copy" v-else>未配餐</span>
              <span
                class="edit"
                @click.stop="openCopyDialog(dw)"
                v-if="cateringFoodNumber(dw.data.foods)"
              >
                复制
              </span>
            </div>
            <div
              class="fee-wrapper "
              v-if="isNutritionGuidance && dw !== '' && dw.data.foods.length > 0"
            >
              <div class="header-wrapper" style="padding:0px;">
                <div class="marker-wrapper marker-box">
                  <div v-for="item in markerList" :key="item.label">
                    <span style="width:9px; height:9px;" :class="item.className"></span>
                    <span style="margin-right: 10px">{{ item.label }}:{{ dw[item.key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 复制到 -->
    <el-dialog
      title="复制到"
      :visible.sync="showCopyDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      width="600px"
    >
      <p>
        <span style="margin-right: 48px">已选：{{ currentDate }}</span>
      </p>
      <el-date-picker
        type="dates"
        v-model="copyDateVal"
        placeholder="选择日期"
        size="small"
        :default-value="dayValue"
        :picker-options="pickerOptions"
        popper-class="el-picker-box"
      ></el-date-picker>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showCopyDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="handleCopy">确 定</el-button>
      </span>
    </el-dialog>
    <menu-preview-dialog
      v-if="menuPreviewDialogVisible"
      :isshow.sync="menuPreviewDialogVisible"
      :formDataDialog="dialogMenuPeviewInfo"
      width="900px"
      ref="menuPreviewDialog"
    />
    <select-model-dialog
      v-if="selectModelDialogVisible"
      :isshow.sync="selectModelDialogVisible"
      :formDataDialog="selectModelDialogInfo"
      @clickSelect="clickSelect"
      width="900px"
      ref="selectModelDialog"
    />
  </div>
</template>

<script>
import * as dayjs from 'dayjs'
import menuPreviewDialog from '../components/menu/menuPreviewDialog'
import selectModelDialog from '../components/menu/selectModelDialog'
import { to } from '@/utils'
import { MEAL_TYPES } from '@/utils/constants'
// import EditMealFoods from '../components/editMealFoods.vue'
export default {
  name: 'AddMonthRecipes',
  components: {
    menuPreviewDialog,
    selectModelDialog
    // EditMealFoods
  },
  props: {
    menuType: {
      type: String,
      default: ''
    },
    menuId: {
      type: String,
      default: ''
    }
    // formDataDialog: {
    //   type: Object,
    //   default() {
    //     return {}
    //   }
    // }
  },
  data() {
    return {
      dayValue: "",
      pickerOptions: this.disabledDate(),
      isLoading: false,
      currentDate: '',
      copyDateVal: [],
      showCopyDialog: false,
      currentMonth: '',
      currentYear: '', // zrj+
      border: '1px solid #ebeef5',
      dayList: [],
      weekDays: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'],
      isNutritionGuidance: false,
      // 开始了 营养指导
      markerList: [
        {
          key: 'insufficientTotal',
          className: 'marker-primary',
          label: '不足'
        },
        {
          key: 'suitableTotal',
          className: 'marker-secondary',
          label: '适宜'
        },
        {
          key: 'overdoseTotal',
          className: 'marker-thridary',
          label: '过量'
        }
      ],
      menuPreviewDialogVisible: false,
      dialogMenuPeviewInfo: {}, // 菜谱预览
      selectModelDialogVisible: false,
      selectModelDialogInfo: {}
    }
  },

  mounted() {
    this.initLoad()
  },

  methods: {
    disabledDate() {
      let that = this;
      return {
        disabledDate(time) {
          let month = new Date(that.dayValue).getMonth()
          let timeMonth = new Date(time).getMonth()
          return month !== timeMonth
        }
      };
    },
    mealTypeName(type) {
      let name = ''
      MEAL_TYPES.forEach(v => {
        if (v.value === type) {
          name = v.label
        }
      })
      return name
    },
    initLoad() {
      this.initDayList()
    },
    clickMenuPreview() {
      this.menuPreviewDialogVisible = true
    },
    switchGuidance(val) {
      // 先默认关闭
      this.selectModelDialogInfo = {
        menuType: this.menuType,
        menuId: this.menuId
      }
      this.isNutritionGuidance = false
      if (val) {
        this.selectModelDialogVisible = true
      } else {
        this.getModifyNutritionGuidance()
      }
    },
    //  关闭营养规则
    async getModifyNutritionGuidance() {
      let params = {
        id: this.menuId,
        operate: 0,
        menu_type: this.menuType
      }
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodMenuModifyNutritionGuidancePost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('关闭营养指导')
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化日期
    async initDayList() {
      const id = Number(this.$route.query.id)
      if (!id) {
        this.$message.error('id获取失败')
        return
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodMenuMonthlyMonthlyDetailPost({ id }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const resKeys = Object.keys(res.data.daily_data)
        // 这个月的天数
        const lastDayInMonth = dayjs(resKeys[0]).daysInMonth() // 31
        // zrj+
        this.currentMonth = dayjs(resKeys[0]).format('M')
        // 当前年月
        this.currentYear =
          dayjs(resKeys[0]).format('YYYY') + '年' + dayjs(resKeys[0]).format('MM') + '月'
        sessionStorage.setItem('mealDailyData', JSON.stringify(res.data.daily_data))
        let result = []
        // 这个月第一天是周几
        let time = resKeys[0]
        let monthStartInWeek = dayjs(dayjs(time).startOf('month')).day()
        if (monthStartInWeek === 0) {
          monthStartInWeek = 7
        }
        // 是否开启营养
        this.isNutritionGuidance = res.data.is_nutrition_guidance
        this.$emit('guidanceChange', this.isNutritionGuidance)

        for (let i = 1; i < monthStartInWeek; i++) {
          // 上个月的时间
          result.push('')
        }

        for (let i = 1; i <= lastDayInMonth; i++) {
          let dailyDataFoods = res.data.daily_data[resKeys[i - 1]].foods
          let insufficientTotal = 0
          let suitableTotal = 0
          let overdoseTotal = 0
          dailyDataFoods.forEach(item => {
            let totalNutrition = item.total_nutrition
            // 合计 不足适宜 过量
            for (const key in totalNutrition) {
              if (totalNutrition[key] >= 120) {
                overdoseTotal += 1
              } else if (totalNutrition[key] >= 80) {
                suitableTotal += 1
              } else if (totalNutrition[key] < 80) {
                insufficientTotal += 1
              }
            }
          })
          result.push({
            dayNumber: i < 10 ? `0${i}` : i,
            data: res.data.daily_data[resKeys[i - 1]],
            insufficientTotal,
            suitableTotal,
            overdoseTotal,
            menu_date: resKeys[i - 1]
          })
        }
        this.dayList = result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 打开复制当天菜单
    openCopyDialog(day) {
      if (this.currentYear !== '') {
        let dayString = this.currentYear
        dayString += `${day.dayNumber}日`
        this.currentDate = dayString
        this.copyDateVal = []
        this.dayValue = day.menu_date
      }
      this.showCopyDialog = true
    },

    // 打开编辑
    openEditDialog(data) {
      // return
      // 处理了日期 和之前一样
      let dateString = data.menu_date
      dateString = dateString.replace('年', '-').replace('月', '-')
      this.$router.push({
        name: 'MerchantAddMealMonthRecipes',
        query: {
          guidance: this.guidance,
          menuType: this.menuType,
          menuId: this.menuId,
          monthDate: dateString,
          isNutritionGuidance: this.isNutritionGuidance ? 'guidance' : 'false'
          // data: this.$encodeQuery(data.data)
        }
      })
    },
    // tab 栏点击事件
    tabClick(type) {
      this.foodMenuType = type
    },
    // 复制到某一天
    async handleCopy() {
      const params = {
        id: this.$route.query.id,
        copy_date: this.currentDate
          .replace('年', '-')
          .replace('月', '-')
          .replace('日', ''),
        dates: this.copyDateVal.map(date => {
          return dayjs(date).format('YYYY-MM-DD')
        })
      }
      const res = await this.$apis.apiBackgroundFoodMenuMonthlyCopyDayFoodPost(params)
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.showCopyDialog = false
        this.initDayList()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickSelect(data) {
      this.selectModelDialogVisible = false
      this.initLoad()
    },
    cateringFoodNumber(food) {
      let foodNumber = 0
      let mealNumber = 0
      if (food && food.length) {
        food.forEach(v => {
          // if (v.foods.length) {
          foodNumber += v.food_count
          // }
          // if (v.set_meal_data.length) {
          mealNumber += v.set_meal_count
          // }
        })
      }
      return foodNumber + mealNumber
    }
  }
}
</script>

<style lang="scss">
@import '../styles/addWeekRecipes.scss';
@import '../styles/addMonthRecipes.scss';
.meal-cell-wrapper-bg {
  background: #ebf3fe;
  padding-left: 10px;
  border-left: 2px solid #4385f7;
  border-radius: 3px;
  height: 80px;
}
.marker-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.el-picker-box{
  .el-picker-panel__icon-btn{
    display: none;
  }
  .el-date-picker__header{
    span:nth-child(3) { /*第三个标签是span的标签，把它隐藏*/
      display: none;
    }
  }
  .el-date-table td.selected span{
    color: #fff !important;
  }
}
</style>
