<template>
  <div class="TransferOrderDetail container-wrapper">
    <h3 class="m-t-20">仓库管理/调拨单/详情</h3>
    <div class="table-wrapper">
      <div id="print-box" class="p-20">
        <div>
          <p class="p">预计归还日期：{{ detailData.expected_return_date }}</p>
          <p class="p">借出仓库：{{ detailData.lend_warehouse_name }}</p>
          <p class="p">借入仓库：{{ detailData.borrow_warehouse_name }}</p>
        </div>
        <div class="">
          <div class="m-b-10">归还物资</div>
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 70%"
            stripe
            size="small"
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item"></table-column>
          </el-table>
          <!-- table end -->
        </div>
        <div class="m-t-30">
          <div class="m-b-10">{{ detailData.transfer_type === 'lend' ? '借出物资' : '借入物资' }}</div>
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="returnTableData"
            ref="tableData"
            style="width: 70%"
            stripe
            size="small"
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in returnTableSettings" :key="item.key" :col="item"></table-column>
          </el-table>
          <!-- table end -->
        </div>
        <div class="footer">
          <p class="p">单据备注：{{ detailData.remark || '--' }}</p>
          <div class="img-box">
            <span class="img-box-label">附件：</span>
            <span v-if="detailData.extra && detailData.extra.images.length > 0">
              <el-image
                v-for="(img, index) in detailData.extra.images"
                :key="img"
                :preview-src-list="detailData.extra.images"
                :initial-index="index"
                class="file-img m-r-6"
                :src="img"
                fit="contain"
              ></el-image>
            </span>
            <span v-else>空</span>
          </div>
        </div>
      </div>
      <div class="m-l-20 p-t-20 p-b-20">
        <el-button class="ps-btn" @click="backHandle">返回</el-button>
        <el-button v-if="detailData.transfer_type === 'borrow' && detailData.transfer_status === 'pend_confirm'" class="ps-btn" @click="clickOperationHandle('receipt')">确认收货</el-button>
        <el-button class="ps-btn" @click="handleExport">导出</el-button>
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入

export default {
  name: 'TransferOrderDetail',
  mixins: [exportExcel],
  directives: {
    print
  },
  components: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      warehouseId: '',
      detailData: {},
      tableData: [],
      tableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '物资分类', key: 'materail_classification_name' },
        { label: '调拨数量', key: 'count' },
        { label: '最小单位', key: 'unit_name' }
      ],
      returnTableData: [],
      returnTableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '物资分类', key: 'materail_classification_name' },
        { label: '调拨数量', key: 'count' },
        { label: '最小单位', key: 'unit_name' }
      ]
    }
  },
  created() {
    this.warehouseId = this.$route.query.warehouse_id
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    async initLoad() {
      this.getTransferDetail()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取调拨单详情数据
    async getTransferDetail() {
      const res = await this.$apis.apiBackgroundDrpTransferInfoDetailsPost({
        id: this.$route.query.id,
        warehouse_id: this.$route.query.warehouse_id
      })
      if (res.code === 0) {
        this.detailData = res.data
        this.tableData = res.data.return_info || []
        this.returnTableData = res.data.materials_info || []
      } else {
        this.$message.error(res.msg)
      }
    },
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = 'apiBackgroundDrpPurchaseInfoSupplierPurchaseModifyPost'
      let params = {}
      switch (type) {
        case 'refuse':
          params = {
            id: data.id,
            supplier_refuse: true
          }
          title = '确定拒收订单吗？'
          break
        case 'receipt':
          apiUrl = 'apiBackgroundDrpTransferInfoConfirmReceiptPost'
          params = {
            id: this.detailData.id,
            warehouse_id: this.detailData.source_warehouse
          }
          title = '确定收货吗？'
          break
      }
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg || '成功')
              this.backHandle()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    gotoHandle(type, row) {
      this.$router.push({
        name: 'ModifyPurchaseOrder',
        query: {
          type,
          id: row.id
        }
      })
    },
    handleExport() {
      const option = {
        type: 'TransferOrderDetail',
        url: 'apiBackgroundDrpTransferInfoDetailsExportPost',
        params: {
          id: this.detailData.id,
          warehouse_id: this.$route.query.warehouse_id
        }
      }
      this.exportHandle(option)
    },
    backHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'TransferOrder')
    }
  }
}
</script>

<style lang="scss">
.TransferOrderDetail {
  width: 100%;
  .w-200 {
    width: 200px !important;
  }
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
  table {
    width: 100% !important;
    font-weight: 500;
    th {
      font-weight: 500;
    }
  }
  .file-img {
    width: 100px;
    height: 100px;
  }
  .img-box {
    margin-top: 20px;
    vertical-align: top;
    .img-box-label {
      vertical-align: top;
    }
  }
  #print-box {
    width: 100%;
    div {
      // width: 100%;
    }
    .el-table thead,
    .ps-table-header-row {
      width: 100%;
    }
    .footer {
      p {
        margin: 10px 0 0;
      }
    }
  }
}
</style>
