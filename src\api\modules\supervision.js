import http from '@/utils/request'
export default {

  /**
   * ['晨检明细']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailPost(param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/morning_check_detail', param)
  },

  /**
   * ['晨检明细导出']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenManagementMorningCheckDetailExportPost(param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/morning_check_detail_export', param)
  },

  /**
   * ['晨检汇总']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectPost(param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/morning_check_collect', param)
  },

  /**
   * ['晨检汇总导出']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenManagementMorningCheckCollectExportPost(param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/morning_check_collect_export', param)
  },
  /**
   * ['智能预警 预警配置']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionWarnManageBusinessWarnListPost(param) {
    return http.post('/api/background_fund_supervision/warn_manage/business_warn_list', param)
  },

  /**
   * ['智能预警 预警配置']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionWarnManageBusinessWarningDetailPost(param) {
    return http.post('/api/background_fund_supervision/warn_manage/business_warning_detail', param)
  },
  /**
   * ['智能预警 预警配置修改']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionWarnManageBusinessWarningEditPost(param) {
    return http.post('/api/background_fund_supervision/warn_manage/business_warning_edit', param)
  },
  /**
   * ['人员排班列表 修改']
   * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
   * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
   */
  apiBackgroundFundSupervisionCanteenSafetyManagementModifyPersonSchedulePost(param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/modify_person_schedule', param)
  },

  /**
 * ['人员排班列表']
 * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
 * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
 */
  apiBackgroundFundSupervisionCanteenSafetyManagementGetPersonSchedulePost(param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/get_person_schedule', param)
  },
  /**
 * ['历史记录接口']
 * @param {{page:number, page_size:number, update_cache:boolean}} param page Page,page_size Page size,update_cache 刷新缓存
 * @returns {{code:number, msg:string, data:ListChartLibAuth}} - rsp
 */
  apiBackgroundFundSupervisionCanteenSafetyManagementGetScheduleHistoryPost(param) {
    return http.post('/api/background_fund_supervision/canteen_safety_management/get_schedule_history', param)
  },
  // 留样记录
  apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordPost(data) {
    return http.post(
      "/api/background_store/retention_record/food_reserved_sample_record",
      data
    )
  },
  // 留样记录导出
  apiBackgroundFundSupervisionChannelCanteenManagementFoodReservedSampleRecordExportPost(data) {
    return http.post(
      "/api/background_store/retention_record/food_reserved_sample_record_export",
      data
    )
  },
  // 获取操作人员人脸信息
  apiBackgroundFundSupervisionChannelCanteenManagementGetAccountFaceUrlPost(data) {
    return http.post(
      "/api/background_store/retention_record/get_account_face_url",
      data
    )
  },
  // 更新未入柜原因
  apiBackgroundFundSupervisionChannelCanteenManagementModifyNotEntryReasonPost(data) {
    return http.post(
      "/api/background_store/retention_record/modify_not_entry_reason",
      data
    )
  },
  // 更新未留样原因
  apiBackgroundFundSupervisionChannelCanteenManagementModifyNotReservedReasonPost(data) {
    return http.post(
      "/api/background_store/retention_record/modify_not_reserved_reason",
      data
    )
  },
  // 留样获取菜谱
  apiBackgroundStoreRetentionRecordMenuList(data) {
    return http.post(
      "/api/background_store/retention_record/menu_list",
      data
    )
  },

  // 陪餐记录
  apiBackgroundFundSupervisionChannelCanteenManagementMealAccompanyingListPost(data) {
    return http.post(
      "/api/background_fund_supervision/channel_canteen_management/meal_accompanying_list",
      data
    )
  },
  // 陪餐记录 导出
  apiBackgroundFundSupervisionChannelCanteenManagementMealAccompanyingListExportPost(data) {
    return http.post(
      "/api/background_fund_supervision/channel_canteen_management/meal_accompanying_list_export",
      data
    )
  },
  // 每日巡查数据列表
  apiBackgroundFundSupervisionDailyPatrolList(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/list",
      data
    )
  },
  // 获取项目配置列表
  apiBackgroundFundSupervisionDailyPatrolGetProjectList(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/get_project_list",
      data
    )
  },
  // 配置项目
  apiBackgroundFundSupervisionDailyPatrolProjectSettings(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/project_settings",
      data
    )
  },
  // 添加巡查记录
  apiBackgroundFundSupervisionDailyPatrolAdd(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/add",
      data
    )
  },
  // 删除巡查记录
  apiBackgroundFundSupervisionDailyPatrolDelete(data) {
    return http.post(
      "/api/background_fund_supervision/daily_patrol/delete",
      data
    )
  },
  // 台账权限 保存
  apiBackgroundFundSupervisionLedgerLedgerPermissionBulkSavePost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/ledger_permission_bulk_save",
      data
    )
  },
  // 台账权限 删除
  apiBackgroundFundSupervisionLedgerLedgerPermissionDeletePost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/ledger_permission_delete",
      data
    )
  },
  // 台账权限 列表
  apiBackgroundFundSupervisionLedgerLedgerPermissionListPost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/ledger_permission_list",
      data
    )
  },
  // 台账权限 列表 保存
  apiBackgroundFundSupervisionLedgerLedgerPermissionSavePost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/ledger_permission_save",
      data
    )
  },
  // 台账复核 列表
  apiBackgroundFundSupervisionLedgerLedgerReviewListPost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/ledger_review_list",
      data
    )
  },
  // 台账复核 列表 状态更新
  apiBackgroundFundSupervisionLedgerLedgerReviewStatusUpdatePost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/ledger_review_status_update",
      data
    )
  },
  // 台账类型 列表
  apiBackgroundFundSupervisionLedgerLedgerTypeListPost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/ledger_type_list",
      data
    )
  },

  // 台账类型 风险管控 列表
  apiBackgroundFundSupervisionLedgerRiskControlListPost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/risk_control_list",
      data
    )
  },

  // 台账类型 风险管控 新增
  apiBackgroundFundSupervisionLedgerRiskControlAddPost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/risk_control_add",
      data
    )
  },

  // 台账类型 导出
  apiBackgroundFundSupervisionLedgerRiskControlExportPost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/risk_control_export",
      data
    )
  },
  // 厨房抽烟机清洗表
  apiBackgroundFundSupervisionLedgerGetKitchenRangeHoodCleanPost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/get_kitchen_range_hood_clean",
      data
    )
  },
  // 厨房抽烟机清洗表 导出
  apiBackgroundFundSupervisionLedgerKitchenRangeHoodCleanExportPost(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/kitchen_range_hood_clean_export",
      data
    )
  },
  // 食品留样记录表
  apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodReservedSampleLedger(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_food_reserved_sample_ledger",
      data
    )
  },
  // 食品留样记录表 导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodReservedSampleLedgerExport(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_food_reserved_sample_ledger_export",
      data
    )
  },

  // 学校每周食品安全排查治理报告
  apiBackgroundFundSupervisionLedgerFoodSafetyGetWeeklyFoodSafetyReportLedger(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_weekly_food_safety_report_ledger",
      data
    )
  },
  // 学校每周食品安全排查治理报告 导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetWeeklyFoodSafetyReportLedgerExport(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_weekly_food_safety_report_ledger_export",
      data
    )
  },

  // 巡检记录表
  apiBackgroundFundSupervisionLedgerFoodSafetyGetInspectionRecordLedger(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_inspection_record_ledger",
      data
    )
  },

  // 巡检记录表 导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetInspectionRecordLedgerExport(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_inspection_record_ledger_export",
      data
    )
  },
  // 获取过敏原使用记录台账
  apiBackgroundFundSupervisionLedgerFoodSafetyGetAllergenUseRecordLedger(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_allergen_use_record_ledger",
      data
    )
  },
  // 获取过敏原使用记录台账 导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetAllergenUseRecordLedgerExport(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_allergen_use_record_ledger_export",
      data
    )
  },
  // 获取过敏原配置
  apiBackgroundFundSupervisionLedgerFoodSafetyGetLedgerItemConfig(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_ledger_item_config",
      data
    )
  },
  // 修改台账配置项 修改
  apiBackgroundFundSupervisionLedgerFoodSafetyModifyLedgerItemConfig(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/modify_ledger_item_config",
      data
    )
  },
  // 获取食品添加剂项
  apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodAdditive(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_food_additive",
      data
    )
  },
  // 获取食品添加剂使用记录台账
  apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodAdditiveUseLedger(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_food_additive_use_ledger",
      data
    )
  },
  // 获取食品添加剂使用记录台账 导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodAdditiveUseLedgerExport(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_food_additive_use_ledger_export",
      data
    )
  },
  // 台账通用历史记录
  /**
    ledger_data_type如下：
    Disease：“疾病特征”
    Egg“蛋类名称”
    Allergen “过敏原”
    Additive “添加剂名称”
    Inspection“巡检项目”
    WipingAgent“擦拭剂”
    Sanitizer“消毒剂”
    Tool"工具名称”
    CookingUtensils“餐炊具名称”
    DeliveryUnit：“配送单位信息”
   */
  apiBackgroundFundSupervisionLedgerFoodSafetyOperateLogList(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/operate_log_list",
      data
    )
  },
  // 通用获取台账详情
  apiBackgroundFundSupervisionLedgerFoodSafetyLedgerReviewDetail(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/ledger_review_detail",
      data
    )
  },
  // 获取从业人员晨检表
  apiBackgroundFundSupervisionLedgerFoodSafetyGetEmployeesMorningCheckLedger(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_morning_check_ledger",
      data
    )
  },
  // 获取从业人员晨检表 导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetMorningCheckLedgerExport(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_morning_check_ledger_export",
      data
    )
  },
  // 获取晨检风险项
  apiBackgroundFundSupervisionLedgerFoodSafetyGetRiskFeature(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_risk_feature",
      data
    )
  },
  // 集体用餐配送单位配送记录
  apiBackgroundFundSupervisionLedgerFoodSafetyGetCollectiveDiningDeliveryRecord(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_collective_dining_delivery_record",
      data
    )
  },
  // 集体用餐配送单位配送记录- 导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetCollectiveDiningDeliveryRecordExport(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_collective_dining_delivery_record_export",
      data
    )
  },
  // 台账复核详情
  apiBackgroundFundSupervisionLedgerFoodSafetyGetLedgerReviewDetail(data) {
    return http.post(
      "/api/background_fund_supervision/ledger/ledger_review_detail",
      data
    )
  },
  // 获取当前排班人员
  apiBackgroundFundSupervisionLedgerFoodSafetyGetTodayPersonSchedule(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_today_person_schedule",
      data
    )
  },
  // 学校校园食品安全工作管理清单-列表
  apiBackgroundFundSupervisionLedgerFoodSafetyGetSchoolFoodSafetyManagement_checklist(data) {
    return http.post(
      "/api/background_fund_supervision/ledger_food_safety/get_school_food_safety_management_checklist",
      data
    )
  },
  // 从业人员食品安全培训记录
  apiBackgroundFundSupervisionLedgerFoodSafetyGetEmployeeFoodSafetyTrainingRecordPost(data) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_employee_food_safety_training_record', data)
  },
  // 从业人员食品安全培训记录导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetEmployeeFoodSafetyTrainingRecordExportPost(param) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_employee_food_safety_training_record_export', param)
  },
  /**
  * ['商户后台 台账管理'] 设备维护清洗记录
  * background_fund_supervision.ledger_food_safety.get_collective_dining_delivery_record_export 从业人员食品安全培训记录导出
  */
  apiBackgroundFundSupervisionLedgerFoodSafetyGetDeviceMaintenanceCleaningRecord(param) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_device_maintenance_cleaning_record', param)
  },
  /**
  * ['商户后台 台账管理'] 设备维护清洗记录 导出
  * background_fund_supervision.ledger_food_safety.get_collective_dining_delivery_record_export 从业人员食品安全培训记录导出
  */
  apiBackgroundFundSupervisionLedgerFoodSafetyGetDeviceMaintenanceCleaningRecordExport(param) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_device_maintenance_cleaning_record_export', param)
  },
  /**
  * ['商户后台 台账管理'] 学校食堂刀具消毒明细管理台账
  * background_fund_supervision.ledger_food_safety.get_collective_dining_delivery_record_export 从业人员食品安全培训记录导出
  */
  apiBackgroundFundSupervisionLedgerFoodSafetyGetKnifeManagementRecord(param) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_kitchen_knife_management', param)
  },
  /**
  * ['商户后台 台账管理'] 学校食堂刀具消毒明细管理台账 导出
  * background_fund_supervision.ledger_food_safety.get_collective_dining_delivery_record_export 从业人员食品安全培训记录导出
  */
  apiBackgroundFundSupervisionLedgerFoodSafetyGetKnifeManagementRecordExport(param) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_kitchen_knife_management_export', param)
  },
  // 团餐公司列表
  apiBackgroundAdminGroupMealCompanyList(data) {
    return http.post(
      "/api/background/admin/group_meal_company/list",
      data
    )
  },
  // 修改团餐公司名称
  apiBackgroundAdminGroupMealCompanyModify(data) {
    return http.post(
      "/api/background/admin/group_meal_company/modify",
      data
    )
  },
  // 团餐公司详情
  apiBackgroundAdminGroupMealCompanyDetails(data) {
    return http.post(
      "/api/background/admin/group_meal_company/details",
      data
    )
  },
  // 配餐公司列表
  apiBackgroundAdminCateringCompanyList(data) {
    return http.post(
      "/api/background/admin/catering_company/list",
      data
    )
  },
  // 修改配餐公司名称
  apiBackgroundAdminCateringCompanyModify(data) {
    return http.post(
      "/api/background/admin/catering_company/modify",
      data
    )
  },
  // 配餐公司详情
  apiBackgroundAdminCateringCompanyDetails(data) {
    return http.post(
      "/api/background/admin/catering_company/details",
      data
    )
  },
  // 项目验收
  apiBackgroundAdminBackgroundAcceptanceListPost(data) {
    return http.post(
      "/api/background/admin/background_acceptance/list",
      data
    )
  },
  // 项目验收导出
  apiBackgroundAdminBackgroundAcceptanceListExportPost(data) {
    return http.post(
      "/api/background/admin/background_acceptance/list_export",
      data
    )
  },
  // 食堂餐厨废弃物处置记录表
  apiBackgroundFundSupervisionLedgerFoodSafetyGetKitchenWasteDisposalRecordPost(param) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_kitchen_waste_disposal_record', param)
  },
  // 食堂餐厨废弃物处置记录表导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetKitchenWasteDisposalRecordExportPost(param) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_kitchen_waste_disposal_record_export', param)
  },
  // 蛋类清洗记录表
  apiBackgroundFundSupervisionLedgerFoodSafetyGetEggCleaningRecord(param) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_egg_cleaning_record', param)
  },
  // 蛋类清洗记录表导出
  apiBackgroundFundSupervisionLedgerFoodSafetyGetEggCleaningRecordExport(param) {
    return http.post('/api/background_fund_supervision/ledger_food_safety/get_egg_cleaning_record_export', param)
  }
}
