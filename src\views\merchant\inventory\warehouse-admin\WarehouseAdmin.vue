<template>
  <div class="warehouse-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" v-permission="['background_drp.unit_management.list']" @click="gotoUnitAdmin">单位管理</button-icon>
          <button-icon color="origin" v-permission="['background_drp.warehouse.add']" @click="showDialogHandle('add')">新增仓库</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- content start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button v-permission="['background_drp.inventory_info.list']"  type="text" class="ps-text" size="small" :disabled="organization != row.organization_id" @click="gotoInventory(row)">进入仓库</el-button>
              <el-button v-permission="['background_drp.warehouse.modify']" type="text" class="ps-text" size="small" :disabled="organization != row.organization_id" @click="showDialogHandle('modify', row)">编辑</el-button>
              <!-- <el-button v-permission="['background_drp.inquiry.list']" type="text" size="small" class="ps-text" :disabled="organization != row.organization_id" @click="gotoHandle('InquiryOrder', row)">询价单</el-button>
              <el-button v-permission="['background_drp.purchase_info.list']" type="text" size="small" class="ps-text" :disabled="organization != row.organization_id" @click="gotoHandle('PurchaseOrderList', row)">采购单</el-button>
              <el-button v-permission="['background_drp.transfer_info.list']" type="text" size="small" class="ps-text" :disabled="organization != row.organization_id" @click="gotoHandle('TransferOrder', row)">调拨单</el-button> -->
              <el-button v-permission="['background_drp.material_inventory.list']" type="text" size="small" class="ps-text" :disabled="organization != row.organization_id" @click="gotoHandle('DocumentManagement', row)">单据管理</el-button>
              <el-button v-permission="['background_drp.material_inventory.list']" type="text" size="small" class="ps-text" :disabled="organization != row.organization_id" @click="gotoHandle('InventoryStock', row)">盘点</el-button>
              <!-- <el-button v-permission="['background_drp.exit_info.list']" type="text" size="small" class="ps-text" :disabled="organization != row.organization_id" @click="gotoOutboundHandle(row)">出库单</el-button>
              <el-button v-permission="['background_drp.entry_info.list']" type="text" size="small" class="ps-text" :disabled="organization != row.organization_id" @click="gotoEntryHandle(row)">入库单</el-button> -->
            </template>
          </table-column>
        </el-table>
        <!-- content end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <warehouse-dialog :show="showDialog" :info-data="dialogData" :dialog-loading="dialogLoading" :type="dialogType" :closehandle="closeDialogHandle" :confirmhandle="clickDialogConfirm"></warehouse-dialog>
    <!-- end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import WarehouseDialog from '../components/WarehouseDialog'
import { mapGetters } from 'vuex'

export default {
  name: 'WarehouseAdmin',
  mixins: [exportExcel],
  components: { WarehouseDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: {
        name: {
          type: 'input',
          value: '',
          label: '仓库名称',
          placeholder: '请输入仓库名称'
        },
        status: {
          type: 'select',
          value: 'enable',
          label: '仓库状态',
          placeholder: '请选择仓库状态',
          dataList: [
            {
              label: '使用',
              value: 'enable'
            },
            {
              label: '禁用',
              value: 'disable'
            }
          ]
        }
        // org_ids: {
        //   type: 'organizationSelect',
        //   value: [],
        //   label: '组织',
        //   checkStrictly: true,
        //   isLazy: false,
        //   clearable: true,
        //   multiple: true
        // }
      },
      showDialog: false, // 是否开启弹窗
      dialogLoading: false, // 弹窗loading
      dialogType: '', // 弹窗的状态，add/modify
      dialogData: {}, // 弹窗数据
      tableData: [],
      tableSettings: [
        { label: '仓库名称', key: 'name' },
        { label: '物资种类', key: 'variety' },
        { label: '库存不足', key: 'deficiency' },
        { label: '库存过多', key: 'nimiety' },
        { label: '临期种类', key: 'near_expired' },
        { label: '仓库状态', key: 'alias_status' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", minWidth: '120px' }
      ]
    }
  },
  computed: {
    ...mapGetters(['organization'])
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getWarehouseList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getWarehouseList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpWarehouseListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data) {
          this.totalCount = res.data.count
          // 处理下后端返回的数据
          this.tableData = res.data.results.map(v => {
            if (v.get_varietys_info) {
              for (let key in v.get_varietys_info) {
                v[key] = v.get_varietys_info[key]
              }
            }
            v.alias_status = v.status === 'enable' ? '使用' : '禁用'
            return v
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getWarehouseList()
    },
    // 显示弹窗
    showDialogHandle(type, data) {
      this.dialogType = type
      if (data) {
        this.dialogData = data
      }
      this.showDialog = true
    },
    // 关闭弹窗
    closeDialogHandle() {
      this.showDialog = false
      this.dialogData = {}
    },
    // 弹窗确定回调
    clickDialogConfirm() {
      this.showDialog = false
      this.getWarehouseList()
    },
    // 跳转单位管理页面
    gotoUnitAdmin() {
      this.$router.push({
        name: 'InventoryUnitAdmin',
        query: {}
      })
    },
    // 跳转库存管理页面
    gotoInventory(row) {
      this.$router.push({
        name: 'InventoryManagement',
        query: {
          warehouse_id: row.id,
          warehouse_name: row.name
        }
      })
    },
    gotoInventoryStock(row) {
      this.$router.push({
        name: 'InventoryStock',
        query: {
          warehouse_id: row.id,
          warehouse_name: row.name
        }
      })
    },
    gotoEntryHandle(row) {
      this.$router.push({
        name: 'InboundOrder',
        query: {
          warehouse_id: row.id,
          warehouse_name: row.name
        }
      })
    },
    gotoOutboundHandle(row) {
      this.$router.push({
        name: 'OutboundOrder',
        query: {
          warehouse_id: row.id,
          warehouse_name: row.name
        }
      })
    },
    // 跳转页面
    gotoHandle(type, row) {
      this.$router.push({
        name: type,
        query: {
          warehouse_id: row.id,
          warehouse_name: row.name
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.warehouse-wrapper {
  .no-pointer{
    cursor: default;
    opacity: 1;
  }
  .ps-pagination{
    padding-top: 0px !important;
  }
}
</style>
