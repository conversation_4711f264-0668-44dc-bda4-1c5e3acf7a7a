<template>
  <div class="add-banner-wrapper">
    <add-banner :type="type" />
  </div>
</template>

<script>
import AddBanner from '@/views/public/banner/add'
export default {
  name: 'SuperBanner',
  components: {
    AddBanner
  },
  data() {
    return {
      type: 'super'
    }
  },
  created() {
    // this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
    }
  }
}
</script>

<style lang="scss" scoped>
.add-banner-wrapper{
}
</style>
