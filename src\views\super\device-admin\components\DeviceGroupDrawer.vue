<template>
  <custom-drawer
    :title="title"
    :show.sync="visible"
    v-bind="$attrs"
    v-on="$listeners"
    @close="handlerClose"
    @cancel="handlerClose"
    @confirm="clickConfirmHandle"
  >
    <el-form
      :model="formData"
      ref="formRef"
      :rules="formDataRules"
      label-width="120px"
      class="material-dialog"
      v-loading="isLoading"
    >
      <el-form-item label="所属项目" prop="company_id">
        <el-select
          v-model="formData.company_id"
          placeholder="请下拉选择"
          class="ps-select w-250"
          popper-class="ps-popper-select"
          filterable
        >
          <el-option
            v-for="item in companyList"
            :key="item.company"
            :label="item.name"
            :value="item.company"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备组名称：" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入设备组名称"
          class="ps-input w-250"
          maxlength="20"
        ></el-input>
      </el-form-item>
      <el-form-item label="VOIP组ID：" prop="group_id">
        <el-input v-model="formData.group_id" placeholder="请输入VOIP组ID" class="ps-input w-250"></el-input>
      </el-form-item>
      <el-form-item label="备注：" prop="remark">
        <el-input
          type="textarea"
          :rows="4"
          class="ps-input w-250"
          maxlength="100"
          placeholder="请输入，不超过100字"
          v-model="formData.remark"
        ></el-input>
      </el-form-item>
    </el-form>
  </custom-drawer>
</template>

<script>
export default {
  name: 'DeviceGroupDrawer',
  components: {},
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '500px'
    },
    isshow: Boolean,
    drawerData: {
      type: Object,
      default: () => {}
    },
    companyList: {
      type: Array,
      default: () => []
    }
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      formData: {
        company_id: '',
        name: '',
        group_id: '',
        remark: ''
      },
      formDataRules: {
        company_id: [{ required: true, message: '请选择所属项目', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '请输入设备组名称', trigger: ['blur', 'change'] }],
        group_id: [{ required: true, message: '请输入VOIP组ID：', trigger: ['blur', 'change'] }],
        remark: [{ required: false, message: '请选择备注', trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initLoad()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    initLoad() {
      this.formData = {
        company_id: '',
        name: '',
        group_id: '',
        remark: ''
      }
      if (this.type === 'modify') {
        this.$nextTick(() => {
          this.formData = {
            id: this.drawerData.id,
            company_id: this.drawerData.company,
            name: this.drawerData.name,
            group_id: this.drawerData.group_id,
            remark: this.drawerData.remark
          }
        })
      }
      console.log(this.formData)
    },
    clickConfirmHandle(isWithdrawal) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (this.type === 'add') {
            this.confirmAdd(this.formData)
          } else {
            this.modifyAddress(this.formData)
          }
        }
      })
    },
    async confirmAdd(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceAddDeviceGroupPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$emit('clickConfirm', this.type)
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    async modifyAddress(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceModifyDeviceGroupPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$emit('clickConfirm', this.type)
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    handlerClose(e) {
      this.isLoading = false
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
      this.visible = false
    }
  }
}
</script>

<style lang="scss"></style>
