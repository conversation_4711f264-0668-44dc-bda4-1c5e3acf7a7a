<template>
  <div class="consumptionrules-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
    >
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addNewConsumptionHandle" v-permission="['background_marketing.consume.add']">
            新增消费规则
          </button-icon>
          <!-- <button-icon color="plain" type="mul">分类管理</button-icon>
          <button-icon color="plain" type="export">导出EXCEL</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column prop="name" label="名称" align="center"></el-table-column>
          <el-table-column
            prop="org_name"
            label="规则所属"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop=""
            label="适用分组"
            align="center"
          >
          <template slot-scope="scope">
              {{ scope.row.rule_info.group_names ? Object.keys(scope.row.rule_info.group_names).join(',') : '' }}
            </template>
          </el-table-column>
          <el-table-column
            prop=""
            label="适用消费点"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.rule_info.org_names ? Object.keys(scope.row.rule_info.org_names).join(',') : '' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="wallet_type"
            label="支付限制"
            align="center"
          >
            <template slot-scope="scope">
              {{ formatWallet(scope.row.wallet_type, scope.row.payway) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="consume_type"
            label="消费类型"
            align="center"
          >
          <template slot-scope="scope">
            {{ scope.row.consume_type ? '扣费' : '计次' }}
          </template>
          </el-table-column>
          <el-table-column
            prop="tip"
            label="备注说明"
            align="center"
          ></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <!-- eslint-disable-next-line vue/no-unused-vars -->
            <template slot-scope="scope">
              <el-button type="text" size="small" class="ps-text" @click="goToConsumptionDetail(scope.row)" v-permission="['background_marketing.consume.modify']">编辑</el-button>
              <el-button type="text" size="small" class="ps-warn"  @click="deleteConsume(scope.row)" v-permission="['background_marketing.consume.delete']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import { WALLET_LIST } from "./constants"

export default {
  name: 'ingredientsAdmin',
  components: {
  },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '名称',
          value: '',
          placeholder: '请输入消费规则名称'
        },
        user_groups: {
          type: 'groupSelect',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          multiple: true,
          collapseTags: true
        },
        org_nos: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          dataList: [],
          multiple: true,
          collapseTags: true,
          checkStrictly: true
        }
      },
      consumeType: {
        JC: '记次',
        WZK: '扣费-无折扣',
        GD: '扣费-固定',
        ZK: '扣费-折扣'
      },
      paywayList: {}
    }
  },
  watch: {
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      await this.getPaywayList()
      this.getConsumeList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getConsumeList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 获取消费规则列表
    async getConsumeList() {
      this.isLoading = true
      let params = {
        // name: this.searchFormSetting.name.value,
        // group_no: this.searchFormSetting.user_groups.value,
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (this.searchFormSetting.name.value) {
        params.name = this.searchFormSetting.name.value
      }
      if (this.searchFormSetting.user_groups.value.length) {
        params.group_nos = this.searchFormSetting.user_groups.value
      }
      if (this.searchFormSetting.org_nos.value.length) {
        params.org_nos = this.searchFormSetting.org_nos.value
      }
      const [err, res] = await to(this.$apis.apiBackgroundMarketingConsumeListPost(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log(res)
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    formatWallet(data, payway) {
      let str = ''
      WALLET_LIST.forEach(item => {
        if (data.includes(item.key)) {
          if (str) {
            str += ',' + item.name
          } else {
            str = item.name
          }
        }
      })
      if (payway && payway instanceof Array) {
        payway.forEach(v => {
          var strPayWay = ''
          for (let key in this.paywayList) {
            if (key === v) {
              strPayWay = this.paywayList[key] || ''
              break;
            }
          }
          if (str && strPayWay) {
            str += ',' + strPayWay
          } else if (strPayWay) {
            str = strPayWay
          }
        })
      }
      return str
    },
    // 删除
    deleteConsume(data) {
      this.$confirm(`确定删除吗?`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            // await this.$sleep(2222)
            const [err, res] = await to(
              this.$apis.apiBackgroundMarketingConsumeDeletePost({
                rule_no: data.rule_no
              })
            )
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getConsumeList()
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getConsumeList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getConsumeList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    addNewConsumptionHandle() {
      this.$router.push({
        name: 'MerchantConsumptionRulesForm',
        params: {
          type: 'add'
        }
      })
    },
    // detail
    goToConsumptionDetail(data) {
      this.$router.push({
        name: 'MerchantConsumptionRulesForm',
        params: {
          type: 'modify'
        },
        query: {
          data: encodeURIComponent(JSON.stringify(data))
        }
      })
    },
    async getPaywayList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundPaymentPayInfoGetCompanyPayinfoPost({
        company_id: this.$store.getters.userInfo.company_id
      }));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.forEach(v => {
          this.paywayList[v.payway] = v.name
        })
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss">
</style>
