@media print {
  // *{
  //   font-size: 12px;
  //   font-weight: 500;
  //   color: #909399;
  // }
  thead {
    display: table-header-group;
  }
  tfoot {
    display: table-footer-group;
  }
  body {
    margin: 0;
  }
  // 设置下table宽度
  table {
    width: 100% !important;
    // width: 676px !important;
    // border:1px solid red;
  }
  // 去除table固定宽度
  colgroup {
    col {
      width: auto !important;
    }
    col[name="gutter"] {
      display: none;
    }
  }
  @page {
    size: auto;
    margin-bottom: 0px;
  }
  .noprint {
    display: none;
  }
}