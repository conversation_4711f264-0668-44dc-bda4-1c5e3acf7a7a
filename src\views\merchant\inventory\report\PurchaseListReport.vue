<template>
  <div class="PurchaseListDetails container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon v-permission="['background_drp.purchase_info.details_list_export']" color="origin" @click="handleExport">导出数据</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          row-key="id"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item"></table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right; ">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :pageSizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, getSevenDateRange } from '@/utils'

export default {
  name: 'PurchaseListReport',
  components: {
  },
  mixins: [exportExcel],
  data() {
    return {
      importLink: '',
      supplierId: '',
      tableData: [],
      isLoading: false, // 刷新数据
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '采购时间',
          format: 'yyyy-MM-dd',
          clearable: false,
          value: getSevenDateRange(7)
        },
        materials_name: {
          type: 'input',
          value: '',
          label: '物资名称',
          placeholder: '请输入物资名称',
          maxlength: 20
        },
        warehouse_ids: {
          type: 'lazySelect',
          label: '仓库名称',
          clearable: true,
          value: [],
          apiUrl: 'apiBackgroundDrpWarehouseListPost',
          params: { status: 'enable' },
          isLazy: false,
          collapseTags: true,
          multiple: true
        },
        materail_classification_ids: {
          type: 'select',
          label: '物资分类',
          clearable: true,
          value: [],
          dataList: [],
          multiple: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id'
        }
      },
      tableSettings: [
        { label: '采购时间', key: 'purchase_time' },
        { label: '仓库名称', key: 'warehouse_name' },
        { label: '物资名称', key: 'materials_name' },
        { label: '物资分类', key: 'materail_classification_name' },
        { label: '采购数量', key: 'purchase_count' },
        { label: '单位', key: 'unit_name' }
      ],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getClassificationList()
      this.getTableDataList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getTableDataList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getTableDataList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDrpPurchaseInfoDetailsListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getTableDataList()
    },
    handleExport() {
      const option = {
        type: 'PurchaseListReport',
        url: 'apiBackgroundDrpPurchaseInfoDetailsListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: 1,
          page_size: 999999
        }
      }
      this.exportHandle(option)
    },
    // 获取物资分类列表
    async getClassificationList () {
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpMaterailClassificationListPost({
        page: 1,
        page_size: 9999
      }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        let data = res.data || []
        let results = data.results || []
        this.searchFormSetting.materail_classification_ids.dataList = results
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
