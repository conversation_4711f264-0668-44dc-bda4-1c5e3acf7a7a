<template>
  <div class="ApprovalManagementDetail container-wrapper">
    <div class="m-t-20">审批列表/查看</div>
    <div class="table-wrapper clearfix">
      <div class="timeline-box float-l w-300 m-l-20 m-t-20 m-b-20">
        <timeline :timeline="detailData.operator_record" class="p-10" />
      </div>
      <div class="content m-t-20">
        <!-- table start -->
        <div class="m-b-20">
          <span class="m-r-20">单据编号：{{ detailData.trade_no }}</span>
          <span class="m-r-20">单据类型：{{ detailData.approve_type_alias }}</span>
          <span>申请人：{{ detailData.account_name }}</span>
        </div>
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          size="small"
          max-height="700px"
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item"></table-column>
        </el-table>
        <!-- table end -->
        <div class="footer m-t-30">
          <el-button class="ps-btn" @click="backHandle">返回</el-button>
          <el-button v-if="detailData.display_operation_buttons" class="ps-btn" @click="clickOperationHandle('reject')">驳回</el-button>
          <el-button v-if="detailData.display_operation_buttons" class="ps-btn" @click="clickOperationHandle('agree')">同意</el-button>
          <el-button v-if="detailData.approve_type === 'subscribe_info' && detailData.approve_status === 'AGREE'" class="ps-btn" @click="showDialogHandle('to_purchase_info')">转采购单</el-button>
          <el-button v-if="detailData.approve_type === 'subscribe_info' && detailData.approve_status === 'AGREE'" class="ps-btn" @click="showDialogHandle('to_exit_info')">转出库单</el-button>
        </div>
      </div>
    </div>
    <dialog-message
      :show.sync="dialogVisible"
      title="选择仓库"
      :loading.sync="dialogLoading"
      width="360px"
      class="FormDialog"
      @close="handlerClose"
      @cancel="clickCancleHandle"
      @confirm="clickDialogConfirm"
    >
      <el-form ref="formRef" :model="dialogFormData" :rules="dialogFormRules" size="small">
        <el-form-item label="仓库名称" class="" prop="warehouse_id">
          <el-select v-model="dialogFormData.warehouse_id" class="ps-select w-280" popper-class="ps-popper-select" placeholder="请选择">
            <el-option v-for="option in warehouseList" :key="option.id" :label="option.name" :value="option.id" ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </dialog-message>
    <!-- dialog start -->
    <dialog-message :show.sync="showDialog" :title="dialogTitle" :loading.sync="dialogLoading" width="435px" :showFooter="showDialogFooter" @close="closeDialogHandle"
      @cancel="clickDialogCancleHandle" @confirm="clickConfirmHandle">
      <el-form ref="dialogFormRef" v-if="dialogType === 'reject'" :model="dialogForm" :rules="dialogrules" label-position="left" label-width="80px" size="medium">
        <el-form-item label="" label-width="0" prop="reason">
          <el-input v-model="dialogForm.reason" type="textarea" :rows="4" :maxlength="50" class="ps-input"></el-input>
        </el-form-item>
      </el-form>
      <timeline v-if="dialogType === 'record'" :timeline="timelineData" />
    </dialog-message>
    <!-- end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import Timeline from './Timeline'
import { APPROVAL_DETAIL_TABLESETTINGS } from '../constants'

export default {
  name: 'TransferOrder',
  mixins: [exportExcel],
  components: { Timeline },
  data() {
    return {
      isLoading: false, // 刷新数据
      detailData: {},
      tableData: [],
      tableSettings: APPROVAL_DETAIL_TABLESETTINGS.purchase_info,
      // 弹窗
      dialogVisible: false,
      dialogLoading: false,
      dialogFormData: {
        warehouse_id: ''
      },
      dialogFormRules: {
        warehouse_id: [{ required: true, message: '请选择仓库', trigger: 'change' }]
      },
      dialogFormType: '',
      warehouseList: [],
      // 弹窗
      showDialog: false,
      dialogLoading: false,
      dialogTitle: '驳回原因',
      dialogType: '',
      showDialogFooter: false,
      dialogForm: {
        id: '',
        reason: ''
      },
      dialogrules: {
        reason: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }]
      },
      timelineData: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.$route.query.currentPage) {
        this.currentPage = Number(this.$route.query.currentPage)
      }
      this.getApprovalDetail()
      this.getWarehouseList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.initLoad()
    }, 300),
    // 切换tab
    changeTabHandle(e) {
      this.searchHandle()
    },
    // 获取列表数据
    async getApprovalDetail() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: +this.$route.query.id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpApproveApproveDetailsPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.detailData = res.data
        this.tableSettings = APPROVAL_DETAIL_TABLESETTINGS[res.data.approve_type]
        // 很神奇，后端没有统一返回的字段，个别类型是统一的，个别又是不同一的（跟后端说过了，他说改不动了。。。）
        if (res.data.materials_detail && res.data.materials_detail.length > 0) {
          this.tableData = res.data.materials_detail
        } else if (res.data.materials_info && res.data.materials_info.length > 0) {
          this.tableData = res.data.materials_info
        } else if (res.data.refund_info && res.data.refund_info.length > 0) {
          this.tableData = res.data.refund_info
        } else if (res.data.approve_type === 'exit_info' && res.data.exit_data && res.data.exit_data.length > 0) {
          let result = []
          // 出库单关联不同的入库单的，物资的供应商对应入库单的的供应商，需要将数据拆分
          res.data.exit_data.forEach(item => {
            if (item.contact_trade_list && item.contact_trade_list.length > 0) {
              item.contact_trade_list.forEach(v => {
                result.push({
                  materials_name: item.materials_name,
                  supplier_manage_name: v.supplier_manage_name,
                  count: v.count,
                  unit_name: item.unit_name
                })
              })
            } else {
              result.push({
                materials_name: item.materials_name,
                supplier_manage_name: '',
                count: item.exit_count,
                unit_name: item.unit_name
              })
            }
          })
          this.tableData = result
        } else {
          this.tableData = []
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 操作点击
    clickOperationHandle(type) {
      let data = {
        id: +this.$route.query.id
      }
      let title = ''
      let apiUrl = ''
      switch (type) {
        case 'confirmReturn':
          title = '确认归还吗？'
          apiUrl = 'apiBackgroundDrpTransferInfoConfirmReturnPost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break;
        case 'delete':
          title = '确定删除吗？'
          apiUrl = 'apiBackgroundDrpInquiryDeletePost'
          this.showOperationDialog(title, apiUrl, { ids: [data.id] })
          break;
        case 'sales':
          title = '确定销单吗？'
          apiUrl = 'apiBackgroundDrpInquirySubmitPost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break;
        case 'receipt':
          title = '确定收货吗？'
          apiUrl = 'apiBackgroundDrpTransferInfoConfirmReceiptPost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break;
        case 'agree':
          title = '确定同意该审批申请？'
          apiUrl = 'apiBackgroundDrpApproveAgreeApprovePost'
          this.showOperationDialog(title, apiUrl, { id: data.id })
          break;
        case 'delete':
          title = '确定删除吗？'
          apiUrl = 'apiBackgroundDrpInquiryDeletePost'
          this.showOperationDialog(title, apiUrl, { ids: [data.id] })
          break;
        case 'reject':
          this.dialogType = 'reject'
          this.dialogTitle = '驳回原因'
          this.showDialogFooter = true
          this.dialogForm.id = data.id
          this.dialogForm.reason = ''
          this.showDialog = true
          // title = '确定拒绝吗？'
          // apiUrl = 'apiBackgroundDrpApproveRejectApprovePost'
          // this.showOperationDialog(title, apiUrl, { id: data.id, reject_reason: this.dialogForm.reason })
          break;
      }
    },
    // 显示操作弹窗
    showOperationDialog(title, apiUrl, params) {
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis[apiUrl](params)
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              // this.getApprovalDetail()
              this.backHandle()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 获取仓库数据列表
    async getWarehouseList() {
      const res = await this.$apis.apiBackgroundDrpWarehouseListPost({
        status: 'enable',
        page: 1,
        page_size: 999999
      })
      if (res.code === 0) {
        // 过滤掉当前的仓库，调拨单不允许选自己
        this.warehouseList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    showDialogHandle(type) {
      this.dialogFormType = type
      this.dialogVisible = true
    },
    handlerClose() {
      this.dialogFormData = {
        warehouse_id: ''
      }
      this.dialogFormType = ''
    },
    clickCancleHandle() {
      this.dialogVisible = false
    },
    clickDialogConfirm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        let warehouse = this.warehouseList.find(v => this.dialogFormData.warehouse_id === v.id)
        if (this.dialogFormType === 'to_purchase_info') {
          this.$router.push({
            name: 'ModifyPurchaseOrder',
            query: {
              warehouse_id: this.dialogFormData.warehouse_id,
              warehouse_name: warehouse.name,
              type: this.dialogFormType,
              sg_trade_no: this.detailData.trade_no
            },
            params: {
              type: 'add'
            }
          })
        }
        if (this.dialogFormType === 'to_exit_info') {
          this.$router.push({
            name: 'AddOutboundOrder',
            params: {
              type: 'add'
            },
            query: {
              warehouse_id: this.dialogFormData.warehouse_id,
              warehouse_name: warehouse.name,
              type: this.dialogFormType,
              sg_trade_no: this.detailData.trade_no
            }
          })
        }
        this.dialogVisible = false
      })
    },
    backHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'ApprovalManagementList')
    },
    closeDialogHandle() {
      // if (this.$refs.dialogFormRef) this.$refs.dialogFormRef.resetForm()
      this.timelineData = []
      this.dialogForm = {
        id: '',
        reason: ''
      }
      this.showDialog = false
    },
    clickConfirmHandle(e) {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          this.sendRejectHandle()
        }
      })
    },
    clickDialogCancleHandle() {},
    async sendRejectHandle() {
      if (this.dialogLoading) return
      this.dialogLoading = true
      let params = {
        id: this.dialogForm.id,
        reject_reason: this.dialogForm.reason
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpApproveRejectApprovePost(params))
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialog = false
        this.backHandle()
      } else {
        this.$message.error(res.msg)
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.ApprovalManagementDetail{
  position: relative;
  // height: 100%;
  .w-medium{
    width: 140px;
    height: 40px;
    &.m-r-20{
      margin-right: 20px;
    }
  }
  .w-300 {
    width: 300px;
    background-color: #f2f2f2;
    border-radius: 6px;
  }
  .table-wrapper {
    // min-height: calc(100% - 60px);
  }
  .timeline-box {
    // min-height: 300px;
    min-height: calc(100vh - 240px);
  }
  .content {
    min-height: 100%;
    margin-left: 340px;
    margin-right: 20px
  }
  .footer {
    position: absolute;
    bottom: 20px;
  }
}
</style>
