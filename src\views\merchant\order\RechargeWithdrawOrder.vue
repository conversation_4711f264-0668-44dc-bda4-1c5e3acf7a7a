<template>
  <!-- eslint-disable vue/no-unused-vars -->
  <div class="appeal-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="130px" :loading="isLoading" :form-setting="searchSetting"
      @search="searchHandle" @reset="resetHandler">
      <template slot="perv">
        <div style="margin-bottom: 20px;">
          <el-radio-group v-model="tabType" :disabled="isLoading" @change="changeTabHandle" class="ps-radio-btn">
            <el-radio-button :label="item.value" v-for="(item, index) in tabList" :key="index">{{ index == 0 ? item.name2
              : item.name }}</el-radio-button>
          </el-radio-group>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport"
            v-permission="['background_order.order_withdraw.approval_list_export']">导出报表</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column :index="indexMethod" v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #reativeTradeNo="{ row }">
              <div :class="[row.order_refund_no && row.order_refund_no.length !== 1 ? 'ps-text pointer' : '']"
                @click="handlerDialogEdit('detail', row)">{{ (row.order_refund_no && row.order_refund_no.length == 1) ?
                  row.order_refund_no[0] : '查看' }}</div>
            </template>
            <template #residual="{ row }">
              <el-statistic ref="statistic" format="HH:mm:ss" :value="getExamineTime(row.apply_time)" time-indices
                :value-style="countDown">
              </el-statistic>
            </template>
            <template #operation="{ row }">
              <el-button v-if="tabType == 'applying'" type="text" size="small" class="ps-text"
                v-permission="['background_order.order_withdraw.approval_withdraw']"
                @click="handlerDialogEdit('agree', row)">同意</el-button>
              <el-button v-if="tabType == 'applying'" type="text" size="small" class="ps-red"
                v-permission="['background_order.order_withdraw.approval_withdraw']"
                @click="handlerDialogEdit('refund', row)">拒绝</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <div style="margin-left: 20px; font-size: 14px;">
        <span v-if="tabType == 'applying'">待处理合计：{{ totalCount || 0 }}</span>
        <span v-if="tabType == 'agree'">已同意合计：{{ totalCount || 0 }}笔</span>
        <span v-if="tabType == 'agree'" class="m-l-20">提现金额合计：{{ withdrawCount || 0 }}元</span>
        <span v-if="tabType == 'refuse'">已拒绝合计：{{ totalCount || 0 }}笔</span>
        <span v-if="tabType == 'cancel'">已取消合计：{{ totalCount || 0 }}笔</span>
      </div>
      <!-- 分页 start -->
      <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'" :total="totalCount"></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting v-if="dialogPrintVisible" :extraParams="{ printType: printType }" :tableSetting="tableSetting" :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible" @confirm="confirmPrintDialog"></print-setting>
    <recharge-control-dialog :show="isShowControlDialog" :dialogType="dialogType" :dialogTitle="dialogTitle"
      ref="controlDialog" @dialogClose="dialogClose" @dialogConfirm="dialogConfirm"></recharge-control-dialog>
  </div>
</template>

<script>
import { debounce, to, divide, deepClone, times, replaceDate } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入

import {
  DIC_EXAMINE_STATUS,
  RECHARGE_WITHDRAW_SEARCH_SETTING,
  RECHARGE_WITHDRAW_TABLE_SETTING_PENDING,
  RECHARGE_WITHDRAW_TABLE_SETTING_AGREEN,
  RECHARGE_WITHDRAW_TABLE_SETTING_REFUND,
  RECHARGE_WITHDRAW_TABLE_SETTING_CANCEL,
  DIC_DATE_TYPE,
  RECHARGE_WITHDRAW_SEARCH_SETTING_ORG,
  RECHARGE_WITHDRAW_SEARCH_SETTING_ORDER
} from './constants'
import report from '@/mixins/report' // 混入
import RechargeControlDialog from './component/RechargeControlDialog.vue'

export default {
  name: 'RechargeWithdrawOrder',
  mixins: [exportExcel, report],
  components: {
    RechargeControlDialog
  },
  data() {
    return {
      isLoading: false,
      tabList: deepClone(DIC_EXAMINE_STATUS),
      tabType: 'applying',
      searchSetting: deepClone(RECHARGE_WITHDRAW_SEARCH_SETTING),
      // 数据列表
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      walletOrgsList: [], // 动账钱包
      levelList: [],
      // 报表设置相关
      tableSetting: deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_PENDING),
      currentTableSetting: deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_PENDING),
      dialogPrintVisible: false,
      printType: 'rechargeWithdrawOrderApplying',
      isShowControlDialog: false, // 是否显示处理弹窗
      dialogTitle: '', //  弹窗标题
      dialogType: '', // 弹窗类型
      agreeCount: '', // 已同意合计
      withdrawCount: '', // 提现合计
      refundCount: '', // 拒绝合计
      cancelCount: '', // 取消合计
      countDown: {
        fontSize: '13px !important'
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
      this.searchSetting.org_ids = RECHARGE_WITHDRAW_SEARCH_SETTING_ORG
      this.initPrintSetting()
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getDataList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.getDataList()
    },
    // 重置
    resetHandler() {
      this.currentPage = 1
      this.getDataList()
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    changeTabHandle(e) {
      console.log("changeTabHandle", e, this.searchSetting);
      var dataList = deepClone(DIC_DATE_TYPE)
      var dateType = 'apply_time'
      console.log("changeTabHandle", dataList);
      this.tabType = e
      switch (e) {
        case "applying":
          dataList = dataList.filter(item => {
            return item.value === 'apply_time'
          })
          this.tableSetting = deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_PENDING)
          this.currentTableSetting = deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_PENDING)
          this.searchSetting.org_ids = RECHARGE_WITHDRAW_SEARCH_SETTING_ORG
          delete this.searchSetting.refund_no
          this.printType = 'rechargeWithdrawOrderApplying'
          break;
        case "agree":
          dataList = dataList.filter(item => {
            return item.value !== 'cancel_time'
          })
          this.tableSetting = deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_AGREEN)
          this.currentTableSetting = deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_AGREEN)
          this.searchSetting.refund_no = RECHARGE_WITHDRAW_SEARCH_SETTING_ORDER
          delete this.searchSetting.org_ids
          this.printType = 'rechargeWithdrawOrderAgree'
          break;
        case "refuse":
          dataList = dataList.filter(item => {
            return item.value !== 'cancel_time'
          })
          this.tableSetting = deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_REFUND)
          this.currentTableSetting = deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_REFUND)
          this.searchSetting = deepClone(RECHARGE_WITHDRAW_SEARCH_SETTING)
          this.printType = 'rechargeWithdrawOrderRefuse'
          break;
        case "cancel":
          dataList = dataList.filter(item => {
            return item.value !== 'process_time'
          })
          this.tableSetting = deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_CANCEL)
          this.currentTableSetting = deepClone(RECHARGE_WITHDRAW_TABLE_SETTING_CANCEL)
          this.searchSetting = deepClone(RECHARGE_WITHDRAW_SEARCH_SETTING)
          this.printType = 'rechargeWithdrawOrderCancel'
          break;
        default:
          break;
      }
      this.$set(this.searchSetting.time_type, 'dataList', dataList)
      this.$set(this.searchSetting.time_type, 'value', dateType)
      console.log("this.searchSetting.time_type", this.searchSetting.date_type);
      this.initPrintSetting()
      this.currentPage = 1
      this.getDataList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      console.log("formatQueryParams", data);
      let params = {}
      let dateType = this.searchSetting.time_type.value || ''
      console.log("dateType", dateType);
      for (const key in data) {
        if (data[key] && data[key].value !== '') {
          if (key !== 'select_time') {
            if (data[key].value instanceof Array) {
              if (data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (key === 'select_time' && data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      if (params.time_type === 'cancel_time') {
        params.time_type = 'process_time'
      }
      return params
    },
    // 获取待处理订单列表
    async getDataList() {
      console.log("getDataList");
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderWithdrawApprovalListPost({
        page: this.currentPage,
        page_size: this.pageSize,
        approval_status: this.tabType,
        ...this.formatQueryParams(this.searchSetting)
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('getDataList', res)
      if (res && res.code === 0) {
        var data = res.data || {}
        this.totalCount = data.count || 0
        this.withdrawCount = data.total_real_withdraw_fee ? divide(data.total_real_withdraw_fee) : 0
        this.tableData = data.results || []
        if (this.tabType === 'applying' && this.$refs.countDown) {
          this.$refs.countDown.reStart()
        }

        // this.tableData = res.data.results.map(v => {
        //   v.origin_fee = '￥' + divide(v.origin_fee)
        //   v.pay_fee = '￥' + divide(v.pay_fee)
        //   v.watting_rate_fee = '￥' + divide(v.watting_rate_fee)
        //   v.subsidy_fee = '￥' + divide(v.subsidy_fee)
        //   v.wallet_fee = '￥' + divide(v.wallet_fee)
        //   v.complimentary_fee = '￥' + divide(v.complimentary_fee)
        //   v.subsidy_balance = '￥' + divide(v.subsidy_balance)
        //   v.wallet_balance = '￥' + divide(v.wallet_balance)
        //   v.complimentary_balance = '￥' + divide(v.complimentary_balance)
        //   v.refund_fee = '￥' + divide(v.refund_fee)
        //   return v
        // })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 显示审批弹窗
    handlerDialogEdit(type, row) {
      if (type === 'detail' && (!row.order_refund_no || (row.order_refund_no && row.order_refund_no.length === 1))) {
        return
      }
      if (type !== 'detail' && this.getExamineTime(row.apply_time) <= 0) {
        return this.$message.error('亲，已经超过审批时间了')
      }
      this.dialogType = type
      this.dialogTitle = type === 'agree' ? '同意审批' : type === 'detail' ? '关联充值退款订单号' : '拒绝审批'
      if (this.$refs.controlDialog) {
        if (type === 'detail' && Reflect.has(this.$refs.controlDialog, 'setOrderList')) {
          this.$refs.controlDialog.setOrderList(row.order_refund_no)
        }
        if (Reflect.has(this.$refs.controlDialog, 'setDialogData')) {
          this.$refs.controlDialog.setDialogData(row)
        }
      }
      this.isShowControlDialog = true
    },
    // 导出报表
    handleExport() {
      const option = {
        type: this.printType,
        params: {
          page: this.currentPage,
          page_size: 999999,
          approval_status: this.tabType,
          ...this.formatQueryParams(this.searchSetting)
        },
        url: "apiBackgroundOrderOrderWithdrawApprovalListExportPost"
      }
      this.exportHandle(option)
    },
    // 获取支付方式 / 支付类型
    async getpayList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost()
      if (res.code === 0) {
        const result = []
        const result2 = []
        res.data.result.payways.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        res.data.result.sub_payways.forEach(d => {
          Object.keys(d).forEach(key => result2.push({ label: d[key], value: key }))
        })
        this.searchSetting.payway.dataList = result
        this.searchSetting.sub_payway.dataList = result2
      } else {
        this.$message.error(res.msg)
      }
    },
    // 弹窗确认
    dialogConfirm(status, data) {
      console.log("dialogConfirm", status, data);
      if (this.dialogType === 'detail') {
        this.isShowControlDialog = false
      } else {
        this.updateOrderStatus(status, data)
      }
    },
    // 弹窗取消
    dialogClose() {
      this.isShowControlDialog = false
    },
    // 更新弹窗按钮状态
    updateDialogBtnStatus(isflag) {
      if (this.$refs.controlDialog) {
        this.$refs.controlDialog.setBtnLoading(isflag)
      }
    },
    // 更新订单状态
    async updateOrderStatus(flag, data) {
      var refundMessage = flag === 'refund' ? "拒绝" : "同意"
      var status = flag === 'refund' ? 'refuse' : 'agree'
      this.updateDialogBtnStatus(true)
      var params = {
        id: data.id,
        process_reason: data.remark,
        real_withdraw_fee: data.price ? times(data.price) : 0,
        approval_status: status
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderWithdrawApprovalWithdrawPost(params))
      this.updateDialogBtnStatus(false)
      if (err) {
        return this.$message.error(refundMessage + "失败")
      }

      if (res && res.code === 0) {
        this.isShowControlDialog = false
        this.$message.success(refundMessage + "成功")
        this.getDataList()
      } else {
        this.$message.error(res.msg ? res.msg : refundMessage + "失败")
      }
    },
    // 格式化价格
    formatPrice(price) {
      if (!price) {
        return 0
      }
      return divide(price)
    },
    // 获取审核倒记时间
    getExamineTime(applyTime) {
      if (!applyTime) {
        return 0
      }
      var applyTimeNow = new Date(replaceDate(applyTime)).getTime() + 48 * 60 * 60 * 1000
      var nowTime = new Date().getTime()
      var count = applyTimeNow - nowTime
      return count > 0 ? Date.now() + count : 0
    }
  }
}
</script>
<style lang="scss" scoped>
.appeal-order {
  .sumWrapper {
    padding-left: 20px;
    padding-bottom: 20px;

    ul,
    li {
      list-style: none;
    }

    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 13px;
    }
  }

  .count-down {
    font-size: 13px !important;
  }
}
</style>
