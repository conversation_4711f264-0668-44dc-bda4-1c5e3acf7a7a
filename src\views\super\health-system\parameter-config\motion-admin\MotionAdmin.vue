<template>
  <div class="MotionAdmin">
    <refresh-tool @refreshPage="refreshHandle" />

    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="showDialogHandle('add')">新增运动</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="showDialogHandle('modify', row)"
              >
                编辑
              </el-button>
              <span style="margin: 0 10px; color: #e2e8f0">|</span>
              <el-button
                type="text"
                size="small"
                class="ps-warn-text"
                @click="deleteHandler('single', row.id)"
              >
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @current-change="handleCurrentChange"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 start -->
    <motion-dialog v-model="showDialog" :type="dialogType" :infoData="dialogData" @confirmdialog="confirmdialogHandle" />
    <!-- 弹窗 end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import MotionDialog from './MotionDialog'

export default {
  name: 'MotionAdmin',
  components: { MotionDialog },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableSettings: [
        { label: '运动类型', key: 'sport_type_alias' },
        { label: '运动名称', key: 'name' },
        { label: '强度', key: 'intensity_alias' },
        { label: 'MET', key: 'met' },
        { label: '能量消耗kcal/（kg·min）', key: 'energy_kcal' },
        { label: '操作人', key: 'operator_name' },
        { label: '修改时间', key: 'update_time', type: 'date' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      tableData: [],
      searchFormSetting: {
        select_time: {
          type: 'datetimerange',
          label: '修改时间',
          format: 'yyyy-MM-dd HH:mm:ss',
          value: []
        },
        name: {
          type: 'input',
          label: '运动名称',
          value: '',
          placeholder: '请输入运动名称'
        },
        operator: {
          type: 'input',
          label: '操作人',
          value: '',
          placeholder: '请输入操作人'
        }
      },
      dialogLoading: false,
      showDialog: false,
      dialogType: '',
      dialogData: {}
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSportsList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 运动列表
    async getSportsList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminSportsListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    showDialogHandle(type, data) {
      this.dialogType = type
      if (type === 'modify') {
        console.log(data)
        this.dialogData = data
      } else {
        this.dialogData = {}
      }
      this.showDialog = true
    },
    // gotoMotion(type, row) {
    //   this.$router.push({
    //     name: 'SuperAddOrModifyMotionAdmin',
    //     query: {
    //       type: type,
    //       data: type === 'modify' ? this.$encodeQuery(row) : ''
    //     }
    //   })
    // },
    deleteHandler(type, id) {
      this.$confirm(`删除后数据不可恢复，确定要删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminSportsDeletePost({
                ids: [id]
              })
            )
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.searchHandle()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getSportsList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSportsList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    confirmdialogHandle() {
      this.getSportsList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.MotionAdmin {
  .motion-box {
    padding: 10px 20px 10px 20px;
    border: 1px solid #797979;
    border-radius: 10px;
    .text {
      font-size: 30px;
    }
  }
}
</style>
