<template>
  <!-- 第三方对账汇总表 -->
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        label-width="120px"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            数据列表
            <span style="margin-left: 20px; font-size: 14px;">当前表格仅统计以下来源的交易对账数据：农行-缴费、微信-JSAPI支付</span>
          </div>
          <div class="align-r">
            <el-button size="mini" @click="gotoExport">导出Excel</el-button>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row" />
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { recentSevenDay } from './constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'ThirdReconciliation',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '交易日期', key: 'trade_date' },
        { label: '渠道', key: 'channel_alias' },
        { label: '商户号', key: 'merchant_id' },
        { label: '交易成功笔数', key: 'success_count' },
        { label: '交易成功金额', key: 'success_fee', type: 'money' },
        { label: '退款成功笔数', key: 'refund_count' },
        { label: '退款成功金额', key: 'refund_fee', type: 'money' },
        { label: '第三方实收金额', key: 'third_income_fee', type: 'money' },
        { label: '后台统计实收金额', key: 'background_collect_fee', type: 'money' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '时间',
          value: recentSevenDay
        },
        channel: {
          type: 'select',
          value: '',
          label: '渠道',
          dataList: [
            {
              value: 'ABC_JF',
              label: '农行-缴费'
            },
            {
              value: 'WECHAT_JSAPI',
              label: '微信-JSAPI'
            }
          ],
          clearable: true
        },
        merchant_id: {
          type: 'input',
          value: '',
          label: '商户号',
          placeholder: '请输入商户号',
          clearable: true
        }
      },
      collect: [ // 统计
        { key: 'total_success_count', value: 0, label: '总交易成功笔数:', unit: "笔" },
        { key: 'total_success_fee', value: 0, label: '总交易成功金额:￥', type: 'money' },
        { key: 'total_refund_count', value: 0, label: '总退款笔数:', unit: "笔" },
        {
          key: 'text',
          value: '实收金额=交易成功金额-退款金额',
          label: '',
          block: true
        }
      ],
      printType: 'ThirdReconciliation',
      isFirstSearch: true
    }
  },
  created () {
    this.initLoad(true)
  },
  mounted() {
    this.initPrintSetting()
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        this.currentTableSetting = this.tableSetting
        this.getWithdrawList()
      }
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.getWithdrawList()
      this.isFirstSearch = true
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getWithdrawList()
        this.isFirstSearch = false
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getWithdrawList() {
      const params = this.formatQueryParams(this.searchFormSetting)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportCompareBillCollectListPost({
        ...params,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.result
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getWithdrawList()
    },
    // 导出
    gotoExport() {
      const option = {
        url: "apiBackgroundReportCenterDataReportCompareBillCollectListExportPost",
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '第三方对账表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportCompareBillCollectListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
