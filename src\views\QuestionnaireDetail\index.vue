<template>
  <div class="preview-wrapper">
    <div class="preview-container" v-if="!isFinish">
      <el-form ref="previewForm" :model="formData" label-position="top" v-loading="loading" v-if="isDataReady">
        <!-- 问卷标题 -->
        <div class="preview-header">
          <h2>{{ title }}</h2>
          <p class="overview">{{ questionnaireData.overview }}</p>
        </div>

        <!-- 问题列表 -->
        <div class="preview-content">
          <div v-for="(item, index) in questionnaireList" :key="index" class="question-item">
            <el-form-item
              :label="`${index + 1}. ${item.caption}`"
              :prop="`${item.id}.value`"
              :rules="computedRules(item)">

              <!-- 单选 -->
              <template v-if="item.question_type === 0 && formData[item.id]" style="width: 100%;">
                <el-radio-group v-model="formData[item.id].value" v-removeAriaHidden :disabled="isFinish">
                  <div v-for="(itemIn, indexIn) in item.choices" :key="indexIn" style="width: 100%;" class="flex flex-col m-t-10 m-b-10">
                    <el-radio :label="itemIn.id">
                      {{ itemIn.description }}
                    </el-radio>
                    <el-input
                      class="m-l-28 w-350"
                      type="textarea"
                      :rows="3"
                      v-if="itemIn.other_content"
                      v-model="itemIn.other_value"
                      :placeholder="itemIn.other_content"
                      :disabled="isFinish">
                    </el-input>
                  </div>
                </el-radio-group>
              </template>

              <!-- 多选 -->
              <template v-else-if="item.question_type === 1 && formData[item.id]" style="width: 100%;">
                <el-checkbox-group v-model="formData[item.id].value" :disabled="isFinish">
                  <div v-for="(itemIn, indexIn) in item.choices" :key="indexIn" style="width: 100%;" class="flex flex-col">
                    <el-checkbox :label="itemIn.id">{{ itemIn.description }}</el-checkbox>
                    <el-input
                      class="m-l-28 w-350"
                      type="textarea"
                      :rows="3"
                      v-if="itemIn.other_content"
                      v-model="itemIn.other_value"
                      :placeholder="itemIn.other_content"
                      :disabled="isFinish">
                    </el-input>
                  </div>
                </el-checkbox-group>
              </template>

              <!-- 评分 -->
              <template v-else-if="item.question_type === 2 && formData[item.id]" style="width: 100%;">
                <div class="mark-topic-content">
                  <div class="mark-topic-content-item">
                    <div class="mark-topic-content-item-top">
                      <div class="point">1</div>
                      <div class="point">{{ item.top_score }}</div>
                    </div>
                    <div class="mark-topic-content-item-bottom">
                      <div v-for="(itemIn, index) in item.top_score" :key="itemIn" :class="['mark', index + 1 <= formData[item.id].value ? 'selectScore' : '', isFinish ? 'disabled' : '']" @click="selectScore(item, itemIn)">
                        {{ itemIn }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>

              <!-- 评价 -->
              <template v-else-if="item.question_type === 3 && formData[item.id]" style="width: 100%;">
                <div v-for="(itemIn, indexIn) in item.choices" :key="indexIn" style="width: 100%;">
                  <div style="display: flex; flex-direction: column; align-items: flex-start; justify-content: flex-start; margin: 10rpx 0;">
                    <div class="m-r-20">{{ itemIn.description }}</div>
                    <el-rate active-color="#FF9B45" inactive-color="#b2b2b2" gutter="20" v-model="itemIn.other_value" @change="(value) => changeRate(item, itemIn, indexIn, value)" :disabled="isFinish"></el-rate>
                  </div>
                </div>
              </template>

              <!-- 填空 -->
              <template v-else-if="item.question_type === 4 && formData[item.id]" style="width: 100%;">
                <el-input
                  v-model="formData[item.id].value"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入"
                  :disabled="isFinish">
                </el-input>
              </template>

              <!-- 照片 -->
              <template v-else-if="item.question_type === 5 && formData[item.id]" style="width: 100%;">
                <el-upload
                  v-loading="uploadingForImg" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                  :action="serverUrl" :data="uploadParams" :file-list="formData[item.id].fileList" :on-success="(response, file, fileList) => uploadSuccess(response, file, fileList, 'img', item)"
                  :before-upload="(file) => beforeUpload(file, 'img')" :limit="item.upload_max_num" :multiple="false" :show-file-list="false"
                  :headers="headersOpts" :disabled="isFinish">
                  <div class="flex w-100-p flex-wrap">
                    <div class="m-5" v-for="(itemIn, indexIn) in formData[item.id].value" :key="indexIn" style="position: relative;">
                      <img style="width: 120px; height: 120px;" :src="itemIn" @click.stop="handleClick(itemIn)">
                      <div class="triangle">
                        <i class="el-icon-delete" style="position: absolute; top: -14px; right: -14px; color: #fff; z-index: 999;" @click.stop="deleteImg(indexIn, item)"></i>
                      </div>
                    </div>
                    <div class="flex-center upload-border m-5" v-if="formData[item.id].value.length < item.upload_max_num">
                      <div>
                        <i class="el-icon-plus" style="font-size: 22px;"></i>
                      </div>
                    </div>
                  </div>
                  <div slot="tip" class="el-upload__tip">不超过5M</div>
                </el-upload>
              </template>

              <!-- 文件 -->
              <template v-else-if="formData[item.id]" style="width: 100%;">
                <el-upload
                  v-loading="uploadingForFile" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                  :action="serverUrl" :data="uploadParams" :file-list="formData[item.id].fileList" :on-success="(response, file, fileList) => uploadSuccess(response, file, fileList, 'file', item)"
                  :before-upload="(file) => beforeUpload(file, 'file')" :limit="item.upload_max_num" :multiple="false" :show-file-list="true"
                  :headers="headersOpts" :disabled="isFinish">
                  <div class="flex-center">
                    <el-button class="m-r-20" size="small" icon="el-icon-plus" :disabled="formData[item.id].value.length >= item.upload_max_num">添加文件</el-button>
                    <div slot="tip" class="el-upload__tip">不超过10M</div>
                  </div>
                </el-upload>
              </template>

            </el-form-item>
          </div>
        </div>

        <!-- 添加提交按钮 -->
        <div class="preview-footer">
          <el-button
            type="primary"
            size="large"
            @click="submit"
            :disabled="isFinish || canSubmit">
            提交问卷
          </el-button>
        </div>
      </el-form>

      <div class="flex-center p-b-20">本次问卷以{{ questionnaireData.commit_type === 'real_name' ? '实名' : '匿名' }}形式提交</div>
    </div>
    <div v-else class="preview-container">
      <el-empty :image="emptyImg" description="您的答卷已提交，感谢您的参与！"></el-empty>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { deepClone, getToken, getSuffix } from '@/utils'
import { mapGetters } from 'vuex'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  name: 'Questionnaire',
  components: {
    ElImageViewer
  },
  data() {
    return {
      loading: false,
      title: '问卷调查',
      questionnaireData: {},
      questionnaireList: [],
      formData: {},
      rules: {},
      isDataReady: false,
      uploadingForImg: false,
      uploadingForFile: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'incumbents',
        key: 'incumbents' + new Date().getTime() + Math.floor(Math.random() * 150)
      },
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      isFinish: false,
      canSubmit: false,
      showImagePreview: false,
      previewList: [],
      emptyImg: require("@/assets/img/completed.png")
    }
  },
  computed: {
    ...mapGetters(['cookie']),
    limitsRules() {
      return d => {
        if (d.question_type === 1) {
          return {
            validator: (rule, value, callback) => {
              if (!value.length) {
                callback(new Error('请选择'))
              } else {
                if (value.length >= d.least_choose_count) {
                  callback()
                } else {
                  callback(new Error(`至少选${d.least_choose_count}项`))
                }
              }
            },
            trigger: ['change', 'blur']
          }
        } else {
          return {
            validator: (rule, value, callback) => {
              if (!value.length) {
                callback(new Error('请选择'))
              } else {
                if (value.length < d.choices.length) {
                  callback(new Error(`有评价未填写`))
                } else {
                  callback()
                }
              }
            },
            trigger: ['change', 'blur']
          }
        }
      }
    },
    computedRules() {
      return d => {
        let rules = [{ required: d.required, message: "此题为必选题", trigger: ["change", "blur"] }]
        if (d.question_type === 1 && d.required) {
          let obj = {
            trigger: ["change", "blur"],
            validator: (rule, value, callback) => {
              if (!value || !value.length) {
                callback(new Error("请选择"))
              } else if (value.length < d.least_choose_count) {
                callback(new Error(`至少选${d.least_choose_count}项`))
              } else {
                callback()
              }
            }
          }
          rules.push(obj)
        } else if (d.question_type === 3 && d.required) {
          let obj = {
            trigger: ["change", "blur"],
            validator: (rule, value, callback) => {
              if (!value || !value.length) {
                callback(new Error("请选择"))
              } else if (value.length < d.choices.length) {
                callback(new Error(`有评价未填写`))
              } else {
                callback()
              }
            }
          }
          rules.push(obj)
        }
        return rules
      }
    }
  },
  async created() {
    let id = this.$route.query.id
    await this.getQuestionnaireList(id)

    if (this.questionnaireData && this.questionnaireData.name) {
      this.title = this.questionnaireData.name
    }

    this.generateQuestionnaire()

    // 标记数据已准备好
    this.isDataReady = true
  },
  methods: {
    async getQuestionnaireList(id) {
      this.loading = true
      try {
        const res = await this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireDetailPost({
          id,
          is_answer: true
        })
        if (res.code === 0) {
          this.questionnaireData = deepClone(res.data)
          this.questionnaireList = deepClone(res.data.questions)
        } else if (res.code === 1) {
          this.$message.error(res.msg)
          this.canSubmit = true
        } else {
          this.$message.error(res.msg)
          this.canSubmit = true
        }
      } catch (error) {
        console.error('获取问卷详情失败:', error)
        this.$message.error('获取问卷详情失败')
      } finally {
        this.loading = false
      }
    },
    // 生成问卷
    generateQuestionnaire() {
      this.questionnaireList.forEach(item => {
        // 根据不同的问题类型初始化不同的默认值
        let defaultValue = ''
        if ([1, 3, 5, 6].includes(item.question_type)) {
          defaultValue = []
        } else if (item.question_type === 2) {
          defaultValue = 0
        }

        this.$set(this.formData, item.id, {
          ...item,
          value: defaultValue,
          fileList: item.question_type === 5 || item.question_type === 6 ? [] : undefined
        })

        if (item.choices && item.choices.length) {
          item.choices.forEach(choice => {
            this.$set(choice, 'other_value', item.question_type === 3 ? 0 : '')
          })
        }
      })
    },
    changeRate(item, itemIn, indexIn, value) {
      console.log('changeRate', item, itemIn, indexIn, value)
      if (!item || !item.id || !this.formData[item.id]) return

      let obj = {
        id: itemIn.id,
        score: value
      }
      if (this.formData[item.id].value.length && this.formData[item.id].value.find(child => child.id === itemIn.id)) {
        this.$set(this.formData[item.id], 'value', this.formData[item.id].value.map(child => {
          if (child.id === itemIn.id) {
            child = { ...obj }
          }
          return child
        }))
      } else {
        this.$set(this.formData[item.id], 'value', [...this.formData[item.id].value, { ...obj }])
      }
    },
    // 获取上传组件的值，包含安全检查
    getUploadValue(item) {
      if (!item || !item.id || !this.formData[item.id]) {
        return []
      }
      return this.formData[item.id].value || []
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList, type, item) {
      if (type === 'img') {
        if (res && res.code === 0) {
          this.$message.success('上传成功')
          this.formData[item.id].value = [...this.formData[item.id].value, res.data.public_url]
        } else {
          this.$message.error(res.msg)
        }
        this.uploadingForImg = false
      } else {
        if (res && res.code === 0) {
          this.$message.success('上传成功')
          this.formData[item.id].value = [...this.formData[item.id].value, res.data.public_url]
        } else {
          this.$message.error(res.msg)
        }
        this.uploadingForFile = false
      }
    },
    // 图片上传前检测
    beforeUpload(file, type) {
      if (type === 'img') {
        const unUploadType = ['.jpeg', '.jpg', '.png', '.gif']
        const isLt2M = file.size / 1024 / 1024 <= 5
        if (!unUploadType.includes(getSuffix(file.name))) {
          this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
          return false
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 5MB!')
          return false
        }
        this.uploadingForImg = true
      } else {
        const unUploadType = ['.jpeg', '.jpg', '.xls', '.xlsx', '.png', '.gif', '.mp4', '.txt', '.zip', '.docx', '.doc', '.apk', '.tiff', ".JPEG", ".PNG", ".TIFF", ".WEBP", ".HEIF", ".JPG", ".exe", ".rar", ".ZIP", ".RAR"]
        const isLt2M = file.size / 1024 / 1024 <= 10
        if (!unUploadType.includes(getSuffix(file.name))) {
          this.$message.error('该文件格式不支持上传!')
          return false
        }
        if (!isLt2M) {
          this.$message.error('上传文件大小不能超过 10MB!')
          return false
        }
        this.uploadingForFile = true
      }
    },
    deleteImg(index, data) {
      this.formData[data.id].value.splice(index, 1)
    },
    selectScore(item, index) {
      if (!this.isFinish) {
        if (!item || !item.id || !this.formData[item.id]) return
        this.$set(this.formData[item.id], 'value', index)
      }
    },
    setChoices(data) {
      switch (data.question_type) {
        case 0:
        case 2: {
          if (data.value.length) {
            return [data.value]
          } else {
            return []
          }
        }
        case 1:
        case 3: {
          if (data.value.length) {
            return [...data.value]
          } else {
            return []
          }
        }
      }
    },
    async submit() {
      // 表单校验
      try {
        this.$refs.previewForm.validate(async (valid) => {
          if (!valid) {
            this.$message.error('请完成必填项')
            return false
          } else {
            // 手动检查必填项

            const hasEmptyRequired = Object.values(this.formData).some(item => {
              if (item.required) {
                switch (item.question_type) {
                  case 0: {
                    console.log("item.value", item)
                    let obj = item.choices.find((itemIn) => itemIn.id === item.value)
                    console.log("obj", obj)
                    if (obj.other_content) {
                      if (!obj.other_value) {
                        return true
                      } else {
                        return false
                      }
                    } else {
                      return false
                    }
                  }
                  case 1: {
                    let arr = []
                    item.value.forEach((itemIn) => {
                      let newObj = item.choices.find((choicesItem) => itemIn === choicesItem.id)
                      console.log("newObj", newObj)
                      if (newObj.other_content) {
                        if (!newObj.other_value) {
                          arr.push(true)
                        } else {
                          arr.push(false)
                        }
                      } else {
                        arr.push(false)
                      }
                    })
                    if (arr.find((flag) => flag)) {
                      return true
                    } else {
                      return false
                    }
                  }
                }
              } else {
                return false
              }
            })
            if (hasEmptyRequired) {
              this.$message.error("请填写选择题中的其他项的填空")
              return false
            }

            let answerList = []
            for (let key in this.formData) {
              answerList.push(deepClone(this.formData[key]))
            }
            console.log('看看这个answerList', answerList)
            answerList = answerList.map(item => {
              let obj = {
                id: item.id || null,
                question_type: item.question_type,
                file_url: item.question_type === 5 || item.question_type === 6 ? item.value : [],
                text: item.question_type === 4 ? item.value : '',
                choices: this.setChoices(item)
              }
              console.log('看看这个obj', obj)
              // 处理一下choices
              if ([0, 1, 2].includes(item.question_type) && obj.choices.length) {
                obj.choices = obj.choices.map(choice => {
                  let itemIn = item.choices && item.choices.length ? item.choices.find(itemIn => itemIn.id === choice) : null
                  if (!itemIn) {
                    return {
                      id: null,
                      other_content: '',
                      score: item.question_type === 2 ? item.value : 0
                    }
                  } else {
                    return {
                      id: choice,
                      other_content: [0, 1].includes(item.question_type) ? (itemIn.other_value || '') : '',
                      score: item.question_type === 2 ? item.value : 0
                    }
                  }
                })
              }
              return obj
            })
            try {
              const res = await this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireAccountAnswerPost({
                id: this.$route.query.id,
                questions: [...answerList]
              })

              if (res.code === 0) {
                this.$message.success('提交成功')
                this.isFinish = true
              } else {
                this.$message.error(res.msg || '提交失败，请稍后重试')
              }
            } catch (error) {
              console.error('提交问卷错误:', error)
              this.$message.error('提交失败，请稍后重试')
            }
          }
        })
      } catch (error) {
        console.error('提交问卷错误:', error)
        this.$message.error('提交失败，请稍后重试')
      }
    },
    handleClick(img) {
      this.previewList = [img]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$primary-color: #409EFF;
$border-color: #EBEEF5;
$text-primary: #303133;
$text-regular: #606266;
$bg-color: #f5f7fa;
$container-width: 800px;
$spacing-base: 8px;

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
}

@mixin hover-effect {
  transition: all 0.3s ease;
  &:hover {
    border-color: $primary-color;
    box-shadow: 0 0 8px rgba($primary-color, 0.1);
  }
}

@mixin section-padding {
  padding: $spacing-base * 3 $spacing-base * 2.5;
}

// 重置最后一个元素的边距
@mixin reset-last-margin {
  &:last-child {
    margin-bottom: 0;
  }
}

.preview-wrapper {
  min-height: 100vh;
  background-color: $bg-color;
  padding: 20px 0;
  margin: 0;
}

.preview-container {
  max-width: $container-width;
  margin: 0 auto;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(#000, 0.1);
  border-radius: 10px;

  .preview-header {
    text-align: center;
    margin-bottom: $spacing-base * 3;
    @include section-padding;

    h2 {
      margin: 0 0 $spacing-base * 1.5;
      color: $text-primary;
      font-size: 24px;
      font-weight: 500;
    }

    .overview {
      color: $text-regular;
      font-size: 14px;
      margin: 0;
      line-height: 1.6;
    }
  }

  .preview-content {
    @include section-padding;

    .question-item {
      margin-bottom: $spacing-base * 3;
      padding: $spacing-base * 2.5;
      border: 1px solid $border-color;
      border-radius: 4px;
      @include hover-effect;
      @include reset-last-margin;
    }
  }

  .evaluate-item {
    @include flex-center;
    margin-bottom: $spacing-base * 1.5;
    @include reset-last-margin;

    .evaluate-label {
      max-width: 120px;
      margin-right: $spacing-base * 2;
      margin-left: $spacing-base * 2;
      color: $text-regular;
    }
  }

  .preview-footer {
    text-align: center;
    margin-top: 0;
    @include section-padding;

    .el-button {
      width: $spacing-base * 25; // 200px
      height: $spacing-base * 5.5; // 44px
      font-size: 16px;
    }
  }
}

// 响应式设计
@media screen and (max-width: #{$container-width + $spacing-base * 4}) {
  .preview-container {
    margin: 0 $spacing-base * 2;
  }
}

@media screen and (max-width: 480px) {
  .preview-container {
    margin: 0;

    .preview-content {
      padding: $spacing-base * 2;

      .question-item {
        padding: $spacing-base * 2;
      }
    }

    .evaluate-item {
      flex-direction: column;
      align-items: flex-start;

      .evaluate-label {
        width: 100%;
        margin-bottom: $spacing-base;
      }
    }
  }
}
.mark-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      border: 1px solid #E7ECF2;
      border-radius: 6px;
      padding: 5px;
      &-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px;
        border-bottom: 1px solid #E7ECF2;
        .point {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      &-bottom {
        margin-top: 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .mark {
          width: 32px;
          height: 32px;
          margin: 0 5px;
          border: 1px solid #E7ECF2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .selectScore {
          background-color: #FF9B45;
          color: #fff;
        }
        .disabled {
          background-color: #F5F7FA;
          color: #C0C4CC;
        }
      }
    }
  }
}
.upload-border {
  border: 1px dotted #DCDFE6;
  width: 120px;
  height: 120px;
  border-radius: 8px;
}
.triangle {
  width: 0px;
  height: 0px;
  position: absolute;
  top: 0px;
  right: 0px;
  border-top: 20px solid #FF9B45;
  border-right: 20px solid #FF9B45;
  border-bottom: 20px solid transparent;
  border-left: 20px solid transparent;
}
</style>
