<template>
  <div id="PhysicalExaminationReport">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="showImportDrawer" v-permission="['background_healthy.healthy_info.batch_add_healthy']">批量导入</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table ref="tableView" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" @click="showDetail(row)">报告详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 体检报告详情 -->
    <PhysicalExaminationReportDetail :isShow.sync="detailShow" :data="PhysicalExaminationDetail" />
    <import-dialog-drawer
      :show="importDrawerShow"
      :width="'77%'"
      :tableSetting="drawerTableSetting"
      :title="'导入体检报告'"
      :templateUrl="downloadUrl"
      :openExcelType="openExcelType"
      @cancel="importDrawerShow = false">
    </import-dialog-drawer>
  </div>
</template>

<script>
import { debounce, deepClone } from '@/utils'
import PhysicalExaminationReportDetail from './components/PhysicalExaminationReportDetail'
import dayjs from 'dayjs';
export default {
  name: 'PhysicalExaminationReport',
  components: {
    PhysicalExaminationReportDetail
  },
  data() {
    return {
      isLoading: false,
      searchForm: {
        create_time: {
          type: 'daterange',
          label: '上传时间',
          value: [
            dayjs()
              .subtract(7, 'day')
              .format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD')
          ],
          clearable: false
        },
        name: {
          label: '姓名',
          type: 'input',
          value: '',
          placeholder: '请输入',
          labelWidth: '100px'
        },
        person_no: {
          label: '人员编号',
          type: 'input',
          value: '',
          placeholder: '请输入',
          labelWidth: '100px'
        },
        phone: {
          label: '手机号',
          type: 'input',
          value: '',
          placeholder: '请输入',
          labelWidth: '100px'
        },
        data_source: {
          label: '报告类型',
          type: 'select',
          value: '',
          placeholder: '请选择',
          clearable: true,
          dataList: [
            {
              value: '',
              label: '全部'
            },
            {
              value: 'custom',
              label: '后台导入'
            },
            {
              value: 'shanghe',
              label: '上禾体检称'
            }
          ]
        }
      },
      tableData: [],
      currentTableSetting: [
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '手机号', key: 'phone' },
        { label: '报告类型', key: 'data_source_alias' },
        { label: '体检日期', key: 'check_date' },
        { label: '上传时间', key: 'create_time' },
        { label: '操作人', key: 'account_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
      ],
      drawerTableSetting: [
        { label: '体检日期', key: 'date' },
        { label: '人员编号', key: 'person_no' },
        { label: '姓名', key: 'name' },
        { label: '性别', key: 'gender' },
        { label: '年龄', key: 'age' },
        { label: '身高（cm）', key: 'height' },
        { label: '体重（kg）', key: 'weight' },
        { label: '左臂高压（mmHg）', key: 'sbp' },
        { label: '左臂低压（mmHg）', key: 'dbp' },
        { label: '左臂心率（次/分）', key: 'hr' },
        { label: '右臂高压（mmHg）', key: 'sbpR' },
        { label: '右臂低压（mmHg）', key: 'dbpR' },
        { label: '右臂心率（次/分）', key: 'hrR' },
        { label: '总胆固醇（mmol/L）', key: 'cholesterol' },
        { label: '血糖（mmol/L）', key: 'blood_sugar' },
        { label: '尿酸（μmol/L）', key: 'uric_acid' }
      ],
      detailShow: false,
      importDrawerShow: false,
      PhysicalExaminationDetail: {},
      downloadUrl: '/api/temporary/template_excel/healthy/体检数据导入模板.xlsx',
      openExcelType: 'PhysicalExaminationReportImport',
      page: 1,
      pageSize: 10,
      totalCount: 0
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getDataList()
    },
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.page = 1
        this.isLoading = true
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.isLoading = true
      this.page = 1
      this.initLoad()
    },
    getDataList() {
      let params = {
        page: this.page,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchForm)
      }
      this.$apis.apiBackgroundHealthyHealthyInfoCheckDataListPost(params).then(res => {
        if (res.code === 0) {
          this.tableData = deepClone(res.data.results)
          this.totalCount = res.data.count
          this.isLoading = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (key === 'create_time' && data[key].value.length) {
          params.start_time = dayjs(data[key].value[0]).format('YYYY-MM-DD HH:mm:ss')
          params.end_time = dayjs(data[key].value[1]).set('hour', 23).set('minute', 59).set('second', 59).format('YYYY-MM-DD HH:mm:ss')
        } else {
          params[key] = data[key].value || undefined
        }
      }
      return params
    },
    showDetail(data) {
      this.PhysicalExaminationDetail = deepClone(data)
      this.detailShow = true
    },
    showImportDrawer() {
      this.importDrawerShow = true
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.isLoading = true
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.isLoading = true
      this.initLoad()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
