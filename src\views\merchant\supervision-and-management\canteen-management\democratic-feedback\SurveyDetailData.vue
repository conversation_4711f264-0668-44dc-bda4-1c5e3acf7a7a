<template>
  <div>
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon type="export" @click="gotoExport">导出</button-icon>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #[item.slotName]="{ row }">
              <div v-if="item.key !== 'number' && item.key !== 'commit_time' && item.key !== 'name'">
                <div v-if="item.data.question_type === 0">
                  {{ row[item.key] }}
                </div>
                <div v-else-if="item.data.question_type === 1">
                  <div v-for="(itemIn, indexIn) in row[item.key]" :key="indexIn">{{ itemIn }}{{ ";" }}</div>
                </div>
                <div v-else-if="item.data.question_type === 2">
                  {{ row[item.key].score }}分
                </div>
                <div v-else-if="item.data.question_type === 3">
                  <div v-for="(itemIn, indexIn) in row[item.key]" :key="indexIn">
                    {{ itemIn.description }}: {{ itemIn.score }}星
                  </div>
                </div>
                <div v-else-if="item.data.question_type === 4">
                  {{ row[item.key].text }}
                </div>
                <div v-else-if="item.data.question_type === 5">
                  <el-button type="text" size="small" class="ps-text" @click="handleClick(row, item.key)">查看</el-button>
                </div>
                <div v-else>
                  <el-button type="text" size="small" class="ps-text" @click="downloadUrl(row, item.key)">下载</el-button>
                </div>
              </div>
              <div v-else>{{ row[item.key] || '--' }}</div>
            </template>
          </table-column>
        </el-table>

        <!-- 分页 start -->
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize" :page-sizes="[10, 20, 50, 100, 500, 1000]"
          :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount"></pagination>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { debounce, deepClone } from '@/utils'
import dayjs from 'dayjs'
import axios from 'axios'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  mixins: [exportExcel],
  components: {
    ElImageViewer
  },
  data() {
    return {
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '提交时间',
          value: [dayjs().subtract(1, 'year').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        },
        name: {
          type: 'input',
          value: '',
          label: '填写人',
          dataList: [],
          placeholder: '请输入',
          maxlength: 20,
          clearable: true
        },
        is_anonymous: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看匿名',
          value: false
        }
      },
      tableData: [],
      tableSetting: [],
      showImagePreview: false,
      printType: 'SurveyDetailData',
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.getDataList()
    },
    getDataList() {
      this.isLoading = true
      this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireAnswerDetailPost({
        id: this.$route.query.id,
        start_date: this.searchFormSetting.select_time.value[0],
        end_date: this.searchFormSetting.select_time.value[1],
        is_anonymous: this.searchFormSetting.is_anonymous.value || undefined,
        name: this.searchFormSetting.name.value || undefined,
        page: this.currentPage,
        page_size: this.pageSize
      }).then(res => {
        this.isLoading = false
        this.totalCount = res.data.count
        if (res.code === 0) {
          if (this.currentPage === 1) {
            this.tableSetting = []
            for (let key in res.data.results[0]) {
              let obj = { label: '', key: '', data: '', type: 'slot', slotName: key }
              obj.label = key !== 'number' && key !== 'commit_time' && key !== 'name' ? res.data.results[0][key].caption : res.data.results[0][key]
              obj.data = key !== 'number' && key !== 'commit_time' && key !== 'name' ? res.data.results[0][key] : {}
              obj.key = key
              this.tableSetting.push(obj)
            }
            this.moveToFront('commit_time')
            this.moveToFront('number')
            this.tableData = res.data.results.filter((item, index) => index >= 1)
          } else {
            this.tableData = deepClone(res.data.results)
          }
          console.log('tableSetting', this.tableSetting, this.tableData)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    moveToFront(key) {
      this.tableSetting.forEach((item, index) => {
        if (item.key === key) {
          this.tableSetting.unshift(this.tableSetting.splice(index, 1)[0])
        }
      })
    },
    async downloadUrl(data, key) {
      console.log('data', data)
      if (data[key].url.length > 0) {
        data[key].url.forEach(async (item) => {
          try {
            const response = await axios({
              url: item,
              method: 'GET',
              responseType: 'blob' // 重要：设置响应类型为 blob
            })
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a')
            const lastSlashIndex = item.lastIndexOf('/');
            const result = item.substring(lastSlashIndex + 1);
            link.href = url
            console.log('result', result)
            link.download = `${result}` // 设置下载的文件名
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          } catch (error) {
            console.error('下载失败:', error)
          }
        })
      } else {
        this.$message.warning('暂无附件')
      }
    },

    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value !== '全部' && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        } else {
          params[key] = undefined
        }
      }
      return params
    },
    gotoExport() {
      let option = {
        url: 'apiBackgroundFundSupervisionPublicityInfoQuestionnaireAnswerDetailExportPost',
        params: {}
      }
      option.params = {
        id: this.$route.query.id,
        ...this.formatQueryParams(this.searchFormSetting)
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const currentTableSetting = this.tableSetting
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: false,
          print_type: this.printType,
          print_title: '数据明细',
          result_key: 'data', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionPublicityInfoQuestionnaireAnswerDetailPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            id: this.$route.query.id,
            start_date: this.searchFormSetting.select_time.value[0],
            end_date: this.searchFormSetting.select_time.value[1],
            name: this.searchFormSetting.name.value || undefined,
            page: this.currentPage,
            page_size: this.pageSize
          }),
          isMerge: "0"
        }
      });
      window.open(href, "_blank");
    },
    handleClick(data, key) {
      if (data[key].url.length > 0) {
        this.previewList = deepClone(data[key].url)
        document.body.style.overflow = 'hidden'
        this.showImagePreview = true
      } else {
        this.$message.warning('暂无图片')
      }
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
