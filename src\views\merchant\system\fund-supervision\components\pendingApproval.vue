<template>
  <div>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false" @linkage="setAccountType" @reset="resetHandler"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="gotoExport" v-permission="['background_approve.approve_fund.list_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #accountFee="{ row }">
              {{ computedFee(row.account_fee) }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="getDetail(row)">详情</el-button>
              <el-button type="text" size="small" class="ps-text" @click="agreeHandle(row)" v-permission="['background_approve.approve_fund.agree_approve']">同意</el-button>
              <el-button type="text" size="small" class="ps-warn-text" @click="rejectHandle(row)" v-permission="['background_approve.approve_fund.reject_approve']">拒绝</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'详情'"
        :visible="detailDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="detailDrawerFormRef" :model="detailDrawerForm" label-width="80px" label-position="right">
            <el-form-item :label="'名称'">
              <div class="m-l-5">{{ detailDrawerForm.name }}</div>
            </el-form-item>
            <el-form-item :label="'申请单号'">
              <div class="m-l-5">{{ detailDrawerForm.application_no }}</div>
            </el-form-item>
            <el-form-item :label="'创建人'">
              <div class="m-l-5">{{ detailDrawerForm.founder }}</div>
            </el-form-item>
            <el-form-item :label="'所属组织'">
              <div class="m-l-5">{{ detailDrawerForm.org }}</div>
            </el-form-item>
            <el-form-item :label="'流水类别'">
              <div class="m-l-5">{{ detailDrawerForm.flow_type }}</div>
            </el-form-item>
            <el-form-item :label="'流水类型'">
              <div class="m-l-5">{{ detailDrawerForm.account_type }}</div>
            </el-form-item>
            <el-form-item :label="'记账日期'">
              <div class="m-l-5">{{ detailDrawerForm.account_date }}</div>
            </el-form-item>
            <el-form-item :label="'记账金额'">
              <div class="m-l-5">{{ computedFee(detailDrawerForm.account_price) }}</div>
            </el-form-item>
            <el-form-item :label="'备注'">
              <div class="m-l-5">{{ detailDrawerForm.remark }}</div>
            </el-form-item>
            <el-form-item :label="'附件'">
              <!-- {{ detailDrawerForm.fileList }} -->
              <div v-for="(item, index) in detailDrawerForm.fileList" :key="index" class="m-l-5">
                <div class="w-350 flex-b-c">
                  <div>
                    <i class="el-icon-document"></i>
                    <span>{{ item.name }}</span>
                  </div>
                  <el-button type="text" size="small" class="ps-text" @click="downloadUrl(item)">下载</el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item :label="'审批状态'">
              <el-timeline class="p-t-10 m-l-5">
                <el-timeline-item
                  :icon="item.icon"
                  :color="item.color"
                  :size="'large'"
                  v-for="(item, index) in detailDrawerForm.status"
                  :key="index"
                  :timestamp="item.status_alias"
                  :placement="'top'">
                  <div v-for="(itemIn, indexIn) in item.data" :key="indexIn" :class="[approveMethod === 'and_approve' && index !== 0 ? 'bg-grey' : '', 'm-b-10']">
                    <!--这里做个区别，会签和其他两个区别显示-->
                    <div v-if="approveMethod !== 'and_approve'" class="flex-col">
                      <div class="w-350 flex-b-c">
                        <div>{{ itemIn.operator }}</div>
                        <div class="w-150 flex-b-c" v-show="itemIn.status !== 'PENDING'">
                          <div v-show="itemIn.status !== 'PENDING'">{{ itemIn.timestamp }}</div>
                          <i :class="itemIn.icon" :style="{'color': itemIn.color, 'fontSize': '18px'}"></i>
                        </div>
                      </div>
                      <div v-show="itemIn.status === 'REJECT'" class="red">
                          拒绝原因：{{ item.reason }}
                        </div>
                    </div>
                    <div v-else>
                      <div v-for="(childItem, childIndex) in itemIn" :key="childIndex" class="flex-col">
                        <div class="w-350 flex-b-c">
                          <div>{{ childItem.operator }}</div>
                          <div class="w-150 flex-b-c" v-show="childItem.status !== 'PENDING'">
                            <div v-show="childItem.status !== 'PENDING'">{{ childItem.timestamp }}</div>
                            <i :class="[childItem.icon, 'icon']" :style="{'color': childItem.color, 'fontSize': '18px'}"></i>
                          </div>
                        </div>
                        <div v-show="childItem.status === 'REJECT'" class="red">
                          拒绝原因：{{ item.reason }}
                        </div>
                      </div>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="cancelHandle('detail')">关闭</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <el-dialog title="提示" :visible.sync="dialogFormVisible" width="25%">
      <div>
        确定拒绝该申请？
      </div>
      <el-form ref="dialogFormRef" :model="dialogForm" label-position="top">
        <el-form-item label="拒绝原因" prop="reason" :rules="{ required: true, message: '请填写拒绝原因', trigger: ['change', 'blur'] }">
          <el-input v-model="dialogForm.reason" type="textarea" placeholder='请输入拒绝原因' :autosize="{ minRows: 6, maxRows: 8}" resize="none" maxlength="100"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelHandle">取 消</el-button>
        <el-button type="primary" @click="rejectThis">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import { debounce, getRequestParams, deepClone, divide } from '@/utils'
import dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  mixins: [exportExcel],
  data() {
    const RECENTSEVEN = [
      dayjs()
        .subtract(7, 'day')
        .format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ]
    return {
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '申请时间',
          clearable: false,
          value: RECENTSEVEN
        },
        name: {
          type: 'input',
          label: '名称',
          value: '',
          placeholder: '请输入申请单名称'
        },
        organization_ids: {
          type: 'organizationSelect',
          value: [],
          label: '所属组织',
          placeholder: '请选择',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: [],
          multiple: true,
          checkStrictly: true,
          collapseTags: true,
          clearable: true
        },
        flow_type: {
          type: 'select',
          label: '流水类别',
          isLinkage: true,
          value: '',
          placeholder: '请选择流水类别',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '营业收入',
              value: 'operating_income'
            },
            {
              label: '非营业收入',
              value: 'non_operating_income'
            },
            {
              label: '营业成本',
              value: 'operating_cost'
            },
            {
              label: '非营业成本',
              value: 'non_operating_cost'
            }
          ]
        },
        account_type: {
          type: 'select',
          label: '流水类型',
          value: '',
          placeholder: '请选择流水类型',
          dataList: [],
          multiple: true,
          collapseTags: true,
          clearable: true,
          disabled: true
        }
      },
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '名称', key: 'name' },
        { label: '申请单号', key: 'application_no' },
        { label: '申请时间', key: 'create_time' },
        { label: '所属组织', key: 'org_name' },
        { label: '流水类别', key: 'flow_type_alias' },
        { label: '流水类型', key: 'account_type_alias' },
        { label: '记账日期', key: 'account_date' },
        { label: '记账金额', key: 'account_fee', type: "slot", slotName: "accountFee" },
        { label: '备注', key: 'remark', showTooltip: true },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "200" }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      detailDrawerShow: false,
      detailDrawerForm: {
        name: '',
        application_no: '',
        founder: '',
        org: '',
        flow_type: '',
        account_type: '',
        account_date: '',
        account_price: 0,
        remark: '',
        fileList: [],
        status: []
      },
      approveMethod: '',
      rejectId: '',
      dialogFormVisible: false,
      dialogForm: {
        reason: ''
      }
    }
  },
  computed: {
    computedFee() {
      return d => {
        return '￥' + divide(d, 100)
      }
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    resetHandler() {
      this.searchFormSetting.account_type.disabled = true
    },
    // select搜素联动（目前只做了select的，后续还有别的要在原组件上改动）
    setAccountType(e) {
      this.searchFormSetting.account_type.value = []
      if (e) {
        let newDataList = []
        switch (e) {
          case 'operating_income':
            newDataList = [
              {
                label: '储值消费',
                value: 'store_consume'
              },
              {
                label: '教师储值消费',
                value: 'teacher_store_consume'
              },
              {
                label: '补贴消费',
                value: 'subsidy_consume'
              },
              {
                label: '教师补贴消费',
                value: 'teacher_subsidy_consume'
              },
              {
                label: '第三方消费',
                value: 'third_consume'
              },
              {
                label: '其他收入',
                value: 'other_income'
              }
            ]
            break;
          case 'non_operating_income':
            newDataList = [
              {
                label: '政府补助',
                value: 'government_subsidy'
              },
              {
                label: '公益捐赠',
                value: 'public_welfare_donation'
              },
              {
                label: '其他收入',
                value: 'other_income'
              }
            ]
            break;
          case 'operating_cost':
            newDataList = [
              {
                label: '原材料成本',
                value: 'raw_material_cost'
              },
              {
                label: '水电气成本',
                value: 'utilities'
              },
              {
                label: '人工成本',
                value: 'labor_cost'
              },
              {
                label: '其他成本',
                value: 'other_costs'
              }
            ]
            break;
          case 'non_operating_cost':
            newDataList = [
              {
                label: '其他成本',
                value: 'other_costs'
              }
            ]
            break;
        }
        this.searchFormSetting.account_type.dataList = deepClone(newDataList)
        this.searchFormSetting.account_type.disabled = false
      } else {
        this.searchFormSetting.account_type.disabled = true
      }
    },
    getDataList() {
      this.isLoading = true
      let params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      params.approval_status = 'pending'
      this.$apis.apiBackgroundApproveApproveFundListPost(params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.tableData = deepClone(res.data.results || [])
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList()
    },
    gotoExport() {
      let params = getRequestParams(this.searchFormSetting, this.currentPage, this.totalCount)
      params.approval_status = 'pending'
      const option = {
        url: 'apiBackgroundApproveApproveFundListExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    getDetail(row) {
      this.$apis.apiBackgroundApproveApproveFundFundDetailsPost({
        id: row.fund_id
      }).then(res => {
        if (res.code === 0) {
          // 这里要区别依次审批与其他审批
          // 这里仅凭数组的长度去判断其实是很不合理的，但和陈老师商讨后确实是不知道该如何处理了，目前只能先这么判断着……
          let result = []
          if (res.data.length > 1) {
            // 大于一则是依次审批的
            result = res.data[res.data.length - 1]
          } else {
            result = res.data[0]
          }
          this.detailDrawerForm.name = result.name
          this.detailDrawerForm.application_no = result.application_no
          this.detailDrawerForm.org = result.org_name
          this.detailDrawerForm.founder = result.creator
          this.detailDrawerForm.flow_type = result.flow_type_alias
          this.detailDrawerForm.account_type = result.account_type_alias
          this.detailDrawerForm.account_date = result.account_date
          this.detailDrawerForm.account_price = result.account_fee
          this.detailDrawerForm.remark = result.remark
          this.approveMethod = result.approve_method
          // 处理文件
          if (result.image_json && result.image_json.length) {
            this.detailDrawerForm.fileList = result.image_json.map(item => {
              let fileName = item.substring(item.lastIndexOf('/') + 1)
              let isSplit = fileName.indexOf('?')
              if (isSplit > -1) {
                fileName = fileName.substring(0, isSplit)
              }
              return {
                name: fileName,
                url: item
              }
            })
          }
          // 处理状态得将之前的一起打包丢进去
          this.detailDrawerForm.status = [
            {
              icon: 'el-icon-check',
              color: '#14ce84',
              status_alias: '提交申请',
              status: 'pending',
              data: [
                {
                  icon: 'el-icon-success',
                  color: '#14ce84',
                  status_alias: '提交申请',
                  status: 'pending',
                  account_id: '',
                  timestamp: result.create_time,
                  operator: `${result.creator} (${result.username})`
                }
              ]
            }
          ]
          let newStatus = []
          // 先判断一下是不是或签，或签的话全部账号显示在一个流程里就可以了
          switch (result.approve_method) {
            case 'one_by_one_approve': {
              // 依次审批还是拿回approve_account_info组成数组显示吧
              // 先循环res.data
              res.data.forEach(item => {
                let obj = {
                  icon: 'el-icon-check',
                  color: '#ff9b45',
                  status_alias: '待审批',
                  status: 'pending',
                  reason: result.reject_reason || '',
                  data: []
                }
                let statusList = []
                if (item.approve_account_info && item.approve_account_info.length) {
                  // 将这个审批账号里的全部账号拉出来组成目标数据丢尽obj.data
                  item.approve_account_info.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name} (${itemIn.username})`
                    }
                    statusList.push(itemIn.approve_status)
                    obj.data.push(child)
                  })
                  let agreeFlag = statusList.some(item => item === 'AGREE')
                  let rejectFlag = statusList.some(item => item === 'REJECT')
                  // 把上传的obj根据里面的内容重新赋值一下
                  obj.icon = agreeFlag ? 'el-icon-check' : (rejectFlag ? 'el-icon-close' : 'el-icon-more')
                  obj.color = agreeFlag ? '#14ce84' : (rejectFlag ? '#fd594e' : '#ff9b45')
                  obj.status_alias = agreeFlag ? '审批通过' : (rejectFlag ? '拒绝审批' : '待审批')
                  obj.status = agreeFlag ? 'AGREE' : (rejectFlag ? 'REJECT' : 'PENDING')
                }
                newStatus.push(obj)
              })
              break
            }
            case 'and_approve': {
              // 如果是会签，将每个审批账号做成一个数组塞到data里面
              let obj = {
                icon: result.approval_status === 'AGREE' ? 'el-icon-check' : (result.approval_status === 'PENDING' ? 'el-icon-more' : 'el-icon-close'),
                color: this.switchColor(result.approval_status),
                status_alias: result.approval_status_alias,
                status: result.approval_status,
                reason: result.reject_reason || '',
                data: []
              }
              if (result.approve_account_info && result.approve_account_info.length) {
                result.extra.forEach(item => {
                  if (item.length) {
                    let arr = []
                    item.forEach(itemIn => {
                      let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                      let child = {
                        icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                        color: this.switchColor(itemIn.approve_status),
                        status_alias: itemIn.approve_status_alias,
                        status: itemIn.approve_status,
                        account_id: itemIn.account_id,
                        timestamp: itemIn.approve_time,
                        operator: `${itemIn.account_name} (${itemIn.username})`
                      }
                      arr.push(child)
                    })
                    obj.data.push(arr)
                  }
                })
                newStatus.push(obj)
              }
              break
            }
            case 'or_approve': {
              // 如果是或签，将所有账号放在一个流程内
              let obj = {
                icon: result.approval_status === 'AGREE' ? 'el-icon-check' : (result.approval_status === 'PENDING' ? 'el-icon-more' : 'el-icon-close'),
                color: this.switchColor(result.approval_status),
                status_alias: result.approval_status_alias,
                status: result.approval_status,
                reason: result.reject_reason || '',
                data: []
              }
              if (result.approve_account_info && result.approve_account_info.length) {
                result.extra.forEach(item => {
                  if (item.length) {
                    item.forEach(itemIn => {
                      let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                      let child = {
                        icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                        color: this.switchColor(itemIn.approve_status),
                        status_alias: itemIn.approve_status_alias,
                        status: itemIn.approve_status,
                        account_id: itemIn.account_id,
                        timestamp: itemIn.approve_time,
                        operator: `${itemIn.account_name} (${itemIn.username})`
                      }
                      obj.data.push(child)
                    })
                  }
                })
                newStatus.push(obj)
              }
              break
            }
          }
          this.addRejectStatus(result, newStatus)
          if (this.approveMethod !== 'and_approve') {
            this.detailDrawerForm.status.push(...newStatus)
          } else {
            let obj = deepClone(this.detailDrawerForm.status[0])
            obj.data = [[obj.data[0]]]
            this.detailDrawerForm.status = [obj, ...newStatus]
          }
          console.log('this.detailDrawerForm', this.detailDrawerForm)
          this.detailDrawerShow = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    addRejectStatus(data, statusArr) {
      console.log(data, statusArr)
      // 处理状态
      if (data.approval_status === 'REVOKE') {
        let obj = {
          icon: 'el-icon-error',
          color: '#909399',
          status_alias: '撤销申请',
          status: 'REVOKE',
          timestamp: data.create_time,
          operator: `${data.creator} (${data.username})`
        }
        let status = {
          icon: 'el-icon-close',
          color: '#909399',
          status_alias: '撤销申请',
          status: 'REVOKE',
          data: []
        }
        // 用历史操作处理旧数据
        let record = []
        if (data.approve_record && data.approve_record.record && data.approve_record.record.length) {
          record = deepClone(data.approve_record.record)
        }
        // 如果是撤销的，直接塞
        switch (data.approve_method) {
          case 'one_by_one_approve': {
            // 先把最后一个干掉
            statusArr.pop()
            statusArr.forEach(item => {
              let approvalStatusArr = []
              item.data.forEach(itemIn => {
                let obj = record.filter(recordItem => recordItem.account_id === itemIn.account_id)
                if (obj.length) {
                  // 如果有就改
                  let childStatus = obj[0].status === 'PENDING' || obj[0].status === 'AGREE'
                  itemIn.icon = childStatus ? 'el-icon-success' : 'el-icon-error'
                  itemIn.color = this.switchColor(obj[0].status)
                  itemIn.status_alias = obj[0].content
                  itemIn.status = obj[0].status
                  itemIn.timestamp = obj[0].time
                } else {
                  // 没有就置空
                  itemIn.icon = ''
                  itemIn.timestamp = ''
                }
                approvalStatusArr.push(itemIn.status)
              })
              // 根据statusArr里的状态去判断
              let flag = approvalStatusArr.some(item => item === 'REJECT')
              // 审批账号里面的改好了，轮到该审批账号本身的状态了
              item.icon = flag ? 'el-icon-close' : 'el-icon-check'
              item.color = flag ? this.switchColor('') : this.switchColor('AGREE')
              item.status_alias = flag ? '' : '审批通过'
              item.status = flag ? '' : 'AGREE'
            })
            // 依次审批的话因为最后一个审批账号没审就撤销了，把最后那个干掉
            status.data = [{ ...obj }]
            statusArr.push(status)
            break
          }
          case 'and_approve': {
            statusArr[0].data.forEach(item => {
              item.forEach(itemIn => {
                console.log(itemIn, record)
                let obj = record.filter(recordItem => recordItem.account_id === itemIn.account_id)
                if (obj.length) {
                  // 如果有就改
                  itemIn.icon = obj[0].status === 'AGREE' ? 'el-icon-success' : 'el-icon-error'
                  itemIn.color = this.switchColor(obj[0].status)
                  itemIn.status_alias = obj[0].content
                  itemIn.status = obj[0].status
                  itemIn.timestamp = obj[0].time
                } else {
                  // 没有就置空
                  itemIn.icon = ''
                  itemIn.timestamp = ''
                }
              })
            })
            // 审批账号里面的改好了，轮到该审批账号本身的状态了
            statusArr[0].icon = 'el-icon-more'
            statusArr[0].color = this.switchColor('PENDING')
            statusArr[0].status_alias = '待审批'
            statusArr[0].status = 'PENDING'
            status.data = [[{ ...obj }]]
            statusArr.push(status)
            break
          }
          case 'or_approve':
            // 先把最后一个干掉
            statusArr.pop()
            status.data = [{ ...obj }]
            statusArr.push(status)
            break
        }
      }
    },
    switchColor(status) {
      let color = ''
      switch (status) {
        case 'PENDING':
          color = '#ff9b45'
          break
        case 'AGREE':
          color = '#14ce84'
          break
        case 'REJECT':
          color = '#fd594e'
          break
        case 'pending':
          color = '#ff9b45'
          break
        case 'agreed':
          color = '#14ce84'
          break
        case 'rejected':
          color = '#fd594e'
          break
        default:
          color = '#909399'
      }
      return color
    },
    agreeHandle(row) {
      this.$confirm('确定同意该申请', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$apis.apiBackgroundApproveApproveFundAgreeApprovePost({
          id: row.approve_process_id
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.msg)
          }
          this.getDataList()
        })
      }).catch(() => {
        this.$message('已取消操作')
      })
    },
    rejectHandle(row) {
      this.rejectId = row.approve_process_id
      this.dialogFormVisible = true
    },
    rejectThis() {
      this.$refs.dialogFormRef.validate((valid) => {
        if (valid) {
          this.$apis.apiBackgroundApproveApproveFundRejectApprovePost({
            id: this.rejectId,
            reject_reason: this.dialogForm.reason
          }).then(res => {
            if (res.code === 0) {
              this.$refs.dialogFormRef.resetFields()
              this.dialogFormVisible = false
              this.$message.success('操作成功')
            } else {
              this.$message.error(res.msg)
            }
            this.getDataList()
          })
        }
      })
    },
    cancelHandle(type) {
      if (type === 'detail') {
        this.detailDrawerShow = false
      } else {
        this.$refs.dialogFormRef.resetFields()
        this.dialogFormVisible = false
      }
    },
    async downloadUrl(data) {
      try {
        const response = await axios({
          url: data.url,
          method: 'GET',
          responseType: 'blob' // 重要：设置响应类型为 blob
        })
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a')
        link.href = url
        link.download = `${data.name}` // 设置下载的文件名
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (error) {
        console.error('下载失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-grey {
  padding: 10px 20px;
  border: 1px solid #E7E9EF;
  border-radius: 4px;
}
.icon {
  font-size: 18px;
}
::v-deep .el-timeline-item__node--large {
  left: -4px;
  width: 18px !important;
  height: 18px !important;
}
</style>
