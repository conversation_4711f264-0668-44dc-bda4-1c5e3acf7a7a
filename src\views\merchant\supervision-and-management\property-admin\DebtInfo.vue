<template>
  <!-- 晨检记录-->
  <div class="PropertyInfo container-wrapper">
    <div class="tab-box">
      <el-radio-group v-model="tabType" class="ps-radio-btn">
        <el-radio-button
          v-for="tab in tabTypeList"
          :key="tab.value"
          :label="tab.value"
          v-permission="[tab.permissions]"
        >
          {{ tab.label }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <debt-info v-if="tabType === 'info'" />
    <repayment-info v-if="tabType === 'repaymentInfo'" />
    <debt-statistics v-if="tabType === 'statistics'" />
  </div>
</template>

<script>
import DebtInfo from './components/DebtInfo.vue'
import RepaymentInfo from './components/RepaymentInfo.vue'
import DebtStatistics from './components/DebtStatistics.vue'

export default {
  components: {
    DebtInfo,
    RepaymentInfo,
    DebtStatistics
  },
  name: 'property-admin-info',
  data() {
    return {
      tabType: 'info',
      tabTypeList: [
        { label: '负债信息表', value: 'info', permissions: 'background_fund_supervision.asset.liability_info_list' },
        {
          label: '还款信息表',
          value: 'repaymentInfo',
          permissions: 'background_fund_supervision.asset.repayment_info_list'
        },
        {
          label: '负债统计表',
          value: 'statistics',
          permissions: 'background_fund_supervision.asset.liability_info_statistics_list'
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.PropertyInfo {
  padding-top: 20px;
}
</style>
