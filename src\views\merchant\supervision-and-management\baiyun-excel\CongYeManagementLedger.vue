<!--从业人员健康管理台账-->
<template>
  <div class="chen-jian container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"
      :autoSearch="false" @reset="resetHandler"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="clickHistoricalRecords">历史记录</button-icon>
          <button-icon color="origin" @click="handlerShowDialog('add', null)" v-permission="['background_fund_supervision.ledger_food_safety.morning_check_config_add']">疾病特征</button-icon>
          <button-icon color="origin" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="gotoExport" v-permission="['background_fund_supervision.ledger_food_safety.get_morning_check_ledger_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe :key="tableKey"
          header-row-class-name="ps-table-header-row" >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operatorUsername="{ row }">
               <div>{{ getOperatorUsername(row) }}</div>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-origin" @click="gotoDetail(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
         <div class="ps-origin m-t-10">“√”：无疾病体征者</div>
         <div class="ps-origin">“-” ：当日不排班者</div>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount">
        </pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 添加弹窗 -->
    <configuration-add-edit-drawer ref="configurationAddEditDrawer" @confirm="confirmRecordDialog" @close="closeDialog" :isshow="dialogVisible" :type="dialogType" :drawerData="drawerData" :keyType="keyType" />
     <!-- 历史记录抽屉 -->
     <HistoryRecordDrawer
      :visible.sync="isShowRecordDialog"
      title="历史记录"
      api="apiBackgroundFundSupervisionLedgerFoodSafetyOperateLogList"
      type="Disease"
      @close="isShowRecordDialog = false"
    >
      <!-- 操作前内容插槽 -->
      <!-- <template #beforeContent="{ row }">
        <div>{{ row.beforeData }}</div>
      </template> -->
      <!-- 操作后内容插槽 -->
      <!-- <template #afterContent="{ row }">
        <div>{{ row.afterData }}</div>
      </template> -->
    </HistoryRecordDrawer>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import { SEARCH_SETTING_EMPLOYEES_CHEN_JIAN_BIAO, TABLE_HEAD_DATA_EMPLOYEES_CHEN_JIAN_BIAO } from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report'
import ConfigurationAddEditDrawer from './compontents/ConfigurationAddEditDrawer.vue'
import HistoryRecordDrawer from './compontents/HistoryRecordDrawer.vue'

export default {
  name: 'GuominyuanManagementLedger',
  mixins: [exportExcel, report],
  components: {
    ConfigurationAddEditDrawer,
    HistoryRecordDrawer
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_EMPLOYEES_CHEN_JIAN_BIAO), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_EMPLOYEES_CHEN_JIAN_BIAO), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_EMPLOYEES_CHEN_JIAN_BIAO), // 查询表单配置
      printType: 'CongYeManagementLedger', // 类型
      isShowRecordDialog: false, // 历史记录
      dialogType: '', // 弹窗类型
      dialogTitle: '', // 弹窗标题
      dialogVisible: false, // 弹窗是否显示
      drawerData: {}, // 弹窗数据
      keyType: 'Disease', // 配置项类型
      diseaseList: [], // 疾病特征
      tableKey: 0 // 表格key
    }
  },
  created() {
    const date = this.$route.query.date
    if (date) {
      this.searchFormSetting.select_time.value = date
    }
    this.initLoad()
  },
  mounted() { },
  methods: {
    async initLoad() {
      this.currentPage = 1
      // 根据选择的月份，设置表格的
      let currentMonth = this.searchFormSetting.select_time?.value || ''
      this.diseaseList = await this.getConfigList()
      this.setTableHeader(currentMonth)
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      console.log('searchHandle', e)
      if (e && e === 'search') {
        this.currentPage = 1
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.month = data[key].value
          }
        }
      }
      return params
    },
    // 获取提交记录
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetEmployeesMorningCheckLedger(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        let results = data.results || []
        if (results && results.length > 0) {
          results = results.map((item, index) => {
            item.order_no = index + 1
            if (typeof item.date === 'object' && Object.keys(item.date).length > 0) {
              for (let key in item.date) {
                let currentdate = new Date().getTime()
                let nowDate = this.searchFormSetting.select_time?.value + '-' + (key < 10 ? '0' + key : key)
                let nowDateTime = new Date(nowDate).getTime()
                item[key] = item.date[key] ? item.date[key] : nowDateTime > currentdate ? ' ' : '-'
              }
            }
            return item
          })
        }
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log('handleSelectionChange', val)
      this.selectedIds = val.map(item => item.id)
      this.chooseData = deepClone(val)
    },
    // 查看详情
    gotoDetail(data) {
      console.log('gotoDetail', data)
      this.dialogType = 'detail'
      this.dialogTitle = '详情'
      this.drawerData = deepClone(data || {})
      this.dialogVisible = true
    },
    // 导出弹窗
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetMorningCheckLedgerExport',
        params: params
      }
      this.exportHandle(option)
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.tableSetting)
      tableSetting = tableSetting.filter(item => item.key !== 'operation')
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: false,
          print_type: this.printType,
          print_title: '从业人员健康管理台账',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetEmployeesMorningCheckLedger', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    },
    // 点击历史记录
    clickHistoricalRecords() {
      this.isShowRecordDialog = true
    },
    // 添加弹窗
    handlerShowDialog(type, data) {
      this.dialogType = type
      this.dialogTitle = type === 'add' ? '添加' : '编辑'
      if (type === 'add') {
        this.drawerData = {}
      } else {
        this.drawerData = deepClone(data || {})
      }
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
    },
    // 确认
    confirmRecordDialog(data) {
      console.log('confirmRecordDialog', data)
      this.dialogVisible = false
      this.initLoad()
    },
    // 获取操作员名称
    getOperatorUsername(row) {
      let operatorMemberName = row.operator_member_name || ''
      let operatorUsername = row.operator_username || ''
      return operatorMemberName + (operatorUsername ? `(${operatorUsername})` : '')
    },
    // 获取配置项
    getConfigList() {
      return new Promise((resolve) => {
        let params = {
          ledger_data_type: this.keyType
        }
        this.isLoading = true
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetLedgerItemConfig(params).then(res => {
          this.isLoading = false
          console.log('res', res)
          if (res && res.code === 0) {
            let data = res.data || []
            if (data && data.length > 0) {
              const tag = data[0] || {}
              const ledgerItemConfig = tag.ledger_item_config || []
              let newList = []
              if (ledgerItemConfig && ledgerItemConfig.length > 0) {
                ledgerItemConfig.forEach((item, index) => {
                  newList.push({
                    value: item + (index + 1)
                  })
                })
              }
              resolve(newList)
            } else {
              resolve([])
            }
          } else {
            this.$message.error(res.msg || '获取配置项失败')
            resolve(false)
          }
        }).catch(err => {
          this.isLoading = false
          this.$message.error(err.message || '获取配置项失败')
          resolve(false)
        })
      })
    },
    // 设置表格头
    setTableHeader(currentMonth) {
      let newTableSetting = deepClone(this.tableSetting)
      newTableSetting.forEach(item => {
        if (item.key === 'disease') {
          item.label = this.diseaseList ? this.diseaseList.map(item => item.value).join('、') : ''
          // 根据当前的月份获取天数，比如2025-08，则天数为31
          let currentDate = new Date(currentMonth)
          currentDate.setMonth(currentDate.getMonth() + 1)
          currentDate.setDate(0)
          let day = currentDate.getDate()
          console.log("day", day)
          item.children = []
          for (let i = 1; i <= day; i++) {
            item.children.push({
              label: i + "",
              key: i + ""
            })
          }
        }
      })
      this.currentTableSetting = newTableSetting
      this.tableSetting = newTableSetting
      this.tableKey++
    }
  }
}
</script>
<style lang="scss" scoped>
.chen-jian {
  ::v-deep .el-dialog {
    .el-dialog__body {
      padding: 0 20px !important;
    }
  }

  .dialog-content {
    padding: 10px 0;
    text-align: left;

    .dialog-content-title {
      margin-bottom: 15px;
      font-size: 14px;
    }

    .reject-reason {
      text-align: left;

      .label {
        margin-bottom: 8px;
        font-size: 14px;

        &.required::before {
          content: '*';
          color: #F56C6C;
          margin-right: 4px;
        }
      }
    }
  }
}
</style>
