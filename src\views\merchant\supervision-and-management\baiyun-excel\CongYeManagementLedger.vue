<template>
  <div class="container-wrapper">
    <div class="employee-health-ledger">
      <h2 class="table-title">从业人员健康体检登记表</h2>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="department" label="部门" width="120" align="center"></el-table-column>
        <el-table-column prop="idCard" label="入职日期" width="120" align="center"></el-table-column>
        <el-table-column prop="idCard" label="身份证号码" width="120" align="center"></el-table-column>
        <el-table-column prop="name" label="姓名" width="120" align="center"></el-table-column>
        <el-table-column prop="healthStatus" label="体检合格情况" align="center"></el-table-column>
        <el-table-column prop="certValidDate" label="健康证明有效期" align="center"></el-table-column>
        <el-table-column prop="leaveDate" label="离职日期" align="center"></el-table-column>
        <el-table-column prop="remarks" label="备注" align="center"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CongYeManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      totalItems: 0,
      tableData: [
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        },
        {
          department: '食堂厨房',
          name: '',
          healthStatus: '',
          certValidDate: '',
          leaveDate: '',
          remarks: ''
        }
      ],
      allData: [] // 存储所有数据，用于分页展示
    }
  },
  created() {
    this.loadFullData();
  },
  methods: {
    loadFullData() {
      // 这里可以添加所有表格数据，或者从API获取
      this.allData = this.tableData;
      this.totalItems = this.allData.length;
      this.updatePageData();
    },
    updatePageData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.tableData = this.allData.slice(start, end);
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.updatePageData();
    },
    handleCurrentChange(page) {
      this.currentPage = page;
      this.updatePageData();
    },
    cellStyle() {
      return {
        padding: '8px 5px',
        fontSize: '13px'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '10px 5px'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.employee-health-ledger {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: bold;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .date-field {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
