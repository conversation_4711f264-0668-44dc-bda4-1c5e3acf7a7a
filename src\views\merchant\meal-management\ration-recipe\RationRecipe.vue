<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="true"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="drawerShow = true">设置食谱</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #date="{ row }">
              {{ row.week_start_date }}~{{ row.week_end_date }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" @click="checkRecipe(row)" :disabled="!allPermissions.includes('background_fund_supervision.ration_recipe.view_recipe')">查看食谱</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>

    <div class="ps-el-drawer">
      <el-drawer
        :title="'设置食谱'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <span class="red font-size-14">
            注意：带量食谱的菜品只取设置菜品价格【菜品/商品价格】的数据，还需要配置食材否则无法显示相关数据，如果已生成了带量食谱的数据，修改菜谱只会更新当天至本周日的带量食谱数据。关闭或删除食谱会清空当天至本周日的带量食谱数据。
          </span>
          <el-form ref="drawerForm" :model="drawerForm" :rules="rules" label-width="80px">
            <el-form-item label="食谱类型" prop="recipeType">
              <el-radio-group v-model="drawerForm.recipeType" @input="setRecipeList">
                <el-radio :label="'week'">周食谱</el-radio>
                <el-radio :label="'month'">月食谱</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="选择食谱" prop="selectRecipe">
              <el-select
                v-model="drawerForm.selectRecipe"
                placeholder="选择食谱"
                :filterable="true"
                :loading="drawerLoading"
                :disabled="drawerLoading"
                @change="setSelectRecipeData"
                class="w-220">
                <el-option
                  v-for="(item, index) in recipeList"
                  :key="index"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="使用期限" prop="timeRange">
              <div>
                <span>期限结束后带量食谱信息需再次配置。</span>
                <div>
                  <el-date-picker
                    class="w-220 m-r-10"
                    v-model="drawerForm.timeRange"
                    type="date"
                    placeholder="选择结束日期"
                    value-format="yyyy-MM-dd"
                    :disabled="drawerForm.isUse"
                    :picker-options="pickerOptions">
                  </el-date-picker>
                  <el-checkbox v-model="drawerForm.isUse">长期使用</el-checkbox>
                </div>
              </div>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button @click="cancelHandle">取消</el-button>
            <el-button
              class="ps-btn"
              type="primary"
              @click="saveHandle"
            >
              保存
            </el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'带量食谱'"
        :visible="rationRecipeShow"
        :show-close="false"
        size="60%">
        <div class="p-20">
          <div class="w-100-p ps-flex-align-c flex-right m-b-20" v-permission="['background_fund_supervision.ration_recipe.export_recipe']">
            <el-button @click="gotoExport">导出</el-button>
          </div>
          <custom-table
            border
            :table-data="rationRecipeTableData"
            :table-setting="rationRecipeTableSetting"
            ref="rationRecipeTableData"
            style="width: 100%"
            stripe
            :span-method="objectSpanMethod"
            header-row-class-name="ps-table-header-row"
          />
          <div class="ps-el-drawer-footer">
            <el-button @click="rationRecipeShow = false">关闭</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { mergeHandle, mergeRowAction } from '@/utils/table'
import { debounce, deepClone, divide } from '@/utils'
import { MEAL_TYPES, WEEK_LIST } from '@/utils/constants'
import dayjs from 'dayjs';
import { mapGetters } from 'vuex';
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  mixins: [exportExcel],
  data() {
    const checkTimeRange = (rule, value, callback) => {
      if (!this.drawerForm.isUse) {
        if (!value) {
          return callback(new Error('请选择日期'))
        } else {
          return callback()
        }
      } else {
        return callback()
      }
    }
    return {
      searchFormSetting: {
        select_time: {
          label: '日期筛选',
          type: 'week',
          value: '',
          clearable: true,
          pickerOptions: {
            firstDayOfWeek: 1
          }
        },
        recipe_name: {
          type: 'select',
          value: '',
          clearable: true,
          label: '食谱',
          listNameKey: 'recipe_name',
          listValueKey: 'recipe_name',
          filterable: true,
          multiple: false,
          collapseTags: true,
          dataList: []
        },
        org_id: {
          type: 'organizationSelect',
          value: '',
          label: '所属组织',
          listNameKey: 'name',
          listValueKey: 'id',
          clearable: false,
          checkStrictly: true,
          multiple: false,
          collapseTags: true
        }
      },
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '日期', key: 'date', type: 'slot', slotName: 'date' },
        { label: '食谱名称', key: 'recipe_name' },
        { label: '所属组织', key: 'org_name' },
        { label: '使用期限', key: 'service_life' },
        { label: '操作时间', key: 'update_time' },
        { label: '操作人', key: 'operator_name' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      drawerShow: false,
      drawerLoading: false,
      drawerForm: {
        recipeType: 'week',
        selectRecipe: '',
        timeRange: '',
        isUse: false
      },
      rules: {
        recipeType: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
        selectRecipe: [{ required: true, message: '请选择食谱', trigger: ['blur', 'change'] }],
        timeRange: [{ validator: checkTimeRange, trigger: ['blur', 'change'] }]
      },
      recipeList: [],
      selectRecipeData: {},
      rationRecipeShow: false,
      rationRecipeTableData: [],
      rationRecipeTableSetting: [
        { label: '日期', key: 'date' },
        { label: '餐次', key: 'meal_period_type' },
        { label: '食物名称', key: 'food_name' },
        { label: '菜品重量/份', key: 'food_weight' },
        { label: '菜品价格/份', key: 'food_price' },
        { label: '配料', key: 'ingredients' },
        { label: '食材重量', key: 'ingredients_weight' }
      ],
      mergeOpts: {
        useKeyList: {
          date: ['meal_period_type'],
          meal_period_type: ['food_name', 'food_price']
        }, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        mergeKeyList: ['date'] // 通用的合并字段，根據值合并
      },
      rowMergeArrs: [],
      selectedId: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < dayjs().subtract(1, 'day').valueOf()
        }
      }
    }
  },
  watch: {
    drawerShow(val) {
      if (val) {
        this.setRecipeList('week')
      }
    },
    'drawerForm.isUse': {
      handler: function(newVal) {
        this.$refs.drawerForm.clearValidate()
      },
      deep: true
    }
  },
  computed: {
    ...mapGetters([
      'organization',
      'allPermissions'
    ])
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.searchFormSetting.org_id.value = this.organization
      this.setSettingData()
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      this.currentPage = 1;
      this.getDataList()
    }, 300),
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.getDataList()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList()
    },
    setSettingData() {
      let params = {
        page: 1,
        page_size: 9999,
        orgs_id: this.organization
      }
      this.$apis.apiBackgroundFundSupervisionRationRecipeListPost(params).then(res => {
        if (res.code === 0) {
          this.searchFormSetting.recipe_name.dataList = deepClone(res.data.results)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getDataList() {
      this.isLoading = true
      let date = dayjs(this.searchFormSetting.select_time.value).format('YYYY-MM-DD')
      let startDate = dayjs(date).startOf('week').add(1, 'day').format('YYYY-MM-DD')
      let endDate = dayjs(date).endOf('week').add(1, 'day').format('YYYY-MM-DD')
      let params = {
        page: this.currentPage,
        page_size: this.pageSize,
        start_date: this.searchFormSetting.select_time.value ? startDate : undefined,
        end_date: this.searchFormSetting.select_time.value ? endDate : undefined,
        orgs_id: this.searchFormSetting.org_id.value,
        recipe_name: this.searchFormSetting.recipe_name.value || undefined
      }
      this.$apis.apiBackgroundFundSupervisionRationRecipeListPost(params).then(res => {
        if (res.code === 0) {
          this.isLoading = false
          this.totalCount = res.data.count
          this.tableData = deepClone(res.data.results)
        } else {
          this.$message.error(res.msg)
        }
      })
    },

    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            console.log('返回看看1', mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex))
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        console.log('返回看看2', mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex))
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    // 根据类型获取list
    setRecipeList(e) {
      this.drawerLoading = true
      this.$apis.apiBackgroundFundSupervisionRationRecipeGetOrgMenuPost({
        menu_type: e
      }).then(res => {
        if (res.code === 0) {
          this.drawerLoading = false
          this.recipeList = deepClone(res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    setSelectRecipeData(e) {
      let arr = this.recipeList.filter(item => item.id === e)
      this.selectRecipeData = arr[0]
    },
    saveHandle() {
      this.$refs.drawerForm.validate((valid) => {
        if (valid) {
          this.$confirm('保存后立即生成本周食谱数据，生成数据不可删除，如本周已有数据，保存设置会更新当天至周日的数据（历史数据除外），确定要保存？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false
          }).then(() => {
            let params = {
              menu_type: this.drawerForm.recipeType,
              limit_date: this.drawerForm.isUse ? undefined : this.drawerForm.timeRange,
              long_user: this.drawerForm.isUse,
              menu: this.selectRecipeData.id,
              recipe_name: this.selectRecipeData.name
            }
            this.$apis.apiBackgroundFundSupervisionRationRecipeSetRecipeTaskPost(params).then(res => {
              if (res.code === 0) {
                this.$message.success('保存成功')
                this.cancelHandle()
                this.getDataList()
              } else {
                this.$message.error(res.msg)
              }
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
        } else {
          this.$message.error('检查表单是否填写正确')
        }
      })
    },
    cancelHandle() {
      this.$refs.drawerForm.resetFields()
      this.drawerShow = false
    },
    checkRecipe(data) {
      this.selectedId = data.id
      this.isLoading = true
      this.$apis.apiBackgroundFundSupervisionRationRecipeViewRecipePost({
        id: this.selectedId
      }).then(res => {
        if (res.code === 0) {
          this.isLoading = false
          let foodData = res.data
          let list = []
          for (const key in foodData) {
            for (const mealKey in foodData[key]) {
              if (foodData[key][mealKey].length) {
                foodData[key][mealKey].forEach(item => {
                  let info = {
                    date: '',
                    meal_period_type: '',
                    food_name: '',
                    food_weight: '',
                    food_price: '',
                    ingredients: '',
                    ingredients_weight: ''
                  }
                  let week = WEEK_LIST.filter(item => item.value === dayjs(key).day())[0].label
                  info.date = `${dayjs(key).format('YYYY年MM月DD日')} ${week}`
                  info.meal_period_type = MEAL_TYPES.filter(item => item.value === mealKey)[0].label
                  info.food_name = item.food_name
                  info.food_weight = `${item.weight}g`
                  info.food_price = `${divide(item.food_price, 100)}元`
                  if (item.ingredient_info_list.length) {
                    // 找食材的数据，依次
                    item.ingredient_info_list.forEach(itemIn => {
                      let newObj = deepClone(info)
                      newObj.ingredients = itemIn.ingredient_name
                      newObj.ingredients_weight = `${itemIn.ingredient_weight}g`
                      list.push(newObj)
                    })
                  } else {
                    info.ingredients = '--'
                    info.ingredients_weight = '--'
                    list.push(info)
                  }
                })
              }
            }
          }
          this.rationRecipeTableData = deepClone(list)
          console.log('看看数据', this.rationRecipeTableData)
          this.rowMergeArrs = mergeHandle(this.rationRecipeTableData, this.mergeOpts)
          this.rationRecipeShow = true
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    gotoExport() {
      let params = {
        id: this.selectedId
      }
      const option = {
        url: 'apiBackgroundFundSupervisionRationRecipeExportRecipePost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
