<template>
  <el-drawer
    class="ps-el-drawer"
    :title="title"
    :visible="showDrawer"
    :show-close="false"
    :wrapper-closable="false"
    @close="showDrawer = false"
    append-to-body
    :size="'820px'">
    <div class="AddMealPackageRule p-20" v-if="type !== 'history'">
      <el-form ref="formRef" :rules="mealFormRule" :model="mealFormData" label-width="120px">
        <el-form-item label="餐包名称：" prop="name">
          <el-input v-model="mealFormData.name" placeholder="请输入餐包名称" maxlength="20" class="w-350"></el-input>
        </el-form-item>
        <el-form-item label="关联规则：" prop="mealReportRule">
          <el-select
            v-model="mealFormData.mealReportRule"
            placeholder="请选择报餐规则"
            class="ps-select w-350"
            :disabled="mealFormData.ruleStatus"
            @change="changeMealType"
            popper-class="ps-popper-select"
          >
            <el-option
              v-for="(item, index) in mealReportRuleList"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="餐段：" prop="mealType">
          <el-select
            v-model="mealFormData.mealType"
            placeholder="请选择餐段"
            class="ps-select w-350"
            :disabled="mealFormData.ruleStatus"
            multiple
            popper-class="ps-popper-select"
          >
            <el-option
              v-for="(item, index) in mealTypeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="消费点：" prop="organization">
          <el-select
            v-model="mealFormData.organization"
            placeholder="请选择消费点"
            class="ps-select w-350"
            popper-class="ps-popper-select"
          >
            <el-option
              v-for="(item, index) in mealOrganizationList"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配餐日期：" prop="mealDate">
          <el-date-picker
            v-model="mealFormData.mealDate"
            :disabled="mealFormData.ruleStatus"
            type="daterange"
            :picker-options="pickerOptions"
            unlink-panels
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            clearable
            class="ps-poper-picker w-350"
            @change="fixedDaysChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item prop="tollType">
          <span slot="label">
            收费类型
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                固定收费：用户购买时，按餐包创建的日期、餐段，进行收费；
                <br/>
                实时收费：用户购买时，需减去当前已结束的日期、餐段，再进行收费。
              </div>
              <i class="el-icon-info"></i>
            </el-tooltip>：
          </span>
          <el-radio-group class="ps-radio" v-model="mealFormData.tollType">
            <el-radio label="fixed">固定收费</el-radio>
            <el-radio label="real_time">实时收费</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="购买日期：" prop="buyDate">
          <el-date-picker
            v-model="mealFormData.buyDate"
            :disabled="mealFormData.ruleStatus"
            type="daterange"
            :picker-options="pickerOptions"
            unlink-panels
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            clearable
            class="ps-poper-picker w-350"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="停餐/恢复：" required>
          <div>
            <span>开餐前</span>
            <span class="m-l-20">小时内，不可申请停餐</span>
          </div>
          <div class="fake-table-wrapper">
            <div class="fake-table">
              <div class="fake-col" v-for="mt in allMealTypeList" :key="mt.value">
                <span class="fake-col-title">{{ mt.label }}</span>
                <el-input-number
                  controls-position="right"
                  v-model="mealFormData[mt.field]"
                  :min="0"
                  :disabled="!mealFormData.mealType.includes(mt.value)"
                  size="mini"
                  style="width: 86px; margin: 10px;"
                ></el-input-number>
              </div>
            </div>
          </div>
          <div class="m-t-15">
            <span>开餐前</span>
            <span class="m-l-20">小时内，不可申请恢复就餐</span>
          </div>
          <div class="fake-table-wrapper">
            <div class="fake-table">
              <div class="fake-col" v-for="mt in allMealTypeList" :key="mt.value">
                <span class="fake-col-title">{{ mt.label }}</span>
                <el-input-number
                  controls-position="right"
                  v-model="mealFormData[mt.field2]"
                  :min="0"
                  :disabled="!mealFormData.mealType.includes(mt.value)"
                  size="mini"
                  style="width: 86px; margin: 10px;"
                ></el-input-number>
              </div>
            </div>
          </div>
          <el-form-item label="是否需要审批" label-width="100px" class="m-t-20">
            <el-switch v-model="mealFormData.isCancelApprove" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item v-if="mealFormData.isCancelApprove" label="自动审批" label-width="100px" class="m-t-20">
            <el-switch v-model="mealFormData.isAutoApprove" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            <el-form-item v-if="mealFormData.isAutoApprove" label="开餐前" label-width="65px" class="m-t-20" prop="autoApproveStop">
              <el-input v-model="mealFormData.autoApproveStop" placeholder="" maxlength="20" class="w-80 m-r-5"></el-input>
              <span>小时，停餐申请自动同意</span>
            </el-form-item>
            <el-form-item v-if="mealFormData.isAutoApprove" label="开餐前" label-width="65px" class="m-t-20" prop="autoApproveResume">
              <el-input v-model="mealFormData.autoApproveResume" placeholder="" maxlength="20" class="w-80 m-r-5"></el-input>
              <span>小时，恢复就餐自动同意</span>
            </el-form-item>
          </el-form-item>
        </el-form-item>
        <el-form-item label="退费设置：" prop="refundType">
          <el-radio-group class="ps-radio" v-model="mealFormData.refundType">
            <el-radio label="manual">仅手动退</el-radio>
            <el-radio label="meal_end">餐段结束后退</el-radio>
            <el-radio label="specify_date">指定日期退</el-radio>
            <el-radio label="immediate_refund">立即退款</el-radio>
          </el-radio-group>
          <div class="refund-type-wrap" v-if="mealFormData.refundType === 'meal_end'">
            <span class="m-r-10">餐段结束后 D+</span>
            <el-form-item label="" prop="refundMealDay" class="form-content-inline">
              <el-input v-model="mealFormData.refundMealDay" maxlength="20" class="w-100"></el-input>
            </el-form-item>
            <span class="m-l-10">天退回餐费</span>
            <div class="tips">注：餐段结束后的D+N天的凌晨统一退款</div>
          </div>
          <div class="refund-type-wrap" v-if="mealFormData.refundType === 'specify_date'">
            <div>
              <span>餐段结束后</span>
              <el-form-item class="form-content-inline" prop="refundFixedType">
                <el-select
                  v-model="mealFormData.refundFixedType"
                  class="ps-select w-100 m-l-10 m-r-10"
                >
                  <el-option label="每周" value="week"></el-option>
                  <el-option label="每月" value="month"></el-option>
                  <el-option label="指定" value="fixed"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="form-content-inline" v-if="mealFormData.refundFixedType === 'week'" prop="weekDays">
                <el-select
                  v-model="mealFormData.weekDays"
                  class="ps-select w-150 m-r-10"
                  multiple
                  :collapse-tags="true"
                >
                <el-option
                  v-for="(item, index) in weekDayList"
                  :key="index"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="form-content-inline" v-if="mealFormData.refundFixedType === 'month'" prop="monthDays">
                <el-select
                  v-model="mealFormData.monthDays"
                  class="ps-select w-150 m-r-10"
                  multiple
                  :collapse-tags="true"
                >
                <el-option
                  v-for="(item, index) in monthDayList"
                  :key="index"
                  :label="item === -1 ? '每月最后一天' : item + '号'"
                  :value="item"
                ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="form-content-inline m-r-10" v-if="mealFormData.refundFixedType === 'fixed'" prop="fixedDays">
                <el-button type="primary" icon="el-icon-date" class="hidden-picker ps-origin-btn">
                  选择日期
                  <el-date-picker
                    type="dates"
                    :clearable="false"
                    v-model="mealFormData.fixedDays"
                    placeholder="选择一个或多个日期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    popper-class="hidden-picker-year"
                    :picker-options="pickerOptions"
                    @change="fixedDaysChange"
                  ></el-date-picker>
                </el-button>
              </el-form-item>
              <span>退回餐费</span>
            </div>
            <div class="ps-flex" v-if="mealFormData.refundFixedType === 'fixed'">
              <transition-group name="el-zoom-in-center" class="fixed-day" tag="div">
                <div class="fixed-day-time" v-for="(item, index) in mealFormData.fixedDays" :key="item">
                  {{item}}
                  <div class="del-time" @click="delFixedDay(index)">
                    <i class="el-icon-error"></i>
                  </div>
                </div>
              </transition-group>
              <el-button v-if="mealFormData.fixedDays.length" type="text" @click="clearFixedDay" class="m-t-8">清空</el-button>
            </div>
            <div v-if="mealFormData.overDaysTextTips" class="tips">提示：当前最后退款日期低于配餐结束日期，{{mealFormData.overDaysTextTips}}的退费订单需进行手动退款</div>
          </div>
          <div class="tips" v-if="mealFormData.refundType === 'immediate_refund'">注：立即退款则表示用户提交停餐申请通过后，即刻对订单发起退款。</div>
          <div class="tips">注：实际产生退费后，用户无法再重新购买同个餐包 (整个餐包退费除外)</div>
        </el-form-item>
        <el-form-item label="状态：">
          <el-switch v-model="mealFormData.ruleStatus" @change="changeRuleStatus" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <div v-if="mealFormData.ruleStatus">
          <el-form-item label="用餐天数：">{{mealFormData.mealDays}}</el-form-item>
          <el-form-item label="用餐份数：">{{mealFormData.mealNum}}</el-form-item>
          <el-form-item label="餐包价格：">￥{{mealFormData.allFee}}</el-form-item>
        </div>
        <el-form-item label="备注：" prop="remark">
          <el-input
            class="ps-input w-350"
            v-model="mealFormData.remark"
            type="textarea"
            :rows="3"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="ps-el-drawer-footer">
        <el-button size="small" class="w-100" @click="cancelHandle">取消</el-button>
        <el-button size="small" type="primary" class="w-100" @click="confirmHandle">保存</el-button>
      </div>
    </div>
    <div v-else class="p-20 m-t-30">
      <el-table
        :data="operationList"
        ref="tableData"
        style="width: 100%"
        stripe
        header-row-class-name="ps-table-header-row"
      >
      <el-table-column prop="update_time" label="操作时间" align="center" width="200"></el-table-column>
      <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
      <el-table-column prop="remark" label="操作内容" align="center"></el-table-column>
      </el-table>
      <div class="ps-el-drawer-footer">
        <el-button size="small" class="w-100" @click="showDrawer = false">关闭</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { mapGetters } from 'vuex'
import { MEAL_TYPES } from '../constant'
import { divide, times } from '@/utils'
import { confirm } from '@/utils/message'
export default {
  name: 'AddMealPackageRule',
  // mixins: [activatedLoadData],
  components: {
  },
  props: {
    show: {
      required: true,
      type: Boolean
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'add'
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    },
    confirm: Function,
    cancel: Function
  },
  data() {
    let validBuyDate = (rule, value, callback) => {
      if (value.length && this.mealFormData.mealDate.length) {
        if (this.mealFormData.tollType === 'real_time' && value[1] >= this.mealFormData.mealDate[1]) {
          callback(new Error('购买日期的截止时间不可大于或等于配餐日期的结束时间'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    let validMealDate = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择配餐日期'))
      } else if (value.length && this.mealFormData.buyDate.length) {
        if (this.mealFormData.tollType === 'real_time' && value[1] < this.mealFormData.buyDate[1]) {
          callback(new Error('配餐日期的结束时间不可小于购买日期的截止时间'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    let validRefundMealDay = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请填写退款时间'))
      } else {
        if (!/^[0-9]+$/.test(value)) {
          callback(new Error('请填写正整数'))
        } else if (value < 1) {
          callback(new Error('不能小于1'))
        } else {
          callback()
        }
      }
    }
    let validHourNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请填写小时数'))
      } else {
        if (!/^[0-9]+$/.test(value)) {
          callback(new Error('请填写正整数'))
        } else if (value < 1) {
          callback(new Error('不能小于1'))
        } else {
          callback()
        }
      }
    }
    return {
      isLoading: false, // 刷新数据
      mealFormData: {
        name: "",
        mealReportRule: "",
        mealType: [],
        organization: '',
        mealDate: [],
        tollType: '',
        buyDate: [],
        breakfast_ahead_stop: 0, // 开餐前(早餐)不可申请停餐
        lunch_ahead_stop: 0, // 开餐前(午餐)
        afternoon_ahead_stop: 0, // 开餐前(下午茶)
        dinner_ahead_stop: 0, // 开餐前(晚餐)
        supper_ahead_stop: 0, // 开餐前(夜宵)
        morning_ahead_stop: 0, // 开餐前(凌晨餐)
        breakfast_ahead_resume: 0, // 开餐前(早餐)不可申请恢复就餐
        lunch_ahead_resume: 0,
        afternoon_ahead_resume: 0,
        dinner_ahead_resume: 0,
        supper_ahead_resume: 0,
        morning_ahead_resume: 0,
        isCancelApprove: false,
        isAutoApprove: false,
        autoApproveStop: '',
        autoApproveResume: '',
        refundType: 'manual',
        refundMealDay: '', // 餐段结束后退
        refundFixedType: 'week', // 指定退
        fixedDays: [], // 指定日期
        overDaysTextTips: '', // 低于配餐结束日期时提示
        monthDays: [], // 指定哪天
        weekDays: [], // 指定周
        ruleStatus: false,
        addDate: [],
        mealDays: "",
        mealNum: "",
        allFee: "",
        remark: ""
      },
      mealFormRule: {
        name: [{ required: true, message: '请输入餐包名称', trigger: 'blur' }],
        mealReportRule: [{ required: true, message: '请选择报餐规则', trigger: 'change' }],
        mealType: [{ required: true, message: '请选择餐段', trigger: 'change' }],
        organization: [{ required: true, message: '请选择消费点', trigger: 'change, blur' }],
        mealDate: [{ required: true, validator: validMealDate, trigger: 'blur' }],
        tollType: [{ required: true, message: '请选择收费类型', trigger: 'change, blur' }],
        autoApproveStop: [{ required: true, validator: validHourNumber, trigger: 'blur' }],
        autoApproveResume: [{ required: true, validator: validHourNumber, trigger: 'blur' }],
        refundType: [{ required: true, message: '请选择退费类型', trigger: 'change, blur' }],
        buyDate: [{ validator: validBuyDate, trigger: 'blur' }],
        refundMealDay: [{ validator: validRefundMealDay, trigger: 'blur' }],
        weekDays: [{ required: true, message: '请选择', trigger: 'change' }],
        monthDays: [{ required: true, message: '请选择', trigger: 'change' }],
        fixedDays: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      weekDayList: [{
        key: 1,
        value: '周一'
      }, {
        key: 2,
        value: '周二'
      }, {
        key: 3,
        value: '周三'
      }, {
        key: 4,
        value: '周四'
      }, {
        key: 5,
        value: '周五'
      }, {
        key: 6,
        value: '周六'
      }, {
        key: 7,
        value: '周日'
      }],
      monthDayList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, -1],
      pickerOptions: {
        disabledDate(time) {
          // if (time.getTime() < new Date(new Date().setHours(0, 0, 0, 0))) return true
          if (time.getTime() > new Date().getTime() + 179 * 24 * 3600 * 1000) return true // 今天起180天内
        }
      },
      mealReportRuleList: [],
      mealTypeList: MEAL_TYPES,
      allMealTypeList: MEAL_TYPES,
      mealOrganizationList: [],
      operationList: []
    }
  },
  created() {
  },
  computed: {
    ...mapGetters(['userInfo']),
    showDrawer: {
      get() {
        if (this.show) {
          this.initLoad()
        }
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  watch: {
    show(val) {
      if (val) {
        if (this.type === 'history') {
          this.getOperationList()
        } else if (this.type === 'add') {
          this.getMealReportList()
        } else if (this.type === 'edit') {
          this.getMealReportList('edit')
        }
      }
    }
  },
  methods: {
    async initLoad() {
      this.resetForm()
      if (this.type === 'edit') {
        this.initEditForm()
      }
    },
    initEditForm() {
      this.mealFormData.name = this.infoData.name
      this.mealFormData.mealReportRule = this.infoData.report_meal_settings
      this.mealFormData.mealType = this.infoData.meal_type
      this.mealFormData.organization = this.infoData.organization
      this.mealFormData.mealDate = [this.infoData.start_date, this.infoData.end_date]
      this.mealFormData.tollType = this.infoData.toll_type
      if (this.infoData.buy_start_date && this.infoData.buy_end_date) {
        this.mealFormData.buyDate = [this.infoData.buy_start_date, this.infoData.buy_end_date]
      } else {
        this.mealFormData.buyDate = []
      }
      this.mealFormData.isCancelApprove = this.infoData.is_approve
      this.mealFormData.isAutoApprove = this.infoData.auto_approve
      this.mealFormData.refundType = this.infoData.refund_type
      this.mealFormData.ruleStatus = this.infoData.is_enable
      this.mealFormData.addDate = this.infoData.all_date
      this.mealFormData.mealDays = this.infoData.meal_days
      this.mealFormData.mealNum = this.infoData.meal_num
      this.mealFormData.allFee = divide(this.infoData.fee)
      this.mealFormData.remark = this.infoData.remark

      this.allMealTypeList.map(item => {
        this.mealFormData[item.field] = this.infoData[item.field]
        this.mealFormData[item.field2] = this.infoData[item.field2]
      })
      if (this.infoData.auto_approve) {
        this.mealFormData.autoApproveStop = this.infoData.auto_stop_ahead
        this.mealFormData.autoApproveResume = this.infoData.auto_resume_ahead
      }
      if (this.infoData.refund_type === 'meal_end') {
        this.mealFormData.refundMealDay = this.infoData.manual_day
      } else if (this.infoData.refund_type === 'specify_date') {
        this.mealFormData.refundFixedType = this.infoData.specify_date_json.specify_type
        if (this.infoData.specify_date_json.specify_type === 'week') {
          this.mealFormData.weekDays = this.infoData.specify_date_json.week
        } else if (this.infoData.specify_date_json.specify_type === 'month') {
          this.mealFormData.monthDays = this.infoData.specify_date_json.days
        } else if (this.infoData.specify_date_json.specify_type === 'fixed') {
          this.mealFormData.fixedDays = this.infoData.specify_date_json.days
        }
      }
    },
    async changeRuleStatus(e) {
      if (!e) return
      this.mealFormData.ruleStatus = false
      if (e && (!this.mealFormData.mealReportRule)) {
        this.mealFormData.ruleStatus = false
        return this.$message.error("请选择关联的报餐规则")
      }
      if (e && !this.mealFormData.mealType.length) {
        this.mealFormData.ruleStatus = false
        return this.$message.error("请选择餐段")
      }
      if (e && !this.mealFormData.mealDate.length) {
        this.mealFormData.ruleStatus = false
        return this.$message.error("请选择配餐日期")
      }
      const res = await this.$apis.apiBackgroundReportMealReportMealPackSettingsGetReportMealSettingsDayPost({
        report_meal_settings_id: this.mealFormData.mealReportRule,
        meal_type_list: this.mealFormData.mealType,
        start_date: this.mealFormData.mealDate[0],
        end_date: this.mealFormData.mealDate[1]
      })
      if (res.code === 0) {
        this.mealFormData.addDate = res.data.all_date
        this.mealFormData.mealDays = res.data.meal_days
        this.mealFormData.mealNum = res.data.meal_num
        this.mealFormData.allFee = divide(res.data.fee)
        this.mealFormData.ruleStatus = true
      } else {
        this.$message.error(res.msg)
      }
    },
    confirmHandle() {
      // 增加判断是否超过180天
      if (!this.checkMealDateLimit()) {
        return
      }
      if (this.mealFormData.mealNum >= 100 && this.mealFormData.mealNum < 200) {
        confirm({
          content: `当前餐包用餐份数过多，预计用户支付时需等待10s，建议分多餐包创建`,
          confirmButtonText: '继续创建'
        }).then(_ => {
          this.saveSetting()
        }).catch(e => {
          if (e === 'cancel') {
            console.log('cancel')
          }
        })
      } else if (this.mealFormData.mealNum >= 200) {
        confirm({
          content: `当前餐包用餐份数过多，预计用户支付时需等待20s，建议分多餐包创建`,
          confirmButtonText: '继续创建'
        }).then(_ => {
          this.saveSetting()
        }).catch(e => {
          if (e === 'cancel') {
            console.log('cancel')
          }
        })
      } else {
        this.saveSetting()
      }
    },
    cancelHandle() {
      this.$emit('cancel')
      this.$refs.formRef.resetFields()
    },
    saveSetting() {
      this.$refs.formRef.validate(valid => {
        console.log("saveSetting", valid)
        if (valid) {
          if (this.isLoading) return
          let api
          let params = {
            name: this.mealFormData.name,
            report_meal_settings: this.mealFormData.mealReportRule,
            meal_type: this.mealFormData.mealType,
            organization: this.mealFormData.organization,
            start_date: this.mealFormData.mealDate[0],
            end_date: this.mealFormData.mealDate[1],
            toll_type: this.mealFormData.tollType,
            refund_type: this.mealFormData.refundType,
            is_approve: this.mealFormData.isCancelApprove,
            auto_approve: this.mealFormData.isAutoApprove,
            is_enable: this.mealFormData.ruleStatus,
            remark: this.mealFormData.remark
          }
          // 购买日期的
          if (this.mealFormData.buyDate.length) {
            params.buy_start_date = this.mealFormData.buyDate[0]
            params.buy_end_date = this.mealFormData.buyDate[1]
          }
          // 餐段停餐/恢复
          this.allMealTypeList.map(item => {
            params[item.field] = this.mealFormData[item.field]
            params[item.field2] = this.mealFormData[item.field2]
          })
          // 审批的
          if (this.mealFormData.isAutoApprove) {
            params.auto_stop_ahead = this.mealFormData.autoApproveStop
            params.auto_resume_ahead = this.mealFormData.autoApproveResume
          }
          // 退费的
          if (this.mealFormData.refundType === 'meal_end') { // 餐段结束后退
            params.manual_day = this.mealFormData.refundMealDay
          } else if (this.mealFormData.refundType === 'specify_date') { // 指定日期
            params.specify_date_json = {
              specify_type: this.mealFormData.refundFixedType
            }
            if (this.mealFormData.refundFixedType === 'week') {
              params.specify_date_json.week = this.mealFormData.weekDays
            } else if (this.mealFormData.refundFixedType === 'month') {
              params.specify_date_json.days = this.mealFormData.monthDays
            } else if (this.mealFormData.refundFixedType === 'fixed') {
              params.specify_date_json.days = this.mealFormData.fixedDays
            }
          }
          // 自动生成的
          if (this.mealFormData.addDate.length) params.all_date = this.mealFormData.addDate
          if (this.mealFormData.mealDays) params.meal_days = this.mealFormData.mealDays
          if (this.mealFormData.mealNum) params.meal_num = this.mealFormData.mealNum
          if (this.mealFormData.allFee) params.fee = times(this.mealFormData.allFee)
          if (this.type === 'edit') {
            params.id = this.infoData.id
            api = this.$apis.apiBackgroundReportMealReportMealPackSettingsModifyPost
          } else {
            api = this.$apis.apiBackgroundReportMealReportMealPackSettingsAddPost
          }
          this.addAndEditMealPackageRule(api, params)
        } else {
          console.log(valid)
        }
      })
    },
    async addAndEditMealPackageRule(api, params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirm', res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    async getMealReportList(type) {
      const res = await this.$apis.apiBackgroundReportMealReportMealSettingsListPost({
        page: 1,
        page_size: 999,
        order_type: 'meal_pack',
        is_open: true
      })
      if (res.code === 0) {
        this.mealReportRuleList = res.data.results
        this.totalCount = res.data.count
        if (type === 'edit') this.changeMealType()
      } else {
        this.$message.error(res.msg)
      }
    },
    changeMealType() {
      let info = this.mealReportRuleList.find(item => item.id === this.mealFormData.mealReportRule)
      this.mealOrganizationList = []
      info.report_organizations.map((org, index) => {
        this.mealOrganizationList.push({
          id: org,
          name: info.report_organization_alias[index]
        })
      })
      this.mealTypeList = []
      info.meal_type_detail.meal_type.map((meal, index) => {
        this.mealTypeList.push({
          value: meal,
          label: info.meal_type_detail.meal_type_verbose[index]
        })
      })
    },
    // 单个删除
    delFixedDay(index) {
      this.mealFormData.fixedDays.splice(index, 1)
    },
    // 清除全部
    clearFixedDay() {
      this.mealFormData.fixedDays = []
    },
    // 当选择最晚的日期低于配餐结束日期时，需给出对应提示
    fixedDaysChange() {
      const maxDate = this.mealFormData.fixedDays.reduce((max, current) => {
        return new Date(current).getDate() > new Date(max).getDate() ? current : max;
      }, this.mealFormData.fixedDays[0]);
      if (new Date(this.mealFormData.mealDate[1]).getDate() > new Date(maxDate).getDate()) {
        if (new Date(this.mealFormData.mealDate[0]).getDate() > new Date(maxDate).getDate()) {
          this.mealFormData.overDaysTextTips = `${this.mealFormData.mealDate[0]}至${this.mealFormData.mealDate[1]}`
        } else {
          this.mealFormData.overDaysTextTips = `${maxDate}至${this.mealFormData.mealDate[1]}`
        }
      } else if (new Date(this.mealFormData.mealDate[1]).getDate() === new Date(maxDate).getDate()) {
        this.mealFormData.overDaysTextTips = this.mealFormData.mealDate[1]
      } else {
        this.mealFormData.overDaysTextTips = ''
      }
      console.log(new Date(this.mealFormData.mealDate[1]).getDate(), new Date(maxDate).getDate())
    },
    async getOperationList() {
      const res = await this.$apis.apiBackgroundReportMealReportMealPackSettingsOperationListPost({
        report_meal_pack_settings_id: this.infoData.id
      })
      if (res.code === 0) {
        this.operationList = res.data.results
        this.showDrawer = true
      } else {
        this.$message.error(res.msg)
      }
    },
    // 检测日期是否超过180天
    checkMealDateLimit() {
      if (this.mealFormData.mealDate) {
        let startDate = new Date(this.mealFormData.mealDate[0])
        let endDate = new Date(this.mealFormData.mealDate[1])
        if (endDate.getTime() - startDate.getTime() > 179 * 24 * 60 * 60 * 1000) {
          this.$message.error('配餐日期不能超过180天')
          return false
        }
      }
      return true
    },
    resetForm() {
      this.mealFormData = {
        name: "",
        mealReportRule: "",
        mealType: [],
        organization: '',
        mealDate: [],
        tollType: '',
        buyDate: [],
        breakfast_ahead_stop: 0, // 开餐前(早餐)不可申请停餐
        lunch_ahead_stop: 0, // 开餐前(午餐)
        afternoon_ahead_stop: 0, // 开餐前(下午茶)
        dinner_ahead_stop: 0, // 开餐前(晚餐)
        supper_ahead_stop: 0, // 开餐前(夜宵)
        morning_ahead_stop: 0, // 开餐前(凌晨餐)
        breakfast_ahead_resume: 0, // 开餐前(早餐)不可申请恢复就餐
        lunch_ahead_resume: 0,
        afternoon_ahead_resume: 0,
        dinner_ahead_resume: 0,
        supper_ahead_resume: 0,
        morning_ahead_resume: 0,
        refundType: 'manual',
        refundMealDay: '', // 餐段结束后退
        refundFixedType: 'week', // 指定退
        fixedDays: [], // 指定日期
        overDaysTextTips: '', // 低于配餐结束日期时提示
        monthDays: [], // 指定哪天
        weekDays: [], // 指定周
        ruleStatus: false,
        addDate: [],
        mealDays: "",
        mealNum: "",
        allFee: "",
        remark: ""
      }
    }
  }
}
</script>

<style lang="scss">
.AddMealPackageRule {
  // .ps-el-drawer{
  //   z-index: 2001;
  //   position: relative;
  // }
  .tips {
    margin-top: 15px;
    color: #f41818;
    font-size: 14px;
    line-height: 20px;
  }
  .fake-table-wrapper {
    display: flex;
    align-items: flex-end;
    .fake-table {
      display: flex;
      // width: 600px;
      border: 1px solid #ddd;
      .fake-col {
        display: flex;
        align-items: center;
        flex-direction: column;
        // width: 130px;
        border-right: 1px solid #ddd;

        .fake-col-title {
          display: block;
          width: 100%;
          border-bottom: 1px solid #ddd;
          text-align: center;
        }
      }
    }
  }
  .refund-type-wrap{
    background-color: #f2f2f2;
    border-radius: 8px;
    padding: 20px 15px;
  }
  .form-content-inline{
    display: inline-block;
    .el-form-item__content{
      display: inline-block;
    }
  }
  .fixed-day {
    padding-top: 10px;
    .fixed-day-time {
      display: inline-block;
      margin-right: 30px;
      font-size: 15px;
      position: relative;
      .del-time {
        position: absolute;
        top: -8px;
        right: -12px;
        cursor: pointer;
      }
    }
  }
  .hidden-picker {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    .el-date-editor {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      .el-input__inner {
        padding: 0 !important;
      }
      opacity: 0;
    }
  }
}
</style>
