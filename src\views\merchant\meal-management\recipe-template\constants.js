// 弹窗表单配置文件
export const DIALOGfORMSETTING = {
  // 块级，为数组，子元素为inline-block模式
  DH: {
    label: '大荤',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  XH: {
    label: '小荤',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  BH: {
    label: '半荤',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  QS: {
    label: '全素',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  JBZS: {
    label: '基本主食',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  HYZS: {
    label: '花样主食',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  DP: {
    label: '蛋品',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  DX: {
    label: '点心',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  XC: {
    label: '小吃',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  RP: {
    label: '乳品',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  YP: {
    label: '饮品',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  },
  SG: {
    label: '水果',
    default: '',
    class: 'inline-block',
    format: 'input',
    unit: '道'
  }
}
