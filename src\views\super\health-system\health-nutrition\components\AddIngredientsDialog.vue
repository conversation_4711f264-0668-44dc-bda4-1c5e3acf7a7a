<template>
  <custom-drawer :show="visible" :title="type === 'add' ? '添加食材' : '编辑食材'" size="60%" @close="handleClose"
    @cancel="handleClose" @confirm="handleSubmit" :loading="loading" :confirmShow="true" :cancelShow="true"
    confirm-text="保存" cancel-text="取消" v-bind="$attrs" v-on="$listeners" :wrapperClosable="false">
    <div class="add-ingredients-form">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="90px" v-loading="loading">
        <div class="form-section">
          <!-- 食材名称 -->
          <el-form-item label="食材名称" prop="name" class="full-width">
            <el-input v-model="form.name" placeholder="请输入食材名称" style="width: 400px"></el-input>
          </el-form-item>

          <!-- 食材别名 -->
          <el-form-item label="食材别名" class="full-width">
            <div class="alias-input-group">
              <div v-for="(alias, index) in form.alias_name" :key="index" class="alias-item">
                <el-input v-model="form.alias_name[index]" placeholder="请输入食材别名" class="alias-input"></el-input>
                <img src="@/assets/img/plus.png" @click="addAlias" alt="">
                <img src="@/assets/img/reduce_red.png" @click="removeAlias(index)" alt="" class="m-l-10" v-if="index > 0" />
              </div>
              <!-- <el-button type="text" @click="addAlias" icon="el-icon-plus">添加别名</el-button> -->
            </div>
          </el-form-item>

          <!-- 食材图片 -->
          <el-form-item label="食材图片" prop="image" class="full-width upload-hidden">
            <div class="image-upload-box">
              <div class="upload-tip">仅支持jpg、png、bmp格式，大小不超过5M</div>
              <el-upload :class="{ 'file-upload': true, 'hide-upload': form.imageList && form.imageList.length > 0 }" ref="fileUpload"
                drag :action="serverUrl" :data="uploadParams" :file-list="form.imageList"
                :on-success="handleImageSuccess" :on-change="handelChange" :before-upload="beforeImageUpload" :limit="1"
                list-type="picture-card" :multiple="false" :headers="headersOpts" accept=".jpeg,.jpg,.png,.bmp">
                <div class="upload-t" v-if="!form.imageList || form.imageList.length < 1">
                  <i class="el-icon-circle-plus"></i>
                  <div class="el-upload__text">
                    上传食材图片
                  </div>
                </div>
                <div slot="file" slot-scope="{file}" v-loading="file.status === 'uploading'" element-loading-text="上传中">
                  <div class="upload-food-img">
                    <el-image class="el-upload-dragger" :src="file.url" fit="cover">
                    </el-image>
                  </div>
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span class="el-upload-list__item-delete" @click="handleImgRemove(file, 'foodImages')">
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
              <!-- <el-upload
                :class="{ 'file-upload': true, 'hide-upload': form.image.length > 0 }"
                  :action="serverUrl"
                  :show-file-list="false"
                  :headers="headersOpts"
                :data="uploadParams"
                :on-success="handleImageSuccess"
                :before-upload="beforeImageUpload"
                 :multiple="false"
                :limit="1"
                list-type="picture-card"
                accept=".jpg,.jpeg,.png,.bmp"
              >
                <img v-if="form.image" :src="form.image" class="avatar" />
                <div v-else class="upload-placeholder">
                  <i class="el-icon-circle-plus"></i>
                  <div class="upload-text">上传食材图片</div>
                </div>
                <div slot="file" slot-scope="{file}" v-loading="file.status === 'uploading'"
                    element-loading-text="上传中">
                    <div class="upload-food-img">
                      <el-image class="el-upload-dragger" :src="file.url" fit="contain">
                      </el-image>
                    </div>
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span class="el-upload-list__item-delete" @click="handleImgRemove(file, 'foodImages')">
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
              </el-upload> -->
            </div>
          </el-form-item>

          <!-- 食材分类 -->
          <el-form-item label="食材分类" prop="sort_ids" class="full-width">
            <el-cascader v-if="visible" v-model="form.sort_ids" ref="cascaderRef" :options="categoryOptions" :props="{
              checkStrictly: false,
              label: 'name',
              value: 'id',
              children: 'sort_list',
              emitPath: false
            }" placeholder="请选择食材分类"></el-cascader>
          </el-form-item>

          <!-- 食材应季 -->
          <el-form-item label="食材应季" prop="seasonal_month" class="full-width">
            <el-select v-model="form.seasonal_month" multiple placeholder="请选择应季月份">
              <el-option v-for="item in monthOptions" :key="item.id" :label="item.month_name"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>

          <!-- 生熟比 -->
          <el-form-item label="生熟比" class="full-width" required>
            <div class="ratio-section">
              <div class="ratio-title">生熟比（生重/熟重*100%）：{{ getRatio }}%</div>
              <div class="weight-input-group">
                <div class="weight-item">
                  <span class="weight-label">食材生重：</span>
                  <el-input v-model="form.raw_weight" placeholder="请输入生重" @input="handleWeightInput('raw_weight')"
                    style="width: 120px">
                    <template slot="append">g</template>
                  </el-input>
                </div>
                <div class="weight-item">
                  <span class="weight-label">食材熟重：</span>
                  <el-input v-model="form.cooked_weight" placeholder="请输入熟重" @input="handleWeightInput('cooked_weight')"
                    style="width: 120px">
                    <template slot="append">g</template>
                  </el-input>
                </div>
              </div>
            </div>
          </el-form-item>

          <!-- 食材标签 -->
          <el-form-item label="食材标签" prop="labels">
            <el-cascader v-if="visible" v-model="form.selectedLabels" :options="labelOptions" :props="{
              value: 'id',
              label: 'name',
              children: 'label_list',
              multiple: true,
              checkStrictly: false,
              emitPath: false
            }" placeholder="请选择标签" clearable @change="handleLabelChange" :collapse-tags="true" />
            <div class="selected-tags" v-if="selectedTags.length">
              <el-tag v-for="tag in selectedTags" :key="tag.id" closable class="tag-item" @close="handleRemoveTag(tag)">
                {{ tag.name }}
              </el-tag>
            </div>
          </el-form-item>

          <!-- 食材营养 -->
          <el-form-item label="食材营养" prop="seasonal_month" class="full-width">
            <div class="ps-red">
              （每100g食材的营养）
              <el-switch v-model="form.is_enable_nutrition" active-color="#ff9b51" inactive-color="#dcdfe6" />
            </div>
          </el-form-item>
          <div class="nutrition-section">
            <div class="nutrition-grid" v-if="form.is_enable_nutrition">
              <template v-for="nutrition in nutritionList">
                <el-form-item :key="nutrition.key" :label="`${nutrition.name}（${nutrition.unit}）`"
                  :prop="`nutrition_info.${nutrition.key}`" label-width="130px" :rules="rules.nutrition_info_value">
                  <el-input style="width: 120px" v-model="form.nutrition_info[nutrition.key]" placeholder="请输入"
                    @input="handleNutritionInput(nutrition.key)"></el-input>
                </el-form-item>
              </template>
            </div>
          </div>
        </div>
      </el-form>
      <el-dialog :visible.sync="dialogVisible" append-to-body>
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </custom-drawer>
</template>

<script>
import { deepClone, getToken } from '@/utils'
import { NUTRITION_LIST } from '../../health-nutrition/constants'

export default {
  name: 'AddIngredientsDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'add'
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    // 自定义验证规则
    const validateWeightRatio = (rule, value, callback) => {
      if (!this.form.raw_weight || !this.form.cooked_weight) {
        callback(new Error('请输入生重和熟重'))
        return
      }

      const raw = parseFloat(this.form.raw_weight)
      const cooked = parseFloat(this.form.cooked_weight)

      if (isNaN(raw) || isNaN(cooked)) {
        callback(new Error('请输入有效的数字'))
        return
      }

      if (raw <= 0 || cooked <= 0) {
        callback(new Error('生重和熟重必须大于0'))
        return
      }

      if (cooked > raw) {
        callback(new Error('熟重不能大于生重'))
        return
      }
      callback()
    }

    const validateNutrition = (rule, value, callback) => {
      if (value === '') {
        callback()
        return
      }

      const num = parseFloat(value)
      if (isNaN(num)) {
        callback(new Error('请输入数字'))
        return
      }

      if (num < 0) {
        callback(new Error('请输入大于等于0的数字'))
        return
      }

      // 检查小数位数
      const decimalPlaces = value.toString().split('.')[1]
      if (decimalPlaces && decimalPlaces.length > 2) {
        callback(new Error('最多输入两位小数'))
        return
      }

      callback()
    }

    return {
      loading: false,
      form: {
        name: '',
        alias_name: [''], // 初始提供一个空的别名输入框
        sort_ids: [],
        seasonal_month: [],
        raw_weight: '100',
        cooked_weight: '100',
        image: '',
        labels: [],
        nutrition_info: {},
        selectedLabels: [],
        is_enable_nutrition: false,
        imageList: []
      },
      rules: {
        name: [{ required: true, message: '请输入食材名称', trigger: 'blur' }],
        sort_ids: [{ required: true, message: '请选择食材分类', trigger: 'change' }],
        raw_weight: [
          { required: true, message: '请输入食材生重', trigger: 'blur' },
          { validator: validateWeightRatio, trigger: 'blur' }
        ],
        cooked_weight: [
          { required: true, message: '请输入食材熟重', trigger: 'blur' },
          { validator: validateWeightRatio, trigger: 'blur' }
        ],
        labels: [],
        nutrition_info_value: [{ validator: validateNutrition, trigger: 'blur' }]
      },
      categoryOptions: [],
      monthOptions: [],
      nutritionList: deepClone(NUTRITION_LIST),
      labelOptions: [],
      selectedTags: [],
      serverUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      uploadParams: {
        prefix: 'super_food_img'
      },
      dialogImageUrl: '',
      dialogVisible: false
    }
  },

  computed: {
    // 计算生熟比
    getRatio() {
      const raw = parseFloat(this.form.raw_weight)
      const cooked = parseFloat(this.form.cooked_weight)

      if (!raw || !cooked || isNaN(raw) || isNaN(cooked)) return '0.00'

      return ((raw / cooked) * 100).toFixed(2)
    }
  },

  watch: {
    visible(val) {
      console.log('visible', val)
      if (val) {
        this.initData()
      } else {
        console.log('关闭')
        this.resetForm()
      }
    }
  },

  created() {
    this.getCategoryOptions()
    this.getMonthOptions()
    this.initNutritionInfo()
    this.fetchLabelOptions()
    this.initFormRules()
  },

  methods: {
    // 初始化数据
    async initData() {
      // 获取标签数据
      await this.fetchLabelOptions()

      if (this.type === 'modify' && this.editData) {
        console.log('initData', this.editData)
        this.$set(this, 'form', deepClone(this.editData))
        this.form.ingredient_id = this.editData.id
        // 食材分类
        this.form.sort_ids = this.editData.sort
        // 是否有营养
        this.form.is_enable_nutrition = this.editData.is_enable_nutrition === 1
        // 确保别名是数组
        if (!Array.isArray(this.form.alias_name)) {
          this.form.alias_name = this.form.alias_name ? [this.form.alias_name] : ['']
        } else if (this.form.alias_name.length === 0) {
          this.form.alias_name = ['']
        }
        // 食材应季
        if (this.editData.seasonal_month && Array.isArray(this.editData.seasonal_month)) {
          this.form.seasonal_month = this.editData.seasonal_month.map(item => {
            return item.month_num
          })
        }
        // 确保营养信息存在
        if (!this.form.nutrition_info) {
          this.initNutritionInfo()
        } else {
          let element = this.form.nutrition_info.element
          if (element && typeof element === 'string') {
            element = JSON.parse(element)
            for (let key in element) {
              this.$set(this.form.nutrition_info, key, element[key])
            }
          }
          let vitamin = this.form.nutrition_info.vitamin
          if (vitamin && typeof vitamin === 'string') {
            vitamin = JSON.parse(vitamin)
            for (let key in vitamin) {
              this.$set(this.form.nutrition_info, key, vitamin[key])
            }
          }
        }
        // 确保重量为字符串类型
        this.form.raw_weight = this.form.raw_weight.toString()
        this.form.cooked_weight = this.form.cooked_weight.toString()
        // 处理已有标签数据
        if (this.form.label && Array.isArray(this.form.label)) {
          this.form.selectedLabels = this.form.label.map(item => {
            return item.id
          })
        }
        this.selectedTags = deepClone(this.form.label)
        if (this.editData.image && this.editData.image.length > 0) {
          this.form.imageList = [
            {
              url: this.editData.image,
              name: this.editData.image,
              status: "success",
              uid: this.editData.image
            }
          ]
        }
        console.log('selectedTags', this.selectedTags)
      } else {
        this.resetForm()
      }
    },

    // 重置表单
    resetForm() {
      this.form = {
        name: '',
        alias_name: [''],
        sort_ids: null,
        seasonal_month: [],
        raw_weight: '100',
        cooked_weight: '100',
        image: '',
        labels: [],
        nutrition_info: {},
        selectedLabels: [],
        is_enable_nutrition: false,
        imageList: []
      }
      this.selectedTags = []
      this.initNutritionInfo()
      this.$refs.formRef && this.$refs.formRef.clearValidate()
    },

    // 初始化营养信息
    initNutritionInfo() {
      const nutritionInfo = {}
      this.nutritionList.forEach(item => {
        nutritionInfo[item.key] = ''
      })
      this.$set(this.form, 'nutrition_info', nutritionInfo)
    },

    // 获取分类选项
    async getCategoryOptions() {
      try {
        const res = await this.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost()
        if (res.code === 0) {
          this.categoryOptions = res.data
        }
      } catch (error) {
        console.error(error)
      }
    },

    // 获取月份选项
    getMonthOptions() {
      this.monthOptions = Array.from({ length: 12 }, (_, i) => ({
        id: i + 1,
        month_name: `${i + 1}月`
      }))
    },

    // 添加别名输入框
    addAlias() {
      this.form.alias_name.push('')
    },

    // 删除别名输入框
    removeAlias(index) {
      if (this.form.alias_name.length > 1) {
        this.form.alias_name.splice(index, 1)
      }
    },

    // 图片上传成功
    handleImageSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.form.image = res.data.url
        this.form.imageList = fileList
      } else {
        this.$message.error('图片上传失败')
      }
    },

    // 图片上传前校验
    beforeImageUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        this.$message.error('上传文件只能是图片格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
      }
      return isImage && isLt2M
    },

    // 获取标签数据
    async fetchLabelOptions() {
      try {
        const response = await this.$apis.apiBackgroundHealthyAdminLabelGroupAllLabelGroupListPost({
          page: 1,
          page_size: 9999, // 获取足够多的数据
          type: 'ingredient'
        })
        console.log("response", deepClone(response))
        // 处理返回的数据结构
        if (response.data && response.data.results) {
          this.labelOptions = response.data.results || []
          console.log('labelOptions', this.labelOptions)
        }
      } catch (error) {
        console.error('获取标签列表失败:', error)
        this.$message.error('获取标签列表失败')
      }
    },

    // 处理标签选择变化
    handleLabelChange(values) {
      // 清空已选标签
      this.selectedTags = []

      // 递归查找第三级标签
      const findThirdLevelLabel = (groups, labelId) => {
        for (const group of groups) {
          if (group.label_list && Array.isArray(group.label_list)) {
            for (const second of group.label_list) {
              if (second.label_list && Array.isArray(second.label_list)) {
                for (const third of second.label_list) {
                  if (third.id === labelId) {
                    return {
                      id: third.id,
                      name: third.name,
                      parentId: second.id,
                      parentName: second.name,
                      grandParentId: group.id,
                      grandParentName: group.name
                    }
                  }
                }
              }
            }
          }
        }
        return null
      }

      values.forEach(labelId => {
        const tag = findThirdLevelLabel(this.labelOptions, labelId)
        if (tag) {
          this.selectedTags.push(tag)
        }
      })

      // 更新表单数据
      this.form.selectedLabels = values // 保持原有的选中值
    },

    // 移除标签
    handleRemoveTag(tag) {
      this.selectedTags = this.selectedTags.filter(item => item.id !== tag.id)
      this.form.selectedLabels = this.form.selectedLabels.filter(id => id !== tag.id)
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    // 处理重量输入
    handleWeightInput(field) {
      // 移除非数字字符（保留小数点）
      this.form[field] = this.form[field].replace(/[^\d.]/g, '')

      // 确保只有一个小数点
      const parts = this.form[field].split('.')
      if (parts.length > 2) {
        this.form[field] = parts[0] + '.' + parts.slice(1).join('')
      }

      // 限制小数位数为2位
      if (parts[1] && parts[1].length > 2) {
        this.form[field] = parseFloat(this.form[field]).toFixed(2)
      }

      // 验证输入
      this.$refs.formRef.validateField([field])
    },

    // 重写格式化表单数据方法
    formatFormData() {
      let formData = deepClone(this.form)
      if (this.form.ingredient_id) {
        formData.id = this.form.ingredient_id
      }
      // 处理别名数据
      formData.alias_name = formData.alias_name.filter(name => name.trim())
      // 处理重量数据
      formData.raw_weight = parseFloat(formData.raw_weight) || 0
      formData.cooked_weight = parseFloat(formData.cooked_weight) || 0
      // 处理标签数据，只保留标签ID
      formData.labels = this.selectedTags.map(tag => tag.id)
      // 处理营养信息
      formData.is_enable_nutrition = this.form.is_enable_nutrition ? 1 : 0
      // 处理分类
      formData.sort_id = this.form.sort_ids
      delete formData.sort_ids
      // 食材标签
      if (formData.seasonal_month) {
        formData.seasonal_month_list = formData.seasonal_month
        delete formData.seasonal_month
      }
      // 食材标签
      if (formData.selectedLabels) {
        formData.label_list = formData.selectedLabels
        delete formData.selectedLabels
      }
      Object.keys(formData.nutrition_info).forEach(key => {
        formData.nutrition_info[key] = Number(formData.nutrition_info[key]) || 0
      })
      if (this.form.is_enable_nutrition) {
        // 营养
        let element = {}
        let vitamin = {}
        NUTRITION_LIST.forEach(nutrition => {
          let value = this.form.nutrition_info[nutrition.key] ? this.form.nutrition_info[nutrition.key] : 0
          if (nutrition.type === 'default') {
            formData[nutrition.key] = value
          }
          if (nutrition.type === 'element') {
            element[nutrition.key] = value
          }
          if (nutrition.type === 'vitamin') {
            vitamin[nutrition.key] = value
          }
        })
        formData.element = JSON.stringify(element)
        formData.vitamin = JSON.stringify(vitamin)
      }
      return formData
    },

    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            // if (parseFloat(this.form.raw_weight || 0) < parseFloat(this.form.cooked_weight || 0)) {
            //   return this.$message.error('食材熟重必须小于食材生重')
            // }
            let raw = parseFloat(this.form.raw_weight || 0)
            let cooked = parseFloat(this.form.cooked_weight || 0)
            if (raw <= 0) {
              this.$message.error('食材生重必须大于0')
              return
            }
            if (cooked <= 0) {
              this.$message.error('食材熟重必须大于0')
              return
            }
            const formData = this.formatFormData()
            const api =
              this.type === 'add'
                ? this.$apis.apiBackgroundAdminIngredientAddPost
                : this.$apis.apiBackgroundAdminIngredientModifyPost

            const res = await api(formData)
            if (res.code === 0) {
              this.$message.success(this.type === 'add' ? '添加成功' : '修改成功')
              this.$emit('submit')
              this.handleClose()
            } else if (res.code === 2) {
              this.$confirm(res.msg, '提示', {
                confirmButtonText: '覆 盖',
                cancelButtonText: this.$t('dialog.cancel_btn'),
                closeOnClickModal: false,
                customClass: 'ps-confirm',
                cancelButtonClass: 'ps-cancel-btn',
                confirmButtonClass: 'ps-btn',
                center: true,
                beforeClose: async (action, instance, done) => {
                  if (action === 'confirm') {
                    this.form.ingredient_id = res.data.ingredient_id
                    this.type = 'modify'
                    instance.confirmButtonLoading = true
                    await this.handleSubmit()
                    instance.confirmButtonLoading = false
                    done()
                  } else {
                    delete this.form.ingredient_id
                    this.type = 'add'
                    if (!instance.confirmButtonLoading) {
                      done()
                    }
                  }
                }
              })
                .then(e => { })
                .catch(e => {
                })
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            console.error(error)
            this.$message.error('操作失败')
          } finally {
            this.loading = false
          }
        }
      })
    },

    // 处理营养成分输入
    handleNutritionInput(key) {
      let value = this.form.nutrition_info[key];

      // 只允许输入数字和小数点
      value = value.replace(/[^\d.]/g, '');

      // 确保只有一个小数点
      if (value.split('.').length > 2) {
        value = value.replace(/\.+/g, '.');
      }

      // 如果有小数点，直接截取两位小数
      if (value.includes('.')) {
        const parts = value.split('.');
        value = parts[0] + '.' + parts[1].slice(0, 2);
      }

      // 限制最大值
      if (parseFloat(value) > 9999.99) {
        this.$message.error('营养成分最大值为9999.99')
        value = '9999.99';
      }
      // 如果是负数，转换为0
      if (parseFloat(value) < 0) {
        value = '0'
      }

      this.form.nutrition_info[key] = value;
    },

    // 初始化表单规则
    initFormRules() {
      // 动态生成营养成分的验证规则
      this.nutritionList.forEach(nutrition => {
        this.$set(this.rules, `nutrition_info.${nutrition.key}`, [
          {
            validator: this.validateNutrition,
            trigger: 'blur'
          }
        ])
      })
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleImgRemove(file, type) {
      console.log(file, type)
      this.form.image = ''
      this.form.imageList = []
    },
    handelChange(file, fileList) {
      this.uploadParams.key =
        this.uploadParams.prefix + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    }
  }
}
</script>

<style lang="scss" scoped>
.add-ingredients-form {
  padding: 0 20px;

  .form-section {
    .full-width {
      width: 100%;
    }

    .alias-input-group {
      .alias-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .alias-input {
          width: 400px;
          margin-right: 10px;
        }
      }
    }

    .weight-input-group {
      display: flex;
      gap: 20px;

      .weight-item {
        display: flex;
        align-items: center;

        .weight-label {
          margin-right: 8px;
        }

        .weight-unit {
          margin-left: 8px;
        }
      }
    }

    .selected-tags {
      margin-top: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag-item {
        background-color: #ff9b51;
        color: #fff;
        border: none;

        .el-tag__close {
          color: #fff;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: #fff;
          }
        }
      }
    }

    .nutrition-section {
      margin: 20px 40px;

      .section-title {
        width: 90px;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 16px;
        padding-left: 8px;
        text-align: right;
        color: #606266;
      }

      .nutrition-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
}

.ratio-section {
  .ratio-title {
    margin-bottom: 16px;
    color: #606266;
  }

  .weight-input-group {
    display: flex;
    gap: 32px;

    .weight-item {
      display: flex;
      align-items: center;

      .weight-label {
        color: #606266;
        margin-right: 8px;
        min-width: 70px;
      }

      :deep(.el-input) {
        .el-input__inner {
          text-align: left;
        }

        .el-input-group__append {
          padding: 0 10px;
        }
      }
    }
  }
}

:deep(.el-cascader) {
  width: 100%;
}
</style>
<style lang="scss">
.add-ingredients-form {
  .upload-w {
    width: 146px;
    height: 146px;
    border-radius: 4px;
    border: solid 1px #e0e6eb;
    text-align: center;
    vertical-align: top;
  }

  .image-upload-box {
    margin-bottom: 10px;
    .file-upload {
      width: 146px;
      height: 146px;

      .el-upload-list--picture-card .el-upload-list__item {
        width: 100%;
        height: 146px;
        border: none;
      }

      .el-upload--picture-card {
        width: auto;
        height: auto;
      }

      &.hide-upload {
        .el-upload--picture-card {
          border: none !important;
        }

        .el-upload-dragger {
          border: none;
        }
      }
    }

  }

  .el-upload-dragger {
    width: 146px !important;
    height: 146px !important;
  }

  .avatar {
    display: block;
    width: 100%;
    max-height: 146px;
  }

  .upload-t {
    vertical-align: top;
    margin-top: 40px;
    line-height: 2;
    color: #ff9b45;

    .el-icon-circle-plus {
      font-size: 30px;
      color: #ff9b45;
    }
  }
  .upload-hidden {
    overflow: hidden;
  }
}
</style>
