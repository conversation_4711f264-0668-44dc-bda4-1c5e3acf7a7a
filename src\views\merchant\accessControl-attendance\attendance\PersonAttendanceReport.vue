<template>
  <div class="PersonAttendanceReport container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      @search="searchHandle"
      :form-setting="searchFormSetting"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_attendance.attendance_record_details.card_user_punch_status_count_export']">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="tableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          :isFirst="isFirstSearch"
          header-row-class-name="ps-table-header-row"/>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import { recentSevenDay, getRequestParams } from '../constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'PersonAttendanceReport',
  mixins: [exportExcel],
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '一级组织', key: 'primary' },
        { label: '二级组织', key: 'secondary' },
        { label: '三级组织', key: 'company' },
        { label: '姓名', key: 'card_user_name' },
        { label: '考勤组', key: 'attendance_groups_name' },
        { label: '出勤时长', key: 'time_hours' },
        { label: '签到次数', key: 'sign_in_count' },
        { label: '签退次数', key: 'sign_out_count' },
        { label: '迟到次数', key: 'be_late_count' },
        { label: '早退次数', key: 'leave_early_count' },
        { label: '请假次数', key: 'for_leave_count' },
        { label: '缺卡次数', key: 'absence_work_count' }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '日期',
          value: recentSevenDay,
          clearable: false
        },
        person_name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        org_ids: {
          type: 'organizationSelect',
          value: [],
          label: '组织',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        attendance_groups_ids: {
          type: 'select',
          label: '考勤组',
          value: [],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择考勤组',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: []
        }
      },
      isFirstSearch: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getPersonAttendanceList()
      this.getAttendanceGroupList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.isFirstSearch = false
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.isFirstSearch = false
      this.tableData = []
      this.initLoad()
    },
    async getPersonAttendanceList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundAttendanceAttendanceRecordDetailsCardUserPunchStatusCountPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getPersonAttendanceList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getPersonAttendanceList()
    },
    gotoExport() {
      const option = {
        type: "PersonAttendanceReportExport",
        params: getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      }
      this.exportHandle(option)
    },
    // 获取考勤组
    async getAttendanceGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.searchFormSetting.attendance_groups_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
