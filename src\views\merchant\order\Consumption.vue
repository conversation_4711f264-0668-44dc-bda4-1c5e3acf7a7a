<template>
  <div class="ConsumptionOrder container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" label-width="105px" @search="searchHandle" :autoSearch="false">
      <template #perv>
        <div class="searchref_top">
          <el-button :class="{ active: current === 0 }" @click="tabHandler(0)" v-permission="['background_order.order_payment.on_scene_list']">堂食订单</el-button>
          <el-button :class="{ active: current === 1 }" @click="tabHandler(1)" v-permission="['background_order.order_payment.list']">预约订单</el-button>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r" v-if="current === 0">
          <button-icon color="plain" @click="mulExportFood">导出菜品明细</button-icon>
          <button-icon color="plain" @click="mulRefundHandle" v-permission="['background_order.order_payment.instore_refund']">批量退款</button-icon>
          <button-icon color="plain" type="export" @click="handleExportLive">导出实况</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport">导出报表</button-icon>
          <button-icon color="plain" @click="openDialog">小票打印</button-icon>
        </div>
        <div class="align-r" v-else>
          <button-icon color="plain" @click="mulExportFood">导出菜品明细</button-icon>
          <button-icon color="plain" @click="mulRefundHandle" v-permission="['background_order.reservation_order.reservation_order_refund']">批量退款</button-icon>
          <button-icon color="plain" type="export" @click="handleExportLive">导出实况</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport">导出报表</button-icon>
          <button-icon color="plain" @click="openDialog">小票打印</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
         <!-- :selectable="selectableHandle" -->
        <el-table ref="tableView" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row" @selection-change="handleOrderSelectionChange" @sort-change="sortChange" :empty-text="isFirstSearch ? '暂无数据，请查询' : ''">
          <el-table-column type="selection" width="55"></el-table-column>
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #orderStatusAlias="{ row }">
              <el-popover
               v-if="row.order_status === 'ORDER_FAILED'"
                placement="top-start"
                width="200"
                trigger="hover"
                :content="row.error_msg">
                <div slot="reference"> {{ row.order_status_alias }} </div>
              </el-popover>
              <div v-else>
                {{ row.order_status_alias }}
              </div>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoDetail(row)">详情</el-button>
              <el-button v-if="current === 0" type="text" class="ps-text" size="small" :disabled="!row.can_refund" @click="openRefundDialog(row)" v-permission="['background_order.order_payment.instore_refund']">退款</el-button>
              <el-button v-else type="text" class="ps-text" size="small" :disabled="!row.can_refund" @click="openRefundDialog(row)" v-permission="['background_order.reservation_order.reservation_order_refund']">退款</el-button>
            </template>
          </table-column>
        </el-table>
        <div v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText">
          <ul class="total">
            <li>
              合计笔数:
              <span>{{ total_count }}</span>
            </li>
            <li>
              合计订单金额:￥
              <span>{{ total_amount | formatMoney }}</span>
            </li>
            <li>
              合计实收金额:￥
              <span>{{ total_origin_amount | formatMoney }}</span>
            </li>
            <li>
              合计餐补金额:￥
              <span>{{ total_food_subsidy_fee | formatMoney }}</span>
            </li>
            <li>
              手续费合计:￥
              <span>{{ total_rate_fee | formatMoney }}</span>
            </li>
            <li v-if="current === 1">
              服务费合计（含已退款的服务费）:￥
              <span>{{ totalServeFee | formatMoney }}</span>
            </li>
          </ul>
        </div>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- 退款对话框 -->
    <div class="refund-confirm">
      <el-dialog top="200px" title="退款" :visible.sync="outerVisible" width="800px" customClass="ps-dialog">
        <el-table
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
          :data="refundTableList"
          border
          row-key="id"
          style="width: 100%;"
          ref="refundTable"
          @selection-change="handleRefundSelectionChange"
        >
          >
          <el-table-column
            type="selection"
            :show-overflow-tooltip="true"
            :reserve-selection="true"
            v-if="refundMethod === 'part'"
            class-name="ps-checkbox"
            width="55"
            :selectable="selectDisabled"
          ></el-table-column>
          <el-table-column prop="consumptionImage" label="图片">
            <template slot-scope="scope">
              <img style="width:60px" :src="scope.row.consumptionImage" alt="">
            </template>
          </el-table-column>
          <el-table-column prop="name" :label="refundData.payment_order_type === 'goods_cashier'?'商品名称':'菜品名称'"></el-table-column>
          <el-table-column prop="food_price" label="销售价格">
            <template slot-scope="scope">
              <!-- raw_fee -->
              <span>￥{{ scope.row.food_price | formatMoney}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="数量"></el-table-column>
          <el-table-column prop="weight" label="重量"></el-table-column>
          <el-table-column prop="buy_price" label="消费金额">
            <template slot-scope="scope">
              <span>￥{{ scope.row.real_fee | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="consumptionStatus" label="退款状态">
            <template slot-scope="scope">
              <span>{{ scope.row.consumptionStatus === 'ORDER_REFUND_SUCCESS' ? '退款成功' : '未退款' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="refund-radio">
          <el-radio text-color="#FF9B45" v-model="refundMethod" @change="changeRefundType" label="all" class="ps-radio">全额退款</el-radio>
          <!-- disabled逻辑： refundData是选中行的数据 如果订单金额是0.00元则不能选择部分退款 -->
          <el-radio text-color="#FF9B45" :disabled="refundData.origin_fee === 0" v-model="refundMethod" @change="changeRefundType" label="part" class="ps-radio">部分退款</el-radio>
        </div>
        <div class="refund-info">
          <el-form
            :model="dialogForm"
            @submit.native.prevent
            status-icon
            ref="dialogFormRef"
            :rules="dialogFormRules"
            label-width="100px"
            class="attendance-form"
            inline
          >
            <div class="inline-box m-r-20">
              <span>可退款余额：</span>
              <span v-if="refundMethod === 'all'">{{ refundData.net_fee | formatMoney }}</span>
              <span v-if="refundMethod === 'part'">&lt;{{ refundData.part_net_fee  }}</span>
            </div>
            <div v-if="refundMethod === 'all'" class="inline-box">
              <span>退款金额：</span>
              <span>{{ refundData.pay_fee | formatMoney }}</span>
            </div>
            <div v-if="refundMethod === 'part' && refundData.payway !== 'PushiPay'" class="inline-box">
              <el-form-item label="退款金额：" prop="refundMoney">
                <el-input
                  class="w-180 ps-input"
                  placeholder="请输入退款金额"
                  v-model="dialogForm.refundMoney"
                ></el-input>
              </el-form-item>
            </div>
            <div v-if="refundMethod === 'part' && refundData.payway === 'PushiPay'">
              <el-form-item label="补贴钱包：" prop="refundSubsidyMoney">
                <el-input
                  class="w-180 ps-input"
                  :placeholder="'可退金额<' + refundData.part_subsidy_fee"
                  :disabled="!Number(refundData.part_subsidy_fee)"
                  v-model="dialogForm.refundSubsidyMoney"
                ></el-input>
              </el-form-item>
              <el-form-item label="储值钱包：" prop="refundWalletMoney">
                <el-input
                  class="w-180 ps-input"
                  :placeholder="'可退金额<' + refundData.part_wallet_fee"
                  :disabled="!Number(refundData.part_wallet_fee)"
                  v-model="dialogForm.refundWalletMoney"
                ></el-input>
              </el-form-item>
              <el-form-item label="赠送钱包：" prop="refundComplimentaryMoney">
                <el-input
                  class="w-180 ps-input"
                  :placeholder="'可退金额<' + refundData.part_complimentary_fee"
                  :disabled="!Number(refundData.part_complimentary_fee)"
                  v-model="dialogForm.refundComplimentaryMoney"
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div style="color:#ff9b45;">温馨提示: 如选择商品进行退款，库存会对应递增，若未实际入库，请及时至‘商品库存’中操作出库。</div>
        <div class="refund-remark">
          <el-form
            :model="dialogForm"
            @submit.native.prevent
            status-icon
            ref="dialogFormRef"
            :rules="dialogFormRules"
            inline
          >
            <el-form-item label-width="140px" label="备注：" class="m-t-20">
              <el-input v-model="refundData.remark" class="ps-input w-250" maxlength="30"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <el-dialog width="30%" title="温馨提示" customClass="ps-dialog" :visible.sync="innerVisible" append-to-body top="280px">
          <p class="twoRefund" v-if="refundMethod === 'all'" style="font-size: 20px;">
            确定要对该订单进行退款吗
          </p>
          <p class="twoRefund" v-else style="font-size: 20px;">
            确定要对该订单进行
            <span style="font-weight: bold;">部分退款吗?</span>
          </p>
          <p class="twoRefund" style="color:#E0364C;">温馨提示: 确定后不可撤销</p>
          <div slot="footer" class="footer-btn">
            <el-button class="ps-cancel-btn" @click="innerVisible = false">取消</el-button>
            <el-button class="ps-btn" :disabled="dialogLoading" @click="handleConfirm">确定</el-button>
          </div>
        </el-dialog>
        <div slot="footer">
          <el-button class="ps-cancel-btn" @click="outerVisible = false">取 消</el-button>
          <el-button class="ps-btn" @click="handleRefund">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
    <print-ticket
      ref="printTicket"
      :isshow.sync="printTicketVisible"
      :type="current =='0'?'onScence':'order'"
      title="小票打印"
      :select-list-id="selectListId"
      :print-type="printTypeList"
      @confirm="searchHandle"
    ></print-ticket>
  </div>
</template>

<script>
import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, deepClone, divide, times, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import PrintTicket from '@/components/PrintTicket'
import {
  DEVICE_STATUS,
  CONSUMPTION_SCENE_TABLE,
  CONSUMPTION_RESERVATION_TABLE,
  MEALTYPE,
  PAYMENTSTATE,
  GETMEALTYPE,
  getRequestParams,
  RECENTSEVEN
} from './constants'
import report from '@/mixins/report' // 混入
export default {
  name: 'Consumption',
  components: { PrintTicket },
  mixins: [activatedLoadData, exportExcel, report],
  data() {
    let validataRefundMoney = (rule, value, callback) => {
      let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      let price
      if (rule.field === 'refundWalletMoney') {
        price = this.refundData.part_wallet_fee
      } else if (rule.field === 'refundSubsidyMoney') {
        price = this.refundData.part_subsidy_fee
      } else if (rule.field === 'refundComplimentaryMoney') {
        price = this.refundData.part_complimentary_fee
      } else if (rule.field === 'refundMoney') {
        price = this.refundData.part_net_fee
      }
      console.log("price", price);
      if (value) {
        if (Number(value) === 0) {
          callback(new Error('金额不能为0'))
        } else if (value >= Number(price)) {
          callback(new Error('金额不能大于等于可退金额'))
        } else if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      current: 0, // 0代表堂食订单 1代表预约订单
      // 搜索筛选相关
      sceneSearchForm: {
        date_type: {
          type: 'select',
          value: 'create_time',
          maxWidth: '130px',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '支付时间',
              value: 'pay_time'
            },
            {
              label: '扣费时间',
              value: 'deduction_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: false,
          value: RECENTSEVEN
        },
        order_status_list: {
          type: 'select',
          label: '支付状态',
          value: '',
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: PAYMENTSTATE
        },
        payway_list: {
          type: 'select',
          label: '支付类型',
          value: '',
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: []
        },
        sub_payway_list: {
          type: 'select',
          label: '支付方式',
          value: '',
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: []
        },
        device_name_list: {
          type: 'select',
          label: '交易设备',
          value: '',
          multiple: true,
          collapseTags: true,
          listNameKey: 'device_name',
          listValueKey: 'device_name',
          placeholder: '请选择',
          clearable: true,
          dataList: []
        },
        pay_device_status: {
          type: 'select',
          label: '设备状态',
          value: '',
          placeholder: '请选择',
          dataList: DEVICE_STATUS
        },
        device_type_list: {
          type: 'select',
          label: '设备类型',
          value: '',
          placeholder: '请选择设备类型',
          multiple: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        },
        // 餐段改成筛选多个的哦，原来的先留着把
        // meal_type: {
        //   type: 'select',
        //   label: '餐段',
        //   value: '',
        //   placeholder: '请选择',
        //   dataList: MEALTYPE
        // },
        meal_type_list: {
          type: 'select',
          label: '餐段',
          value: [],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: MEALTYPE.slice(1)
        },
        wallet_org: {
          type: 'organizationSelect',
          value: [],
          label: '动账组织',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        consume_organization_ids: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        payer_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          multiple: true,
          collapseTags: true
        },
        payer_department_group_ids: {
          type: 'organizationDepartmentSelect',
          value: [],
          label: '部门'
        },
        making_type: {
          type: 'select',
          label: '制作状态',
          value: '',
          placeholder: '请选择',
          dataList: [
            {
              label: '待制作',
              value: 'queues'
            },
            {
              label: '已制作',
              value: 'finish'
            },
            {
              label: '已取餐',
              value: 'take_out'
            }
          ]
        },
        name: {
          type: 'input',
          value: '',
          label: '用户姓名',
          placeholder: '请输入'
        },
        phone: {
          type: 'input',
          value: '',
          label: '手机号',
          placeholder: '请输入'
        },
        person_no: {
          type: 'input',
          value: '',
          label: '人员编号',
          placeholder: '请输入'
        },
        third_and_trade_no: {
          type: 'input',
          value: '',
          labelWidth: '145px',
          label: '订单号/第三方订单号',
          placeholder: '请输入'
        },
        controller: {
          type: 'input',
          value: '',
          label: '操作员',
          placeholder: '请输入要搜索的操作员'
        },
        // 废弃
        // device_org: {
        //   type: 'organizationParentSelect',
        //   value: [],
        //   label: '交易组织',
        //   checkStrictly: true,
        //   isLazy: false,
        //   multiple: true
        // },
        only_discount: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看优惠',
          value: false
        },
        only_rate_fee: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看手续费',
          value: false
        },
        only_debt_fee: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看透支',
          value: false
        }
      },
      reservationSearchForm: {
        date_type: {
          type: 'select',
          value: 'create_time',
          maxWidth: '130px',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '报餐时间',
              value: 'report_date'
            },
            {
              label: '预约时间',
              value: 'reservation_date'
            },
            {
              label: '用餐时间',
              value: 'dining_time'
            },
            {
              label: '支付时间',
              value: 'pay_time'
            },
            {
              label: '扣费时间',
              value: 'deduction_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: false,
          value: RECENTSEVEN
        },
        take_meal_type_list: {
          type: 'select',
          label: '取餐方式',
          value: '',
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: GETMEALTYPE
        },
        order_status_list: {
          type: 'select',
          label: '支付状态',
          value: '',
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: PAYMENTSTATE
        },
        payway_list: {
          type: 'select',
          label: '支付类型',
          value: '',
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: []
        },
        sub_payway_list: {
          type: 'select',
          label: '支付方式',
          value: '',
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: []
        },
        meal_type_list: {
          type: 'select',
          label: '餐段',
          value: '',
          multiple: true,
          collapseTags: true,
          placeholder: '请选择',
          dataList: MEALTYPE
        },
        wallet_org: {
          type: 'organizationSelect',
          value: [],
          label: '动账组织',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        consume_organization_ids: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        payer_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          multiple: true,
          collapseTags: true
        },
        payer_department_group_ids: {
          type: 'organizationDepartmentSelect',
          value: [],
          label: '部门'
        },
        making_type: {
          type: 'select',
          label: '制作状态',
          value: '',
          placeholder: '请选择',
          dataList: [
            {
              label: '待制作',
              value: 'queues'
            },
            {
              label: '已制作',
              value: 'finish'
            }
          ]
        },
        name: {
          type: 'input',
          value: '',
          label: '用户姓名',
          placeholder: '请输入'
        },
        phone: {
          type: 'input',
          value: '',
          label: '手机号',
          placeholder: '请输入'
        },
        person_no: {
          type: 'input',
          value: '',
          label: '人员编号',
          placeholder: '请输入'
        },
        third_and_trade_no: {
          type: 'input',
          value: '',
          labelWidth: '145px',
          label: '订单号/第三方订单号',
          placeholder: '请输入'
        },
        only_discount: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看优惠',
          value: false
        },
        only_rate_fee: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看手续费',
          value: false
        },
        only_debt_fee: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看透支',
          value: false
        }
      },
      searchForm: {},
      tableData: [],
      isLoading: false, // 刷新数据
      isLoadingCollect: false,
      elementLoadingText: "数据正在加载，请耐心等待...",
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      page: 1, // 第几页
      total_count: 0,
      total_amount: 0,
      total_origin_amount: 0,
      total_food_subsidy_fee: 0, // 合计餐补
      total_rate_fee: 0,
      // 退款相关
      refundMethod: 'all', // all代表全额退款 part代表部分退款
      dialogForm: {
        refundMoney: '', // 部分退款金额
        refundWalletMoney: '', // 部分退款金额
        refundSubsidyMoney: '', // 部分退款金额
        refundComplimentaryMoney: '' // 部分退款金额
      },
      dialogFormRules: {
        refundMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }],
        refundWalletMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }],
        refundSubsidyMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }],
        refundComplimentaryMoney: [{ required: true, validator: validataRefundMoney, trigger: "change" }]
      },
      refundFoodId: [], // 部分退款菜品id
      outerVisible: false,
      innerVisible: false,
      dialogLoading: false,
      // 退款弹框的数组
      refundData: [],
      refundStatus: ['ORDER_REFUNDING', 'ORDER_REFUND_SUCCESS'],

      // 报表设置相关
      tableSetting: [],
      sceneTableSetting: deepClone(CONSUMPTION_SCENE_TABLE),
      reservationSableSetting: deepClone(CONSUMPTION_RESERVATION_TABLE),
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'ConsumptionOrderScene',
      printTicketVisible: false,
      refundOrderIds: [], // 可以退款的id
      selectListId: [], // 多选功能
      totalServeFee: 0, // 服务费合计
      printTypeList: [ // 打印列表  ，
        {
          label: '后厨单',
          value: 'kitchen'
        },
        {
          label: '结账单',
          value: 'bill'
        }
      ],
      sort_type: '', // 排序类型
      sort_date_type: '', // 需要排序的日期类型
      isFirstSearch: true,
      refundTableList: []
    }
  },
  created() {
    this.setDefault()
    // this.sceneSearchForm.device_org.value = [this.$store.getters.organization]
    // this.initLoad(true)
  },
  mounted() {
    this.getLevelNameList() // 根据项目点获取公司的层级组织
    this.getpayList() // 支付方式 / 支付类型
    this.getDeviceList()
    this.getDeviceType()
  },
  methods: {
    initLoad(isFirst) {
      if (this.current === 1) {
        this.searchForm = this.reservationSearchForm
        if (!isFirst) {
          this.getConsumptionList()
          this.getConsumptionStatisticalDataList()
        }
      } else {
        this.searchForm = this.sceneSearchForm
        if (!isFirst) {
          this.getOnSceneList()
          this.getOnSceneStatisticalDataList()
        }
      }
      this.$nextTick(() => {
        if (Reflect.has(this.$refs, "printTicket")) {
          this.$refs.printTicket.setPrintType('bill')
        }
      })
    },
    // 兼容下餐包跳转
    setDefault() {
      try {
        if (this.$route.query.queryData) {
          let query = JSON.parse(this.$route.query.queryData)
          if (this.$route.query.tab) {
            this.current = Number(this.$route.query.tab)
          }
          if (this.current === 1) {
            if (query.date_type) {
              this.reservationSearchForm.date_type.value = query.date_type
            }
            if (query.select_time) {
              this.reservationSearchForm.select_time.value = query.select_time
            }
            if (query.trade_no) {
              this.reservationSearchForm.third_and_trade_no.value = query.trade_no
            }
            if (query.meal_type_list) {
              this.reservationSearchForm.meal_type_list.value = query.meal_type_list
            }
          }
          this.initLoad()
        } else {
          this.initLoad(true)
        }
      } catch (error) {
        this.initLoad(true)
      }
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.printTicketVisible = false
        this.page = 1
        this.initLoad()
        this.isFirstSearch = false
      }
    }, 300),
    triggerTypeByClick() {
      this.printTicketVisible = false
      this.page = 1
      switch (this.current) {
        case 0:
          this.getOnSceneList()
          break
        case 1:
          this.getConsumptionList()
          break
      }
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.page = 1
      this.isFirstSearch = true
      // this.initLoad()
    },
    tabHandler(type) {
      this.current = type
      this.page = 1
      this.sort_type = null
      // 重置合计数
      this.total_count = 0
      this.total_amount = 0
      this.total_origin_amount = 0
      this.total_food_subsidy_fee = 0
      this.total_rate_fee = 0
      this.totalServeFee = 0
      this.$refs.tableView.clearSort()
      this.isFirstSearch = true
      this.$nextTick(() => {
        if (type === 0) {
          this.printType = 'ConsumptionOrderScene'
          this.searchForm = this.sceneSearchForm
          this.tableSetting = this.sceneTableSetting
          this.initPrintSetting()
          this.tableData = []
          // this.getOnSceneList()
          // this.getOnSceneStatisticalDataList()
        } else {
          this.printType = 'ConsumptionOrderReservation'
          this.searchForm = this.reservationSearchForm
          this.tableSetting = this.reservationSableSetting
          this.initPrintSetting()
          this.tableData = []
          // this.getConsumptionList()
          // this.getConsumptionStatisticalDataList()
        }
        if (Reflect.has(this.$refs, "printTicket")) {
          this.$refs.printTicket.setPrintType(type === 0 ? 'bill' : '')
        }
      })
    },
    // 获取数据列表
    async getConsumptionList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      switch (this.sort_type) {
        case 'descending':
          params.sort_type = 'desc'
          params.sort_date_type = this.sort_date_type
          break
        case 'ascending':
          params.sort_type = 'asc'
          params.sort_date_type = this.sort_date_type
          break
        default:
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderPaymentListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        // this.total_amount = res.data.total_amount
        // this.total_count = res.data.count
        // this.total_origin_amount = res.data.total_pay_amount
        // this.total_rate_fee = res.data.total_rate_fee
        // this.totalServeFee = res.data.total_fuwu_fee || 0
      } else {
        this.$message.error(res.msg)
      }
    },
    async getOnSceneList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      switch (this.sort_type) {
        case 'descending':
          params.sort_type = 'desc'
          params.sort_date_type = this.sort_date_type
          break
        case 'ascending':
          params.sort_type = 'asc'
          params.sort_date_type = this.sort_date_type
          break
        default:
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderPaymentOnSceneListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        // this.total_amount = res.data.total_amount
        // this.total_count = res.data.count
        // this.total_origin_amount = res.data.total_pay_amount
        // this.total_rate_fee = res.data.total_rate_fee
        // this.totalServeFee = res.data.total_fuwu_fee || 0
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取统计数据
    async getConsumptionStatisticalDataList() {
      this.isLoadingCollect = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderPaymentListCollectPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log(res)
      if (res.code === 0) {
        this.elementLoadingText = '数据正在加载，请耐心等待...'
        this.isLoadingCollect = false
        this.total_amount = res.data.total_amount
        this.total_count = res.data.total_count
        this.total_origin_amount = res.data.total_pay_amount
        this.total_food_subsidy_fee = res.data && res.data.total_food_subsidy_fee ? res.data.total_food_subsidy_fee : "0.00"
        this.total_rate_fee = res.data.total_rate_fee
        this.totalServeFee = res.data.total_fuwu_fee || 0
      } else {
        this.elementLoadingText = '汇总数据加载失败，请重试。'
        this.$message.error(res.msg)
      }
    },
    async getOnSceneStatisticalDataList() {
      this.isLoadingCollect = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderPaymentOnSceneListCollectPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log(res)
      if (res.code === 0) {
        this.elementLoadingText = '数据正在加载，请耐心等待...'
        this.isLoadingCollect = false
        this.total_amount = res.data.total_amount
        this.total_count = res.data.total_count
        this.total_origin_amount = res.data.total_pay_amount
        this.total_food_subsidy_fee = res.data && res.data.total_food_subsidy_fee ? res.data.total_food_subsidy_fee : "0.00"
        this.total_rate_fee = res.data.total_rate_fee
        this.totalServeFee = res.data.total_fuwu_fee || 0
      } else {
        this.elementLoadingText = '汇总数据加载失败，请重试。'
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.initLoad()
    },
    // 动态获取组织的层级
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundGetlevelNameListPost()
      let arr = JSON.parse(JSON.stringify(res.data).replace(/name/g, 'label'))
      let arr2 = JSON.parse(JSON.stringify(arr).replace(/level/g, 'key'))
      // 初始化每个tableSetting
      this.sceneTableSetting = deepClone(CONSUMPTION_SCENE_TABLE)
      this.reservationSableSetting = deepClone(CONSUMPTION_RESERVATION_TABLE)
      this.sceneTableSetting.splice(25, 0, ...arr2)
      this.reservationSableSetting.splice(22, 0, ...arr2)
      if (this.current === 0) {
        this.tableSetting = this.sceneTableSetting
      } else if (this.current === 1) {
        this.tableSetting = this.reservationSableSetting
      }
      this.initPrintSetting()
    },
    // 获取支付方式 / 支付类型
    async getpayList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost()
      if (res.code === 0) {
        const result = []
        const result2 = []
        res.data.result.payways.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        res.data.result.sub_payways.forEach(d => {
          Object.keys(d).forEach(key => result2.push({ label: d[key], value: key }))
        })
        this.sceneSearchForm.payway_list.dataList = result
        this.sceneSearchForm.sub_payway_list.dataList = result2
        this.reservationSearchForm.payway_list.dataList = result
        this.reservationSearchForm.sub_payway_list.dataList = result2
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设备列表
    async getDeviceList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.sceneSearchForm.device_name_list.dataList = res.data.results
        console.log('设备列表', res.data.results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳转详情
    gotoDetail(row) {
      this.$router.push({
        // path: `/order/consumption_detail?id=${this.current}`,
        // path: `/order/consumption_detail`,
        name: 'MerchantConsumptionDetail',
        query: {
          id: row.id,
          order_id: row.order,
          type: this.current,
          type_path: 'consumption'
        }
      })
    },
    // 导出报表
    handleExport() {
      let type
      if (this.current === 1) {
        type = 'ExportOrderConsumption'
      } else {
        type = 'ExportOnSceneList'
      }
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const option = {
        type,
        params
      }
      console.log(option)
      this.exportHandle(option)
    },
    // 导出实况
    handleExportLive() {
      let params = getRequestParams(this.searchForm, this.page, this.pageSize)
      if (this.current === 1) {
        params.export_order_types = 'reservation'
      } else {
        params.export_order_types = 'instore'
      }
      const option = {
        type: 'OrderPaymentLiveExport',
        url: 'apiBackgroundOrderOrderPaymentLiveExportPost',
        params
      }
      this.exportHandle(option)
    },
    // 退款start
    openRefundDialog(data) {
      this.dialogForm = {
        refundMoney: '', // 部分退款金额
        refundWalletMoney: '', // 部分退款金额
        refundSubsidyMoney: '', // 部分退款金额
        refundComplimentaryMoney: '' // 部分退款金额
      }
      this.refundData = deepClone(data)
      console.log(this.refundData, 88888888)
      // 菜品
      if (this.refundData.food_list.length) {
        this.refundTableList = this.refundData.food_list.map(v => {
          v.consumptionType = 'food'
          v.consumptionImage = v.food_extra.food_img
          v.consumptionStatus = v.food_status
          return v
        })
      }
      // 商品
      if (this.refundData.goods_list.length) {
        this.refundTableList = this.refundData.goods_list.map(v => {
          v.consumptionType = 'goods'
          v.consumptionImage = v.goods_extra.goods_img
          v.consumptionStatus = v.goods_status
          return v
        })
      }
      console.log(this.refundTableList, '你好好好')
      this.refundData.part_wallet_fee = divide(Math.abs(this.refundData.part_wallet_fee))
      this.refundData.part_subsidy_fee = divide(Math.abs(this.refundData.part_subsidy_fee))
      this.refundData.part_complimentary_fee = divide(Math.abs(this.refundData.part_complimentary_fee))
      this.refundData.part_net_fee = divide(Math.abs(this.refundData.part_net_fee))
      this.outerVisible = true
      this.refundMethod = 'all'
      console.log("openRefundDialog", this.refundData);
    },
    changeRefundType() {
      if (this.refundMethod === 'part') {
        this.$refs.refundTable.clearSelection()
      }
    },
    handleRefund() {
      if ((this.refundMethod === 'part' && this.refundData.payway === 'PushiPay' &&
      !this.dialogForm.refundWalletMoney && !this.dialogForm.refundSubsidyMoney && !this.dialogForm.refundComplimentaryMoney) ||
      (this.refundMethod === 'part' && this.refundData.payway !== 'PushiPay' && !this.dialogForm.refundMoney)) {
        return this.$message.error("请输入退款金额")
      }
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          this.innerVisible = true
        }
      })
    },
    // 退款弹出框 退款成功
    handleConfirm() {
      let params = {
        trade_no: this.refundData.trade_no
      }
      if (this.refundMethod === 'all') {
        params.refund_fee = this.refundData.pay_fee
      } else {
        if (this.refundData.payway === 'PushiPay') {
          params.refund_wallet_fee = times(this.dialogForm.refundWalletMoney)
          params.refund_subsidy_fee = times(this.dialogForm.refundSubsidyMoney)
          params.refund_complimentary_fee = times(this.dialogForm.refundComplimentaryMoney)
        } else {
          params.refund_fee = times(this.dialogForm.refundMoney)
        }
        if (this.refundData.payment_order_type === 'goods_cashier') {
          params.payment_goods_ids = this.refundFoodId
        } else {
          params.payment_food_ids = this.refundFoodId
        }
      }
      // 如果有备注才传
      if (this.refundData.remark) {
        params.remark = this.refundData.remark
      }
      this.orderRefund(params)
    },
    async orderRefund(params) {
      if (this.dialogLoading) return
      this.dialogLoading = true
      let res
      try {
        if (this.current) {
          // 预约退款
          res = await this.$apis.apiBackgroundOrderOrderReservationReservationOrderRefundPost(params)
        } else {
          // 堂食退款
          res = await this.$apis.apiBackgroundOrderOrderPaymentInstoreRefundPost(params)
        }
        this.dialogLoading = false
        if (res.code === 0) {
          this.innerVisible = false
          this.outerVisible = false
          this.initLoad()
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.dialogLoading = false
        this.$message.error(error.message)
      }
    },
    // 多选框  当选择项发生变化时会触发该事件
    handleRefundSelectionChange(val) {
      this.dialogForm.refundMoney = 0
      this.refundFoodId = []
      val.map(item => {
        this.dialogForm.refundMoney += item.real_fee
        this.refundFoodId.push(item.id)
      })
      this.dialogForm.refundMoney = divide(this.dialogForm.refundMoney)
    },
    // 退款end
    selectDisabled(row, index) {
      return !this.refundStatus.includes(row.refund_status)
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        this.sceneSearchForm.device_type_list.dataList = res.data
        console.log('设备类型', res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // // 多选禁用
    // selectableHandle(row) {
    //   return row.can_refund
    // },
    // 订单的多选
    handleOrderSelectionChange(val) {
      this.refundOrderIds = []
      this.selectListId = val.map(item => {
        if (item.can_refund) {
          this.refundOrderIds.push(item.id)
        }
        return item.id
      })
    },
    async mulRefundHandle() {
      if (this.dialogLoading) return
      if (!this.selectListId.length) return this.$message.error('请选择要退款的订单！')
      if (!this.refundOrderIds.length) return this.$message.error('当前所选订单不存在可退款订单！')
      this.dialogLoading = true
      this.$confirm(`确定要将这些订单进行退款？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        // customClass: 'ps-confirm',
        // cancelButtonClass: 'ps-cancel-btn',
        // confirmButtonClass: 'ps-btn',
        closeOnClickModal: false,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {
              order_ids: this.refundOrderIds
            }
            let res
            if (this.current) {
              // 预约退款
              res = await this.$apis.apiBackgroundOrderOrderReservationReservationOrderRefundPost(params)
            } else {
              // 堂食退款
              res = await this.$apis.apiBackgroundOrderOrderPaymentInstoreRefundPost(params)
            }
            this.dialogLoading = false
            if (res.code === 0) {
              this.initLoad()
              this.$message.success(`操作成功，其中不可退款订单数${this.selectListId.length - this.refundOrderIds.length}笔`)
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              this.dialogLoading = false
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    openDialog() {
      // 改造：先判断是否全选，再判断有无数据
      // 从后台获取全部ID
      if (!this.selectListId.length) {
        // 弹窗选择是否全选
        return this.$message.error('请先选择数据！')
      }
      this.printTicketVisible = true
    },
    // 监听表格的sortChange事件实现排序
    sortChange(row) {
      // 具体判断还需结合接口考虑
      this.orderBy = row.order
      this.sort_type = row.order
      this.sort_date_type = row.prop
      // 后台请求
      if (this.current === 1) {
        this.getConsumptionList()
      } else {
        this.getOnSceneList()
      }
    },
    // 导出菜品明细
    mulExportFood() {
      if (this.dialogLoading) return
      if (!this.selectListId.length) return this.$message.error('请先选择数据')
      let params = {
        order_payment_ids: this.selectListId
      }
      const option = {
        type: 'OrderPaymentLiveExport',
        url: 'apiBackgroundReportCenterManageReportFoodPaymentDetailListExportPost',
        params
      }
      console.log("this.selectListId", this.selectListId);
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
// 日历
// .el-range-editor--mini.el-input__inner {
//   height: 32px !important;
//   width: 320px;
// }
.el-loading-wrapp{
  .el-loading-spinner{
    margin-top:0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
.container-wrapper {
  // font-size: 12px !important;
  .active {
    background-color: #ff9b45!important;
    color: #fff!important;
  }
  .el-button:focus.el-button--default:not(.is-plain):not(.el-button--primary), .el-button:hover.el-button--default:not(.is-plain):not(.el-button--primary) {
    background-color: #fff;
    color: #ff9b45;
  }
  .searchref_top {
    margin-bottom: 10px;
    .el-button {
      width: 120px;
    }
  }
  .ps-small-box {
    .block {
      display: inline-block;
    }
    .el-select {
      width: 200px !important;
    }
    .el-input {
      width: 180px !important;
    }
  }
}
.table-wrapper {
  .el-table {
    text-align: center;
    font-size: 12px;
  }
  .total {
    margin-top: 10px;
    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 14px;
    }
  }
}
.twoRefund {
  text-align: center;
}
.search-form-wrapper {
  overflow: hidden;
  // border-radius: 6px;
  .search-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 20px;
    &:after {
      content: '';
      position: absolute;
      left: 24px;
      right: 24px;
      bottom: 0;
      height: 1px;
      background-color: #e7ecf2;
    }
    .search-h-l {
      border-left: 4px solid #ff9b45;
      padding-left: 18px;
      font-size: 20px;
      color: #23282d;
    }
    .search-h-r {
      display: flex;
      align-items: center;
      .search-h-r-btn {
        margin-right: 10px;
        min-width: auto;
      }
      .search-collapse-btn {
        width: 121px;
        height: 16px;
        cursor: pointer;
        img {
          display: inline-block;
          width: 100%;
          vertical-align: middle;
        }
      }
    }
  }
  .collapse-wrapper {
    padding: 0 20px;
    // overflow: hidden;
  }
  .search-item-w {
    width: 200px;
  }
  .vue-treeselect__control {
    height: 40px;
  }
  .vue-treeselect__placeholder {
    line-height: 40px;
    font-size: 13px;
  }
}

.refund-confirm {
  .refund-radio{
    margin: 25px 0 10px;
  }
  .refund-info{
    line-height: 40px;
    .inline-box{
      display: inline-block;
    }
    .refund-info-item{
      min-width: 150px;
    }
  }
}
.footer-btn{
  .ps-btn{
    background-color: #ff9b45;
    color: #fff;
    border: #ff9b45;
  }
}
</style>
