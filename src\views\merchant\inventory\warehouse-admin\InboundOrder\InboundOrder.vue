<template>
  <div class="InboundOrder container-wrapper">
    <refresh-tool v-if="showRefresh" @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          数据列表
          <span class="inline-block m-l-20 font-size-16">
            当前仓库：
            <span style="color: 000; font-weight: 700">{{ $route.query.warehouse_name }}</span>
          </span>
        </div>
        <div class="align-r">
          <button-icon color="origin" @click="showDraft = true">草稿箱</button-icon>
          <!-- <button-icon color="origin" @click="openImport">导入入库</button-icon> -->
          <button-icon color="origin" @click="gotoAddInboundOrder('add')">新增入库单</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button v-if="row.is_entry === 0 && row.approve_status !== 'AGREE'" type="text" size="small" class="ps-text" @click="gotoHandle('modify', row)">重新编辑</el-button>
              <el-button v-if="row.is_entry === 0 && row.approve_status === 'AGREE'" type="text" size="small" class="ps-text" @click="gotoWarehousing(row)">入库</el-button>
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <import-files
      :show.sync="showImportDialog"
      :templateUrl="importTemplateUrl"
      :api="importApi"
      :params="importParamsData"
    ></import-files>
    <!-- dialog end -->
    <!-- 草稿箱 star -->
    <draft-box-dialog
      :showdialog.sync="showDraft"
      :api="draftApi"
      :params="draftParams"
      :tableSettings="drafttableSettings"
      @recovery="recoveryHandle"
    />
    <!-- 草稿箱 end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import DraftBoxDialog from '../../components/DraftBoxDialog'

export default {
  name: 'ProcureOfferList',
  mixins: [exportExcel],
  components: { DraftBoxDialog },
  props: {
    showRefresh: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      warehouseId: '',
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [{}],
      tableSettings: [
        { label: '创建时间', key: 'create_time' },
        { label: '入库时间', key: 'entry_time' },
        { label: '单据编号', key: 'trade_no' },
        { label: '入库类型', key: 'inventory_entry_type_alias' },
        { label: '审核状态', key: 'approve_status_alias' },
        { label: '入库状态', key: 'entry_display' },
        { label: '经手人', key: 'account_name' },
        { label: '备注', key: 'remark', showTooltip: true },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', width: '160px', fixed: 'right' }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '',
          value: 'create_time',
          maxWidth: '130px',
          placeholder: '请选择',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '入库时间',
              value: 'entry_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        },
        inventory_entry_type: {
          type: 'select',
          label: '入库类型',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '采购入库',
              value: 'PURCHASE_ENTRY'
            },
            // {
            //   label: '赠予入库',
            //   value: 'BESTOW_ENTRY'
            // },
            {
              label: '调拨入库',
              value: 'BORROW_ENTRY'
            },
            {
              label: '归还入库',
              value: 'RETURN_ENTRY'
            },
            {
              label: '其他入库',
              value: 'OTHER_ENTRY'
            }
          ]
        },
        is_entry: {
          type: 'select',
          label: '入库状态',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '已入库',
              value: 1
            },
            {
              label: '未入库',
              value: 0
            },
          ]
        }
      },
      // 导入的弹窗数据
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/drp/导入入库单.xlsx',
      importHeaderLen: 2,
      importApi: 'apiBackgroundDrpEntryInfoImportDataPost',
      importParamsData: {
        warehouse_id: this.$route.query.warehouse_id
      },
      // 草稿弹窗
      showDraft: false,
      drafttableSettings: [
        { label: '草稿名称', key: 'name' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right' }
      ],
      draftApi: 'apiBackgroundDrpTemplateInfoTempListPost',
      draftParams: {
        warehouse_id: +this.$route.query.warehouse_id,
        inventory_info_type: 'entry_info',
        temp_type: 'draft'
      }
    }
  },
  created() {
    this.warehouseId = this.$route.query.warehouse_id
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getInboundOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getInboundOrderList() {
      if (!this.warehouseId) return this.$message.error('获取仓库id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: this.warehouseId,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpEntryInfoListPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getInboundOrderList()
    },
    gotoAddInboundOrder(type, data) {
      this.$router.push({
        name: 'AddInboundOrder',
        params: {
          type
        },
        query: {
          ...this.$route.query
        }
      })
    },
    openImport(type) {
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    gotoWarehousing(row) {
      this.$router.push({
        name: 'Warehousing',
        query: {
          id: row.id,
          ...this.$route.query
        }
      })
    },
    gotoHandle(type, row) {
      if (type === 'detail') {
        this.$router.push({
          name: 'InboundOrderDetail',
          query: {
            ...this.$route.query,
            id: row.id
          }
        })
      }
      if (type === 'modify') {
        this.$router.push({
          name: 'AddInboundOrder',
          params: {
            type
          },
          query: {
            ...this.$route.query,
            id: row.id
          }
        })
      }
    },
    // 草稿恢复编辑
    recoveryHandle(data) {
      let query = {
        ...this.$route.query,
        id: data.id, // 草稿id
        type: 'recovery' // 类型
      }
      this.$router.push({
        name: 'AddInboundOrder',
        query: query,
        params: {
          type: 'add'
        }
      })
    }
    // handleExport(row) {
    //   const option = {
    //     type: 'InquiryList',
    //     url: 'apiBackgroundDrpInquiryExportInquiryPost',
    //     params: {
    //       id: row.id
    //     }
    //   }
    //   this.exportHandle(option)
    // }
  }
}
</script>

<style lang="scss" scoped>
.InboundOrder {
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
}
</style>
