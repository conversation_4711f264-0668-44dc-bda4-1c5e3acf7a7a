<template>
  <el-drawer
    title="选择项目"
    :visible.sync="showDrawer"
    :direction="direction"
    :wrapperClosable="wrapperClosable"
    class="drawer-wrapper"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <div class="p-20">
      <el-form
        :model="formData"
        label-width="80px"
        :rules="formRules"
        ref="formRef"
      >
        <el-form-item label="项目" prop="companyId">
          <company-select
            class="search-item-w ps-select"
            v-model="formData.companyId"
            :clearable="true"
            :filterable="true"
            :options="{
              label: 'name',
              value: 'company'
            }"
            :params="companyParams"
            placeholder="请选择项目"
            style="width:300px;"
            @getselect="getCompanySelect"
            ></company-select>
        </el-form-item>
        <el-form-item label="日期" prop="date">
          <el-date-picker
            v-model="formData.date"
            type="date"
            placeholder="请选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width:300px;"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="餐段" prop="mealType">
          <el-select
            v-model="formData.mealType"
            clearable
            filterable
            class="ps-input"
            placeholder="请选择餐段"
            style="width:300px;"
            @change="changeMeal"
          >
            <el-option
              v-for="item in mealTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop=""  class="m-t-60">
          <el-button class="ps-cancel min-btn-w" @click="clickCancelHandle">取消</el-button>
          <el-button class="ps-origin-btn min-btn-w" @click="clickConfirmHandle">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
</template>

<script>
import CompanySelect from '@/components/CompanySelect'
import { MEAL_TYPES } from "@/utils/constants"
import { parseTime } from '@/utils'

export default {
  name: '',
  components: {
    CompanySelect
  },
  props: {
    show: {
      required: true,
      type: Boolean
    },
    wrapperClosable: {
      type: Boolean,
      default: true
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      // 表单
      formData: {
        companyName: '',
        companyId: '',
        date: parseTime(new Date(), '{y}-{m}-{d}'),
        mealType: '',
        mealName: ''
      },
      // 表单校验规则
      formRules: {
        companyId: [{ required: true, message: '请选择项目', trigger: 'change' }],
        date: [{ required: true, message: '请选择日期', trigger: 'change' }],
        mealType: [{ required: true, message: '请选择餐段', trigger: 'change' }]
      },
      mealTypeList: MEAL_TYPES,
      companyParams: {}
    }
  },
  computed: {
    showDrawer: {
      get() {
        if (this.show) {
          this.formData = Object.assign(this.formData, this.infoData)
        }
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  watch: {

  },
  created() {
    // 正式环境只有一个项目点哦，说先写死
    if (process.env.NODE_ENV === 'production' && location.origin.indexOf('debug') < 0) {
      [26, 58]
      this.companyParams = {
        company__in: [26, 58]
      }
    } else {
      this.companyParams = {
        // company__in: [301]
      }
    }
  },
  mounted() {

  },
  methods: {
    //
    getCompanySelect(select) {
      console.log(select)
      this.formData.companyName = select.item.name
    },
    changeMeal(e) {
      const mealItem = this.mealTypeList.find(v => {
        return v.value === e
      })
      console.log(mealItem)
      this.formData.mealName = mealItem.label
    },
    // 取消
    clickCancelHandle() {
      this.showDrawer = false
    },
    // 确定
    clickConfirmHandle() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.$emit('confirm', this.formData)
          this.resetForm()
          // 也不需要提交数据直接在这关闭算了
          this.showDrawer = false
        } else {

        }
      })
    },
    // 重置表单
    resetForm() {
      this.formData = {
        companyName: '',
        companyId: '',
        date: '',
        mealType: ''
      }
      this.$refs.formRef.clearValidate()
    }
  }
};
</script>

<style lang="scss">
.drawer-wrapper {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
  .min-btn-w {
    min-width: 80px;
  }
  .el-button+.el-button {
      margin-left: 20px;
  }
}
</style>
