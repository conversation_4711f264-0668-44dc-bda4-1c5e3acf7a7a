<template>
  <div class="exclusive-rights container-wrapper">
    <!--刷新页面-->
    <refresh-tool @refreshPage="refreshHandle" />
    <!--菜单切换-->
    <div class="tab">
      <div :class="['tab-item', tabType === 'record' ? 'active' : 'no-active']" @click="tabClick('record')">
        购买记录
      </div>
      <div :class="['tab-item', tabType === 'setting' ? 'active' : 'no-active']" @click="tabClick('setting')">
        权益配置
      </div>
    </div>
    <!--搜索层-->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler"
      @reset="resetHandler"></search-form>
    <!--表格-->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="showRecordDialog('send', row)"
            v-if="tabType === 'record'">手动发放</button-icon>
          <el-button class="ps-origin-plain-btn h-26" @click="goToMemberSetting()"
            v-if="tabType === 'setting'">功能配置</el-button>
          <button-icon color="origin" @click="showRecordDialog('add')" v-if="tabType === 'setting'">新增</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" height="460" stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #permission="{ row }">
              {{ getPermissonName(row.member_permissions_name) }}
            </template>
            <template #price="{ row }">
              {{ getOriginPrice(row) }}
            </template>
            <template #days="{ row }">
              {{ row.days + '天' }}
            </template>
            <template #name="{ row }">
              {{ row.name || '--' }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text-blue"
                @click="showRecordDialog('edit', row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-text-blue" @click="handlerDeleteRecord(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" v-if="tabType === 'record'" />
      <!-- table content end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination @size-change="handlerSizeChange" @current-change="handlerPageChange" :current-page.sync="currentPage"
          :page-size.sync="pageSize" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount"
          ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 -->
    <member-exclusive-dialog ref="exclusiveDialog" :title="dialogTitle" :type="dialogType" :seqs-list="seqsList"
      @confirm="handlerConfirmBack"></member-exclusive-dialog>
  </div>
</template>
<script>
import { debounce, deepClone, to, divide } from '@/utils'
import { SEARCH_FORM_RECORD_DATA, SEARCH_FORM_EXCLUSIVE_DATA, TABLE_HEAD_RECORD_DATA, TABLE_HEAD_EXCLUSIVE_DATA } from './constants.js'
import MemberExclusiveDialog from './components/MemberExclusiveDialog.vue'
export default {
  name: 'MemberExclusiveRightsList',
  data() {
    return {
      tabType: 'record',
      searchFormSetting: deepClone(SEARCH_FORM_RECORD_DATA),
      tableSettings: deepClone(TABLE_HEAD_RECORD_DATA),
      collect: [
        { key: 'user_count', value: 0, label: '购买用户数:', unit: '人' },
        { key: 'total_amount', value: 0, label: '购买金额:', type: 'moneyRmb', unit: '元' }
      ],
      dialogVisible: false, // 弹窗是否可见
      dialogTitle: '', // 弹窗标题
      dialogType: '', // 弹窗类型
      isLoading: false, // 表格是否加载
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页数量
      totalCount: 0,
      tableData: [], // 表单数据
      permissionList: [], // 权益列表
      seqsList: [] // 排序列表
    }
  },
  components: {
    MemberExclusiveDialog
  },
  created() {
    this.initData();
  },
  methods: {
    // 初始化数据
    initData() {
      this.getMemberPermission()
      this.getDataList()
    },
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initLoad()
    },
    // 切换菜单
    tabClick(type) {
      this.tabType = type
      this.searchFormSetting = type === 'record' ? deepClone(SEARCH_FORM_RECORD_DATA) : deepClone(SEARCH_FORM_EXCLUSIVE_DATA)
      this.tableSettings = type === 'record' ? deepClone(TABLE_HEAD_RECORD_DATA) : deepClone(TABLE_HEAD_EXCLUSIVE_DATA)
      if (type === 'record') {

      } else {
        this.searchFormSetting.member_permissions.dataList = deepClone(this.permissionList)
      }
      this.getDataList()
    },
    // 节下流咯
    searchHandler: debounce(function () {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getDataList()
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.currentPage = 1;
      this.getDataList()
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    handlerPageChange(val) {
      console.log("handlerPageChange", val);
      this.currentPage = val
      this.getDataList()
    },
    /**
     * 页面条数改变监听
     */
    handlerSizeChange(val) {
      console.log("handlerSizeChange", val);
      this.pageSize = val
      this.getDataList()
    },
    /**
     * 获取二级商户列表
     */
    async getDataList() {
      if (this.isLoading) return
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      var api = ''
      if (this.tabType === 'record') {
        api = this.$apis.apiBackgroundMemberRightsReceiveListPost(params)
      } else {
        api = this.$apis.apiBackgroundMemberRightsSettingListPost(params)
      }
      this.isLoading = true
      const [err, res] = await to(api)
      console.log("apiBackgroundSubMerchantInfoList", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        var resultList = res.data.results || []
        if (Array.isArray(resultList) && resultList.length > 0) {
          // 给列表增加序号
          resultList.map((item, index) => {
            item.index = index + 1 + this.pageSize * (this.currentPage - 1)
            return item
          })
        }
        this.tableData = deepClone(resultList)
        this.totalCount = res.data.count || -1
        if (this.tabType !== 'record') {
          var sequsList = res.data.summary_data ? res.data.summary_data.seqs : []
          if (sequsList) {
            this.seqsList = deepClone(sequsList)
          }
          console.log("this.seqsList", this.seqsList);
        }
        this.collect.forEach(item => {
          if (res.data.summary_data && res.data.summary_data[item.key] !== undefined) {
            this.$set(item, 'value', res.data.summary_data[item.key])
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'member_permissions') {
            params[key] = [data[key].value]
          } else
          if (key === 'select_time') {
            params.start_date = data[key].value[0] || ''
            params.end_date = data[key].value[1] || ''
          } else
          if (key === 'receive_type') {
            params.receive_type = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    // 跳转设置
    goToMemberSetting() {
      this.$router.push({
        name: 'MemberExclusiveSetting'
      })
    },
    // 删除
    handlerDeleteRecord(item) {
      var id = item.id || ''
      // 弹窗二次确认
      this.$confirm(`是否删除权益配置？`, '提示', {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundMemberRightsSettingDeletePost({
                ids: [id]
              })
            )
            instance.confirmButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.searchHandler()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => { })
    },
    // 根据类型显示弹窗
    showRecordDialog(type, itemData) {
      this.dialogType = type
      switch (type) {
        case 'add':
          this.dialogTitle = "新增权益"
          break;
        case 'edit':
          this.dialogTitle = "编辑权益"
          break;
        case 'send':
          this.dialogTitle = "手动发放"
          break;
        default:
          break;
      }
      if (this.$refs.exclusiveDialog) {
        if (itemData) {
          this.$refs.exclusiveDialog.setDialogData(itemData)
        }
        this.$refs.exclusiveDialog.showDialog(true)
      }
    },
    // 新增返回
    handlerConfirmBack(type) {
      this.getDataList()
    },
    // 获取权益名称
    getPermissonName(list) {
      if (!list || !Array.isArray(list)) {
        return '--'
      }
      return list.join(',')
    },
    // 获取权益
    async getMemberPermission() {
      const [err, res] = await to(this.$apis.apiBackgroundMemberMemberPermissionListPost({
        page: 1,
        page_size: 99999
      }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        this.permissionList = data.results || []
      }
    },
    // 获取价格
    getOriginPrice(row) {
      var price = row.origin_fee || ''
      if (!price) {
        return '¥ 0'
      }
      return '¥ ' + divide(price)
    }
  }
}
</script>
<style lang="scss" scoped>
.exclusive-rights {
  .tab {
    margin-bottom: 20px;

    .tab-item {
      display: inline-block;
      width: 90px;
      height: 28px;
      line-height: 28px;
      margin-right: 10px;
      text-align: center;
      border-radius: 5px;
      font-size: 16px;
      vertical-align: middle;
      cursor: pointer;

      &.active {
        color: #ffffff;
        background-color: #fd953c;
      }

      &.no-active {
        color: #fd953c;
        background-color: #ffffff;
      }
    }
  }

  .h-26 {
    line-height: 26px;
    padding: 0;
  }
}
</style>
