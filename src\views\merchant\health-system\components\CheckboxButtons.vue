<template>
  <div class="checkbox-buttons">
    <el-checkbox-group v-bind="$attrs" v-on="$listeners">
      <el-checkbox-button
        v-for="item in dataList"
        :label="item[options.value]"
        :key="item[options.value]"
        :disabled="item.disabled || disabledList.includes(item[options.value])"
      >
        {{ item[options.label] }}
      </el-checkbox-button>
    </el-checkbox-group>
  </div>
</template>

<script>
export default {
  name: 'CheckboxButton',
  inheritAttrs: false,
  props: {
    options: {
      type: Object,
      default: () => {
        return {
          value: 'id',
          label: 'name'
        }
      }
    },
    dataList: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 禁止选中项
    disabledList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      value: []
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss">
.checkbox-buttons {
  .el-checkbox-button:last-child .el-checkbox-button__inner {
    margin-right: 20px;
  }
  .el-checkbox-button__inner {
    margin-top: 5px;
    margin-right: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px !important;
  }
  .el-checkbox-button.is-focus .el-checkbox-button__inner {
    border-color: #dcdfe6;
    border: 1px solid #dcdfe6;
  }
  .el-checkbox-button.is-checked .el-checkbox-button__inner {
    color: #fff;
    background-color: #fd9445;
    border-color: #fd9445;
    -webkit-box-shadow: -1px 0 0 0 #fd9445;
    box-shadow: -1px 0 0 0 #fd9445;
  }
  .is-disabled{
    .el-checkbox-button__inner {
      color: #ffffff;
      background-color: #d7d7d7;
      opacity: .7;
    }
  }
}
</style>
