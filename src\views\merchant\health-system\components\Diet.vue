<template>
  <div class="diet-wrapp">
    <div class="ps-flex flex-wrap m-t-10">
      <div>
        <div class="diet-title m-r-10 m-b-10">
          <div class="ps-flex flex-align-c">
            <span class="text p-l-10">饮食记录</span>
          </div>
          <!-- <div class="number p-l-20">{{ dietData.intakeRecordTotal }}</div> -->
          <div ref="intake_record" id="circular_chart" style="height:240px;"></div>
        </div>
      </div>
      <div>
        <div class="diet-title m-r-10  m-b-10">
          <div class="ps-flex flex-align-c">
            <span class="text p-l-10">记录来源</span>
          </div>
          <!-- <div class="number p-l-20">{{ dietData.sourceTotal }}</div> -->
          <div ref="source" id="circular_chart" style="height:240px;"></div>
        </div>
      </div>
      <div>
        <div class="diet-title m-r-10  m-b-10">
          <div class="ps-flex flex-align-c">
            <span class="text p-l-10">摄入超标</span>
          </div>
          <!-- <div class="number p-l-20">{{ dietData.intakeExceedTotal }}</div> -->
          <div ref="intake_exceed" id="circular_chart" style="height:240px;"></div>
        </div>
      </div>
      <div>
        <div class="diet-title m-r-10  m-b-10">
          <div class="ps-flex flex-align-c">
            <span class="text p-l-10">食物种类</span>
          </div>
          <!-- <div class="number p-l-20">{{ dietData.foodCategoryTotal }}</div> -->
          <div ref="food_category" id="circular_chart" style="height:240px;"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { MEALTIME_SETTING } from '../constants'
import { debounce } from '@/utils'
import NP from 'number-precision'
export default {
  props: {
    formInfoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      formData: {},
      circularChartRefList: [
        {
          // type: 'unreached',
          key: 'intake_record',
          data: [
            { value: 0, name: '早餐', key: 'breakfast', current: 0, unit: '次' },
            { value: 0, name: '午餐', key: 'lunch', current: 0, unit: '次' },
            { value: 0, name: '晚餐', key: 'dinner', current: 0, unit: '次' }
          ],
          color: ['#07DED0', '#FE985F', '#e98397']
        },
        {
          // type: 'reached',
          key: 'source',
          data: [
            { value: 0, name: '系统', key: 'system', current: 0, unit: '次' },
            { value: 0, name: '手动', key: 'user', current: 0, unit: '次' }
          ],
          color: ['#FE985F', '#f6c80e']
        },
        {
          // type: 'exceed',
          key: 'intake_exceed',
          data: [
            { value: 0, name: '早餐', key: 'breakfast', current: 0, unit: '次' },
            { value: 0, name: '午餐', key: 'lunch', current: 0, unit: '次' },
            { value: 0, name: '晚餐', key: 'dinner', current: 0, unit: '次' }
          ],
          color: ['red', '#FE985F', '#e98397']
        },
        {
          // type: 'exceed',
          key: 'food_category',
          // title: '摄入过量',
          data: [
            { value: 0, name: '谷类薯类', key: 'cereals_tubers', current: 0, unit: '种' },
            { value: 0, name: '鱼禽蛋肉', key: 'eggsandmeat', current: 0, unit: '种' },
            { value: 0, name: '蔬菜水果', key: 'fruit_vegetable', current: 0, unit: '种' },
            { value: 0, name: '奶类豆类', key: 'dairy', current: 0, unit: '种' }
          ],
          color: ['#08d7d7', '#4e95fa', '#4ad96c', '#727aff']
        }
      ],
      pieChart: {
        intake_record: null,
        source: null,
        intake_exceed: null,
        food_category: null
      },
      tabType: 'food', // 菜品食材
      tableData: [],
      dietData: {
        intake_exceed_total: 0, // 摄入超标
        intake_record_total: 0, // 摄入记录
        source_total: 0, // 来源
        food_category_total: 0 // 种类
      },
      currentPage: 1,
      pageSize: 6,
      totalCount: 0,
      foodList: [],
      ingredientList: []
    }
  },
  watch: {
    formInfoData(val) {
      this.tabType = 'food'
      this.formData = val
      // 总数
      this.dietData.intake_record_total = NP.plus(
        this.formData.intake_record.breakfast,
        this.formData.intake_record.lunch,
        this.formData.intake_record.dinner
      )
      this.dietData.source_total = NP.plus(this.formData.source.system, this.formData.source.user)
      this.dietData.intake_exceed_total = NP.plus(
        this.formData.intake_exceed.breakfast,
        this.formData.intake_exceed.lunch,
        this.formData.intake_exceed.dinner
      )
      this.dietData.food_category_total = NP.plus(
        this.formData.food_category.cereals_tubers,
        this.formData.food_category.dairy,
        this.formData.food_category.eggsandmeat,
        this.formData.food_category.fruit_vegetable
      )
      // table数据
      this.tableData = this.formData.food_list
      this.foodList = this.formData.food_list
      this.ingredientList = this.formData.ingredient_list

      // 健康分
      this.$nextTick(() => {
        this.initMealTimeDataPie()
      })
    }
  },
  created() {},
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChartHandle)
  },
  mounted() {
    window.addEventListener('resize', this.resizeChartHandle)
  },
  methods: {
    // 初始化餐段统计数据
    initMealTimeDataPie() {
      this.circularChartRefList.forEach(v => {
        let data = v.data
        let nameKey = {}
        data.forEach(item => {
          if (v.key === 'intake_record') {
            if (this.formData[v.key][item.key] / this.dietData.intake_record_total) {
              item.value = Number(
                (
                  (this.formData[v.key][item.key] / this.dietData.intake_record_total) *
                  100
                ).toFixed(2)
              )
            }
          }
          if (v.key === 'source') {
            if (this.formData[v.key][item.key] / this.dietData.source_total) {
              item.value = Number(
                ((this.formData[v.key][item.key] / this.dietData.source_total) * 100).toFixed(2)
              )
            }
          }
          if (v.key === 'intake_exceed') {
            if (this.formData[v.key][item.key] / this.dietData.intake_exceed_total) {
              item.value = Number(
                (
                  (this.formData[v.key][item.key] / this.dietData.intake_exceed_total) *
                  100
                ).toFixed(2)
              )
            }
          }
          if (v.key === 'food_category') {
            if (this.formData[v.key][item.key] / this.dietData.food_category_total) {
              item.value = Number(
                (
                  (this.formData[v.key][item.key] / this.dietData.food_category_total) *
                  100
                ).toFixed(2)
              )
            }
          }
          nameKey[item.name] = {
            value: item.value,
            current: this.formData[v.key][item.key],
            unit: item.unit
          }
        })
        // 名字
        // MEALTIME_SETTING.title.subtext = v.title
        let mealtimeSetting = MEALTIME_SETTING
        mealtimeSetting.legend.formatter = function(name) {
          let obj = nameKey[name]
          return name + '    ' + (obj.value || 0) + '%' + '    ' + (obj.current || 0) + obj.unit
        }
        mealtimeSetting.series[0].data = data
        // mealtimeSetting.color = v.color + v.key === 'food_category' ? '种' : '次'
        mealtimeSetting.title.text = `${this.dietData[v.key + '_total']}${
          v.key === 'food_category' ? '种' : '次'
        }`
        console.log(v.key)
        if (!this.pieChart[v.key]) {
          this.pieChart[v.key] = this.$echarts.init(this.$refs[v.key])
        }
        this.pieChart[v.key].setOption(mealtimeSetting)
      })
    },
    tableLoadmore() {
      setTimeout(() => {
        this.pageSize += 10
      }, 100)
      // this.$message.success('加载下一页')
    },
    // tab 栏点击事件
    tabClick(type) {
      this.tabType = type
      this.tableData = []
      this.pageSize = 6
      if (type === 'food') {
        this.tableData = this.foodList
      } else {
        this.tableData = this.ingredientList
      }
    },
    resizeChartHandle: debounce(function() {
      if (this.pieChart.intake_record) this.pieChart.intake_record.resize()
      if (this.pieChart.source) this.pieChart.source.resize()
      if (this.pieChart.intake_exceed) this.pieChart.intake_exceed.resize()
      if (this.pieChart.food_category) this.pieChart.food_category.resize()
    }, 300)
  }
}
</script>

<style lang="scss">
.diet-wrapp {
  padding: 10px 20px;
  // min-width: 873px;
  .diet-title {
    // width: 25%;
    // width: 290px;
    background-color: #f1f2f5;
    border-radius: 10px;
    padding: 10px;
    .text {
      font-size: 14px;
      color: #7b7c82;
    }
    .number {
      font-size: 34px;
    }
  }
  .diet-item {
    display: flex;
    flex-wrap: wrap;
    height: 100%;
    border-radius: 10px;
  }
  #circular_chart {
    position: relative;
    width: 270px;
    height: 100%;
  }
  .tab {
    margin-bottom: 20px;
    .tab-item {
      display: inline-block;
      padding: 0 25px;
      height: 28px;
      line-height: 28px;
      margin-right: 10px;
      text-align: center;
      border-radius: 14px;
      border: solid 1px #dae1ea;
      font-size: 14px;
      color: #7b7c82;
      vertical-align: middle;
      cursor: pointer;
      &.active {
        color: #ffffff;
        background-color: #fd953c;
      }
    }
  }
  .table-index-1 {
    padding: 2px 7px 2px 7px;
    border-radius: 50px;
    color: #fff;
    background-color: #fd953c;
  }
  .table-index-2 {
    padding: 2px 7px 2px 7px;
    border-radius: 50px;
    color: #fff;
    background-color: #f6c80e;
  }
  .table-index-3 {
    padding: 2px 7px 2px 7px;
    border-radius: 50px;
    color: #fff;
    background-color: #a1a5a9;
  }
}
.el-progress {
  display: flex;
  align-items: center;
  .el-progress-bar {
    flex: 1;
  }
}
.el-form-item__label {
  font-weight: normal;
}
</style>
