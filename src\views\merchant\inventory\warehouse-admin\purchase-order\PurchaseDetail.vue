<template>
  <div class="PurchaseDetail container-wrapper">
    <h3 class="m-t-20">仓库管理/采购单/详情</h3>
    <div class="">
      <div class="absolute-btn align-r p-t-20 p-b-20">
        <el-button class="ps-btn" size="small" @click="backHandle">返回</el-button>
        <el-button class="ps-btn" size="small" @click="handleExport">导出</el-button>
        <el-button class="ps-btn" size="small" v-print="printObj">打印</el-button>
      </div>
      <!-- 需打印的内容部分 start -->
      <div id="print-box">
        <div class="box-wrapper p-20">
          <div class="text-center m-b-20 font-size-26">采购单</div>
          <div class="header m-b-20">
            <span class="inline-block m-r-30">
              当前仓库：
              <span style="font-weight: 600">{{ detailData.warehouse_name }}</span>
            </span>
            <span class="inline-block m-r-30">单据编号：{{ detailData.trade_no }}</span>
            <span class="inline-block m-r-30">采购时间：{{ detailData.purchase_time }}</span>
            <span class="inline-block m-r-30">经手人：{{ detailData.account_name }}</span>
          </div>
          <div class="">
            <!-- table start -->
            <el-table
              v-loading="isLoading"
              :data="tableData"
              ref="tableData"
              style="width: 100%"
              stripe
              size="small"
              header-row-class-name="ps-table-header-row"
              @selection-change="handleSelectionChange"
            >
              <table-column v-for="item in tableSettings" :key="item.key" :col="item"></table-column>
            </el-table>
            <!-- table end -->
          </div>
          <p class="p">合计：{{ totalPrice | formatMoney }}元</p>
          <div class="footer m-t-20">
            <span class="inline-block m-r-30">制单人：{{ detailData.account_name }}</span>
            <span class="inline-block m-r-30">制单日期：{{ new Date() | formatDate('YYYY-MM-DD HH:mm') }}</span>
            <span class="inline-block m-r-30">审核人：{{ detailData.approve_account_name }}</span>
            <span class="inline-block m-r-30">审核日期：{{ detailData.approve_time | formatDate('YYYY-MM-DD HH:mm') }}</span>
          </div>
        </div>
        <div class="approve-box">
          <h3 style="margin: 0 0 30px;">审批进度</h3>
          <template v-if="detailData.operator_record && detailData.operator_record.length > 0">
            <el-timeline class="m-t-20 m-l-20">
              <el-timeline-item hide-timestamp size="large" v-for="(item, index) in detailData.operator_record" :key="index">
                <div class="step">
                  <div class="step-item step-status">{{ item.approve_status || '--' }}</div>
                  <div class="step-item step-name">{{ item.operator }}</div>
                  <div class="step-item step-no">{{ item.role_name }}</div>
                  <div class="step-item step-time">{{ item.time }}</div>
                  <div class="step-item step-reason">{{ item.content }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </template>
          <div v-else class="m-l-20" style="font-size: 13px;">空</div>
        </div>
      </div>
      <!-- 需打印的内容部分 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import printUtils from '@/mixins/printUtils' // 导出混入
// import report from '@/mixins/report' // 混入
import print from 'vue-print-nb'
import NP from 'number-precision'

export default {
  name: 'ProcureOfferList',
  mixins: [exportExcel, printUtils],
  directives: {
    print
  },
  components: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      accountName: this.$store.getters.userInfo.member_name,
      warehouseId: '',
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      detailData: {},
      tableData: [],
      tableSettings: [
        { label: '物资名称', key: 'name' },
        { label: '物资分类', key: 'materail_classification_name' },
        { label: '数量', key: 'count' },
        { label: '规格', key: 'material_specification_record' },
        { label: '最小单位', key: 'unit_name' },
        { label: '最小单位数量', key: 'limit_count_record' },
        { label: '采购价', key: 'ref_unit_price', type: 'money' },
        { label: '合计', key: 'total', type: 'money' },
        { label: '供应商', key: 'supplier_manage_name' }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '',
          value: 'create_time',
          maxWidth: '130px',
          placeholder: '请选择',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '入库时间',
              value: 'entry_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '',
          clearable: true,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        },
        order_status: {
          type: 'select',
          label: '入库类型',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '采购入库',
              value: 'PURCHASE_ENTRY'
            },
            // {
            //   label: '赠予入库',
            //   value: 'BESTOW_ENTRY'
            // },
            {
              label: '调拨入库',
              value: 'entry_time'
            },
            {
              label: '其他入库',
              value: 'OTHER_ENTRY'
            }
          ]
        }
      },
      // 导入的弹窗数据
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: '',
      importHeaderLen: 2,
      importApi: 'apiBackgroundFoodIngredientSupplierBatchAddOfferPost',
      showDraft: false, // 打开草稿箱
      printObj: {
        id: '#print-box', // 这里是要打印元素的ID
        popTitle: '&nbsp', // 打印的标题
        extraCss: '', // 打印可引入外部的一个 css 文件
        extraHead: '<style>.el-image{width: 100px;height: 100px;display:inline-block;}.approve-box {margin-top: 20px;padding: 20px;border-radius: 12px;background-color: #fff;}.approve-box .step {padding: 10px;background-color: #f2f2f2;border-radius: 4px;}.approve-box .step-item {display: table-cell;min-width: 80px;padding: 0 10px;text-align: center;}.approve-box .step-status {width: 100px;}.approve-box .step-name {width: 120px;}.approve-box .step-no {width: 120px;}.approve-box .el-timeline-item__wrapper {top: -12px;}.approve-box .el-timeline-item:not(:last-child) {padding-bottom: 36px;}.approve-box .el-timeline-item:last-child {padding-bottom: 0;}</style>' // 打印头部文字
      },
      totalPrice: 0
    }
  },
  created() {
    this.warehouseId = this.$route.query.warehouse_id
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    async initLoad() {
      this.getPurchaseDetail()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getPurchaseDetail() {
      if (!this.warehouseId) return this.$message.error('获取仓库id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: this.warehouseId,
        id: this.$route.query.id,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpPurchaseInfoDetailsPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        // is_apporve 为0时表示无需审批
        if (res.data.is_apporve === 0) {
          res.data.operator_record = [
            { approve_status: '审批通过', operator: '', role_name: '', time: '', content: '' }
          ]
        }
        this.detailData = res.data
        this.tableData = res.data.materials_detail
        this.totalPrice = res.data.materials_detail.reduce((prev, next) => {
          return NP.plus(prev, next.total || 0)
        }, 0)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getPurchaseDetail()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = 'apiBackgroundDrpPurchaseInfoSupplierPurchaseModifyPost'
      let params = {}
      switch (type) {
        case 'refuse':
          params = {
            id: data.id,
            supplier_refuse: true
          }
          title = '确定拒收订单吗？'
          break
        case 'accept':
          params = {
            id: data.id,
            accept_order: true
          }
          title = '确定接收订单吗？'
          break
      }
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg || '成功')
              this.getPurchaseDetail()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    gotoAddInboundOrder(type, data) {
      this.$router.push({
        name: 'AddInboundOrder',
        params: {
          type
        },
        query: {
          ...this.$route.query
        }
      })
    },
    openImport(type) {
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    gotoHandle(type, row) {
      this.$router.push({
        name: 'ModifyPurchaseOrder',
        query: {
          type,
          id: row.id
        }
      })
    },
    handleExport() {
      const option = {
        type: 'PurchaseDetail',
        url: 'apiBackgroundDrpPurchaseInfoDetailsExportPost',
        params: {
          id: this.detailData.id
        }
      }
      this.exportHandle(option)
    },
    backHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'PurchaseOrderList')
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/print.scss';
.PurchaseDetail {
  width: 100%;
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
  table {
    width: 100% !important;
    font-weight: 500;
    th {
      font-weight: 500;
    }
  }
  .absolute-btn {
    position: absolute;
    right: 30px;
  }
  .box-wrapper{
    margin-top: 20px;
    box-shadow: 6px 6px 10px 0px rgba(202, 210, 221, 0.3), inset 2px 2px 0px 0px #ffffff;
    border-radius: 12px;
    overflow: hidden;
    background-color: #f8f9fa;
  }
  #print-box {
    width: 100%;
    div {
      // width: 100%;
    }
    .el-table thead,
    .ps-table-header-row {
      width: 100%;
    }
    .footer {
      p {
        margin: 10px 0 0;
      }
    }
  }
  .footer {
    .inline-block {
      min-width: 120px;
    }
  }
  .approve-box {
    margin-top: 20px;
    padding: 20px;
    border-radius: 12px;
    background-color: #fff;
    .step {
      // width: 100%;
      // display: flex;
      // align-items: center;
      padding: 10px;
      background-color: #f2f2f2;
      border-radius: 4px;
    }
    .step-item {
      display: table-cell;
      // margin-right: 10px;
      min-width: 80px;
      padding: 0 10px;
      text-align: center;
    }
    .step-status {
      width: 100px;
    }
    .step-name {
      width: 120px;
    }
    .step-no {
      width: 120px;
    }
    .step-time {
      // padding-right: 10px;
    }
    // .step-icon {
    //   margin-right: 0;
    //   min-width: 40px;
    //   width: 40px;
    // }
    .el-timeline-item__wrapper {
      top: -12px;
    }
    .el-timeline-item:not(:last-child) {
      padding-bottom: 36px;
    }
    .el-timeline-item:last-child {
      padding-bottom: 0;
    }
  }
}
</style>
