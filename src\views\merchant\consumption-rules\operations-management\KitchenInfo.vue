<template>
  <div class="kitchen-info container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper">
      <div class="table-content m-t-20">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="mealtext" label="排班时间" align="center" width="100"></el-table-column>
          <template v-for="item in tableSetting">
            <el-table-column :key="item.key" :label="item.label" :prop="item.key" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  class="ps-black"
                  v-if="!scope.row[item.key] || scope.row[item.key].length===0"
                  @click="openChooseDialog(scope.row, item.key)"
                >未选择人员</el-button>
                <div
                  class="person-text"
                  v-else
                  @click="openChooseDialog(scope.row, item.key)"
                >
                  <el-tag
                    v-for="(person, personIndex) in scope.row[item.key]"
                    style="margin-right: 8px"
                    :key="personIndex"
                  >
                    {{ person.name }}
                    <i @click.stop="delPersonConfirm(scope.row, item.key, personIndex)" class="el-icon-error"></i>
                  </el-tag>
                </div>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <!-- table end -->
      </div>
    </div>
    <el-dialog
      custom-class="ps-dialog"
      title="选择人员"
      width="600px"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false">
      <div class="content m-b-20" v-loading="isLoading">
        <el-radio-group class="ps-radio-btn" v-model="dialogForm.selectPersonType" size="mini">
          <el-radio-button label="auto">选择人员</el-radio-button>
          <el-radio-button label="handle">手动添加</el-radio-button>
        </el-radio-group>
      </div>
      <div v-if="dialogForm.selectPersonType === 'auto'">
        <el-table
          v-loading="isLoading"
          :data="accountList"
          ref="accountList"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column prop="role_alias" label="角色名称" align="center"></el-table-column>
          <el-table-column prop="member_name" label="账号名称" align="center"></el-table-column>
          <el-table-column prop="username" label="账号" align="center"></el-table-column>
        </el-table>
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="dialogForm.currentPage"
            :page-size="dialogForm.pageSize"
            layout="total, prev, pager, next"
            :total="dialogForm.totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
      <div v-if="dialogForm.selectPersonType === 'handle'">
        <el-form
          :model="dialogForm"
          ref="selectPersonFormRef"
          :rules="selectPersonRules"
          label-width="100px"
          class="dialog-form"
          v-loading="isLoading"
        >
          <el-form-item label="人员姓名：" prop="name">
            <el-input
              v-model="dialogForm.name"
              placeholder="请输入"
              class="ps-input w-250"
            ></el-input>
          </el-form-item>
          <el-form-item label="职务：" prop="position">
            <el-input
              v-model="dialogForm.position"
              placeholder="请输入"
              class="ps-input w-250"
            ></el-input>
          </el-form-item>
          <el-form-item label="上传人脸：">
            <file-upload
              ref="faceFileRef"
              :fileList="dialogForm.fileLists"
              type="enclosure"
              :before-upload="beforeUpload"
              @fileLists="getFileLists"
              class="avatar-uploader"
              :show-file-list="false"
            >
              <img v-if="dialogForm.fileLists.length" :src="dialogForm.fileLists[0].url" class="avatar" @click="clearFileHandle">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </file-upload>
            <div class="tips">注：仅支持jpg格式文件大小不超过1M，分辨率不得超过1280*1280</div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">取消</el-button>
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { MEAL_TYPES } from '@/utils/constants'
import { to } from '@/utils'
import { confirm } from '@/utils/message'
export default {
  name: 'MerchantKitchenInfo',
  data() {
    return {
      isLoading: false, // 刷新数据
      tableData: [],
      tableSetting: [
        {
          key: 'monday',
          label: '星期一'
        }, {
          key: 'tuesday',
          label: '星期二'
        }, {
          key: 'wednesday',
          label: '星期三'
        }, {
          key: 'thursday',
          label: '星期四'
        }, {
          key: 'friday',
          label: '星期五'
        }, {
          key: 'saturday',
          label: '星期六'
        }, {
          key: 'sunday',
          label: '星期日'
        }
      ],
      dialogVisible: false,
      accountList: [],
      dialogForm: {
        selectPersonType: 'auto',
        totalCount: 0,
        currentPage: 1,
        pageSize: 5,
        name: '',
        position: '',
        fileLists: []
      },
      selectPersonRules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        position: [{ required: true, message: '请输入职务', trigger: 'change' }]
      },
      dialogInfo: {},
      dialogKey: '',
      selectList: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getKitchenInfoList()
      this.getAccountList()
    },
    // 刷新页面
    refreshHandle() {
      this.initLoad()
    },
    async getKitchenInfoList() {
      let params = {
      }
      const res = await this.$apis.apiBackgroundOperationManagementKitchenInfoListPost(params)
      if (res.code === 0) {
        this.tableData = []
        MEAL_TYPES.map(meal => {
          let info = {}
          res.data.map(item => {
            if (item.meal_type === meal.value) {
              info[item.week_day] = item.person_info
            }
          })
          info.mealkey = meal.value
          info.mealtext = meal.label
          this.tableData.push(info)
        })
        console.log(this.tableData)
      } else {
        this.$message.error(res.msg)
      }
    },
    delPersonConfirm(data, key, index) {
      confirm({ content: '是否删除该人员？' }).then(e => {
        this.delKitchenInfo(data, key, index)
      })
    },
    delKitchenInfo(data, key, index) {
      data[key].splice(index, 1)
      let params = {
        meal_type: data.mealkey,
        week_day: key,
        person_info: data[key]
      }
      this.addKitchenInfo(params)
    },
    openChooseDialog(data, key) {
      this.dialogInfo = data
      this.dialogKey = key
      this.dialogVisible = true
    },
    clickConfirmHandle() {
      let params = {
        meal_type: this.dialogInfo.mealkey,
        week_day: this.dialogKey
      }
      if (this.dialogForm.selectPersonType === 'auto') {
        if (!this.selectList.length) return this.$message.error('请先选择数据！')
        params.person_info = this.selectList.map(item => {
          return {
            name: item.member_name,
            position: item.role_alias,
            img: item.face_url ? item.face_url : ''
          }
        })
        this.addKitchenInfo(params)
      } else {
        this.$refs.selectPersonFormRef.validate(valid => {
          if (valid) {
            let info = {
              name: this.dialogForm.name,
              position: this.dialogForm.position
            }
            if (this.dialogForm.fileLists.length) info.img = this.dialogForm.fileLists[0].url
            params.person_info = [info]
            this.addKitchenInfo(params)
          } else {
          }
        })
      }
    },
    clickCancleHandle() {
      this.dialogVisible = false
    },
    async addKitchenInfo (params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOperationManagementKitchenInfoAddPost({
          kitchen_infos: [params]
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success("成功")
        this.getKitchenInfoList()
        this.dialogVisible = false
      } else {
        this.$message.error(res.msg)
        this.accountList = []
      }
    },
    // 获取账号列表
    async getAccountList () {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationAccountListPost({
          page: this.dialogForm.currentPage,
          page_size: this.dialogForm.pageSize,
          status: 1
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogForm.totalCount = res.data.count
        this.accountList = res.data.results
      } else {
        this.$message.error(res.msg)
        this.accountList = []
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.dialogForm.pageSize = val;
      this.getAccountList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.dialogForm.currentPage = val;
      this.getAccountList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectList = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => { this.selectList.push(item) })
    },
    getFileLists(fileLists) {
      this.dialogForm.fileLists = fileLists
      console.log('fileLists', fileLists)
    },
    clearFileHandle() {
      console.log('clear')
      this.$refs.faceFileRef.clearHandle()
      this.dialogForm.fileLists = []
    },
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过 1MB!');
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.kitchen-info {
  .person-text{
    cursor: pointer;
    text-decoration: underline;
  }
  .avatar-uploader{
    .avatar{
      width: 300px;
    }
  }
}
</style>
