export const TABLE_HEAD_DATA_IMPORT_PERSON = [
  { label: '姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' }
]
// 日期月份设置
export const TIME_DAY_OPTION = {
  // shortcuts: [
  //   {
  //     text: '最近一周',
  //     onClick(picker) {
  //       const end = new Date()
  //       const start = new Date()
  //       start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
  //       picker.$emit('pick', [start, end])
  //     }
  //   },
  //   {
  //     text: '最近一个月',
  //     onClick(picker) {
  //       const end = new Date()
  //       const start = new Date()
  //       start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
  //       picker.$emit('pick', [start, end])
  //     }
  //   },
  //   {
  //     text: '最近三个月',
  //     onClick(picker) {
  //       const end = new Date()
  //       const start = new Date()
  //       start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
  //       picker.$emit('pick', [start, end])
  //     }
  //   }
  // ],
  disabledDate(time) {
    return time.getTime() < Date.now() - 8.64e7
  }
}

// 限制餐段
export const LIMIT_MEAL_TYPE = [{
  key: 'breakfast',
  name: '早餐'
}, {
  key: 'lunch',
  name: '午餐'
}, {
  key: 'afternoon',
  name: '下午茶'
}, {
  key: 'dinner',
  name: '晚餐'
}, {
  key: 'supper',
  name: '宵夜'
}, {
  key: 'morning',
  name: '凌晨餐'
}]
