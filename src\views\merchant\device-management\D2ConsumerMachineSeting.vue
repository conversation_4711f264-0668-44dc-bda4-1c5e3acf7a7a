<template>
  <div class="ConsumerMachineSeting container-wrapper p-t-30">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <div class="table-wrapper" style="margin-top: 0px; margin-bottom: 50px">
      <div class="table-header">
        <div>
          <div
            class="table-title"
            style="display: flex; align-items: center; justify-content: space-between; width: 380px"
          >
            适用组织：
            <organization-select
              class="search-item-w ps-input w-250"
              placeholder="请选择所属组织"
              :isLazy="false"
              :multiple="false"
              :check-strictly="true"
              v-model="organizationId"
              @change="changeOrganization"
              :append-to-body="true"
            ></organization-select>
          </div>
        </div>
        <div style="padding-right: 20px">
          <el-button size="small" type="primary" class="ps-origin-btn" @click="checkForm('self')">
            保存
          </el-button>
          <el-button size="small" type="primary" class="ps-plain-btn" @click="openOtherOrg">
            适用到其他组织
          </el-button>
        </div>
      </div>
      <div class="setting-wrap">
        <div class="tab-type">
          <div
            v-for="item in tabTypeList"
            :key="item.key"
            :class="['tabItem', tabType === item.key ? 'activeTab' : '']"
            @click="changeType(item.key)"
          >
            {{ item.name }}
          </div>
        </div>
        <el-form
          :model="settingForm"
          :rules="settingFormRules"
          ref="settingForm"
          v-loading="loading"
        >
          <div v-show="tabType === '1'">
            <div class="title">核销设置</div>
            <el-form-item label="核销类型">
              <el-radio-group class="ps-radio m-r-30" v-model="settingForm.is_auto_verification" @change="autoVerificationChange">
                <el-radio :label="false">手动核销</el-radio>
                <el-radio :label="true">自动核销</el-radio>
              </el-radio-group>
              <div class="inline-box" v-if="settingForm.is_auto_verification">
                <el-form-item class="form-content-inline" prop="auto_verification_second">
                  <el-input
                    v-model.number="settingForm.auto_verification_second"
                    class="margin-input w-180 ps-input"
                  ></el-input>
                </el-form-item>
                秒后，自动核销
              </div>
            </el-form-item>
            <el-form-item label="核销方式">
              <el-checkbox-group v-model="settingForm.verification_way">
                <el-checkbox
                  v-for="item in verificationWayList"
                  :key="item.key"
                  :label="item.key"
                  class="ps-checkbox"
                >
                  {{ item.name }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="核销成功信息停留时间（秒）" label-width="195px">
              <el-slider
                class="detection-range-slider w-250"
                v-model="settingForm.verification_wait_time"
                :min="0"
                :max="60"
              ></el-slider>
            </el-form-item>
            <el-form-item label="核销成功是否打印小票">
              <el-radio-group class="ps-radio m-r-30" v-model="settingForm.verification_auto_print">
                <el-radio :label="true">打印</el-radio>
                <el-radio :label="false">不打印</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="状态为制作中仍支持核销">
              <el-checkbox-group v-model="settingForm.can_verification_status">
                <el-checkbox label="on_scene" class="ps-checkbox">堂食订单</el-checkbox>
                <!-- <el-checkbox label="reservation" class="ps-checkbox">预约订单</el-checkbox> -->
              </el-checkbox-group>
            </el-form-item>
            <div class="title">基础设置</div>
            <el-form-item label="默认收银模式">
              <el-radio-group class="ps-radio m-r-30" v-model="settingForm.cashier_mode">
                <el-radio label="custom_mode">自定义收银</el-radio>
                <el-radio label="fixed_mode">定额收银</el-radio>
                <el-radio label="meal_type_mode">餐段收银</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="余额查询-账户信息显示时长" label-width="185px">
              <el-slider
                class="detection-range-slider w-250"
                v-model="settingForm.user_info_wait_time"
                :min="0"
                :max="60"
              ></el-slider>
            </el-form-item>
          </div>
          <div v-show="tabType === '2'">
            <div class="title">支付方式</div>
            <el-form-item>
              <span class="m-r-5">刷卡支付</span>
              <el-switch
                v-model="settingForm.can_micropay"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
              <span class="m-l-30 m-r-5">人脸支付</span>
              <el-switch
                v-model="settingForm.can_facepay"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
              <span class="m-l-30 m-r-5">扫码支付</span>
              <el-switch
                v-model="settingForm.can_scanpay"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
            </el-form-item>
            <el-form-item>
              <span class="m-r-5">人脸支付是否确认</span>
              <el-switch
                v-model="settingForm.face_pay_confirm"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
            </el-form-item>
            <el-form-item label="支付成功信息停留时间（秒）" label-width="195px">
              <el-slider
                class="detection-range-slider w-250"
                v-model="settingForm.face_pay_confirm_wait_time"
                :min="0"
                :max="60"
              ></el-slider>
            </el-form-item>
            <div class="title">支付限制</div>
            <el-form-item>
              <span class="m-r-5">大额确认</span>
              <el-switch
                v-model="settingForm.large_amount_confirm"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
              <div class="inline-box m-l-20" v-if="settingForm.large_amount_confirm">
                支付价格大于
                <el-form-item class="form-content-inline" prop="large_amount_minimum">
                  <el-input
                    v-model="settingForm.large_amount_minimum"
                    class="margin-input w-180 ps-input"
                  ></el-input>
                </el-form-item>
                元时，需要用户二次确认
              </div>
            </el-form-item>
            <el-form-item>
              <span class="m-r-5">固定收银是否支持使用后台规则</span>
              <el-switch
                v-model="settingForm.fixed_amount_consume_rule"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
            </el-form-item>
            <el-form-item>
              <span class="m-r-5">餐段收银是否支持使用后台规则</span>
              <el-switch
                v-model="settingForm.meal_type_amount_consume_rule"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
            </el-form-item>
            <div class="title">支付限制</div>
            <el-form-item label="在线人脸识别距离" label-width="125px">
              <el-slider
                class="detection-range-slider w-250"
                v-model="settingForm.online_face_recognize_range"
                :min="100"
                :max="300"
              ></el-slider>
            </el-form-item>
          </div>
          <div v-show="tabType === '3'">
            <div class="offline-title">
              <div class="title">自动开启离线模式</div>
              <el-switch
                v-model="settingForm.autoOpenOfflineMode"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
            </div>
            <div v-if="settingForm.autoOpenOfflineMode">
              <el-form-item>
                <el-radio-group
                  class="ps-radio"
                  v-model="settingForm.openOfflineType"
                  @change="changeOpenOfflineType"
                >
                  <el-radio label="on_network_status">根据网络状态切换</el-radio>
                  <el-radio label="on_date">固定时间开启</el-radio>
                  <el-radio label="on_meal_time">固定餐段开启</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                prop="openOfflineTime"
                class="m-l-20"
                v-if="settingForm.openOfflineType === 'on_date'"
              >
                <el-time-picker
                  is-range
                  v-model="settingForm.openOfflineTime"
                  value-format="HH:mm"
                  format="HH:mm"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="选择时间范围"
                ></el-time-picker>
              </el-form-item>
              <el-form-item
                prop="openOfflineMeal"
                class="m-l-20"
                v-if="settingForm.openOfflineType === 'on_meal_time'"
              >
                <el-checkbox-group v-model="settingForm.openOfflineMeal">
                  <el-checkbox
                    v-for="item in mealList"
                    :key="item.key"
                    :label="item.key"
                    :disabled="item.disabled"
                    class="ps-checkbox"
                  >
                    {{ item.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>

            <div class="offline-title">
              <div class="title">自动上传离线订单</div>
              <el-switch
                v-model="settingForm.autoUploadOfflineOrder"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
            </div>
            <div v-if="settingForm.autoUploadOfflineOrder">
              <el-form-item>
                <el-radio-group class="ps-radio" v-model="settingForm.uploadOfflineType">
                  <el-radio label="auto">联网自动上传</el-radio>
                  <el-radio label="on_date">固定时间上传</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                prop="uploadOfflineTime"
                class="m-l-20"
                v-if="settingForm.uploadOfflineType === 'on_date'"
              >
                <el-time-picker
                  v-model="settingForm.uploadOfflineTime"
                  value-format="HH:mm"
                  format="HH:mm"
                  placeholder="选择时间"
                ></el-time-picker>
              </el-form-item>
            </div>

            <div class="title">离线人脸设置</div>
            <el-form-item label="是否自动开启离线人脸支付" label-width="180px">
              <el-switch
                v-model="settingForm.isAutoOpenOfflineFacePay"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
              <span class="tips">注：关闭则设备离线时，不支持使用人脸绑盘/支付。</span>
            </el-form-item>
            <el-form-item label="离线人脸识别分数" label-width="180px">
              <el-slider
                class="detection-range-slider w-250"
                v-model="settingForm.offlineFaceRecognizeScore"
                :min="75"
                :max="100"
              ></el-slider>
            </el-form-item>
            <el-form-item label="离线人脸识别距离（像素）" label-width="180px">
              <el-slider
                class="detection-range-slider w-250"
                v-model="settingForm.detectionRange"
                :min="100"
                :max="300"
              ></el-slider>
            </el-form-item>
            <el-form-item label="离线是否开启活体检测" label-width="180px">
              <el-switch
                v-model="settingForm.isAutoOpenLiveDetection"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
            </el-form-item>
            <el-form-item label="活体检测识别分数" label-width="180px">
              <el-slider
                class="detection-range-slider w-250"
                v-model="settingForm.liveFaceRecognizeScore"
                :min="50"
                :max="100"
              ></el-slider>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <setting-dialog
      :isshow.sync="dialogVisible"
      type="other"
      title="请选择适用的组织"
      :confirm="dialogConfirm"
      @otherOrgConfirm="otherOrgConfirm"
    />
  </div>
</template>

<script>
import NP from 'number-precision'
import OrganizationSelect from '@/components/OrganizationSelect'
import SettingDialog from './components/SettingDialog.vue'
export default {
  components: {
    OrganizationSelect,
    SettingDialog
  },
  data() {
    let validMoney = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('金额不能为空'))
      } else {
        let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      }
    }
    return {
      loading: false,
      organizationId: '',
      organizationIds: [],
      dialogVisible: false,
      tabType: '1',
      tabTypeList: [
        {
          name: '基础设置',
          key: '1'
        },
        {
          name: '支付设置',
          key: '2'
        },
        {
          name: '离线设置',
          key: '3'
        }
      ],
      settingForm: {
        is_auto_verification: true, // 核销类型
        auto_verification_second: 0, // 核销多少秒
        verification_way: [], // 核销方式
        verification_wait_time: 0, // 核销成功信息停留时间（秒）
        verification_auto_print: true, // 核销成功是否打印小票
        can_verification_status: [], // 状态为制作中仍支持核销
        cashier_mode: 'custom_mode', // 默认收银模式
        user_info_wait_time: 0, // 余额查询-账户信息显示时长
        // 支付设置
        can_micropay: false, // 刷卡支付
        can_facepay: false, // 人脸支付
        can_scanpay: false, // 扫码支付
        face_pay_confirm: false, // 人脸支付是否确认
        face_pay_confirm_wait_time: 0, // 支付成功信息停留时间（秒）
        large_amount_confirm: false, // 大额确认
        large_amount_minimum: '', // 大额确认最小值
        fixed_amount_consume_rule: false, // 大额确认
        meal_type_amount_consume_rule: false, // 大额确认
        online_face_recognize_range: 0, // 在线人脸识别距离
        // 离线设置
        autoOpenOfflineMode: false, // 是否自动开启离线模式
        openOfflineType: 'auto', // 根据网络状态切换
        openOfflineTime: [], // 固定时间开启
        openOfflineMeal: [], // 固定餐段开启
        // 自动上传离线订单
        autoUploadOfflineOrder: false, // 是否自动上传离线订单
        uploadOfflineType: 'network', // 联网自动上传
        uploadOfflineTime: '', // 固定时间上传
        isAutoOpenOfflineFacePay: true, // 是否自动开启离线人脸支付
        offlineFaceRecognizeScore: 0, // 离线人脸识别分数
        detectionRange: 0, // 离线人脸识别距离（像素）
        isAutoOpenLiveDetection: true, // 离线是否开启活体检测
        liveFaceRecognizeScore: 0 // 活体检测识别分数
      },
      menuList: [],
      mealList: [
        {
          key: 'breakfast',
          name: '早餐',
          disabled: false
        },
        {
          key: 'lunch',
          name: '午餐',
          disabled: false
        },
        {
          key: 'afternoon',
          name: '下午茶',
          disabled: false
        },
        {
          key: 'dinner',
          name: '晚餐',
          disabled: false
        },
        {
          key: 'supper',
          name: '宵夜',
          disabled: false
        },
        {
          key: 'morning',
          name: '凌晨餐',
          disabled: false
        }
      ],
      verificationWayList: [
        {
          key: 'can_micropay_verification',
          name: '刷卡核销'
        },
        {
          key: 'can_facepay_verification',
          name: '人脸核销'
        },
        {
          key: 'can_scanpay_verification',
          name: '扫码核销'
        }
      ],
      settingFormRules: {
        auto_verification_second: [
          { required: true, message: '请输入秒数', trigger: ['blur', 'change'] },
          { type: 'number', message: '请输入整数' }
        ],
        large_amount_minimum: [{ required: true, validator: validMoney, trigger: 'blur' }],
        openOfflineTime: [{ required: true, message: '请选择时间', trigger: 'change' }],
        openOfflineMeal: [
          { type: 'array', required: true, message: '请至少选择一个餐段', trigger: 'change' }
        ],
        uploadOfflineTime: [{ required: true, message: '请选择时间', trigger: 'change' }]
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.organizationId = Number(sessionStorage.getItem('organization'))
      this.getSettingDetail()
    },
    async getSettingDetail() {
      this.loading = true
      const res = await this.$apis.apiBackgroundDeviceD2ConsumeDetailsPost({
        org_no: this.organizationId
      })
      this.loading = false
      if (res.code === 0) {
        if (res.data && typeof res.data === 'object' && Object.keys(res.data).length > 0) {
          let data = res.data.setting
          this.settingForm.is_auto_verification = data.is_auto_verification
          this.settingForm.auto_verification_second = data.auto_verification_second
          this.settingForm.verification_way = data.verification_way
          this.settingForm.verification_wait_time = data.verification_wait_time
          this.settingForm.verification_auto_print = data.verification_auto_print
          this.settingForm.can_verification_status = data.can_verification_status
          this.settingForm.cashier_mode = data.cashier_mode
          this.settingForm.user_info_wait_time = data.user_info_wait_time
          this.settingForm.can_micropay = data.can_micropay
          this.settingForm.can_facepay = data.can_facepay
          this.settingForm.can_scanpay = data.can_scanpay
          this.settingForm.face_pay_confirm = data.face_pay_confirm
          this.settingForm.face_pay_confirm_wait_time = data.face_pay_confirm_wait_time
          this.settingForm.large_amount_confirm = data.large_amount_confirm
          this.settingForm.large_amount_minimum = data.large_amount_minimum
            ? NP.divide(data.large_amount_minimum, 100)
            : ''
          this.settingForm.fixed_amount_consume_rule = data.fixed_amount_consume_rule
          this.settingForm.meal_type_amount_consume_rule = data.meal_type_amount_consume_rule
          this.settingForm.online_face_recognize_range = data.online_face_recognize_range
          // 自动开启离线模式
          // eslint-disable-next-line no-unneeded-ternary
          this.settingForm.autoOpenOfflineMode = data.auto_switch_offline_model === 'off' ? false : true
          this.settingForm.openOfflineType = data.auto_switch_offline_model
          if (
            data.auto_switch_offline_model_conf.timestamp &&
            data.auto_switch_online_model_conf.timestamp
          ) {
            this.settingForm.openOfflineTime[0] = this.formateTimestamp(
              data.auto_switch_offline_model_conf.timestamp
            )
            this.settingForm.openOfflineTime[1] = this.formateTimestamp(
              data.auto_switch_online_model_conf.timestamp
            )
          } else {
            this.settingForm.openOfflineTime = null
          }
          if (
            Object.keys(data.auto_switch_offline_model_conf) &&
            Object.keys(data.auto_switch_offline_model_conf).length
          ) {
            if (data.auto_switch_offline_model_conf.meal) {
              this.settingForm.openOfflineMeal = data.auto_switch_offline_model_conf.meal
            }
          }
          // 自动上传离线订单
          // eslint-disable-next-line no-unneeded-ternary
          this.settingForm.autoUploadOfflineOrder = data.auto_upload_offline_order === 'off' ? false : true
          this.settingForm.uploadOfflineType = data.auto_upload_offline_order
          this.settingForm.uploadOfflineTime = data.auto_upload_offline_order_conf.timestamp
            ? this.formateTimestamp(data.auto_upload_offline_order_conf.timestamp)
            : ''
          this.settingForm.isAutoOpenOfflineFacePay = data.offline_face_pay_limit
          this.settingForm.offlineFaceRecognizeScore = data.offline_face_recognize_score
          this.settingForm.detectionRange = data.offline_face_recognize_range
          this.settingForm.isAutoOpenLiveDetection = data.live_detection
          this.settingForm.liveFaceRecognizeScore = data.live_face_recognize_score
        } else {
          this.resetForm()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    checkForm(type) {
      let params = {
        is_auto_verification: this.settingForm.is_auto_verification,
        auto_verification_second: this.settingForm.auto_verification_second ? this.settingForm.auto_verification_second : 0,
        verification_way: this.settingForm.verification_way,
        verification_wait_time: this.settingForm.verification_wait_time,
        verification_auto_print: this.settingForm.verification_auto_print,
        can_verification_status: this.settingForm.can_verification_status,
        cashier_mode: this.settingForm.cashier_mode,
        user_info_wait_time: this.settingForm.user_info_wait_time,
        can_micropay: this.settingForm.can_micropay,
        can_facepay: this.settingForm.can_facepay,
        can_scanpay: this.settingForm.can_scanpay,
        face_pay_confirm: this.settingForm.face_pay_confirm,
        face_pay_confirm_wait_time: this.settingForm.face_pay_confirm_wait_time,
        large_amount_confirm: this.settingForm.large_amount_confirm,
        large_amount_minimum: NP.times(this.settingForm.large_amount_minimum, 100),
        fixed_amount_consume_rule: this.settingForm.fixed_amount_consume_rule,
        meal_type_amount_consume_rule: this.settingForm.meal_type_amount_consume_rule,
        online_face_recognize_range: this.settingForm.online_face_recognize_range,
        auto_switch_offline_model: this.settingForm.autoOpenOfflineMode
          ? this.settingForm.openOfflineType
          : 'off',
        auto_switch_offline_model_conf: {},
        auto_switch_online_model_conf: {},
        auto_upload_offline_order: this.settingForm.autoUploadOfflineOrder
          ? this.settingForm.uploadOfflineType
          : 'off',
        auto_upload_offline_order_conf: {},
        offline_face_pay_limit: this.settingForm.isAutoOpenOfflineFacePay,
        offline_face_recognize_score: this.settingForm.offlineFaceRecognizeScore,
        offline_face_recognize_range: this.settingForm.detectionRange,
        live_detection: this.settingForm.isAutoOpenLiveDetection,
        live_face_recognize_score: this.settingForm.liveFaceRecognizeScore
      }
      // 关于离线模式
      if (
        this.settingForm.openOfflineTime &&
        this.settingForm.openOfflineTime[0] &&
        this.settingForm.openOfflineTime[1]
      ) {
        let timeOff = this.settingForm.openOfflineTime[0].split(':')
        params.auto_switch_offline_model_conf = {
          timestamp: timeOff[0] * 60 * 60 + timeOff[1] * 60
        }
        let timeOn = this.settingForm.openOfflineTime[1].split(':')
        params.auto_switch_online_model_conf = {
          timestamp: timeOn[0] * 60 * 60 + timeOn[1] * 60
        }
      }
      if (this.settingForm.openOfflineMeal && this.settingForm.openOfflineMeal.length) {
        params.auto_switch_offline_model_conf.meal = this.settingForm.openOfflineMeal
      }
      if (this.settingForm.uploadOfflineTime) {
        let time = this.settingForm.uploadOfflineTime.split(':')
        params.auto_upload_offline_order_conf = {
          timestamp: time[0] * 60 * 60 + time[1] * 60
        }
      }
      this.$refs.settingForm.validate(valid => {
        if (valid) {
          if (type === 'self') {
            if (!this.organizationId) {
              this.$message.error('请选择适用组织')
              return
            }
            params.mode = 'self'
            params.org_id = this.organizationId
          } else {
            params.mode = 'other'
            params.org_ids = this.organizationIds
          }
          this.getDeviceD2ConsumeAdd(params)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    // 添加D2消费机配置
    async getDeviceD2ConsumeAdd(params) {
      this.loading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundDeviceD2ConsumeAddPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      this.loading = false
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.getSettingDetail()
      } else {
        this.$message.error(res.msg)
      }
    },
    formateTimestamp(timestamp) {
      return parseInt(timestamp / 3600) + ':' + (timestamp % 3600) / 60
    },
    changeOpenOfflineType() {
      this.$nextTick(() => {
        this.$refs.settingForm.clearValidate(['openOfflineTime', 'openOfflineMeal'])
      })
    },
    /**
     * 20231218  钟婷要求如果没有配置，就是data返回null ,前端要设置默认值跟D2那边设置的默认值一致，具体如下
     * 基础设置默认：核销类型：手动核销 ； 核销方式：刷卡核销 人脸核销 扫码核销  ； 核销成功信息停留时间：3秒 ；核销成功是否打印小票： 不打印 ； 默认收银模式 ：自定义收银； 余额查询账号信息显示时长：3秒
     * 支付设置：支付方式 ： 刷卡支付开，人脸支付开，扫码支付开 ； 人脸支付是否确认：关 ;支付成功信息停留时间：3秒 ； 支付限制：在线人脸识别距离：150
     */
    resetForm() {
      this.tabType = '1'
      this.organizationIds = []
      this.menuList = []
      this.settingForm = {
        is_auto_verification: false, // 核销类型
        auto_verification_second: 0, // 核销多少秒
        verification_way: ['can_micropay_verification', 'can_facepay_verification', 'can_scanpay_verification'], // 核销方式
        verification_wait_time: 3, // 核销成功信息停留时间（秒）
        verification_auto_print: false, // 核销成功是否打印小票
        can_verification_status: [], // 状态为制作中仍支持核销
        cashier_mode: 'custom_mode', // 默认收银模式
        user_info_wait_time: 3, // 余额查询-账户信息显示时长
        // 支付设置
        can_micropay: true, // 刷卡支付
        can_facepay: true, // 人脸支付
        can_scanpay: true, // 扫码支付
        face_pay_confirm: false, // 人脸支付是否确认
        face_pay_confirm_wait_time: 3, // 支付成功信息停留时间（秒）
        large_amount_confirm: false, // 大额确认
        large_amount_minimum: '', // 大额确认最小值
        fixed_amount_consume_rule: false, // 大额确认
        meal_type_amount_consume_rule: false, // 大额确认
        online_face_recognize_range: 150, // 在线人脸识别距离
        // 离线设置
        autoOpenOfflineMode: false, // 是否自动开启离线模式
        openOfflineType: 'auto', // 根据网络状态切换
        openOfflineTime: [], // 固定时间开启
        openOfflineMeal: [], // 固定餐段开启
        // 自动上传离线订单
        autoUploadOfflineOrder: false, // 是否自动上传离线订单
        uploadOfflineType: 'network', // 联网自动上传
        uploadOfflineTime: '', // 固定时间上传
        isAutoOpenOfflineFacePay: true, // 是否自动开启离线人脸支付
        offlineFaceRecognizeScore: 0, // 离线人脸识别分数
        detectionRange: 0, // 离线人脸识别距离（像素）
        isAutoOpenLiveDetection: true, // 离线是否开启活体检测
        liveFaceRecognizeScore: 0 // 活体检测识别分数
      }
    },
    changeOrganization() {
      this.resetForm()
      if (this.organizationId) {
        this.getSettingDetail()
      }
    },
    openOtherOrg() {
      this.dialogVisible = true
      this.dialogTitle = '请选择适用的组织'
      this.dialogType = 'other'
    },
    dialogConfirm() {
      this.dialogVisible = false
    },
    otherOrgConfirm(val) {
      this.dialogVisible = false
      this.organizationIds = val
      this.checkForm('other')
    },
    changeType(key) {
      this.tabType = key
    },
    // 监听自动核销改变
    autoVerificationChange(value) {
      this.$set(this.settingForm, 'auto_verification_second', value ? 3 : 0)
    }
  }
}
</script>

<style lang="scss">
.ConsumerMachineSeting {
  .setting-wrap {
    margin: 0 20px;
    .tab-type {
      display: flex;
      margin: 20px 0;
      .tabItem {
        border: 1px #dae1eb solid;
        border-radius: 15px;
        height: 30px;
        line-height: 30px;
        width: 90px;
        text-align: center;
        font-size: 14px;
        color: #7b7c80;
        margin-right: 20px;
        cursor: pointer;
      }
      .activeTab {
        color: #fff;
        background-color: #ff9b45;
        border-color: #ff9b45;
      }
    }
    .offline-title {
      display: flex;
      align-items: center;
    }
    .title {
      font-size: 16px;
      font-weight: bold;
      border-left: 5px #ff9b45 solid;
      padding: 0 10px;
      margin: 15px 0;
    }
  }
  .tips {
    margin: 0 30px;
    color: #b1b2b9;
  }
  .inline-box {
    display: inline-block;
  }
  .form-content-inline {
    display: inline-block;
    .el-form-item__content {
      display: inline-block;
    }
  }
  .el-input-number__decrease:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled),
  .el-input-number__increase:hover:not(.is-disabled)
    ~ .el-input
    .el-input__inner:not(.is-disabled) {
    border-color: #ff9b45;
  }
  .el-input.is-active .el-input__inner,
  .el-input__inner:focus {
    border-color: #ff9b45;
  }
  .el-input-number__decrease:hover,
  .el-input-number__increase:hover {
    color: #ff9b45;
  }
  .detection-range-slider {
    .el-slider__bar {
      background-color: #ff9b45;
    }
    .el-slider__button {
      border: 2px solid #ff9b45;
    }
  }
}
</style>
