<template>
  <div class="BatchAddGoodsDialog">
    <!-- 成功的 -->

    <el-dialog
      title="批量新增"
      :visible.sync="visible"
      top="20vh"
      custom-class="ps-dialog"
      show-close
      :close-on-click-modal="false"
      width="1400px"
    >
      <div class="text-right p-b-20">
        <el-button class="m-r-10" type="primary" size="mini" @click="clickDownloadUrl">下载模版</el-button>
        <parse-excel titleName="选择本地文件" @excel="getXlsxData">导入Excel</parse-excel>
      </div>
      <div class="ps-goods-dialog">
        <el-form ref="formData" v-loading="isLoading" :model="formData" inline label-width="120px" :rules="rules">
          <el-form-item label="" class="form-item-block">
            <el-table
              :data="formData.goodsList.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
              border
              header-row-class-name="ps-table-header-row"
              class="ps-table"
            >
              <el-table-column prop="index" label="序号" align="center"></el-table-column>
              <el-table-column prop="spec" label="*商品名称" align="center" width="180">
                <template slot-scope="scope">
                  <el-form-item label="" :prop="'goodsList.' + scope.$index + '.name'" :rules="rules.name">
                    <el-input class="ps-input" size="mini" v-model="scope.row.name"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="spec" label="*分类" align="center" width="120">
                <template slot-scope="scope">
                  <el-form-item
                    label=""
                    :prop="'goodsList.' + scope.$index + '.goods_category_id'"
                    :rules="rules.goods_category_id"
                  >
                    <el-select
                      v-model="scope.row.goods_category_id"
                      size="mini"
                      clearable
                      filterable
                      class="ps-select"
                      popper-class="ps-popper-select"
                    >
                      <el-option
                        v-for="item in goodsCategoryList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="spec" label="*规格" align="center" width="120">
                <template slot-scope="scope">
                  <el-form-item label="" :prop="'goodsList.' + scope.$index + '.spec'" :rules="rules.spec">
                    <el-input class="ps-input" size="mini" maxlength="20" v-model="scope.row.spec"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="goods_unit" label="*单位" align="center" width="120">
                <template slot-scope="scope">
                  <el-form-item label="" :prop="'goodsList.' + scope.$index + '.goods_unit'" :rules="rules.goods_unit">
                    <el-select
                      v-model="scope.row.goods_unit"
                      size="mini"
                      clearable
                      filterable
                      class="ps-select"
                      popper-class="ps-popper-select"
                    >
                      <el-option
                        v-for="item in unitList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="cost_price" label="成本价" align="center" width="120px">
                <template slot-scope="scope">
                  <el-form-item label="" :prop="'goodsList.' + scope.$index + '.cost_price'" :rules="rules.cost_price">
                    <el-input class="ps-input" size="mini" v-model="scope.row.cost_price"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="sales_price" label="*零售价" align="center" width="120px">
                <template slot-scope="scope">
                  <el-form-item
                    label=""
                    :prop="'goodsList.' + scope.$index + '.sales_price'"
                    :rules="rules.sales_price"
                  >
                    <el-input class="ps-input" size="mini" v-model="scope.row.sales_price"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="stock_num" label="库存" align="center">
                <template slot-scope="scope">
                  <el-input
                    class="ps-input"
                    size="mini"
                    maxlength="4"
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                    v-model="scope.row.stock_num"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="supplier_id" label="供应商" align="center" width="120">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.supplier_id"
                    size="mini"
                    clearable
                    filterable
                    class="ps-select"
                    popper-class="ps-popper-select"
                  >
                    <el-option
                      v-for="item in supplierList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="barcode" label="*条码" align="center" width="210px">
                <template slot-scope="scope">
                  <div class="ps-flex flex-align-c">
                    <el-form-item label="" :prop="'goodsList.' + scope.$index + '.barcode'" :rules="rules.specBarcode">
                      <el-input v-model="scope.row.barcode" class="ps-input" size="mini" maxlength="13"></el-input>
                    </el-form-item>
                    <el-button
                      slot="append"
                      size="mini"
                      class="ps-green-btn m-l-10"
                      type="primary"
                      @click="clickGenerateUniqueID(scope.$index)"
                    >
                      生成
                    </el-button>
                  </div>
                </template>
                <!-- barcode -->
              </el-table-column>
              <el-table-column label="操作" align="center" fixed="right" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" class="ps-text" @click="clickAddGoods">新增</el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="ps-warn"
                    @click="clickDelectGoods(scope.row)"
                    v-if="formData.goodsList.length > 1"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>

        <div class="pageSizeItem ps-pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-size="pageSize"
            layout="prev, pager, next, total, jumper"
            :total="formData.goodsList.length"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="canceDialogHandle">取 消</el-button>
        <el-button class="ps-btn" type="primary" v-loading="isLoading" @click="clickDetermineDialog">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import NP from 'number-precision'
import { to, generateUniqueID } from '@/utils/index'
import { positiveMoney } from '@/utils/validata'
export default {
  props: {
    isshow: Boolean,
    type: {
      type: String,
      default: ''
    },
    goodsCategoryList: {
      type: Array,
      default() {
        return []
      }
    },
    unitList: {
      type: Array,
      default() {
        return []
      }
    },
    supplierList: {
      type: Array,
      default() {
        return []
      }
    },
    confirm: Function
  },
  data() {
    let validataCostPrice = (rule, value, callback) => {
      if ((!positiveMoney(value) || Number(value) >= 10000) && value) {
        callback(new Error('格式错误'))
      } else {
        callback()
      }
    }
    let validataSalesPrice = (rule, value, callback) => {
      if (!positiveMoney(value) || Number(value) >= 10000) {
        callback(new Error('格式错误'))
      } else {
        callback()
      }
    }
    let validateSpecBarcode = (rule, value, callback) => {
      let regPass = /^[a-zA-Z0-9_-]+$/
      if (!value) {
        return callback(new Error('请输入条码'))
      } else if (value.length < 13) {
        return callback(new Error('条码长度不能少于13位'))
      } else if (!regPass.test(value)) {
        callback(new Error("条码由：数字、字母、'_'、'-'组成"))
      } else {
        callback()
      }
    }
    return {
      // eslint-disable-next-line
      isLoading: false,
      currentPage: 1,
      pageSize: 10,
      formData: {
        goodsList: [
          {
            index: 1,
            name: '',
            goods_category_id: '',
            spec: '',
            goods_unit: '',
            cost_price: '',
            sales_price: '',
            stock_num: '',
            supplier_id: null,
            barcode: ''
          }
        ]
      },
      rules: {
        name: [{ required: true, message: '请输入商品名称', trigger: ['blur', 'change'] }],
        goods_category_id: [{ required: true, message: '请选择分类', trigger: ['blur', 'change'] }],
        spec: [{ required: true, message: '请输入规格', trigger: ['blur', 'change'] }],
        goods_unit: [{ required: true, message: '请选择单位', trigger: ['blur', 'change'] }],
        cost_price: [
          { required: false, validator: validataCostPrice, message: '请输入成本价', trigger: ['blur', 'change'] }
        ],
        sales_price: [
          { required: true, validator: validataSalesPrice, message: '请输入零售价', trigger: ['blur', 'change'] }
        ],
        specBarcode: [{ required: true, validator: validateSpecBarcode, trigger: ['blur', 'change'] }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {},
  methods: {
    clickDownloadUrl() {
      let url = location.origin + '/api/temporary/template_excel/food_stock/导入商品模板.xlsx' // 下载模板链接
      window.open(url, '_blank')
    },
    canceDialogHandle() {
      this.visible = false
    },
    clickGenerateUniqueID(index) {
      this.formData.goodsList[index].barcode = generateUniqueID()
    },
    clickAddGoods() {
      this.formData.goodsList.push({
        index: this.formData.goodsList.length + 1,
        name: '',
        spec: '',
        goods_unit: '',
        cost_price: '',
        sales_price: '',
        stock_num: '',
        supplier_id: null,
        barcode: ''
      })
    },
    clickDelectGoods(row) {
      // 拿当前索引
      let index = row.index - 1
      this.formData.goodsList.splice(index, 1)
      this.formData.goodsList.forEach((item, goodsIndex) => {
        item.index = goodsIndex + 1
      })
    },
    clickDetermineDialog() {
      this.$refs.formData.validate((valid, object) => {
        if (valid) {
          let list = []
          // let reg = /^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/
          let barcodeReg = /^[a-zA-Z0-9_-]+$/
          const goodsBarcodeSet = new Set()
          console.log(this.formData.goodsList)
          for (let index = 0; index < this.formData.goodsList.length; index++) {
            const element = this.formData.goodsList[index]
            // if (!element.supplier_id) {
            //   return this.$message.error(`新增下的第${element.index}条，请选择供应商`)
            // }
            if (!element.barcode) {
              return this.$message.error(`新增下的第${element.index}条，请输入条码`)
            } else if (!barcodeReg.test(element.barcode)) {
              return this.$message.error(`新增下的第${element.index}条，条码由：数字、字母、'_'、'-'组成`)
            }
            // 判断是否相同
            if (goodsBarcodeSet.has(element.barcode)) {
              return this.$message.error(`新增下的第${element.index}条,条码有相同,请修改`)
            }
            goodsBarcodeSet.add(element.barcode)
            let multiSpecParams = {
              name: element.name,
              goods_category_id: element.goods_category_id,
              spec: element.spec,
              goods_unit: element.goods_unit,
              cost_price: element.cost_price ? NP.times(element.cost_price, 100) : 0,
              sales_price: element.sales_price ? NP.times(element.sales_price, 100) : 0,
              stock_num: element.stock_num ? Number(element.stock_num) : 0,
              supplier_id: element.supplier_id ? element.supplier_id : null,
              barcode: element.barcode
            }
            list.push(multiSpecParams)
          }
          this.setGoodsAddOrModify(list)
        } else {
          // 需要使用 message的error 好像用他本身的错误信息 高度会顶到 f12 是有错误信息显示出来的，但是页面上 高度顶住了 显示不出来
          console.log(object)
          // 处理验证错误信息
          if (object && typeof object === 'object') {
            for (const key in object) {
              if (Object.prototype.hasOwnProperty.call(object, key) && Array.isArray(object[key])) {
                // 从字段名中提取索引，例如从 'goodsList.0.sales_price' 提取 '0'
                const match = key.match(/goodsList\.(\d+)/)
                if (match && match[1]) {
                  const index = parseInt(match[1])
                  const errorMsg = object[key][0].message
                  return this.$message.error(`序号${index + 1}${errorMsg}`)
                }
              }
            }
          }
        }
      })
    },
    async setGoodsAddOrModify(listData) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsBatchAddPost({
          goods_list: listData
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$emit('confirm', 'search')
        // this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    getXlsxData(json, name) {
      const tableSetting = [
        { key: 'name', label: '商品名称' },
        { key: 'goods_category_id', label: '分类' },
        { key: 'spec', label: '规格' },
        { key: 'goods_unit', label: '单位' },
        { key: 'cost_price', label: '成本价' },
        { key: 'sales_price', label: '零售价' },
        { key: 'stock_num', label: '库存' },
        { key: 'supplier_id', label: '供应商' },
        { key: 'barcode', label: '条码' }
      ]

      const excelObj = {}
      tableSetting.forEach(item => {
        excelObj[item.label] = item.key
      })

      const result = json.slice(1).map((item, index) => {
        const data = {}
        for (let key in item) {
          if (excelObj[key]) {
            if (key === '分类') {
              const category = this.goodsCategoryList.find(cat => cat.name === String(item[key]))
              data[excelObj[key]] = category ? category.id : ''
            } else if (key === '单位') {
              const unitItem = this.unitList.find(uni => uni.name === String(item[key]))
              data[excelObj[key]] = unitItem ? unitItem.id : ''
            } else if (key === '供应商') {
              const supplierItem = this.supplierList.find(sup => sup.name === String(item[key]))
              data[excelObj[key]] = supplierItem ? supplierItem.id : ''
            } else {
              data[excelObj[key]] = item[key]
            }
          }
        }
        data.index = this.formData.goodsList.length + (index + 1) // 添加索引
        return data
      })
      // result.forEach((item, index) => {
      //   this.goodsList.push({ ...item, index });
      // });
      this.formData.goodsList.push(...result)
    },
    handleSizeChange(val) {
      this.pageSize = val
      // this.currentPageLoad = 1; // 改变页面大小后，跳转回第一页
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style lang="scss" scoped>
.BatchAddGoodsDialog {
  .ps-goods-dialog {
    // max-height: calc(100vh - 40vh);
    // overflow-y: auto;
  }
}
</style>
