<template>
  <div class="AddSupplierManagement form-container">
    <h3>基本信息</h3>
    <el-form v-loading="isLoading" ref="formRef" :model="formData" :rules="formRuls" label-width="142px" label-position="right" size="small" class="m-t-10 form-warpper">
      <div class="m-l-20">
        <el-form-item label="供应商名称" prop="name">
          <el-input v-model="formData.name" :maxlength="20" class="w-200" :disabled="type === 'modify'"></el-input>
          <!-- <el-select
            v-model="formData.name"
            clearable
            filterable
            remote
            :loading="remoteLoading"
            :remote-method="getSystemSupplierList"
            placeholder=""
            ref="supplierNameRef"
            @visible-change="visibleChangeSupplierHandle"
            @focus="focusSupplierHandle"
            @clear="clearSupplierHandle"
            @change="changeSupplierHandle"
            class="w-200"
            :disabled="type === 'modify'"
          >
            <el-option
              v-for="item in systemSupplierList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              :text-light="true"
            >
            <div v-html="item.highlight_text"></div>
            </el-option>
          </el-select> -->
        </el-form-item>
        <!-- <el-form-item label="上传附件" prop="imageFile">
          <el-upload
            ref="uploadFoodImage"
            :class="{'upload-food': true, 'hide-upload':formData.imageFileList.length>8}"
            drag
            :data="uploadParams"
            :action="actionUrl"
            :multiple="false"
            :file-list="formData.imageFileList"
            list-type="picture-card"
            :on-change="handelChange"
            :on-success="handleImgSuccess"
            :before-upload="beforeImgUpload"
            :limit="9"
            :headers="headersOpts"
          >
            <i v-if="formData.imageFileList.length<9" class="el-icon-plus"></i>
            <div slot="file" slot-scope="{file}" v-loading="file.status==='uploading'" element-loading-text="上传中">
              <div class="upload-food-img"><img :src="file.url" alt=""></div>
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleImgRemove(file, 'imageFile')">
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
        </el-form-item> -->
        <el-form-item label="负责人姓名">
          <el-input v-model="formData.contactName" class="w-200"></el-input>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="formData.contactPhone" class="w-200"></el-input>
        </el-form-item>
        <el-form-item label="所在地址">
          <el-input v-model="formData.address" :maxlength="50" class="w-200" ></el-input>
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="creditCode">
          <el-input v-model="formData.creditCode" :disabled="type === 'modify'" class="w-200"></el-input>
        </el-form-item>
        <el-form-item label="类别" prop="supplyCategory">
          <el-input v-model="formData.supplyCategory" class="w-200"></el-input>
        </el-form-item>
        <div class="m-b-30">
          <div class="aptitude-management m-b-10 font-size-14">
            <span class="font-weight-600">资质管理</span>
            <span v-if="type === 'add'"><el-button size="small" class="ps-origin-btn" @click="clickRowHandle('add')" >新增</el-button></span>
          </div>
          <el-table
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
            size="small"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item">
              <template #imgUrl="{ row }">
                <el-image
                  v-for="(img, index) in row.imgUrl"
                  :key="index"
                  :preview-src-list="row.imgUrl"
                  :initial-index="index"
                  class="file-img m-r-6"
                  :src="img"
                  fit="contain"
                  style="width: 120px;height: 150px"
                ></el-image>
              </template>
              <template #aptitude="{ row }">
                {{ computedFileType(row.aptitude) }}
              </template>
              <template #expiry_date="{ row }">
                <span>{{ row.expiry_date[0]}} - {{ row.expiry_date[1] }}</span>
              </template>
              <template #operation="{ row }">
                <el-button  type="text" size="small" class="ps-text" @click="clickRowHandle('detail', row)" >详情</el-button>
                <el-button v-if="type === 'add'" type="text" size="small" class="ps-text" @click="clickRowHandle('modify', row)" >编辑</el-button>
                <el-button v-if="type === 'add'" type="text" size="small" class="ps-text"  @click="deleteHandle(row)">删除</el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <el-form-item label="中标通知书" prop="acceptanceFiles">
          <el-upload
            class="upload-demo"
            :data="uploadParams"
            :action="actionUrl"
            :on-success="handleAcceptanceSuccess"
            :before-upload="beforeAcceptanceUpload"
            :on-remove="handleAcceptanceRemove"
            :on-change="handelChange"
            multiple
            :headers="headersOpts"
            :file-list="formData.acceptanceFiles">
            <el-button size="small" type="primary">上传附件</el-button>
            <span slot="tip" class="el-upload__tip m-l-20">支持上传多个，支持docx/doc/xlxs/xls/pptx/ppt/pdf格式，总数不得超过20M</span>
            <template slot="file" slot-scope="{file}">
              <div class="custom-file-item">
                <span class="file-name" @click="handleFileClick(file, 'acceptanceFileList')">{{ file.name }}</span>
                <span class="file-actions">
                  <i class="el-icon-delete" @click="handleAcceptanceRemove(file, formData.acceptanceFiles)"></i>
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="合同" prop="contractFiles">
          <el-upload
            class="upload-demo"
            :data="uploadParams"
            :action="actionUrl"
            :on-success="handleContractSuccess"
            :before-upload="beforeContractUpload"
            :on-remove="handleContractRemove"
            :on-change="handelChange"
            multiple
            :headers="headersOpts"
            :file-list="formData.contractFiles">
            <el-button size="small" type="primary">上传附件</el-button>
            <span slot="tip" class="el-upload__tip m-l-20">支持上传多个，支持jpg/png/docx/doc/xlxs/xls/pptx/ppt/pdf格式，总数不得超过20M</span>
            <template slot="file" slot-scope="{file}">
              <div class="custom-file-item">
                <span class="file-name" @click="handleFileClick(file, 'contractFileList')">{{ file.name }}</span>
                <span class="file-actions">
                  <i class="el-icon-delete" @click="handleContractRemove(file, formData.contractFiles)"></i>
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="其他附件" prop="otherFiles">
          <el-upload
            class="upload-demo"
            :data="uploadParams"
            :action="actionUrl"
            :on-success="handleOtherSuccess"
            :before-upload="beforeOtherUpload"
            :on-remove="handleOtherRemove"
            :on-change="handelChange"
            multiple
            :headers="headersOpts"
            :file-list="formData.otherFiles">
            <el-button size="small" type="primary">上传附件</el-button>
            <span slot="tip" class="el-upload__tip m-l-20">支持上传多个，支持jpg/png/docx/doc/xlxs/xls/pptx/ppt/pdf格式，总数不得超过20M</span>
            <template slot="file" slot-scope="{file}">
              <div class="custom-file-item">
                <span class="file-name" @click="handleFileClick(file, 'otherFileList')">{{ file.name }}</span>
                <span class="file-actions">
                  <i class="el-icon-delete" @click="handleOtherRemove(file, formData.otherFiles)"></i>
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </div>
      <!-- <el-divider></el-divider> -->
      <div class="ps-flex-align-c flex-align-c">
        <h3>账号信息</h3>
        <div class="m-l-20 red font-size-14">新增时可为供应商添加账号，后期无法更改</div>
      </div>
      <div class="m-l-20 m-t-20">
        <el-form-item label="账号名" label-width="142px" :prop="type === 'add' ? 'accountName' : ''">
          <el-input v-model="formData.accountName" class="w-250" :disabled="type === 'modify'"></el-input>
        </el-form-item>
        <el-form-item label="登录账号" label-width="142px" prop="account">
          <el-input v-model="formData.account" class="w-250" :disabled="type === 'modify'"></el-input>
          <!-- <span class="m-l-30 m-r-10">是否启用</span> -->
          <!-- <el-switch v-model="formData.open" inactive-text="" active-color="#ff9b45" inactive-color="#ffcda2" ></el-switch> -->
        </el-form-item>
        <el-form-item label="登录密码" label-width="142px" prop="password">
          <el-input v-model="formData.password" placeholder="不填则默认为供应商编号" :disabled="type === 'modify'" class="w-250"></el-input>
        </el-form-item>
      </div>
      <div class="m-l-40 p-t-30 p-b-30">
        <el-button class="ps-origin-btn w-130" size="medium" @click="cancelHandle">取消</el-button>
        <el-button class="ps-origin-btn w-130" size="medium" @click="submitFormHandle()">保存</el-button>
      </div>
    </el-form>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <addAptitudeDialog :showdialog.sync="showAptitudeDialog" @clickConfirm="setTableData" :infoData="addAptitudeDialogInfoData" :type="addAptitudeDialogType" @showImgViewer="handleClick"></addAptitudeDialog>

     <!-- 图片预览 -->
     <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSuffix, getToken, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import { validateTelphone } from '@/assets/js/validata'
import { defaultNo } from '@/utils/validata'
import addAptitudeDialog from '@/components/AddAptitudeDialog/index.vue'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import md5 from 'js-md5';

export default {
  name: 'AddSupplierManagement',
  mixins: [exportExcel],
  components: { addAptitudeDialog, ElImageViewer },
  data() {
    let validataAccount = (rule, value, callback) => {
      if (value) {
        if (!defaultNo(value)) {
          callback(new Error('仅支持英文、数字'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      type: 'add',
      isLoading: false, // 刷新数据
      // form表单数据
      formData: {
        id: '',
        name: '', // 供应商名称
        creditCode: '', // 工商营业执照（统一社会信用代码）
        contactName: '', // 联系人
        contactPhone: '', // 手机
        address: '', // 地址
        supplyCategory: '',
        account: '', // 账号
        accountName: '', // 账号名
        password: '', // 密码
        // open: true
        // imageFile: [],
        // imageFileList: []
        acceptanceFiles: [], // 中标通知书
        acceptanceFileList: [],
        contractFiles: [], // 合同
        contractFileList: [],
        otherFiles: [], // 其他附件
        otherFileList: []
      },
      formRuls: {
        name: [{ required: true, message: '请输入供应商名称', trigger: 'change' }],
        creditCode: [{ required: true, message: '工商营业执照', trigger: 'change' }],
        contactName: [{ required: true, message: '请输入负责人姓名', trigger: 'change' }],
        contactPhone: [
          { required: true, message: '请输入联系电话', trigger: 'change' },
          { validator: validateTelphone, trigger: 'change' }
        ],
        supplyCategory: [{ required: true, message: '请选择供应商类型', trigger: 'change' }],
        address: [{ required: true, message: '请输入所在地址', trigger: 'change' }],
        // imageFile: [{ required: true, message: '请上传附件', trigger: 'blur' }],
        account: [
          { required: true, message: '请输入姓名', trigger: 'change' }
        ],
        accountName: [
          { required: true, message: '请输入账号名', trigger: ['change', 'change'] }
        ],
        password: [
          { min: 8, max: 16, message: "长度在 8 到 16 个字符", trigger: "change" },
          { validator: validataAccount, trigger: 'change' }
        ]
      },
      uploadParams: {
        prefix: 'inventoryImage'
      },
      actionUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      infoData: {},
      // 图片放大查看
      dialogImageUrl: [],
      dialogVisible: false,
      systemSupplierList: [], // 菜品列表
      filterSupplierName: '', // 菜品筛选的输入文字
      isSelectSupplier: false, // 是否通过select选择了菜
      remoteLoading: false, // 远程筛选菜品的状态
      selectSupplier: null, // 下拉选中的菜品数据，记录下用于还原显示
      systemIngredientsList: [],
      systemIngredientsIdsObj: {},
      allIngredientsList: {},
      showAptitudeDialog: false,
      tableData: [],
      tableSettings: [
        { label: '图片', key: 'imgUrl', type: "slot", slotName: "imgUrl" },
        { label: '资质类型', key: 'aptitude', type: 'slot', slotName: 'aptitude' },
        { label: '上传时间', key: 'upLoadTime' },
        { label: '修改时间', key: 'editTime' },
        { label: '有效期', key: 'expiry_date', type: 'slot', slotName: 'expiry_date' },
        { label: '操作人', key: 'operator' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      addAptitudeDialogInfoData: {},
      addAptitudeDialogType: 'add',
      showImagePreview: false,
      previewList: []
    }
  },
  computed: {
    computedFileType() {
      return d => {
        switch (d) {
          case '1':
            return '营业执照'
          case '2':
            return '食品经营许可证'
          case '3':
            return '食品生产许可证'
        }
      }
    }
  },
  created() {
    this.type = this.$route.params.type
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.type === 'modify') {
        this.getProcureDetail(this.$route.query.id)
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 获取列表数据
    async getProcureDetail(id) {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpSupplierManageDetailsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        // this.formData = res.data
        let infoData = res.data
        this.formData = {
          id: infoData.id,
          name: infoData.name, // 供应商名称
          creditCode: infoData.credit_code, // 工商营业执照（统一社会信用代码）
          contactName: infoData.contact_name, // 联系人
          contactPhone: infoData.contact_phone, // 手机
          supplyCategory: infoData.supply_category,
          address: infoData.address, // 地址
          account: infoData.login_account, // 账号
          accountName: infoData.login_account_name, // 密码
          // open: !!infoData.login_use
          // imageFile: infoData.extra.imageList || [],
          // imageFileList: []
          acceptanceFiles: infoData.bid_winning_notice || [],
          acceptanceFileList: infoData.bid_winning_notice || [],
          contractFiles: infoData.contract || [],
          contractFileList: infoData.contract || [],
          otherFiles: infoData.extra_json || [],
          otherFileList: infoData.extra_json || []
        }
        if (infoData.certification_info.length) {
          this.tableData = deepClone(infoData.certification_info)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置文件名
    handelChange(file, fileList) {
      // 获取原始文件的扩展名
      const fileExtension = getSuffix(file.name)
      this.uploadParams.key =
        this.uploadParams.prefix + '_' + new Date().getTime() + Math.floor(Math.random() * 150) + fileExtension
    },
    // 上传成功数据处理
    handleImgSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.formData.imageFileList = fileList
        this.formData.imageFile.push(res.data.public_url)
        this.$nextTick(() => {
          this.$refs.formRef.clearValidate('imageFile')
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除图片
    handleImgRemove(file, type) {
      let index = this.formData[type + 'List'].findIndex(item => item.url === file.url)
      this.formData[type].splice(index, 1)
      this.formData[type + 'List'].splice(index, 1)
    },
    // 上传图片前校验文件类型
    beforeImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.svg']
      const isLt5M = file.size / 1024 / 1024 > 5
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是PNG/JPG/SVG格式!')
        return false
      }

      if (isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
    },
    cancelHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'SupplierManagement')
    },
    // 保存
    async submitFormHandle(type) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (this.isLoading) return
          let api
          let params = {
            name: this.formData.name,
            credit_code: this.formData.creditCode,
            contact_name: this.formData.contactName,
            contact_phone: this.formData.contactPhone,
            address: this.formData.address,
            supply_category: this.formData.supplyCategory,
            // login_use: this.formData.open ? 1 : 0, // 新版不要这个了
            certification_info: this.tableData,
            bid_winning_notice: this.formData.acceptanceFileList,
            contract: this.formData.contractFileList,
            extra_json: this.formData.otherFileList
          }
          this.isLoading = true
          if (this.type === 'add') {
            params.login_account = this.formData.account
            params.login_account_name = this.formData.accountName
            if (this.formData.password) {
              params.login_password = md5(this.formData.password)
            }
            api = this.$apis.apiBackgroundDrpSupplierManageAddPost(params)
          } else {
            params.id = this.formData.id
            api = this.$apis.apiBackgroundDrpSupplierManageModifyPost(params)
          }
          this.sendFormData(api)
        }
      })
    },
    // 发送请求
    async sendFormData(xhr) {
      const [err, res] = await this.$to(xhr)
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('成功')
        this.cancelHandle()
      } else {
        this.$message.error(res.msg)
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 选中菜品
    changeSupplierHandle(e) {
      this.isSelectSupplier = true
      this.selectSupplier = this.systemSupplierList.find(item => item.id === e)
      this.filterSupplierName = this.selectSupplier.name
      this.setSystemSupplier(this.selectSupplier)
    },
    setSystemSupplier(data) {
      this.$set(this.formData, 'name', data.name || '')
      this.$set(this.formData, 'creditCode', data.credit_code || '')
      this.$set(this.formData, 'contactName', data.contact_name || '')
      this.$set(this.formData, 'contactPhone', data.contact_phone || '')
      this.$set(this.formData, 'address', data.address || '')
      this.$set(this.formData, 'imageFile', data.extra.imageList || [])
      if (data.extra?.imageList) {
        this.formData.imageFileList = data.extra.imageList.map(v => {
          return {
            url: v,
            name: v,
            status: "success",
            uid: v
          }
        })
      } else {
        this.formData.imageFileList = []
      }
    },
    // 请除菜品输入/选中
    clearSupplierHandle(e) {
      this.systemSupplierList = []
      this.isSelectSupplier = false
    },
    // select下拉框显示or隐藏事件
    visibleChangeSupplierHandle(e) {
      if (!e) {
        if (!this.isSelectSupplier) {
          this.formData.name = this.filterSupplierName
        }
        return
      }
      // 初始化filter的数据，设置高亮需要
      this.$nextTick(_ => {
        if (this.filterSupplierName && !this.isSelectSupplier) {
          this.$refs.supplierNameRef.query = this.filterSupplierName
          this.$refs.supplierNameRef.selectedLabel = this.filterSupplierName
          // 显示时需要手动触发下filter
          this.$refs.supplierNameRef.handleQueryChange(this.filterSupplierName)
          // this.$refs.supplierNameRef.remoteMethod(this.filterSupplierName)
        }
      })
    },
    // 获取焦点时
    focusSupplierHandle(e) {
      this.$nextTick(_ => {
        setTimeout(() => {
          this.$refs.supplierNameRef.query = this.filterSupplierName
          this.$refs.supplierNameRef.selectedLabel = this.filterSupplierName
        }, 70)
      })
    },
    // 菜品远程搜索
    async getSystemSupplierList(e) {
      // 将搜索的值赋给foodName
      // 当上一次搜索的关键字和本次的相同时跳过重新请求数据
      if (e && (e === this.filterSupplierName)) return
      this.filterSupplierName = e
      if (!e) {
        this.$refs.supplierNameRef.query = ''
        this.$refs.supplierNameRef.selectedLabel = ''
        if (!this.isSelectSupplier) {
          this.formData.name = e
        }
        this.systemSupplierList = []
        return
      } else {
        this.formData.name = e
      }
      this.remoteLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundDrpSupplierManageGetCreateSupplierManagePost({
          name: e
        })
      )
      this.remoteLoading = false
      if (err) {
        this.$message.error(err.message)
        this.systemSupplierList = []
        return
      }
      if (res.code === 0) {
        // this.systemSupplierList = res.data.results.map(item => {
        //   // 新开个字段给需要高亮的处理，防止污染旧数据
        //   item.highlight_text = item.name
        //   return this.setHightLight(item, e)
        // })
        if (res.data.results.length !== 0) {
          this.systemSupplierList = []
          res.data.results.map(v => {
            v.highlight_text = v.name
            this.systemSupplierList.push(this.setHightLight(v, e))
          })
        } else {
          this.systemSupplierList = []
        }
      } else {
        this.$message.error(res.msg)
        this.systemSupplierList = []
      }
    },
    // 设置文字高亮
    setHightLight(item, keyword) {
      let reg = new RegExp(keyword, "g");
      let replaceString = `<span style="color: #FF9B45;">${keyword}</span>`;
      if (item.highlight_text.match(reg)) {
        item.highlight_text = item.highlight_text.replace(reg, replaceString);
        return item;
      }
      return item;
    },
    // ————资质————
    clickRowHandle(type, row) {
      this.addAptitudeDialogType = type
      if (type === 'modify' || type === 'add') {
        this.addAptitudeDialogInfoData = type === 'add' ? {} : deepClone(row)
        this.showAptitudeDialog = true
      } else {
        this.addAptitudeDialogInfoData = deepClone(row)
        this.showAptitudeDialog = true
      }
    },
    deleteHandle(row) {
      let index = this.tableData.findIndex(item => item.id === row.id)
      this.tableData.splice(index, 1)
      this.$message.success('删除成功')
    },
    // 将弹窗返回的东西塞进tableData
    setTableData(data, type) {
      console.log('data', data, type)
      // if (type === 'add') {
      //   this.tableData.push(data)
      // } else {

      // }
      if (this.tableData.length > 0) {
        let index = this.tableData.findIndex(v => v.aptitude === data.aptitude)
        if (index > -1) {
          this.tableData.splice(index, 1, data)
        } else {
          this.tableData.push(data)
        }
      } else {
        this.tableData.push(data)
      }
    },
    handleClick(img) {
      this.previewList = [img]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    // 处理文件点击事件
    handleFileClick(file, key) {
      let index = this.formData[key].findIndex(item => item.name === file.name)

      const imageTypes = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg']
      const fileExtension = getSuffix(file.name).toLowerCase()

      if (imageTypes.includes(fileExtension)) {
        // 如果是图片，则预览
        this.previewList = [this.formData[key][index].url]
        document.body.style.overflow = 'hidden'
        this.showImagePreview = true
      } else {
        // 如果是其他文件，则下载
        window.open(this.formData[key][index].url, '_blank')
      }
    },
    // 下载文件
    // downloadFile(url, filename) {
    //   const link = document.createElement('a')
    //   link.href = url
    //   link.download = filename
    //   link.target = '_blank'
    //   document.body.appendChild(link)
    //   link.click()
    //   document.body.removeChild(link)
    // },
    // ————中标通知书————
    handleAcceptanceSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.formData.acceptanceFiles = fileList
        this.formData.acceptanceFileList.push({
          url: res.data.public_url,
          name: file.name
        })
        this.$nextTick(() => {
          this.$refs.formRef.clearValidate('acceptanceFiles')
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    handleAcceptanceRemove(file, fileList) {
      // 从文件列表中删除
      let fileIndex = this.formData.acceptanceFiles.findIndex(item => item.uid === file.uid)
      if (fileIndex > -1) {
        this.formData.acceptanceFiles.splice(fileIndex, 1)
      }

      // 从URL列表中删除（如果存在）
      let urlIndex = this.formData.acceptanceFileList.findIndex(item => item === file.url)
      if (urlIndex > -1) {
        this.formData.acceptanceFileList.splice(urlIndex, 1)
      }
    },
    beforeAcceptanceUpload(file) {
      // 支持上传docx、excel、ppt格式
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
        'application/msword', // doc
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
        'application/vnd.ms-excel', // xls
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
        'application/vnd.ms-powerpoint', // ppt
        'application/pdf' // pdf
      ]

      if (!allowedTypes.includes(file.type)) {
        this.$message.error('上传文件只能是 DOCX/DOC/XLSX/XLS/PPTX/PPT/PDF 格式!')
        return false
      }
      // 所有文件大小总数不能超过20M
      let totalSize = 0
      if (this.formData.acceptanceFiles?.length) {
        this.formData.acceptanceFiles.forEach(item => {
          totalSize += item.size
        })
      }
      totalSize += file.size
      if (totalSize / 1024 / 1024 > 20) {
        this.$message.error('所有文件大小总数不能超过20M')
        return false
      }
    },
    // ————合同————
    handleContractSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.formData.contractFiles = fileList
        this.formData.contractFileList.push({
          url: res.data.public_url,
          name: file.name
        })
        this.$nextTick(() => {
          this.$refs.formRef.clearValidate('contractFiles')
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    handleContractRemove(file, fileList) {
      // 从文件列表中删除
      let fileIndex = this.formData.contractFiles.findIndex(item => item.uid === file.uid)
      if (fileIndex > -1) {
        this.formData.contractFiles.splice(fileIndex, 1)
      }

      // 从URL列表中删除（如果存在）
      let urlIndex = this.formData.contractFileList.findIndex(item => item === file.url)
      if (urlIndex > -1) {
        this.formData.contractFileList.splice(urlIndex, 1)
      }
    },
    beforeContractUpload(file) {
      // 支持上传图片、docx、excel、ppt格式
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
        'application/msword', // doc
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
        'application/vnd.ms-excel', // xls
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
        'application/vnd.ms-powerpoint', // ppt
        'application/pdf' // pdf
      ]

      if (!allowedTypes.includes(file.type)) {
        this.$message.error('上传文件只能是 JPG/PNG/DOCX/DOC/XLSX/XLS/PPTX/PPT/PDF 格式!')
        return false
      }
      // 所有文件大小总数不能超过20M
      let totalSize = 0
      if (this.formData.contractFiles?.length) {
        this.formData.contractFiles.forEach(item => {
          totalSize += item.size
        })
      }
      totalSize += file.size
      if (totalSize / 1024 / 1024 > 20) {
        this.$message.error('所有文件大小总数不能超过20M')
        return false
      }
    },
    // ————其他附件————
    handleOtherSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.formData.otherFiles = fileList
        this.formData.otherFileList.push({
          url: res.data.public_url,
          name: file.name
        })
        this.$nextTick(() => {
          this.$refs.formRef.clearValidate('otherFiles')
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    handleOtherRemove(file, fileList) {
      // 从文件列表中删除
      let fileIndex = this.formData.otherFiles.findIndex(item => item.uid === file.uid)
      if (fileIndex > -1) {
        this.formData.otherFiles.splice(fileIndex, 1)
      }

      // 从URL列表中删除（如果存在）
      let urlIndex = this.formData.otherFileList.findIndex(item => item === file.url)
      if (urlIndex > -1) {
        this.formData.otherFileList.splice(urlIndex, 1)
      }
    },
    beforeOtherUpload(file) {
      // 支持上传图片、docx、excel、ppt格式
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
        'application/msword', // doc
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
        'application/vnd.ms-excel', // xls
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
        'application/vnd.ms-powerpoint', // ppt
        'application/pdf' // pdf
      ]

      if (!allowedTypes.includes(file.type)) {
        this.$message.error('上传文件只能是 JPG/PNG/DOCX/DOC/XLSX/XLS/PPTX/PPT/PDF 格式!')
        return false
      }
      // 所有文件大小总数不能超过20M
      let totalSize = 0
      if (this.formData.otherFiles?.length) {
        this.formData.otherFiles.forEach(item => {
          totalSize += item.size
        })
      }
      totalSize += file.size
      if (totalSize / 1024 / 1024 > 20) {
        this.$message.error('所有文件大小总数不能超过20M')
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.AddSupplierManagement {
  background-color: #fff;
  .form-warpper{
    box-sizing: border-box;
  }
  h3{
    margin: 0;
  }
  .w-200 {
    width: 220px;
  }
  .w-auto{
    width: 300px;
  }
  .error {
    ::v-deep .el-input__inner {
      border-color: red;
    }
  }
  .upload-food{
    overflow: hidden;
    max-height: 830px;
    &.hide-upload{
      .el-upload--picture-card{
        display: none;
      }
    }
    .el-upload--picture-card{
      border: none;
    }
    .el-upload-dragger{
      width: 148px;
      height: 148px;
    }
    .upload-food-img{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 145px;
      height: 145px;
      img{
        max-width: 145px;
        max-height: 145px;
      }
    }
  }
  .aptitude-management {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .font-weight-600 {
      font-weight: 600;
    }
  }

  .upload-demo {
    ::v-deep .el-upload-list__item {
      background-color: none;
    }
  }

  .custom-file-item {
    width: 300px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin: 4px 0;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f5f7fa;

    .file-name {
      flex: 1;
      cursor: pointer;
      color: #409eff;
      text-decoration: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        color: #66b1ff;
        text-decoration: underline;
      }
    }

    .file-actions {
      margin-left: 10px;

      i {
        cursor: pointer;
        color: #f56c6c;
        font-size: 16px;

        &:hover {
          color: #f78989;
        }
      }
    }
  }
}
</style>
