<template>
  <div class="ps-el-drawer meal-management-detail">
    <el-drawer :title="title" :visible="visible" :show-close="false" :size="width">
      <div class="p-20" v-loading="loading">
        <div>
          <div class="ps-flex">
            <div class="tag-title">陪餐人</div>
            <div class="tag-content">{{ ruleForm.person }}</div>
          </div>
          <div class="ps-flex">
            <div class="tag-title">备餐间（含服务态度）</div>
            <div class="tag-content">{{ ruleForm.pantry }}</div>
          </div>
          <div class="ps-flex">
            <div class="tag-title">就餐区（含餐饮浪费情况）</div>
            <div class="tag-content">{{ ruleForm.dining_area }}</div>
          </div>
          <div class="ps-flex">
            <div class="tag-title">其他加工操作区</div>
            <div class="tag-content">{{ ruleForm.other_area }}</div>
          </div>
          <div class="ps-flex">
            <div class="tag-title">餐具消洗</div>
            <div class="tag-content">{{ ruleForm.dishware_cleaning }}</div>
          </div>
          <div class="ps-flex">
            <div class="tag-title border-bottom">明厨亮灶运作</div>
            <div class="tag-content border-bottom">{{ ruleForm.bright_kitchen }}</div>
          </div>
        </div>
        <!--菜品信息-->
        <div class="position-relative">
          <div class="m-t-20 m-b-10">菜品信息及直观评价</div>
          <div class="table-content">
            <!-- table start -->
            <el-table v-loading="loading" :data="tableDataDetail" ref="tableData" style="width: 100%" stripe
              header-row-class-name="ps-table-header-row">
              <table-column v-for="item in detailTableSetting" :key="item.key" :col="item">
                <template #name="{ row }">
                  {{ getFoodName(row) }}
                </template>
                <template #operationImg="{ row }">
                  <el-button type="text" size="small" class="ps-text"
                    @click="viewPicDetail(row.image ? [row.image] : [], 0, row.name)">查看</el-button>
                </template>
              </table-column>
            </el-table>
            <!-- table end -->
          </div>
        </div>
        <div>
          <div class="m-t-20 m-b-10">陪餐实况</div>
          <div class="ps-flex">
            <div :class="[index > 0 ? 'm-l-20' : '']" v-for="(item, index) in ruleForm.personImgList" :key="index">
              <img :src="item" class="w-100px h-100px pointer"
                @click="viewPicDetail(ruleForm.personImgList, index,'陪餐实况')" />
              <div>{{ item.name }}</div>
            </div>
          </div>
        </div>
        <div>
          <div class="m-t-20 m-b-10">陪餐人员签字</div>
          <div class="ps-flex">
            <div :class="[signIndex > 0 ? 'm-l-20' : '']" v-for="(item, signIndex) in ruleForm.signImgList"
              :key="signIndex">
              <img :src="item" class="w-100px h-100px pointer"
                @click="viewPicDetail(ruleForm.signImgList, signIndex,'陪餐人员签字')" />
              <div>{{ item.name }}</div>
            </div>
          </div>
        </div>
        <div class="m-t-20">备注：{{ ruleForm.remark || "无" }}</div>
        <div class="ps-el-drawer-footer">
          <div>
            <el-button size="small" type="primary" class="w-100" @click="closeDialog">关闭</el-button>
          </div>
        </div>
      </div>
      <image-view-preview :isshow.sync="isShowPreViewDialog" :title="dialogPreViewTitle" :picList="imgUrlList"
        :current="currentImgIndex" @close="closePreviewDialog"></image-view-preview>
    </el-drawer>
  </div>
</template>

<script>
import { deepClone, divide } from "@/utils"
import { TABLE_HEAD_MEAL_RECORD_DETAIL } from "../constants"
import ImageViewPreview from '@/components/ImageViewPreview'
import { getNameByType, getPersonNameByList } from "../utils"

export default {
  name: 'MealDetailDialog',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'edit'
    },
    title: {
      type: String,
      default: '详情'
    },
    width: {
      type: String,
      default: '1200px'
    }
  },
  components: {
    ImageViewPreview
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        console.log('newVal', newVal)
      }
    }
  },
  data() {
    return {
      ruleForm: {
        person: "",
        pantry: "",
        dining_area: "",
        other_area: "",
        dishware_cleaning: "",
        bright_kitchen: "",
        personImgList: [],
        signImgList: [],
        remark: ""
      },
      detailTableSetting: deepClone(TABLE_HEAD_MEAL_RECORD_DETAIL),
      tableDataDetail: [],
      loading: false,
      isShowPreViewDialog: false, // 图片预览弹窗
      dialogPreViewTitle: "详情", // 图片弹窗详情
      imgUrlList: [], // 图片预览列表
      currentImgIndex: 0 // 图片预览索引
    }
  },
  methods: {
    // 取消
    closeDialog() {
      console.log("clickCancleHandle")
      this.visible = false
      this.ruleForm = {
        person: "",
        pantry: "",
        dining_area: "",
        other_area: "",
        dishware_cleaning: "",
        bright_kitchen: "",
        personImgList: [],
        signImgList: [],
        remark: ""
      }
      this.tableDataDetail = []
      this.$emit("closeDialog")
    },
    // 关闭图片预览
    closePreviewDialog() {
      this.isShowPreViewDialog = false
    },
    /**
     * 查看图片大图
     * @param {*} imgUrl
     */
    viewPicDetail(imgUrl, index, title) {
      this.dialogPreViewTitle = title
      if (imgUrl && imgUrl.length > 0) {
        // 预览图片
        this.$set(this, 'imgUrlList', imgUrl)
        this.currentImgIndex = index
        this.isShowPreViewDialog = true
      } else {
        this.$message.error('亲，没有图片喔！')
      }
      console.log("viewPicDetail", imgUrl);
    },
    // 设置对话框数据
    setDialogData(data) {
      if (!data || typeof data !== 'object') {
        return
      }
      let ruleForm = deepClone(this.ruleForm)
      ruleForm.person = this.getPersonList(data.person_record_list)
      ruleForm.pantry =
        this.getCurrentNameByType("room_clean_type", data.room_clean_type) +
        "、" +
        this.getCurrentNameByType("room_attitude_type", data.room_attitude_type)
      ruleForm.dining_area =
        this.getCurrentNameByType("area_clean_type", data.area_clean_type) +
        "、" +
        this.getCurrentNameByType("area_waste_type", data.area_waste_type)
      ruleForm.other_area =
        this.getCurrentNameByType("oa_clean_type", data.oa_clean_type) +
        "、" +
        this.getCurrentNameByType("oa_operate_type", data.oa_operate_type)
      ruleForm.dishware_cleaning =
        this.getCurrentNameByType("tda_clean_type", data.tda_clean_type) +
        "、" +
        this.getCurrentNameByType("tda_disinfection_type", data.tda_disinfection_type)
      ruleForm.bright_kitchen = this.getCurrentNameByType("operation_type", data.operation_type)
      ruleForm.personImgList = data.images || []
      ruleForm.signImgList = data.face_url ? [data.face_url] : []
      ruleForm.remark = data.remark
      this.ruleForm = deepClone(ruleForm)
      let foodRecordList = data.food_record_list || []
      let setMealRecordList = data.set_meal_record_list || []
      this.tableDataDetail = foodRecordList.concat(setMealRecordList)
    },
    // 获取人员列表
    getPersonList(list) {
      return getPersonNameByList(list)
    },
    // 获取名称
    getCurrentNameByType(type, value) {
      console.log("getCurrentNameByType", type, value)
      return getNameByType(type, value)
    },
    // 获取详情菜品名称
    getFoodName(row) {
      let name = row.name
      let price = row.price ? divide(row.price) : "0.00"
      return `${name}  ¥${price}/份`
    }
  }
}
</script>

<style lang="scss" scoped>
.meal-management-detail {
  .tag-title {
    width: 250px;
    min-width: 120px;
    background: #f4f6fc;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-left: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }

  .tag-content {
    min-width: 300px;
    max-width: 300px;
    font-size: 14px;
    padding: 15px 20px;
    border-top: 1px solid #e9ecf1;
    border-right: 1px solid #e9ecf1;
  }

  .border-bottom {
    border-bottom: 1px solid #e9ecf1;
  }
}

.el-width-100 {
  width: 100px !important;
  margin: 0 10px;
}

.el-width-180 {
  width: 180px !important;
  margin: 0 10px;
}

.table-content {
  margin-top: 20px;
  padding: 0 !important;
}

.w-100px {
  width: 100px;
}

.h-100px {
  height: 100px;
}
</style>
