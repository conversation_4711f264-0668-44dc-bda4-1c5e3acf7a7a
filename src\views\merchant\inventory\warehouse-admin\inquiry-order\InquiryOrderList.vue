<template>
  <div class="inquiry-list container-wrapper">
    <refresh-tool v-if="showRefresh" @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openImport('inquiry')">导入询价单</button-icon>
          <button-icon color="plain" @click="openImport('offer')">导入报价单</button-icon>
          <button-icon color="origin" @click="gotoHandle('add')">新建询价单</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button
                v-if="row.status === 'revocation'"
                type="text"
                size="small"
                class="ps-text"
                @click="gotoHandle('modify', row)"
              >
                重新编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-text"
                :disabled="!!row.enable"
                @click="gotoHandle('detail', row)"
              >
                详情
              </el-button>
              <el-button
                v-if="row.status === 'disable'"
                type="text"
                size="small"
                class="ps-text"
                @click="clickOperationHandle('submit', row)"
              >
                发起
              </el-button>
              <el-button type="text" size="small" class="ps-text" @click="clickOperationHandle('delete', row)">
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <dialog-message :show.sync="showDialog" title="新增单位" width="435px" @close="closeDialogHandle">
      <el-form :model="dialogForm" :rules="dialogrules" label-position="left" label-width="80px" size="medium">
        <el-form-item label="单位名称" label-width="80px">
          <el-input v-model="dialogForm.name" class="ps-input"></el-input>
        </el-form-item>
        <el-form-item label="关联食物" prop="org_id" label-width="80px">
          <!-- <el-input v-model="dialogForm.org_id" class="ps-input"></el-input> -->
          <el-select class="" style="width: 100%">
            <el-option></el-option>
            <el-option></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="tool" class="footer-center m-t-60">
        <el-button :disabled="dialogLoading" class="ps-cancel-btn w-150" @click="closeDialogHandle">取消</el-button>
        <el-button :disabled="dialogLoading" class="ps-btn w-150" type="primary" @click="clickDialogConfirm">
          确定
        </el-button>
      </div>
    </dialog-message>
    <!-- end -->
    <!-- dialog start -->
    <!-- 涉及到合并表头的模板显示有问题，当前版本没适配 -->
    <!-- <import-page-dialog
      v-if="showImportDialog"
      :show.sync="showImportDialog"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :url="importApi"
      :paramsData="importParamsData"
      :deleteIndexs="deleteIndexs"
    ></import-page-dialog> -->
    <!-- 那就换一直导入的显示方式咯 -->
    <import-files
      :show.sync="showImportDialog"
      :templateUrl="importTemplateUrl"
      :api="importApi"
      :params="importParamsData"
    ></import-files>
    <!-- dialog end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入

export default {
  name: 'InquiryList',
  mixins: [exportExcel],
  props: {
    showRefresh: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '单据编号', key: 'trade_no' },
        { label: '创建时间', key: 'create_time' },
        { label: '发起时间', key: 'initiation_time' },
        { label: '截止日期', key: 'end_time' },
        { label: '询价状态', key: 'status_alias' },
        { label: '经手人', key: 'account_name' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '',
          value: 'create_time',
          maxWidth: '130px',
          placeholder: '请选择',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '发起时间',
              value: 'initiation_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        },
        status: {
          type: 'select',
          label: '询价状态',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '询价中',
              value: 'enable'
            },
            {
              label: '已报价',
              value: 'complete'
            },
            {
              label: '未发起',
              value: 'disable'
            }
          ]
        }
      },
      showDialog: false,
      dialogLoading: false,
      dialogForm: {
        name: '',
        org_id: ''
      },
      dialogrules: {
        // name: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }]
      },
      // 导入的弹窗数据
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: '',
      importHeaderLen: 2,
      importApi: '',
      importParamsData: {
        warehouse_id: +this.$route.query.warehouse_id,
      },
      deleteIndexs: [],
      lazyValue: ''
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      if (this.$route.query.currentPage) {
        this.currentPage = Number(this.$route.query.currentPage)
      }
      this.getInquiryList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key.indexOf('time') < 0) {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            if (key === 'initiation_time') {
              params.initiation_start_time = data[key].value[0]
              params.initiation_end_time = data[key].value[1]
            } else if (key === 'cutoff_time') {
              params.cutoff_start_time = data[key].value[0]
              params.cutoff_end_time = data[key].value[1]
            } else {
              params.start_date = data[key].value[0]
              params.end_date = data[key].value[1]
            }
          }
        }
      }
      return params
    },
    // 切换tab
    changeTabHandle(e) {
      this.searchHandle()
      this.replaceHash()
    },
    // 保存下参数
    replaceHash() {
      this.$router.replace({
        name: 'InquiryList',
        query: {
          currentPage: this.currentPage
        }
      })
    },
    // 获取列表数据
    async getInquiryList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        warehouse_id: +this.$route.query.warehouse_id,
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpInquiryListPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getInquiryList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = ''
      switch (type) {
        case 'revocation':
          title = '确定撤回？'
          apiUrl = 'apiBackgroundDrpInquiryRevocationPost'
          this.showOperationDialog(title, apiUrl, { id: data.id, warehouse_id: +this.$route.query.warehouse_id })
          break
        case 'delete':
          title = '确定删除吗？'
          apiUrl = 'apiBackgroundDrpInquiryDeletePost'
          this.showOperationDialog(title, apiUrl, { ids: [data.id], warehouse_id: +this.$route.query.warehouse_id })
          break
        case 'submit':
          title = '确定发起吗？'
          apiUrl = 'apiBackgroundDrpInquirySubmitPost'
          this.showOperationDialog(title, apiUrl, { id: data.id, warehouse_id: +this.$route.query.warehouse_id })
          break
      }
    },
    // 显示操作弹窗
    showOperationDialog(title, apiUrl, params) {
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getInquiryList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    showDialogHandle(data) {
      this.showDialog = true
    },
    closeDialogHandle() {
      this.showDialog = false
    },
    clickDialogConfirm() {
      this.showDialog = false
    },
    // 导入
    openImport(type) {
      this.importDialogTitle = '批量导入'
      const baseUrl = location.origin
      switch (type) {
        case 'inquiry':
          this.importTemplateUrl = baseUrl + '/api/temporary/template_excel/drp/导入询价.xlsx'
          this.importApi = 'apiBackgroundDrpInquiryBatchAddInquiryPost'
          this.importHeaderLen = 1
          // this.deleteIndexs = [3]
          break
        case 'offer':
          this.importTemplateUrl = baseUrl + '/api/temporary/template_excel/导入报价模板.xlsx'
          this.importApi = 'apiBackgroundDrpInquiryBatchAddOfferPost'
          this.importHeaderLen = 3
          // this.deleteIndexs = [2]
          break
      }
      this.showImportDialog = true
    },
    // 跳转
    gotoHandle(type, row) {
      // 新增
      if (type === 'add') {
        this.$router.push({
          name: 'AddInquiry',
          query: this.$route.query,
          params: {
            type
          }
        })
        return
      }
      if (type === 'modify') {
        this.$router.push({
          name: 'AddInquiry',
          query: {
            ...this.$route.query,
            id: row.id
          },
          params: {
            type
          }
        })
        return
      }
      // 详情
      if (type === 'detail') {
        this.$router.push({
          name: 'InquiryDetail',
          query: {
            id: row.id,
            ...this.$route.query
          }
        })
        return
      }
      // 未发起的详情是跳新增页面
      // if (type === 'detail') {
      //   this.$router.push({
      //     name: 'AddInquiry',
      //     query: {
      //       type,
      //       id: row.id
      //     }
      //   })
      //   return
      // }
    },
    // 导出
    handleExport(row) {
      const option = {
        type: 'InquiryList',
        url: 'apiBackgroundDrpInquiryExportInquiryPost',
        params: {
          id: row.id,
          warehouse_id: +this.$route.query.warehouse_id
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.inquiry-list {
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
}
</style>
