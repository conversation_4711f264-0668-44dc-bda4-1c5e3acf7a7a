<template>
  <!-- 所有台账列表查看详情的抽屉-复核表也能复用这个组件 -->
  <custom-drawer
    v-bind="$attrs"
    :show.sync="showDrawer"
    :title="dialogTitle"
    :loading.sync="isLoading"
    :wrapperClosable="false"
    :size="size"
    :showClose="false"
    :showFooter="showFooter"
    :confirmShow="confirmShow"
    :cancelShow="cancelShow"
    :confirmText="confirmText"
    :cancelText="cancelText"
    :printShow="printShow"
    :printText="printText"
    :exportPermission="exportPermission"
    @open="handleOpen"
    @confirm="handleSave"
    @cancel="handleClose"
    @print="clickPrintHandle"
    @export="clickExportHandle"
  >
    <!-- 这里放置每个人的详情组件 -->
    <component :is="currentComponent" v-if="currentComponent" v-bind="componentProps" ref="currentTableComponent" />
    <!-- 自定义底部按钮 -->
    <slot name="footer"></slot>
  </custom-drawer>
</template>

<script>
import CustomDrawer from '@/components/global/customDrawer'
import { deepClone } from '@/utils'

export default {
  name: 'AllLedgerDetailsDrawerDeetail',
  components: {
    CustomDrawer
  },
  props: {
    visible: {
      type: Boolean,
      required: true,
      default: false
    },
    // ledgerSerialNumber26张台账表的序号
    // 页面引用参考：
    // <details-drawer :visible.sync="isShowDrawer" ledgerSerialNumber="24"></details-drawer>
    ledgerSerialNumber: {
      type: String,
      required: true,
      default: '24'
    },
    // 抽屉标题
    dialogTitle: {
      type: String,
      default: '详情'
    },
    size: {
      type: String,
      default: '40%'
    },
    showFooter: {
      type: Boolean,
      default: false
    },
    ledgerNo: {
      type: String,
      default: ''
    },
    ledgerId: {
      type: [String, Number],
      default: ''
    },
    confirmShow: {
      type: Boolean,
      default: false
    },
    cancelShow: {
      type: Boolean,
      default: false
    },
    confirmText: {
      type: String,
      default: '保 存'
    },
    cancelText: {
      type: String,
      default: '取 消'
    },
    printShow: {
      type: Boolean,
      default: false
    },
    printText: {
      type: String,
      default: '打印'
    },
    reviewPerson: {
      type: String,
      default: ''
    },
    operatorName: {
      type: String,
      default: ''
    },
    exportPermission: {
      type: Array,
      default: () => []
    },
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      // 台账组件映射
      componentMap: {
        Table24: () => import('./compontents/Table24MonthlyFoodSafety.vue'),
        Table25: () => import('./compontents/Table25WorkManagementFoodSafety.vue'),
        Table4: () => import('./compontents/Table4ChoufangChouYouDetail.vue'),
        Table22: () => import('./compontents/Table22XuexiaoShipinAnquanManagementLedger.vue'),
        Table26: () => import('./compontents/Table26JitiYongcanManagementLedger.vue'),
        Table2: () => import('./compontents/Table2CongYeLedgerDetail.vue'),
        Table8: () => import('./compontents/Table8TianjiajiLedgerDetail.vue'),
        Table9: () => import('./compontents/Table9GuoMinYuanLedgerDetail.vue'),
        Table11: () => import('./compontents/Table11xunchaJiluManagementLedger.vue'),
        Table23: () => import('./compontents/Table23XuexiaoMeizhouShipinLedger.vue'),
        Table13: () => import('./compontents/Table13ShipinLiuyangDetail.vue'),
        Table21: () => import('./compontents/Table21CongyeRenyuanPeixunLedgerDetail.vue')
      },
      currentComponent: null,
      componentProps: {}, // 可传递给动态组件的props
      localVisible: false,
      isLoading: false,
      // 打印设置
      printType: '',
      tableSettings: [],
      totalCount: 99999,
      printResultKey: 'result',
      mergeOpts: {
        useKeyList: {}, // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        mergeKeyList: ['inspection_items', 'inspection_contents_secondary_indicators'] // 通用的合并字段，根據值合并
      }
    }
  },
  created() {},
  watch: {},
  computed: {
    showDrawer: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    // 抽屉打开
    async handleOpen() {
      try {
        this.isLoading = true
        this.currentComponent = this.componentMap[`Table${this.ledgerSerialNumber}`] || null
        // 设置组件props
        this.componentProps = {
          // id: this.$route.params.id,
          // ledgerSerialNumber: this.ledgerSerialNumber
          // 其他必要props...
          ledgerId: this.ledgerId, // 台账ID
          ledgerNo: this.ledgerNo, // 台账编号
          reviewPerson: this.reviewPerson, // 复核人
          operatorName: this.operatorName, // 提交人
          id: this.id // 台账id
        }
      } catch (error) {
        this.isLoading = false
        console.error('组件加载失败:', error)
        this.$message.error(error.message || '组件加载失败')
      } finally {
        this.isLoading = false
      }
    },
    // 抽屉关闭
    handleClose() {
      this.showDrawer = false
      this.currentComponent = null
      this.componentProps = {}
      this.$emit('close')
    },
    // 抽屉确认按钮
    handleSave() {
      this.$emit('confirm')
    },
    // 打印事件
    clickPrintHandle() {
      // 获取当前表格组件的tableData
      if (this.$refs.currentTableComponent) {
        const tableData = Reflect.has(this.$refs.currentTableComponent, 'tableData') ? this.$refs.currentTableComponent.tableData : []
        const tableSetting = Reflect.has(this.$refs.currentTableComponent, 'tableSetting') ? this.$refs.currentTableComponent.tableSetting : []
        console.log('当前表格数据:', tableData)
        // 将tableData传递给父组件
        this.$emit('print', tableData, tableSetting)
        switch (this.ledgerSerialNumber) {
          case '25':
            this.printType = 'Table25WorkManagementFoodSafety'
            this.printTitle = '学校校园食品安全工作管理清单'
            this.printApi = 'apiBackgroundFundSupervisionLedgerFoodSafetyLedgerReviewDetail'
            this.printParams = {
              id: this.ledgerId,
              username: this.$refs.currentTableComponent.baseInfo.username,
              create_time: this.$refs.currentTableComponent.baseInfo.create_time,
              phone: this.$refs.currentTableComponent.baseInfo.phone
            }
            this.printResultKey = 'data'
            this.tableSettings = this.$refs.currentTableComponent.tableSetting
            // console.log('打印参数', this.printParams);
            // return
            this.gotoPrint()
            break;
          default:
            break;
        }
      } else {
        this.$emit('print')
      }
    },
    // 导出事件
    clickExportHandle() {
      this.$emit('export')
    },
    // 打印事件
    gotoPrint() {
      // const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.tableSettings)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: this.printTitle,
          result_key: this.printResultKey, // 返回的数据处理的data keys
          api: this.printApi, // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          mergeOpts: JSON.stringify(this.mergeOpts), // 合并字段
          params: JSON.stringify({
            ...this.printParams,
            page: 1,
            page_size: this.totalCount || 10
          }),
          // 自定义打印表头 start
          isUstomTableHeaders: true,
          customHeaderFields: JSON.stringify([
            { label: '检查人', key: 'username', prefix: '检查人', defaultValue: '--' },
            { label: '检查时间', key: 'create_time', prefix: '检查时间', defaultValue: '--' },
            { label: '联系电话', key: 'phone', prefix: '联系电话', defaultValue: '--' }
          ])
          // 自定义打印表头 end
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  .el-select {
    width: 100%;
  }
}

.tip-icon {
  margin-left: 10px;
}

.txt-gray {
  color: #3d3b3b;
}
</style>
