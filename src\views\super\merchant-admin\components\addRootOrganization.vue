<template>
  <div class="container-wrapper super-add-organization is-fixed-footer">
    <!-- tab end -->
    <el-form
      ref="organizationFormRef"
      v-loading="isLoading"
      :rules="formDataRuls"
      :model="formData"
      class="organization-form-wrapper"
      :hide-required-asterisk='true'
      size="small"
    >
      <div v-if="operate === 'add'" class="add-title">新建组织</div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">基本信息</span>
        <el-button
          v-if="!checkIsFormStatus"
          @click="changeOperate"
          size="mini"
          class="float-r"
        >
          编辑
        </el-button>
      </div>
      <div class="item-box clearfix">
        <div class="item-b-l">{{ labelName }}</div>
        <div class="item-b-r">
          <el-form-item class="block-label" prop="name">
            <template #label>
              <span class="warn">*</span>
              组织名称：
            </template>
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.name"
            ></el-input>
            <div class="item-form-text" v-else>{{ formData.name }}</div>
          </el-form-item>
        </div>
      </div>
      <!-- 当前组织层次 -->
      <el-form-item class="block-label form-item-box" label="当前组织层次：" prop="levelName">
        <!-- <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.levelName"></el-input> -->
        <div class="item-form-text">{{ formData.levelName }}</div>
      </el-form-item>
      <!-- 官网 -->
      <el-form-item class="block-label form-item-box" label="官网：" prop="url">
        <el-input
          v-if="checkIsFormStatus"
          class="ps-input"
          size="small"
          v-model="formData.url"
        ></el-input>
        <div class="item-form-text" v-else>{{ formData.url }}</div>
      </el-form-item>

      <el-row class="form-item-row-box" :gutter="24">
        <!-- 固定电话 -->
        <el-col :span="12">
          <el-form-item class="block-label form-item-box" label="固定电话：" prop="tel">
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.tel"
            ></el-input>
            <div class="item-form-text" v-else>{{ formData.tel }}</div>
          </el-form-item>
        </el-col>
        <!-- 组织邮箱 -->
        <el-col :span="12">
          <div class="form-item-box">
            <el-form-item class="block-label" label="组织邮箱：" prop="mailAddress">
              <el-input
                v-if="checkIsFormStatus"
                class="ps-input"
                size="small"
                v-model="formData.mailAddress"
              ></el-input>
              <div class="item-form-text" v-else>{{ formData.mailAddress }}</div>
            </el-form-item>
          </div>
        </el-col>
      </el-row>

      <el-row class="form-item-row-box" :gutter="24">
        <!-- 行业性质 -->
        <el-col class="block-label form-item-box" :span="12">
          <el-form-item label="行业性质：" prop="industry">
            <template #label>
              <div>
                <span class="warn">*</span>
                行业性质：
              </div>
            </template>
            <el-select
              v-model="formData.industry"
              placeholder="请选择行业性质"
              class="ps-select"
              style="width: 100%"
              size="small"
              :disabled="!checkIsFormStatus"
            >
              <el-option
                v-for="item in industryTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 所在地址 -->
        <el-col class="block-label form-item-box" :span="12">
          <el-form-item prop="district">
            <template #label>
              <div class="flex-b-c">
                <div>
                  <span class="warn">*</span>
                  所在地址：
                </div>
                <el-button type="text" class="ps-origin-text" :disabled="!checkIsFormStatus" @click="openMessageBox">同步到下级</el-button>
              </div>
            </template>
            <template #default>
              <el-cascader
                ref="cascaderRef"
                size="small"
                :options="addrOptions"
                v-model="formData.district"
                style="display: block"
                :disabled="!checkIsFormStatus"
                filterable
                @blur="getAddress"
              ></el-cascader>
            </template>
          </el-form-item>
        </el-col>
         <!-- 所属渠道 -->
         <el-col class="block-label form-item-box" :span="12">
          <el-form-item label="所属渠道" class="block-label" prop="channel_id">
            <template #label>
              <div>
                <span class="warn">*</span>
                所属渠道：
              </div>
            </template>
          <el-cascader class="ps-select" placeholder="请选择" style="width: 100%" v-model="formData.channel_id" clearable ref="channelMul"
            :options="channelTreeList" :show-all-levels="false" :props="cascaderProps" :disabled="!checkIsFormStatus" filterable></el-cascader>
          </el-form-item>
          </el-col>
      </el-row>
      <div class="form-line"></div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">功能配置</span>
      </div>
      <!-- 新增版本 -->
      <div>
        <el-button size="small" :class="['w-100', 'm-l-10', 'm-b-10', versionData.tollVersion === -1 ? 'is-click' : '']" :disabled="!checkIsFormStatus" @click="selectThisVersion(null, true)">自由配置</el-button>
        <el-button size="small" :class="['w-100', 'm-b-10', versionData.tollVersion === item.id ? 'is-click' : '']" v-for="(item, index) in versionList" :key="index" :disabled="!checkIsFormStatus" @click="selectThisVersion(item, true)">{{ item.name }}</el-button>
      </div>
      <!-- 添加组织层级 -->
      <el-form-item
        v-if="operate === 'add'"
        class="block-label form-item-box"
        label="添加组织层级："
        prop="initOrganizationLevel"
      >
        <el-select
          clearable
          v-model="formData.initOrganizationLevel"
          style="width: 100%"
          placeholder="请选择"
        >
          <el-option
            v-for="item in levelList"
            :key="item.level"
            :label="item.name"
            :value="item.level"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 功能菜单配置 -->
      <el-form-item class="block-label form-item-box" label="" prop="permission">
        <span class="ps-flex-align-c">
          <div class="warn m-r-5">*</div>
          <div class="f-w-700" style="color: #606266">食堂管理系统-功能配置：</div>
          <el-button size="small" type="text" class="w-80" :disabled="!checkIsFormStatus" @click="showDrawer('merchant')">去配置</el-button>
          <span class="font-size-12 origin">（{{ formData.permission.length }} 个）</span>
        </span>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="">
        <span class="ps-flex-align-c">
          <div class="f-w-700" style="color: #606266">商户移动端-菜单配置：</div>
          <el-button size="small" type="text" class="w-80" :disabled="!checkIsFormStatus" @click="showDrawer('merchant_app')">去配置</el-button>
          <span class="font-size-12 origin">（{{ formData.merchantAppPermission.length }} 个）</span>
        </span>
      </el-form-item>
      <!-- <el-form-item class="block-label form-item-box" label="" prop="useCardNoLimit">
        <span>
          <span class="warn">*</span>
          IC卡验证
          <el-radio-group
            v-model="formData.useCardNoLimit"
            :disabled="!checkIsFormStatus"
            class="ps-radio"
          >
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </span>
      </el-form-item> -->
      <!-- 账号 -->
      <el-form-item
        v-if="type === 'root'"
        class="block-label form-item-box fixed-login-box"
        prop="username"
      >
        <template #label>
          <span class="warn">*</span>
          账号：
        </template>
        <span v-if="type === 'root' && operate !== 'add'" class="fixed-login">
          <el-button type="text" size="mini" @click="gotoLogin">登录</el-button>
        </span>
        <el-input
          v-if="type === 'root' && operate === 'add'"
          class="ps-input"
          size="small"
          v-model="formData.username"
        ></el-input>
        <div v-else class="item-form-text">{{ formData.username }}</div>
      </el-form-item>
      <!-- 密码 -->
      <el-form-item
        v-if="type === 'root' && operate === 'add'"
        class="block-label form-item-box"
        prop="password"
      >
        <template #label>
          <span class="warn">*</span>
          密码：
        </template>
        <el-input
          :disabled="!checkIsFormStatus"
          class="ps-input"
          size="small"
          v-model="formData.password"
        ></el-input>
        <div style="margin-top: 3px; color: #f56c6c; line-height: 1; font-size: 12px">
          密码有效期为90天，请在期限前重置密码
        </div>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span>
          到期修改密码
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.isExpireChangePwd"
            active-color="#ff9b45"
          ></el-switch>
          <el-checkbox
            v-model="formData.allowJumpChangePwd"
            v-if="formData.isExpireChangePwd"
            style="margin-left: 10px"
            :disabled="!checkIsFormStatus"
            class="ps-checkbox"
          >
            允许跳过本次
          </el-checkbox>
        </span>
      </el-form-item>
      <!-- <el-form-item class="block-label form-item-box" label="退款密码：" prop="refundPassword">
        <el-input :disabled="!checkIsFormStatus" class="ps-input" size="small" v-model="formData.refundPassword"></el-input>
      </el-form-item> -->

      <div class="form-line"></div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">联系方式</span>
      </div>

      <el-row class="form-item-row-box" :gutter="24">
        <!-- 联系人 -->
        <el-col :span="12">
          <el-form-item class="block-label" label="联系人：" prop="contact">
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.contact"
            ></el-input>
            <div class="item-form-text" v-else>{{ formData.contact }}</div>
          </el-form-item>
        </el-col>
        <!-- 手机号码 -->
        <el-col :span="12">
          <el-form-item class="block-label" prop="mobile">
            <template #label>
              <span class="warn">*</span>
              手机号码:
            </template>
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.mobile"
            ></el-input>
            <div class="item-form-text" v-else>{{ formData.mobile }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 短信模板 -->
      <el-form-item class="block-label form-item-box" label="短信模板：" prop="smsTemplateId">
        <el-input
          v-if="checkIsFormStatus"
          class="ps-input"
          v-model="formData.smsTemplateId"
          type="textarea"
          :rows="3"
        ></el-input>
        <div class="item-form-text" v-else>{{ formData.smsTemplateId }}</div>
      </el-form-item>
      <!-- 备注 -->
      <el-form-item class="block-label form-item-box" label="备注：" prop="remark">
        <el-input
          v-if="checkIsFormStatus"
          class="ps-input"
          v-model="formData.remark"
          type="textarea"
          :rows="3"
        ></el-input>
        <div class="item-form-text" v-else>{{ formData.remark }}</div>
      </el-form-item>

      <div class="form-line"></div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">其它设置</span>
      </div>
      <!-- 钱包设置 -->
      <el-form-item class="block-label form-item-box" label="钱包设置" prop="">
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.storeWalletOn"
        >
          储值钱包
        </el-checkbox>
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.electronicWalletOn"
        >
          电子钱包
        </el-checkbox>
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.subsidyWalletOn"
        >
          补贴钱包
        </el-checkbox>
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.complimentaryWalletOn"
        >
          赠送钱包
        </el-checkbox>
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.otherWalletOn"
        >
          第三方钱包
        </el-checkbox>
      </el-form-item>
      <!-- <el-form-item class="block-label form-item-box" label="" prop="">
        钱包扣款规则
        <el-radio-group
          v-model="formData.isWalletPayOrderAsc"
          :disabled="!checkIsFormStatus"
          class="ps-radio"
        >
          <el-radio :label="false">扣上级钱包余额</el-radio>
          <el-radio :label="true">扣下级钱包余额</el-radio>
        </el-radio-group>
        <div style="margin-left: 88px;color: #ff9b45">当订单所属组织余额不足时将扣取余额充足的上级/下级钱包金额;该规则仅适合线下消费</div>
      </el-form-item> -->
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span style="margin-right: 25px">
          线下组合支付
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.combineWalletOn"
            active-color="#ff9b45"
          ></el-switch>
        </span>
        <span>
          线上组合支付
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.onlineCombineWallet_on"
            active-color="#ff9b45"
          ></el-switch>
        </span>
      </el-form-item>
      <!-- 开关设置 -->
      <el-form-item class="form-item-box" label="开关设置" prop="">
        <span style="margin-right: 25px">
          人脸支付
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.facepay"
            active-color="#ff9b45"
          ></el-switch>
        </span>
        <span>
          支持退款
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.refundOn"
            active-color="#ff9b45"
          ></el-switch>
        </span>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span>
          是否农行项目点展示
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.isAbcProject"
            active-color="#ff9b45"
          ></el-switch>
        </span>
      </el-form-item>
      <!-- <el-form-item class="block-label form-item-box" label="" prop="">
        <span>
          开启会员功能
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.is_member_on"
            active-color="#ff9b45"
          ></el-switch>
        </span>
      </el-form-item> -->
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span>人脸更新消息提醒：</span>
        <el-switch
          class="m-r-20"
          :disabled="!checkIsFormStatus"
          v-model="formData.enableUpdateNotify"
          active-color="#ff9b45"
        ></el-switch>
        <div v-if="formData.enableUpdateNotify" style="margin-left: 125px">
          <span class="m-r-20">上传人脸时间每隔</span>
          <el-form-item
            class="inline-label form-item-box m-t-2 m-b-2 m-r-20"
            label=""
            prop="faceUpdateTime"
          >
            <el-select
              class="w-110"
              clearable
              v-model="formData.faceUpdateTime"
              :disabled="!checkIsFormStatus"
              placeholder="请选择"
            >
              <el-option
                v-for="item in faceUploadOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="formData.faceUpdateTime === 'auto'"
            class="inline-label form-item-box m-t-2 m-b-2 m-r-20"
            label=""
            prop="customFaceDate"
          >
            <el-input
              v-model="formData.customFaceDate"
              :disabled="!checkIsFormStatus"
              class="w-100"
            ></el-input>
            <span class="m-l-10">天</span>
          </el-form-item>
          <span class="">进行消息提醒</span>
          <el-form-item class="block-label form-item-box" label="" prop="">
            <span style="vertical-align: top">提醒内容：</span>
            <el-input
              v-model="formData.notifyMsg"
              :disabled="!checkIsFormStatus"
              type="textarea"
              :rows="2"
              style="width: 70%"
            ></el-input>
          </el-form-item>
        </div>
      </el-form-item>
      <div v-if="operate !== 'add'">
        <div class="form-line"></div>
        <div class="l-title clearfix">
          <span class="float-l min-title-h">
            第三方设置
            <el-switch
              :disabled="!checkIsFormStatus"
              style="margin-left: 15px"
              v-model="formData.isThirdInterface"
              active-color="#ff9b45"
            ></el-switch>
          </span>
        </div>
        <div v-loading="loadingThirdInfo" v-show="formData.isThirdInterface">
          <el-row class="form-item-row-box" :gutter="24">
            <!-- 应用key -->
            <el-col :span="12">
              <el-form-item class="block-label" label="应用key：" prop="thirdAppKey">
                <el-input
                  v-if="checkIsFormStatus"
                  class="ps-input"
                  size="small"
                  v-model="formData.thirdAppKey"
                ></el-input>
                <div v-else class="item-form-text">{{ formData.thirdAppKey }}</div>
              </el-form-item>
            </el-col>
            <!-- 应用secret -->
            <el-col :span="12">
              <el-form-item class="block-label" label="应用secret：" prop="thirdSecretKey">
                <el-input
                  v-if="checkIsFormStatus"
                  class="ps-input"
                  size="small"
                  v-model="formData.thirdSecretKey"
                ></el-input>
                <el-tooltip
                  v-else
                  class="item"
                  effect="dark"
                  :content="formData.thirdSecretKey"
                  placement="top"
                >
                  <div class="item-form-text ellipsis">{{ formData.thirdSecretKey }}</div>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item class="block-label" label="应用名称：" prop="thirdAppName">
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.thirdAppName"
            ></el-input>
            <div class="item-form-text ellipsis" v-else>{{ formData.thirdAppName }}</div>
          </el-form-item>
          <el-row class="form-item-row-box" :gutter="24">
            <!-- 联系人 -->
            <el-col :span="12">
              <el-form-item class="block-label" label="跳转地址：" prop="thirdAppUrl">
                <el-input
                  v-if="checkIsFormStatus"
                  class="ps-input"
                  size="small"
                  v-model="formData.thirdAppUrl"
                ></el-input>
                <div class="item-form-text" v-else>{{ formData.thirdAppUrl }}</div>
              </el-form-item>
            </el-col>
            <!-- 回调地址 -->
            <el-col :span="12">
              <el-form-item class="block-label" label="回调地址：" prop="thirdAppCallbackUrl">
                <el-input
                  v-if="checkIsFormStatus"
                  placeholder="http://127.0.0.1/?userId={0}&bb=1，{0}将会被替换掉"
                  class="ps-input"
                  size="small"
                  v-model="formData.thirdAppCallbackUrl"
                ></el-input>
                <div class="item-form-text" v-else>{{ formData.thirdAppCallbackUrl }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item v-if="checkIsFormStatus" class="block-center" label="" prop="">
            <el-button class="ps-origin-btn" type="primary" @click="generateThirdAppinfo">
              重新生成
            </el-button>
          </el-form-item>
        </div>
      </div>

      <div v-if="checkIsFormStatus" class="form-footer">
        <el-button @click="cancelFormHandle" size="small">取消</el-button>
        <el-button
          @click="sendFormdataHandle"
          class="ps-origin-btn"
          type="primary"
          size="small"
        >
          保存
        </el-button>
      </div>
    </el-form>

    <!-- 抽屉 -->
    <ConfigurationList :isShow.sync="drawerShow" :type="drawerType" @refreshPermission="refreshPermission"/>
  </div>
</template>

<script>
import { to, debounce, getTreeDeepkeyList, camelToUnderline, deepClone, setRemoveEmptyLevel } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { regionData } from 'element-china-area-data'
import industryType from '@/assets/data/industryType.json'
import { validateTelphone, validateName } from '@/assets/js/validata'
import { validateEmail, validateTel } from '@/utils/form-validata'
// import SelectTree from '@/components/SelectTree'
import ConfigurationList from './ConfigurationList.vue'
import { mapGetters } from 'vuex'
// import md5 from 'js-md5';

export default {
  name: 'SuperAddRootOrganization',
  components: { ConfigurationList },
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: {
      // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    treeData: Object,
    id: [String, Number],
    operate: String,
    restoreHandle: Function
  },
  data() {
    let validateAccount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('账号不能为空'))
      } else {
        // let regNum = /(^\d{3,12}$)|(^[a-zA-Z]{3,12}$)/;
        let regNum = /^\w{5,20}$/
        if (!regNum.test(value)) {
          callback(new Error('账号长度5到20位，只支持数字、大小写英文或下划线组合'))
        } else {
          callback()
        }
      }
    }
    let validatePass = (rule, value, callback) => {
      let regPass = /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/
      // let regRPass = /(^\w{6,32}$)/;
      if (!value) {
        if (this.formOperate === 'modify') {
          callback()
          return
        }
        return callback(new Error('密码不能为空'))
      } else {
        if (!regPass.test(value)) {
          callback(new Error('密码长度8到20位，字母和数组组合'))
        } else {
          callback()
        }
      }
    }
    let validatePermission = (rule, value, callback) => {
      if (value.length > 0) {
        callback()
      } else {
        callback(new Error('功能菜单配置不能为空！'))
      }
    }
    let validateCustomFaceDate = (rule, value, callback) => {
      if (value === '' || value === '0') {
        return callback(new Error('请输入大于0的数字'))
      } else {
        let number = /^\d+$/
        if (!number.test(value)) {
          callback(new Error('请输入正确数字'))
        } else {
          callback()
        }
      }
    }
    return {
      labelName: '',
      formOperate: 'detail',
      isLoading: false,
      industryTypeList: industryType,
      addrOptions: regionData,
      formData: {
        id: '',
        appid: '',
        secretKey: '',
        name: '', // 组织名称
        levelName: '', // 当前组织层次名称
        initOrganizationLevel: '', // 组织层级
        tollVersion: '',
        permission: [], // 功能菜单配置
        merchantAppPermission: [], // 移动端菜单配置
        appPermission: [], // h5金刚区权限
        username: '', // 账号
        password: '', // 密码
        url: '', // 官网
        district: [], // 所在地址
        contact: '', // 联系人
        mobile: '', // 手机
        mailAddress: '', // 邮箱
        tel: '', // 电话
        industry: '', // 行业性质
        remark: '', // 备注
        facepay: false, // 人脸支付开关
        refundOn: false, // 支持退款
        refundPassword: '', // 退款密码
        storeWalletOn: false, // 储值钱包开关
        electronicWalletOn: false, // 电子钱包开关
        subsidyWalletOn: false, // 补贴钱包开关
        complimentaryWalletOn: false, // 赠送钱包开关
        otherWalletOn: false, // 第三方钱包开关
        isThirdInterface: false, // 是否开启第三方配置
        combineWalletOn: false, // 组合支付开关
        onlineCombineWallet_on: false,
        thirdAppKey: '',
        thirdSecretKey: '', //
        thirdAppName: '', // 第三方名称
        thirdAppUrl: '', // 第三方配置地址
        thirdAppCallbackUrl: '', // 第三方配置回调url
        smsTemplateId: '', // 短信模板
        isAbcProject: false, // 是否农行项目点展示
        isExpireChangePwd: false, // 到期修改密码
        allowJumpChangePwd: false, // 允许跳过修改密码
        // useCardNoLimit: false, // IC卡校验
        enableUpdateNotify: false, // 是否开启人脸更新提醒
        faceUpdateTime: '', // 人脸更新提醒日期
        notifyMsg: '', // 消息内容
        isWalletPayOrderAsc: false, // 钱包扣款顺序，不要了哦
        channel_id: [], // 所属渠道
        is_member_on: false // 是否开启会员功能
      },
      formDataRuls: {
        name: [
          { required: true, message: '组织名称不能为空', trigger: 'blur' },
          { validator: validateName, trigger: 'blur' }
        ],
        district: [
          { required: true, message: '所在地址不能为空', trigger: 'blur' }
        ],
        channel_id: [{ required: true, message: '请选择渠道', trigger: ['blur', 'change'] }],
        level_name: [{ required: true, message: '层级名称不能为空', trigger: 'blur' }],
        mobile: [{ required: true, validator: validateTelphone, trigger: 'blur' }],
        username: [{ required: true, validator: validateAccount, trigger: 'blur' }],
        password: [{ required: true, validator: validatePass, trigger: 'blur' }],
        industry: [{ required: true, message: '请选择', trigger: 'blur' }],
        refundPassword: [{ validator: validatePass, trigger: 'blur' }],
        tel: [{ validator: validateTel, trigger: 'blur' }],
        mailAddress: [{ validator: validateEmail, trigger: 'blur' }],
        permission: [{ required: true, validator: validatePermission, trigger: 'blur' }],
        // useCardNoLimit: [{ required: true, message: '不能为空', trigger: 'blur' }],
        faceUpdateTime: [{ required: true, message: '请选择人脸更新天数', trigger: 'blur' }],
        customFaceDate: [{ validator: validateCustomFaceDate, trigger: 'blur' }]
      },
      levelList: [],
      // mobileTree: [
      //   {
      //     id: 'folder',
      //     label: 'Normal Folder',
      //     children: [
      //       { id: 'disabled-checked', label: 'Checked', isDisabled: true },
      //       { id: 'disabled-unchecked', label: 'Unchecked', isDisabled: true },
      //       { id: 'item-1', label: 'Item' }
      //     ]
      //   }
      // ],
      // permissionTree: [
      //   {
      //     id: 'folder',
      //     label: 'Normal Folder',
      //     children: [
      //       { id: 'disabled-checked', label: 'Checked', isDisabled: true },
      //       { id: 'disabled-unchecked', label: 'Unchecked', isDisabled: true },
      //       { id: 'item-1', label: 'Item' }
      //     ]
      //   }
      // ],
      // permissionTreeProps: {
      //   value: 'key',
      //   label: 'verbose_name',
      //   isLeaf: 'is_leaf',
      //   children: 'children'
      // },
      loadingThirdInfo: false,
      faceUploadOptions: [
        { name: '60天', value: 60 },
        { name: '90天', value: 90 },
        { name: '180天', value: 180 },
        { name: '1年', value: 365 },
        { name: '自定义', value: 'auto' }
      ],
      cascaderProps: {
        label: 'name',
        value: 'id',
        children: 'children_list',
        checkStrictly: true
      },
      channelTreeList: [], // 渠道选择列表
      drawerType: '',
      drawerShow: false,
      versionList: [],
      versionData: {},
      defaultVersionData: {},
      addressData: {}
    }
  },
  computed: {
    ...mapGetters([
      'permissionData'
    ]),
    // formOperate: {
    //   get() {
    //     return this.operate
    //   },
    //   set(val) {
    //     // this.$emit('update:operate', val)
    //   }
    // },
    // 检查当前状态，编辑还是详情
    checkIsFormStatus: function () {
      // 默认为false
      let show = false
      switch (
        this.operate // 目前从父组件传过来的操作类型只会有2个add和detail
      ) {
        case 'add':
          show = true
          break
        case 'detail':
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break
        default:
          // 没传的话
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break
      }
      return show
    }
  },
  watch: {
    operate: function (val, old) {
      if (!val) {
        this.formOperate = 'detail'
      }
    }
  },
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      await this.initDic()
      this.getLevelList(this.id)
      this.getPermissionTreeList(this.id)
      this.getMobileList(this.id)
      await this.getVersionList()
      if (this.id && this.operate !== 'add') {
        this.initInfoHandle()
      }
      if (this.operate) {
        this.formOperate = this.operate
      }
      if (this.treeData && this.operate !== 'add') {
        this.labelName = this.treeData.name.substring(0, 1)
      }
      if (this.operate === 'add') {
        this.labelName = '朴'
      }
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function () {}, 300),
    getAddress(e) {
      this.addressData = this.$refs.cascaderRef.getCheckedNodes()
      console.log('获取数据', this.addressData[0])
    },
    initInfoHandle() {
      console.log('this.infoData', this.infoData)
      for (let key in this.formData) {
        let val = this.infoData[camelToUnderline(key)]
        if (val) {
          switch (key) {
            case 'industry':
              this.formData[key] = val.toString()
              break
            case 'district':
              this.formData[key] = JSON.parse(val)
              break
            case 'channel_id':
              if (val !== null) {
                var checkeckList = this.getParentsById(this.channelTreeList, val)
                this.formData[key] = checkeckList || []
              }
              break
            case 'faceUpdateTime':
              // this.formData[key] = JSON.parse(val)
              // eslint-disable-next-line no-case-declarations
              let hasVal = false
              this.faceUploadOptions.forEach(v => {
                // eslint-disable-next-line eqeqeq
                if (v.value == val) {
                  hasVal = true
                }
              })
              if (hasVal) {
                this.formData[key] = val
              } else {
                if (val) {
                  this.formData[key] = 'auto'
                  this.formData.customFaceDate = val
                }
              }
              break
            case 'tollVersion':
              this.formData.tollVersion = val
              this.versionData.tollVersion = val || -1
              break
            case 'permission':
              this.formData.permission = [
                ...this.defaultVersionData.permission || [],
                ...val
              ]
              break
            case 'merchantAppPermission':
              this.formData.merchantAppPermission = [
                ...this.defaultVersionData.merchant_app_permission || [],
                ...val
              ]
              break
            case 'appPermission':
              this.formData.appPermission = [
                ...this.defaultVersionData.app_permission || [],
                ...val
              ]
              break
            default:
              this.formData[key] = val
              break
          }
        }
      }
      console.log('this.formData', this.formData)
      // 整理一下权限
      let obj = {
        tollVersion: this.formData.tollVersion || -1,
        permission: this.formData.permission || [],
        merchant_app_permission: this.formData.merchantAppPermission || [],
        app_permission: this.formData.appPermission || []
      }
      this.versionData = deepClone(obj)
      this.$store.dispatch('permission/setPermissionData', this.versionData)
    },
    // pc
    // permissionNormalizer(node) {
    //   return {
    //     id: node.key,
    //     label: node.verbose_name,
    //     children: node.children
    //   }
    // },
    // // 移动端
    // mobileNormalizer(node) {
    //   return {
    //     id: node.key,
    //     label: node.verbose_name,
    //     children: node.children
    //   }
    // },

    // 处理下没有children_list
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        if (data && data.length) {
          data.map(item => {
            if (_that.checkIsFormStatus) {
              item.isDisabled = false
            } else {
              item.isDisabled = true
            }
            if (item[key]) {
              if (item[key].length > 0) {
                traversal(item[key])
              } else {
                _that.$delete(item, key)
              }
            } else {
              _that.$delete(item, key)
            }
          })
        }
      }
      traversal(treeData)
      return treeData
    },
    // 获取指定公司的层级列
    async getLevelList(companyId) {
      let params = {}
      if (companyId) {
        params.company_id = companyId
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminOrganizationGetLevelNameMapPost(params)
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.levelList = res.data
        if (res.data.length > 0 && this.operate === 'add') {
          this.formData.levelName = res.data[0].name
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取权限树状结构
    async getPermissionTreeList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminOrganizationGetMerchantPermissionsPost()
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.permissionTree = this.deleteEmptyChildren(res.data, 'children')
        if (this.operate === 'add') {
          this.formData.permission = getTreeDeepkeyList(this.permissionTree, 'key')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取移動端菜單配置树状结构
    async getMobileList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminOrganizationGetMerchantMobilePermissions()
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.mobileTree = this.deleteEmptyChildren(res.data, 'children')
        if (this.operate === 'add') {
          this.formData.merchantAppPermission = getTreeDeepkeyList(this.mobileTree, 'key')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 功能菜單配置全選和不全选
    clickSelectPermissionTree(type) {
      if (type === 1) {
        this.formData.permission = getTreeDeepkeyList(this.permissionTree, 'key')
      } else {
        this.formData.permission = []
      }
    },
    // 移动端菜單配置全選和不全选
    clickSelectMobileTree(type) {
      if (type === 1) {
        this.formData.merchantAppPermission = getTreeDeepkeyList(this.mobileTree, 'key')
      } else {
        this.formData.merchantAppPermission = null
      }
    },
    changeOperate() {
      switch (
        this.operate // 目前从父组件传过来的操作类型只会有2个add和detail
      ) {
        case 'add': // noth
          break
        default:
          if (this.formOperate === 'detail') {
            this.formOperate = 'modify'
          } else {
            this.formOperate = 'detail'
          }
          break
      }
      this.permissionTree = this.deleteEmptyChildren(this.permissionTree, 'children')
      this.mobileTree = this.deleteEmptyChildren(this.mobileTree, 'children')
    },
    // 取消
    cancelFormHandle() {
      if (this.operate === 'add') {
        this.$refs.organizationFormRef.resetFields() // 重置表单数据
      } else {
        this.$refs.organizationFormRef.clearValidate() // 清空表单校验
        this.formOperate = 'detail'
        this.permissionTree = this.deleteEmptyChildren(this.permissionTree, 'children')
        this.mobileTree = this.deleteEmptyChildren(this.mobileTree, 'children')
      }
      this.restoreHandle(this.type, this.formOperate)
    },
    async generateThirdAppinfo() {
      this.loadingThirdInfo = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminOrganizationGenerateThirdAppinfoPost({
          id: this.id
        })
      )
      this.loadingThirdInfo = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.formData.thirdAppKey = res.data.third_app_key
        this.formData.thirdSecretKey = res.data.third_secret_key
      } else {
        this.$message.error(res.msg)
      }
    },
    // 发送请求
    sendFormdataHandle() {
      this.$refs.organizationFormRef.validate(valid => {
        if (valid) {
          if (this.operate === 'add') {
            // 添加
            this.addRootOrganization(this.formatData())
          } else {
            // 编辑
            this.modifyOrganization(this.formatData())
          }
        }
      })
    },
    // 格式化下传给后台的参数
    formatData() {
      let params = {
        status: 'enable'
      }
      for (let key in this.formData) {
        let val = this.formData[key]
        if (val !== '') {
          switch (key) {
            case 'district':
              val = JSON.stringify(val)
              break
            case 'channel_id':
              if (!this.formData.channel_id || this.formData.channel_id.length === 0) {
                val = null
              } else {
                var id = null
                if (this.$refs.channelMul) {
                  var checkList = this.$refs.channelMul.getCheckedNodes({ leafOnly: false })
                  if (checkList && checkList.length > 0) {
                    id = checkList[0].value
                  }
                }
                val = id
              }
              break
            case 'password':
              // val = md5(val)
              break
            case 'refundPassword':
              // val = md5(val)
              break
            case 'thirdAppUrl':
              val = encodeURIComponent(val)
              break
            case 'faceUpdateTime': // 人脸更新天数
              val = val === 'auto' ? this.formData.customFaceDate : val
              break
            case 'tollVersion':
              val = (val && val !== -1) ? this.formData.tollVersion : null
          }
          if (key !== 'levelName' && key !== 'customFaceDate') {
            params[camelToUnderline(key)] = val
          }
        }
        if (this.formOperate === 'modify') {
          params.company = this.treeData.company
        }
      }
      if (params.channel_id === null || params.channel_id.length === 0) {
        delete params.channel_id
      }
      // 如果不是自由配置，即选了版本的，只提交版本没有的permission就可以了
      if (this.formData.tollVersion && this.formData.tollVersion !== -1) {
        let newArr1 = this.getDifference(this.formData.permission, this.defaultVersionData.permission)
        params.permission = deepClone(newArr1)
        let newArr2 = this.getDifference(this.formData.merchantAppPermission, this.defaultVersionData.merchant_app_permission)
        params.merchant_app_permission = deepClone(newArr2)
        let newArr3 = this.getDifference(this.formData.appPermission, this.defaultVersionData.app_permission)
        params.app_permission = deepClone(newArr3)
      } else {
        params.permission = deepClone(this.formData.permission)
        params.merchant_app_permission = deepClone(this.formData.merchantAppPermission)
        params.app_permission = deepClone(this.formData.appPermission)
      }
      return params
    },
    // 权限的处理
    getPermissionLevelParent(data) {
      let parentKeys = []
      // this.permissionTree = this.deleteEmptyChildren(res.data, 'children')
      // this.formData.permission
      function checkKeyinData(list, key, arr) {
        let keys = []
        for (let i = 0; i < list.length; i++) {
          let current = list[i]
          if (current.key === key) {
            keys = arr
            break
          } else {
            arr.push(key)
            if (current.children && current.children.length > 0) {
              checkKeyinData(current.children, key, arr)
            }
          }
        }
        return keys
      }
      data.forEach(item => {
        let arr = []
        let result = checkKeyinData(this.permissionTree, item, arr)
        let mobresult = checkKeyinData(this.mobileTree, item, arr)
        parentKeys = parentKeys.concat(result)
        parentKeys = parentKeys.concat(mobresult)
      })
      return parentKeys
    },
    // 根添加
    async addRootOrganization(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationAddRootPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('添加成功')
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 根编辑
    async modifyOrganization(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.formOperate = 'detail'
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 登录
    gotoLogin() {
      if (this.infoData.login_token) {
        // 注意sessionStorage共享的问题 在新标签或窗口打开一个页面时会复制顶级浏览会话的上下文作为新会话的上下文 https://developer.mozilla.org/zh-CN/docs/Web/API/Window/sessionStorage
        // <a target="_blank"></a> 创建新 Tab 的初始缓存为空
        // window.open 创建新 Tab 会基于原页面的 sessionStorage 拷贝一份
        // https://github.com/lmk123/blog/issues/66
        // window.open(location.origin + '/#/login?token=' + this.infoData.login_token, "_blank")
        let aEl = document.createElement('a')
        aEl.href = location.origin + '/#/login?token=' + this.infoData.login_token
        aEl.target = '_blank'
        aEl.click()
        aEl = null
      } else {
        this.$message.error('无法获取token!')
      }
    },
    /**
     * 初始化字典
     */
    async initDic() {
      var list = await this.getChannelTreeList()
      if (Array.isArray(list) && list.length > 0) {
        this.channelTreeList = deepClone(list)
      }
    },
    /**
    * 获取树形字典列表
    */
    getChannelTreeList() {
      return new Promise((resolve) => {
        this.$apis.apiBackgroundAdminChannelTreeListPost().then(res => {
          if (Reflect.has(res, "code") && res.code === 0) {
            var data = res.data || {}
            setRemoveEmptyLevel(data.results, "children_list")
            resolve(data.results)
          }
          resolve([])
        })
          .catch(error => {
            console.log("error", error);
            resolve([])
          })
      })
    },
    /**
     * 获取父节点Id
     */
    getParentsById(list, id) {
      for (let i in list) {
        if (list[i].id === id) {
          // 查询到就返回该数组对象的value
          return [list[i].id];
        }
        if (list[i].children_list) {
          let node = this.getParentsById(list[i].children_list, id);
          if (node !== undefined) {
            // 查询到把父节把父节点加到数组前面
            node.unshift(list[i].id);
            return node;
          }
        }
      }
    },
    // 显示抽屉
    showDrawer(type) {
      if (this.versionData.tollVersion === -1) {
        this.selectThisVersion(null, false)
      }
      this.drawerType = type
      this.drawerShow = true
    },
    // 获取版本列表
    async getVersionList() {
      await this.$apis.apiBackgroundAdminBackgroundTollVersionListPost({
        page: 1,
        page_size: 9999
      }).then(res => {
        if (res.code === 0) {
          this.versionList = deepClone(res.data.results) || []
          let arr = this.versionList.filter(item => item.id === this.infoData.toll_version)
          this.$store.dispatch('permission/setVersionPermissionData', arr[0])
          this.defaultVersionData = deepClone(arr[0] || [])
          console.log('this.defaultVersionData', this.defaultVersionData)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 点击选择版本
    selectThisVersion(data, isClick) {
      this.formData.tollVersion = data ? data.id : -1
      this.formData.permission = data ? data.permission : (isClick ? [] : this.formData.permission)
      this.formData.appPermission = data ? data.app_permission : (isClick ? [] : this.formData.appPermission)
      this.formData.merchantAppPermission = data ? data.merchant_app_permission : (isClick ? [] : this.formData.merchantAppPermission)
      let obj = {
        tollVersion: data ? data.id : -1,
        permission: data ? data.permission : (isClick ? [] : this.formData.permission),
        app_permission: data ? data.app_permission : (isClick ? [] : this.formData.appPermission),
        merchant_app_permission: data ? data.merchant_app_permission : (isClick ? [] : this.formData.merchantAppPermission)
      }
      this.versionData = deepClone(obj)
      this.defaultVersionData = deepClone(obj)
      this.$store.dispatch('permission/setVersionPermissionData', obj)
      this.$store.dispatch('permission/setPermissionData', this.versionData)
    },
    refreshPermission(data) {
      console.log('保存后的data', data)
      let arr = this.removeDuplicates(data)
      if (this.drawerType === 'merchant') {
        this.versionData.permission = deepClone(arr)
        this.formData.permission = deepClone(arr)
      } else {
        this.versionData.merchant_app_permission = deepClone(arr)
        this.formData.merchantAppPermission = deepClone(arr)
      }
      this.$store.dispatch('permission/setPermissionData', this.versionData)
    },
    // 返回数组差异
    getDifference(arr1, arr2) {
      const diff1 = arr1.filter(item => !arr2.includes(item))
      const diff2 = arr2.filter(item => !arr1.includes(item))
      return diff1.concat(diff2)
    },
    // 数组去重
    removeDuplicates(arr) {
      return [...new Set(arr)]
    },
    // 同步到下级
    openMessageBox() {
      this.$confirm('此操作将当前组织地址同步到所有未填写地址的下级, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.synchronizeToSubordinates()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消同步到下级'
        })
      })
    },
    synchronizeToSubordinates() {
      this.$apis.apiBackgroundAdminOrganizationSynchronizationOrgDistrictPost({
        organization_id: this.formData.id,
        district: JSON.stringify(this.formData.district)
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('同步成功')
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.super-add-organization {
  position: relative;
  .add-title {
    font-size: 18px;
    text-align: center;
    font-family: 600;
  }
  .min-title-h {
    height: 28px;
    line-height: 28px;
  }
  .inline-label {
    display: inline-block;
  }
  .item-box {
    // display: flex;
    padding: 10px 0 0 !important;
    .item-b-l {
      // display: flex;
      // justify-content: center;
      // align-items: center;
      float: left;
      width: 56px;
      height: 56px;
      line-height: 56px;
      text-align: center;
      vertical-align: middle;
      background-color: #ff9b45;
      border-radius: 8px;
      font-size: 30px;
      letter-spacing: 2px;
      color: #ffffff;
    }
    .item-b-r {
      margin-left: 76px;
    }
    .item-text-box {
      display: flex;
      padding: 5px 0;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 24px;
      letter-spacing: 1px;
      color: #23282d;
      .item-label {
        opacity: 0.7;
      }
      .item-text {
        flex: 1;
      }
    }
  }
  .organization-form-wrapper {
    width: 100%;
    max-width: 800px;
    .block-label {
      width: 100%;
      .el-form-item__label {
        display: block;
        text-align: left;
        line-height: 1.5;
        float: none;
      }
    }
    .form-line {
      width: 100%;
      height: 1px;
      background-color: #e0e6eb;
    }
    .block-center {
      text-align: center;
    }
    .item-form-text {
      padding: 0 15px;
      color: #23282d;
      font-weight: bold;
      min-height: 32px;
      border: 1px solid #e0e6eb;
      opacity: 0.7;
      background-color: #fff;
    }
  }
  .form-footer {
    text-align: center;
    .el-button {
      width: 120px;
    }
  }
  &.is-fixed-footer {
    // padding-bottom: 30px;
    .form-footer {
      margin-top: 30px;
      // position: fixed;
      // padding: 10px 20px;
      // left: 263px;
      // right: 40px;
      // bottom: 0;
      // background-color: #fff;
      // box-shadow: -4px 0px 6px 0px rgba(214, 214, 214, 0.6);
      z-index: 99;
    }
  }
  .fixed-login-box {
    position: relative;
  }
  .fixed-login {
    position: absolute;
    top: -28px;
    right: 0;
    .el-button--mini {
      padding: 4px 15px;
    }
  }
  .el-radio__input.is-checked .el-radio__inner {
    border-color: #ff9b45;
    background: #ff9b45;
  }
  .el-radio__input.is-checked + .el-radio__label {
    color: #ff9b45;
  }
  .is-click  {
    color: #FF9B45;
    background-color: #FFF5EC;
    border: 1px solid #FFE3C9;
  }
}
</style>
