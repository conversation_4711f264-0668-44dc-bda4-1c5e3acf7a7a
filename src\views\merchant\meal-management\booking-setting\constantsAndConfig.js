import { MEAL_TYPES } from '@/utils/constants'
import * as dayjs from 'dayjs'

const payMethods = [
  { label: '全部', value: '' },
  { label: '线上', value: 'online' },
  { label: '线下', value: 'offline' }
]

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const searchFormSetting = {
  date_type: {
    type: 'select',
    value: 'create_time',
    maxWidth: '100px',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '预约时间',
        value: 'reservation_date'
      },
      {
        label: '用餐时间',
        value: 'dining_time'
      }
    ]
  },
  select_time: {
    clearable: false,
    type: 'daterange',
    value: recentSevenDay,
    fixedWidth: true
  },
  user_group_id: {
    type: 'select',
    label: '分组',
    value: null,
    placeholder: '请选择分组',
    dataList: [],
    listNameKey: 'group_name',
    listValueKey: 'id',
    multiple: false,
    collapseTags: true,
    fixedWidth: true
  },
  // organization_id: {
  //   type: 'organizationSelect',
  //   value: '',
  //   label: '组织',
  //   listNameKey: 'name',
  //   listValueKey: 'id',
  //   dataList: [],
  //   multiple: false,
  //   checkStrictly: true,
  //   isExpandedSelect: true
  // },
  // meal_type: {
  //   type: 'select',
  //   value: '',
  //   label: '餐段',
  //   dataList: MEAL_TYPES
  // },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES,
    fixedWidth: true
  },
  take_meal_status: {
    type: 'select',
    value: '',
    label: '取餐状态',
    fixedWidth: true,
    dataList: [
      { value: 'take_out', label: '已取餐' },
      { value: 'no_take', label: '未取餐' },
      { value: 'cancel', label: '已取消' },
      { value: 'time_out', label: '已过期' }
    ]
  },
  take_meal_type: {
    type: 'select',
    value: '',
    label: '取餐方式',
    fixedWidth: true,
    dataList: [
      { value: 'on_scene', label: '堂食' },
      { value: 'waimai', label: '外卖' },
      { value: 'bale', label: '堂食自提' },
      { value: 'cupboard', label: '取餐柜' }
    ]
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名',
    fixedWidth: true
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号',
    fixedWidth: true
  },
  consume: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    multiple: true,
    checkStrictly: true,
    collapseTags: true,
    fixedWidth: true
  },
  consume_type: {
    type: 'select',
    value: null,
    label: '支付方式',
    dataList: payMethods,
    fixedWidth: true
  },
  unified_out_trade_no: {
    type: 'input',
    value: '',
    label: '总单号',
    placeholder: '请输入总单号',
    fixedWidth: true
  },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入订单号',
    fixedWidth: true
  },
  payer_department_group_ids: {
    type: 'departmentSelect',
    multiple: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    checkStrictly: true,
    dataList: [],
    limit: 1,
    level: 1,
    fixedWidth: true
    // normalizer: this.departmentNode
  },
  food_name: {
    type: 'input',
    value: '',
    // multiple: true,
    // collapseTags: true,
    label: '菜品',
    placeholder: '请输入菜品',
    fixedWidth: true
    // dataList: []
  },
  verification_type: {
    type: 'select',
    label: '核销类型',
    value: '',
    placeholder: '请选择核销类型',
    fixedWidth: true,
    dataList: [
      {
        label: '在线核销',
        value: 'online'
      },
      {
        label: '离线核销',
        value: 'offline'
      }
    ]
  },
  review_status: {
    type: 'select',
    label: '审核状态',
    value: '',
    placeholder: '请选择审核状态',
    fixedWidth: true,
    clearable: true,
    dataList: [
      {
        label: '待审核',
        value: 'wait'
      },
      {
        label: '已同意',
        value: 'success'
      },
      {
        label: '已拒绝',
        value: 'reject'
      },
      {
        label: '已撤回',
        value: 'cancel'
      }
    ]
  }
}

export const columns = [
  { label: '总单号', column: 'unified_trade_no', width: '160px' },
  { label: '订单号', column: 'trade_no', width: '160px' },
  { label: '取餐号', column: 'pickup_number' },
  { label: '支付时间', column: 'pay_time', sortable: true, width: '160px' },
  { label: '分组', column: 'payer_group_name' },
  { label: '姓名', column: 'name' },
  { label: '人员编号', column: 'person_no' },
  { label: '手机号', column: 'phone', width: '110px' },
  { label: '部门', column: 'payer_department_group_name', width: '100px' },
  { label: '创建时间', column: 'create_time', width: '160px' },
  { label: '预约时间', column: 'reservation_date', width: '160px' },
  { label: '用餐时间', column: 'dining_time', width: '160px' },
  { label: '预约餐段', column: 'meal_type_alias' },
  { label: '取餐方式', column: 'take_meal_type_alias' },
  { label: '扣费方式', column: 'consume_type' },
  { label: '菜品', column: 'create_date6' },
  { label: '份数', column: 'count' },
  { label: '订单金额', column: 'origin_fee' },
  { label: '审核状态', column: 'review_status_alias' },
  { label: '取餐状态', column: 'take_meal_status_alias' },
  { label: '预约消费点', column: 'consumption_name', width: '120px' },
  { label: '核销类型', column: 'verification_type_alias' }
]

export const menuManagerFormSetting = {
  name: {
    type: 'input',
    value: '',
    label: '菜谱名称',
    placeholder: '请输入菜谱名称'
  },
  account_name: {
    type: 'input',
    value: '',
    label: '创建人',
    placeholder: '请输入'
  },
  apply_groups: {
    type: 'select',
    value: [],
    label: '适用人群',
    placeholder: '请选择',
    listNameKey: 'name',
    listValueKey: 'id',
    multiple: true,
    collapseTags: true,
    dataList: []
  },
  use_user_groups: {
    type: 'select',
    label: '可见范围',
    value: null,
    placeholder: '请选择',
    dataList: [],
    listNameKey: 'group_name',
    listValueKey: 'id',
    multiple: false,
    collapseTags: true
  },
  organization: {
    type: 'treeselect',
    label: '创建组织',
    multiple: true,
    flat: false,
    value: null,
    placeholder: '请选择创建组织',
    dataList: [],
    listValueKey: 'id',
    limit: 1,
    level: 1,
    normalizer: node => ({
      id: node.id,
      label: node.name,
      children: node.children_list
    })
  },
  use_organization: {
    type: 'treeselect',
    label: '适用组织',
    multiple: true,
    flat: false,
    value: [],
    placeholder: '请选择',
    dataList: [],
    listValueKey: 'id',
    limit: 1,
    level: 1,
    normalizer: node => ({
      id: node.id,
      label: node.name,
      children: node.children_list
    })
  }
}

export const deviceList = [
  { label: '全部', value: '' },
  { label: 'H5', value: 'H5' },
  { label: '小程序', value: 'MAPP' },
  { label: 'K1点餐机', value: 'K1DCJ' },
  { label: '双屏点餐机', value: 'SPDCJ' },
  // { label: '挂式消费机', value: 'GSXFJ' },
  { label: '结算台', value: 'JST' },
  // { label: '自助秤', value: 'ZZC' },
  // { label: '取餐柜', value: 'QCJ' },
  { label: 'P2手持消费机', value: 'P2XFJ' },
  // { label: '闸机', value: 'ZJ' },
  { label: 'D2消费机', value: 'D2XFJ' },
  { label: '智能秤', value: 'ZNC' }
  // { label: '叫号屏', value: 'JHP' }
]

export const dateTypes = [
  {
    label: '创建时间',
    value: 'create_time'
  },
  {
    label: '预约时间',
    value: 'reservation_date'
  },
  {
    label: '用餐时间',
    value: 'dining_time'
  }
]
export const CUPBOARDORDER = {
  date_type: {
    type: 'select',
    value: 'create_time',
    label: '',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '预约时间',
        value: 'reservation_date'
      },
      {
        label: '用餐时间',
        value: 'dining_time'
      }
    ]
  },
  select_time: {
    clearable: false,
    type: 'daterange',
    value: recentSevenDay
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '总订单号/订单号',
    placeholder: '请输入总订单号或者订单号',
    labelWidth: '150px'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: ''
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: ''
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: ''
  },
  // reservation_take_meal_type: {
  //   type: 'select',
  //   value: '',
  //   label: '取餐方式',
  //   dataList: [
  //     {
  //       label: '全部',
  //       value: ''
  //     },
  //     {
  //       label: '堂食',
  //       value: 'on_scene'
  //     },
  //     {
  //       label: '打包',
  //       value: 'takeaway'
  //     }
  //   ]
  // },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  meal_type: {
    type: 'select',
    value: '',
    label: '预约餐段',
    listNameKey: 'label',
    listValueKey: 'value',
    multiple: false,
    collapseTags: true,
    filterable: true,
    dataList: [{ value: '', label: '全部' }, ...MEAL_TYPES]
  },
  // meal_type_list: {
  //   type: 'select',
  //   label: '餐段',
  //   value: [],
  //   multiple: true,
  //   placeholder: '请选择',
  //   dataList: MEAL_TYPES
  // },
  is_save: {
    type: 'select',
    value: '2',
    label: '是否已存餐',
    dataList: [
      {
        label: '全部',
        value: '2'
      },
      {
        label: '未存餐',
        value: '0'
      },
      {
        label: '已存餐',
        value: '1'
      }
    ]
  },
  organization_id: {
    type: 'organizationSelect',
    value: '',
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: false,
    collapseTags: true,
    clearable: true
  },
  take_meal_status_list: {
    type: 'select',
    value: [],
    label: '取餐状态',
    listNameKey: 'label',
    listValueKey: 'value',
    multiple: true,
    collapseTags: true,
    filterable: true,
    dataList: [
      { value: 'take_out', label: '已取餐' },
      { value: 'no_take', label: '未取餐' },
      // { value: 'cancel', label: '已取消' },
      { value: 'time_out', label: '已过期' }
    ]
  },
  device_ids: {
    type: 'select',
    value: [],
    label: '餐柜名称',
    listNameKey: 'device_name',
    listValueKey: 'device_id',
    multiple: true,
    collapseTags: true,
    filterable: true,
    dataList: []
  }
}
