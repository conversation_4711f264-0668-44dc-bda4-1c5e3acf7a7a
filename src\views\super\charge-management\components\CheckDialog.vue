<template>
  <!-- <dialog-message
    :show.sync="visible"
    :title="dialogTitle"
    customClass="ps-dialog"
    :width="width"
    :showFooter="false"
  >
    <div v-if="title === 'voucherUrl'">
      <img width="460" :src="data" />
    </div>

    <div v-if="title === 'invoiceStatus'">
      <div v-if="invoiceInformation" id="dialog-content-invoice" class="dialog-content-invoice">
        <h4 class="content-detail">{{ invoiceInformation.invoice_type === 1 ? '电子普通发票' : '增值税专用发票'}}</h4>
        <div class="content-detail company_address">
          <h4>公司信息：</h4>
        </div>
        <div v-if="invoiceInformation.invoice_type === 1">
          <div class="content-detail">
            <span class="four-length">发票抬头</span>
            <span class="text-black">{{ invoiceInformation.title }}</span>
          </div>
          <div class="content-detail">
            <span class="two-length">税号</span>
            <span class="text-black">{{ invoiceInformation.tax_no }}</span>
          </div>
        </div>
        <div v-if="invoiceInformation.invoice_type === 2">
          <div class="content-detail">
            <span class="four-length">客户名称</span>
            <span class="text-black">{{ invoiceInformation.customer_name }}</span>
          </div>
          <div class="content-detail">
            <span class="two-length">税号</span>
            <span class="text-black">{{ invoiceInformation.tax_no }}</span>
          </div>
          <div class="content-detail">
            <span class="two-length">地址</span>
            <span class="text-black">{{ invoiceInformation.company_address }}</span>
          </div>
          <div class="content-detail">
            <span class="two-length">电话</span>
            <span class="text-black">{{ invoiceInformation.company_phone }}</span>
          </div>
          <div class="content-detail">
            <span class="three-length">开户行</span>
            <span class="text-black">{{ invoiceInformation.bank_name }}</span>
          </div>
          <div class="content-detail">
            <span class="two-length">账户</span>
            <span class="text-black">{{ invoiceInformation.account }}</span>
          </div>
        </div>
        <h4 class="content-detail">接收信息</h4>
        <div class="content-detail">
          <span class="four-length">{{ invoiceInformation.invoice_type === 1 ? '邮箱地址' : '邮寄地址' }}</span>
          <span class="text-black">{{ invoiceInformation.invoice_type === 1 ? invoiceInformation.email : invoiceInformation.mailing_address}}</span>
        </div>
      </div>
      <div
        class="tool-copy content-detail"
        data-clipboard-target="#dialog-content-invoice"
        id="copy-btn"
        @click="copyFun"
      >
        一键复制
      </div>
    </div>
  </dialog-message> -->

  <div class="ps-el-drawer">
    <el-drawer
      :title="dialogTitle"
      :visible="visible"
      :show-close="false"
      size="40%">
      <div class="p-20">
        <div v-if="title === 'voucherUrl'">
          <img width="460" :src="data" />
        </div>

        <div v-if="title === 'invoiceStatus'">
          <div v-if="invoiceInformation" id="dialog-content-invoice" class="dialog-content-invoice">
            <h4 class="content-detail">{{ invoiceInformation.invoice_type === 1 ? '电子普通发票' : '增值税专用发票'}}</h4>
            <div class="content-detail company_address">
              <h4>公司信息：</h4>
            </div>
            <div v-if="invoiceInformation.invoice_type === 1">
              <div class="content-detail">
                <span class="four-length">发票抬头</span>
                <span class="text-black">{{ invoiceInformation.title }}</span>
              </div>
              <div class="content-detail">
                <span class="two-length">税号</span>
                <span class="text-black">{{ invoiceInformation.tax_no }}</span>
              </div>
            </div>
            <div v-if="invoiceInformation.invoice_type === 2">
              <div class="content-detail">
                <span class="four-length">客户名称</span>
                <span class="text-black">{{ invoiceInformation.customer_name }}</span>
              </div>
              <div class="content-detail">
                <span class="two-length">税号</span>
                <span class="text-black">{{ invoiceInformation.tax_no }}</span>
              </div>
              <div class="content-detail">
                <span class="two-length">地址</span>
                <span class="text-black">{{ invoiceInformation.company_address }}</span>
              </div>
              <div class="content-detail">
                <span class="two-length">电话</span>
                <span class="text-black">{{ invoiceInformation.company_phone }}</span>
              </div>
              <div class="content-detail">
                <span class="three-length">开户行</span>
                <span class="text-black">{{ invoiceInformation.bank_name }}</span>
              </div>
              <div class="content-detail">
                <span class="two-length">账户</span>
                <span class="text-black">{{ invoiceInformation.account }}</span>
              </div>
            </div>
            <h4 class="content-detail">接收信息</h4>
            <div class="content-detail">
              <span class="four-length">{{ invoiceInformation.invoice_type === 1 ? '邮箱地址' : '邮寄地址' }}</span>
              <span class="text-black">{{ invoiceInformation.invoice_type === 1 ? invoiceInformation.email : invoiceInformation.mailing_address}}</span>
            </div>
          </div>
          <div
            class="tool-copy content-detail"
            data-clipboard-target="#dialog-content-invoice"
            id="copy-btn"
            @click="copyFun"
          >
            一键复制
          </div>
        </div>
        <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
          <div class="m-r-30">
            <el-button size="small"  class="w-100" @click="visible = false">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="visible = false">保存</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { to } from '@/utils'
import Clipboard from 'clipboard'
export default {
  name: 'CheckDialog',
  props: {
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: ''
    },
    data: {
      type: [String, Number],
      default: ''
    },
    isShow: Boolean
  },
  computed: {
    dialogTitle() {
      let name = ''
      switch (this.title) {
        case 'voucherUrl':
          name = '转账凭证'
          break
        case 'invoiceStatus':
          name = '发票申请'
          break
      }
      return name
    },
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {
    isShow(newVal) {
      if (newVal && this.title === 'invoiceStatus') {
        this.getInvoiceInfo(this.data)
      }
    }
  },
  data() {
    return {
      invoiceInformation: {}
    }
  },
  methods: {
    // 一键复制
    copyFun() {
      let clipboard = new Clipboard('#copy-btn') // 获取点击按钮的元素
      /* 注意此事件监听是异步的   */
      clipboard.on('success', e => {
        console.info('Text:', e.text)
        e.clearSelection()
        // 释放内存
        clipboard.destroy()
        return this.$message.success('复制成功')
      })
      // 复制失败
      clipboard.on('error', e => {
        console.error('Action:', e.action)
        console.error('Trigger:', e.trigger)
        // 释放内存
        clipboard.destroy()
        return this.$message.error('该浏览器不支持复制')
      })
    },
    async getInvoiceInfo(id) {
      console.log('获取发票')
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminBackgroundTollOrderGetInvoiceInfoPost({
          id
        })
      )
      if (err) {
        return this.$message.error(err.msg)
      }
      if (res) {
        this.invoiceInformation = res.data
        console.log('invoiceInformation', this.invoiceInformation)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.text-black {
  color: black;
}
// 发表信息
.dialog-content-invoice {
    h4 {
      color: black;
    }
    .company_address {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 19px;
      h4 {
        margin-right: 16px;
      }
    }
    .content-detail {
      margin: 20px 0 20px 0;
    }
    .four-length {
      margin-right: 30px;
    }
    .two-length {
      margin-right: 58px;
    }
    .three-length {
      margin-right: 44px;
    }
  }
  // 一键复制
  .tool-copy {
    display: inline-block;
    color: #39a5ff;
    font-weight: bold;
    cursor: pointer;
    padding-bottom: 20px;
  }

</style>
