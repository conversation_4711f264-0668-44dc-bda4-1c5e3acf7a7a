<template>
  <el-drawer title="选择核验人员" class="ps-el-drawer" :visible.sync="dialogVisible" direction="rtl" size="50%"
    :before-close="handleClose" :destroy-on-close="true">
    <div class="person-add-container">
      <!-- 搜索区域 -->
      <div class="search-area">
        <div class="search-item">
          <span class="label">角色名称</span>
          <el-select v-model="queryParams.roleId" placeholder="请选择" clearable v-loading="roleLoading" @change="handleSearch">
            <el-option v-for="item in roleOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span class="label">账号</span>
          <el-input v-model="queryParams.username" placeholder="请输入账号" clearable @clear="handleSearch" @input="handleSearch"></el-input>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-content">
        <el-table v-loading="isLoading" :data="tableData" ref="multipleTable" style="width: 100%" stripe
          header-row-class-name="ps-table-header-row" @select="handleSelection"
          @select-all="handleAllSelection">
          <el-table-column type="selection" width="55">
          </el-table-column>
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
          </table-column>
        </el-table>
        <!-- table end -->
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="queryParams.pageNo"
            :page-sizes="[10, 20, 50, 100, 500]" :page-size="queryParams.pageSize" layout="total, prev, pager, next, sizes, jumper"
            :total="total" background class="ps-text" popper-class="ps-popper-select"></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
      <!-- 底部按钮区域 -->
      <div class="ps-el-drawer-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { deepClone } from '@/utils/index'

export default {
  name: 'PersonAddDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 已选择的人员列表
    selectedPersons: {
      type: Array,
      default: () => []
    },
    // 类型：entry-入库核验，receipt-收货核验
    type: {
      type: String,
      default: 'entry'
    }
  },
  data() {
    return {
      dialogVisible: false,
      isLoading: false,
      roleLoading: false,
      tableSetting: [
        { label: '角色名称', key: 'role_alias' },
        { label: '账号名', key: 'member_name' },
        { label: '账号', key: 'username' }
      ],
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        roleId: '',
        username: ''
      },
      roleOptions: [],
      tableData: [],
      total: 0,
      // 传送过来的的已经选中的人员
      multipleSelection: [],
      // 标记是否正在恢复选择状态
      isRestoring: false
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          // 初始化已选人员的ID列表
          if (this.selectedPersons) {
            this.multipleSelection = [...this.selectedPersons]
          }
          this.getList()
        } else {
          this.clearData()
        }
      },
      immediate: true
    }
  },
  created() {
    this.getRoleList()
  },
  methods: {
    // 搜索
    handleSearch() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 获取账号列表
    async getList() {
      this.isLoading = true
      try {
        let params = {
          status: 1,
          page: this.queryParams.pageNo,
          page_size: this.queryParams.pageSize
        }
        if (this.queryParams.roleId) {
          params.role_id = this.queryParams.roleId
        }
        if (this.queryParams.username) {
          params.username = this.queryParams.username
        }
        const [err, res] = await this.$to(this.$apis.apiBackgroundOrganizationAccountListPost(params))

        this.isLoading = false
        if (err) {
          this.$message.error(err.message || '获取账号列表失败')
          return
        }
        if (res && res.code === 0) {
          const data = res.data || {}
          const results = data.results || []
          this.tableData = deepClone(results) || []
          this.total = data.count || 0

          // 设置已选中的行
          this.$nextTick(() => {
            this.restoreSelection()
          })
        }
      } catch (error) {
        console.error('获取账号列表失败:', error)
        this.$message.error('获取账号列表失败')
      } finally {
        this.isLoading = false
      }
    },
    // 获取角色列表
    async getRoleList() {
      this.roleLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrganizationRoleListPost({
        page: 1,
        page_size: 99999
      }))
      this.roleLoading = false
      if (err) {
        this.$message.error(err.message || '获取角色列表失败')
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.roleOptions = deepClone(results) || []
      }
    },
    clearData() {
      // 关闭弹窗时清空选择状态
      this.multipleSelection = []
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        roleId: '',
        username: ''
      }
    },
    // 重置表格选中状态
    restoreSelection() {
      if (!this.$refs.multipleTable) return

      // 设置恢复标志，防止handleSelectionChange重新计算
      this.isRestoring = true

      // 先清除所有选择
      this.$refs.multipleTable.clearSelection()

      // 根据selectedIds重新设置选择状态
      let seletcIds = this.multipleSelection.map(row => row.id)
      this.tableData.forEach(row => {
        if (seletcIds.includes(row.id)) {
          this.$refs.multipleTable.toggleRowSelection(row, true)
        }
      })

      // 恢复完成后重置标志
      this.$nextTick(() => {
        this.isRestoring = false
      })
    },
    handleSelection(val, row) {
      let index = this.multipleSelection.findIndex(item => item.id === row.id)
      if (index === -1) {
        this.multipleSelection.push(row)
      } else {
        this.multipleSelection.splice(index, 1)
      }
    },
    // 全选
    handleAllSelection(selection) {
      console.log("handleAllSelection", selection)
      let flag = selection && selection.length > 0
      if (flag) { // 全选
        this.tableData.forEach(user => {
          let index = this.multipleSelection.findIndex(item => item.id === user.id)
          if (index === -1) { // 把之前没有的加上
            this.multipleSelection.push(user)
          }
        })
      } else { // 全不选
        this.tableData.forEach(user => {
          let index = this.multipleSelection.findIndex(item => item.id === user.id)
          if (index !== -1) { // 把之前有的去掉
            this.multipleSelection.splice(index, 1)
          }
        })
      }
    },
    // 每页条数变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.queryParams.pageNo = val
      this.getList()
    },

    // 取消
    handleCancel() {
      this.clearData()
      this.$emit('update:visible', false)
      this.$emit('cancel')
    },

    // 确定
    handleConfirm() {
      let chooseList = deepClone(this.multipleSelection)
      if (chooseList.length === 0) {
        this.$message.warning('请先选择账号')
        return
      }
      console.log("chooseList", chooseList)
      this.$emit('confirm', chooseList, this.type)
      this.$emit('update:visible', false)
    },

    // 关闭前的回调
    handleClose(done) {
      this.$emit('update:visible', false)
      done()
    }
  }
}
</script>

<style lang="scss" scoped>
.person-add-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-area {
  display: flex;
  margin-bottom: 20px;

  .search-item {
    display: flex;
    align-items: center;
    margin-right: 20px;

    .label {
      margin-right: 10px;
      white-space: nowrap;
    }

    .el-select,
    .el-input {
      width: 200px;
    }
  }
}

</style>
