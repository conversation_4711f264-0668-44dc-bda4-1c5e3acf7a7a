<template>
  <div class="info-show p-20">
    <el-form ref="infoShowFormRef" :model="infoShowForm" label-position="top">
      <el-form-item prop="level">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">餐饮服务食品安全等级公示</div>
            <el-button type="text" class="ps-origin-text" @click="showDrawer('level', infoShowForm.level)" v-permission="['background_fund_supervision.publicity_info.modify_security_level']">修改</el-button>
          </div>
        </template>
        <div class="level-show p-l-20">
          <div class="level-show-left m-r-40">
            <svg-icon v-show="infoShowForm.level" :icon-class="infoShowForm.level" style="width: 128px; height: 128px;" />
          </div>
          <div class="level-show-right">
            <div class="level-show-right-tips m-r-20">
              <svg-icon :icon-class="'excellent'" class="m-b-10 m-r-10" style="width: 36px; height: 36px;" />
              <span>A级：优秀</span>
            </div>
            <div class="level-show-right-tips m-r-20">
              <svg-icon :icon-class="'good'" class="m-b-10 m-r-10" style="width: 36px; height: 36px;" />
              <span>B级：良好</span>
            </div>
            <div class="level-show-right-tips m-r-20">
              <svg-icon :icon-class="'average'" class="m-b-10 m-r-10" style="width: 36px; height: 36px;" />
              <span>C级：一般</span>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="type">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">食堂类型</div>
            <el-button type="text" class="ps-origin-text" @click="showDrawer('type', infoShowForm.type)" v-permission="['background_fund_supervision.publicity_info.modify_canteen_type']">修改</el-button>
          </div>
        </template>
        <div class="">
          {{ computedType(infoShowForm.type) }}
        </div>
      </el-form-item>
      <el-form-item prop="list">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">资质公示</div>
            <el-button type="text" class="ps-origin-text" @click="showDrawer('list', infoShowForm.list)" v-permission="['background_fund_supervision.publicity_info.modify_qualification_publicity']">编辑</el-button>
          </div>
        </template>
        <div class="qualification-publicity" v-if="infoShowForm.list.length">
          <div v-for="(item, index) in infoShowForm.list" :key="index" class="qualification-publicity-item">
            <el-image
              style="width: 200px; height: 100px"
              :src="item.url"
              :preview-src-list="[item.url]"
              fit="fill" />
            <span>{{ item.label }}</span>
          </div>
        </div>
        <div v-else>
          <el-empty description="暂无资质公示" :image-size="100"></el-empty>
        </div>
      </el-form-item>
      <el-form-item prop="tableData">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">食品卫生安全管理</div>
            <el-button type="text" class="ps-origin-text" @click="showInfoDrawer('add')" v-permission="['background_fund_supervision.publicity_info.modify_canteen_admin']">添加</el-button>
          </div>
        </template>
        <el-table
          :data="infoShowForm.tableData"
          v-loading="isLoading"
          stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="(itemIn, indexIn) in tableSetting" :key="indexIn" :col="itemIn">
            <template #faceUrl="{ row }">
              <el-image
              style="width: 100px; height: 100px"
              :src="row.face_url"
              :preview-src-list="[row.face_url]"
              fit="fill" />
            </template>
            <template #operation="{ row }">
              <el-button type="text" class="ps-origin-text" @click="showInfoDrawer('edit', row)" v-permission="['background_fund_supervision.publicity_info.modify_canteen_admin']">编辑</el-button>
              <el-button type="text" class="ps-origin-text" @click="deleteInfo(row)" v-permission="['background_fund_supervision.publicity_info.delete_canteen_admin']">删除</el-button>
            </template>
          </table-column>
        </el-table>
      </el-form-item>
    </el-form>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'修改食品安全等级'"
        :visible="foodSafetyDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="foodSafetyFormRef" :model="foodSafetyForm" label-width="80px" label-position="right">
            <el-form-item :label="'安全等级'" prop="level" :rules="[{ required: true, message: '请选择安全等级', trigger: ['change', 'blur'] }]">
              <el-select v-model="foodSafetyForm.level" placeholder="请选择">
                <el-option
                  v-for="(item, index) in levelList"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('level')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('level')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'修改食堂类型'"
        :visible="canteenTypeDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="canteenTypeFormRef" :model="canteenTypeForm" label-width="80px" label-position="right">
            <el-form-item :label="'食堂类型'" prop="type" :rules="[{ required: true, message: '请选择食堂类型', trigger: ['change', 'blur'] }]">
              <el-select v-model="canteenTypeForm.type" placeholder="请选择">
                <el-option
                  v-for="(item, index) in canteenTypeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('type')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('type')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'修改资质信息'"
        :visible="certificationDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="certificationFormRef" :model="certificationForm" label-width="80px" label-position="right">
            <el-form-item :label="'资质名称'" prop="name" :rules="[{ required: true, message: '请输入资质名称', trigger: ['blur'] }]">
              <el-input v-model="certificationForm.name" class="w-300" placeholder="请输入岗位信息，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="上传图片" prop="img" :rules="[{ required: true, message: '请上传资质图片', trigger: ['blur'] }]">
              <div class="certification-info-show-tips">
                图片最大不超过2MB，仅支持jpg,png格式
              </div>
              <el-upload v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                :action="serverUrl" :file-list="fileLists" :on-success="uploadSuccessForList"
                :before-upload="beforeImgUpload" :limit="1" :multiple="false" :show-file-list="false"
                :headers="headersOpts" accept=".jpeg,.jpg,.png,.bmp">
                <img v-if="certificationForm.img" :src="certificationForm.img" class="certification-info-show-img">
                <div v-else style="width: 100px; height: 100px; border: 1px dashed #C0C4CC; text-align: center; line-height: 100px;">
                  <i class="el-icon-plus"></i>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" class="w-100" @click="addCertification">添加</el-button>
            </el-form-item>
            <div class="certification-info-show">
              <el-form-item :label="'当前公示'">
                <div class="certification-info-show-tips">添加或删除均需要保存才可生效。</div>
                <div class="certification-info-show-content">
                  <div class="certification-info-show-content-item" v-for="(item, index) in certificationForm.imageList" :key="index">
                    <div class="certification-info-show-content-item-close" @click="deleteThisCertification(index)">
                      <i class="el-icon-close"></i>
                    </div>
                    <el-image
                      style="width: 200px; height: 100px"
                      :src="item.url"
                      :preview-src-list="[item.url]"
                      fit="fill" />
                    <span>{{ item.label }}</span>
                  </div>
                </div>
              </el-form-item>
            </div>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('list')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('list')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="selectType === 'add' ? '新建信息' : '编辑信息'"
        :visible="infoDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="infoFormRef" :model="infoForm" label-width="80px" label-position="right">
            <el-form-item :label="'姓名'" prop="name" :rules="[{ required: true, message: '请输入姓名', trigger: ['change', 'blur'] }]">
              <el-input v-model="infoForm.name" class="w-300 m-r-20" placeholder="请输入姓名，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item :label="'联系电话'" prop="phone" :rules="[{ required: true, message: '请输入手机号码', trigger: ['change', 'blur'] }]">
              <el-input v-model="infoForm.phone" class="w-300 m-r-20" placeholder="请输入手机号码，不超过11位" maxlength="11"></el-input>
            </el-form-item>
            <el-form-item :label="'所属岗位'" prop="post" :rules="[{ required: true, message: '请输入岗位信息', trigger: ['change', 'blur'] }]">
              <el-input v-model="infoForm.post" class="w-300 m-r-20" placeholder="请输入岗位信息，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="上传图片" prop="img" :rules="[{ required: true, message: '请上传图片', trigger: ['change', 'blur'] }]">
              <div class="certification-info-show-tips">
                图片最大不超过2MB，仅支持jpg,png格式
              </div>
              <el-upload v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                :action="serverUrl" :file-list="fileLists" :on-success="uploadSuccessForData"
                :before-upload="beforeImgUpload" :limit="1" :multiple="false" :show-file-list="false"
                :headers="headersOpts" accept=".jpeg,.jpg,.png,.bmp">
                <img v-if="infoForm.img" :src="infoForm.img" class="certification-info-show-img">
                <div v-else style="width: 100px; height: 100px; border: 1px dashed #C0C4CC; text-align: center; line-height: 100px;">
                  <i class="el-icon-plus"></i>
                </div>
              </el-upload>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('data')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('data')">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { getToken, getSuffix, deepClone } from '@/utils/index'
import { isEmptyObject } from '@/utils/type'
export default {
  data() {
    return {
      isLoading: false,
      infoShowForm: {
        level: '',
        type: '',
        list: [],
        tableData: []
      },
      tableSetting: [
        { label: '姓名', key: 'name', align: 'center' },
        { label: '联系电话', key: 'phone', align: 'center' },
        { label: '所属岗位', key: 'job_title', align: 'center' },
        { label: '照片', key: 'face_url', align: 'center', type: "slot", slotName: "faceUrl" },
        { label: '修改时间', key: 'update_time', align: 'center' },
        { label: '操作人', key: 'operator', align: 'center' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      foodSafetyDrawerShow: false,
      foodSafetyForm: {
        level: ''
      },
      levelList: [
        {
          label: 'A级：优秀',
          value: 'excellent'
        },
        {
          label: 'B级：良好',
          value: 'good'
        },
        {
          label: 'C级：一般',
          value: 'average'
        }
      ],
      canteenTypeDrawerShow: false,
      canteenTypeForm: {
        type: 'chengbao'
      },
      canteenTypeList: [
        {
          label: '承包/托管经营食堂',
          value: 'chengbao'
        },
        {
          label: '自营食堂',
          value: 'ziying'
        }
      ],
      certificationDrawerShow: false,
      certificationForm: {
        name: '',
        img: '',
        imageList: []
      },
      uploading: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      fileLists: [],
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      selectType: '',
      infoDrawerShow: false,
      selectId: '',
      infoForm: {
        name: '',
        phone: '',
        post: '',
        img: ''
      }
    }
  },
  computed: {
    computedType() {
      return d => {
        if (d === 'chengbao') {
          return '承包/托管经营食堂'
        } else {
          return '自营食堂'
        }
      }
    }
  },
  created() {
    this.getCanteenPublicity()
  },
  methods: {
    // 获取食堂公示
    getCanteenPublicity() {
      // 重置一下infoShowForm
      this.infoForm.level = 'excellent'
      this.infoForm.type = ''
      this.infoForm.list = []
      this.infoForm.tableData = []
      // 获取新的数据
      this.$apis.apiBackgroundFundSupervisionPublicityInfoGetCanteenPublicityPost().then(res => {
        if (res.code === 0) {
          if (res.data.publicity_info && !isEmptyObject(res.data.publicity_info)) {
            this.infoShowForm.level = res.data.publicity_info.security_level
            this.infoShowForm.type = res.data.publicity_info.canteen_type
            if (!isEmptyObject(res.data.publicity_info.qualification_publicity)) {
              let newArr = []
              for (let key in res.data.publicity_info.qualification_publicity) {
                let obj = {
                  label: key,
                  url: res.data.publicity_info.qualification_publicity[key]
                }
                newArr.push(obj)
              }
              this.infoShowForm.list = deepClone(newArr)
            } else {
              this.infoShowForm.list = []
            }
          }
          this.infoShowForm.tableData = res.data.person_list || []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 图片上传成功
    uploadSuccessForList(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        this.fileLists = []
        this.certificationForm.img = res.data.public_url
      } else {
        this.certificationForm.img = ''
        this.$message.error(res.msg)
      }
    },
    uploadSuccessForData(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        this.fileLists = []
        this.infoForm.img = res.data.public_url
      } else {
        this.infoForm.img = ''
        this.$message.error(res.msg)
      }
    },
    // 图片上传前检测
    beforeImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 2
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },
    showInfoDrawer(type, data) {
      this.selectType = type
      if (type === 'edit') {
        this.selectId = data.id
        this.infoForm.name = data.name
        this.infoForm.phone = data.phone
        this.infoForm.post = data.job_title
        this.infoForm.img = data.face_url
      }
      this.infoDrawerShow = true
    },
    showDrawer(openType, data) {
      switch (openType) {
        case 'level':
          this.foodSafetyForm.level = data
          this.foodSafetyDrawerShow = true
          break
        case 'type':
          this.canteenTypeForm.type = data
          this.canteenTypeDrawerShow = true
          break
        case 'list':
          this.certificationForm.imageList = deepClone(data) || []
          this.certificationDrawerShow = true
          break
      }
    },
    addCertification() {
      this.$refs.certificationFormRef.validate((valid) => {
        if (valid) {
          let obj = {
            label: this.certificationForm.name,
            url: this.certificationForm.img
          }
          this.certificationForm.imageList.push(obj)
          this.certificationForm.name = ''
          this.certificationForm.img = ''
          this.$refs.certificationFormRef.clearValidate(['name', 'img'])
          this.fileList = []
        } else {
          return this.$$message.error('请检查上传的内容是否正确')
        }
      })
    },
    // 删除某个资质
    deleteThisCertification(targetIndex) {
      let newArr = this.certificationForm.imageList.filter((item, index) => index !== targetIndex)
      this.certificationForm.imageList = deepClone(newArr)
    },
    // 抽屉的方法
    cancelHandle(type) {
      switch (type) {
        case 'level':
          this.$refs.foodSafetyFormRef.resetFields()
          this.foodSafetyDrawerShow = false
          break
        case 'type':
          this.$refs.canteenTypeFormRef.resetFields()
          this.canteenTypeDrawerShow = false
          break
        case 'list':
          this.$refs.certificationFormRef.resetFields()
          this.certificationDrawerShow = false
          break
        case 'data':
          this.$refs.infoFormRef.resetFields()
          this.infoDrawerShow = false
          break
      }
    },
    saveHandle(type) {
      switch (type) {
        case 'level':
          this.changeLevel()
          break
        case 'type':
          this.changeType()
          break
        case 'list':
          this.saveCertificationList()
          break
        case 'data':
          this.saveInfoData()
          break
      }
    },
    // 修改等级
    changeLevel() {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifySecurityLevelPost({
        security_level: this.foodSafetyForm.level
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
          this.$refs.foodSafetyFormRef.resetFields()
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.foodSafetyFormRef.resetFields()
        this.foodSafetyDrawerShow = false
        this.getCanteenPublicity()
      })
    },
    // 修改食堂类型
    changeType() {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyCanteenTypePost({
        canteen_type: this.canteenTypeForm.type
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
          this.$refs.canteenTypeFormRef.resetFields()
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.canteenTypeFormRef.resetFields()
        this.canteenTypeDrawerShow = false
        this.getCanteenPublicity()
      })
    },
    // 保存资质
    saveCertificationList() {
      if (this.certificationForm.name && this.certificationForm.img) {
        this.$confirm(`您还有正在编辑的资质，是否将其加入资质公示中`, '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          let obj = {
            label: this.certificationForm.name,
            url: this.certificationForm.img
          }
          this.certificationForm.imageList.push(obj)
          this.saveCertificationHandle()
        }).catch(action => {
          this.$refs.certificationFormRef.resetFields()
        })
      } else if ((!this.certificationForm.name && this.certificationForm.img) || (!this.certificationForm.img && this.certificationForm.name)) {
        this.$confirm(`您还有尚未编辑完的资质，是否取消编辑并保存现有的资质`, '提示', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.$refs.certificationFormRef.resetFields()
          this.saveCertificationHandle()
        }).catch(action => {
          this.$message('请完善正在编辑的资质后再保存')
        })
      } else {
        this.saveCertificationHandle()
      }
    },
    saveCertificationHandle() {
      let params = {
        qualification_publicity: {}
      }
      if (this.certificationForm.imageList.length) {
        this.certificationForm.imageList.forEach((item) => {
          params.qualification_publicity[`${item.label}`] = item.url
        })
      }
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyQualificationPublicityPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('保存成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.certificationFormRef.resetFields()
        this.certificationDrawerShow = false
        this.getCanteenPublicity()
      })
    },
    // 保存人员信息
    saveInfoData() {
      this.$refs.infoFormRef.validate((valid) => {
        if (valid) {
          let params = {
            id: this.selectType === 'add' ? undefined : this.selectId,
            phone: this.infoForm.phone,
            face_url: this.infoForm.img,
            name: this.infoForm.name,
            job_title: this.infoForm.post
          }
          if (this.selectType === 'add') {
            this.addInfoData(params)
          } else {
            this.editInfoData(params)
          }
        } else {
          this.$message.error('请检查填写的内容是否正确')
        }
      })
    },
    // 新增人员
    addInfoData(params) {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoAddCanteenAdminPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('新增成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.infoFormRef.resetFields()
        this.infoDrawerShow = false
        this.getCanteenPublicity()
      })
    },
    // 编辑人员
    editInfoData(params) {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyCanteenAdminPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.infoFormRef.resetFields()
        this.infoDrawerShow = false
        this.getCanteenPublicity()
      })
    },
    // 删除人员
    deleteInfo(data) {
      this.$confirm(`确定要删除的公示信息？删除后不可恢复，请谨慎操作。`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.deleteHandle(data)
      }).catch(action => {
        this.$message.info(`您已取消删除${data.name}的公示信息`)
      })
    },
    deleteHandle(data) {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoDeleteCanteenAdminPost({
        id: data.id
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('删除成功')
          this.getCanteenPublicity()
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.info-show {
  .level-show {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    &-right {
      display: flex;
      &-tips {
        display: flex;
        justify-content: flex-start;
        align-items: flex-end;
      }
    }
  }
  .qualification-publicity {
    display: grid;
    grid-template-columns: repeat(6, 200px);
    grid-gap: 20px;
    &-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start
    }
  }
  .certification-info-show {
    border-top: 1px solid #C0C4CC;
    padding-top: 20px;
    &-tips {
      color: #C0C4CC;
    }
    &-img {
      width: 200px;
      height: 200px;
    }
    &-content {
      display: grid;
      grid-template-columns: repeat(3, 200px);
      grid-gap: 20px;
      &-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        &-close {
          padding: 2px;
          border-radius: 4px;
          line-height: 14px;
          z-index: 10;
          position: absolute;
          top: 2px;
          right: 2px;
          background-color: #C0C4CC;
          color: #fff;
        }
      }
    }
  }
}
</style>
