<template>
  <!-- 添加/编辑 -->
  <custom-drawer
    :title="title"
    :show.sync="visible"
    :size="size"
    class="drawer-wrapper"
    v-bind="$attrs"
    v-on="$listeners"
    confirm-text="保存"
    @close="handlerClose"
  >
    <el-form
      :model="formData"
      ref="formData"
      :rules="formDataRules"
      label-width="90px"
      class="dialog-form m-t-20"
      v-loading="isLoading"
      :status-icon="false"
    >
      <el-form-item label="分类名称" required>
        <el-form-item
          v-for="(item, index) in formData.classifyList"
          :key="index"
          label=""
          label-width="0"
          :prop="`classifyList.${index}.name`"
          :rules="formDataRules.classifyList"
        >
          <div class="flex flex-align-c">
            <el-input v-model="item.name" :disabled="item.disabled" :maxlength="20" class="search-item-w"></el-input>
            <div class="tool-box flex flex-align-c m-l-10">
              <i
                v-if="formData.classifyList.length < 50 && is_modify && index === formData.classifyList.length - 1"
                class="tool-icon el-icon-circle-plus"
                @click="clickToolIcon('add', item, index)"
              ></i>
              <i
                v-if="formData.classifyList.length > 1 && is_modify && (index > 0 || !item.disabled)"
                class="tool-icon el-icon-remove"
                @click="clickToolIcon('remove', item, index)"
              ></i>
            </div>
          </div>
        </el-form-item>
      </el-form-item>
    </el-form>
    <div slot="footer" class="ps-drawer-footer">
      <el-button class="ps-cancel-btn" @click="clickCancleHandle">{{ is_modify ? '取 消' : '关 闭' }}</el-button>
      <el-button class="ps-btn" type="primary" @click="clickConfirmHandle">
        {{ is_modify ? '保 存' : '编 辑' }}
      </el-button>
    </div>
  </custom-drawer>
  <!-- end -->
</template>

<script>
import { deepClone } from '@/assets/js/util'

export default {
  name: 'PropertyCategoryDrawer',
  props: {
    isShow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '资产分类'
    },
    size: {
      type: [String, Number],
      default: '40%'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      formData: {
        classifyList: [{ name: '' }]
      },
      formDataRules: {
        classifyList: [{ required: true, message: '请输入分类名称', trigger: 'change' }]
      },
      delIds: [],
      is_modify: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getAssetAssetClassifyList()
    },
    async getAssetAssetClassifyList() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundFundSupervisionAssetAssetClassifyListPost({
          page: 1,
          page_size: 9999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // disabled: true 不能删除
        if (res.data.results.length) {
          if (res.data.results[0].name === '食材') {
            res.data.results[0].disabled = true
          }
          this.formData.classifyList = deepClone(res.data.results)
          console.log(this.formData.classifyList)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 添加/删除
    clickToolIcon(type, item, index) {
      if (type === 'add') {
        this.formData.classifyList.push({ name: '' })
      } else {
        if (item.id) {
          this.delIds.push(item.id)
        }
        this.formData.classifyList.splice(index, 1)
      }
    },
    clickConfirmHandle() {
      this.is_modify = !this.is_modify
      if (this.is_modify) return
      let params = {
        asset_classify_list: this.formData.classifyList,
        del_asset_classify_ids: this.delIds
      }
      this.$refs.formData.validate(valid => {
        if (valid) {
          if (this.isLoading) return
          this.isLoading = true
          this.sendFormData(this.$apis.apiBackgroundFundSupervisionAssetAssetClassifySavePost(params))
        } else {
        }
      })
    },
    async sendFormData(api) {
      const [err, res] = await this.$to(api)
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('成功')
        this.$emit('clickConfirm')
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handlerClose(e) {
      this.formData = {
        name: '',
        attribute: []
      }
      if (this.$refs.formData) {
        this.$refs.formData.resetFields()
      }
      this.isLoading = false
      this.visible = false
      // this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scope>
.drawer-wrapper {
  font-size: 14px;
  .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
  .search-item-w {
    width: 260px;
  }
  .flex {
    display: flex;
  }
  .tool-icon {
    cursor: pointer;
    font-size: 24px;
    & + .tool-icon {
      margin-left: 6px;
    }
    &:hover {
      color: #ff9b45;
    }
  }
  .el-form-item .el-form-item {
    margin-bottom: 22px;
  }
}
</style>
