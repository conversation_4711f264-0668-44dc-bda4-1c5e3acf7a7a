<template>
  <div>
    <refresh-tool :title="title" :show-refresh="false" />
    <import-page class="importPage" :initial="initial" :url="url" :header-len="headerLen" :template-url="templateUrl" :is-url-download-result="true" />
  </div>
</template>

<script>
export default {
  name: 'ImportIngredients',
  data() {
    return {
      type: 'import',
      title: '批量导入食材',
      headerLen: 1,
      initial: true,
      url: 'apiBackgroundAdminIngredientTrayBatAddPost',
      templateUrl: '/api/temporary/template_excel/food_stock/shop_ingredients.xlsx'
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.$route.params.type) {
      this.type = this.$route.params.type
    }
    if (this.type === 'import') {
      this.title = '批量导入食材'
      this.url = 'apiBackgroundFoodIngredientBatchAddPost'
    } else {
      this.title = '导入编辑'
      this.url = 'apiBackgroundAdminIngredientTrayBatModifyPost'
    }
  },
  mounted() {
  },
  methods: {}
}
</script>

<style lang="scss">
.importPage {
  // max-width: 1160px;
}
</style>
