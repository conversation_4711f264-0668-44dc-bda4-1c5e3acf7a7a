<template>
<!-- eslint-disable vue/no-unused-vars -->
  <div class="receipt-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="105px" :form-setting="searchSetting" :loading="isLoading" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="plain" type="export" @click="handleExport" v-permission="['background_report_center.finance_report.recharge_summary_list_export']">导出</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row" :empty-text="isFirstSearch ? '暂无数据，请查询' : ''">
          <table-column :index="indexMethod" v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoDetail('detail', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <table-statistics :statistics="collect" />
      <div class="red m-t-10 m-l-20">注:充值退款统计包含退款手续费</div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { beforeRecentSevenDay, berforePickerOptions } from '@/utils/formatPickerOptions'

export default {
  name: 'ConsumptionSummaryReport.vue',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tabType: 1,
      searchSetting: {
        select_date: {
          label: '充值日期',
          type: 'daterange',
          value: beforeRecentSevenDay,
          pickerOptions: berforePickerOptions,
          format: 'yyyy-MM-dd',
          clearable: false
        },
        org_ids: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: [],
          checkStrictly: true,
          multiple: true,
          collapseTags: true,
          clearable: true
        }
      },
      // 数据列表
      tableData: [],

      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      levelList: [],

      // 报表设置相关
      tableSetting: [
        { label: '充值日期', key: 'date' },
        { label: '组织名称', key: 'organization_name' },
        // { label: '现金充值到账', key: 'CashPay', type: 'money' },
        // { label: '农行充值到账', key: 'ABCPay', type: 'money' },
        // { label: '微信充值到账', key: 'WechatPay', type: 'money' },
        { label: '充值退款', key: 'refund_fee', type: 'money' },
        { label: '提现', key: 'withdraw_fee', type: 'money' },
        { label: '充值收入', key: 'income_fee', type: 'money' },
        { label: '手续费', key: 'rate_fee', type: 'money' }
        // { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      collect: [
        // 统计
        { key: 'total_finish_fee', value: 0, label: '累计充值到账：', type: 'money' },
        { key: 'total_refund_fee', value: 0, label: '累计充值退款：', type: 'money' },
        { key: 'total_withdraw_fee', value: 0, label: '累计提现：', type: 'money' },
        { key: 'total_income_fee', value: 0, label: '累计充值收入：', type: 'money' },
        { key: 'total_rate_fee', value: 0, label: '累计手续费：', type: 'money' }
      ],
      printType: 'RechargeSummaryReport',
      isFirstSearch: true
    }
  },
  created() {
    this.getPechargeMethod()
    this.initLoad(true)
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        this.getRechargeSummaryList()
      }
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getRechargeSummaryList()
        this.isFirstSearch = false
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_date') {
            if (data[key].value instanceof Array) {
              if (data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取待处理订单列表
    async getRechargeSummaryList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundReportCenterDataReportRechargeSummaryListPost({
        page: this.currentPage,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchSetting)
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.result
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 充值方式
    async getPechargeMethod() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportPechargeMethod({
        page: 1,
        page_size: 999,
        org_ids: []
      })
      if (res.code === 0) {
        const result = []
        res.data.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], key: key, type: 'money' }))
        })
        // this.searchFormSetting.payway.dataList = [{ label: '全部', value: '' }, ...result]
        // result.forEach(v => {
        //   // let key = v.value.toLowerCase().replace('pay', '_pay')
        //   this.tableSetting.push({ label: v.label, key: v.value, type: 'money' })
        // })
        this.tableSetting.splice(2, 0, ...result)
        // this.currentTableSetting = this.tableSetting
        this.initPrintSetting()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getRechargeSummaryList()
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '充值汇总表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportRechargeSummaryListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    },
    // 导出报表
    handleExport() {
      const option = {
        type: this.printType,
        url: 'apiBackgroundReportCenterDataReportRechargeSummaryListExportPost',
        params: {
          page: this.currentPage,
          page_size: 999999,
          ...this.formatQueryParams(this.searchSetting)
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
