<template>
  <div class="public-information container-wrapper">
    <div class="table-type">
      <el-button :class="[tableType==='CanteenInfo' ? 'ps-origin-btn' : '']" @click="changeTableType('CanteenInfo')" v-permission="['background_fund_supervision.publicity_info.get_canteen_info']">食堂信息</el-button>
      <el-button :class="[tableType==='foodHygieneAndSafetyManagement' ? 'ps-origin-btn' : '']" @click="changeTableType('foodHygieneAndSafetyManagement')" v-permission="['background_fund_supervision.publicity_info.security_admin_list']">食品卫生安全</el-button>
      <el-button :class="[tableType==='leadingGroup' ? 'ps-origin-btn' : '']" @click="changeTableType('leadingGroup')" v-permission="['background_fund_supervision.publicity_info.food_admin_list']">膳食管理小组</el-button>
      <el-button :class="[tableType==='incumbents' ? 'ps-origin-btn' : '']" @click="changeTableType('incumbents')" v-permission="['background_fund_supervision.job_person.job_person_list']">在职人员</el-button>
      <el-button :class="[tableType==='hostingManagement' ? 'ps-origin-btn' : '']" @click="changeTableType('hostingManagement')" v-permission="['background_fund_supervision.custody_management.list']">托管管理</el-button>
      <el-button :class="[tableType==='cateringManagement' ? 'ps-origin-btn' : '']" @click="changeTableType('cateringManagement')" v-permission="['background_fund_supervision.catering_company.list']">配餐公司</el-button>
      <el-button :class="[tableType==='verification' ? 'ps-origin-btn' : '']" @click="changeTableType('verification')" v-permission="['background_drp.drp_face_verification.list']">核验人员</el-button>
    </div>
    <div class="table-wrapper">
      <div v-if="tableType === 'CanteenInfo'">
        <canteen-info />
      </div>
      <div v-else-if="tableType === 'foodHygieneAndSafetyManagement'">
        <food-hygiene-and-safety-management />
      </div>
      <div v-else-if="tableType === 'leadingGroup'">
        <leading-group />
      </div>
      <div v-else-if="tableType === 'incumbents'">
        <incumbents />
      </div>
      <!-- 托管管理 -->
      <div v-else-if="tableType === 'hostingManagement'">
        <hosting-management />
      </div>
      <!-- 配餐管理 -->
      <div v-else-if="tableType === 'cateringManagement'">
        <catering-management />
      </div>
      <div v-if="tableType === 'verification'">
        <verification-person />
      </div>
    </div>
  </div>
</template>

<script>
import canteenInfo from './components/canteenInfo.vue'
import foodHygieneAndSafetyManagement from './components/foodHygieneAndSafetyManagement.vue'
import leadingGroup from './components/leadingGroup.vue'
import incumbents from './components/incumbents.vue'
import hostingManagement from './components/hostingManagement.vue'
import cateringManagement from './components/cateringManagement.vue'
import VerificationPerson from './components/VerificationPerson.vue'
export default {
  components: {
    canteenInfo,
    foodHygieneAndSafetyManagement,
    leadingGroup,
    incumbents,
    hostingManagement,
    cateringManagement,
    VerificationPerson
  },
  created() {
    this.tableType = this.$route.query.tabType || 'CanteenInfo'
  },
  data() {
    return {
      tableType: 'CanteenInfo'
    }
  },
  methods: {
    changeTableType(type) {
      this.tableType = type
    }
  }
}
</script>

<style lang="scss" scoped>
.public-information {
  .table-type{
    padding: 20px 0px 0px ;
    display: flex;
    font-size: 16px;
  }
}
</style>
