<template>
  <!-- 表23-学校每周食品安全排查治理报告-详情 -->
  <div class="content">
    <custom-table
      border
      v-loading="isLoading"
      :table-data="tableData"
      :table-setting="currentTableSetting"
      ref="tableData"
      style="width: 100%"
      stripe
      header-row-class-name="ps-table-header-row"
    />
    <div class="sign-content" v-if="tableData && tableData.length">
      <div class="sign-item m-r-40">
        <div class="m-b-10">
          <span class="color-999">食品安全总监(1): </span><span>{{ tableData[0].first_director }}</span>
        </div>
        <el-image
          style="width: 100px; height: 100px"
          :src="tableData[0].sign_info[0]"
          :preview-src-list="[tableData[0].sign_info[0]]"
        ></el-image>
      </div>
      <div class="sign-item">
        <div class="m-b-10">
          <span class="color-999">食品安全总监(2): </span><span>{{ tableData[0].second_director }}</span>
        </div>
        <el-image
          style="width: 100px; height: 100px"
          :src="tableData[0].sign_info[1]"
          :preview-src-list="[tableData[0].sign_info[1]]"
        ></el-image>
      </div>
    </div>
  </div>
</template>
<script>
import { to } from '@/utils'
export default {
  name: 'Table23XuexiaoMeizhouShipinLedger',
  data() {
    return {
      isLoading: false,
      tableData: [],
      currentTableSetting: [
        { label: '本周排查发现问题情况', key: 'problem', showOverflowTooltip: true },
        { label: '周排查问题整改情况和措施', key: 'measure' },
        { label: '本周食品安全管理情况评价', key: 'evaluate_verbose' },
        { label: '食品安全总监(1)', key: 'first_director' },
        // { label: '食品安全总监(1)签名', key: 'sign_info_first', type: 'slot', slotName: 'first_director_sign' },
        { label: '食品安全总监(2)', key: 'second_director' },
        // { label: '食品安全总监(2)签名', key: 'sign_info_second', type: 'slot', slotName: 'second_director_sign' },
        { label: '操作人', key: 'username' },
        { label: '复核人', key: 'confirmer', showOverflowTooltip: true }
      ]
    }
  },
  props: {
    ledgerId: {
      type: [String, Number],
      default: ''
    }
  },
  watch: {
    // 监听 ledgerId 变化
    ledgerId: {
      handler(newVal) {
        if (newVal) {
          this.getLedgerReviewDetail(newVal)
        }
      },
      immediate: true // 立即执行
    }
  },
  methods: {
    // 获取详情通用接口
    async getLedgerReviewDetail(id) {
      this.isLoading = true
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyLedgerReviewDetail({ id }))
      if (err) {
        this.$message.error(err.message)
        this.isLoading = false
        return
      }
      if (res.code === 0) {
        this.tableData = res.data ? [res.data] : []
      } else {
        this.$message.error(res.msg)
      }
      this.isLoading = false
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 0 10px;
}
.sign-content{
  display: flex;
  align-items: center;
  // justify-content: space-between;
  margin-top: 20px;
  .sign-item{
    display: flex;
    flex-direction: column;
    .color-999{
      color: #999;
    }
  }
}
</style>
