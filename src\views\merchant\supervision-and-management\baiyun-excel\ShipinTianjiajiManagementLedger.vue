<template>
  <div class="container-wrapper">
    <div class="food-additives-record">
      <h2 class="table-title">食品添加剂使用记录</h2>

      <div class="year-month-row">
        <span class="year-month-value">{{ currentYear }}</span>
        <span class="year-month-label">年</span>
        <span class="year-month-value">{{ currentMonth }}</span>
        <span class="year-month-label">月</span>
      </div>

      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">
        <el-table-column prop="itemName" label="品种名称" min-width="120" align="center"></el-table-column>
        <el-table-column prop="manufacturer" label="食品添加剂的生产经营单位" min-width="180" align="center"></el-table-column>
        <el-table-column prop="foodName" label="加入的食品名称" min-width="120" align="center"></el-table-column>
        <el-table-column prop="rawInput" label="原料投入（kg/g）" min-width="120" align="center"></el-table-column>
        <el-table-column prop="limitStandard" label="限量标准（kg/g）" min-width="120" align="center"></el-table-column>
        <el-table-column prop="useTime" label="使用时间" min-width="120" align="center"></el-table-column>
        <el-table-column prop="responsible" label="责任人" min-width="80" align="center"></el-table-column>
        <el-table-column prop="remarks" label="备注" min-width="80" align="center"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalItems">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShipinTianjiajiManagementLedger',
  data() {
    return {
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      currentPage: 1,
      pageSize: 10,
      totalItems: 4,
      tableData: [
        {
          itemName: '柠檬黄',
          manufacturer: '食品有限公司',
          foodName: '果汁',
          rawInput: '0.09',
          limitStandard: '0.1',
          useTime: '2024.3.1',
          responsible: '张三',
          remarks: ''
        },
        {
          itemName: '苯甲酸钠',
          manufacturer: '食品有限公司',
          foodName: '酱菜',
          rawInput: '0.4',
          limitStandard: '0.5',
          useTime: '2024.3.5',
          responsible: '张三',
          remarks: ''
        },
        {
          itemName: '硫酸钙',
          manufacturer: '食品有限公司',
          foodName: '豆腐',
          rawInput: '1',
          limitStandard: '1.5',
          useTime: '2024.3.6',
          responsible: '张三',
          remarks: ''
        },
        {
          itemName: '丙二醇',
          manufacturer: '食品有限公司',
          foodName: '糕点',
          rawInput: '25',
          limitStandard: '3',
          useTime: '2024.3.30',
          responsible: '张三',
          remarks: ''
        }
      ]
    };
  },
  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    cellStyle() {
      return {
        padding: '8px 5px',
        fontSize: '14px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 5px',
        textAlign: 'center'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.food-additives-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: bold;
  }

  .year-month-row {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 15px;
    font-size: 16px;

    .year-month-label {
      margin: 0 5px;
    }

    .year-month-value {
      min-width: 40px;
      text-align: center;
      border-bottom: 1px solid #000;
      margin: 0 5px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  ::v-deep .el-table {
    .red-text {
      color: #f56c6c;
    }
  }
}
</style>
