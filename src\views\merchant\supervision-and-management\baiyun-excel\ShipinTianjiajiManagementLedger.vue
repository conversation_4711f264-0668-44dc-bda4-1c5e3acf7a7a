<template>
  <div class="ShipinTianjiajiManagementLedger container-wrapper">
    <refresh-tool @refreshPage="refreshHandle"></refresh-tool>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          数据列表
        </div>
        <div class="align-r">
          <button-icon color="origin" @click="historyDrawerVisible = true">历史记录</button-icon>
          <button-icon color="origin" @click="handlerShowDrawer('add', null)" v-permission="['background_fund_supervision.ledger_food_safety.food_additive_config_add']">添加剂配置</button-icon>
          <button-icon color="origin" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="gotoExport" v-permission="['background_fund_supervision.ledger_food_safety.get_food_additive_use_ledger_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <!-- 历史记录抽屉 -->
    <HistoryRecordDrawer
      :visible.sync="historyDrawerVisible"
      title="历史记录"
      api="apiBackgroundFundSupervisionLedgerFoodSafetyOperateLogList"
      type="Additive"
      @close="historyDrawerVisible = false"
    >
      <!-- 操作前内容插槽 -->
      <!-- <template #beforeContent="{ row }">
        <div>{{ row.beforeData }}</div>
      </template> -->
      <!-- 操作后内容插槽 -->
      <!-- <template #afterContent="{ row }">
        <div>{{ row.afterData }}</div>
      </template> -->
    </HistoryRecordDrawer>
    <!-- 食品添加剂抽屉 -->
     <TianJiaJiAddDrawer
      ref="tianJiaJiAddDrawer"
      @confirm="confirmTianJiaJiDrawer"
      @close="closeTianJiaJiDrawer"
      :isshow="tianJiaJiDrawerVisible"
      title="添加剂配置"
      :type="tianJiaJiDrawerType"
      :tianJiaJiDrawerData="tianJiaJiDrawerData" />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import HistoryRecordDrawer from './compontents/HistoryRecordDrawer.vue'
import TianJiaJiAddDrawer from './compontents/TianJiaJiAddDrawer.vue'

export default {
  name: 'ShipinTianjiajiManagementLedger',
  components: {
    HistoryRecordDrawer, TianJiaJiAddDrawer
  },
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '使用时间', key: 'create_time' },
        { label: '品种名称', key: 'additive_name' },
        { label: '生产经营单位', key: 'unit' },
        { label: '食品名称', key: 'food_name' },
        { label: '原料投入（kg/g）', key: 'material_input' },
        { label: '限量标准（kg/g）', key: 'limit_standard' },
        { label: '责任人', key: 'username' },
        { label: '备注', key: 'remark' },
        { label: '复核人', key: 'confirmer' }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '日期筛选',
          clearable: false,
          value: getSevenDateRange(7)
        },
        additive_name: {
          type: 'input',
          label: '品种名称',
          value: '',
          placeholder: '请输入'
        },
        unit: {
          type: 'input',
          labelWidth: '110px',
          label: '生产经营单位',
          value: '',
          placeholder: '请输入'
        },
        food_name: {
          type: 'input',
          label: '食品名称',
          value: '',
          placeholder: '请输入'
        },
        username: {
          type: 'input',
          label: '责任人',
          value: '',
          placeholder: '请输入'
        },
        confirmer: {
          type: 'input',
          label: '复核人',
          value: '',
          placeholder: '请输入'
        }
      },
      historyDrawerVisible: false,
      tianJiaJiDrawerVisible: false,
      tianJiaJiDrawerType: '',
      tianJiaJiDrawerData: {}
    }
  },
  created() {
    const date = this.$route.query.date
    if (date) {
      this.searchFormSetting.select_time.value = [date, date]
    }
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getRecordList()
      // this.getAdditiveType() 暂时不需要
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getRecordList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodAdditiveUseLedger(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          v.contact_trade = v.contact_trade ? v.contact_trade.join('，') : ''
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getRecordList()
    },
    handlerShowDrawer(type, data) {
      this.tianJiaJiDrawerType = type
      if (type === 'add') {
        this.tianJiaJiDrawerData = {}
      } else {
        this.tianJiaJiDrawerData = deepClone(data || {})
      }
      this.tianJiaJiDrawerVisible = true
    },
    closeTianJiaJiDrawer() {
      this.tianJiaJiDrawerVisible = false
    },
    confirmTianJiaJiDrawer(data) {
      console.log('confirmTianJiaJiDrawer', data)
      this.closeTianJiaJiDrawer()
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.tableSettings)
      tableSetting = tableSetting.filter(item => item.key !== 'operation')
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '食品添加剂使用管理台账',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodAdditiveUseLedger', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    },
    gotoExport() {
      const option = {
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodAdditiveUseLedgerExport',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          date_type: 'ShipinTianjiajiManagementLedger',
          page: this.currentPage,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    },
    async getAdditiveType() {
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetFoodAdditive())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        let data = res.data || []
        this.searchFormSetting.additive_name.dataList = data.map(v => ({
          label: v.name,
          value: v.id
        }))
      }
    }
  }
}
</script>

<style lang="scss" soped>
.ShipinTianjiajiManagementLedger {
}
</style>
