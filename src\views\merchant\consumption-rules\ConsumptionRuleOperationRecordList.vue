<template>
  <div class="operation-record-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"
      @reset="resetHandler" :autoSearch="false">
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <div class="align-r">
            <el-button size="mini" @click="gotoPrint">打印</el-button>
            <el-button size="mini" @click="gotoExport" type="primary"
              v-permission="['background_marketing.consume.get_consume_rule_record_export']">
              导出
            </el-button>
            <el-button size="mini" @click="openPrintSetting" type="primary">报表设置</el-button>
          </div>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" :height="maxHeight"
          :max-height="maxHeight" stripe header-row-class-name="ps-table-header-row" empty-text="暂无数据，请查询">
          <table-column v-for="(item, index) in currentTableSetting" :key="index" :col="item">
            <template #creater="{ row }">
              <div>{{ getUserName(row) }}</div>
            </template>
            <template #operation="{ row }">
              <div @click="handlerDetail(row)" class="ps-origin pointer">查看</div>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount" background class="ps-text" popper-class="ps-popper-select"></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 报表设置 -->
    <print-setting v-if="dialogPrintVisible" :extraParams="{ printType: printType }" :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting" :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"></print-setting>
    <!-- 详情弹窗 -->
    <consumption-rules-detail-dialog ref="detailDialogRef" :isshow="dialogDetailVisible" :title="dialogDetailTitle"
      :dialogType="dialogDetailType" @confirm="confirmDetailDialog" @close="closeDetailDialog" />
  </div>
</template>

<script>
import { debounce, deepClone, to, getSevenDateRange } from '@/utils'
import { OPERATION_RECORD_LIST_SEARCH_SETTING, OPERATION_RECORD_LIST_TABLE_SETTING } from "./constants"
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import ConsumptionRulesDetailDialog from './components/ConsumptionRulesDetailDialog'
export default {
  name: 'ConsumptionRuleOperationRecordList',
  components: {
    ConsumptionRulesDetailDialog
  },
  props: {},
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: deepClone(OPERATION_RECORD_LIST_SEARCH_SETTING),
      tableSetting: deepClone(OPERATION_RECORD_LIST_TABLE_SETTING),
      maxHeight: 'calc(100vh - 531px)',
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'ConsumptionRuleOperationRecordList',
      dialogDetailVisible: false, // 详情弹窗
      dialogDetailTitle: '详情', // 详情弹窗标题
      dialogDetailType: 'detail',
      startTime: '',
      endTime: ''
    }
  },
  watch: {
  },
  created() { },
  mounted() {
    this.initPrintSetting()
    this.startTime = getSevenDateRange(7)[0] + " 00:00:00"
    this.endTime = getSevenDateRange(7)[1] + " 23:59:59"
    this.getConsumptionList()
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      console.log("searchHandle", e)
      const params = this.formatQueryParams(this.searchFormSetting)
      if (params.start_time !== this.startTime || params.end_time !== this.endTime) {
        this.startTime = params.start_time
        this.endTime = params.end_time
        this.getConsumptionList()
      }
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 重置
    resetHandler() {
      this.currentPage = 1
      this.getDataList()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 获取消费规则列表
    async getDataList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundMarketingConsumeGetConsumeRuleRecord(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log(res)
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value !== '全部') {
          if (key !== 'select_time') {
            if (Array.isArray(data[key].value)) {
              if (data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0] + " 00:00:00"
            params.end_time = data[key].value[1] + " 23:59:59"
          }
        }
      }
      return params
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.currentTableSetting)
      tableSetting = tableSetting.filter(item => item.key !== 'operation')
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '规则变更记录',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundMarketingConsumeGetConsumeRuleRecord', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    },
    // 导出报表
    gotoExport() {
      const option = {
        type: this.printType,
        url: 'apiBackgroundMarketingConsumeGetConsumeRuleRecordExport',
        params: {
          page: this.currentPage,
          page_size: 999999,
          ...this.formatQueryParams(this.searchFormSetting)
        }
      }
      this.exportHandle(option)
    },
    // 确认详情
    confirmDetailDialog() {
      this.dialogDetailVisible = false
    },
    // 关闭详情
    closeDetailDialog() {
      this.dialogDetailVisible = false
    },
    // 查看详情
    handlerDetail(row) {
      if (this.$refs.detailDialogRef) {
        row.detail.name = row.name
        this.$refs.detailDialogRef.setFormData(row.detail)
      }
      this.dialogDetailVisible = true
    },
    // 获取消费规则
    async getConsumptionList() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const [err, res] = await this.$to(this.$apis.apiBackgroundMarketingConsumeGetConsumeRuleFilterName({
        page: 1,
        page_size: 99999,
        start_time: params.start_time,
        end_time: params.end_time
      }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        const data = res.data || []
        this.searchFormSetting.rule_ids.dataList = deepClone(data)
      }
    },
    // 获取用户名称
    getUserName(row) {
      let creater = row.creater || ''
      let userName = row.userName || ''
      if (!creater && !userName) {
        return "--"
      }
      return creater + "(" + userName + ")"
    }

  }
}
</script>
<style lang="scss" scoped></style>
