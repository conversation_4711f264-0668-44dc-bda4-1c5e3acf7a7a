<template>
  <div class="ps-el-drawer">
    <el-drawer
      title="体检报告详情"
      :visible="visible"
      :show-close="false"
      size="40%">
      <div class="p-20">
        <div class="flex flex-col">
          <span class="f-w-700 m-b-20">报告类型：{{ data.data_source_alias }}</span>
          <div v-if="data.integrity_data && data.integrity_data.length !== 0">
            <div v-for="(item, index) in data.integrity_data" :key="index" class="m-b-20">
              <div class="f-w-700 m-b-10">{{ item.name }}</div>
              <div :class="[item.name.includes('血压心率') || item.name.includes('人体成分') ? 'default-layout' : 'special-layout', 'bg-gray']">
                <div v-for="(item1, index1) in item.children" :key="index1" class="flex-b-c">
                  <div class="m-r-10">
                    <div>{{ item1.name }}：{{ item1.value || '--' }} {{ item1.unit }}</div>
                  </div>
                  <div :class="['tips', item1.result_text === '正常' ? 'normal' : '', item1.result_text === '偏高' ? 'height' : '', item1.result_text === '偏低' ? 'low' : '', 'font-size-12']">
                    {{ item1.result_text }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <el-empty description="暂无数据"></el-empty>
          </div>
        </div>
        <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
          <div class="m-r-30">
            <el-button size="small"  class="w-100 ps-origin-btn" @click="visible = false">关闭</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'PhysicalExaminationReportDetail',
  props: {
    isShow: Boolean,
    reportName: String,
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    visible: {
      get() {
        console.log('this.data', this.data)
        // this.setData()
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  data() {
    return {
      isLoading: false
    }
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.default-layout {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 10px 30px;
}
.special-layout {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 10px;
}
.bg-gray {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}
.tips {
  padding: 4px 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 4px;
}
.normal {
  background-color: #83E4BB;
}
.height {
  background-color: #F3AE8D;
}
.low {
  background-color: #A7D9FA;
}
</style>
