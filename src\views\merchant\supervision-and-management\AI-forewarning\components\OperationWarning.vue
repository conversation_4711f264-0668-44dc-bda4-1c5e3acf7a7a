<template>
  <div class="container-wrapper">
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler"
      @reset="resetHandler">
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="origin" @click="showWarnTimeDialog"
            v-permission="['']">预警时间</button-icon> -->
            <button-icon color="origin" @click="showWarnTimeDialog"
            >预警时间</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #accountType="{row}">
               <div>{{ getAccountType(row.warn_type) }}</div>
            </template>
            <template #warnDetail="{row}">
              <div>{{ row.warn_detail }}<span v-if="row.warn_val">( <span class="ps-red"> {{ row.warn_val ? row.warn_val + '%' : "0%" }}</span> )</span></div>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'" :total="totalCount"></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 -->
    <warning-time-dialog :is-show="isShowWarnTimeDialog" :title="dialogTitle" :type="dialogType" @closeDialog="closeWarnTimeDialog" @confirmDialog="confirmWarnTimeDialog" />
  </div>
</template>

<script>
import { deepClone, to, debounce } from '@/utils'
import { TABLE_HEAD_BUSINESS_WARNING, SEARCH_FORM_BUSINESS_WARNING, TYPE_WARNING } from '../constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import WarningTimeDialog from './WarningTimeDialog'
export default {
  name: "BusinessWarning",
  props: ['isShow'],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: deepClone(TABLE_HEAD_BUSINESS_WARNING),
      searchFormSetting: deepClone(SEARCH_FORM_BUSINESS_WARNING),
      isShowWarnTimeDialog: false, // 是否显示弹窗
      dialogType: 'edit', // 弹窗类型
      dialogTitle: '预警时间' // 弹窗标题
    }
  },
  components: {
    WarningTimeDialog
  },
  mixins: [exportExcel],
  created() {
    this.initLoad()
  },
  watch: {
    isShow: {
      handler(val) {
        if (val) {
          this.initLoad()
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initLoad()
    },
    /**
     * 初始化数据
     */
    initLoad() {
      // 获取数据列表
      this.getDataList()
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    /**
     * 获取数据列表
     */
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionWarnManageBusinessWarnListPost(params))
      console.log("getDataList", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        if (data) {
          var resultList = data ? data.results : []
          this.tableData = deepClone(resultList)
          this.totalCount = data.count || 0
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'select_time') {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          } else if (key === 'warn_type_list') {
            params.warn_type_list = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },

    /**
     * 筛选
     */
    searchHandler: debounce(function () {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.refreshHandle()
    },
    // 显示预警时间弹窗
    showWarnTimeDialog() {
      this.isShowWarnTimeDialog = true
    },
    // 取消弹窗
    closeWarnTimeDialog() {
      console.log("closeWarnTimeDialog")
      this.isShowWarnTimeDialog = false
    },
    // 确认弹窗
    confirmWarnTimeDialog() {
      console.log("confirmWarnTimeDialog")
      this.isShowWarnTimeDialog = false
    },
    // 获取类型
    getAccountType(type) {
      console.log("getAccountType", type);
      let list = deepClone(TYPE_WARNING)
      let result = list.find(item => item.value === type)
      if (result) {
        return result.label
      }
      return type
    }
  }
}

</script>

<style lang="scss" scoped></style>
