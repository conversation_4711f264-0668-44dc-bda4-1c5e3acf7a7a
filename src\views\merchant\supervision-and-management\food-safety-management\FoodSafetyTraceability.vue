<template>
  <!-- 晨检记录-->
  <div class="morning-wrapper container-wrapper">
    <div class="tab-box">
      <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
        <el-radio-button v-for="tab in tabTypeList" :key="tab.value" :label="tab.value"
          v-permission="[tab.permissions]">{{ tab.label }}</el-radio-button>
      </el-radio-group>
    </div>
    <div class="tab-item m-t-20">
      <search-form ref="searchRef" :loading="isLoading" @search="searchHandle" :form-setting="searchFormSetting"
        @reset="resetHandler" label-width="120px" :autoSearch="false"></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe
            header-row-class-name="ps-table-header-row" empty-text="暂无数据，请查询">
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #foodIngredient="{ row }">
                <el-tooltip placement="top">
                  <div slot="content">
                    <div class="w-300">
                      <span v-for="(item, index) in row.food_ingredient" :key="index">{{ item.name }}{{ index < row.food_ingredient.length - 1 ? '、' : '' }}</span>
                    </div>
                  </div>
                  <div class="w-200 ellipsis">
                    <span v-for="(item, index) in row.food_ingredient" :key="index">{{ item.name }}{{ index < row.food_ingredient.length - 1 ? '、' : '' }}</span>
                  </div>
                </el-tooltip>
              </template>
              <template #totalCount="{ row }">
                {{ row.total_count }}份
              </template>
              <template #operation="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="openDrawer(row)">查看</el-button>
              </template>
              <template #download="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="gotoExport(row)" v-permission="['background_fund_supervision.food_safety_source.download_order']">下载</el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize" :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount"></pagination>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'溯源详情'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="drawerFormRef" :model="drawerForm" label-width="100px" label-position="right">
            <el-form-item label="组织名称：">
              {{ drawerForm.orgName }}
            </el-form-item>
            <el-form-item label="日期：">
              {{ drawerForm.date }}
            </el-form-item>
            <el-form-item label="餐段：">
              {{ drawerForm.mealType }}
            </el-form-item>
            <el-form-item label="菜品名称：">
              {{ drawerForm.foodName }}
            </el-form-item>
            <el-form-item label="留样信息：">
              <el-table v-loading="drawerLoading" :data="drawerForm.sampleInfo" ref="tableData" style="width: 100%" stripe
                header-row-class-name="ps-table-header-row" empty-text="暂无数据，请查询">
                <table-column v-for="(item, index) in drawerTableSetting" :key="index" :col="item">
                  <template #foodWeight="{ row }">
                    {{ row.food_weight }}g
                  </template>
                  <template #images="{ row }">
                    <el-button type="text" size="small" class="ps-text" @click="handleClick(row)">查看</el-button>
                  </template>
                </table-column>
              </el-table>
            </el-form-item>
            <el-form-item label="值班人员：">
              <div class="flex-col">
                <div class="ps-flex-align-c flex-right m-b-20">
                  <el-button type="primary" size="mini" @click="gotoPath('MerchantCanteenInfo')">健康证信息</el-button>
                  <el-button type="primary" size="mini" @click="gotoPath('MerchantMorningCheckLog')">晨检信息</el-button>
                </div>
                <div class="drawer-table-box">
                  <el-table v-for="item in 3" :key="item"  v-loading="drawerLoading" :data="drawerForm.personList[`arr${item}`]" ref="tableData" style="width: 100%" stripe
                  header-row-class-name="ps-table-header-row" empty-text="暂无数据，请查询">
                    <table-column v-for="(item, index) in drawerTablePersonListSetting" :key="index" :col="item" :show-overflow-tooltip="item.showTooltip">
                    </table-column>
                  </el-table>
                </div>
              </div>

            </el-form-item>
            <el-form-item label="订单溯源：">
              <el-button type="primary" size="mini" @click="gotoExport">导出</el-button>
            </el-form-item>
            <el-form-item label="食材溯源：">
              <div class="flex-col source-data">
                <!-- 循环食材 -->
                <div v-for="(item, index) in drawerForm.sourceData" :class="['source-data-food', index < drawerForm.sourceData.length - 1 ? 'm-b-20' : '']" :key="index">
                  <div>关联食材（{{ index + 1 }}）：{{ item.ingredient_name }}</div>
                  <div class="source-data-food-content">
                    <div v-for="(item1, index1) in item.detail" :key="index1" :class="['source-data-food-content-item', index1 < item.detail.length - 1 ? 'm-b-20' : '']">
                      <div :class="['flex-b-c', 'source-data-food-content-item-head', 'm-b-20']">
                        <div>出库单：{{ item1.exist_trade_no }}</div>
                        <el-button type="text" size="mini" @click="gotoPath('DocumentManagement', item1.detail[0], 'outboundOrder')">查看</el-button>
                      </div>
                      <div v-for="(item2, index2) in item1.detail" :key="index2" :class="['source-data-food-content-item-detail', index2 < item1.detail.length - 1 ? 'm-b-20' : '']">
                        <div>供应商{{ index2 + 1 }}：{{ item2.supplier_manage_name }}</div>
                        <div class="flex-b-c">
                          <div>入库单：{{ item2.entry_trade_no }}</div>
                          <el-button type="text" size="mini" @click="gotoPath('DocumentManagement', item2, 'inboundOrder')">查看</el-button>
                        </div>
                        <div class="flex-b-c" v-if="item2.purchase_order">
                          <div>采购单：{{ item2.purchase_order }}</div>
                          <el-button type="text" size="mini" @click="gotoPath('DocumentManagement', item2, 'purchaseOrder')">查看</el-button>
                        </div>
                        <div class="flex-b-c" v-if="item2.delivery_order">
                          <div>配送单：{{ item2.delivery_order }}</div>
                          <el-button type="text" size="mini" @click="gotoPath('DocumentManagement', item2, 'deliveryOrder')">查看</el-button>
                        </div>
                        <div class="flex-b-c" v-if="item2.receipt_order">
                          <div>收货单：{{ item2.receipt_order }}</div>
                          <el-button type="text" size="mini" @click="gotoPath('DocumentManagement', item2, 'receiptOrder')">查看</el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="drawerShow = false">关闭</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { SEARCH_SETTING_DISHESROOTS, SEARCH_SETTING_REPASTROOTS, TABLE_HEAD_DATA_DISHESROOTS, TABLE_HEAD_DATA_REPAST } from './constants'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { mapGetters } from 'vuex'
import { deepClone, download } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'FoodSafeTyTraceability',
  mixins: [exportExcel],
  components: {
    ElImageViewer
  },
  data() {
    return {
      isLoading: false,
      tabType: '',
      tabTypeList: [
        { label: '菜品溯源', value: 'dishes', permissions: 'background_fund_supervision.food_safety_source.food_source_list' },
        { label: '就餐溯源', value: 'repast', permissions: 'background_fund_supervision.food_safety_source.dine_source_list' }
      ],
      tableSetting: deepClone(TABLE_HEAD_DATA_DISHESROOTS),
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: deepClone(SEARCH_SETTING_DISHESROOTS),
      selectData: {},
      drawerShow: false,
      drawerLoading: false,
      drawerTableSetting: [
        { label: '留样时间', key: 'reserved_time' },
        { label: '所属组织', key: 'org_name' },
        { label: '餐段', key: 'meal_type_alias' },
        { label: '菜品名称', key: 'food_name', showTooltip: true },
        { label: '留样重量', key: 'food_weight', type: "slot", slotName: 'foodWeight' },
        { label: '留样图片', key: 'food_image', type: "slot", slotName: 'images' },
        { label: '留样员', key: 'sample_person', showTooltip: true }
      ],
      drawerTablePersonListSetting: [
        { label: '姓名', key: 'name', showTooltip: true },
        { label: '岗位', key: 'job_title', showTooltip: true }
      ],
      drawerForm: {
        orgName: '',
        date: '',
        mealType: '',
        foodName: '',
        sampleInfo: '',
        personList: {},
        sourceData: []
      },
      showImagePreview: false,
      previewList: []
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'organization', 'allPermissions']),
    // 判断是不是日期是同一天，同一天才展示，不是同一天不展示
    getIsDateRange() {
      try {
        let startDate = this.searchFormSetting.select_time.value[0]
        let endDate = this.searchFormSetting.select_time.value[1]
        return startDate === endDate
      } catch (error) {
        return false
      }
    }
  },
  created() {

  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.initTabList()
    },
    // 初始页面权限
    initTabList() {
      let result = []
      this.tabTypeList.forEach(v => {
        if (this.allPermissions.includes(v.permissions)) {
          result.push(v)
        }
      })
      this.tabTypeList = result
      this.tabType = this.tabTypeList.length ? this.tabTypeList[0].value : ''
      console.log('this.tabType', this.tabType)
      this.setTabDataHandle(this.tabType)
    },
    resetHandler() {
      this.currentPage = 1
      this.setTabDataHandle(this.tabType)
    },
    async refreshHandle() {
      this.currentPage = 1
      this.initTabList()
    },
    // 筛选
    async searchHandle(e) {
      this.currentPage = 1
      this.getDataList()
    },
    // 切换tab
    changeTabHandle(e) {
      this.setTabDataHandle(e)
    },
    // 设置tab数据
    setTabDataHandle(e) {
      if (e === 'dishes') {
        this.searchFormSetting = deepClone(SEARCH_SETTING_DISHESROOTS)
        this.tableSetting = deepClone(TABLE_HEAD_DATA_DISHESROOTS)
      } else {
        this.searchFormSetting = deepClone(SEARCH_SETTING_REPASTROOTS)
        this.tableSetting = deepClone(TABLE_HEAD_DATA_REPAST)
      }
      this.tableData = []
      console.log('this.tableSetting', this.tableSetting)
      this.getDataList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value !== '全部' && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        } else {
          params[key] = undefined
        }
      }
      return params
    },
    // 请求列表数据
    async getDataList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const res = this.tabType === 'dishes' ? await this.$apis.apiBackgroundFundSupervisionFoodSafetySourceFoodSourceListPost(params) : await this.$apis.apiBackgroundFundSupervisionFoodSafetySourceDineSourceListPost(params)
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        this.tableData = deepClone(data.results)
        if (this.$refs.tableData) {
          this.$refs.tableData.doLayout()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    async openDrawer(data) {
      this.isLoading = true
      this.selectData = data
      this.drawerForm = {
        orgName: data.org_name,
        date: data.pay_date,
        mealType: data.meal_type_alias,
        foodName: data.food_name,
        sampleInfo: [],
        personList: {},
        sourceData: []
      }
      let flag1 = await this.getSourceData(data)
      let flag2 = await this.getSampleData(data)
      let flag3 = await this.getPersonList(data)
      if (flag1 && flag2 && flag3) {
        this.isLoading = false
        this.drawerShow = true
      }
    },
    handleClick(data) {
      this.previewList = [data.food_image]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    async getSourceData(data) {
      let params = {
        org_id: data.org_id,
        meal_type: data.meal_type,
        date: data.pay_date,
        ingredient_list: data.ingredient_id
      }
      const res = await this.$apis.apiBackgroundFundSupervisionFoodSafetySourceGetFoodIngredientSourcePost(params)
      if (res && res.code === 0) {
        let resultData = res.data || []
        // 先按食材分类
        let resArr = Object.entries(this.groupBy(resultData, 'ingredient_id')).map(([id, items]) => ({
          ingredient_id: items[0].ingredient_id,
          ingredient_name: items[0].ingredient_name,
          detail: items
        }))
        // 再将每个食材按照出库单分类
        resArr.forEach(item => {
          let newItem = Object.entries(this.groupBy(item.detail, 'exist_trade_no')).map(([id, items]) => ({
            exist_trade_no: id,
            detail: items
          }))
          item.detail = deepClone(newItem)
        })
        console.log(resArr)
        this.drawerForm.sourceData = deepClone(resArr)
        return true
      } else {
        this.$message.error(res.msg)
        return false
      }
    },
    // 数据分类
    groupBy(data, key) {
      // 使用
      let obj = data.reduce((accumulator, current) => {
        const keyValue = current[key]

        // 如果 accumulator 中已经有该 ingredient_id，则将其添加到对应的数组中
        if (accumulator[keyValue]) {
          accumulator[keyValue].push(current)
        } else {
          // 否则创建一个新的数组
          accumulator[keyValue] = [current]
        }
        return accumulator
      }, {})
      return obj
    },
    async getSampleData(data) {
      let params = {
        org_id: data.org_id,
        food_id: data.food_id,
        meal_type: data.meal_type,
        reserved_date: data.pay_date
      }
      const res = await this.$apis.apiBackgroundFundSupervisionFoodSafetySourceGetReservedSampleInfoPost(params)
      if (res && res.code === 0) {
        this.drawerForm.sampleInfo = deepClone(res.data)
        return true
      } else {
        this.$message.error(res.msg)
        return false
      }
    },
    async getPersonList(data) {
      let params = {
        use_date: data.pay_date,
        org_id: data.org_id
      }
      const res = await this.$apis.apiBackgroundFundSupervisionFoodSafetySourceGetSchedulePersonPost(params)
      if (res && res.code === 0) {
        let obj = {
          arr1: [],
          arr2: [],
          arr3: []
        }
        if (res.data.job_person && res.data.job_person.length > 0) {
          res.data.job_person.forEach((item, index) => {
            if (index % 3 === 0) {
              obj.arr1.push(item); // 索引为 0, 3, 6... 的元素放入 array1
            } else if (index % 3 === 1) {
              obj.arr2.push(item); // 索引为 1, 4, 7... 的元素放入 array2
            } else {
              obj.arr3.push(item); // 索引为 2, 5, 8... 的元素放入 array3
            }
          })
        }
        this.drawerForm.personList = { ...obj }
        return true
      } else {
        this.$message.error(res.msg)
        return false
      }
    },
    gotoExport(data) {
      console.log('data', data)
      let params = {
        trade_no_list: this.tabType === 'dishes' ? this.selectData.trade_no : data.trade_no_list
      }
      const option = {
        url: this.tabType === 'dishes' ? 'apiBackgroundFundSupervisionFoodSafetySourceExportOrderSourcePost' : 'apiBackgroundFundSupervisionFoodSafetySourceDownloadOrderPost',
        params: params
      }
      this.exportHandle(option)
    },
    gotoPath(path, data, type) {
      let option = {
        name: path,
        query: {}
      }
      switch (path) {
        case 'MerchantCanteenInfo':
          option.query = {
            tabType: 'SecurityInfo',
            tableType: 'incumbents'
          }
          break
        case 'DocumentManagement':
          option.query = {
            tabType: type,
            warehouse_id: data.warehouse_id,
            warehouse_name: data.warehouse_id
          }
          break
        default:
          option.query = {}
      }
      this.$router.push({
        ...option
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.morning-wrapper {
  padding-top: 20px;
}
.drawer-table-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 10px;
}

.source-data {
  width: 500px;
  &-food {
    border: 1px solid #EBEEF5;
    padding: 20px;
    border-radius: 8px;
    &-content {
      &-item {
        background-color: #F2F2F2;
        padding: 20px;
        border-radius: 8px;
         &-head {
          background-color: #D7D7D7;
          padding: 20px;
          border-radius: 8px;
         }
         &-detail {
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
         }
      }
    }
  }
}
</style>
