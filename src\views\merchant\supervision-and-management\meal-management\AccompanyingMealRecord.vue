<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandler"
      @reset="resetHandler">
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- v-permission="['no-permission']"-->
          <button-icon color="origin" @click="gotoExport" v-permission="['background_fund_supervision.channel_canteen_management.meal_accompanying_list_export']">
            导出</button-icon>
            <button-icon color="origin" @click="gotoPrint"
            >打印</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe  :span-method="customSpanMethod"
          header-row-class-name="ps-table-header-row">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #meal_type="{ row }">
              {{ getMealType(row.meal_type) }}
            </template>
            <template #person="{ row }">
              {{ getPersonList(row.person_record_list) }}
            </template>
            <template #accountType="{row}">
               <div>{{ getAccountType(row.warn_type) }}</div>
            </template>
            <template #operation="{row}">
              <el-button type="text" size="small" class="ps-text"  @click="handlerShowDetail(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'" :total="totalCount"></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 -->
    <meal-detail-dialog :is-show="isShowDetail" :title="dialogTitle" :type="dialogType" @closeDialog="closeDetailDialog" ref="detailDialog"/>
  </div>
</template>

<script>
import { deepClone, to, debounce, divide } from '@/utils'
import { TABLE_HEAD_MEAL_RECORD, SEARCH_FORM_MEAL_RECORD, TYPE_WARNING } from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import MealDetailDialog from './components/MealDetailDialog'
import { getNameByType, getMealTypeName, getPersonNameByList } from "./utils"

export default {
  name: "AccompanyingMealRecord",
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: deepClone(TABLE_HEAD_MEAL_RECORD),
      searchFormSetting: deepClone(SEARCH_FORM_MEAL_RECORD),
      isShowDetail: false, // 是否显示弹窗
      dialogType: 'detail', // 弹窗类型
      dialogTitle: '详情', // 弹窗标题
      printType: 'AccompanyingMealRecord',
      oldTableData: [] // 保存列表
    }
  },
  components: {
    MealDetailDialog
  },
  mixins: [exportExcel],
  created() {
    this.initLoad()
  },
  methods: {
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initLoad()
    },
    /**
     * 初始化数据
     */
    initLoad() {
      console.log("initLoad");
      // 获取数据列表
      this.getDataList()
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    /**
     * 获取数据列表
     */
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionChannelCanteenManagementMealAccompanyingListPost(params))
      console.log("getDataList", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        if (data) {
          var resultList = data ? data.results : []
          // 处理陪餐管理数据
          if (resultList && resultList.length > 0) {
            this.oldTableData = deepClone(resultList)
            resultList = this.getNewListByGoodList(resultList)
          }
          this.tableData = deepClone(resultList)
          this.totalCount = data.count || 0
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'select_time') {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          } else if (key === 'warn_type_list') {
            params.warn_type_list = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    /**
     * 筛选
     */
    searchHandler: debounce(function () {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.currentPage = 1
      this.initLoad()
    },
    // 显示预警时间弹窗
    showWarnTimeDialog() {
      this.isShowDetail = true
    },
    // 取消弹窗
    closeDetailDialog() {
      console.log("closeDetailDialog")
      this.isShowDetail = false
    },
    // 确认弹窗
    confirmDetailDialog() {
      console.log("confirmDetailDialog")
      this.isShowDetail = false
    },
    // 获取类型
    getAccountType(type) {
      console.log("getAccountType", type);
      let list = deepClone(TYPE_WARNING)
      let result = list.find(item => item.value === type)
      if (result) {
        return result.label
      }
      return type
    },
    // 查看详情
    handleDetailClick(row) {
      console.log("handleDetailClick", row);
      this.isShowDetail = true
    },
    // 导出
    gotoExport() {
      const option = {
        url: 'apiBackgroundFundSupervisionChannelCanteenManagementMealAccompanyingListExportPost',
        type: this.printType,
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 打印
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tabbleSetting = deepClone(this.tableSettings)
      tabbleSetting = tabbleSetting.filter((item) => item.key !== "operation")
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '陪餐记录',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionChannelCanteenManagementMealAccompanyingListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tabbleSetting),
          current_table_setting: JSON.stringify(tabbleSetting),
          collect: null,
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          }),
          isMerge: '0'
        }
      });
      window.open(href, "_blank");
    },
    // 获取人员列表
    getPersonList (list) {
      return getPersonNameByList(list)
    },
    // 获取名称
    getCurrentNameByType (type, value) {
      console.log("getCurrentNameByType", type, value)
      return getNameByType(type, value)
    },
    // 获取餐段类型
    getMealType(type) {
      return getMealTypeName(type)
    },
    // 获取新列表，根据菜品列表
    getNewListByGoodList(list) {
      if (!list || list.length === 0) {
        return []
      }
      // 用一个新的列表存储拿出来的菜品数据
      let newList = []
      list.forEach((item) => {
        let foodRecordList = item.food_record_list || []
        let setMealRecordList = item.set_meal_record_list || []
        item.room_clean_type =
          this.getCurrentNameByType("room_clean_type", item.room_clean_type) +
          "、" +
          this.getCurrentNameByType("room_attitude_type", item.room_attitude_type)
        item.area_clean_type =
        this.getCurrentNameByType("area_clean_type", item.area_clean_type) +
          "、" +
          this.getCurrentNameByType("area_waste_type", item.area_waste_type)
        item.oa_clean_type =
        this.getCurrentNameByType("oa_clean_type", item.oa_clean_type) +
          "、" +
          this.getCurrentNameByType("oa_operate_type", item.oa_operate_type)
        item.tda_clean_type =
        this.getCurrentNameByType("tda_clean_type", item.tda_clean_type) +
          "、" +
          this.getCurrentNameByType("tda_disinfection_type", item.tda_disinfection_type)
        item.operation_type = this.getCurrentNameByType("operation_type", item.operation_type)
        foodRecordList = foodRecordList.concat(setMealRecordList)
        if (foodRecordList && foodRecordList.length > 0) {
          foodRecordList.forEach((subItem, index) => {
            newList.push({
              ...item,
              food_name: this.getFoodName(subItem),
              gg_excellent_type_alias: subItem.gg_excellent_type_alias,
              zl_excellent_type_alias: subItem.zl_excellent_type_alias,
              quantity_type_alias: subItem.quantity_type_alias,
              price_type_alias: subItem.price_type_alias,
              food_remark: subItem.remark,
              isMerge: foodRecordList && foodRecordList.length > 1,
              spanNumber: foodRecordList && foodRecordList.length > 1 ? (index === 0 ? foodRecordList.length : 0) : 1
            })
          })
        } else {
          newList.push({
            ...item,
            foodName: "",
            gg_excellent_type_alias: "",
            zl_excellent_type_alias: "",
            quantity_type_alias: "",
            price_type_alias: "",
            food_remark: "",
            isMerge: false,
            spanNumber: 1
          })
        }
      })
      return newList
    },
    // 第一到第九列值相同的进行合并单元格
    customSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if ((columnIndex < 10 || columnIndex === 16) && row.isMerge) {
        return { rowspan: row.spanNumber, colspan: 1 }
      }
      // 其他列不合并
      return { rowspan: 1, colspan: 1 }
    },
    // 反馈详情
    handlerShowDetail (row) {
      console.log(row)
      let id = row.id
      let findItem = this.oldTableData.find((item) => item.id === id)
      this.dialogTitle = "详情"
      this.dialogType = "detail"
      if (this.$refs.detailDialog) {
        this.$refs.detailDialog.setDialogData(findItem)
      }
      this.isShowDetail = true
    },
    // 获取详情菜品名称
    getFoodName(row) {
      let name = row.name
      let price = row.price ? divide(row.price) : "0.00"
      return `${name}  ¥${price}/份`
    }
  }
}

</script>

<style lang="scss" scoped></style>
