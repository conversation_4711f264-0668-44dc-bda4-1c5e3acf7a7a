<template>
  <div class="AttendanceGroupAdmin container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openDialog('addAttendanceGroup')" v-permission="['background_attendance.group.add']">新建</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="name" label="考勤组名称" align="center"></el-table-column>
          <el-table-column prop="card_users.length" label="人数" align="center"></el-table-column>
          <el-table-column prop="remark" label="备注" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDialog('choosePerson', scope.row)"
                v-permission="['background_attendance.attendance_group.modify']"
                >选择人员</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="openDialog('editAttendanceGroup', scope.row)"
                v-permission="['background_attendance.group.modify']"
                >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                @click="delPushSetting(scope.row.id)"
                v-permission="['background_attendance.group.delete']"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <attendance-group-dialog
      :isshow.sync="dialogVisible"
      :title="dialogTitle"
      :type="dialogType"
      :width="dialogWidth"
      :select-info="selectInfo"
      :person-list="personList"
      @confirm="searchHandle"/>

  </div>
</template>

<script>
import { debounce } from '@/utils'
import AttendanceGroupDialog from './AttendanceGroupDialog.vue'

export default {
  name: 'AttendanceGroupAdmin',
  components: { AttendanceGroupDialog },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      dialogWidth: '',
      selectInfo: {},
      personList: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getAttendanceGroupList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getAttendanceGroupList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async getAttendanceGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupListPost({
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getAttendanceGroupList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getAttendanceGroupList()
    },
    async delPushSetting(id) {
      this.$confirm(`确定删除该考勤组？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupDeletePost({
              ids: [id]
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getAttendanceGroupList()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    openDialog(type, data) {
      this.dialogType = type
      this.selectInfo = data
      if (type === 'choosePerson') {
        this.personList = data.card_users
        this.dialogTitle = '选择人员'
        this.dialogWidth = '850px'
      } else if (type === 'addAttendanceGroup') {
        this.dialogTitle = '新增考勤组'
        this.dialogWidth = '400px'
      } else if (type === 'editAttendanceGroup') {
        this.dialogTitle = '编辑考勤组'
        this.dialogWidth = '400px'
      }
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
