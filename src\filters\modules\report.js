export default {
  // 操作类型
  OperatType(val) {
    if (val === 'ORDER_PART_REFUND') {
      return '部分退款'
    } else if (val === 'ORDER_REFUND') {
      return '退款'
    } else if (val === 'JC_CONSUME') {
      return '计次消费'
    } else if (val === 'KF_CONSUME') {
      return '扣费消费'
    } else if (val === 'DRAW') {
      return '后台取款'
    } else if (val === 'RECHARGE') {
      return '充值'
    } else if (val === 'SUBSIDY_PUBLISH') {
      return '补贴发放'
    } else if (val === 'SUBSIDY_CLEAR') {
      return '补贴清零'
    } else if (val === 'FLAT_COST_CONLLECTION') {
      return '工本费退款'
    } else if (val === 'PATCH_COST') {
      return '补卡'
    } else if (val === 'UNKNOWN') {
      return '未知'
    } else if (val === 'reservation') {
      return '"预约订单'
    } else if (val === 'report_meal') {
      return '"报餐'
    }
  },
  // 支付类型
  SubPayway(val) {
    if (val === 'wallet') {
      return '储值钱包'
    } else if (val === 'ewallet') {
      return '电子钱包'
    } else if (val === 'twallet') {
      return '第三方钱包'
    } else if (val === 'daikou') {
      return '授权代扣支付'
    } else if (val === 'ermb') {
      return '数字人民币支付'
    } else if (val === 'jsapi') {
      return 'JSAPI支付'
    } else if (val === 'h5') {
      return 'H5支付'
    } else if (val === 'wap') {
      return 'WAP支付'
    } else if (val === 'miniapp') {
      return '小程序支付'
    } else if (val === 'cash') {
      return '现金支付'
    } else if (val === 'micropay') {
      return 'B扫C支付'
    } else if (val === 'scanpay') {
      return 'C扫B支付'
    } else if (val === 'cardpay') {
      return '刷卡支付'
    } else if (val === 'facepay') {
      return '刷脸支付'
    } else if (val === 'facecode') {
      return '会员码支付'
    } else if (val === 'jf') {
      return '缴费方式支付'
    } else if (val === 'fastepay') {
      return '快e付支付'
    } else if (val === '') {
      return ''
    } else if (val === '') {
      return ''
    } else if (val === '') {
      return ''
    } else if (val === '') {
      return ''
    } else if (val === '') {
      return ''
    } else if (val === '') {
      return ''
    } else if (val === '') {
      return ''
    } else if (val === '') {
      return ''
    }
  }
}
