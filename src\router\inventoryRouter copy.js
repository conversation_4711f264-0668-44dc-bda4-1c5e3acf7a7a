// import Layout from '@/layout'
// 注意layout一定要最后生成再替换成import的方式引入，不然会触发页面重复渲染
let Layout = 'Layout'

// 进销存模块路由
const inventory = [
  { // 进销存
    path: '/inventory',
    name: 'Inventory',
    component: Layout,
    alwaysShow: true,
    redirect: '/inventory/warehouse_admin',
    meta: {
      title: 'inventory',
      noCache: true,
      permission: ['background_drp']
      // no_permission: true
    },
    children: [
      { // 仓库管理
        path: 'warehouse_admin',
        component: () =>
          import(
            /* webpackChunkName: "warehouse_admin" */ '@/views/merchant/inventory/warehouse-admin/WarehouseAdmin'
          ),
        name: 'WarehouseAdmin',
        meta: {
          noCache: true,
          title: 'warehouse_admin',
          permission: ['background_drp.warehouse.list']
        }
      },
      { // 单位管理
        path: 'unit_admin',
        component: () =>
          import(
            /* webpackChunkName: "inventory_unit_admin" */ '@/views/merchant/inventory/warehouse-admin/UnitAdmin'
          ),
        name: 'InventoryUnitAdmin',
        // hidden: true,
        meta: {
          noCache: true,
          title: 'inventory_unit_admin',
          permission: ['background_drp.unit_management.list']
        }
      },
      { // 库存管理
        path: 'inventory_management',
        component: () =>
          import(
            /* webpackChunkName: "inventory_management" */ '@/views/merchant/inventory/warehouse-admin/InventoryManagement'
          ),
        name: 'InventoryManagement',
        hidden: true,
        meta: {
          noCache: true,
          title: 'inventory_management',
          activeMenu: '/inventory/warehouse_admin',
          breadcrumb: true,
          permission: ['background_drp.inventory_info.list']
        },
        children: [
          { // 库存流水
            path: 'inventory_flow',
            component: () =>
              import(
                /* webpackChunkName: "Inventory_flow" */ '@/views/merchant/inventory/warehouse-admin/InventoryFlow'
              ),
            name: 'InventoryFlow',
            hidden: true,
            meta: {
              noCache: true,
              title: 'Inventory_flow',
              activeMenu: '/inventory/warehouse_admin',
              permission: ['background_drp.inventory_info.inventory_record']
            }
          },
          { // 盆点
            path: 'inventory_stock',
            component: () =>
              import(
                /* webpackChunkName: "inventory_stock" */ '@/views/merchant/inventory/warehouse-admin/InventoryStock/InventoryStock'
              ),
            name: 'InventoryStock',
            hidden: true,
            meta: {
              noCache: true,
              title: 'inventory_stock',
              activeMenu: '/inventory/warehouse_admin',
              permission: ['background_drp.warehouse.list']
            }
          },
          { // 盘点详情
            path: 'inventory_stock_detail',
            component: () =>
              import(
                /* webpackChunkName: "inventory_stock_detail" */ '@/views/merchant/inventory/warehouse-admin/InventoryStock/InventoryStockDetail'
              ),
            name: 'InventoryStockDetail',
            hidden: true,
            meta: {
              noCache: true,
              title: 'inventory_stock_detail',
              activeMenu: '/inventory/warehouse_admin',
              permission: ['background_drp.warehouse.list']
            }
          },
          { // 入库单
            path: 'inbound_order',
            component: () =>
              import(
                /* webpackChunkName: "inbound_order" */ '@/views/merchant/inventory/warehouse-admin/InboundOrder/InboundOrder'
              ),
            name: 'InboundOrder',
            hidden: true,
            meta: {
              noCache: true,
              title: 'inbound_order',
              activeMenu: '/inventory/warehouse_admin',
              permission: ['background_drp.inventory_info.inventory_enter_add']
            },
            children: [
              { // 添加入库单
                path: 'inbound_order_:type',
                component: () =>
                  import(
                    /* webpackChunkName: "add_inbound_order" */ '@/views/merchant/inventory/warehouse-admin/InboundOrder/AddInboundOrder'
                  ),
                name: 'AddInboundOrder',
                hidden: true,
                meta: {
                  noCache: true,
                  title: 'inbound_order',
                  activeMenu: '/inventory/warehouse_admin',
                  permission: ['background_drp.inventory_info.inventory_enter_add']
                }
              },
              { // 入库
                path: 'warehousing',
                component: () =>
                  import(
                    /* webpackChunkName: "warehousing" */ '@/views/merchant/inventory/warehouse-admin/InboundOrder/Warehousing'
                  ),
                name: 'Warehousing',
                hidden: true,
                meta: {
                  noCache: true,
                  title: 'warehousing',
                  activeMenu: '/inventory/warehouse_admin',
                  permission: ['background_drp.inventory_info.inventory_exit_add']
                }
              },
              { // 入库单详情
                path: 'inbound_detail',
                component: () =>
                  import(
                    /* webpackChunkName: "inbound_order_detail" */ '@/views/merchant/inventory/warehouse-admin/InboundOrder/InboundOrderDetail'
                  ),
                name: 'InboundOrderDetail',
                hidden: true,
                meta: {
                  noCache: true,
                  title: 'inbound_order_detail',
                  activeMenu: '/inventory/warehouse_admin',
                  permission: ['background_drp.inventory_info.inventory_exit_add']
                }
              }
            ]
          },
          { // 出库单
            path: 'outbound_order',
            component: () =>
              import(
                /* webpackChunkName: "outbound_order" */ '@/views/merchant/inventory/warehouse-admin/OutboundOrder/OutboundOrder'
              ),
            name: 'OutboundOrder',
            hidden: true,
            meta: {
              noCache: true,
              title: 'outbound_order',
              activeMenu: '/inventory/warehouse_admin',
              permission: ['background_drp.inventory_info.inventory_enter_add']
            }
          },
          { // 添加出库单
            path: 'outbound_order_:type',
            component: () =>
              import(
                /* webpackChunkName: "add_outbound_order" */ '@/views/merchant/inventory/warehouse-admin/OutboundOrder/AddOutboundOrder'
              ),
            name: 'AddOutboundOrder',
            hidden: true,
            meta: {
              noCache: true,
              title: 'outbound_order',
              activeMenu: '/inventory/warehouse_admin',
              permission: ['background_drp.inventory_info.inventory_exit_add']
            }
          }
        ]
      },
      { // 物资库
        path: 'material_warehouse',
        component: () =>
          import(
            /* webpackChunkName: "material_warehouse" */ '@/views/merchant/inventory/material-warehouse/MaterialWarehouse'
          ),
        name: 'MaterialWarehouse',
        meta: {
          noCache: true,
          title: 'material_warehouse',
          permission: ['background_drp']
        }
      },
      { // 供应商管理
        path: 'supplier_management',
        component: () =>
          import(
            /* webpackChunkName: "supplier_management" */ '@/views/merchant/inventory/supplier-management/SupplierManagement'
          ),
        name: 'SupplierManagement',
        meta: {
          noCache: true,
          title: 'supplier_management',
          permission: ['background_drp']
        }
      },
      { // 新建供应商
        path: 'supplier_management_:type',
        component: () =>
          import(
            /* webpackChunkName: "add_supplier_management" */ '@/views/merchant/inventory/supplier-management/AddSupplierManagement'
          ),
        name: 'AddSupplierManagement',
        hidden: true,
        meta: {
          activeMenu: '/inventory/supplier_management',
          noCache: true,
          title: 'supplier_management',
          permission: ['background_drp']
        }
      },
      { // 关联物资
        path: 'supplier_management/related_materials',
        component: () =>
          import(
            /* webpackChunkName: "related_materials" */ '@/views/merchant/inventory/supplier-management/RelatedMaterials'
          ),
        name: 'RelatedMaterials',
        hidden: true,
        meta: {
          activeMenu: '/inventory/supplier_management',
          noCache: true,
          title: 'related_materials',
          permission: ['background_drp']
        }
      },
      { // 采购清单明细
        path: 'pulchase_list_Details',
        component: () =>
          import(
            /* webpackChunkName: "pulchase_list_Details" */ '@/views/merchant/inventory/report/PurchaseListDetails'
          ),
        name: 'PurchaseListDetails',
        meta: {
          noCache: true,
          title: 'pulchase_list_Details',
          permission: ['background_drp']
        }
      }
    ]
  }
]
export default inventory
