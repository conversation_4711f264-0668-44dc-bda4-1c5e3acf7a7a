<template>
  <div class="PrintSetting container-wrapper" v-loading="isLoading">
    <div class="table-wrapper">
      <div class="table-header" style="display: flex; justify-content: space-between;">
        <div style="display: flex;align-items: center;">
          <div class="table-title">设备设置</div>
        </div>
        <div style="padding-right:20px;">
          <el-button size="small" type="primary" class="ps-origin-btn" @click="clickConfirmHandle">保存</el-button>
        </div>
      </div>
      <div class="table-content">
        <el-form
          :model="printForm"
          ref="printForm"
          inline
          label-width="80px"
          :rules="printFormRule"
        >
          <el-form-item label="消费点：" prop="organizationId">
            <!-- <tree-select
              class="w-250"
              :options="organizationList"
              :multiple="false"
              :limit="1"
              :limitText="count => '+' + count"
              :default-expand-level="1"
              placeholder="请选择消费点"
              :normalizer="normalizer"
              v-model="printForm.organizationId"
              :appendToBody="true"
              z-index='3000'
            >
            </tree-select> -->
            <consume-select
              class="ps-input w-250"
              v-model="printForm.organizationId"
              placeholder="请选择"
              :collapse-tags="true"
              ></consume-select>
          </el-form-item>
          <el-form-item label="设备类型" prop="deviceType">
            <el-select
              v-model="printForm.deviceType"
              placeholder="请选择设备类型"
              class="ps-input"
            >
              <el-option
                v-for="item in deviceTypeList"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div v-for="(item,index) in printCheckBoxData" :key="index" class="table-wrapper">
      <div class="table-header setting-content">
        <div class="table-title">{{item.label}}</div>
      </div>
      <div v-for="(typeItem,typeIndex) in item.type" :key="typeIndex" class="table-content">
        <div class="type-item">
          <div class="type-item-label" style="">{{typeItem.label}}</div>
          <el-checkbox class="ps-checkbox" v-model="typeItem.all" @change="changeAll(typeItem.all,typeItem.data)">全选</el-checkbox>
        </div>
        <div class="checkbox-wrap">
          <div class="checkbox-margin" v-for="(checkbox,checkboxIndex) in typeItem.data" :key="checkboxIndex">
            <el-checkbox class="ps-checkbox" v-model="checkbox.value" @change="changeOne(typeItem)">{{checkbox.label}}</el-checkbox>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import ConsumeSelect from '@/components/ConsumeSelect'

export default {
  name: 'PrintSetting',
  components: { ConsumeSelect },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      printForm: {
        organizationId: null,
        deviceType: '',
        deviceNo: ''
      },
      printFormRule: {
        deviceType: [{ required: true, message: '请输入设备类型', trigger: 'change' }],
        organizationId: [{ required: true, message: '请选择消费点', trigger: 'blur' }]
      },
      printCheckBoxData: {
        canteen: {
          label: '堂食打印设置',
          key: 'canteen',
          type: {
            order: {
              label: '结账单小票字段',
              key: 'order',
              all: false,
              data: {
                take_no: {
                  label: '取餐号',
                  value: false
                },
                code_verify: {
                  label: '核销码',
                  value: false
                },
                pay_time: {
                  label: '下单时间',
                  value: false
                },
                out_trade_no: {
                  label: '订单号',
                  value: false
                },
                food_info: {
                  label: '菜品信息',
                  value: false
                },
                payway: {
                  label: '支付方式',
                  value: false
                },
                dinning_table_no: {
                  label: '桌号',
                  value: false
                },
                remark: {
                  label: '备注',
                  value: false
                }
              }
            },
            back: {
              label: '后厨单小票字段',
              key: 'back',
              all: false,
              data: {
                take_no: {
                  label: '取餐号',
                  value: false
                },
                code_verify: {
                  label: '核销码',
                  value: false
                },
                pay_time: {
                  label: '下单时间',
                  value: false
                },
                out_trade_no: {
                  label: '订单号',
                  value: false
                },
                food_info: {
                  label: '菜品信息',
                  value: false
                },
                dinning_table_no: {
                  label: '桌号',
                  value: false
                },
                remark: {
                  label: '备注',
                  value: false
                }
              }
            }
          }
        },
        reserve: {
          label: '预约打印设置',
          key: 'reserve',
          type: {
            order: {
              label: '结账单小票字段',
              key: 'order',
              all: false,
              data: {
                take_no: {
                  label: '取餐号',
                  value: false
                },
                code_verify: {
                  label: '核销码',
                  value: false
                },
                pay_time: {
                  label: '下单时间',
                  value: false
                },
                out_trade_no: {
                  label: '订单号',
                  value: false
                },
                food_info: {
                  label: '菜品信息',
                  value: false
                },
                take_way: {
                  label: '取餐方式',
                  value: false
                },
                take_user: {
                  label: '取餐人',
                  value: false
                },
                mobile: {
                  label: '手机号',
                  value: false
                },
                payway: {
                  label: '支付方式',
                  value: false
                },
                remark: {
                  label: '备注',
                  value: false
                }
              }
            },
            back: {
              label: '后厨单小票字段',
              key: 'back',
              all: false,
              data: {
                take_no: {
                  label: '取餐号',
                  value: false
                },
                code_verify: {
                  label: '核销码',
                  value: false
                },
                pay_time: {
                  label: '下单时间',
                  value: false
                },
                out_trade_no: {
                  label: '订单号',
                  value: false
                },
                food_info: {
                  label: '菜品信息',
                  value: false
                },
                take_way: {
                  label: '取餐方式',
                  value: false
                },
                remark: {
                  label: '备注',
                  value: false
                }
              }
            }
          }
        },
        waimai: {
          label: '外卖打印设置',
          key: 'waimai',
          type: {
            order: {
              label: '结账单小票字段',
              key: 'order',
              all: false,
              data: {
                pay_time: {
                  label: '下单时间',
                  value: false
                },
                out_trade_no: {
                  label: '订单号',
                  value: false
                },
                food_info: {
                  label: '菜品信息',
                  value: false
                },
                take_user: {
                  label: '取餐人',
                  value: false
                },
                mobile: {
                  label: '手机号',
                  value: false
                },
                addr: {
                  label: '配送地址',
                  value: false
                },
                remark: {
                  label: '备注',
                  value: false
                }
              }
            },
            back: {
              label: '后厨单小票字段',
              key: 'back',
              all: false,
              data: {
                pay_time: {
                  label: '下单时间',
                  value: false
                },
                out_trade_no: {
                  label: '订单号',
                  value: false
                },
                food_info: {
                  label: '菜品信息',
                  value: false
                },
                remark: {
                  label: '备注',
                  value: false
                }
              }
            }
          }
        }
      },
      deviceTypeList: [],
      organizationList: [],
      type: 'add',
      deviceId: ''
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    $route: {
      handler: function(route) {
        if (this.$route.query.id) {
          this.getPrintDetail(this.$route.query.id)
          this.type = 'edit'
          this.deviceId = this.$route.query.id
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDeviceType()
      this.getOrganizationTreeList()
    },
    clickConfirmHandle() {
      this.$refs.printForm.validate(valid => {
        if (valid) {
          let printData = {}
          for (let key in this.printCheckBoxData) {
            for (let type in this.printCheckBoxData[key].type) {
              printData[key + '_' + type] = {}
              for (let data in this.printCheckBoxData[key].type[type].data) {
                if (this.printCheckBoxData[key].type[type].all) {
                  printData[key + '_' + type].all = this.printCheckBoxData[key].type[type].all
                } else {
                  printData[key + '_' + type][data] = this.printCheckBoxData[key].type[type].data[data].value
                }
              }
            }
          }
          let params = {
            data: printData,
            organization_id: this.printForm.organizationId,
            device_type: this.printForm.deviceType
          }
          if (this.type === 'add') {
            this.addPrintDevice(params)
          } else {
            params.conf_no = this.deviceId
            this.editPrintDevice(params)
          }
        } else {
        }
      })
    },
    async addPrintDevice(params) {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDevicePrintConfAddPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('新建成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    async editPrintDevice(params) {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDevicePrintConfModifyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('编辑成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    changeAll(value, data) {
      for (let key in data) {
        data[key].value = value
      }
    },
    changeOne(item) {
      // 变成数组
      let arr = Object.values(item.data)
      if (arr.every(item => item.value)) {
        item.all = true
      } else {
        item.all = false
      }
    },
    getValue(value) {
      this.printForm.organizationId = value
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        this.deviceTypeList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取打印设备详情
    async getPrintDetail(id) {
      const res = await this.$apis.apiBackgroundDeviceDevicePrintConfConfDeatailPost({
        device_no: id
      })
      if (res.code === 0) {
        this.printForm.organizationId = res.data.organization_no
        this.printForm.deviceType = res.data.device_type
        for (let key in res.data.data) {
          let typeList = this.printCheckBoxData[key.split('_')[0]].type[key.split('_')[1]]
          for (let data in typeList.data) {
            typeList.all = res.data.data[key].all
            typeList.data[data].value = res.data.data[key][data]
          }
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取组织结构tree表
    async getOrganizationTreeList() {
      const res = await this.$apis.apiBackgroundOrganizationOrganizationTreeListPost()
      if (res.code === 0) {
        this.organizationList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children_list
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.PrintSetting{
  .setting-content{
    margin-bottom: 0!important;
  }
  .type-item{
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    .type-item-label{
      margin-right: 30px;
      font-weight: bold;
      font-size: 15px;
    }
  }
  .checkbox-wrap{
    display: flex;
    flex-wrap: wrap;
    .checkbox-margin{
      margin: 0 0 10px 30px;
    }
  }
}
</style>
