import { getDateRang,getSevenDateRange } from '@/utils'

export const MEAL_TYPES = [
  { label: '早餐', value: 'breakfast', field: 'breakfast_ahead_stop', field2: 'breakfast_ahead_resume' },
  { label: '午餐', value: 'lunch', field: 'lunch_ahead_stop', field2: 'lunch_ahead_resume' },
  { label: '下午茶', value: 'afternoon', field: 'afternoon_ahead_stop', field2: 'afternoon_ahead_resume' },
  { label: '晚餐', value: 'dinner', field: 'dinner_ahead_stop', field2: 'dinner_ahead_resume' },
  { label: '夜宵', value: 'supper', field: 'supper_ahead_stop', field2: 'supper_ahead_resume' },
  { label: '凌晨餐', value: 'morning', field: 'morning_ahead_stop', field2: 'morning_ahead_resume' }
]

// 导入报餐绑定 表单表头
export const TABLE_HEAD_DATA_IMPORT_MEAL = [
  { label: '姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '分组', key: 'payer_group' },
  { label: '部门', key: 'department_group' },
  { label: '消费点', key: 'organization' },
  { label: '报餐时间', key: 'report_date' },
  { label: '报餐餐段', key: 'meal_type' },
  { label: '份数', key: 'count' },
  { label: '取餐方式', key: 'take_meal_type' }
]

// 餐包明细搜索
export const MEAL_PACKAGE_SEARCH = {
  // report_meal_pack_settings_id: { // 有默认值的不能应懒加载
  //   type: 'lazySelect',
  //   label: '餐包名称',
  //   clearable: true,
  //   placeholder: '请选择',
  //   value: '',
  //   apiUrl: 'apiBackgroundReportMealReportMealPackSettingsListPost',
  //   params: {},
  //   isLazy: true,
  //   multiple: false,
  //   hasDefault: true
  // },
  report_meal_pack_settings_id: {
    type: 'select',
    label: '餐包名称',
    value: '',
    placeholder: '请选择',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: []
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择分组'
  },
  payer_department_group_ids: {
    type: 'departmentSelect',
    multiple: true,
    isLazy: false,
    checkStrictly: true,
    collapseTags: true,
    label: '部门',
    value: [],
    placeholder: '请选择部门'
  },
  name: {
    type: 'input',
    label: '姓名',
    value: '',
    placeholder: '请输入姓名'
  },
  person_no: {
    type: 'input',
    label: '人员编号',
    value: '',
    placeholder: '请输入人员编号'
  },
  phone: {
    type: 'input',
    label: '手机号',
    value: '',
    maxlength: 11,
    placeholder: '请输入手机号'
  },
  buy_status: {
    type: 'select',
    value: [],
    label: '购买状态',
    multiple: true,
    collapseTags: true,
    dataList: [
      { label: '已购买', value: 'buy' },
      { label: '未购买', value: 'not_buy' },
      { label: '已退款', value: 'refund' }
    ]
  }
}

// 餐包明细table
export const MEAL_PACKAGE_TABLE = [
  { label: '餐包名称', key: 'report_meal_pack_settingsname' },
  { label: '姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'card_user_group_alias', showTooltip: true  },
  { label: '部门', key: 'department_group_name' },
  { label: '购买状态', key: 'order_status_alias' },
  { label: '购买日期', key: 'pay_time', isComponents: true, type: 'date', format: 'YYYY-MM-DD' },
  { label: '购买价格', key: 'real_fee', type: 'money' },
  { label: '退款日期', key: 'refund_time', isComponents: true, type: 'date', format: 'YYYY-MM-DD' },
  {
    label: '使用情况',
    key: 'operation',
    type: 'slot',
    slotName: 'operation',
    fixed: 'right',
    width: '80'
  }
]

export const DEPARTMENT_SUMMARY = {
  select_time: {
    type: 'daterange',
    label: '日期',
    value: getSevenDateRange(1),
    format: 'yyyy-MM-dd',
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  group_ids: {
    type: 'departmentSelect',
    multiple: true,
    isLazy: false,
    checkStrictly: true,
    collapseTags: true,
    label: '部门',
    value: [],
    placeholder: '请选择部门'
  }
}

export const GROUP_SUMMARY = {
  select_time: {
    type: 'daterange',
    label: '日期',
    value: getSevenDateRange(1),
    format: 'yyyy-MM-dd',
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  group_ids: {
    type: 'groupSelect',
    multiple: true,
    isLazy: false,
    checkStrictly: true,
    collapseTags: true,
    label: '分组',
    value: [],
    placeholder: '请选择分组'
  }
}
