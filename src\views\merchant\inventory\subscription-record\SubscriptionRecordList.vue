<template>
  <div class="procure-order-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入

export default {
  name: 'ProcureOfferList',
  mixins: [exportExcel],
  components: { },
  data() {
    return {
      isLoading: false, // 刷新数据
      warehouseId: this.$route.query.warehouse_id,
      tabType: 1,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '创建时间', key: 'create_time' },
        { label: '单据编号', key: 'trade_no' },
        { label: '所属组织', key: 'organization_name' },
        { label: '处理结果', key: 'deal_result_alias' }, // , isComponents: true, type: 'date', format: 'YYYY-MM-DD'
        { label: '申请人', key: 'subscribe_person_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '创建时间',
          clearable: false,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        },
        organization_ids: {
          type: 'organizationSelect',
          value: [],
          label: '所属组织',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: [],
          multiple: true,
          checkStrictly: true,
          collapseTags: true,
          clearable: true
        },
        deal_result: {
          type: 'select',
          label: '处理结果',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '转采购单',
              value: 'to_purchase_info'
            },
            {
              label: '转出库单',
              value: 'to_exit_info'
            }
          ]
        },
        subscribe_person_name: {
          type: 'input',
          value: '',
          label: '申请人',
          placeholder: '请输入'
        }
      },
      // 草稿弹窗
      showDraft: false,
      drafttableSettings: [
        { label: '采购单名称', key: 'name' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right' }
      ],
      draftApi: 'apiBackgroundDrpPurchaseInfoTempListPost',
      draftParams: {
        warehouse_id: +this.$route.query.warehouse_id,
        inventory_info_type: 'purchase',
        temp_type: 'draft'
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.getSubscriptionRecordList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getSubscriptionRecordList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        // audit_status: 'approve',
        ...this.formatQueryParams(this.searchFormSetting),
        approve_status: 'AGREE',
        subscribe_status: 'initiated',
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoListPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSubscriptionRecordList()
    },
    gotoHandle(type, row) {
      let query = {
        ...this.$route.query
      }
      if (row) {
        query.id = row.id
      }
      this.$router.push({
        name: 'SubscriptionRecordDetail',
        query: query,
        params: {
          type
        }
      })
    },
    // 草稿恢复编辑
    recoveryHandle(data) {
      let query = {
        ...this.$route.query,
        data: JSON.stringify(data.extra.supplier_manage_info)
      }
      this.$router.push({
        name: 'ModifyPurchaseOrder',
        query: query,
        params: {
          type: 'modify'
        }
      })
    }
    // handleExport(row) {
    //   const option = {
    //     type: 'InquiryList',
    //     url: 'apiBackgroundDrpInquiryExportInquiryPost',
    //     params: {
    //       id: row.id
    //     }
    //   }
    //   this.exportHandle(option)
    // }
  }
}
</script>

<style lang="scss" scoped>
.procure-order-list{
  .w-medium{
    width: 140px;
    height: 40px;
    &.m-r-20{
      margin-right: 20px;
    }
  }
}
</style>
