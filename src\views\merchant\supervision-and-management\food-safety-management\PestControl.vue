<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="true"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="gotoExport" v-permission="['background_fund_supervision.canteen_safety_management.pest_control_record_export']">导出</button-icon>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="plain" @click="openDrawer('add')" v-permission="['background_fund_supervision.canteen_safety_management.add_pest_control_record']">新增</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #prevent_area="{ row }">
              {{ computedData(row.prevent_area) }}m²
            </template>
            <template #prevent_duration="{ row }">
              {{ computedData(row.prevent_duration) }}h
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="handleClick(row)">图片</el-button>
              <el-button type="text" size="small" class="ps-text" @click="openDrawer('edit', row)" v-permission="['background_fund_supervision.canteen_safety_management.add_pest_control_record']">复制新增</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'新增防制记录'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="drawerFormRef" :model="drawerForm" :rules="drawerFormRules" label-width="130px" label-position="right">
            <el-form-item :label="'防制时间'" prop="prevent_time">
              <el-date-picker class="w-300" v-model="drawerForm.prevent_time" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择防制时间" :picker-options="pickerOptions"></el-date-picker>
            </el-form-item>
            <el-form-item :label="'防制区域'" prop="prevent_region">
              <el-input v-model="drawerForm.prevent_region" class="w-300" placeholder="请输入防制区域，不超过30个字" maxlength="30"></el-input>
            </el-form-item>
            <el-form-item :label="'区域面积'" prop="prevent_area">
              <el-input v-model="drawerForm.prevent_area" class="w-300" placeholder="默认单位m²，支持小数点2位数" maxlength="20" type="number"></el-input>
            </el-form-item>
            <el-form-item :label="'防治措施'" prop="measure">
              <el-select v-model="drawerForm.measure" placeholder="请选择" class="w-300">
                <el-option
                  v-for="(item, index) in controlMeasure"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="'有害生物类别'" prop="category">
              <el-select v-model="drawerForm.category" placeholder="请选择" class="w-300">
                <el-option
                  v-for="(item, index) in pestClass"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="'使用药物'" prop="using_drug">
              <el-input v-model="drawerForm.using_drug" class="w-300" placeholder="请输入使用药物，不超过30个字" maxlength="30"></el-input>
            </el-form-item>
            <el-form-item :label="'药物用量'" prop="drug_dosage">
              <el-input v-model="drawerForm.drug_dosage" class="w-300" placeholder="例如10g、10张" maxlength="10"></el-input>
            </el-form-item>
            <el-form-item :label="'防制时长'" prop="prevent_duration">
              <el-input v-model="drawerForm.prevent_duration" class="w-300" placeholder="默认单位h，支持小数点2位数" maxlength="20" type="number"></el-input>
            </el-form-item>
            <el-form-item :label="'防制人员'" prop="prevent_accounts">
              <el-input v-model="drawerForm.prevent_accounts" class="w-300" placeholder="请输入防制人员，不超过10个字" maxlength="10"></el-input>
            </el-form-item>
            <el-form-item :label="'监督人'" prop="supervisor_accounts">
              <el-input v-model="drawerForm.supervisor_accounts" class="w-300" placeholder="请输入监督人，不超过10个字" maxlength="10"></el-input>
            </el-form-item>
            <el-form-item :label="'责任人'" prop="responsible_account">
              <el-input v-model="drawerForm.responsible_account" class="w-300" placeholder="请输入责任人，不超过10个字" maxlength="10"></el-input>
            </el-form-item>
            <el-form-item :label="'备注'" prop="remark">
              <el-input v-model="drawerForm.remark" class="w-300" placeholder="请输入备注，不超过50个字" maxlength="50"></el-input>
            </el-form-item>
            <el-form-item :label="'图片（上限10张）'" prop="images">
              <el-upload
                class="avatar-uploader"
                v-loading="uploading"
                element-loading-text="上传中"
                :data="uploadParams"
                list-type="picture-card"
                ref="uploadFood"
                :limit="10"
                :action="serverUrl"
                :on-success="uploadSuccess"
                :before-upload="beforeImgUpload"
                :headers="headersOpts"
                :file-list="fileLists"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="cancelHandle">取消</el-button>
            <el-button class="w-100 ps-btn" @click="confirmHandle">确认</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="30%">
      <el-carousel trigger="click" indicator-position="outside" :loop='false'>
        <el-carousel-item v-for="(item, index) in previewList" :key="index">
          <div class="flex-center w-100-p">
            <el-image
              style="height: 350px"
              :src="item"
              :fit="'contain'"></el-image>
          </div>
        </el-carousel-item>
      </el-carousel>
      <template slot="footer">
        <div class="flex-center">
          <el-button @click="dialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { debounce, getToken, deepClone, divide, times } from '@/utils'
import { CONTROL_MEASURE_LIST, PEST_CLASS_LIST } from './constants'
import dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  mixins: [exportExcel],
  data() {
    const checkoutLimit = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('年龄不能为空'));
      } else if (value >= 100000) {
        return callback(new Error('最大值不能超过100000'));
      } else {
        callback();
      }
    };
    return {
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'create_date',
          label: '',
          maxWidth: '100px',
          dataList: [
            {
              label: '创建时间',
              value: 'create_date'
            },
            {
              label: '防制时间',
              value: 'prevent_date'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          value: [
            dayjs()
              .subtract(7, 'day')
              .format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD')
          ],
          clearable: false
        },
        org_ids: {
          type: 'organizationSelect',
          value: [],
          label: '所属食堂',
          listNameKey: 'name',
          listValueKey: 'id',
          checkStrictly: true,
          multiple: true,
          collapseTags: true,
          clearable: true
        },
        measures: {
          type: 'select',
          value: '',
          label: '防制措施',
          dataList: CONTROL_MEASURE_LIST,
          clearable: true,
          multiple: true,
          collapseTags: true
        },
        categorys: {
          type: 'select',
          value: '',
          labelWidth: '120px',
          label: '有害生物类别',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            ...PEST_CLASS_LIST
          ],
          clearable: true,
          multiple: true,
          collapseTags: true
        },
        name: {
          type: 'input',
          value: '',
          label: '防制人员/监督人/责任人',
          labelWidth: '165px',
          dataList: [],
          placeholder: '请输入',
          clearable: true
        }
      },
      isLoading: false,
      tableData: [],
      controlMeasure: CONTROL_MEASURE_LIST,
      pestClass: PEST_CLASS_LIST,
      tableSetting: [
        { label: '创建时间', key: 'create_time', width: '100px' },
        { label: '防制时间', key: 'prevent_time', width: '100px' },
        { label: '所属食堂', key: 'organization_name' },
        { label: '防制区域', key: 'prevent_region', showTooltip: true },
        { label: '区域面积', key: 'prevent_area', type: 'slot', slotName: 'prevent_area' },
        { label: '防治措施', key: 'measure_alias' },
        { label: '有害生物类别', key: 'category_alias' },
        { label: '使用药物', key: 'using_drug', showTooltip: true },
        { label: '药物用量', key: 'drug_dosage' },
        { label: '防制时长/h', key: 'prevent_duration', type: 'slot', slotName: 'prevent_duration' },
        { label: '防制人员', key: 'prevent_accounts', showTooltip: true },
        { label: '监督人', key: 'supervisor_accounts', showTooltip: true },
        { label: '责任人', key: 'responsible_account', showTooltip: true },
        { label: '备注', key: 'remark', showTooltip: true },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', width: '120px' }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      drawerShow: false,
      drawerForm: {
        prevent_time: '',
        prevent_region: '',
        prevent_area: '',
        measure: '',
        category: '',
        using_drug: '',
        drug_dosage: '',
        prevent_duration: '',
        prevent_accounts: '',
        supervisor_accounts: '',
        responsible_account: '',
        remark: '',
        images: []
      },
      drawerFormRules: {
        prevent_time: [
          { required: true, message: '请输入防制时间', trigger: ['change', 'blur'] }
        ],
        prevent_region: [
          { required: true, message: '请输入防制区域', trigger: ['change', 'blur'] }
        ],
        prevent_area: [
          { required: true, message: '请输入区域面积', trigger: ['change', 'blur'] },
          {
            pattern: /^(0|([1-9]\d*))(\.\d{1,2})?$/, // 正则表达式
            message: '最多允许两位小数',
            trigger: ['blur', 'change']
          },
          { validator: checkoutLimit, trigger: ['change', 'blur'] }
        ],
        measure: [
          { required: true, message: '请输入防治措施', trigger: ['change', 'blur'] }
        ],
        category: [
          { required: true, message: '请输入有害生物类别', trigger: ['change', 'blur'] }
        ],
        using_drug: [
          { required: true, message: '请输入使用药物', trigger: ['change', 'blur'] }
        ],
        drug_dosage: [
          { required: true, message: '请输入药物用量', trigger: ['change', 'blur'] }
        ],
        prevent_duration: [
          { required: true, message: '请输入防制时长', trigger: ['change', 'blur'] },
          {
            pattern: /^(0|([1-9]\d*))(\.\d{1,2})?$/, // 正则表达式
            message: '最多允许两位小数',
            trigger: ['blur', 'change']
          },
          { validator: checkoutLimit, trigger: ['change', 'blur'] }
        ],
        prevent_accounts: [
          { required: true, message: '请输入防制人员', trigger: ['change', 'blur'] }
        ],
        supervisor_accounts: [
          { required: true, message: '请输入监督人', trigger: ['change', 'blur'] }
        ],
        responsible_account: [
          { required: true, message: '请输入责任人', trigger: ['change', 'blur'] }
        ],
        remark: [
          { required: false, message: '请输入备注', trigger: ['change', 'blur'] }
        ],
        images: [
          { required: true, message: '请上传图片', trigger: ['change', 'blur'] }
        ]
      },
      uploading: false,
      uploadParams: {
        prefix: 'super_food_img'
      },
      fileLists: [],
      serverUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      previewList: [],
      dialogTitle: '',
      dialogVisible: false,
      printType: 'PestControlRecord',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    }
  },
  computed: {
    computedData() {
      return d => {
        return divide(d, 100)
      }
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      this.currentPage = 1;
      this.getDataList()
      // if (e && e === 'search') {
      // }
    }, 300),
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.getDataList()
    }, // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList()
    },
    getDataList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      this.$apis.apiBackgroundFundSupervisionCanteenSafetyManagementPestControlRecordPost(params).then(res => {
        if (res.code === 0) {
          this.isLoading = false
          this.totalCount = res.data.count
          this.tableData = deepClone(res.data.results || [])
        }
      })
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value !== '全部' && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        } else {
          params[key] = undefined
        }
      }
      return params
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        console.log('file', file, fileList)
        let arr = []
        fileList.forEach(item => {
          arr.push(item.response.data.public_url)
        })
        this.fileLists = deepClone(fileList)
        this.drawerForm.images = deepClone(arr)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 图片上传前检测
    beforeImgUpload(file) {
      // 文件校验
      const fileType = ['jpeg', 'jpg', 'png', 'bmp', 'tiff', "JPEG", "PNG", "BMP", "TIFF", "HEIF", "JPG"]
      const isLt2M = file.size / 1024 / 1024 <= 20
      let result = file.type.split("/")[1]
      if (this.fileLists.length >= 10) {
        this.$message.error('最多上传10张图片')
        return false
      }
      if (!fileType.includes(result)) {
        this.$message.error('请上传正确的文件')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },
    cancelHandle() {
      this.fileLists = []
      this.$refs.drawerFormRef.resetFields()
      this.drawerShow = false
    },
    confirmHandle() {
      this.$refs.drawerFormRef.validate(valid => {
        if (valid) {
          let params = {
            ...this.drawerForm
          }
          params.prevent_area = times(this.drawerForm.prevent_area, 100)
          params.prevent_duration = times(this.drawerForm.prevent_duration, 100)
          this.$apis.apiBackgroundFundSupervisionCanteenSafetyManagementAddPestControlRecordPost(params).then(res => {
            if (res.code === 0) {
              this.$message.success('新增成功')
              this.cancelHandle()
              this.getDataList()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.$message.error('请检查表单是否填写正确')
        }
      })
    },
    // 打开弹窗
    openDrawer(type, data) {
      if (type === 'edit') {
        for (let key in this.drawerForm) {
          this.drawerForm[key] = data[key]
        }
        this.drawerForm.prevent_area = divide(this.drawerForm.prevent_area, 100)
        this.drawerForm.prevent_duration = divide(this.drawerForm.prevent_duration, 100)
        this.drawerForm.prevent_time = ''
        this.drawerForm.remark = ''
        this.drawerForm.images = []
      } else {
        let obj = {
          prevent_time: '',
          prevent_region: '',
          prevent_area: '',
          measure: '',
          category: '',
          using_drug: '',
          drug_dosage: '',
          prevent_duration: '',
          prevent_accounts: '',
          supervisor_accounts: '',
          responsible_account: '',
          remark: '',
          images: []
        }
        this.drawerForm = { ...obj }
      }
      this.drawerShow = true
      setTimeout(() => {
        this.$refs.drawerFormRef.clearValidate()
      }, 10)
    },
    handleClick(data) {
      this.previewList = [...data.images]
      this.dialogTitle = `${data.prevent_region}-${data.measure_alias}-${data.category_alias}`
      this.dialogVisible = true
    },
    closePreview() {
      this.previewList = []
      this.dialogVisible = false
    },
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const option = {
        url: 'apiBackgroundFundSupervisionCanteenSafetyManagementPestControlRecordExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let currentTableSetting = [
        { label: '创建时间', key: 'create_time', width: '120px' },
        { label: '防制时间', key: 'prevent_time', width: '120px' },
        { label: '所属食堂', key: 'organization_name' },
        { label: '防制区域', key: 'prevent_region' },
        { label: '区域面积', key: 'prevent_area', type: 'slot', slotName: 'prevent_area' },
        { label: '防治措施', key: 'measure_alias' },
        { label: '有害生物类别', key: 'category_alias' },
        { label: '使用药物', key: 'using_drug' },
        { label: '药物用量', key: 'drug_dosage' },
        { label: '防制时长/h', key: 'prevent_duration', type: 'slot', slotName: 'prevent_duration' },
        { label: '防制人员', key: 'prevent_accounts' },
        { label: '监督人', key: 'supervisor_accounts' },
        { label: '责任人', key: 'responsible_account' },
        { label: '备注', key: 'remark' }
      ]
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_type: this.printType,
          print_title: '有害生物防制表',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionCanteenSafetyManagementPestControlRecordPost', // 请求的api
          current_table_setting: JSON.stringify(currentTableSetting),
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          params: JSON.stringify({ ...params })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
</style>
