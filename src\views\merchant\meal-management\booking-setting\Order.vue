<template>
  <div class="booking-meal-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      @search="searchHandle"
      :form-setting="searchFormSetting"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <el-button size="mini" color="plain" icon="el-icon-plus" @click="addBookingOrder" v-permission="['background_reservation.background_reservation_settings.add']">
            新建预约点餐
          </el-button>
          <el-button size="mini" color="plain" type="danger" @click="deleteHandler(1)" v-permission="['background_reservation.background_reservation_settings.delete']">
            删除
          </el-button>
        </div>
      </div>

      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="bookingTable"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <el-table-column align="center" prop="organization_alias" label="组织"></el-table-column>
          <el-table-column align="center" prop="consume_organizations_alias" label="适用消费点">
            <template slot-scope="scope">
              <el-tag
                v-for="item in scope.row.consume_organizations_alias"
                style="margin-right: 8px"
                :key="item"
              >
                {{ item }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="card_user_groups_alias" label="适用分组">
            <template slot-scope="scope">
              <el-tag
                v-for="item in scope.row.card_user_groups_alias"
                style="margin-right: 8px"
                :key="item"
              >
                {{ item }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="menu_type_alias" label="适用餐段">
            <template slot-scope="scope">
              <el-tag
                v-for="item in scope.row.meal_type_detail.meal_type_verbose"
                style="margin: 0 8px 8px 0"
                :key="item"
              >
                {{ item }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column align="center" prop="create_time" label="创建时间"></el-table-column>
          <el-table-column label="状态" prop="status" width="180" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.is_open"
                @change="modifyStatus($event, scope.row.id)"
                :disabled="!allPermissions.includes('background_reservation.background_reservation_settings.modify_open_status')"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="addBookingOrder(scope.row)"
                v-permission="['background_reservation.background_reservation_settings.modify']"
              >
                编辑
              </el-button>
              <el-button type="text" size="small" @click="deleteHandler(0, scope.row.id)" v-permission="['background_reservation.background_reservation_settings.delete']">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <common-pagination
        ref="pagination"
        :total="total"
        :onPaginationChange="onPaginationChange"
      ></common-pagination>
    </div>
  </div>
</template>

<script>
import { to, debounce } from '@/utils'
import CommonPagination from './CommonPagination'
import { mapGetters } from 'vuex'
export default {
  name: 'Order',
  props: {},
  components: {
    CommonPagination
  },
  mounted() {
    this.initLoad()
  },
  computed: {
    ...mapGetters(['allPermissions'])
  },
  data() {
    return {
      card_user_groups: [],
      searchFormSetting: {
        card_user_group_ids: {
          type: 'select',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          dataList: [],
          listNameKey: 'group_name',
          listValueKey: 'id',
          multiple: true,
          collapseTags: true,
          filterable: true
        },
        consume_organizations: {
          type: 'organizationSelect',
          label: '消费点',
          value: [],
          placeholder: '请选择消费点',
          dataList: [],
          listNameKey: 'name',
          listValueKey: 'id',
          multiple: true,
          checkStrictly: true,
          collapseTags: true
        }
      },
      groupList: [],
      // loading状态
      isLoading: false,
      // 表格数据
      tableData: [],
      // 每页数量
      pageSize: 10,
      // 总条数
      total: 0,
      // 第几页
      currentPage: 1,
      value: ''
    }
  },
  methods: {
    initLoad() {
      this.userGroupList()
      this.requestListData()
    },

    // 获取表格数据
    async requestListData() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundReservationBackgroundReservationSettingsListPost({
          page: this.currentPage,
          page_size: this.pageSize,
          card_user_groups: this.searchFormSetting.card_user_group_ids.value,
          consume_organizations: this.searchFormSetting.consume_organizations.value
        })
      )

      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
        console.log(err)
      }
    },

    // 防抖
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.$refs.pagination.handleCurrentChange(1, true)
        this.$refs.pagination.handleSizeChange(10, true)
        this.onPaginationChange({ current: 1, pageSize: 10 })
      }
    }, 300),

    // 刷新处理
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.onPaginationChange({ current: 1, pageSize: 10 })
    },

    onPaginationChange(data) {
      this.pageSize = data.pageSize
      this.currentPage = data.current
      this.requestListData()
    },

    // 新增预约点餐
    addBookingOrder(row) {
      this.$router.push({
        name: 'MerchantAddBookingOrder',
        query: {
          data: row.isTrusted ? '' : JSON.stringify(row)
        }
      })
    },

    // 获取分组信息
    async userGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        page: 1,
        page_size: 9999999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.groupList = res.data.results
        this.searchFormSetting.card_user_group_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除数据
    async deleteHandler(type, id) {
      let delId
      if (type === 0) {
        delId = id
      } else {
        const selectData = this.$refs.bookingTable.selection
        console.log(selectData)
        if (selectData.length === 0) {
          this.$message({ message: '请选择需要删除的数据', type: 'warning' })
          return
        }
        delId = selectData.map(d => d.id)
      }
      this.$confirm('是否删除此数据？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.cancelButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundReservationBackgroundReservationSettingsDeletePost({
                ids: type === 0 ? [delId] : delId
              })
            )
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success('删除成功')
              this.currentPage = 1
              this.requestListData()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            done()
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },

    // 修改状态
    async modifyStatus(isOpen, id) {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReservationBackgroundReservationSettingsModifyOpenStatusPost(
        { id, is_open: isOpen }
      )
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('操作成功！')
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.search-wrapper {
  padding-left: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .left-part {
    display: flex;
    align-items: center;

    .search-item {
      display: flex;
      align-items: center;
      margin-right: 12px;

      .item-label {
        font-size: 12px;
      }
    }
  }
}
</style>
