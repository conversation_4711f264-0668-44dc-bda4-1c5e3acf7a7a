<template>
  <div class="MealOrderDetail" v-loading="isLoading">
    <div class="info">
      <div class="user_info">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">基本信息</span>
        </div>
        <div class="content">
          <div class="user_image">
            <el-image :src="orderData.card_face_url?orderData.card_face_url:require('@/assets/img/man.png')" style="width: 80px; height: 50px" fit="contain" :preview-src-list="[orderData.card_face_url]">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
          <ul>
            <li>
              <span>姓名:</span>
              <span>{{ orderData.name }}</span>
            </li>
            <li>
              <span>手机号:</span>
              <span>{{ orderData.phone }}</span>
            </li>
            <li>
              <span>分组:</span>
              <span>{{ orderData.payer_group_name }}</span>
            </li>
            <li>
              <span>人群:</span>
              <span></span>
            </li>
            <li>
              <span>人员编号:</span>
              <span>{{ orderData.person_no }}</span>
            </li>
            <li>
              <span>卡号:</span>
              <span>{{ orderData.card_no }}</span>
            </li>
            <li>
              <span>部门:</span>
              <span>{{ orderData.payer_department_group_name }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div class="order_info">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">订单信息</span>
        </div>
        <ul>
          <li>
            <span>总订单号:</span>
            <span>{{ orderData.unified_out_trade_no }}</span>
          </li>
          <li>
            <span>订单号:</span>
            <span>{{ orderData.trade_no }}</span>
          </li>
          <li>
            <span>创建时间:</span>
            <span>{{ orderData.create_time }}</span>
          </li>
          <li>
            <span>支付时间:</span>
            <span>{{ orderData.pay_time }}</span>
          </li>
          <li v-if="$route.query.type==0">
            <span>扣款时间:</span>
            <span>{{ orderData.deduction_time }}</span>
          </li>
          <li>
            <span>第三方订单号:</span>
            <span>{{ orderData.out_trade_no }}</span>
          </li>
        </ul>
        <ul>
          <li>
            <span>消费点:</span>
            <span>{{ orderData.consumption_name }}</span>
          </li>
          <li>
            <span>餐段:</span>
            <span>{{ orderData.meal_type_alias }}</span>
          </li>
          <li v-show="$route.query.id == 1">
            <span>取餐方式:</span>
            <span>{{ orderData.take_meal_type_alias }}</span>
          </li>
          <li v-show="$route.query.id == 1">
            <span>配送地址:</span>
            <span>{{ orderData.adders_name }}</span>
          </li>
        </ul>
        <ul>
          <li>
            <span>订单金额:</span>
            <span>
              <i>￥</i>
              {{ orderData.origin_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>优惠金额:</span>
            <span>
              <i>￥</i>
              {{ orderData.discount_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>餐补金额:</span>
            <span>
              <i>￥</i>
              {{ orderData.food_subsidy_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>补贴消费:</span>
            <span>
              <i>￥</i>
              {{ orderData.subsidy_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>实收金额:</span>
            <span>
              <i>￥</i>
              {{ orderData.pay_fee | formatMoney }}
            </span>
          </li>
          <li>
            <span>优惠类型:</span>
            <span>{{ orderData.discount_type }}</span>
          </li>
          <li>
            <span>动账钱包:</span>
            <span>{{ orderData.wallet }}</span>
          </li>
        </ul>
        <ul>
          <li>
            <span>支付类型:</span>
            <span>{{ orderData.sub_payway_alias }}</span>
          </li>
          <li>
            <span>支付状态:</span>
            <span>{{ orderData.order_status_alias }}</span>
          </li>
          <li>
            <span>设备状态:</span>
            <span>{{ orderData.pay_device_status_alias }}</span>
          </li>
          <li>
            <span>对账状态:</span>
            <span>{{ orderData.settle_status_alias }}</span>
          </li>
        </ul>
      </div>
      <div class="shebei_info">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">设备信息</span>
        </div>
        <table border="1">
          <tbody align="center">
            <tr>
              <th>设备类型</th>
              <th>设备号</th>
              <th>设备名</th>
              <!-- <th>操作员</th> -->
            </tr>
            <tr>
              <td>{{ orderData.device_type }}</td>
              <td>{{ orderData.device_number }}</td>
              <td>{{ orderData.device_name }}</td>
              <!-- <td></td> -->
            </tr>
          </tbody>
        </table>
      </div>
      <div class="menu_info">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">菜品信息</span>
        </div>
        <el-table
          :data="foodData"
          ref="foodData"
          style="width:800px"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column label="图片" prop="food_img" align="center">
            <template slot-scope="scope">
              <el-image :src="scope.row.food_img? scope.row.food_img: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'" :preview-src-list="[scope.row.food_img]">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column label="菜品名称" prop="food_name" align="center"></el-table-column>
          <el-table-column label="销售价格" prop="raw_fee" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.raw_fee | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="count" align="center"></el-table-column>
          <el-table-column label="重量" prop="weight" align="center"></el-table-column>
          <el-table-column label="消费金额" prop="real_fee" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.real_fee | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column label="营养详情" prop="" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="showDialogNutrition(scope.row)"
              >
                查看
              </el-button>
            </template>

          </el-table-column>
        </el-table>
      </div>
      <div class="shebei_info" v-if="orderData.food_scene_image">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">菜品实况图</span>
        </div>
        <div>
          <img style="width: 40%;" :src="orderData.food_scene_image" alt="">
        </div>
      </div>
      <div v-if="orderData.sub_payway == 'facepay'" class="face_info" v-loading="checkFaceLoading">
        <div class="l-title clearfix">
          <span class="float-l min-title-h title">人脸支付信息</span>
        </div>
        <div class="face-wrapper m-t-20">
          <span class="face-label vertical-t">刷脸照片:</span>
          <el-image :src="orderData.face_url" class="face-img m-l-10 m-r-10" fit="cover" :preview-src-list="[orderData.face_url]">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <el-button class="vertical-t" type="primary" size="small" @click="checkFaceHandle">在线校验</el-button>
          <div class="face-table-wrapper m-t-20 clearfix">
            <div class="float-l face-table m-r-20 m-b-20">
              <div class="m-b-15">刷脸结果</div>
              <el-table
                :data="trackInfo"
                stripe
                header-row-class-name="ps-table-header-row"
              >
                <el-table-column type="index" label="排序" :index="indexMethod" align="center"></el-table-column>
                <el-table-column label="近似人脸" prop="face_url" align="center">
                  <template slot-scope="scope">
                    <el-image :src="scope.row.face_url" :preview-src-list="[scope.row.face_url]">
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </template>
                </el-table-column>
                <el-table-column label="人员编号" prop="member_card" align="center"></el-table-column>
                <el-table-column label="姓名" prop="card_name" align="center"></el-table-column>
                <el-table-column label="手机号" prop="card_phone" align="center"></el-table-column>
                <el-table-column label="性别" prop="gender" align="center">
                  <template slot-scope="scope">
                    {{ scope.row.gender === 'WOMEN' ? '女' : (scope.row.gender === 'MAN' ? '男' : '') }}
                  </template>
                </el-table-column>
                <el-table-column label="识别分数" prop="score" align="center">
                  <template slot-scope="scope">
                    {{ scope.row.score.toFixed(2) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-if="showCheckFace" class="m-b-20 float-l face-table">
              <div class="m-b-15">识别结果</div>
              <el-table
                :data="recognizedInfo"
                stripe
                header-row-class-name="ps-table-header-row"
                max-height="600px"
                height="600px"
              >
                <el-table-column type="index" label="排序" :index="indexMethod" align="center"></el-table-column>
                <el-table-column label="近似人脸" prop="face_url" align="center">
                  <template slot-scope="scope">
                    <el-image :src="scope.row.face_url" :preview-src-list="[scope.row.face_url]">
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </template>
                </el-table-column>
                <el-table-column label="人员编号" prop="member_card" align="center"></el-table-column>
                <el-table-column label="姓名" prop="card_name" align="center"></el-table-column>
                <el-table-column label="手机号" prop="card_phone" align="center"></el-table-column>
                <el-table-column label="性别" prop="gender" align="center">
                  <template slot-scope="scope">
                    {{ scope.row.gender === 'WOMEN' ? '女' : (scope.row.gender === 'MAN' ? '男' : '') }}
                  </template>
                </el-table-column>
                <el-table-column label="识别分数" prop="score" align="center">
                  <template slot-scope="scope">
                    {{ scope.row.score.toFixed(2) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <div class="">
        <el-dialog title="营养信息" custom-class="ps-dialog" :visible.sync="dialogVisible" width="70%">
          <!-- <el-table
          :data="nutritionData"
          border
          stripe
          header-row-class-name="ps-table-header-row">
            <el-table-column v-for="item in nutritionList" :label="item.name" :key="item.key" :prop="item.key" ></el-table-column>
          </el-table> -->
          <el-form
          v-loading="isLoading"
          :model="nutritionData"
            class=""
            size="small"
          >
            <!-- 营养 start -->
            <div>
              <template v-for="nutrition in nutritionList">
                <div class="nutrition-item" :key="nutrition.key">
                  <div class="nutrition-label">{{nutrition.name+'：'}}</div>
                  <el-form-item :prop="nutrition.key">
                    <el-input style="width: 120px;" readonly v-model="nutritionData[nutrition.key]" class="ps-input"></el-input><span style="margin-left: 10px;">{{nutrition.unit}}</span>
                  </el-form-item>
                </div>
              </template>
            </div>
            <!-- 营养 end -->
          </el-form>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { to, replaceSingleQuote } from '@/utils'
import { type } from '@/utils/type'
import { NUTRITION_LIST } from '@/views/merchant/meal-management/food-admin/constants'

export default {
  name: 'MealOrderDetail',
  data() {
    return {
      isLoading: false,
      dialogVisible: false,
      orderData: {},
      detail_obj: {},
      foodData: [],
      tableSetting: [
        { label: '图片', key: 'food_img', width: '100px' },
        // { label: '识别图片', key: 'food_img', width: '100px' },
        { label: '菜品名称', key: 'food_name', width: '100px' },
        { label: '销售价格', key: 'real_fee', width: '100px' },
        { label: '数量', key: 'count', width: '100px' },
        { label: '重量', key: 'weight', width: '100px' },
        { label: '消费金额', key: 'raw_fee', width: '100px' },
        { label: '营养详情', key: 'detailChange', width: '100px' }
      ],
      nutritionList: NUTRITION_LIST,
      nutritionData: {},
      checkFaceLoading: false,
      showCheckFace: false,
      faceTraceback: null,
      trackInfo: [], // 刷脸结果
      recognizedInfo: [] // 识别结果
    }
  },
  created() {
    this.getOrderDetail()
  },
  methods: {
    async getOrderDetail() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderPaymentDetailListPost({
        order_payment_id: this.$route.query.id
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.orderData = res.data
        if (res.data.food_info) {
          this.foodData = res.data.food_info
        }
        this.trackInfo = res.data && res.data.face_user_list ? res.data.face_user_list : []
      } else {
        this.$message.error(res.msg)
      }
    },
    showDialogNutrition(rowNutrition) {
      console.log(rowNutrition)
      this.nutritionData = {}
      if (!rowNutrition) {
        // 防止nutrition没值的情况
        rowNutrition = {}
      }
      let element = {}
      let vitamin = {}
      // 什么时候更了新版？返回对象了，之前不是一直返回字符串吗
      if (type(rowNutrition.element) === 'object') {
        element = rowNutrition.element
      } else {
        element = rowNutrition.element
          ? JSON.parse(replaceSingleQuote(rowNutrition.element))
          : {}
      }
      if (type(rowNutrition.element) === 'object') {
        vitamin = rowNutrition.vitamin
      } else {
        vitamin = rowNutrition.vitamin
          ? JSON.parse(replaceSingleQuote(rowNutrition.vitamin))
          : {}
      }

      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(this.nutritionData, nutrition.key, rowNutrition[nutrition.key])
        }
        if (nutrition.type === 'element') {
          this.$set(this.nutritionData, nutrition.key, element[nutrition.key])
        }
        if (nutrition.type === 'vitamin') {
          this.$set(this.nutritionData, nutrition.key, vitamin[nutrition.key])
        }
      })
      this.dialogVisible = true
    },
    async checkFaceHandle() {
      this.checkFaceLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderPaymentFaceTracebackPost({
        order_payment_id: this.$route.query.id,
        organization_id: this.orderData.organization_id
      }))
      await this.$sleep(1000)
      this.checkFaceLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log(res)
      this.showCheckFace = true
      if (res.code === 0) {
        // this.faceTraceback = res.data
        // this.trackInfo = res.data && res.data.track_info ? res.data.track_info : []
        this.recognizedInfo = res.data && res.data.recognized_info ? res.data.recognized_info : []
      } else {
        this.$message.error(res.msg)
      }
    },
    indexMethod(index) {
      return index + 1
    },
    showHomeFn() {
      // this.$emit('toOrderDetailFn', true)
      // console.log(this.current)
      // 返回form的路由
      this.$closeCurrentTab(this.$route.path)
    }
  }
}
</script>

<style lang="scss">
i {
  font-style: normal;
}
.MealOrderDetail {
  background-color: #fff;
  margin-top: 20px;
  padding-top: 10px;
  .info {
    font-size: 14px;
    margin-left: 30px;
    padding-bottom: 300px;
    .user_info {
      font-weight: bold;
      margin-bottom: 30px;
      .title {
        margin: 15px 0;
      }
      .content {
        display: flex;
        .user_image {
          // border: 1px solid #cbcbcb;
          width: 80px;
          height: 50px;
          margin-right: 30px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        ul {
          display: inline-block;
          vertical-align: top;
          width: 600px;
          li {
            float: left;
            margin: 0 50px 10px 0;
            span {
              &:nth-of-type(2) {
                display: inline-block;
                text-indent: 8px;
              }
            }
          }
        }
      }
    }
    .order_info {
      margin-bottom: 30px;
      .title {
        margin: 15px 0;
      }
      font-weight: bold;
      ul {
        height: 20px;
        margin-bottom: 6px;
        li {
          float: left;
          margin-right: 30px;
          span {
            &:nth-of-type(2) {
              display: inline-block;
              text-indent: 8px;
            }
          }
        }
      }
    }
    .shebei_info .title,
    .menu_info .title {
      margin: 15px 0;
      font-weight: bold;
    }
    .menu_info {
      margin: 30px 0;
    }
    .vertical-t{
      vertical-align: top;
    }
    .face-wrapper{
      .face-img{
        width: 100px;
        height: 120px;
        // text-align: center;
        // background-color: ;
        .image-slot{
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background: #f5f7fa;
          color: #909399;
          font-size: 14px;
        }
      }
    }
    table {
      border-collapse: collapse;
      tr th,
      tr td {
        width: 100px;
      }
      tr td {
        height: 35px;
      }
    }
    .nutrition_info {
      table {
        tr,
        td,
        th {
          border: 1px solid #ccc;
        }
      }
    }
  }
}
.ps-dialog{
  .nutrition-item{
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label{
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
}
</style>
