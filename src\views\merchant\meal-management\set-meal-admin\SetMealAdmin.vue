<template>
  <div class="set-meal-admin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addOrEditSetMeal('add')" v-permission="['background_food.set_meal.add']">
            新建套餐
          </button-icon>
          <button-icon color="plain" type="menu" @click="goToSetMealClassify" v-permission="['background_food.set_meal_category.list']">套餐分类</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column type="index" label="序号" align="center" key="index">
            <template slot-scope="scope">
              <div v-text="(currentPage - 1) * pageSize + 1 + scope.$index"></div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="套餐名称" align="center"></el-table-column>
          <el-table-column prop="category_name" label="套餐分类" align="center"></el-table-column>
          <el-table-column prop="" label="套餐价格" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.price_type === 'fixed'">
                {{ scope.row.fixed_price | formatMoney }}
              </div>
              <div v-else-if="scope.row.price_type === 'total'">菜品价格合计</div>
            </template>
          </el-table-column>
          <el-table-column prop="operator_name" label="创建人" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" class="ps-text" @click="addOrEditSetMeal('modify',scope.row)" v-permission="['background_food.set_meal.modify']">编辑</el-button>
              <el-button type="text" size="small" class="ps-warn" @click="clickDelete(scope.row)" v-permission="['background_food.set_meal.delete']">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
export default {
  name: 'SetMealAdmin',
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '套餐名称',
          value: '',
          placeholder: '请输入套餐名称',
          maxlength: 10
        },
        category: {
          type: 'select',
          label: '套餐分类',
          listNameKey: 'name',
          listValueKey: 'id',
          value: [],
          placeholder: '请选择套餐分类',
          multiple: true,
          collapseTags: true,
          dataList: []
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSetMealList()
      this.getSetMealCategoryList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getSetMealList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 列表
    async getSetMealList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodSetMealListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分类列表
    async getSetMealCategoryList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodSetMealCategoryListPost({
          page: 1,
          page_size: 9999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.category.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    clickDelete(row) {
      // 把写的提示信心需要换行的地方分成数组 confirmText
      const confirmText = [
        '是否删除当前套餐？',
        '删除后，已添加此套餐的菜谱会同步删除，但不影响用户已点的套餐'
      ]
      const newDatas = []
      const h = this.$createElement
      for (const i in confirmText) {
        newDatas.push(h('p', null, confirmText[i]))
      }
      this.$confirm('提示', {
        title: '提示',
        message: h('div', null, newDatas),
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            // await this.$sleep(1000);
            const [err, res] = await to(
              this.$apis.apiBackgroundFoodSetMealDeletePost({
                ids: [row.id]
              })
            )
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.getSetMealList()
              this.$message.success(res.msg)
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    addOrEditSetMeal(type, row) {
      this.$router.push({
        name: 'MerchantAddEditSetMeal',
        query: {
          type: type,
          data: this.$encodeQuery(row)
        }
      })
    },
    goToSetMealClassify() {
      this.$router.push({ name: 'MerchantSetMealClassify' })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getSetMealList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSetMealList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
