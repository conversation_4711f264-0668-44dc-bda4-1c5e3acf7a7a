import * as dayjs from 'dayjs'
import { MEAL_TYPES } from '@/utils/constants'
import { beforeRecentSevenDay, berforePickerOptions } from '@/utils/formatPickerOptions'
import { parseTime } from '@/utils'
// const defaultdate = getSevenDateRange(7);
// const rechargeMethod = [
//   { label: '线上', value: 'ONLINE' },
//   { label: '线下', value: 'OFFLINE' }
// ]

// const operateTypeList = [
//   { value: 'JC_CONSUME', label: '计次消费' },
//   { value: 'KF_CONSUME', label: '扣费消费' },
//   { value: 'RESERVATION_CONSUME', label: '预约' },
//   { value: 'REPORT_CONSUME', label: '报餐' },
//   { value: 'SUBSIDY_PUBLISH', label: '补贴发放' },
//   { value: 'SUBSIDY_CLEAR', label: '补贴清零' },
//   { value: 'ORDER_REFUND', label: '全额退款' },
//   { value: 'ORDER_PART_REFUND', label: '部分退款' },
//   { value: 'RECHARGE', label: '充值 ' }
//   // { value: 'CASH_WITHDRAWAL', label: '提现' }
//   // { value: 'DRAW', label: '后台取款' },
//   // { value: 'RECHARGE', label: '充值' },
//   // { value: 'FLAT_COST_CONLLECTION', label: '工本费退款' },
//   // { value: 'PATCH_COST', label: '补卡' },
//   // { value: 'UNKNOWN', label: '未知' }
// ]

const operateTypeList = [
  { value: 'ORDER_PART_REFUND', label: '部分退款' },
  { value: 'ORDER_REFUND', label: '退款' },
  { value: 'JC_CONSUME', label: '计次消费' },
  { value: 'KF_CONSUME', label: '扣费消费' },
  { value: 'RESERVATION_CONSUME', label: '预约消费' },
  { value: 'REPORT_MEAL_CONSUME', label: '报餐消费' },
  { value: 'BUFFET_CONSUME', label: '称重消费' },
  { value: 'DRAW', label: '后台提现' },
  { value: 'RECHARGE', label: '充值' },
  // { value: 'JIAOFEI', label: '缴费' },
  { value: 'SUBSIDY_PUBLISH', label: '补贴发放' },
  { value: 'SUBSIDY_CLEAR', label: '补贴清零' },
  { value: 'SUBSIDY_CHARGE', label: '补贴冲销' },
  { value: 'FLAT_COST_REFUND', label: '退费' },
  { value: 'SUPPLEMENTARY', label: '补卡' },
  { value: 'PUBLISH', label: '发卡' },
  { value: 'ORDERING_FOOD_PAY', label: '订餐扣费' },
  { value: 'UNKNOWN', label: '未知' },
  { value: 'THIRD_PARTY_CONSUMPTION', label: '第三方消费' },
  { value: 'ORDER_APPROVE_VISITOR_CONSUME', label: '访客消费' }
]

// const wallteArr = [
//   { label: '', value: 'online' },
//   { label: '', value: 'instore' },
//   { label: '', value: 'charge' },
//   { label: '', value: 'charge_offline' }
// ]

export const payMethods = [
  { value: 'PushiPay', label: '朴食储值支付' },
  { value: 'OCOMPAY', label: '一卡通-鑫澳康支付' },
  { value: 'SHIYAOPAY', label: '一卡通-石药支付' },
  { value: 'ABCPay', label: '农行支付' },
  { value: 'CCBPAY', label: '建行支付' },
  { value: 'BOCPAY', label: '中行支付' },
  { value: 'ICBCPAY', label: '工行支付' },
  { value: 'MEITUANPAY', label: '美团支付' },
  { value: 'ShouqianbaPay', label: '收钱吧支付' },
  { value: 'WechatPay', label: '微信支付' },
  // { value: 'UNKNOWN', label: '未知' },
  { value: 'CashPay', label: '现金支付' }
]

// 消费类型
export const paymentOrderType = [
  { value: 'reservation', label: '预约订单' },
  { value: 'report_meal', label: '报餐' },
  { value: 'buffet', label: '称重' },
  { value: 'instore', label: '到店就餐' },
  { value: 'FLAT_COST_REFUND', label: '退费' },
  { value: 'SUPPLEMENTARY', label: '补卡' },
  { value: 'PUBLISH', label: '发卡' },
  { value: 'other', label: '其他' },
  { value: 'THIRD_PARTY_CONSUMPTION', label: '第三方消费' },
  { value: 'zdshg', label: '自动售货柜' },
  { value: 'order_approve_visitor', label: '访客消费' }
]

// 设备状态
export const DEVICE_STATUS = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '在线',
    value: 'ONLINE'
  },
  {
    label: '离线',
    value: 'OFFLINE'
  }
]
export const ORDER_TYPE = [
  { value: 0, label: '消费类' },
  { value: 1, label: '退款类' },
  { value: 2, label: '提现类' },
  { value: 3, label: '充值类' },
  { value: 4, label: '补贴类' },
  { value: 5, label: '工本费类' },
  { value: 6, label: '缴费类' }
]

export const RECONCILIATION_STATUS_LIST = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '对账成功',
    value: 'success'
  },
  {
    label: '对账失败',
    value: 'failed'
  }
]

export const PROCESSING_STATE = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '已处理',
    value: ''
  },
  {
    label: '未处理',
    value: ''
  }
]

export const PAYWAYLIST = [
  {
    value: 'PushiPay',
    label: '储值支付'
  },
  {
    value: 'OCOMPAY',
    label: '一卡通-鑫澳康支付'
  },
  {
    value: 'SHIYAOPAY',
    label: '一卡通-石药支付'
  },
  {
    value: 'ZhengYuanPay',
    label: '正元支付'
  },
  {
    value: 'YTYPay',
    label: '正元易通云支付'
  },
  {
    value: 'ABCPay',
    label: '农行支付'
  },
  {
    value: 'CCBPay',
    label: '建行支付'
  },
  {
    value: 'BOCPAY',
    label: '中行支付'
  },
  {
    value: 'ICBCPAY',
    label: '工行支付'
  },
  {
    value: 'PsbcPay',
    label: '邮储银行支付'
  },
  {
    value: 'CMBPAY',
    label: '招商银行'
  },
  {
    value: 'NingXiaPay',
    label: '宁夏银行'
  },
  {
    value: 'NingXiaWxPay',
    label: '宁夏银行-微信'
  },
  {
    value: 'UnionPay',
    label: '银联支付'
  },
  {
    value: 'SuNingPay',
    label: '苏宁支付'
  },
  {
    value: 'WXYFPay',
    label: '微邮付'
  },
  {
    value: 'MEITUANPAY',
    label: '美团支付'
  },
  {
    value: 'ShouqianbaPay',
    label: '收钱吧支付'
  },
  {
    value: 'WechatPay',
    label: '微信支付'
  },
  {
    value: 'AliPay',
    label: '支付宝'
  },
  {
    value: 'YunShanFuPay',
    label: '云闪付支付'
  },
  {
    value: 'WingPay',
    label: '翼支付'
  },
  {
    value: 'QyWechatPay',
    label: '企业微信支付'
  },
  {
    value: 'ZiTengPay',
    label: '紫藤支付'
  },
  {
    value: 'UNKNOWN',
    label: '未知'
  },
  {
    value: 'CashPay',
    label: '现金支付'
  },
  {
    value: 'ThridPartyMachine',
    label: '第三方卡机支付'
  },
  {
    value: 'UnionBankPay',
    label: '联合银行支付'
  },
  {
    value: 'GZCBPay',
    label: '广州银行支付'
  },
  {
    value: 'AliQyCodePay',
    label: '支付宝企业码支付'
  },
  {
    value: 'QiZhiPay',
    label: '企智支付'
  },
  {
    value: 'ELingPay',
    label: '壹零后支付'
  },
  {
    value: 'ZhongHePay',
    label: '中核支付'
  },
  {
    value: 'SodexoPay',
    label: '索迪斯支付'
  }
]
export const SUBPAYWAYLIST = [
  {
    value: 'jf',
    label: '缴费方式支付'
  },
  {
    value: 'fastepay',
    label: '快e付支付'
  },
  {
    value: 'daikou',
    label: '授权代扣支付'
  },
  {
    value: 'ermb',
    label: '数字人民币'
  },
  {
    value: 'jsapi',
    label: 'JSAPI支付'
  },
  {
    value: 'h5',
    label: 'H5支付'
  },
  {
    value: 'wap',
    label: 'WAP支付'
  },
  {
    value: 'miniapp',
    label: '小程序支付'
  },
  {
    value: 'cash',
    label: '现金支付'
  },
  {
    value: 'yhf',
    label: '邮惠付'
  },
  {
    value: 'ermb_daikou',
    label: '数币授权代扣'
  },
  {
    value: 'ermb_b2c',
    label: '数币网关支付'
  },
  {
    value: 'ermb_scanpay',
    label: '数币C扫B支付'
  }
]

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]
export const DetailTotalSearchForm = {
  date_type: {
    type: 'select',
    value: 'create_time',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      }
    ]
  },
  select_time: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入订单号'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  card_no: {
    type: 'input',
    value: '',
    label: '卡号',
    placeholder: '请输入卡号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  payment_order_type_list: {
    type: 'select',
    value: [],
    label: '消费类型',
    clearable: true,
    multiple: true,
    collapseTags: true,
    dataList: paymentOrderType
  },
  payway_list: {
    type: 'select',
    value: [],
    label: '支付类型',
    multiple: true,
    collapseTags: true,
    dataList: payMethods,
    clearable: true
  },
  sub_payway_list: {
    type: 'select',
    value: [],
    label: '支付方式',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  // meal_type: {
  //   type: 'select',
  //   value: null,
  //   label: '餐段',
  //   clearable: true,
  //   dataList: [
  //     {
  //       label: '全部',
  //       value: ''
  //     },
  //     ...MEAL_TYPES
  //   ]
  // },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  },
  device_type_list: {
    type: 'select',
    value: [],
    label: '设备类型',
    listNameKey: 'name',
    listValueKey: 'key',
    multiple: true,
    collapseTags: true,
    dataList: [],
    clearable: true
  },
  device_name: {
    type: 'input',
    value: '',
    label: '交易设备',
    placeholder: '请输入交易设备'
  },
  pay_device_status: {
    type: 'select',
    value: '',
    label: '设备状态',
    dataList: DEVICE_STATUS,
    clearable: true
  },
  wallet_org: {
    type: 'organizationSelect',
    value: [],
    label: '动账钱包',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    multiple: true,
    checkStrictly: true,
    collapseTags: true,
    clearable: true
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    multiple: true,
    checkStrictly: true,
    collapseTags: true,
    clearable: true
  },
  controller: {
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入要搜索的操作员'
  },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入订单号',
    labelWidth: '100px'
  },
  pay_method: {
    type: 'select',
    label: '访客餐支付方式',
    value: '',
    placeholder: '请选择',
    labelWidth: '110px',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '访客记账',
        value: 'Accounting'
      },
      {
        label: '即付',
        value: 'PayAtSight'
      }
    ]
  }
  // device_org: {
  //   type: 'organizationParentSelect',
  //   value: [],
  //   label: '交易组织',
  //   checkStrictly: true,
  //   isLazy: false,
  //   multiple: true
  // }
}

export const DetailTotalSearchForm2 = {
  date_type: {
    type: 'select',
    value: 'create_time',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      }
    ]
  },
  select_time: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入订单号'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机',
    placeholder: '请输入手机号'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  card_no: {
    type: 'input',
    value: '',
    label: '卡号',
    placeholder: '请输入卡号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  operate_type_list: {
    type: 'select',
    value: [],
    label: '操作类型',
    multiple: true,
    collapseTags: true,
    dataList: [...operateTypeList],
    clearable: true
  },
  payway_list: {
    type: 'select',
    value: '',
    label: '支付类型',
    multiple: true,
    collapseTags: true,
    dataList: [],
    clearable: true
  },
  sub_payway_list: {
    type: 'select',
    value: [],
    label: '支付方式',
    multiple: true,
    collapseTags: true,
    dataList: [],
    clearable: true
  },
  // meal_type: {
  //   type: 'select',
  //   value: '',
  //   label: '餐段',
  //   clearable: true,
  //   dataList: [
  //     {
  //       label: '全部',
  //       value: ''
  //     },
  //     ...MEAL_TYPES
  //   ]
  // },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  },
  // device_name: {
  //   type: 'select',
  //   value: '',
  //   label: '交易设备',
  //   listNameKey: 'name',
  //   listValueKey: 'key',
  //   dataList: []
  // },
  wallet_org: {
    type: 'organizationSelect',
    value: [],
    label: '动账组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  controller: {
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入要搜索的操作员'
  },
  // device_org: {
  //   type: 'organizationParentSelect',
  //   value: [],
  //   label: '交易组织',
  //   checkStrictly: true,
  //   isLazy: false,
  //   multiple: true
  // },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入订单号',
    labelWidth: '100px'
  },
  pay_method: {
    type: 'select',
    label: '访客餐支付方式',
    value: '',
    placeholder: '请选择',
    labelWidth: '110px',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '访客记账',
        value: 'Accounting'
      },
      {
        label: '即付',
        value: 'PayAtSight'
      }
    ]
  },
  only_discount: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看优惠',
    value: false
  },
  only_rate_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看手续费',
    value: false
  },
  only_debt_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看透支',
    value: false
  }
}

export const TopUpDetailSearchForm = {
  select_time: {
    type: 'daterange',
    label: '充值时间',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    listNameKey: 'group_name',
    listValueKey: 'id',
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    flat: false,
    checkStrictly: true,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  recharge_method: {
    type: 'select',
    value: '',
    label: '充值方式',
    dataList: [],
    clearable: true
  },
  recharge_type: {
    type: 'select',
    value: '',
    label: '充值类型',
    dataList: [
      { label: '线上充值', value: 'charge' },
      { label: '线下充值', value: 'charge_offline' }
    ],
    clearable: true
  },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入订单号',
    labelWidth: '100px'
  },
  only_rate_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看手续费',
    value: false
  }
}

export const DeviceCodeSearchForm = {
  select_time: {
    type: 'daterange',
    label: '搜索日期',
    value: recentSevenDay
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  device_id: {
    type: 'input',
    value: '',
    label: '设备号',
    placeholder: '请输入设备号'
  },
  // device_name: {
  //   type: 'input',
  //   value: '',
  //   label: '设备名',
  //   placeholder: '请输入设备名'
  // },
  device_type: {
    type: 'select',
    value: '',
    label: '设备类型',
    listNameKey: 'name',
    listValueKey: 'key',
    dataList: [],
    clearable: true
  }
}

// 提现明细表
export const WithdrawSearchForm = {
  select_time: {
    type: 'daterange',
    label: '申请时间',
    value: recentSevenDay
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入订单号',
    clearable: true
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名',
    clearable: true
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号',
    clearable: true
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号',
    clearable: true
  },
  card_no: {
    type: 'input',
    value: '',
    label: '卡号',
    placeholder: '请输入卡号',
    clearable: true
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  payway: {
    type: 'select',
    value: '',
    label: '提现方式',
    dataList: payMethods,
    clearable: true
  }
}

// 个人充值汇总
export const PERSONAL_RECHARGE_SUMMARY = {
  select_time: {
    type: 'daterange',
    label: '搜索时间',
    value: recentSevenDay,
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机',
    placeholder: '请输入手机'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  payway: {
    type: 'select',
    value: '',
    label: '充值方式',
    dataList: [],
    clearable: true
  }
}

// 部门消费汇总
export const DEPARTMENTAL_CONSUMPTION_SUMMARY = {
  select_time: {
    labelWidth: '110px',
    type: 'daterange',
    label: '消费时间',
    value: beforeRecentSevenDay,
    pickerOptions: berforePickerOptions,
    clearable: false
  },
  // name: {
  //   type: 'input',
  //   value: '',
  //   label: '姓名',
  //   placeholder: '请输入姓名'
  // },
  // phone: {
  //   type: 'input',
  //   value: '',
  //   label: '手机',
  //   placeholder: '请输入手机'
  // },
  // person_no: {
  //   type: 'input',
  //   value: '',
  //   label: '人员编号',
  //   placeholder: '请输入人员编号'
  // },
  // payer_group_ids: {
  //   type: 'select',
  //   label: '分组',
  //   value: [],
  //   placeholder: '请选择分组',
  //   dataList: [],
  //   listNameKey: 'group_name',
  //   listValueKey: 'id',
  //   multiple: true,
  //   collapseTags: true
  // },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}
// 部门消费汇总
export const PERSONAL_CONSUMPTION_SUMMARY = {
  select_time: {
    labelWidth: '110px',
    type: 'daterange',
    label: '消费时间',
    value: beforeRecentSevenDay,
    pickerOptions: berforePickerOptions,
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机',
    placeholder: '请输入手机'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}

// 部门消费汇总
export const ACCOUNT_WALLET_DAILY = {
  select_time: {
    type: 'daterange',
    label: '搜索日期',
    value: beforeRecentSevenDay,
    pickerOptions: berforePickerOptions,
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}

export const PersonalWalletDailySearchForm = {
  select_time: {
    type: 'daterange',
    label: '搜索日期',
    value: beforeRecentSevenDay,
    pickerOptions: berforePickerOptions,
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    maxlength: 20,
    placeholder: '请输入姓名'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  }
}
export const CollectionlCodeReportSearchForm = {
  select_time: {
    type: 'daterange',
    label: '搜索日期',
    value: recentSevenDay,
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}
// 第三方对账表
export const THIRD_RECONCILIATION = {
  select_time: {
    type: 'daterange',
    label: '时间',
    value: recentSevenDay
  },
  order_type: {
    type: 'select',
    value: '',
    label: '订单类型',
    dataList: ORDER_TYPE,
    clearable: true
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '总单号',
    placeholder: '请输入总单号',
    clearable: true
  },
  business_no: {
    type: 'input',
    value: '',
    label: '商户号',
    placeholder: '请输入总单号',
    clearable: true
  },
  // third_name: {
  //   type: 'input',
  //   value: '',
  //   label: '第三方名称',
  //   placeholder: '请输入第三方名称',
  //   clearable: true
  // },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入第三方订单号',
    // labelWidth: '100px',
    clearable: true
  },
  reconciliation_status: {
    type: 'select',
    value: '',
    label: '对账状态',
    dataList: RECONCILIATION_STATUS_LIST,
    clearable: true
  },
  processing_state: {
    type: 'select',
    value: '',
    label: '处理状态',
    dataList: PROCESSING_STATE,
    clearable: true
  },
  payway_list: {
    type: 'select',
    value: [],
    label: '支付类型',
    multiple: true,
    collapseTags: true,
    dataList: payMethods,
    clearable: true
  },
  sub_payway_list: {
    type: 'select',
    value: [],
    label: '支付方式',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  }
  // name: {
  //   type: 'input',
  //   value: '',
  //   label: '姓名',
  //   placeholder: '请输入姓名',
  //   clearable: true
  // },
  // phone: {
  //   type: 'input',
  //   value: '',
  //   label: '手机号',
  //   placeholder: '请输入手机号',
  //   clearable: true
  // }
}
export const DeductionServiceReportSetting = {
  date_type: {
    type: 'select',
    value: 'create_time',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '预约时间',
        value: 'reservation_date'
      }
    ]
  },
  select_date: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  payway: {
    type: 'select',
    listNameKey: 'name',
    listValueKey: 'payway',
    value: [],
    label: '支付渠道',
    dataList: [],
    clearable: true,
    multiple: true,
    collapseTags: true
  },
  take_type: {
    type: 'select',
    value: '',
    label: '就餐方式',
    dataList: [
      {
        label: '到店就餐',
        value: 'instore'
      },
      {
        label: '预约-堂食',
        value: 'on_scene'
      },
      {
        label: '预约-食堂自取',
        value: 'bale'
      },
      {
        label: '预约-取餐柜取餐',
        value: 'cupboard'
      },
      {
        label: '预约-外卖',
        value: 'waimai'
      },
      {
        label: '报餐-堂食',
        value: 'report_on_scene'
      },
      {
        label: '报餐-堂食自提',
        value: 'report_bale'
      }
    ],
    clearable: true
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '涉及订单号',
    labelWidth: '100px',
    placeholder: '请输入涉及订单号',
    clearable: true
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  meal_time: {
    type: 'select',
    value: null,
    label: '餐段',
    clearable: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      ...MEAL_TYPES
    ]
  }
}
export const RechargeServiceReportSetting = {
  date_type: {
    type: 'select',
    value: 'create_time',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      }
    ]
  },
  select_date: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  payway: {
    type: 'select',
    listNameKey: 'name',
    listValueKey: 'payway',
    value: [],
    label: '支付渠道',
    dataList: [],
    clearable: true,
    multiple: true,
    collapseTags: true
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '涉及订单号',
    labelWidth: '100px',
    placeholder: '请输入涉及订单号',
    clearable: true
  }
}

export const RECEIPTSTATUS = [
  { label: '全部', value: '' },
  { label: '已开票', value: 'success' },
  { label: '未开票', value: 'non_open' },
  { label: '开票失败', value: 'fail' },
  { label: '红冲', value: 'red' }
]

// 开票记录
export const RECEIPT_LIST_SEARCH = {
  date_type: {
    type: 'select',
    value: 2,
    maxWidth: '130px',
    dataList: [
      {
        label: '创建时间',
        value: 1
      },
      {
        label: '申请时间',
        value: 2
      }
    ]
  },
  select_time: {
    type: 'daterange',
    label: '',
    clearable: true,
    value: recentSevenDay
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '总单号/订单号',
    placeholder: ''
  },
  invoice_status: {
    type: 'select',
    label: '开票状态',
    value: '',
    placeholder: '',
    dataList: RECEIPTSTATUS
  },
  organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  name: {
    type: 'input',
    value: '',
    label: '开票人'
  }
}

// 冲销明细
export const WRITE_OFF_DETAIL = {
  select_time: {
    type: 'daterange',
    label: '冲销时间',
    value: recentSevenDay,
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机',
    placeholder: '请输入手机'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  order_no: {
    type: 'input',
    value: '',
    label: '订单编号',
    placeholder: '请输入订单编号'
  }
}

// 人员就餐统计报表
export const PersonMealReportSearchForm = {
  date: {
    type: 'date',
    label: '时间',
    clearable: false,
    value: parseTime(new Date(), '{y}-{m}-{d}')
  },
  org_id: {
    type: 'organizationSelect',
    value: '',
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    multiple: false,
    checkStrictly: true,
    collapseTags: true,
    clearable: true
  },
  payer_group_id: {
    type: 'groupSelect',
    label: '分组',
    value: '',
    placeholder: '请选择分组',
    dataList: [],
    clearable: true
  },
  meal_status: {
    type: 'select',
    value: '',
    label: '点餐情况',
    dataList: [
      { label: '已点餐', value: 1 },
      { label: '未点餐', value: 0 }
    ],
    clearable: true
  },
  meal_type: {
    type: 'select',
    value: null,
    label: '餐段',
    clearable: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      ...MEAL_TYPES
    ]
  }
}

// 补贴明细
export const SUBSIDY_CLEAR_DETAIL = {
  select_time: {
    type: 'daterange',
    label: '清零时间',
    value: recentSevenDay,
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机',
    placeholder: '请输入手机'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  order_no: {
    type: 'input',
    value: '',
    labelWidth: '220px',
    label: '订单号/补贴编号/补贴批次号',
    placeholder: '请输入'
  }
}
// 分组储值汇总表
export const GROUP_PREPAID_SUMMARY = {
  select_time: {
    labelWidth: '110px',
    type: 'daterange',
    label: '消费时间',
    value: beforeRecentSevenDay,
    pickerOptions: berforePickerOptions,
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}
// 优惠券明细表
export const COUPON_DETAILS = {
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入订单号'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  coupon_type_list: {
    type: 'select',
    value: [],
    label: '券类型',
    placeholder: '请选择券类型',
    multiple: true,
    collapseTags: true,
    dataList: [
      {
        label: '满减券',
        value: 'FULL_DISCOUNT'
      },
      {
        label: '立减券',
        value: 'INSTANT_DISCOUNT'
      },
      {
        label: '折扣券',
        value: 'DISCOUNT'
      },
      {
        label: '兑换券',
        value: 'EXCHANGE'
      }
    ]
  },
  consume_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  }
}
// 餐补使用明细表筛选设置
export const MEAL_ALLOWANCE_USED_DETAIL_LIST_SEARCH_SETTING = {
  select_time: {
    type: 'daterange',
    label: '就餐日期',
    value: recentSevenDay,
    clearable: false
  },
  consume_rule_ids: {
    type: 'select',
    label: '消费规则',
    value: [],
    placeholder: '请选择',
    listNameKey: 'name',
    listValueKey: 'rule_no',
    multiple: true,
    collapseTags: true,
    dataList: []
  },
  consume_rule_guid: {
    type: "input",
    value: '',
    label: '规则ID',
    placeholder: '请输入'
  },
  name: {
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入',
    maxWidth: "350px"
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号码',
    placeholder: '请输入'
  },
  department_id: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
  },
  group_id: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}
// 餐补使用明细表表头设置
export const MEAL_ALLOWANCE_USED_DETAIL_LIST_TABLE_SETTING = [
  {
    label: '就餐日期',
    key: 'order_date',
    width: '150'
  },
  {
    label: '消费规则',
    key: 'rule_name',
    width: '150'
  },
  {
    label: '规则ID',
    key: 'rule_guid',
    width: '150',
    type: 'slot',
    slotName: 'rule_guid'
  },
  {
    label: '用户名称',
    key: 'name',
    width: '150'
  },
  {
    label: '人员编号',
    key: 'person_no',
    width: '150'
  },
  {
    label: '手机号',
    key: 'phone',
    width: '150'
  },
  {
    label: '分组',
    key: 'user_groups_name',
    width: '150'
  },
  {
    label: '部门',
    key: 'department_group_name',
    width: '150'
  },
  {
    label: '应发餐补',
    key: 'meal_supplements',
    children: [
      {
        label: '早餐',
        key: 'hair_breakfast_price',
        type: 'money'
      },
      {
        label: '午餐',
        key: 'hair_lunch_price',
        type: 'money'
      },
      {
        label: '下午茶',
        key: 'hair_afternoon_price',
        type: 'money'
      },
      {
        label: '晚餐',
        key: 'hair_dinner_price',
        type: 'money'
      },
      {
        label: '宵夜',
        key: 'hair_supper_price',
        type: 'money'
      },
      {
        label: '凌晨餐',
        key: 'hair_morning_price',
        type: 'money'
      },
      {
        label: '合并餐段',
        key: 'hair_all_price',
        type: 'money'
      }
    ]
  },
  {
    label: '已用餐补',
    key: 'meal_supplements_already',
    children: [
      {
        label: '早餐',
        key: 'use_breakfast_price',
        type: 'money'
      },
      {
        label: '午餐',
        key: 'use_lunch_price',
        type: 'money'
      },
      {
        label: '下午茶',
        key: 'use_afternoon_price',
        type: 'money'
      },
      {
        label: '晚餐',
        key: 'use_dinner_price',
        type: 'money'
      },
      {
        label: '宵夜',
        key: 'use_supper_price',
        type: 'money'
      },
      {
        label: '凌晨餐',
        key: 'use_morning_price',
        type: 'money'
      },
      {
        label: '小计',
        key: 'use_all_price',
        type: 'money'
      }
    ]
  },
  {
    label: '退款餐补',
    key: 'meal_supplements_refund',
    children: [
      {
        label: '早餐',
        key: 'ref_breakfast_price',
        type: 'money'
      },
      {
        label: '午餐',
        key: 'ref_lunch_price',
        type: 'money'
      },
      {
        label: '下午茶',
        key: 'ref_afternoon_price',
        type: 'money'
      },
      {
        label: '晚餐',
        key: 'ref_dinner_price',
        type: 'money'
      },
      {
        label: '宵夜',
        key: 'ref_supper_price',
        type: 'money'
      },
      {
        label: '凌晨餐',
        key: 'ref_morning_price',
        type: 'money'
      },
      {
        label: '小计',
        key: 'ref_all_price',
        type: 'money'
      }
    ]
  },
  {
    label: '实用餐补',
    key: 'meal_supplements_useful',
    children: [
      {
        label: '早餐',
        key: 'uti_breakfast_price',
        type: 'money'
      },
      {
        label: '午餐',
        key: 'uti_lunch_price',
        type: 'money'
      },
      {
        label: '下午茶',
        key: 'uti_afternoon_price',
        type: 'money'
      },
      {
        label: '晚餐',
        key: 'uti_dinner_price',
        type: 'money'
      },
      {
        label: '宵夜',
        key: 'uti_supper_price',
        type: 'money'
      },
      {
        label: '凌晨餐',
        key: 'uti_morning_price',
        type: 'money'
      },
      {
        label: '小计',
        key: 'uti_all_price',
        type: 'money'
      }
    ]
  }
]
// 餐补使用汇总表表头设置
export const MEAL_ALLOWANCE_USED_SUMMARY_LIST_SEARCH_SETTING = {
  select_time: {
    type: 'daterange',
    label: '日期筛选',
    value: recentSevenDay,
    clearable: false
  },
  consume_rule_ids: {
    type: 'select',
    label: '消费规则',
    value: [],
    placeholder: '请选择',
    listNameKey: 'name',
    listValueKey: 'rule_no',
    multiple: true,
    collapseTags: true,
    dataList: []
  },
  consume_rule_guid: {
    type: "input",
    value: '',
    label: '规则ID',
    placeholder: '请输入'
  }
}
// 餐补使用汇总表表头设置
export const MEAL_ALLOWANCE_USED_SUMMARY_LIST_TABLE_SETTING = [
  {
    label: '就餐日期',
    key: 'order_date',
    width: '150'
  },
  {
    label: '消费规则',
    key: 'rule_name',
    width: '150'
  },
  {
    label: '规则ID',
    key: 'rule_guid',
    width: '150',
    type: 'slot',
    slotName: 'rule_guid'
  },
  {
    label: '应发餐补',
    key: 'meal_supplements',
    children: [
      {
        label: '早餐',
        key: 'hair_breakfast_price',
        type: 'money'
      },
      {
        label: '午餐',
        key: 'hair_lunch_price',
        type: 'money'
      },
      {
        label: '下午茶',
        key: 'hair_afternoon_price',
        type: 'money'
      },
      {
        label: '晚餐',
        key: 'hair_dinner_price',
        type: 'money'
      },
      {
        label: '宵夜',
        key: 'hair_supper_price',
        type: 'money'
      },
      {
        label: '凌晨餐',
        key: 'hair_morning_price',
        type: 'money'
      },
      {
        label: '合并餐段',
        key: 'hair_all_price',
        type: 'money'
      }
    ]
  },
  {
    label: '已用餐补',
    key: 'meal_supplements_already',
    children: [
      {
        label: '早餐',
        key: 'use_breakfast_price',
        type: 'money'
      },
      {
        label: '午餐',
        key: 'use_lunch_price',
        type: 'money'
      },
      {
        label: '下午茶',
        key: 'use_afternoon_price',
        type: 'money'
      },
      {
        label: '晚餐',
        key: 'use_dinner_price',
        type: 'money'
      },
      {
        label: '宵夜',
        key: 'use_supper_price',
        type: 'money'
      },
      {
        label: '凌晨餐',
        key: 'use_morning_price',
        type: 'money'
      },
      {
        label: '小计',
        key: 'use_all_price',
        type: 'money'
      }
    ]
  },
  {
    label: '退款餐补',
    key: 'meal_supplements_refund',
    children: [
      {
        label: '早餐',
        key: 'ref_breakfast_price',
        type: 'money'
      },
      {
        label: '午餐',
        key: 'ref_lunch_price',
        type: 'money'
      },
      {
        label: '下午茶',
        key: 'ref_afternoon_price',
        type: 'money'
      },
      {
        label: '晚餐',
        key: 'ref_dinner_price',
        type: 'money'
      },
      {
        label: '宵夜',
        key: 'ref_supper_price',
        type: 'money'
      },
      {
        label: '凌晨餐',
        key: 'ref_morning_price',
        type: 'money'
      },
      {
        label: '小计',
        key: 'ref_all_price',
        type: 'money'
      }
    ]
  },
  {
    label: '实用餐补',
    key: 'meal_supplements_useful',
    children: [
      {
        label: '早餐',
        key: 'uti_breakfast_price',
        type: 'money'
      },
      {
        label: '午餐',
        key: 'uti_lunch_price',
        type: 'money'
      },
      {
        label: '下午茶',
        key: 'uti_afternoon_price',
        type: 'money'
      },
      {
        label: '晚餐',
        key: 'uti_dinner_price',
        type: 'money'
      },
      {
        label: '宵夜',
        key: 'uti_supper_price',
        type: 'money'
      },
      {
        label: '凌晨餐',
        key: 'uti_morning_price',
        type: 'money'
      },
      {
        label: '小计',
        key: 'uti_all_price',
        type: 'money'
      }
    ]
  }
]
