<template>
  <div class="info-show p-20">
    <el-form ref="infoShowFormRef" :model="infoShowForm" label-position="top">
      <el-form-item prop="school_type">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">学校类型</div>
            <el-button type="text" class="ps-origin-text" @click="showDrawer('school_type', infoShowForm.school_type)">修改</el-button>
          </div>
        </template>
        <div class="">
          {{ infoShowForm.school_type }}
        </div>
      </el-form-item>
      <el-form-item prop="level">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">餐饮服务食品安全等级公示</div>
            <el-button type="text" class="ps-origin-text" @click="showDrawer('level', infoShowForm.level)">修改</el-button>
          </div>
        </template>
        <div class="level-show p-l-20">
          <div class="level-show-left m-r-40">
            <svg-icon v-show="infoShowForm.level" :icon-class="infoShowForm.level" style="width: 128px; height: 128px;" />
          </div>
          <div class="level-show-right">
            <div class="level-show-right-tips m-r-20">
              <svg-icon :icon-class="'excellent'" class="m-b-10 m-r-10" style="width: 36px; height: 36px;" />
              <span>A级：优秀</span>
            </div>
            <div class="level-show-right-tips m-r-20">
              <svg-icon :icon-class="'good'" class="m-b-10 m-r-10" style="width: 36px; height: 36px;" />
              <span>B级：良好</span>
            </div>
            <div class="level-show-right-tips m-r-20">
              <svg-icon :icon-class="'average'" class="m-b-10 m-r-10" style="width: 36px; height: 36px;" />
              <span>C级：一般</span>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="type">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">食堂类型</div>
            <el-button type="text" class="ps-origin-text" @click="showDrawer('type', infoShowForm.canteen_type)">修改</el-button>
          </div>
        </template>
        <div class="">
          {{ infoShowForm.canteen_type_alias }}
        </div>
      </el-form-item>
      <el-form-item prop="plan">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">营养改善计划</div>
            <el-button type="text" class="ps-origin-text" @click="showDrawer('plan', infoShowForm.plan)">修改</el-button>
          </div>
        </template>
        <div class="">
          {{ computedPlan(infoShowForm.plan) }}
        </div>
      </el-form-item>
      <el-form-item prop="complain">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">投诉渠道</div>
            <el-button type="text" class="ps-origin-text" @click="showDrawer('complain', infoShowForm.complain)">修改</el-button>
          </div>
        </template>
        <div class="ps-flex-align-c flex-align-c">
          <div class="m-r-20 w-250">
            投诉电话：{{ infoShowForm.complain.phone }}
          </div>
          <div class="m-r-20 w-250">
            投诉邮箱：{{ infoShowForm.complain.email }}
          </div>
          <div class="w-250">
            投诉链接：{{ infoShowForm.complain.link }}
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="tableData">
        <template #label>
          <div class="ps-flex-align-c flex-align-c">
            <div class="m-r-20">资质公示</div>
            <el-button type="text" class="ps-origin-text" @click="showInfoDrawer('add')">新增</el-button>
          </div>
        </template>
        <el-table
          :data="infoShowForm.tableData"
          v-loading="isLoading"
          stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="(itemIn, indexIn) in tableSetting" :key="indexIn" :col="itemIn">
            <template #img="{ row }">
              <el-image style="width: 200px; height: 100px" :src="row.image" :preview-src-list="[row.image]" fit="contain" />
            </template>
            <template #timeRange="{ row }">
              <div v-if="!['CY', 'SP', 'WS', 'FR'].includes(row.qualification_type)">
                {{ '永久有效' }}
              </div>
              <div v-else>
                {{ computedDate(row.extra.expirationDate[0]) }} 至 {{ computedDate(row.extra.expirationDate[1]) }}
              </div>
            </template>
            <template #faceUrl="{ row }">
              <el-image
              style="width: 100px; height: 100px"
              :src="row.face_url"
              :preview-src-list="[row.face_url]"
              fit="fill" />
            </template>
            <template #operation="{ row }">
              <el-button type="text" class="ps-origin-text" @click="showInfoDrawer('detail', row)">详情</el-button>
              <el-button type="text" class="ps-origin-text" @click="showInfoDrawer('edit', row)">编辑</el-button>
              <el-button type="text" class="ps-origin-text" @click="deleteInfo(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
      </el-form-item>
    </el-form>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'修改学校类型'"
        :visible="schoolTypeDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="schoolTypeFormRef" :model="schoolTypeForm" label-width="80px" label-position="right">
            <el-form-item :label="'学校类型'" prop="school_type" :rules="[{ required: true, message: '请选择学校类型', trigger: ['change', 'blur'] }]">
              <el-select v-model="schoolTypeForm.school_type" placeholder="请选择">
                <el-option
                  v-for="(item, index) in schoolTypeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('school_type')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('school_type')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'修改食品安全等级'"
        :visible="foodSafetyDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="foodSafetyFormRef" :model="foodSafetyForm" label-width="80px" label-position="right">
            <el-form-item :label="'安全等级'" prop="level" :rules="[{ required: true, message: '请选择安全等级', trigger: ['change', 'blur'] }]">
              <el-select v-model="foodSafetyForm.level" placeholder="请选择">
                <el-option
                  v-for="(item, index) in levelList"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('level')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('level')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'修改食堂类型'"
        :visible="canteenTypeDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="canteenTypeFormRef" :model="canteenTypeForm" label-width="80px" label-position="right">
            <el-form-item :label="'食堂类型'" prop="type" :rules="[{ required: true, message: '请选择食堂类型', trigger: ['change', 'blur'] }]">
              <el-select v-model="canteenTypeForm.type" placeholder="请选择">
                <el-option
                  v-for="(item, index) in canteenTypeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('type')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('type')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'修改营养改善计划'"
        :visible="planDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="planFormRef" :model="planForm" label-width="120px" label-position="right">
            <el-form-item :label="'营养改善计划'" prop="type" :rules="[{ required: true, message: '请选择对应的状态', trigger: ['change', 'blur'] }]">
              <el-select v-model="planForm.type" placeholder="请选择">
                <el-option
                  v-for="(item, index) in planList"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('plan')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('plan')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'修改投诉渠道'"
        :visible="complainDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="complainFormRef" :model="complainForm" :rules="complainRules" label-width="80px" label-position="right">
            <el-form-item label="投诉电话" prop="phone">
              <el-input class="w-350" v-model="complainForm.phone" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="投诉邮箱" prop="email">
              <el-input class="w-350" v-model="complainForm.email" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="投诉链接" prop="link">
              <el-input class="w-350" v-model="complainForm.link" placeholder="请输入"></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('complain')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('complain')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="computedTitle(selectType)"
        :visible="infoDrawerShow"
        :show-close="false"
        size="35%">
        <div class="p-20">
          <el-form ref="infoFormRef" :model="infoForm" label-width="auto" label-position="right">
            <el-form-item :label="'资质类型'" prop="typeOfQualification" :rules="[{ required: true, message: '请选择相应的资质类型', trigger: ['change', 'blur'] }]">
              <el-select v-model="infoForm.typeOfQualification" placeholder="请选择" @change="changeTypeOfQualification(infoForm.typeOfQualification, true)" class="w-350" :disabled="selectType === 'detail' || selectType === 'edit'">
                <el-option
                  v-for="(item, index) in typeOfQualificationList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled">
                </el-option>
              </el-select>
            </el-form-item>
            <div v-for="(item, index) in infoFormList" :key="index">
              <el-form-item :label="item.label" :prop="item.prop" :rules="item.rule" v-if="item.type === 'input'">
                <el-input class="w-350" v-model="infoForm[item.prop]" placeholder="请输入" :disabled="selectType === 'detail'" @input="(e) => handleInput(item.inputType, item.prop, e)"></el-input>
              </el-form-item>
              <el-form-item :label="item.label" :prop="item.prop" :rules="item.rule" v-if="item.type === 'date'">
                <el-date-picker
                  :key="item.type"
                  :disabled="selectType === 'detail'"
                  append-to-body
                  class="w-350"
                  v-model="infoForm[`${item.prop}`]"
                  format="yyyy年MM月dd日"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="item.label" :prop="item.prop" :rules="item.rule" v-if="item.type === 'dateRange'">
                <el-date-picker
                  :key="item.type"
                  :disabled="selectType === 'detail'"
                  append-to-body
                  v-model="infoForm[`${item.prop}`]"
                  format="yyyy年MM月dd日"
                  value-format="yyyy-MM-dd"
                  class="w-350"
                  type="daterange"
                  range-separator="~"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
              <el-form-item :label="item.label" :prop="item.prop" :rules="item.rule" v-if="item.type === 'img'">
                <div class="certification-info-show-tips">
                  图片最大不超过2MB，仅支持jpg,png格式
                </div>
                <el-upload
                 :disabled="selectType === 'detail'"
                  v-loading="uploading"
                  element-loading-text="上传中"
                  class="upload-w" ref="fileUpload"
                  :action="serverUrl"
                  :file-list="fileLists"
                  :on-success="uploadSuccessForList"
                  :before-upload="beforeImgUpload"
                  :limit="1"
                  :multiple="false"
                  :show-file-list="false"
                  :headers="headersOpts"
                  accept=".jpeg,.jpg,.png,.bmp">
                  <img v-if="infoForm[`${item.prop}`]" :src="infoForm[`${item.prop}`]" class="certification-info-show-img">
                  <div v-else style="width: 200px; height: 100px; border: 1px dashed #C0C4CC; text-align: center; line-height: 100px;">
                    <i class="el-icon-plus"></i>
                  </div>
                </el-upload>
              </el-form-item>
            </div>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" v-if="selectType !== 'detail'" @click="cancelHandle('info')">取消</el-button>
            <el-button size="small" type="primary" v-if="selectType !== 'detail'" class="w-100" @click="saveHandle('info')">保存</el-button>
            <el-button size="small" type="primary" class="w-100" v-if="selectType === 'detail'" @click="cancelHandle('info')">关闭</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { getToken, getSuffix, deepClone } from '@/utils/index'
import {
  publicInstitutionForm,
  publicInstitutionFormList,
  businessLicenseForm,
  businessLicenseFormList,
  cateringServiceLicenseForm,
  cateringServiceLicenseList,
  foodBusinessLicenseForm,
  foodBusinessLicenseList,
  sanitaryPermitForm,
  sanitaryPermitList,
  fireSafetyCertificateForm,
  fireSafetyCertificateList,
  taxRegistrationCertificateForm,
  taxRegistrationCertificateList
} from './constants'
import dayjs from 'dayjs'
export default {
  data() {
    const validatePhone = (rule, value, callback) => {
      const reg = /^1[3-9]\d{9}$/
      if (!reg.test(value)) {
        callback(new Error('请输入有效的联系方式'))
      } else {
        callback()
      }
    }
    const validateEmail = (rule, value, callback) => {
      const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      if (!reg.test(value)) {
        callback(new Error('请输入有效的邮箱地址'))
      } else {
        callback()
      }
    }
    const validateLink = (rule, value, callback) => {
      const reg = /^(https?:\/\/)?([\w-]+\.)+[\w-]+(:\d+)?(\/[\w-./?%&=]*)?$/
      if (!reg.test(value)) {
        callback(new Error('请输入有效的链接'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      infoShowForm: {
        school_type: '',
        level: '',
        canteen_type: '',
        canteen_type_alias: '',
        plan: false,
        complain: {},
        tableData: []
      },
      tableSetting: [
        { label: '图片', key: 'image', align: 'center', type: 'slot', slotName: 'img' },
        { label: '资质类型', key: 'qualification_type_alias', align: 'center' },
        { label: '有效期', key: 'time', align: 'center', type: 'slot', slotName: 'timeRange' },
        { label: '创建时间', key: 'create_time', align: 'center' },
        { label: '修改时间', key: 'update_time', align: 'center' },
        { label: '操作人', key: 'operator_name', align: 'center' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      schoolTypeDrawerShow: false,
      schoolTypeForm: {
        school_type: ''
      },
      schoolTypeList: [
        {
          label: '幼儿园',
          value: 'kindergarten'
        },
        {
          label: '小学',
          value: 'primary_school'
        },
        {
          label: '中学',
          value: 'middle_school'
        },
        {
          label: '中小学',
          value: 'middle_primary_school'
        }
      ],
      foodSafetyDrawerShow: false,
      foodSafetyForm: {
        level: ''
      },
      levelList: [
        {
          label: 'A级：优秀',
          value: 'excellent'
        },
        {
          label: 'B级：良好',
          value: 'good'
        },
        {
          label: 'C级：一般',
          value: 'average'
        }
      ],
      canteenTypeDrawerShow: false,
      canteenTypeForm: {
        type: 'chengbao'
      },
      canteenTypeList: [
        {
          label: '承包/托管经营食堂',
          value: 'chengbao'
        },
        {
          label: '自营食堂',
          value: 'ziying'
        },
        {
          label: '校外配餐',
          value: 'xiaowai'
        },
        {
          label: '不供餐',
          value: 'no'
        }
      ],
      complainDrawerShow: false,
      complainForm: {
        phone: '',
        email: '',
        link: ''
      },
      complainRules: {
        phone: [
          { required: false, message: '请输入联系电话', trigger: ['change', 'blur'] },
          { validator: validatePhone, trigger: ['change', 'blur'] }
        ],
        email: [
          { required: false, message: '请输入邮箱', trigger: ['change', 'blur'] },
          { validator: validateEmail, trigger: ['change', 'blur'] }
        ],
        link: [
          { required: false, message: '请输入链接', trigger: ['change', 'blur'] },
          { validator: validateLink, trigger: ['change', 'blur'] }
        ]
      },
      planDrawerShow: false,
      planForm: {
        type: false
      },
      planList: [
        {
          label: '未参与',
          value: false
        },
        {
          label: '已参与',
          value: true
        }
      ],
      uploading: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      fileLists: [],
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      infoDrawerShow: false,
      selectType: '',
      selectId: '',
      typeOfQualificationList: [
        {
          label: '事业单位法人证书',
          value: 'FR',
          disabled: false
        },
        {
          label: '营业执照',
          value: 'YY',
          disabled: false
        },
        {
          label: '餐饮服务许可证',
          value: 'CY',
          disabled: false
        },
        {
          label: '食品经营许可证',
          value: 'SP',
          disabled: false
        },
        {
          label: '卫生许可证',
          value: 'WS',
          disabled: false
        },
        {
          label: '消费安全证书',
          value: 'XF',
          disabled: false
        },
        {
          label: '税务登记证',
          value: 'SW',
          disabled: false
        }
      ],
      infoFormList: [],
      infoForm: {
        typeOfQualification: ''
      },
      legalRepresentativeForFR: '', // 法定代表人
      legalRepresentativeForSP: '' // 法定代表人
    }
  },
  computed: {
    computedPlan() {
      return d => {
        if (d) {
          return '已参与'
        } else {
          return '未参与'
        }
      }
    },
    computedTitle() {
      return d => {
        let str = ''
        switch (d) {
          case 'add':
            str = '新增资质'
            break
          case 'edit':
            str = '编辑资质'
            break
          case 'detail':
            str = '资质详情'
            break
        }
        return str
      }
    },
    computedDate() {
      return d => {
        return dayjs(d).format('YYYY年MM月DD日')
      }
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    handleInput(type, prop, value) {
      if (type === 'number') {
        // 过滤非数字字符，并转换为整数
        const filtered = String(value).replace(/[^\d]/g, '');
        console.log('看看', value, filtered, parseInt(filtered))
        // 确保结果大于0，否则置空
        this.infoForm[prop] = filtered && parseInt(filtered) > 0 ? parseInt(filtered) : null;
      }
    },
    initLoad() {
      this.getCanteenInfo()
      this.getQualificationPublicity()
    },
    // 获取食堂信息
    getCanteenInfo() {
      // 重置一下infoShowForm
      this.infoShowForm.level = 'excellent'
      this.infoShowForm.canteen_type = ''
      this.infoShowForm.canteen_type_alias = ''
      this.infoShowForm.plan = false
      this.infoShowForm.complain = {
        phone: '--',
        email: '--',
        link: '--'
      }
      // this.infoShowForm.tableData = []
      // 获取新的数据
      this.$apis.apiBackgroundFundSupervisionPublicityInfoGetCanteenInfoPost().then(res => {
        if (res.code === 0) {
          this.infoShowForm.level = res.data.security_level
          this.infoShowForm.canteen_type = res.data.canteen_type
          this.infoShowForm.canteen_type_alias = res.data.canteen_type_alias
          this.infoShowForm.school_type = res.data.school_type_alias
          let obj = {
            phone: res.data.complaint_phone || '--',
            email: res.data.complaint_email || '--',
            link: res.data.complaint_link || '--'
          }
          this.infoShowForm.complain = { ...obj }
          this.infoShowForm.plan = res.data.plan
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取资质公示
    getQualificationPublicity() {
      this.isLoading = true
      this.$apis.apiBackgroundFundSupervisionPublicityInfoGetQualificationInfoPost().then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.infoShowForm.tableData = deepClone(res.data)
          // 找一下法定代表人
          let FRList = res.data.filter(item => item.qualification_type === 'FR')
          let SPList = res.data.filter(item => item.qualification_type === 'SP')
          if (FRList.length) {
            this.legalRepresentativeForFR = FRList[0].extra.legalRepresentative || ''
          } else {
            this.legalRepresentativeForFR = ''
          }
          if (SPList.length) {
            this.legalRepresentativeForSP = SPList[0].extra.legalRepresentative || ''
          } else {
            this.legalRepresentativeForSP = ''
          }
          console.log('看看法定代表人', this.legalRepresentativeForFR, this.legalRepresentativeForSP)
          // 如果已经有了就禁用吧
          this.infoShowForm.tableData.forEach(item => {
            if (['FR', 'SP'].includes(item.qualification_type)) {
              let index = this.typeOfQualificationList.findIndex(itemIn => itemIn.value === item.qualification_type)
              this.typeOfQualificationList[index].disabled = true
            }
          })
          console.log('看看list', this.typeOfQualificationList)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 图片上传成功
    uploadSuccessForList(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        this.fileLists = []
        this.infoForm.img = res.data.public_url
      } else {
        this.infoForm.img = ''
        this.$message.error(res.msg)
      }
    },
    // 图片上传前检测
    beforeImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 2
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },
    showInfoDrawer(type, data) {
      this.selectType = type
      this.selectId = data && data.id ? data.id : ''
      if (type !== 'add') {
        this.changeTypeOfQualification(data.extra.typeOfQualification, false)
        this.$nextTick(() => {
          this.infoForm = deepClone(data.extra)
        })
      } else {
        let obj = {
          typeOfQualification: ''
        }
        this.infoForm = deepClone(obj)
        this.infoFormList = deepClone([])
      }
      console.log('看看infoForm', this.infoForm)
      this.infoDrawerShow = true
      setTimeout(() => {
        this.$refs.infoFormRef.clearValidate()
      }, 10)
    },
    changeTypeOfQualification(e, needToClear) {
      this.infoForm = {
        typeOfQualification: e
      }
      switch (e) {
        case 'FR': {
          this.infoForm = deepClone({
            ...this.infoForm,
            ...publicInstitutionForm
          })
          this.infoFormList = deepClone(publicInstitutionFormList)
          break
        }
        case 'YY': {
          this.infoForm = deepClone({
            ...this.infoForm,
            ...businessLicenseForm
          })
          this.infoFormList = deepClone(businessLicenseFormList)
          break
        }
        case 'CY': {
          this.infoForm = deepClone({
            ...this.infoForm,
            ...cateringServiceLicenseForm
          })
          this.infoFormList = deepClone(cateringServiceLicenseList)
          break
        }
        case 'SP': {
          this.infoForm = deepClone({
            ...this.infoForm,
            ...foodBusinessLicenseForm
          })
          this.infoFormList = deepClone(foodBusinessLicenseList)
          break
        }
        case 'WS': {
          this.infoForm = deepClone({
            ...this.infoForm,
            ...sanitaryPermitForm
          })
          this.infoFormList = deepClone(sanitaryPermitList)
          break
        }
        case 'XF': {
          this.infoForm = deepClone({
            ...this.infoForm,
            ...fireSafetyCertificateForm
          })
          this.infoFormList = deepClone(fireSafetyCertificateList)
          break
        }
        case 'SW': {
          this.infoForm = deepClone({
            ...this.infoForm,
            ...taxRegistrationCertificateForm
          })
          this.infoFormList = deepClone(taxRegistrationCertificateList)
          break
        }
      }
      if (needToClear) {
        this.$refs.infoFormRef.clearValidate()
      }
      console.log('this.infoForm', this.infoForm)
    },
    showDrawer(openType, data) {
      switch (openType) {
        case 'school_type':
          this.schoolTypeForm.school_type = data
          this.schoolTypeDrawerShow = true
          break
        case 'level':
          this.foodSafetyForm.level = data
          this.foodSafetyDrawerShow = true
          break
        case 'type':
          this.canteenTypeForm.type = data
          this.canteenTypeDrawerShow = true
          break
        case 'plan':
          this.planForm.type = data
          this.planDrawerShow = true
          break
        case 'complain':
          this.complainForm.phone = data.phone === '--' ? '' : data.phone
          this.complainForm.email = data.email === '--' ? '' : data.email
          this.complainForm.link = data.link === '--' ? '' : data.link
          this.complainDrawerShow = true
      }
    },
    // 抽屉的方法
    cancelHandle(type) {
      switch (type) {
        case 'school_type':
          this.$refs.schoolTypeFormRef.resetFields()
          this.schoolTypeDrawerShow = false
          break
        case 'level':
          this.$refs.foodSafetyFormRef.resetFields()
          this.foodSafetyDrawerShow = false
          break
        case 'type':
          this.$refs.canteenTypeFormRef.resetFields()
          this.canteenTypeDrawerShow = false
          break
        case 'plan':
          this.$refs.planFormRef.resetFields()
          this.planDrawerShow = false
          break
        case 'info':
          this.$refs.infoFormRef.resetFields()
          this.infoDrawerShow = false
          break
        case 'complain':
          this.$refs.complainFormRef.resetFields()
          this.complainDrawerShow = false
      }
    },
    saveHandle(type) {
      switch (type) {
        case 'school_type':
          this.changeSchoolType()
          break
        case 'level':
          this.changeLevel()
          break
        case 'type':
          this.changeType()
          break
        case 'plan':
          this.changePlan()
          break
        case 'info':
          this.addOrEditInfo()
          break
        case 'complain':
          this.changeComplain()
          break
      }
    },
    // 修改学校类型
    changeSchoolType() {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifySchoolTypePost({
        school_type: this.schoolTypeForm.school_type
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.schoolTypeFormRef.resetFields()
        this.schoolTypeDrawerShow = false
        this.getCanteenInfo()
      })
    },
    // 修改等级
    changeLevel() {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifySecurityLevelPost({
        security_level: this.foodSafetyForm.level
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.foodSafetyFormRef.resetFields()
        this.foodSafetyDrawerShow = false
        this.getCanteenInfo()
      })
    },
    // 修改食堂类型
    changeType() {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyCanteenTypePost({
        canteen_type: this.canteenTypeForm.type
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.canteenTypeFormRef.resetFields()
        this.canteenTypeDrawerShow = false
        this.getCanteenInfo()
      })
    },
    // 修改营养计划
    changePlan() {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyCanteenPlanPost({
        plan: this.planForm.type
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.planFormRef.resetFields()
        this.planDrawerShow = false
        this.getCanteenInfo()
      })
    },
    // 修改投诉
    changeComplain() {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyComplaintInfoPost({
        complaint_phone: this.complainForm.phone,
        complaint_email: this.complainForm.email,
        complaint_link: this.complainForm.link
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.complainFormRef.resetFields()
        this.complainDrawerShow = false
        this.getCanteenInfo()
      })
    },
    // 新增或编辑资质
    addOrEditInfo() {
      if (['FR', 'SP'].includes(this.infoForm.typeOfQualification)) {
        // 校验法人是否一致
        if (this.infoForm.typeOfQualification === 'SP') {
          if (this.legalRepresentativeForFR && this.legalRepresentativeForFR !== this.infoForm.legalRepresentative) {
            this.$confirm('学校法人与食品经营许可证法人不一致，确认提交资质信息?', '提示', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.modifyInfo()
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消'
              })
            })
          } else {
            this.modifyInfo()
          }
        } else if (this.infoForm.typeOfQualification === 'FR') {
          if (this.legalRepresentativeForSP && this.legalRepresentativeForSP !== this.infoForm.legalRepresentative) {
            this.$confirm('学校法人与食品经营许可证法人不一致，确认提交资质信息?', '提示', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.modifyInfo()
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消'
              })
            })
          } else {
            this.modifyInfo()
          }
        }
      } else {
        this.modifyInfo()
      }
    },
    modifyInfo() {
      this.$refs.infoFormRef.validate((valid) => {
        if (valid) {
          let param = {
            id: this.selectType === 'edit' ? this.selectId : undefined,
            qualification_type: this.infoForm.typeOfQualification,
            image: this.infoForm.img,
            extra: { ...this.infoForm }
          }
          if (this.selectType === 'add') {
            this.addInfo(param)
          } else {
            this.editInfo(param)
          }
        } else {
          this.$message.error('请检查表单内容是否正确')
        }
      })
    },
    addInfo(param) {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoAddQualificationPost(param).then(res => {
        if (res.code === 0) {
          this.$message.success('新增成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.infoFormRef.resetFields()
        this.typeOfQualificationList = this.typeOfQualificationList.map(item => {
          item.disabled = false
          return item
        })
        this.infoDrawerShow = false
        this.getQualificationPublicity()
      })
    },
    editInfo(param) {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifyQualificationPost(param).then(res => {
        if (res.code === 0) {
          this.$message.success('编辑成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.infoFormRef.resetFields()
        this.typeOfQualificationList = this.typeOfQualificationList.map(item => {
          item.disabled = false
          return item
        })
        this.infoDrawerShow = false
        this.getQualificationPublicity()
      })
    },
    // 删除资质
    deleteInfo(data) {
      this.$confirm(`确定要删除${data.qualification_type_alias}的公示信息？删除后不可恢复，请谨慎操作。`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.deleteHandle(data)
      }).catch(action => {
        this.$message.info(`您已取消删除${data.qualification_type_alias}的公示信息`)
      })
    },
    deleteHandle(data) {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoDeleteQualificationPost({
        id: data.id
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('删除成功')
          this.getQualificationPublicity()
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.info-show {
  .level-show {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    &-right {
      display: flex;
      &-tips {
        display: flex;
        justify-content: flex-start;
        align-items: flex-end;
      }
    }
  }
  .qualification-publicity {
    display: grid;
    grid-template-columns: repeat(6, 200px);
    grid-gap: 20px;
    &-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start
    }
  }
  .certification-info-show {
    border-top: 1px solid #C0C4CC;
    padding-top: 20px;
    &-tips {
      color: #C0C4CC;
    }
    &-img {
      width: 200px;
      height: 200px;
    }
    &-content {
      display: grid;
      grid-template-columns: repeat(3, 200px);
      grid-gap: 20px;
      &-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        &-close {
          padding: 2px;
          border-radius: 4px;
          line-height: 14px;
          z-index: 10;
          position: absolute;
          top: 2px;
          right: 2px;
          background-color: #C0C4CC;
          color: #fff;
        }
      }
    }
  }
}
</style>
