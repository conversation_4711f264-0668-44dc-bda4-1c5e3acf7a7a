<template>
  <div class="feedback-dialog">
    <CustomDrawer
      :show.sync="showDrawer"
      :size="size"
      :title="title"
      :loading.sync="isLoading"
      :showFooter="false"
      v-bind="$attrs"
      v-on="$listeners"
    >
    <el-form
      :model="dialogForm"
      @submit.native.prevent
      ref="dialogForm"
      :rules="dialogFormRules"
      label-width="100px"
      class="dialog-feedback-form"
      v-loading="isLoading"
    >
      <div class="fb-box line-bottom m-b-20">
        <div class="fb-item clearfix">
          <div class="fb-item-label">所属项目</div>
          <div class="fb-item-value">{{ dialogInfo.company_name || '--' }}</div>
        </div>
        <div class="fb-item clearfix">
          <div class="fb-item-label">人员编号</div>
          <div class="fb-item-value">{{ dialogInfo.person_no || '--' }}</div>
        </div>
        <div class="fb-item clearfix">
          <div class="fb-item-label">用户姓名</div>
          <div class="fb-item-value">{{ dialogInfo.person_name || '--' }}</div>
        </div>
        <div class="fb-item clearfix m-b-10">
          <div class="fb-item-label">手机号</div>
          <div class="fb-item-value">{{ dialogInfo.phone || '--' }}</div>
        </div>
      </div>
      <div class="fb-box">
        <div class="fb-item clearfix">
          <div class="fb-item-label">反馈时间</div>
          <div class="fb-item-value">{{ dialogInfo.create_time | formatDate }}</div>
        </div>
        <div class="fb-item clearfix m-b-10">
          <div class="fb-item-label">反馈内容</div>
          <div class="fb-item-value fb-item-content">{{ dialogInfo.remark || '--' }}</div>
        </div>
        <div class="fb-item clearfix">
          <div class="fb-item-label no-line-height">反馈图片</div>
          <div class="fb-item-value" v-if="dialogInfo.feedback_images && dialogInfo.feedback_images.length > 0">
            <el-image v-for="(img, i) in dialogInfo.feedback_images" :key="img+i" :src="img" class="evalute-img m-r-20" :preview-src-list="dialogInfo.feedback_images">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
          <div class="no-line-height m-b-20" v-else>--</div>
        </div>
      </div>
      <el-form-item label="超管回复" class="no-line-height m-t-6">
        <el-input v-if="canReply" v-model="dialogForm.replyContent" class="ps-input w-input" type="textarea" :rows="4" :maxlength="150"></el-input>
        <div v-if="!canReply">
          <div class="m-b-10" v-if="dialogInfo.merchant_reply_time">{{ dialogInfo.merchant_reply_time | formatDate }}</div>
          <div class="m-b-10" v-else>--</div>
          <div class="is-reply" v-if="dialogInfo.merchant_remark">{{ dialogInfo.merchant_remark }}</div>
        </div>
      </el-form-item>
      <el-form-item label-width="0">
        <div class="ps-drawer-footer">
          <!-- ps-cancel-btn -->
          <el-button @click="closeHandle">{{ canReply ? '取消' : '关闭' }}</el-button>
          <el-button v-if="canReply" class="ps-origin-btn" @click="submitHandle">保存</el-button>
        </div>
      </el-form-item>
    </el-form>
    </CustomDrawer>
  </div>
</template>

<script>
export default {
  name: 'AddVisitorAccessAreaDialog',
  props: {
    isshow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '反馈详情'
    },
    size: {
      type: String,
      default: '700px'
    },
    dialogInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      dialogForm: {
        replyContent: ''
      },
      dialogFormRules: {
        replyContent: [{ required: true, message: '请输入回复内容', trigger: 'blur' }]
      },
      feedback_images: [
        'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png',
        'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png',
        'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png',
        'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'
      ]
    }
  },
  computed: {
    showDrawer: {
      get() {
        if (this.isshow) {
          this.dialogForm.replyContent = ''
        }
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    },
    canReply() {
      return this.dialogInfo.feedback_status === 'no_reply' && this.type === 'reply'
    }
  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {
    submitHandle() {
      // this.$refs.dialogForm.validate((valid) => {
      //   if (valid) {
      //     this.replyHandle()
      //   } else {
      //     console.warn('error submit')
      //   }
      // })
      if (this.dialogForm.replyContent) {
        this.replyHandle()
      } else {
        this.$message.error('请输入回复内容')
      }
    },
    // 回复
    async replyHandle() {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundFeedbackSuperFeedbackRecordModifyPost({
          id: this.dialogInfo .id,
          feedback_status: 'reply',
          merchant_remark: this.dialogForm.replyContent
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDrawer = false
        this.$message.success(res.msg)
        this.$emit('confirm')
      } else {
        this.$message.error(res.msg)
      }
    },
    closeHandle() {
      this.showDrawer = false
    }
  }
};
</script>

<style scoped lang="scss">
.feedback-dialog{
  .w-input {
    width: 100%;
  }
  .no-line-height {
    line-height: 1.2;
  }
  .dialog-feedback-form {
    margin-right: 30px;
  }
  .fb-item {
    font-size: 14px;
    line-height: 40px;
    .fb-item-label {
      float: left;
      width: 100px;
      text-align: right;
      vertical-align: middle;
      float: left;
      color: #606266;
      padding: 0 12px 0 0;
      // font-weight: 600;
    }
    .fb-item-value {
      margin-left: 100px;
      vertical-align: middle;
      word-break: break-all;
      flex-wrap: wrap;
    }
    .fb-item-content {
      padding-top: 12px;
      line-height: 1.2;
    }
  }
  .line-bottom {
    position: relative;
    &::after{
      content: '';
      position: absolute;
      left: 30px;
      right: 0;
      height: 1px;
      background-color: #797979;
    }
  }
  ::v-deep.el-form-item__label{
    font-weight: 500;
  }
  ::v-deep.no-line-height {
    .el-form-item__label {
      line-height: 1.2;
      vertical-align: top;
    }
    .el-form-item__content {
      line-height: 1.2;
      vertical-align: top;
    }
  }
  .evalute-img{
    width: 60px;
    height: 60px;
    .el-icon-picture-outline{
      font-size: 60px;
      opacity: .5;
    }
  }
  .is-reply {
    min-height: 40px;
    line-height: 1.5;
    padding: 12px 10px;
    border-radius: 8px;
    background-color: #e7e9ef;
    word-break: break-all;
  }
}
</style>
