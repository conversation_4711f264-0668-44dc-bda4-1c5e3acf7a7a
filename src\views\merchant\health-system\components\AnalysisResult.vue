<template>
  <div class="ps-el-drawer">
    <el-drawer
      :title="'营养分析'"
      :visible="visible"
      :show-close="false"
      size="70%">
      <div class="drawer-content p-20 flex-col">
        <div class="flex-b-c m-b-20">
          <div class="flex-b-c">
            <el-button size="small" :class="['w-100', type === 'day' ? 'ps-origin-btn' : '']" @click="type = 'day'">按天</el-button>
            <el-button size="small" :class="['w-100', type === 'week' ? 'ps-origin-btn' : '']" @click="type = 'week'">按自然周</el-button>
          </div>
          <special-date-picker :selectType="type" :selectedId="selectedId" :showRecord="visible" @setHealthyInfo="setHealthyInfo"></special-date-picker>
        </div>
        <div class="drawer-content-list" v-if="type === 'day'">
          <div class="f-w-700 font-size-18 m-b-10">食物清单（{{ this.foodList.length }}）</div>
          <div class="bg-grey flex-center">
            <div class="drawer-content-list-box w-100-p">
              <div v-for="(item, index) in foodList" :key="index"  class="drawer-content-list-box-item flex-b-c">
                <div>{{ item.name }}</div>
                <div>{{ item.weight }}g</div>
              </div>
            </div>
          </div>
        </div>
        <div class="drawer-content-layout">
          <div class="f-w-700 font-size-18 m-b-10">饮食结构</div>
          <div class="bg-grey flex-center">
            <div class="drawer-content-layout-box w-100-p">
              <div v-for="(item, index) in dietData" :key="index"  class="drawer-content-layout-box-item flex-b-c">
                <div>{{ item.text }}：{{ item.value }}g / {{ item.range[0] }}g - {{ item.range[1] }}g</div>
                <div v-if="computedStatus(item) !== 'none'">
                  <i :class="[computedStatus(item) === 'up' ? 'el-icon-top' : 'el-icon-bottom']" :style="computedStatus(item) === 'up' ? { 'color': '#D9001B'} : { 'color': '#F59A23'}"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="drawer-content-analysis">
          <div class="f-w-700 font-size-18 m-b-10">总能量分析</div>
          <div class="bg-grey flex-center">
            <div class="drawer-content-analysis-box w-100-p">
              <div v-for="(item, index) in analysisData" :key="index"  class="drawer-content-analysis-box-item flex-b-c">
                <div>{{ item.text }}：{{ item.value }}kcal / {{ item.range[0] }}kcal - {{ item.range[1] }}kcal</div>
                <div v-if="computedStatus(item) !== 'none'">
                  <i :class="[computedStatus(item) === 'up' ? 'el-icon-top' : 'el-icon-bottom']" :style="computedStatus(item) === 'up' ? { 'color': '#D9001B'} : { 'color': '#F59A23'}"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">能量来源</div>
          <div class="bg-grey">
            <el-table ref="tableView" :data="energyTableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
              <table-column  v-for="item in energyTableSetting" :key="item.key" :col="item">
                <template #value="{ row }">
                  <div class="text-right">
                    <span class="m-r-10">{{ row.value }}{{ row.unit }}</span>
                    <i :class="[computedStatus(row) === 'up' ? 'el-icon-top' : 'el-icon-bottom']" :style="computedStatus(row) === 'up' ? { 'color': '#D9001B'} : { 'color': '#F59A23'}"></i>
                  </div>
                </template>
                <template #value_rate="{ row }">
                  {{ row.range[0] }} - {{ row.range[1] }}g
                </template>
                <template #range="{ row }">
                  {{ row.value_rate }}%
                </template>
                <template #range_rate="{ row }">
                  {{ row.range_rate[0] }} - {{ row.range_rate[1] }}%
                </template>
              </table-column>
            </el-table>
          </div>
        </div>
        <div class="m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">营养素分析</div>
          <div class="bg-grey drawer-content-table">
            <el-table ref="tableView" :data="nutrientTableData[0]" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
              <table-column  v-for="(item, tableSettingIndex) in nutrientTableSetting" :key="tableSettingIndex" :col="item">
                <template #value="{ row }">
                  <div class="text-right">
                    <span class="m-r-10">{{ row.value }}{{ row.unit }}</span>
                    <i :class="[computedStatus(row) === 'up' ? 'el-icon-top' : 'el-icon-bottom']" :style="computedStatus(row) === 'up' ? { 'color': '#D9001B'} : { 'color': '#F59A23'}"></i>
                  </div>
                </template>
                <template #range="{ row }">
                  {{ row.range[0] }} - {{ row.range[1] }}{{ row.unit }}
                </template>
              </table-column>
            </el-table>
            <el-table ref="tableView" :data="nutrientTableData[1]" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
              <table-column  v-for="(item, tableSettingIndex) in nutrientTableSetting" :key="tableSettingIndex" :col="item">
                <template #value="{ row }">
                  <div class="text-right">
                    <span class="m-r-10">{{ row.value }}{{ row.unit }}</span>
                    <i :class="[computedStatus(row) === 'up' ? 'el-icon-top' : 'el-icon-bottom']" :style="computedStatus(row) === 'up' ? { 'color': '#D9001B'} : { 'color': '#F59A23'}"></i>
                  </div>
                </template>
                <template #range="{ row }">
                  {{ row.range[0] }} - {{ row.range[1] }}{{ row.unit }}
                </template>
              </table-column>
            </el-table>
          </div>
        </div>
        <div class="m-b-20">
          <div class="f-w-700 font-size-18 m-b-10">饮食分析</div>
          <div class="bg-grey">
            <div v-for="(item, index) in nutritionAnalyzeTextData" :key="index">
              {{ index + 1 }}. {{ item }}
            </div>
          </div>
        </div>
        <div class="m-b-20" v-if="type === 'week'">
          <div class="f-w-700 font-size-18 m-b-10">指导建议</div>
          <div class="bg-grey">
            <div v-for="(item, index) in suggestionTextData" :key="index">
              {{ index + 1 }}. {{ item }}
            </div>
          </div>
        </div>
        <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
          <el-button size="small" type="primary" class="w-100" @click="closeDrawer">关闭</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import SpecialDatePicker from './SpecialDatePicker.vue'
import { deepClone } from '@/utils'

export default {
  components: {
    SpecialDatePicker
  },
  props: {
    isShow: Boolean,
    selectedId: Number
  },
  data() {
    return {
      isLoading: false,
      type: 'day',
      foodList: [],
      dietData: [],
      analysisData: [],
      energyTableData: [],
      nutrientTableData: [],
      nutritionAnalyzeTextData: [],
      suggestionTextData: [],
      energyTableSetting: [
        { label: '三大供能营养素', align: 'right', key: 'text' },
        { label: '摄入量', align: 'right', key: 'value', type: "slot", slotName: "value" },
        { label: '推荐量', align: 'right', key: 'value_rate', type: "slot", slotName: "value_rate" },
        { label: '摄入供能比', align: 'right', key: 'range', type: "slot", slotName: "range" },
        { label: '推荐供能比', align: 'right', key: 'range_rate', type: "slot", slotName: "range_rate" }
      ],
      nutrientTableSetting: [
        { label: '营养素名称', align: 'right', key: 'text' },
        { label: '摄入量', align: 'right', key: 'value', type: "slot", slotName: "value" },
        { label: '推荐量', align: 'right', key: 'range', type: "slot", slotName: "range" }
      ],
      pickerOptions: this.disableDate()
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    },
    computedWeek() {
      return d => {
        let start = dayjs(d).startOf('week').format('YYYY-MM-DD')
        let end = dayjs(d).endOf('week').format('YYYY-MM-DD')
        if (this.type === 'day') {
          return dayjs(d).format('YYYY-MM-DD')
        } else {
          return `${start} 至 ${end}`
        }
      }
    },
    computedStatus() {
      return d => {
        let value = d.value
        let range = d.range
        let str = ''
        if (value < range[0]) {
          str = 'down'
        } else if (value > range[1]) {
          str = 'up'
        } else {
          str = 'none'
        }
        return str
      }
    }
  },
  methods: {
    disableDate() {
      let that = this
      return {
        disabledDate(time) {
          if (that.type === 'week') {
            return time.getTime() > new Date(dayjs().endOf('week')).getTime()
          } else {
            return time.getTime() > Date.now()
          }
        }
      }
    },
    setHealthyInfo(e) {
      if (this.type === 'day') {
        this.foodList = e ? deepClone(e.food_data) : []
        this.dietData = e ? deepClone(e.food_diversity.data) : []
        this.analysisData = e ? deepClone(e.energy_kcal_data) : []
        this.energyTableData = e ? deepClone(e.three_nutrition) : []
        // 处理一下营养素的数据
        if (e.total_nutrition && e.total_nutrition.length !== 0) {
          let arr1 = e.total_nutrition.filter((item, index) => index % 2 === 0)
          let arr2 = e.total_nutrition.filter((item, index) => index % 2 !== 0)
          this.nutrientTableData = [
            arr1,
            arr2
          ]
        } else {
          this.nutrientTableData = []
        }
        let newArr = []
        if (e && e.nutrition_analyze_text) {
          newArr = [
            ...(e.nutrition_analyze_text.meal_type || []),
            ...(e.nutrition_analyze_text.category || []),
            ...(e.nutrition_analyze_text.full || [])
          ]
        }
        this.nutritionAnalyzeTextData = e ? deepClone(newArr) : []
      } else {
        this.dietData = e ? deepClone(e.food_diversity.data) : []
        this.analysisData = e ? deepClone(e.energy_kcal_data) : []
        this.energyTableData = e ? deepClone(e.three_nutrition) : []
        // 处理一下营养素的数据
        if (e && e.total_nutrition && e.total_nutrition.length !== 0) {
          let arr1 = e.total_nutrition.filter((item, index) => index % 2 === 0)
          let arr2 = e.total_nutrition.filter((item, index) => index % 2 !== 0)
          this.nutrientTableData = [
            arr1,
            arr2
          ]
        } else {
          this.nutrientTableData = []
        }
        let newArr1 = []
        if (e && e.nutrition_analyze_text) {
          newArr1 = [
            ...(e.nutrition_analyze_text.category || []),
            ...(e.nutrition_analyze_text.full || [])
          ]
        }
        this.nutritionAnalyzeTextData = e ? deepClone(newArr1) : []
        this.suggestionTextData = e ? deepClone(e.nutrition_guid_text) : []
      }
    },
    closeDrawer() {
      this.type = 'day'
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-content {
  &-list {
    &-box {
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      grid-gap: 10px;
      &-item{
        font-size: 14px;
        padding: 6px 12px;
        border-radius: 4px;
        background-color: #fff;
      }
    }
  }
  &-layout {
    &-box {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 10px;
      &-item{
        font-size: 14px;
        padding: 6px 12px;
        border-radius: 4px;
        background-color: #fff;
      }
    }
  }
  &-analysis {
    &-box {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 10px;
      &-item{
        font-size: 14px;
        padding: 6px 12px;
        border-radius: 4px;
        background-color: #fff;
      }
    }
  }
  &-table {
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }
  .bg-grey {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
  }
}
</style>
