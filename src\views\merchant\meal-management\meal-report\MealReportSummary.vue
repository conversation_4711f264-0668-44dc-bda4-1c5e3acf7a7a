<template>
  <div class="ReportSummary container-wrapper">
    <div class="table-type">
      <div
        :class="['table-type-btn', tableType === 'department' ? 'active-btn' : '']"
        @click="changeTableType('department')"
        v-permission="['background_order.order_report_meal.department_group_summary_list']"
      >
        部门汇总
      </div>
      <div
        :class="['table-type-btn', tableType === 'group' ? 'active-btn' : '']"
        @click="changeTableType('group')"
        v-permission="['background_order.order_report_meal.group_summary_list']"
      >
        分组汇总
      </div>
    </div>
    <department-summary
      v-if="tableType === 'department'"
      ref="department"
      v-permission="['background_order.order_report_meal.department_group_summary_list']"
    />
    <group-summary
      v-if="tableType === 'group'"
      ref="group"
      v-permission="['background_order.order_report_meal.group_summary_list']"
    />
  </div>
</template>
<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'
import DepartmentSummary from './component/DepartmentSummary'
import GroupSummary from './component/GroupSummary'
export default {
  // mixins: [activatedLoadData],
  components: { DepartmentSummary, GroupSummary },
  name: 'MealReportSummary',
  data() {
    return {
      tableType: 'department'
    }
  },
  created() {
    if (this.$route.query.tableType) {
      this.tableType = this.$route.query.tableType
    }
  },
  mounted() {},
  methods: {
    initLoad() {},
    changeTableType(type) {
      this.tableType = type
      this.changeHash()
    },
    changeHash() {
      this.$router.replace({
        name: 'MerchantMealReportSummary',
        query: {
          tableType: this.tableType
        }
      })
    },

    // 节下流咯
    searchHandle: debounce(function () {}, 300)
  }
}
</script>
<style lang="scss" scoped>
.ReportSummary {
  .table-type {
    padding: 20px 0;
    display: flex;
    font-size: 16px;
    .table-type-btn {
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      background-color: #e8f0f8;
      border-radius: 40px;
      margin-right: 20px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn {
      color: #fff;
      background-color: #ff9b45;
      border: none;
    }
  }
}
</style>
