<template>
  <div v-loading="isLoading" class="delivery-order-box">
    <div class="info-wrap">
      <div class="title">基本信息</div>
      <div class="form-item"><span class="form-label">单据编号：</span>{{ detailData.trade_no }}</div>
      <div class="form-item"><span class="form-label">创建时间：</span>{{ detailData.create_time }}</div>

      <div class="form-item"><span class="form-label">采购日期：</span>{{ detailData.purchase_time }}</div>
      <div class="form-item"><span class="form-label">经手人：</span>{{ detailData.account_name }}</div>
      <!-- <div v-if="tabType === 'deliveryOrder'" class="form-item"><span class="form-label">送达时间：</span>{{  }}</div> -->
    </div>

    <div class="info-wrap">
      <div class="title">物资信息</div>
      <div>合计金额：￥{{ totalPrice }}</div>
      <el-table
        :data="detailData.materials_detail"
        ref="tableData"
        style="width: 100%"
        stripe
        size="mini"
        header-row-class-name="ps-table-header-row"
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item">
        </table-column>
      </el-table>
    </div>
  </div>
  <!-- end -->
</template>

<script>
import { divide } from '@/utils'
import NP from 'number-precision'

export default {
  name: 'DeliveryOrderBox',
  props: {
    type: { //
      type: String,
      default: 'detail'
    },
    params: {
      type: Object,
      default: () => {}
    },
    api: {
      type: String,
      default: ''
    },
    infoData: {
      type: Object,
      default: () => {}
    }
  },
  components: {
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '采购数量', key: 'count' },
        { label: '规格', key: 'material_specification_record' },
        { label: '成本价', key: 'ref_unit_price' },
        { label: '合计金额', key: 'total' }
      ],
      detailData: {},
      totalPrice: 0 // 合计金额
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getDeliveryNoteInfo()
    },
    async getDeliveryNoteInfo() {
      if (this.isLoading) return
      let currentApi = this.api
      // if (!this.api && this.type === 'detail') {
      //   currentApi = 'apiBackgroundDrpVendorDataVendorDeliveryDetailListPost'
      // }
      if (!currentApi) {
        return this.$message.error('缺少参数，请检查！')
      }
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis[currentApi](this.params))
      this.isLoading = false
      this.detailData = {}
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let data = res.data || {}
        this.totalPrice = 0
        // eslint-disable-next-line camelcase
        if (data?.materials_detail) {
          data.materials_detail = data.materials_detail.map((v) => {
            this.totalPrice = NP.plus(this.totalPrice, v.total)
            v.count = v.count + (v.unit_name || '')
            v.ref_unit_price = "￥" + divide(v.ref_unit_price)
            v.total = "￥" + divide(v.total)
            // v.unit_name = "￥" + divide(v.unit_name)
            // v.valid_date = v.start_valid_date + "-" + v.end_valid_date
            return v
          })
        }
        this.totalPrice = divide(this.totalPrice)
        this.detailData = data || {}
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.delivery-order-box{
  .info-wrap{
    font-size: 14px;
    line-height: 30px;
    .title{
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0 10px;
    }
    .form-item {
      display: flex;
      font-size: 14px;
      line-height: 30px;
    }
    .form-img-box {
      flex: 1;
    }
    .detault-img {
      width: 160px;
      height: 92px;
      border: 1px dashed #e1e1e1;
      border-radius: 2px;
    }
  }
}
</style>
