<template>
  <div class="ps-el-drawer">
    <el-drawer
      :title="type === 'add' ? '新建规则' : '编辑规则'"
      :visible="visible"
      :show-close="false"
      size="35%">
      <div class="p-20 flex-col">
        <el-form
          :model="formData"
          @submit.native.prevent
          status-icon
          ref="formDataRef"
          :rules="formDataRules"
          label-width="90px"
          class="addData-form"
          label-position="right"
        >
          <div>
            <el-form-item label="规则名称:" prop="name">
              <el-input v-model="formData.name" class="ps-input w-250" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="系统版本:" prop="toll_version">
              <el-select v-model="formData.toll_version" placeholder="请选择系统收费版本（单选）" class="w-250">
                <el-option
                  v-for="(item, index) in versionList"
                  :key="index"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="收费规则:" prop="fee">
              <div class="addData-form-content p-t-10 p-l-20 p-r-20 p-b-20">
                <span class="origin">提示：续费优惠设置的折扣不填，则商户续费时不可见</span>
                <el-form-item label="用户收费金额" label-width="100px">
                  <el-input size="mini" v-model="formData.fee" class="w-80 m-r-10" type="number"></el-input>
                  <span>元，人/年</span>
                </el-form-item>
                <el-form-item label="续费优惠设置" label-width="100px">
                  <el-form-item prop="first_discount" :inline-message="true">
                    <span>1年折扣</span>
                    <el-input size="mini" v-model="formData.first_discount" class="w-80 m-l-10 m-r-10" type="number"></el-input>
                    <span>%</span>
                  </el-form-item>
                  <el-form-item prop="second_discount" :inline-message="true">
                    <span>2年折扣</span>
                    <el-input size="mini" v-model="formData.second_discount" class="w-80 m-l-10 m-r-10" type="number"></el-input>
                    <span>%</span>
                  </el-form-item>
                  <el-form-item prop="third_discount" :inline-message="true">
                    <span>3年折扣</span>
                    <el-input size="mini" v-model="formData.third_discount" class="w-80 m-l-10 m-r-10" type="number"></el-input>
                    <span>%</span>
                  </el-form-item>
                  <el-form-item prop="fourth_discount" :inline-message="true">
                    <span>4年折扣</span>
                    <el-input size="mini" v-model="formData.fourth_discount" class="w-80 m-l-10 m-r-10" type="number"></el-input>
                    <span>%</span>
                  </el-form-item>
                  <el-form-item prop="fifth_discount" :inline-message="true">
                    <span>5年折扣</span>
                    <el-input size="mini" v-model="formData.fifth_discount" class="w-80 m-l-10 m-r-10" type="number"></el-input>
                    <span>%</span>
                  </el-form-item>
                </el-form-item>
              </div>
            </el-form-item>
          </div>
        </el-form>
        <div class="ps-el-drawer-footer ps-flex-align-c flex-align-c">
          <div class="m-r-30">
            <el-button size="small"  class="w-100" @click="cancel">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="save">保存</el-button>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { deepClone, times } from '@/utils'
export default {
  name: 'AddOrEditDialog',
  props: {
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    nameList: {
      type: Array,
      default: () => {
        return []
      }
    },
    isShow: Boolean
  },
  data() {
    let checkName = (rule, value, callback) => {
      if (value) {
        if (this.type === 'add') {
          if (this.nameList.includes(value)) {
            callback(new Error('该规则名已被使用，请重新输入'))
          } else {
            callback()
          }
        } else {
          if (this.newNameList.includes(value)) {
            callback(new Error('该规则名已被使用，请重新输入'))
          } else {
            callback()
          }
        }
      } else {
        callback(new Error('请输入活动名称'))
      }
    }
    let checkFee = (rule, value, callback) => {
      const regex = /^(?:\d{1,2}|\d{1,2}\.\d)$/
      if (value) {
        if (regex.test(value)) {
          if (value < 6) {
            callback(new Error('最低不能低于6元/人/年'))
          } else {
            callback()
          }
        } else {
          callback(new Error('请输入一个两位且至多保留一位小数的数'))
        }
      } else {
        callback()
      }
    }
    let checkDiscount = (rule, value, callback) => {
      const discountList = ['first_discount', 'second_discount', 'third_discount', 'fourth_discount', 'fifth_discount']
      const regex = /^(0|[1-9][0-9]?)$/
      if (value) {
        if (rule.fullField !== 'first_discount' && !regex.test(value)) {
          callback(new Error('最多输入两位且不能有小数'))
        } else {
          if (rule.fullField === 'first_discount' && value > 100) {
            callback(new Error('最大不能超过100'))
          } else if (rule.fullField !== 'first_discount') {
            let thisDiscountIndex = discountList.indexOf(rule.fullField)
            for (let count = 1; count <= thisDiscountIndex; count++) {
              let lastDiscountIndex = (thisDiscountIndex - count)
              if (this.formData[`${discountList[lastDiscountIndex]}`]) {
                if (value > this.formData[`${discountList[lastDiscountIndex]}`]) {
                  callback(new Error(`第${thisDiscountIndex + 1}年折扣需小于第${lastDiscountIndex + 1}年`))
                } else {
                  callback()
                }
              } else {
                continue
              }
            }
          } else {
            callback()
          }
        }
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      formDataRules: {
        name: [
          { max: 10, message: '最大输入10个字符', trigger: ['change', 'blur'] },
          { validator: checkName, trigger: ['change', 'blur'] }
        ],
        toll_version: [
          { required: true, message: '请选择收费版本', trigger: 'blur' }
        ],
        fee: [
          { required: true, message: '用户收费金额不能为空', trigger: ['change', 'blur'] },
          { validator: checkFee, trigger: ['change', 'blur'] }
        ],
        first_discount: [
          { validator: checkDiscount, trigger: ['change', 'blur'] }
        ],
        second_discount: [
          { validator: checkDiscount, trigger: ['change', 'blur'] }
        ],
        third_discount: [
          { validator: checkDiscount, trigger: ['change', 'blur'] }
        ],
        fourth_discount: [
          { validator: checkDiscount, trigger: ['change', 'blur'] }
        ],
        fifth_discount: [
          { validator: checkDiscount, trigger: ['change', 'blur'] }
        ]
      },
      formData: {},
      newNameList: [],
      versionList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {
    isShow(newVal) {
      if (newVal) {
        this.formData = deepClone(this.rowData)
        this.newNameList = deepClone(this.nameList.filter(item => item !== this.formData.name))
        this.getVersionList()
      }
    }
  },
  methods: {
    save() {
      let params = {
        id: this.type === 'add' ? undefined : this.rowData.id,
        name: this.formData.name,
        toll_version: this.formData.toll_version,
        fee: times(this.formData.fee),
        first_discount: this.formData.first_discount || undefined,
        second_discount: this.formData.second_discount || undefined,
        third_discount: this.formData.third_discount || undefined,
        fourth_discount: this.formData.fourth_discount || undefined,
        fifth_discount: this.formData.fifth_discount || undefined
      }
      this.$refs.formDataRef.validate((valid) => {
        if (valid) {
          if (this.type === 'edit') {
            Object.assign(params, { id: this.rowData.id })
            this.$apis.apiBackgroundAdminBackgroundTollRuleModifyPost(params)
              .then(res => {
                if (res.code === 0) {
                  this.$message.success('编辑成功')
                } else {
                  this.$message.error(res.msg)
                }
              })
          } else {
            this.$apis.apiBackgroundAdminBackgroundTollRuleAddPost(params)
              .then(res => {
                if (res.code === 0) {
                  this.$message.success('新增成功')
                } else {
                  this.$message.error(res.msg)
                }
              })
          }
        } else {
          return this.$message.error('创建失败，请确认规则填写是否正确');
        }
        this.visible = false
      });
    },
    cancel() {
      this.$refs.formDataRef.resetFields()
      this.visible = false
    },
    // 获取系统版本列表
    getVersionList() {
      this.$apis.apiBackgroundAdminBackgroundTollVersionListPost({
        page: 1,
        page_size: 9999,
        is_toll: true
      }).then(res => {
        if (res.code === 0) {
          this.versionList = deepClone(res.data.results) || []
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.addData-form {
  &-content {
    border-radius: 6px;
    background-color: #f8f9fa;
  }
}
</style>
