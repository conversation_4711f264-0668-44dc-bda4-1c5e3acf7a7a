<template>
  <div class="evaluate-dialog">
    <CustomDrawer
      :show.sync="showDrawer"
      :size="size"
      :title="title"
      :loading.sync="isLoading"
      :showFooter="false"
      v-bind="$attrs"
      v-on="$listeners"
    >
    <el-form
      :model="dialogForm"
      @submit.native.prevent
      ref="dialogForm"
      :rules="dialogFormRules"
      label-width="100px"
      class="dialog-evaluate-form"
      v-loading="isLoading"
    >
      <div class="fb-box line-bottom m-b-10 p-b-14">
        <div class="fb-item clearfix">
          <div class="fb-item-label">组织名称</div>
          <div class="fb-item-value">{{ dialogData.organization_name || '--' }}</div>
        </div>
        <div v-if="!dialogData.is_anonymous" class="">
          <div class="fb-item clearfix">
            <div class="fb-item-label">人员编号</div>
            <div class="fb-item-value">{{ dialogData.person_no || '--' }}</div>
          </div>
          <div class="fb-item clearfix">
            <div class="fb-item-label">用户姓名</div>
            <div class="fb-item-value">{{ dialogData.name || '--' }}</div>
          </div>
          <div class="fb-item clearfix">
            <div class="fb-item-label">手机号</div>
            <div class="fb-item-value">{{ dialogData.phone || '--' }}</div>
          </div>
          <div class="fb-item clearfix">
            <div class="fb-item-label">订单号</div>
            <div class="fb-item-value">{{ dialogData.trade_no || '--' }}</div>
          </div>
        </div>
      </div>
      <div class="fb-box">
        <div class="fb-item clearfix">
          <div class="fb-item-label">评价时间</div>
          <div class="fb-item-value">{{ dialogData.create_time | formatDate }}</div>
        </div>
        <div class="fb-item clearfix">
          <div class="fb-item-label">整单评价</div>
          <div class="fb-item-value d-line-h">
            <!-- <el-rate class="rate reset-rate" v-model="dialogData.order_score" disabled></el-rate> -->
            <template v-for="i in 5">
              <el-image v-if="i <= dialogData.order_score" :key="i" src="https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/icons/rate2_1.png " class="m-r-10" style="widht: 23px; height: 23px; vertical-align: middle;"></el-image>
              <el-image v-if="i > dialogData.order_score" :key="i" src="https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/icons/rate2_0.png " class="m-r-10" style="widht: 23px; height: 23px; vertical-align: middle;"></el-image>
            </template>
            <span class="m-l-10">{{ dialogData.order_score_alias }}</span>
          </div>
        </div>
        <div class="fb-item clearfix">
          <div class="fb-item-label">订单评价</div>
          <div class="fb-item-value evaluate-content">{{ dialogData.evaluation_content || '--' }}</div>
        </div>
        <div class="fb-item clearfix no-item-height">
          <div class="fb-item-label">评价图片</div>
          <div class="fb-item-value">
            <el-image v-for="(img, i) in dialogData.evaluation_img_list" :key="img+i" :src="img" class="evalute-img m-r-20" :preview-src-list="dialogData.evaluation_img_list">
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
        </div>
      </div>
      <div v-if="dialogData.food_evaluation_score && dialogData.food_evaluation_score.length > 0" class="fb-box p-t-5 m-b-6">
        <div class="fb-item clearfix no-item-height">
          <div class="fb-item-label">菜品评价</div>
          <div class="fb-item-value food-box">
            <!-- <div class="food-item">
              <div class="food-name ellipsis">番茄炒鸡蛋</div>
              <div v-for="(rate, k) in evaluation_score" :key="'rate1'+k" class="rate-item">
                <span class="rate-label">{{ rate.field_name }}</span>
                <el-rate class="rate" v-model="rate.score" void-color="#d7d7d7" show-text text-color="#333333" :texts="['极差', '失望', '一般', '满意', '惊喜']" disabled></el-rate>
              </div>
            </div> -->
            <div class="food-item m-b-10" v-for="food in dialogData.food_evaluation_score">
              <div class="food-name ellipsis">{{ food.food_name }}</div>
              <div v-for="(rate, k) in food.evaluation_score" :key="'rate1'+k" class="rate-item">
                <span class="rate-label">{{ rate.field_name }}</span>
                <el-rate class="rate ellipsis" v-model="rate.score" void-color="#d7d7d7" show-text text-color="#333333" :texts="foodScoreList" disabled></el-rate>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- no-line-height -->
      <el-form-item label="商家回复" class="m-t-6">
        <div v-if="canReply" class="m-b-10 quick-reply-box">
          <el-button size="mini" class="ps-origin-btn no-line-height" @click="clickQuickHandle">快捷回复</el-button>
          <div class="quick-reply-text ellipsis m-l-20">{{ dialogData.shortcut_reply_field }}</div>
        </div>
        <el-input v-if="canReply" v-model="dialogForm.replyContent" class="ps-input w-input" type="textarea" :rows="4" :maxlength="150" show-word-limit></el-input>
        <div class="" v-else>
          <div class="m-b-10">{{ dialogData.reply_time | formatDate }}</div>
          <div v-if="dialogData.reply_content" class="is-reply">{{ dialogData.reply_content || '--' }}</div>
        </div>
      </el-form-item>
      <el-form-item label-width="0">
        <div class="ps-drawer-footer">
          <!-- ps-cancel-btn -->
          <el-button @click="closeHandle">{{ canReply ? '取消' : '关闭'}}</el-button>
          <el-button v-if="!dialogData.is_reply && type === 'reply'" class="ps-origin-btn" @click="submitHandle">保存</el-button>
        </div>
      </el-form-item>
    </el-form>
    </CustomDrawer>
  </div>
</template>

<script>
import { deepClone} from "@/utils/index"
import { isArray} from "@/utils/type"
export default {
  name: 'evaluateDialog',
  props: {
    isshow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '评价详情'
    },
    size: {
      type: String,
      default: '800px'
    },
    dialogInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      dialogForm: {
        replyContent: ''
      },
      dialogFormRules: {
        replyContent: [{ required: true, message: '请输入回复内容', trigger: 'blur' }]
      },
      evaluate_images: [
        'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png',
        'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png',
        'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png',
        'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'
      ],
      evaluation_score: [
        { field_name: '11', score: 5 },
        { field_name: '22', score: 4 },
        // { field_name: '33', score: 3 },
        // { field_name: '44', score: 2 }
      ],
      // 整体评价，是写死的文字描述
      scoreList: {
        1: "非常差",
        2: "差",
        3: "一般",
        4: "满意",
        5: "非常满意"
      },
      foodScoreList: [],
      dialogData: {}
    }
  },
  computed: {
    showDrawer: {
      get() {
        if (this.isshow) {
          this.dialogForm.replyContent = ''
          this.initHandle()
        }
        console.log('dialogInfo', this.dialogInfo)
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    },
    canReply() {
      return !this.dialogInfo.is_reply && this.type === 'reply'
    }
  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {
    // 初始化下
    initHandle() {
      let info = deepClone(this.dialogInfo)
      info.order_score_alias = this.scoreList[info.order_score]
      let evaluationSetting = info.evaluation_setting
      if (evaluationSetting && evaluationSetting.start_score_field) {
        this.foodScoreList = Object.values(evaluationSetting.start_score_field)
        // 菜品评价，菜品评价可能没开启的，要判断先
        // if (info.food_evaluation_score && info.food_evaluation_score.length > 0 && isArray(info.food_evaluation_score)) {
        //   console.log(info.food_evaluation_score)
        //   info.food_evaluation_score.forEach(food => {
        //     food.evaluation_score.forEach(rate => {
        //       rate.score_alias = evaluationSetting.start_score_field[rate.score]
        //     })
        //   })
        // }
      }
      if (info.evaluation_setting.shortcut_reply_field) {
        info.shortcut_reply_field = info.evaluation_setting.shortcut_reply_field[0]
      }
      console.log('info', info)
      this.dialogData = info
    },
    submitHandle() {
      // this.$refs.dialogForm.validate((valid) => {
      //   if (valid) {
      //     this.replyHandle()
      //   } else {
      //     console.warn('error submit')
      //   }
      // })
      if (this.dialogForm.replyContent) {
        this.replyHandle()
      } else {
        this.$message.error('请输入回复内容')
      }
    },
    // 回复
    async replyHandle() {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOperationManagementOrderEvaluationReplyOrderPost({
          id: this.dialogData.id,
          reply_content: this.dialogForm.replyContent
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDrawer = false
        this.$message.success(res.msg)
        this.$emit('confirm')
        this.dialogForm.replyContent = ''
      } else {
        this.$message.error(res.msg)
      }
    },
    closeHandle() {
      this.showDrawer = false
    },
    clickQuickHandle() {
      if (this.dialogData.shortcut_reply_field) {
        this.dialogForm.replyContent = this.dialogData.shortcut_reply_field
      } else {
        this.$message.error('请前往基础设置配置常用语')
      }
    }
  }
};
</script>

<style scoped lang="scss">
.evaluate-dialog{
  .w-input {
    width: 100%;
  }
  .no-line-height {
    line-height: 1.2;
  }
  .no-item-height {
    &.fb-item {
      margin-top: 12px;
      margin-bottom: 4px;
      line-height: 1.2;
    }
  }
  .reset-rate {
    display: inline-block;
    height: 40px;
    line-height: 40px;
    ::v-deep.el-rate__item{
      font-size: 26px;
    }
    ::v-deep .el-rate__icon{
      font-size: 26px;
    }
  }
  .dialog-evaluate-form {
    margin-right: 30px;
  }
  .fb-item {
    font-size: 14px;
    line-height: 40px;
    .fb-item-label {
      float: left;
      width: 100px;
      text-align: right;
      vertical-align: middle;
      float: left;
      color: #606266;
      padding: 0 12px 0 0;
      // font-weight: 600;
    }
    .fb-item-value {
      margin-left: 100px;
      vertical-align: middle;
      word-break: break-all;
      flex-wrap: wrap;
    }
  }
  .evaluate-content {
    padding-top: 12px;
    line-height: 1.2;
  }
  .line-bottom {
    position: relative;
    &::after{
      content: '';
      position: absolute;
      left: 30px;
      right: 0;
      bottom: 0;
      height: 1px;
      background-color: #797979;
    }
  }
  ::v-deep.el-form-item__label{
    font-weight: 500;
  }
  ::v-deep.no-line-height {
    .el-form-item__label {
      line-height: 1.2;
      vertical-align: top;
    }
    .el-form-item__content {
      line-height: 1.2;
      vertical-align: top;
    }
  }
  .evalute-img{
    width: 60px;
    height: 60px;
    border-radius: 10px;
    .el-icon-picture-outline{
      font-size: 60px;
      opacity: .5;
    }
  }
  .quick-reply-box {
    display: flex;
    align-items: center;
    .quick-reply-text {
      flex: 1;
    }
  }
  .is-reply {
    min-height: 40px;
    line-height: 1.5;
    padding: 12px 10px;
    border-radius: 6px;
    background-color: #e7e9ef;
    word-break: break-all;
  }
  .food-box {
    display: flex;
    justify-content: space-between;
  }
  .food-item {
    width: 48%;
    padding: 10px 16px;
    line-height: 1;
    border-radius: 10px;
    background-color: #e7e9ef;
    .food-name {
      padding: 6px 0 8px;
    }
  }
  .rate-item{
    display: flex;
    align-items: center;
    padding: 3px 0;
    // line-height: 30px;
    .rate-label{
      margin-right: 6px;
      vertical-align: middle;
    }
    .rate{
      // display: inline-block;
      flex: 1;
      overflow: hidden;
      ::v-deep .el-rate__icon{
        font-size: 23px;
      }
      ::v-deep.el-rate__text {
        margin-left: 2px;
        font-size: 13px;
      }
    }
  }
}
</style>
