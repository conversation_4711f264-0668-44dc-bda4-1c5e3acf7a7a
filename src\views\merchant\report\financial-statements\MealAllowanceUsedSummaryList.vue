<template>
  <div>
    <div class="meal-allowance-summary booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        @reset="resetHandler"
        :autoSearch="false"
      ></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-button size="mini"  @click="gotoPrint">打印</el-button>
            <el-button
              size="mini"
              @click="gotoExport"
               type="primary"
              v-permission="['background_report_center.data_report.meal_supplement_summary_list_export']"
            >
              导出
            </el-button>
            <el-button size="mini" type="primary" @click="openPrintSetting">报表设置</el-button>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%"  stripe header-row-class-name="ps-table-header-row" empty-text="暂无数据，请查询"
         :key="tableKey">
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #rule_guid="{ row }">
                <div v-if="row.order_date === '合计'">{{ row.rule_guid }}</div>
                <div v-else class="rule-guid pointer" @click="handlerGoRuleDetail(row)">{{ row.rule_guid }}</div>
            </template>
            <template #operation="{ row }">
              <div @click="handlerDetail(row)" class="ps-origin pointer">查看</div>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
        <div class="ps-origin font-size-14 m-t-10">注：为保持数据的准确一致性，请于封账前及时上传离线订单</div>
        </div>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper"
            :total="total" background class="ps-text" popper-class="ps-popper-select"></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
     <!-- 详情弹窗 -->
     <consumption-rules-detail-dialog ref="detailDialogRef" :isshow="dialogDetailVisible" :title="dialogDetailTitle"
      :dialogType="dialogDetailType" @confirm="confirmDetailDialog" @close="closeDetailDialog" />
  </div>
</template>

<script>
import { MEAL_ALLOWANCE_USED_SUMMARY_LIST_SEARCH_SETTING, MEAL_ALLOWANCE_USED_SUMMARY_LIST_TABLE_SETTING } from './constantsConfig'
import { debounce, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import ConsumptionRulesDetailDialog from '@/views/merchant/consumption-rules/components/ConsumptionRulesDetailDialog'

export default {
  name: 'MealAllowanceUsedSummaryList',
  mixins: [exportExcel, report],
  components: {
    ConsumptionRulesDetailDialog
  },
  data() {
    return {
      isLoading: false,
      tableSetting: deepClone(MEAL_ALLOWANCE_USED_SUMMARY_LIST_TABLE_SETTING),
      tableData: [],
      currentPage: 1,
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: deepClone(MEAL_ALLOWANCE_USED_SUMMARY_LIST_SEARCH_SETTING),
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'MealSupplementUseSummary',
      isFirstSearch: true,
      dialogDetailVisible: false, // 详情弹窗
      dialogDetailTitle: '详情', // 详情弹窗标题
      dialogDetailType: 'detail',
      tableKey: 0
    }
  },
  created() {},
  mounted() {
    // 初始化表头
    this.initPrintSetting()
    // 获取消费规则列表
    this.getConsumptionList()
    // 获取数据
    this.initLoad(true)
  },
  watch: {
    'currentTableSetting'(val) {
      console.log(val, 'val')
      this.tableKey = this.tableKey + 1
    }
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        this.getDataList()
      }
    },
    tableRowClassNameDeviceCostSummary({ row, rowIndex }) {
      if (row.primary === '小计') {
        return 'hang-row-class'
      }
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
        this.isFirstSearch = false
      }
    }, 300),
    // 重置
    resetHandler() {
      this.currentPage = 1
      this.getDataList()
    },
    // 获取设备名称
    async getDeviceSelectList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportDeviceSelectListPost({
        org_ids: this.searchFormSetting.org_ids.value
      })
      if (res.code === 0) {
        this.searchFormSetting.device_ids.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设备列表
    async requestDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost({})
      if (res.code === 0) {
        this.searchFormSetting.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表数据
    async getDataList() {
      this.isLoading = true
      // params
      const [err, res] = await this.$to(this.$apis.apiBackgroundReportCenterDataReportMealSupplementSummaryList({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        return this.$message.error(err.message || '请求失败')
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        const results = data.result || []
        const listData = results.list_data || []
        this.tableData = deepClone(listData)
        console.log("this.tableData", this.tableData)
        this.total = res.data.count || this.tableData.length

        // 在末尾增加一行合计，第一个格显示合计，其他列显示合计值
        if (this.tableData.length > 0 && this.currentTableSetting.length > 0) {
          const summaryRow = {}
          // 根据表头设置获取第一列的key
          const firstColumn = this.currentTableSetting[0]
          summaryRow[firstColumn.key] = '合计'

          // 递归处理所有列（包括children）
          const processColumns = (columns) => {
            let keyList = ['rule_name', 'rule_guid', "name", "person_no", "phone", "user_groups_name", "department_group_name"]
            columns.forEach(column => {
              if (column.key && column.key !== firstColumn.key && !keyList.includes(column.key)) {
                const sum = this.tableData.reduce((sum, row) => {
                  const value = row[column.key]
                  return sum + (typeof value === 'number' ? value : 0)
                }, 0)
                summaryRow[column.key] = sum
              } else if (column.key === firstColumn.key) {
                summaryRow[column.key] = '合计'
              } else {
                summaryRow[column.key] = '--'
              }
              // 如果有子列，递归处理
              if (column.children && column.children.length > 0) {
                processColumns(column.children)
              }
            })
          }

          processColumns(this.currentTableSetting)
          this.tableData.push(summaryRow)
        }
      } else {
        this.$message.error(res.msg)
      }
    },

    // 翻页
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList()
    },
    // 导出
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const option = {
        type: this.printType,
        url: 'apiBackgroundReportCenterDataReportMealSupplementSummaryListExport',
        params: params
      }
      this.exportHandle(option)
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            if (Array.isArray(data[key].value)) {
              if (data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    gotoPrint() {
      // const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '餐补使用汇总表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportMealSupplementSummaryList', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          push_summary: true, // 合计添加到到table数据最后
          isMerge: 0,
          params: JSON.stringify({
            ...this.formatQueryParams(this.searchFormSetting),
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 获取消费规则
    async getConsumptionList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundMarketingConsumeListPost({
        page: 1,
        page_size: 99999
      }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.searchFormSetting.consume_rule_ids.dataList = deepClone(results)
      }
    },
    // 确认详情
    confirmDetailDialog() {
      this.dialogDetailVisible = false
    },
    // 关闭详情
    closeDetailDialog() {
      this.dialogDetailVisible = false
    },
    // 查看详情
    handlerGoRuleDetail(row) {
      if (this.$refs.detailDialogRef) {
        row.rule_detail.name = row.rule_name
        this.$refs.detailDialogRef.setFormData(row.rule_detail)
      }
      this.dialogDetailVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
.meal-allowance-summary {
  ::v-deep .hang-row-class {
    background: #eef1f6 !important;
  }
  ::v-deep th.el-table__cell {
    border: 1px solid  #eef1f6 !important;
  }
  .search-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 10px;
  }
  .rule-guid {
    text-decoration: underline;
  }
}
</style>
