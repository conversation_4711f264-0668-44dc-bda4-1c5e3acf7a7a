<template>
  <div>
    <div class="consumptionSummaryReport container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper"  v-loading="isLoading">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon
              color="plain"
              type="export"
              @click="gotoExport"
              v-permission="['background_report_center.finance_report.consumption_summary_list_export']"
            >
              导出Excel
            </button-icon>
            <!-- <button-icon color="plain" @click="gotoPrint">打印</button-icon> -->
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row"
          />
        </div>
        <table-statistics :statistics="collect" />

        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="total"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { beforeRecentSevenDay, berforePickerOptions } from '@/utils/formatPickerOptions'
import { debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
export default {
  name: 'ConsumptionSummaryReport',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '日期', key: 'date' },
        { label: '储值钱包充值款', key: 'wallet_charge', type: 'money' },
        { label: '微信支付款', key: 'wechat_consume', type: 'money' },
        { label: '储值钱包余额', key: 'wallet_balance', type: 'money' }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: {
        select_date: {
          type: 'daterange',
          label: '搜索时间',
          value: beforeRecentSevenDay,
          pickerOptions: berforePickerOptions,
          clearable: false
        }
      },
      collect: [
        // 统计
        { key: 'total_date_start_fee', value: 0, label: '期初储值钱包余额:￥', type: 'money' },
        { key: 'total_date_end_fee', value: 0, label: '期末储值钱包余额:￥', type: 'money' }
      ],
      printType: 'ConsumptionSummaryReport',
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
  },
  async mounted() {
    this.getOrganizationList()
  },
  methods: {
    async initLoad(isFirst) {
      if (!isFirst) {
        this.getPersonPaymentList()
      }
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getPersonPaymentList()
        this.isFirstSearch = false
      }
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取组织信息
    async getOrganizationList() {
      this.isLoading = true
      const res =
        await this.$apis.apiBackgroundReportCenterDataReportGetAllSubordinateOrganizationPost({
          status: 'enable',
          page: 1,
          page_size: 9999
        })
      this.isLoading = false
      if (res.code === 0) {
        let arr = res.data.map(v => {
          return {
            type: 'money',
            label: v.name,
            key: v.id
          }
        })
        this.tableSetting.splice(3, 0, ...arr)
        this.initPrintSetting()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表数据
    async getPersonPaymentList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportConsumptionSummaryListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.results
        if (res.data.result.length) {
          // 设置合计的值
          this.tableData = res.data.result
          this.setSummaryData(res)
          this.setCollectData(res)
        }
        // this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getPersonPaymentList()
    },
    gotoExport() {
      const option = {
        type: 'ConsumptionSummaryReport',
        url: 'apiBackgroundReportCenterDataReportConsumptionSummaryListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
    // gotoPrint() {
    //   const params = this.formatQueryParams(this.searchFormSetting)
    //   const { href } = this.$router.resolve({
    //     name: 'Print',
    //     query: {
    //       print_date_state: true,
    //       print_type: this.printType,
    //       print_title: '个人消费汇总',
    //       result_key: 'results', // 返回的数据处理的data keys
    //       api: 'apiBackgroundReportCenterDataReportPersonPaymentCollectListPost', // 请求的api
    //       show_summary: false, // 合计
    //       show_print_header_and_footer: true, // 打印页头页尾
    //       table_setting: JSON.stringify(this.tableSetting),
    //       current_table_setting: JSON.stringify(this.currentTableSetting),
    //       push_summary: false, // 合计添加到到table数据最后
    //       params: JSON.stringify({
    //         ...params,
    //         page: 1,
    //         page_size: this.total ? this.total : 10
    //       })
    //     }
    //   })
    //   window.open(href, '_blank')
    // }
  }
}
</script>
<style lang="scss" scoped>
.el-table {
  text-align: center;
  font-size: 12px;
}
</style>
