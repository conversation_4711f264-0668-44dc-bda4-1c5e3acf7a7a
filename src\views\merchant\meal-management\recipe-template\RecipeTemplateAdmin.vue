<template>
  <div class="AiRetentionInstrument-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="addTemplateHandle('add')" v-permission="['background_food.menu_template.add']">新建模板</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="addTemplateHandle('modify', row)" v-permission="['background_food.menu_template.modify']">编辑</el-button>
              <el-button type="text" size="small" class="ps-warn" @click="deleteHandle('single', row)" v-permission="['background_food.menu_template.delete']">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <recipe-template-dialog ref="dialogRef" v-model="showDialog" :info-data="dialogData" :type="dialogType" :confirmhandle="confirmDialogHandle"></recipe-template-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入

import RecipeTemplateDialog from './RecipeTemplateDialog'

export default {
  name: 'RecipeTemplateAdmin',
  mixins: [exportExcel],
  components: { RecipeTemplateDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '创建时间', key: 'create_time' },
        { label: '模板名称', key: 'name' },
        { label: '备注', key: 'remark', showTooltip: true },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      searchFormSetting: {
        name: {
          type: 'input',
          value: '',
          label: '模板名称',
          placeholder: ''
        }
      },
      showDialog: false,
      dialogType: 'add',
      dialogData: {}
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getRecipeTemplateList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getRecipeTemplateList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(this.$apis.apiBackgroundFoodMenuTemplateListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getRecipeTemplateList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    addTemplateHandle(type, data) {
      this.dialogType = type
      if (type === 'modify') {
        this.dialogData = data
      } else {
        this.dialogData = {}
      }
      this.showDialog = true
    },
    // 添加和编辑成功后刷新数据
    confirmDialogHandle() {
      this.getRecipeTemplateList()
    },
    // 查看详情
    gotoDetail(data) {
      this.$router.push({
        name: 'MerchantAiRetentionInstrumentDetail',
        query: {
          id: data.id,
          data: this.$encodeQuery(data)
        }
      })
    },
    // 删除
    async deleteHandle(type, data) {
      let params = {}
      if (type === 'single') {
        params.ids = [data.id]
      } else {
      } // 删除多个，原型没有
      if (this.isLoading) return this.$message.error('请勿重复提交！')
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          if (action === 'confirm') {
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundFoodMenuTemplateDeletePost(params))
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getRecipeTemplateList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 导出弹窗
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const option = {
        type: 'AIDataList',
        url: 'apiBackgroundFoodFoodReservedListExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
.AiRetentionInstrument-list {}
</style>
