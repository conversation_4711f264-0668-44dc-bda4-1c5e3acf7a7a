<template>
  <div class="tabs-header-warpper" >
    <div class="tab-header">
      <div class="header-left">
        <!-- <div v-for="menu in navMenuList" :key="menu.key" :class="['client', 'item', activeHeaderMenu===menu.key?'active':'']" @click="clickNavHeaderTab(menu.key)">{{ menu.name }}</div> -->
        <!-- <div :class="['system', 'item', activeHeaderTab==='system'?'active':'']" @click="clickNavHeaderTab('system')">系统配置</div> -->
        <el-tabs v-model="activeHeaderTab" @tab-click="handleClick">
          <el-tab-pane v-for="menu in navMenuList" :key="menu.key"  :label="menu.name" :name="menu.key"></el-tab-pane>
        </el-tabs>
      </div>
      <!-- 评价 -->
      <div v-if="userInfo.role_id !== 1" style="margin-right:15px;display: inline-block;">
        <div style="display: flex;align-items: center;" @click="onShowPrasieDialog">
          <img :src="getImgPraiseStatus" style="width: 16px;height: 16px;margin-right: 5px;"/>
          <el-badge :is-dot="getIsDot" class="item">
            <span class="font_14" style="line-height: 24px;color: #fff;">评价</span>
          </el-badge>
        </div>
      </div>
      <div class="header-right">
        <el-dropdown style="margin-right:15px;" @command="clickOrganizationHandle" v-if="!userInfo.is_channel_account">
          <span>
            <i class="el-icon-office-building" style="margin-right: 5px"></i>
            <!-- <img src="@/assets/img/company.png" alt="user"> -->
            <span>{{ organizationName }}</span>
            <!-- <img src="@/assets/img/dropdown.png" alt="user"> -->
            <i class="el-icon-caret-bottom" style="margin-left: 5px"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-scrollbar wrap-class="org-scrollbar-wrapper">
              <el-dropdown-item v-for="org in userInfo.organizationList" :key="org.id" :command="org.id" :class="{'is-select': organization === org.id}" >{{ org.name }}</el-dropdown-item>
            </el-scrollbar>
            <!-- <el-dropdown-item>{{this.userInfo.company_name}}</el-dropdown-item> -->
            <!-- <el-dropdown-item>新增新增</el-dropdown-item>
            <el-dropdown-item>删除删除</el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
        <!-- 渠道用户只显示名称 -->
        <div v-if="userInfo.is_channel_account" class = 'ps-inline m-r-10'>
          <i class="el-icon-office-building" style="margin-right: 5px"></i>
          <span class="font_14">{{ userInfo.member_name }}</span>
        </div>

        <el-dropdown style="margin-right:15px;" @command="handleCommand">
          <span>
            <i class="el-icon-s-custom" style="margin-right: 5px"></i>
            <!-- <img src="@/assets/img/user.png" alt="user"> -->
            <span></span>
            <span>{{this.userInfo.role_name}}</span>
            <!-- <img src="@/assets/img/dropdown.png" alt="user"> -->
            <i class="el-icon-caret-bottom" style="margin-left: 5px"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>{{this.userInfo.username}}</el-dropdown-item>
            <el-dropdown-item command='accountSetting'>账号设置</el-dropdown-item>
            <el-dropdown-item v-if="$store.getters.userInfo.role_name !== '超级管理员' && $store.getters.userInfo.level_tag === 0" command='upgradeService'>升级服务</el-dropdown-item>
            <!-- <el-dropdown-item>删除删除</el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
        <!-- <lang-select style="margin-right:8px;" /> -->
        <div
          v-if="userInfo.role_id != 1 && allPermissions.includes('background_messages.notice')"
          class="nav-notice"
          @click="gotoNoticeList"
        >
          <span class="nav-h-icon">
            <img src="@/assets/img/notice.png" alt="logout" />
          </span>
          <div class="notice-num" v-if="noticeNum">{{ noticeNum }}</div>
        </div>
        <span class="logout nav-h-icon" @click="clickLogoutBtn">
          <img src="@/assets/img/logout.png" alt="logout">
        </span>
      </div>
    </div>
    <!-- 弹窗 -->
    <dialog-message
      message="确定退出登录吗？"
      :show.sync="showDialog"
      :loading.sync="loading"
      @close="closeDialogHandle"
      @confirm="logoutHandle"
    />
    <!-- 评价弹窗 -->
    <dialog-message
      width="500px"
      :title="getSRTitle"
      :show.sync="praiseDialogShow"
      customClass="expire-dialog"
      :showFooter="false"
      @close="handleClose"
    >
      <template class="expire-dialog-content ">
        <el-form ref="dialogFormRef" :model="formPraise" :rules="dialogFormRules" label-width="auto" >
          <el-form-item label="" label-width="10px" class="radio-content">
            <el-radio-group size="medium" v-model="formPraise.is_satisfied">
              <el-radio :label="true" class="radio-item">
                <span>满意</span>
                <img :src="ImgPraise" class="radio-img" />
              </el-radio>
              <el-radio :label="false" class="radio-item un-radio">
                <span>不满意</span>
                <img :src="ImgNotPraise" class="radio-img" />
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="!formPraise.is_satisfied">
            <el-form-item label="反馈功能" prop="module_key" required>
              <el-select v-model="formPraise.module_key" placeholder="请选择" @change="changeModuleKey" style="width: 100%;">
                <el-option
                  v-for="item in featOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="selectedModulekey && selectedModulekey.tips" label=" ">
              {{ selectedModulekey.tips }}
            </el-form-item>
            <el-form-item v-if="selectedModulekey && selectedModulekey.tags && selectedModulekey.tags.length" label="选择类型" prop="tags" required>
              <el-checkbox-group v-model="formPraise.tags" size="small">
                <el-checkbox style="margin-left: 0;margin-right: 10px;"
                  v-for="(item, index) in selectedModulekey.tags" :key="index"
                  :label="item" border>
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="不满意原因" prop="reason">
              <el-input
                type="textarea"
                :rows="4"
                maxLength="100"
                placeholder="请描述您不满意的点,我们将根据您的信息对功能进行新的思考，不超过100字"
                v-model="formPraise.reason">
              </el-input>
            </el-form-item>
            <el-form-item v-if="satisfictionRecord.reply">
              <template slot="label">
                <div style="height: 24px;line-height: 24px;">回复信息:</div>
              </template>
              <div style="font-size: 14px;line-height: 24px;"> {{ satisfictionRecord.reply }}</div>
            </el-form-item>
          </template>
        </el-form>
      </template>
      <template #tool>
        <div class="dialog-footer" style="margin-top: 20px; text-align: right;" >
          <el-button class="ps-cancel-btn renew-btn" @click="handleClose">取消</el-button>
          <el-button class="ps-btn " @click="goUpgrade">确定</el-button>
        </div>
      </template>
    </dialog-message>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from "vuex"
// import LangSelect from '@/components/LangSelect'
import { setSessionStorage, to, removeSessionStorage } from '@/utils'
import ImgPraise from '@/assets/img/praise.png'
import ImgNotPraise from '@/assets/img/not_praise.png'
export default {
  name: 'tabHeaser',
  components: {
    // LangSelect
  },
  props: {
    getPopMsg: {
      type: Function
    },
    closeAllNotificationFun: {
      type: Function
    }
  },
  inject: ['reload'],
  data() {
    return {
      // 评价
      dialogFormRules: {
        module_key: [{ required: true, message: '请选择反馈功能具体模板', trigger: ['blur', 'change'] }],
        tags: [{ required: true, message: '请选择您不满意的类型', trigger: ['blur', 'change'] }]
        // reason: [{ required: true, message: '请输入不满意原因', trigger: ['blur','change'] }]
      },
      ImgPraise,
      ImgNotPraise,
      satisfictionRecord: {},
      featOptions: [],
      formPraise: {
        is_satisfied: true,
        module_key: '',
        reason: '',
        tags: []
      },
      selectedModulekey: {},
      praiseDialogShow: false,
      activeHeaderTab: '',
      loading: false,
      showDialog: false,
      noticeNum: 0,
      organizationName: '' // 当前登录的组织名
    }
  },
  computed: {
    getIsDot() {
      return this.satisfictionRecord?.is_read === false && this.satisfictionRecord?.is_reply === true ?  true : false
    },
    getSRTitle() {
      if (this.satisfictionRecord.quarter) {
        const str = this.satisfictionRecord.quarter.split('-') || '-'
        return `${str[0]}年${str[1]}季度系统使用调查`;
      }
      return ''
    },
    getImgPraiseStatus() {
      return this.satisfictionRecord?.is_satisfied ? ImgPraise : ImgNotPraise
    },
    ...mapGetters([
      'userInfo',
      'navMenuList',
      'activeNavMenu',
      'addRoutes',
      'visitedViews',
      'organization',
      'allPermissions'
    ])
    // activeHeaderTab() {
    //   return this.activeHeaderMenu
    // }
  },
  watch: {
    activeNavMenu(val) {
      if (this.activeHeaderTab !== val) {
        this.activeHeaderTab = val
      }
    },
    organization(val) {
      this.setOrganizationName()
    }
  },
  created() {
    this.setOrganizationName()
  },
  async mounted () {
    this.activeHeaderTab = this.activeNavMenu
    if (this.userInfo.role_id !== 1) {
      this.getMsgNum()
      this.$root.eventHub.$on("updateNoticeCount", (noticeNum) => {
        this.noticeNum = noticeNum
      });
      this.getSatisfactionRecord()
    }
  },
  methods: {
    ...mapMutations('user', ['SET_ISFIRSTGETPOPMSG']),
    changeModuleKey(val) {
      this.formPraise.tags = []
      this.selectedModulekey = this.featOptions.find(item => item.value === val)
    },
    // 更新已读状态
    async updateReadState() {
      const [err, res] = await to(this.$apis.apiBackgroundFeedbackSatisfactionRecordUpdateReadStatePost({
        is_read: true
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.satisfictionRecord.is_read = true
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取商户模块列表
    async getModuleList() {
      const [err,res] = await to(this.$apis.apiBackgroundFeedbackSatisfactionRecordModuleListPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const datas = res.data || []
        this.featOptions = [
          ...datas.map(item=> ({
            label: item.name,
            value: item.key,
            tags: item.tags,
            tips: item.tips
          }))
        ]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取本季度记录
    async getSatisfactionRecord() {
      const [err,res] = await to(this.$apis.apiBackgroundFeedbackSatisfactionRecordGetPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.satisfictionRecord = res.data || {}
        // 不满意要回填原因
        if (!res.data.is_satisfied) {
          this.formPraise.is_satisfied = res.data.is_satisfied
          this.formPraise.module_key = res.data.module_key
          this.formPraise.reason = res.data.reason
          // this.formPraise.tips = res.data.tips
          this.formPraise.tags = res.data.tags
          this.selectedModulekey = {
            tags: res.data.tags
          }
        } else {
          // this.formPraise.module_key = ''
          // this.formPraise.reason = ''
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    async onConfirm() {
      const params = {
        ...this.formPraise
      }
      if (this.formPraise.is_satisfied) {
        delete params.module_key
        delete params.reason
      }
      if (!params.module_key) {
        delete params.module_key
      }
      // console.log(params)
      // return
      const [err,res] = await to(this.$apis.apiBackgroundFeedbackSatisfactionRecordAddPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.handleClose()
        this.getSatisfactionRecord()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 保存
    goUpgrade() {
      if(this.formPraise.is_satisfied) {
        this.onConfirm()
      } else {
        this.formPraise.reason = this.formPraise.reason.trim() // 清除首尾空格
        this.$refs.dialogFormRef.validate(valid => {
          if (valid) {
            this.onConfirm()
          }
        })
      }
    },
    handleClose() {
      this.praiseDialogShow = false
    },
    onShowPrasieDialog() {
      this.praiseDialogShow = true

      this.getSatisfactionRecord()
      this.getModuleList()
      this.updateReadState()
    },
    handleClick() {
      this.clickNavHeaderTab(this.activeHeaderTab)
    },
    async clickNavHeaderTab(type, to = true) {
      await this.$store.dispatch('navTabs/setActiveNavMenu', type)
      // await this.$sleep(5000)
      // this.$store.dispatch('navTabs/upadtePermissions')
      // 切换顶部菜单时默认跳回第一条
      // let currentPath = ''
      console.log('type', type, this.navMenuList)
      for (let index = 0; index < this.navMenuList.length; index++) {
        let item = this.navMenuList[index];
        if (item.key === type) {
          if (item.activePath) {
            if (this.$route.path !== item.activePath) { // 新版vue-router如何跳转到同一个地址会报错的，做个判断吧
              // this.$router.push({
              //   path: item.activePath
              // })
              this.queryStoreVisitedViews(item, 'activePath')
            }
          } else if (item.indexPath) {
            if (this.$route.path !== item.indexPath) {
              // this.$router.push({
              //   path: item.indexPath
              // })
              this.queryStoreVisitedViews(item, 'indexPath')
            }
          } else {
            if (this.$route.path !== '/error/404') {
              this.$router.push({
                path: '/error/404'
              })
            }
          }
        }
      }
      this.$store.dispatch('navTabs/upadtePermissions')
    },
    // 查询下vuex里面是否有这个路由的数据，有就直接用
    queryStoreVisitedViews(item, pathKey) {
      let hasTab = false
      let currentTab = null
      for (let index = 0; index < this.visitedViews.length; index++) {
        currentTab = this.visitedViews[index];
        if (currentTab.path === item[pathKey]) {
          hasTab = true
          break
        }
      }
      if (hasTab) {
        this.$router.push(currentTab)
      } else {
        this.$router.push({
          path: item[pathKey]
        })
      }
    },
    clickLogoutBtn() {
      this.showDialog = true
    },
    async logoutHandle(e) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundLogoutPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        await this.$store.dispatch('user/logout')
        this.$message.success('退出登录成功！')
        this.loading = false
        this.showDialog = false
        this.closeAllNotificationFun()
        this.$router.push('/login')
        this.SET_ISFIRSTGETPOPMSG(true) // 退出登录时重置公告弹窗状态
        removeSessionStorage('serviceExpireShown') // 清除服务長到期提示
        // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
      } else {
        this.$message.error(res.msg)
      }
    },
    closeDialogHandle(e) {},
    gotoNoticeList() {
      this.$router.push({
        name: 'MerchantNoticeList',
        query: {
          // un_read: this.noticeNum
        }
      })
    },
    async getMsgNum() {
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesGetMsgNumPost({}))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data && res.data.unread_count) {
          this.noticeNum = res.data.unread_count > 99 ? '99+' : res.data.unread_count
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    handleCommand(command) {
      if (command === 'accountSetting') {
        this.$router.push({
          name: 'AccountSetting'
        })
      } else if (command === 'upgradeService') {
        this.$router.push({
          name: 'UpgradeService'
        })
      }
    },
    setOrganizationName() {
      if (this.userInfo && !this.userInfo.organizationList) return
      this.userInfo.organizationList.forEach(item => {
        if (this.organization === item.id) {
          this.organizationName = item.name
        }
      });
    },
    // 点击选择组织
    async clickOrganizationHandle(e) {
      if (this.organization === e) return
      this.$confirm(`确定切换组织？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // instance.cancelButtonLoading = true
            if (this.isLoading) return this.$message.error('请勿重复提交！')
            this.isLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundBindOrgPost({
              org_no: e
            }))
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$store.dispatch('navTabs/setNavMenuList', [])
              sessionStorage.setItem('isChoiceOrg', 1)
              let obj = {
                ...res.data,
                orgs_level: this.userInfo.orgs_level
              }
              this.$store.dispatch('user/setOrganization', e)
              this.$store.dispatch('user/setUserInfo', obj)

              // 更新路由
              // 更新路由
              const permissions = await this.$store.dispatch('user/getPermissionList', { key: '' })
              await this.$store.dispatch('permission/changeRoutes', permissions)
              // 跳转到第一个页面
              let item = this.navMenuList[0]
              if (item.activePath) {
                if (this.$route.path !== item.activePath) { // 新版vue-router如何跳转到同一个地址会报错的，做个判断吧
                  this.queryStoreVisitedViews(item, 'activePath')
                  this.$store.dispatch('navTabs/delAllVisitedViews') // 关闭所有已打开的页面
                } else { // 同一个地址，只关闭其他页面
                  this.$store.dispatch('navTabs/delOthersVisitedViews') // 关闭其他的页面
                }
              } else if (item.indexPath) {
                if (this.$route.path !== item.indexPath) {
                  this.queryStoreVisitedViews(item, 'indexPath')
                  this.$store.dispatch('navTabs/delAllVisitedViews') // 关闭所有已打开的页面
                } else { // 同一个地址，只关闭其他页面
                  this.$store.dispatch('navTabs/delOthersVisitedViews') // 关闭其他的页面
                }
              } else {
                if (this.$route.path !== '/error/404') {
                  this.$router.push({
                    path: '/error/404'
                  })
                }
              }

              // 刷新当前页面的数据
              this.reload()
              // 切换组织再次调用获取公告接口
              this.SET_ISFIRSTGETPOPMSG(true)
              this.getPopMsg()
              this.$message.success(res.msg)
              done()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
            this.getAbcAgreement(e)
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 获取协议
    async getAbcAgreement(id) {
      console.log("getAbcAgreement", id);
      var params = {
        organization_id: id
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationAccountGetUserAbcAgreementPost(params))
      console.log("getAbcAgreement", err, res);
      if (err) {
        return
      }
      if (res && res.code === 0) {
        console.log("getAbcAgreement", res);
        var data = res.data || {}
        if (data && Reflect.has(data, "is_show")) {
          var isShow = data.is_show
          if (isShow) {
            // 显示弹窗
            setSessionStorage("showAgreement", isShow)
            this.$store.dispatch('user/setAgreementInfo', data)
          }
        }
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/mixin.scss';
.tabs-header-warpper {
  .tab-header {
    .nav-notice {
      display: inline;
      position: relative;
      margin-right: 10px;
      cursor: pointer;

      .notice-num {
        height: 15px;
        background-color: red;
        color: #fff;
        border-radius: 10px;
        position: absolute;
        top: -7px;
        left: 20px;
        line-height: 16px;
        font-size: 12px;
        padding: 0 4px;
      }
    }
  }
}
.el-dropdown-menu{
  .is-select{
    color: #ff9b45;
    @include font_color($font-color-theme)
  }
}
.font_14 {
  font-size: 14px;
}
.org-scrollbar-wrapper{
  max-height: 300px;
}

.radio-content {
  .el-radio-group {
    display: flex;
    .radio-item {
      display: flex;
      align-items: center;
      margin-right: 30px;
      .el-radio__label{
        display: flex;
        align-items: center;
        font-size: 16px;
      }
      .radio-img {
        width: 28px;
        height: 28px;
        margin-left: 5px;
      }
    }
    .un-radio{
      .el-radio__input.is-checked .el-radio__inner {
        background: #ff2929 !important;
      }
      .el-radio__input.is-checked+.el-radio__label{
        color: #ff2929 !important;
      }
    }
  }
}
</style>
