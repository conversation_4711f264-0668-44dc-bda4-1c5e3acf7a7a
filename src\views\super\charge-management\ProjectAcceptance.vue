
<template>
  <div id="chargeOrder">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="goExport">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
         <!-- :selectable="selectableHandle" -->
        <el-table ref="tableView" :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #remark="{ row }">
              <el-tooltip placement="top">
                <div slot="content">
                  <div class="w-300">
                    <span>{{ row.remark }}</span>
                  </div>
                </div>
                <div class="w-200 ellipsis">
                  <span>{{ row.remark }}</span>
                </div>
              </el-tooltip>
            </template>
          </table-column>
        </el-table>
      </div>
    </div>
    <!-- 分页 start -->
    <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[10, 20, 50, 100, 500]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount"
        background
        class="ps-text"
        popper-class="ps-popper-select"
      ></el-pagination>
    </div>
    <!-- 分页 end -->
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import * as dayjs from 'dayjs'
import { ProjectAcceptanceTableSetting } from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'ProjectAcceptance',
  mixins: [exportExcel],
  data() {
    return {
      total_fee: 0,
      isLoading: false,
      tableData: [],
      searchForm: {
        select_date: {
          type: 'daterange',
          label: '创建时间',
          clearable: false,
          value: [dayjs().subtract(3, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        },
        company_ids: {
          type: 'CompanySelect',
          value: [],
          label: '所属项目点',
          dataList: [],
          multiple: true,
          checkStrictly: true,
          clearable: true,
          labelWidth: '100px',
          companyOpts:
          {
            label: 'name',
            value: 'company'
          },
          role: 'super'
        },
        will: {
          type: 'select',
          label: '验收意愿',
          value: undefined,
          placeholder: '请选择',
          dataList: [
            {
              label: '全部',
              value: undefined
            },
            {
              label: '同意',
              value: 'yes'
            },
            {
              label: '不同意',
              value: 'no'
            }
          ]
        }
      },
      currentTableSetting: ProjectAcceptanceTableSetting,
      // 搜索量
      page: 1,
      pageSize: 10,
      totalCount: 0
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getProjectAcceptanceList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.isLoading = true
      this.page = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.isLoading = true
      this.$refs.searchRef.resetForm()
      this.page = 1
      this.initLoad()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.isLoading = true
      this.pageSize = val
      this.page = 1
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.isLoading = true
      this.page = val
      this.pageSize = 10
      this.initLoad()
    },
    // 获取订单列表数据
    async getProjectAcceptanceList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminBackgroundAcceptanceListPost(this.formatQueryParams(this.searchForm))
      )
      if (err) {
        return this.$message.error(err.msg)
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.isLoading = false
      } else {
        return this.$message.error(res.msg)
      }
      this.isLoading = false
    },
    // 格式化表单参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = dayjs(data[key].value[0]).format('YYYY-MM-DD')
            params.end_date = dayjs(data[key].value[1]).endOf('date').format('YYYY-MM-DD')
          }
        }
      }
      params.page = this.page
      params.page_size = this.pageSize
      console.log('params', params)
      return params
    },
    goExport() {
      let option = {
        type: 'SuperProjectAcceptance',
        url: 'apiBackgroundAdminBackgroundAcceptanceListExportPost',
        params: this.formatQueryParams(this.searchForm)
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style>

</style>
