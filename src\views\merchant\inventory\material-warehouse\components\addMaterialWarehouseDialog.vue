<template>
  <!-- 添加/编辑 -->
  <custom-drawer
    :title="title"
    :show.sync="visible"
    direction="rtl"
    :wrapperClosable="true"
    :size="760"
    class="drawer-wrapper"
    v-bind="$attrs"
    v-on="$listeners"
    confirm-text="确定"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <el-form
      :model="formData"
      ref="formData"
      :rules="formDataRules"
      label-width="120px"
      class="material-dialog"
      v-loading="isLoading"
      :status-icon="false"
    >
      <el-form-item label="物资名称" prop="name">
        <el-input v-model="formData.name" :maxlength="20" class="search-item-w"></el-input>
      </el-form-item>
      <el-form-item label="物资分类" prop="category">
        <el-select
          v-model="formData.category"
          placeholder="请选择"
          filterable
          class="ps-select search-item-w"
          @change="changeHandle"
        >
          <el-option
            v-for="item in materialCategoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分类属性">
        <el-select
          v-model="formData.categoryAttribute"
          placeholder="请选择"
          filterable
          class="ps-select search-item-w"
          @change="changeUnitHandle"
        >
          <el-option
            v-for="item in categoryAttributeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关联食材" prop="ingredient">
        <el-select
          v-model="formData.ingredient"
          placeholder="请选择"
          filterable
          class="ps-select search-item-w"
        >
          <el-option
            v-for="item in ingredientNameList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="最小单位" prop="" required class="specifications">
        <el-form-item v-for="(item, index) in formData.specificationsList" :key="index" label="" :prop="'specificationsList.'+index+'.isRepeat'" :rules="formDataRules.isRepeat" :error="item.isRepeat ? '数据重复！' : ''" class="specifications-item">
          <el-form-item label="" label-width="0" :show-message="false" :prop="'specificationsList.'+index+'.unit'" :rules="formDataRules.unit"  class="inline-block">
            <el-select
              v-model="item.unit"
              placeholder="请选择"
              filterable
              class="ps-select w-100 m-r-10"
            >
              <el-option
                v-for="item in unitList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" label-width="0" :show-message="false" :prop="'specificationsList.'+index+'.unitType'" :rules="formDataRules.unitType" class="inline-block">
            <el-select
              v-model="item.unitType"
              placeholder="请选择"
              filterable
              class="ps-select w-100 m-r-10"
              @change="unitTypeChange(item)"
            >
              <el-option
                v-for="item in unitTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" label-width="0" :show-message="false" :prop="'specificationsList.'+index+'.netContent'" :rules="formDataRules.netContent" class="inline-block">
            <el-input v-model="item.netContent" :maxlength="20" class="w-100 m-r-10"></el-input>
          </el-form-item>
          <el-form-item label="" label-width="0" :show-message="false" :prop="'specificationsList.'+index+'.netContentUnit'" :rules="formDataRules.netContentUnit" class="inline-block">
            <el-select
              v-model="item.netContentUnit"
              placeholder="请选择"
              filterable
              class="ps-select w-100 m-r-10"
            >
              <el-option
                v-for="item in netContentUnitList[item.unitType]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <div class="tool-box inline-block m-l-10">
            <i class="tool-icon el-icon-circle-plus" @click="clickToolIcon('add', index, item)"></i>
            <i class="tool-icon el-icon-remove" v-if="index > 0 || formData.specificationsList.length > 1" @click="clickToolIcon('remove', index, item)"></i>
          </div>
        </el-form-item>
      </el-form-item>
    </el-form>
  </custom-drawer>
  <!-- end -->
</template>

<script>
// import { deepClone } from '@/utils';
import { oneDecimal, positiveMoney } from '@/utils/validata.js'
import { UNIT_TYPE, NET_CONTENT_LIST } from '../../constants'

export default {
  name: 'addMaterialWarehouseDialog',
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '新增物资'
    },
    width: {
      type: String,
      default: '740px'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    },
    confirm: Function
  },
  // mixins: [activatedLoadData],
  data() {
    const validateRate = (rule, value, callback) => {
      if (value) {
        if (!oneDecimal(value)) {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    const validateNetContent = (rule, value, callback) => {
      if (value) {
        if (!positiveMoney(value) || value === '0') {
          callback(new Error('格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    // 校验最小单位是否重复
    const validateIsRepeat = (rule, value, callback) => {
      if (value) {
        callback(new Error('数据重复！'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      formData: {
        ingredient: '',
        name: '',
        category: '', // 分类
        categoryAttribute: '', // 分类属性
        specificationsList: [ // 规格
          {
            unit: '', // 单位
            unitType: '', // 单位类型
            netContent: '', // 净含量
            netContentUnit: '', // 净含量单位
            isRepeat: false // 数据是否重复
          }
        ]
      },
      unitList: [],
      ingredientNameList: [],
      materialCategoryList: [],
      formDataRules: {
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        // ingredient: [{ required: true, message: '请选择关联食材', trigger: 'change' }],
        name: [{ required: true, message: '请输入物资名称', trigger: 'change' }],
        rate: [{ validator: validateRate, trigger: 'change' }],
        rateUnit: [{ required: true, message: '请选择', trigger: 'change' }],
        category: [{ required: true, message: '请选择物资分类', trigger: 'change' }],
        unitType: [{ required: true, message: '请选择', trigger: 'change' }],
        netContent: [
          { required: true, message: '请输入', trigger: 'change' },
          { validator: validateNetContent, trigger: 'change' }
        ],
        netContentUnit: [{ required: true, message: '请选择', trigger: 'change' }],
        isRepeat: [{ validator: validateIsRepeat, trigger: 'change' }]
      },
      tableSettings: [
        { label: '当前单位', key: 'name' },
        { label: '关系', key: 'relative', type: 'slot', slotName: 'relative' },
        { label: '转换率', key: 'rate', type: 'slot', slotName: 'rate' },
        { label: '换算单位', key: 'changeUnit', type: 'slot', slotName: 'changeUnit' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      currentSelectUnit: {}, // 记录当前选择的单位数据，用于在table中显示
      deleteIdList: [], // 删除的id列表
      categoryAttributeList: [],
      unitTypeList: UNIT_TYPE,
      netContentUnitList: NET_CONTENT_LIST
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    }
    // 设置单位禁选，表单中的单位不可选择，转换率中的单位不能重复选择
    // unitDistable() {
    //   console.log('cccc')
    //   return (select, unitItem) => {
    //     // const allSelect = []
    //     const allSelect = this.formData.unitRateList.reduce((prev, current) => {
    //       if (select !== current.rateUnit) {
    //         return prev.concat(current.rateUnit)
    //       } else {
    //         return prev
    //       }
    //     }, [this.formData.unit])
    //     console.log('ddd', select, unitItem)
    //     let disabled = false
    //     if (allSelect.includes(unitItem)) {
    //       disabled = true
    //     }
    //     return disabled
    //   }
    // }
    // 禁用规格，当最小单位+单位类型+净含量+类型单位只能有一条数据；如瓶+容量+300+ml有了，就不能再添加一条一模一样的
    // specificationsDistable() {
    //   let allSelect = this.formData.specificationsList.reduce((prev, current) => {
    //     if (current.unit && current.unitType && current.netContent && current.netContentUnit) {
    //       return prev.concat(`${current.unit}-${current.unitType}-${current.netContent}-${current.netContentUnit}`)
    //     } else {
    //       return prev
    //     }
    //   }, [])
    //   return (select, unitItem) => {
    //     console.log('ddd', select, unitItem)
    //     let disabled = false
    //     if (allSelect.includes(unitItem)) {
    //       disabled = true
    //     }
    //     return disabled
    //   }
    // }
  },
  watch: {
    showdialog(val) {
      if (val) {
        this.initLoad()
      }
    },
    'formData.specificationsList': {
      handler(val) {
        let selectLen = {}
        val.forEach(item => {
          if (item.unit && item.unitType && item.netContent && item.netContentUnit) {
            let key = `${item.unit}-${item.unitType}-${item.netContent}-${item.netContentUnit}`
            if (selectLen[key]) {
              selectLen[key] += 1
            } else {
              selectLen[key] = 1
            }
          }
        })
        val.forEach(item => {
          if (item.unit && item.unitType && item.netContent && item.netContentUnit) {
            let key = `${item.unit}-${item.unitType}-${item.netContent}-${item.netContentUnit}`
            if (selectLen[key] && selectLen[key] > 1) {
              this.$set(item, 'isRepeat', true)
            } else {
              this.$set(item, 'isRepeat', false)
            }
          }
        })
      },
      deep: true
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    // 获取食材
    async getIngredientNameList() {
      const res = await this.$apis.apiBackgroundFoodIngredientIngredientNamePost()
      if (res.code === 0) {
        this.ingredientNameList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取物资分类
    async getMaterialCategoryList() {
      const res = await this.$apis.apiBackgroundDrpMaterailClassificationListPost({
        page: 1,
        pageSize: 9999
      })
      if (res.code === 0) {
        this.materialCategoryList = res.data.results.map(item => {
          let obj = {
            name: item.name,
            id: item.id,
            attribute: item.attribute
          }
          return obj
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    async initLoad() {
      this.isLoading = true
      this.getIngredientNameList()
      await this.getMaterialCategoryList()
      await this.getUnitManagementList()
      if (this.type === 'modify') {
        this.formData.name = this.infoData.name
        this.formData.unit = this.infoData.unit_management_id
        this.formData.ingredient = this.infoData.ingredient_id
        this.formData.category = this.infoData.materail_classification_id
        let arr = this.materialCategoryList.find(item => item.id === this.formData.category)
        if (arr && arr.attribute.length) {
          this.categoryAttributeList = arr.attribute.map(item => {
            let obj = {
              name: item,
              id: item
            }
            return obj
          })
          this.formData.categoryAttribute = this.infoData.attribute
        }
        this.changeUnitHandle(this.infoData.unit_management_id)
        console.log(1111, this.infoData)
        if (this.infoData.limit_unit_management && this.infoData.limit_unit_management.length > 0) {
          this.formData.specificationsList = this.infoData.limit_unit_management.map(v => {
            return {
              id: v.id,
              unit: v.unit_management,
              unitType: v.unit_type,
              netContent: v.net_content,
              netContentUnit: v.net_content_unit
            }
          })
        }
      }
      this.isLoading = false
    },
    clickConfirmHandle() {
      let api
      let params = {
        // unit_management_id: this.formData.unit,
        // ingredient_id: this.formData.ingredient,
        name: this.formData.name,
        materail_classification_id: this.formData.category,
        attribute: this.formData.categoryAttribute
      }
      let addList = []
      let modifyList = []

      if (this.formData.ingredient) {
        params.ingredient_id = this.formData.ingredient
      }

      if (this.formData.specificationsList && this.formData.specificationsList.length > 0) {
        for (let i = 0; i < this.formData.specificationsList.length; i++) {
          let paramsItem = {
            unit_management_id: this.formData.specificationsList[i].unit,
            unit_type: this.formData.specificationsList[i].unitType,
            net_content: this.formData.specificationsList[i].netContent,
            net_content_unit: this.formData.specificationsList[i].netContentUnit
          }
          if (this.formData.specificationsList[i].id) {
            paramsItem.id = this.formData.specificationsList[i].id
            modifyList.push(paramsItem)
          } else {
            addList.push(paramsItem)
          }
        }
      }
      if (addList && addList.length > 0) {
        params.add_list = addList
      }
      if (modifyList && modifyList.length > 0) {
        params.modify_list = modifyList
      }
      if (this.deleteIdList && this.deleteIdList.length > 0) {
        params.delete_list = this.deleteIdList
      }

      this.$refs.formData.validate(valid => {
        if (valid) {
          if (this.isLoading) return
          this.isLoading = true
          if (this.type === 'add') {
            api = this.$apis.apiBackgroundDrpMaterialsAddPost(params)
          } else {
            params.id = this.infoData.id
            api = this.$apis.apiBackgroundDrpMaterialsModifyPost(params)
          }
          this.sendFormData(api)
        } else {
        }
      })
    },
    async sendFormData(api) {
      const [err, res] = await this.$to(api)
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('成功')
        this.confirm && this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handlerClose(e) {
      this.formData = {
        ingredient: '',
        name: '',
        category: '', // 分类
        categoryAttribute: '', // 分类属性
        specificationsList: [ // 规格
          {
            unit: '', // 单位
            unitType: '', // 单位leixing
            netContent: '', // 重量
            netContentUnit: '', // 重量类型、容量类型
            isRepeat: false // 错误提示，如果数据重复则显示checkMessage
          }
        ]
      }
      if (this.$refs.formData) {
        this.$refs.formData.resetFields()
      }
      this.isLoading = false
      // this.visible = false
      // this.$emit('close')
    },
    // 获取单位列表数据
    async getUnitManagementList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpUnitManagementListPost({
        page: 1,
        page_size: 999999
        // organization_id: this.$store.getters.organization
      }))
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.unitList = res.data.results
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 物资分类切换除了要切换单位，还要讲分类属性赋值
    changeHandle(e) {
      this.changeUnitHandle(e)
      this.$nextTick(() => {
        let arr = this.materialCategoryList.filter(item => item.id === e)
        this.categoryAttributeList = arr[0].attribute.map(item => {
          let obj = {
            name: item,
            id: item
          }
          return obj
        })
        this.formData.categoryAttribute = ''
      })
    },
    // 单位切换
    changeUnitHandle(e) {
      for (let index = 0; index < this.unitList.length; index++) {
        const unitItem = this.unitList[index];
        if (e === unitItem.id) {
          this.currentSelectUnit = {
            id: unitItem.id,
            name: unitItem.name,
            organization_id: unitItem.organization_id
          }
          break;
        }
      }
    },
    // 最小单位添加和删除
    clickToolIcon(type, index, row) {
      if (type === 'add') {
        this.formData.specificationsList.push({
          unit: '', // 单位
          unitType: '', // 单位类型
          netContent: '', // 净含量
          netContentUnit: '', // 净含量单位
          isRepeat: false // 数据是否重复
        })
      } else {
        if (row.id) {
          this.deleteIdList.push(row.id)
        }
        this.formData.specificationsList.splice(index, 1)
      }
    },
    unitTypeChange(data) {  // 修复切换数据的时候，出现“无匹配数据”
      data.netContentUnit = ''
    }
  }
}
</script>

<style lang="scss">
.material-dialog {
  .search-item-w {
    width: 260px;
  }
  .w-100 {
    width: 100px;
  }
  .flex {
    display: flex;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .el-upload__tip {
    color: red;
  }
  .el-form-item {
    &.m-b-0 {
      margin-bottom: 0;
    }
    &.is-error {
      // margin-bottom: 18px;
    }
  }
  .tool-icon {
    height: 40px;
    line-height: 40px;
    vertical-align: middle;
    cursor: pointer;
    font-size: 24px;
    &+.tool-icon {
      margin-left: 6px;
    }
    &:hover{
      color: #ff9b45;
    }
  }
  .specifications {
    .specifications-item {
      margin-bottom: 22px;
      &>.el-form-item__content {
        &>.el-form-item {
          // margin-bottom: 22px;
        }
      }
    }
  }
}
</style>
