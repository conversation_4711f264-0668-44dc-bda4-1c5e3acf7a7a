<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :showFooter="false"
    :loading.sync="isLoading"
    :width="width"
    top="200px"
    @close="handlerClose"
  >
    <div class="search-wrapper m-b-20">
      <span class="inline-block">提交时间：</span>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        unlink-panels
        size="small"
        :clearable="false"
        @change="changeDateHandle"
      ></el-date-picker>
    </div>
    <div class="">
      <!-- table start -->
      <el-table
        v-loading="isLoading"
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        size="small"
        stripe
        header-row-class-name="ps-table-header-row"
        row-key="id"
        max-height="600"
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item">
          <template #operation="{ row }">
            <el-button type="text" size="small" class="ps-text" @click="operationHandle('recovery', row)">
              恢复编辑
            </el-button>
            <el-button type="text" size="small" class="ps-text" @click="operationHandle('delete', row)">删除</el-button>
          </template>
        </table-column>
      </el-table>
      <!-- table end -->
    </div>
    <!-- 分页 start -->
    <div v-if="totalCount" class="block" style="text-align: right">
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
// 草稿箱
import { debounce, getSevenDateRange } from '@/utils'
export default {
  name: 'DraftBoxDialog',
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: '' //
    },
    title: {
      type: String,
      default: '草稿箱'
    },
    width: {
      type: String,
      default: '660px'
    },
    api: {
      type: String,
      required: true
    },
    // 调用接口的默认参数
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    InfoData: {
      type: Object,
      default() {
        return {}
      }
    },
    tableSettings: {
      type: Array,
      default() {
        return [
          { label: '采购单名称', key: 'name' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right' }
        ]
      }
    }
    // modifyfun: Function,
    // deletefun: Function
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      dateRange: getSevenDateRange(7),
      tableData: [{}],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 初始化
    init() {
      this.getDraftBoxList()
    },
    // 格式化下参数
    formatQueryParams(data) {
      let params = {}
      // for (const key in data) {
      //   if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
      //     if (key !== 'select_time') {
      //       params[key] = data[key].value
      //     } else if (data[key].value.length > 0) {
      //       params.start_date = data[key].value[0]
      //       params.end_date = data[key].value[1]
      //     }
      //   }
      // }
      if (this.dateRange.length > 0) {
        params.start_date = this.dateRange[0]
        params.end_date = this.dateRange[1]
      }
      return params
    },
    // 获取草稿箱列表数据
    async getDraftBoxList() {
      if (!this.api) {
        return this.$message.error('获取接口地址失败！')
      }
      this.isLoading = true
      const res = await this.$apis[this.api]({
        ...this.formatQueryParams(),
        ...this.params
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    changeDateHandle: debounce(function () {
      this.currentPage = 1
      this.getDraftBoxList()
    }, 300),
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDraftBoxList()
    },
    // 操作
    operationHandle(type, row) {
      let message = ''
      switch (type) {
        case 'recovery':
          message = '确定恢复编辑吗？'
          break
        case 'delete':
          message = '确定删除吗？'
      }
      this.$confirm(message, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            if (type === 'recovery') {
              await this.recoveryHandle(row, done)
            }
            if (type === 'delete') {
              await this.deleteHandle(row, done)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 恢复
    async recoveryHandle(row, done) {
      done()
      this.$emit('recovery', row)
      this.deleteHandle(row)
      this.visible = false
    },
    // 删除
    async deleteHandle(row, done) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpTemplateInfoTempDeletePost({
        id: row.id,
        inventory_info_type: this.params.inventory_info_type
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('成功')
        // this.visible = false
        // 执行回调
        if (done) {
          this.$emit('delete')
          done()
          this.getDraftBoxList()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    handlerClose(e) {
      this.isLoading = false
      // this.visible = false
      // this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scope>
.dialog-form {
}
</style>
