<template>
  <!-- 添加/编辑 -->
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :width="width"
    class="FormDialog"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" :label-width="formLabelWidth" :size="formSize">
      <el-form-item v-if="type === 'draft' || type === 'template' || type === 'add'" :label="inputLabel" prop="name">
        <el-input v-model="formData.name" :maxlength="20" class="ps-input w-220"></el-input>
      </el-form-item>
      <div v-if="type === 'recipes'">
        <el-form-item label="" label-width="0" class="" prop="recipes_type">
          <el-radio-group v-model="formData.recipes_type" @change="changeRecipesType">
            <el-radio-button v-for="item in recipesTypes" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜谱名称" class="" prop="menu_id">
          <el-select v-model="formData.menu_id" class="ps-select w-280" popper-class="ps-popper-select" :loading="remoteLoading" placeholder="请选择">
            <el-option v-for="option in recipesList" :key="option.id" :label="option.name" :value="option.id" ></el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </dialog-message>
  <!-- end -->
</template>

<script>
// 草稿箱
import { deepClone } from '@/utils'
// import { integer } from '@/utils/validata'

export default {
  name: 'FormDialog',
  components: {},
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add' // add：添加物资，purchase: 采购单
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '460px'
    },
    api: {
      type: String,
      required: true
    },
    formSize: {
      type: String,
      default: 'medium'
    },
    formLabelWidth: {
      type: String,
      default: '80px'
    },
    inputLabel: {
      type: String,
      default: '草稿名称'
    },
    InfoData: {
      type: Object,
      default() {
        return {}
      }
    },
    // 调用接口的默认参数
    params: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  // mixins: [activatedLoadData],
  data() {
    // const validateCount = (rule, value, callback) => {
    //   if (value) {
    //     if (!integer(value)) {
    //       callback(new Error('格式错误'))
    //     } else {
    //       callback()
    //     }
    //   } else {
    //     callback()
    //   }
    // }
    return {
      isLoading: false,
      selectList: [],
      formData: {
        name: '',
        recipes_type: 'week',
        menu_id: ''
      },
      formRules: {
        name: [{ required: true, message: '请输入', trigger: 'change' }],
        menu_id: [{ required: true, message: '请选择菜谱', trigger: 'change' }]
      },
      // 查看弹窗
      showViewDialog: false,
      remoteLoading: false,
      recipesList: [], // 菜谱列表
      weekRecipesList: [],
      monthRecipesList: [],
      recipesTypes: [
        { label: '周菜谱', value: 'week' },
        { label: '月菜谱', value: 'month' }
      ]
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    },
    // 当前选择数量
    selectLenght() {
      return this.selectList.length
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 初始化
    async init() {
      // this.getDraftBoxList()
      // 设置选中table状态
      // this.$refs.tableData.toggleRowSelection(row, true)
      switch (this.type) {
        case 'recipes':
          await this.getWeekRecipesList()
          await this.getMonthRecipesList()
          this.changeRecipesType(this.formData.recipes_type)
          break;
      }
    },
    clickConfirmHandle() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          // this.sendDataHandle(this.formatParams())
          let formParams = {}
          switch (this.type) {
            case 'recipes':
              formParams = {
                menu_id: this.formData.menu_id,
                menu_type: this.formData.recipes_type
              }
              break;
            case 'draft':
              formParams.name = this.formData.name
              break;
            case 'template':
              formParams.name = this.formData.name
              break;
          }
          // this.$emit('update', this.formData)
          this.sendFormData(formParams)
        }
      })
    },
    // 发送请求数据
    async sendFormData(data = {}) {
      if (!this.api || !this.$apis[this.api]) {
        return this.$message.error('获取接口地址失败！')
      }
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis[this.api]({
          ...data,
          ...this.params
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '出错了')
        return
      }
      if (res.code === 0) {
        if (this.type === 'recipes') {
          this.$emit('confirmForm', {
            type: this.type,
            data: res.data
          })
          this.visible = false
        } else {
          this.$message.success(res.msg || '添加成功')
          this.$emit('confirmForm', {
            type: this.type
          })
          this.visible = false
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handlerClose(e) {
      console.log(11111, 'ChooseListDialog')
      this.isLoading = false
      if (this.$refs.formRef) {
        // 请除table选中状态
        this.$refs.formRef.resetFields()
      }
      // this.visible = false
      // this.$emit('close')
    },
    // 周菜谱列表
    async getWeekRecipesList(params = {}) {
      this.remoteLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundFoodMenuWeeklyListPost({
        page: 1,
        page_size: 999999,
        nowaday: true,
        ...params
      }))
      this.remoteLoading = false
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.weekRecipesList = res.data.results
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 月菜谱列表
    async getMonthRecipesList(params = {}) {
      this.remoteLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundFoodMenuMonthlyListPost({
        page: 1,
        page_size: 999999,
        nowaday: true,
        ...params
      }))
      this.remoteLoading = false
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.monthRecipesList = res.data.results
      } else {
        // this.$message.error(res.msg)
      }
    },
    async changeRecipesType(type, init) {
      if (type === 'week') {
        this.recipesList = deepClone(this.weekRecipesList)
      } else {
        this.recipesList = deepClone(this.monthRecipesList)
      }
      this.formData.menu_id = ''
    }
  }
}
</script>

<style lang="scss" scope>
.FormDialog {
  .w-220 {
    width: 220px;
  }
  .w-136 {
    width: 136px;
  }
  .w-120 {
    width: 120px;
  }
  .w-auto {
    width: auto;
  }
}
</style>
