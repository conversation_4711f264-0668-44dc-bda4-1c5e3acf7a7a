import { mergeHandle, mergeRowAction } from '@/utils/table'
// 使用事例 省妇幼分支 EmployeeProducedTotal 页面
// table 加上
// :span-method="spanMethod"
// :cell-style="cellStyle"
// end
// ----配置----
// this.mergeOpts = {
//   // 行合并配置
//   rowMergeList: [‘‘weekDate’’], 这个字段和mergeKeyList好像差不多
//   // 列合并配置
//   colMergeList: [
//     {
//       labels: ['数量', '菜品名称', '数量', '菜品名称', '数量', '菜品名称'], table的头
//       subtotals: ['总单'], 列合并的文字
//       attr: 'count_1' 根据哪个字段
//     }
//   ],
//   useKeyList: {
// id: [
//   'selection',
//   'l1_addr'
// ]},
//   mergeKeyList: [‘weekDate’] 这个字段和rowMergeList好像差不多
// }

// // 初始化合并数据
// this.initMergeData(this.tableData)
// --end--
export default {
  data() {
    return {
      // 合并配置
      mergeOpts: {
        useKeyList: {}, // 根据固定key进行合并的配置
        mergeKeyList: [], // 通用的合并字段配置
        colMergeList: [], // 需要列合并的配置，格式：[{ labels: [], subtotals: [], attr: '' }]
        rowMergeList: [] // 需要行合并的字段列表
      },
      rowMergeArrs: {}, // 存储合并后的数据
      // 添加缓存对象
      mergeCacheMap: new Map()
    }
  },

  methods: {
    /**
     * 清除合并缓存
     */
    clearMergeCache() {
      this.mergeCacheMap.clear()
    },

    /**
     * 获取缓存的合并结果
     */
    getCachedMergeResult(row, column, rowIndex, columnIndex) {
      const cacheKey = `${rowIndex}-${columnIndex}-${column.property}`
      if (this.mergeCacheMap.has(cacheKey)) {
        return this.mergeCacheMap.get(cacheKey)
      }
      return null
    },

    /**
     * 缓存合并结果
     */
    setCacheMergeResult(row, column, rowIndex, columnIndex, result) {
      const cacheKey = `${rowIndex}-${columnIndex}-${column.property}`
      this.mergeCacheMap.set(cacheKey, result)
    },

    /**
     * 优化后的表格合并单元格方法
     */
    spanMethod({ row, column, rowIndex, columnIndex }) {
      try {
        // 检查缓存
        const cachedResult = this.getCachedMergeResult(row, column, rowIndex, columnIndex)
        if (cachedResult) {
          return cachedResult
        }

        let result = [1, 1]

        // 1. 处理列合并
        if (this.mergeOpts.colMergeList?.length) {
          result = this.handleColumnMerge(row, column, rowIndex, columnIndex)
          if (result[0] !== 1 || result[1] !== 1) {
            this.setCacheMergeResult(row, column, rowIndex, columnIndex, result)
            return result
          }
        }

        // 2. 处理行合并
        if (this.mergeOpts.rowMergeList?.includes(column.property)) {
          result = this.getRowMergeResult(rowIndex, column.property)
          this.setCacheMergeResult(row, column, rowIndex, columnIndex, result)
          return result
        }

        // 3. 处理useKeyList中的合并
        if (Object.keys(this.mergeOpts.useKeyList || {}).length) {
          for (const key in this.mergeOpts.useKeyList) {
            if (this.mergeOpts.useKeyList[key].includes(column.property)) {
              result = mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
              this.setCacheMergeResult(row, column, rowIndex, columnIndex, result)
              return result
            }
          }
        }

        // 4. 处理mergeKeyList中的合并
        if (this.mergeOpts.mergeKeyList?.includes(column.property)) {
          result = mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          this.setCacheMergeResult(row, column, rowIndex, columnIndex, result)
          return result
        }

        return result
      } catch (error) {
        console.error('合并单元格出错：', error)
        return [1, 1]
      }
    },

    /**
     * 处理列合并逻辑
     */
    handleColumnMerge(row, column, rowIndex, columnIndex) {
      for (const { labels, subtotals, attr } of this.mergeOpts.colMergeList) {
        const columnIndex = labels.indexOf(column.label)
        if (columnIndex !== -1 && subtotals?.includes(row[attr])) {
          return columnIndex === 0 ? [1, labels.length] : [0, 0]
        }
      }
      return [1, 1]
    },

    /**
     * 优化后的行合并结果计算
     */
    getRowMergeResult(rowIndex, property) {
      const data = this.$refs.tableData?.store?.states?.data
      if (!data?.length) return [1, 1]

      const value = data[rowIndex][property]
      if (value === undefined || value === null) return [1, 1]

      // 向上查找相同值的行数
      for (let i = rowIndex - 1; i >= 0; i--) {
        if (data[i][property] === value) {
          return [0, 0]
        }
      }

      // 向下查找相同值的行数
      let rowspan = 1
      for (let i = rowIndex + 1; i < data.length; i++) {
        if (data[i][property] === value) {
          rowspan++
        } else {
          break
        }
      }

      return [rowspan, 1]
    },

    /**
     * 优化后的初始化合并数据
     */
    initMergeData(data) {
      if (!data?.length) return

      this.clearMergeCache()
      this.rowMergeArrs = mergeHandle(data, this.mergeOpts)
    },

    /**
     * 设置单元格样式
     * @param {Object} param0 - 参数对象
     * @returns {Object} - 样式对象
     */
    cellStyle({ row, column }) {
      // 处理列合并的样式
      for (const { labels, subtotals, attr } of this.mergeOpts.colMergeList) {
        const columnIndex = labels.indexOf(column.label)
        if (columnIndex !== -1 && subtotals.includes(row[attr])) {
          if (columnIndex === 0) {
            return { 'text-align': 'left' }
          }
        }
      }
      return {}
    }
  }
}
