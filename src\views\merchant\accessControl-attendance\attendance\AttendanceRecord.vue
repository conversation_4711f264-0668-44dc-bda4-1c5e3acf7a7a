<template>
  <div class="AttendanceRecord container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      @search="searchHandle"
      :form-setting="searchFormSetting"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_attendance.attendance_record.list_export']">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="tableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          :isFirst="isFirstSearch"
          header-row-class-name="ps-table-header-row"/>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import { recentSevenDay, punchStatuaList, getRequestParams } from '../constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'AttendanceRecord',
  mixins: [exportExcel],
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '一级组织', key: 'primary' },
        { label: '二级组织', key: 'secondary' },
        { label: '三级组织', key: 'company' },
        { label: '抓拍人脸', key: 'face_url', type: 'img' },
        { label: '姓名', key: 'person_name' },
        { label: '人员编号', key: 'person_no' },
        { label: '考勤组', key: 'attendance_groups_name' },
        { label: '上传时间', key: 'create_time' },
        { label: '打卡时间', key: 'punch_time' },
        { label: '打卡班次', key: 'attendance_settings_name' },
        { label: '打卡结果', key: 'punch_status_alias' },
        { label: '体温', key: 'temperature' },
        { label: '打卡设备', key: 'device_name' }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'punch_time',
          dataList: [
            {
              label: '打卡时间',
              value: 'punch_time'
            },
            {
              label: '上传时间',
              value: 'upload_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          value: recentSevenDay,
          clearable: false
        },
        person_name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        },
        punch_status: {
          type: 'select',
          label: '打卡结果',
          value: [],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择打卡结果',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: punchStatuaList
        },
        attendance_groups_ids: {
          type: 'select',
          label: '考勤组',
          value: [],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择考勤组',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: []
        },
        device_info_ids: {
          type: 'select',
          label: '打卡设备',
          value: [],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择打卡设备',
          listNameKey: 'device_name',
          listValueKey: 'device_no',
          dataList: []
        }
      },
      isFirstSearch: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getAttendanceRecordList()
      this.getAttendanceGroupList()
      this.getDeviceList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.isFirstSearch = false
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.isFirstSearch = false
      this.tableData = []
      this.initLoad()
    },
    async getAttendanceRecordList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundAttendanceAttendanceRecordListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getAttendanceRecordList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getAttendanceRecordList()
    },
    gotoExport() {
      const option = {
        type: "AttendanceRecordListExport",
        params: getRequestParams(this.searchFormSetting, this.page, this.totalCount)
      }
      this.exportHandle(option)
    },
    // 获取考勤组
    async getAttendanceGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.searchFormSetting.attendance_groups_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设备列表
    async getDeviceList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceListPost({
        device_type: 'RLZJ',
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.searchFormSetting.device_info_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
