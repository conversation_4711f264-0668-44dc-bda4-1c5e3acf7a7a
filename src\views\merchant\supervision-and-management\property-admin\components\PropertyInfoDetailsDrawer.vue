<template>
  <!-- 添加/编辑 -->
  <custom-drawer
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :wrapperClosable="false"
    :size="770"
    destroy-on-close
    v-bind="$attrs"
    v-on="$listeners"
    :confirmShow="false"
    cancelText="关闭"
    @close="handlerClose"
    @cancel="clickCancleHandle"
  >
    <div class="p-10">
      <div class="m-b-10">{{ dateName }}原材料统计</div>
      <el-table
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        size="small"
        stripe
        header-row-class-name="ps-table-header-row"
        tooltip-effect="dark property-info-details-drawer-tooltips"
        row-key="id"
        border
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item"></table-column>
      </el-table>
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :page-sizes="[10, 20, 50, 100, 500]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      ></pagination>
    </div>
  </custom-drawer>
  <!-- end -->
</template>

<script>
import { INFO_SYSTEM_DETAILS_TABLE, INFO_HAND_DETAILS_TABLE } from './constants'
import { deepClone } from '@/utils'
import * as dayjs from 'dayjs'

export default {
  inheritAttrs: false,
  name: 'addMaterialWarehouseDialog',
  props: {
    isShow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '详情'
    },
    width: {
      type: String,
      default: '560px'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSettings: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      dateName: ''
    }
  },
  computed: {
    visible: {
      get() {
        return this.isShow
      },
      set(val) {
        this.$emit('update:isShow', val)
      }
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.dateName = dayjs(this.infoData.create_time).format('YYYY年MM月DD日')
      if (this.infoData.is_sys) {
        this.tableSettings = deepClone(INFO_SYSTEM_DETAILS_TABLE)
      } else {
        this.tableSettings = deepClone(INFO_HAND_DETAILS_TABLE)
      }
      this.getDataList()
    },
    async getDataList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundFundSupervisionAssetAssetInfoDetailsListPost({
        page: this.currentPage,
        page_size: this.pageSize,
        asset_info_id: this.infoData.id
      })
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        this.tableData = data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 取消事件
    clickCancleHandle() {
      this.visible = false
    },
    // 关闭弹窗
    handlerClose(e) {
      this.visible = false
    }
  }
}
</script>

<style lang="scss">
.dialog-form {
}
.property-info-details-drawer-tooltips {
  white-space: normal; /* 允许换行 */
  word-break: break-all; /* 长单词自动换行 */
  max-width: 200px; /* 设置最大宽度，超出后换行 */
}
</style>
