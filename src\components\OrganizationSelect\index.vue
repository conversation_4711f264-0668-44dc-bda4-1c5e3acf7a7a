<template>
  <select-tree
    v-model="selectData"
    :treeData="organizationList"
    :treeProps="treeProps"
    :loadTree="loadOrganizationList"
    :isLazy="isLazy"
    :role="role"
    :clearable="isClear"
    :size="size"
    :multiple="multiple"
    v-bind="$attrs"
    v-on="$listeners"
    :filterable="true"
    :filterNode="filterNode"
    :loading="loading"
    :disabled="disabled"
    :defaultExpandedKeys="defaultExpandedKeys"
    :class="{'search-item-w': true, 'disabled-tag': disabledLevel}"
  ></select-tree>
</template>

<script>
import { to, deleteEmptyGroup, getTreeChildArr, unique, getCurrentTree, deepClone } from '@/utils'
import SelectTree from '@/components/SelectTree'
export default {
  inheritAttrs: false,
  name: 'OrganizationSelect',
  components: {
    SelectTree
  },
  model: {
    prop: 'checked',
    event: 'changeCheck'
  },
  props: {
    disabled: {
      // required: true
      type: Boolean
    },
    checked: {
      // required: true
      type: [String, Number, Array]
    },
    popperClass: {
      type: String,
      default: 'ps-popper-select'
    },
    options: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'id'
        }
      }
    },
    // 是否开启懒加载，不要用懒加载有问题，level参数
    isLazy: {
      type: Boolean,
      default: () => false
    },
    role: {
      type: String,
      default: ''
    },
    dataList: {
      type: Array,
      default: () => {
        return []
      }
    },
    onlyChild: { // 是否只显示下级组织，一版请求回来都包含当前登录的组织，isLazy为false可用
      type: Boolean,
      default: () => false
    },
    disabledLevel: { // 禁止选中第level层数据，0为不限制
      type: Number,
      default: () => 0
    },
    // 是否可清空选项
    clearable: {
      type: Boolean,
      default: () => true
    },
    // 单选初始值
    size: {
      type: String,
      default: 'medium'
    },
    company: {
      type: Number,
      default: () => 0
    },
    // 禁止选中的数据id
    disabledList: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 是否正在从远程获取数据
    loading: {
      type: Boolean,
      default: () => false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否选中当前禁止项及其父级，v-modal中不会绑定当前禁止项及其父级的值（update:input中处理了一层），当前只做显示用
    checkDisabledAndParents: {
      type: Boolean,
      default: false
    },
    // 如果当前有子级=>子级也要默认禁止选择，与平级不可同时使用（disabledSameLevel，allowDisabledSameChild）
    allowDisabledChild: {
      type: Boolean,
      default: false
    },
    // 是否禁止平级选中
    disabledSameLevel: { //
      type: Boolean,
      default: false
    },
    // 如果当前平级有子级=>子级也要默认禁止选择
    allowDisabledSameChild: {
      type: Boolean,
      default: false
    },
    orgId: {
      type: [String, Number],
      default: ''
    },
    onlyShowOrgIdTree: {
      type: Boolean,
      default: false
    },
    // 一般用于筛选组件的联动，具体参考激活码管理
    orgs: {
      type: Array,
      default: () => {
        return []
      }
    },
    parentId: {
      type: Number,
      default: 0
    },
    defaultExpandAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      allOrganizationList: [],
      organizationList: [],
      treeProps: {
        value: 'id',
        label: 'name',
        isLeaf: 'is_leaf',
        children: 'children_list'
      },
      isClear: true, // 是否显示清空图标
      defaultExpandedKeys: [],
      parentDisabledList: [], // 当前禁止选中项的所有父级数据列表
      childDisabledList: [], // 当前禁止选中项的所有子级数据列表
      allDisabledList: [] // 所有disabled的数据
    }
  },
  computed: {
    selectData: {
      get() {
        let val = this.checked
        if (this.checkDisabledAndParents && this.multiple) {
          val = unique(val.concat(this.allDisabledList))
        }
        return val
      },
      set(val) {
        let result = []
        if (this.checkDisabledAndParents && this.multiple) {
          result = val.filter(v => {
            return !this.allDisabledList.includes(v)
          })
        } else {
          result = val
        }
        this.$emit('changeCheck', result)
      }
    }
  },
  watch: {
    clearable: function(val) {
      this.isClear = val
    },
    orgs: function(val) {
      if (this.orgs.length) {
        this.organizationList = this.allOrganizationList.filter(item => this.orgs.indexOf(item.id) !== -1)
      } else {
        this.organizationList = this.allOrganizationList
      }
    }
  },
  created() {},
  mounted() {
    this.isClear = this.clearable
    if (!this.isLazy) {
      this.getOrganizationList()
    }
  },
  methods: {
    setTreeDeepList(treeData, key, childrenKey, deep) {
      let deepNo = 1
      key = key || 'key'
      childrenKey = childrenKey || 'children'
      function getDeepHandle(treeData, key, childrenKey, deep) {
        if (treeData instanceof Array) {
          treeData.forEach(treeItem => {
            treeItem.disabled = true
            if ((deep && deepNo < deep) && treeItem[childrenKey] && treeItem[childrenKey].length > 0) {
              deepNo++
              getDeepHandle(treeItem[childrenKey], key, childrenKey, deep)
            }
          })
        }
      }
      getDeepHandle(treeData, key, childrenKey, deep)
      return treeData
    },
    // 获取所有数据
    async getOrganizationList() {
      let api
      if (this.role === 'super') {
        api = this.$apis.apiBackgroundAdminOrganizationTreeListPost()
      } else {
        api = this.$apis.apiBackgroundOrganizationOrganizationTreeListPost()
      }
      const res = await api
      if (res.code === 0) {
        let dataList = []
        let list = res.data || []
        if (this.parentId) {
          list = list.filter(item => {
            return item.id === this.parentId
          })
        }
        console.log("list 1111111111", list);
        if (this.onlyChild) {
          list = deleteEmptyGroup(list)[0]
          list = list.children_list ? list.children_list : []
          // console.log(list)
          dataList = [].concat(this.dataList, list)
        } else {
          dataList = [].concat(this.dataList, deleteEmptyGroup(list))
        }
        if (this.orgId && this.onlyShowOrgIdTree) {
          dataList = getCurrentTree(dataList, this.orgId, 'id', 'children_list')
        }

        if (this.disabledLevel > 0) {
          this.isClear = false
          this.setTreeDeepList(dataList, 'id', 'children_list', this.disabledLevel)
        }
        // 有禁止选中的数据
        if (this.disabledList.length > 0) {
          this.isClear = false
          if (this.checkDisabledAndParents || this.disabledSameLevel) {
            let disabledLen = this.disabledList.length
            let allList = []
            for (let i = 0; i < disabledLen; i++) {
              // 如果当前list没有disabled需要去遍历tree树拿自己以及子级的数据
              if (!this.childDisabledList.includes(this.disabledList[i])) {
                let currentList = getTreeChildArr(dataList, this.disabledList[i], {
                  key: 'id',
                  childkey: 'children_list',
                  hasId: false,
                  hasParent: true
                })
                this.parentDisabledList = this.parentDisabledList.concat(currentList.parents)
                this.childDisabledList = this.childDisabledList.concat(currentList.childrens)
                allList = allList.concat(currentList.total)
              }
            }
            this.parentDisabledList = unique(this.parentDisabledList)
            this.childDisabledList = unique(this.childDisabledList)
            this.disabledTreeList(dataList)
            this.allDisabledList = unique([...this.parentDisabledList, ...this.disabledList])
          } else {
            this.disabledTreeList(dataList, this.disabledList)
          }
        }

        this.organizationList = dataList
        this.allOrganizationList = dataList

        if (this.defaultExpandAll) {
          let arr = []
          this.organizationList.forEach(item => {
            this.setDefaultExpandedKey(arr, item)
          })
          this.defaultExpandedKeys = deepClone(arr)
          console.log("获取组织树defaultExpandedKeys", this.defaultExpandedKeys)
        }

        if (this.role === 'super' && this.company) {
          this.organizationList = this.organizationList.filter(item => item.company === this.company)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    setDefaultExpandedKey(arr, data) {
      arr.push(data.id)
      if (data.has_children && data.children_list.length) {
        data.children_list.forEach(item => {
          this.setDefaultExpandedKey(arr, item)
        })
      }
    },
    disabledTreeList(list, disabledList = []) {
      let _selt = this
      if (this.checkDisabledAndParents) {
        disabledList = this.parentDisabledList.concat(this.disabledList)
      }
      if (this.allowDisabledChild) {
        disabledList = disabledList.concat(this.childDisabledList)
      }
      let unDisabledList = []
      if (this.disabledSameLevel && !this.checkDisabledAndParents) {
        disabledList = disabledList.concat(this.disabledList)
        unDisabledList = this.parentDisabledList.concat(this.childDisabledList)
      }
      const that = this
      function traversal(data) {
        data.map(item => {
          if (disabledList.includes(item[_selt.treeProps.value])) {
            item.disabled = true
          }
          if (that.checkDisabledAndParents) {
            if (that.disabledSameLevel && that.allowDisabledSameChild) {
              if (that.childDisabledList.includes(item[_selt.treeProps.value])) {
                item.disabled = false
              } else {
                item.disabled = true
              }
            }
          } else if (that.disabledSameLevel) {
            if (!unDisabledList.includes(item[_selt.treeProps.value])) {
              item.disabled = true
            }
          }
          if (item[_selt.treeProps.children]) {
            if (item[_selt.treeProps.children].length > 0) {
              traversal(item[_selt.treeProps.children])
            }
          }
        })
      }
      traversal(list)
      return list
    },
    // 动态加载远程数据
    async loadOrganizationList(tree, resolve) {
      let params = {
        page: 1,
        page_size: 99999999,
        status__in: ['enable', 'disable']
      }
      if (tree.level === 0) {
        params.level = 0
      } else {
        params.parent = tree.data.id
      }
      let api
      if (this.role === 'super' || this.role === 'custom') {
        api = this.$apis.apiBackgroundAdminOrganizationListPost(params)
      } else {
        api = this.$apis.apiBackgroundOrganizationOrganizationListPost(params)
      }
      const [err, res] = await to(api)
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(item => {
          item.is_leaf = !item.has_children;
        })
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },

    filterNode (value, data, node) {
      if (!value) {
        node.expanded = false
        return true
      }
      // 查询列表是否有匹配数据
      // let val = value.toLowerCase()
      return this.chooseNode(value, data, node)
    },
    chooseNode (value, data, node) {
      if (data.name.indexOf(value) !== -1) {
        return true
      }
      const level = node.level
      // 如果传入的节点本身就是一级节点就不用校验了
      if (level === 1) {
        return false
      }
      // 先取当前节点的父节点
      let parentData = node.parent
      // 遍历当前节点的父节点
      let index = 0
      while (index < level - 1) {
        // 如果匹配到直接返回 匹配中英文过滤，name中文，enName英文
        if (parentData.data.name.indexOf(value) !== -1) {
          return true
        }
        // 否则的话再往上一层做匹配
        parentData = parentData.parent
        index++
      }
      // 没匹配到返回false
      return false
    }
    // 新增点东西
  }
}
</script>

<style lang="scss" scope>
// .user-group_select {
// }
</style>

<style lang="scss">
.disabled-tag{
  .el-tag__close {
    display: none;
  }
}
</style>
