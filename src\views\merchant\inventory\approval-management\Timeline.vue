<template>
  <div class="timeline">
    <el-timeline>
      <el-timeline-item v-for="(item, index) in timeline" :key="index" :timestamp="item.operator + ' ' + item.approve_status" size="large" placement="top">
        <div class="m-t-10">
          <p>
            {{ item.content }}
            <span class="m-l-10">{{ item.time }}</span>
          </p>
          <!-- <p>王小虎 提交于 2018/4/12 20:46</p> -->
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
export default {
  name: 'Timeline',
  props: {
    timeline: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {

    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {

  }
};
</script>

<style scoped lang="scss">
.timeline {
  ::v-deep.el-timeline-item {
    .el-timeline-item__node {
      border: 2px solid #dcdfe6;
      background-color: #fff;
    }
    .el-timeline-item__timestamp{
      font-size: 14px;
      color: #303133;
    }
    .el-timeline-item__content {
      color: #6e7176;
    }
  }
  p{
    margin: 6px 0;
  }
}
</style>
