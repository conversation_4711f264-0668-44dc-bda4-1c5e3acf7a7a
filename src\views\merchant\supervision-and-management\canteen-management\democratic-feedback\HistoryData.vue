<template>
  <div class="container-wrapper">
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false" label-width="90px"></search-form>
    <div class="table-wrapper p-t-20">
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #context="{ row }">
              <el-popover
                placement="left"
                width="1000"
                trigger="click"
                v-if="row.operation === 'modify'">
                <div class="flex h-600px">
                  <div class="flex-1 h-100-p overflow-auto">
                    <div class="pos-sticky pos-top-none w-100-p bg-white" style="z-index: 999;">修改前：</div>
                    <el-form ref="previewForm" :model="row.beforeForm" label-position="top">
                      <!-- 问题列表 -->
                      <div class="preview-content">
                        <div v-for="(question, index) in row.context.before" :key="index" class="question-item">
                          <el-form-item
                            :label="`${index + 1}. ${question.caption}`"
                            :prop="`answers.${index}`"
                            :rules="{ required: question.required, message: '此题为必答题', trigger: 'change' }"
                          >
                            <!-- 单选题 -->
                            <template v-if="question.question_type === 0">
                              <el-radio-group v-model="row.beforeForm.answers[index]">
                                <div class="flex flex-col">
                                  <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                                    <el-radio
                                      :label="choiceIndex"
                                      disabled
                                      :style="choice.other_content ? {} : { display: 'inline-block' }"
                                    >
                                      {{ choice.description }}
                                    </el-radio>
                                    <div class="m-l-20 w-350">
                                      <el-input
                                        v-if="choice.other_content"
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        :placeholder="choice.other_content"
                                        disabled
                                      />
                                    </div>
                                  </div>
                                </div>
                              </el-radio-group>
                            </template>

                            <!-- 多选题 -->
                            <template v-if="question.question_type === 1">
                              <el-checkbox-group
                                v-model="row.beforeForm.answers[index]"
                                :min="question.least_choose_count || 0"
                                @change="(val) => handleChange(val, index, row.beforeForm)"
                              >
                                <div class="flex flex-col">
                                  <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                                    <el-checkbox
                                      :label="choiceIndex"
                                      disabled
                                      :style="choice.other_content ? {} : { display: 'inline-block' }"
                                    >
                                      {{ choice.description }}
                                    </el-checkbox>
                                    <div class="m-l-20 w-350">
                                      <el-input
                                        v-if="choice.other_content"
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        :placeholder="choice.other_content"
                                        disabled
                                      />
                                    </div>
                                  </div>
                                </div>
                              </el-checkbox-group>
                            </template>

                            <!-- 评分题 -->
                            <template v-if="question.question_type === 2">
                              <div class="mark-topic-content">
                                <div class="mark-topic-content-item">
                                  <div class="mark-topic-content-item-top">
                                    <div class="point">1</div>
                                    <div class="point">{{ question.top_score }}</div>
                                  </div>
                                  <div class="mark-topic-content-item-bottom">
                                    <div
                                      v-for="item in question.top_score"
                                      :key="item"
                                      :class="['mark', 'selectScore']"
                                    >
                                      {{ item }}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>

                            <!-- 评价题 -->
                            <template v-if="question.question_type === 3">
                              <div
                                v-for="(choice, choiceIndex) in question.choices"
                                :key="choiceIndex"
                                class="evaluate-item"
                              >
                                <span class="evaluate-label">{{ choice.description }}</span>
                                <el-rate
                                  v-model="row.beforeForm.answers[index][choiceIndex]"
                                  :max="question.top_score"
                                  disabled
                                />
                              </div>
                            </template>

                            <!-- 填空题 -->
                            <template v-if="question.question_type === 4">
                              <el-input
                                v-model="row.beforeForm.answers[index]"
                                type="textarea"
                                :autosize="{ minRows: 3 }"
                                placeholder="请输入您的答案"
                                disabled
                              />
                            </template>

                            <!-- 文件上传 -->
                            <template v-if="question.question_type === 6">
                              <el-upload
                                class="upload-demo"
                                drag
                                action="https://jsonplaceholder.typicode.com/posts/"
                                multiple
                                disabled>
                                <i class="el-icon-upload"></i>
                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                <div class="el-upload__tip" slot="tip">只能上传word、excel、pdf、ppt、txt等文件，且不超过2M</div>
                              </el-upload>
                            </template>

                            <!-- 图片上传 -->
                            <template v-if="question.question_type === 5">
                              <el-upload
                                action="https://jsonplaceholder.typicode.com/posts/"
                                list-type="picture-card"
                                disabled
                              >
                                <i class="el-icon-plus"></i>
                              </el-upload>
                            </template>
                          </el-form-item>
                        </div>
                      </div>
                    </el-form>
                  </div>
                  <div class="flex-1 h-100-p overflow-auto">
                    <div class="pos-sticky pos-top-none w-100-p bg-white" style="z-index: 999;">修改后：</div>
                    <el-form ref="previewForm" :model="row.beforeForm" label-position="top">
                      <!-- 问题列表 -->
                      <div class="preview-content">
                        <div v-for="(question, index) in row.context.after" :key="index" class="question-item">
                          <el-form-item
                            :label="`${index + 1}. ${question.caption}`"
                            :prop="`answers.${index}`"
                            :rules="{ required: question.required, message: '此题为必答题', trigger: 'change' }"
                          >
                            <!-- 单选题 -->
                            <template v-if="question.question_type === 0">
                              <el-radio-group v-model="row.afterForm.answers[index]">
                                <div class="flex flex-col">
                                  <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                                    <el-radio
                                      :label="choiceIndex"
                                      disabled
                                      :style="choice.other_content ? {} : { display: 'inline-block' }"
                                    >
                                      {{ choice.description }}
                                    </el-radio>
                                    <div class="m-l-20 w-350">
                                      <el-input
                                        v-if="choice.other_content"
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        :placeholder="choice.other_content"
                                        disabled
                                      />
                                    </div>
                                  </div>
                                </div>
                              </el-radio-group>
                            </template>

                            <!-- 多选题 -->
                            <template v-if="question.question_type === 1">
                              <el-checkbox-group
                                v-model="row.afterForm.answers[index]"
                                :min="question.least_choose_count || 0"
                                @change="(val) => handleChange(val, index, row.afterForm)"
                              >
                                <div class="flex flex-col">
                                  <div v-for="(choice, choiceIndex) in question.choices" :key="choiceIndex">
                                    <el-checkbox
                                      :label="choiceIndex"
                                      disabled
                                      :style="choice.other_content ? {} : { display: 'inline-block' }"
                                    >
                                      {{ choice.description }}
                                    </el-checkbox>
                                    <div class="m-l-20 w-350">
                                      <el-input
                                        v-if="choice.other_content"
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        :placeholder="choice.other_content"
                                        disabled
                                      />
                                    </div>
                                  </div>
                                </div>
                              </el-checkbox-group>
                            </template>

                            <!-- 评分题 -->
                            <template v-if="question.question_type === 2">
                              <div class="mark-topic-content">
                                <div class="mark-topic-content-item">
                                  <div class="mark-topic-content-item-top">
                                    <div class="point">1</div>
                                    <div class="point">{{ question.top_score }}</div>
                                  </div>
                                  <div class="mark-topic-content-item-bottom">
                                    <div
                                      v-for="item in question.top_score"
                                      :key="item"
                                      :class="['mark', 'selectScore']"
                                    >
                                      {{ item }}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </template>

                            <!-- 评价题 -->
                            <template v-if="question.question_type === 3">
                              <div
                                v-for="(choice, choiceIndex) in question.choices"
                                :key="choiceIndex"
                                class="evaluate-item"
                              >
                                <span class="evaluate-label">{{ choice.description }}</span>
                                <el-rate
                                  v-model="row.afterForm.answers[index][choiceIndex]"
                                  :max="question.top_score"
                                  disabled
                                />
                              </div>
                            </template>

                            <!-- 填空题 -->
                            <template v-if="question.question_type === 4">
                              <el-input
                                v-model="row.afterForm.answers[index]"
                                type="textarea"
                                :autosize="{ minRows: 3 }"
                                placeholder="请输入您的答案"
                                disabled
                              />
                            </template>

                            <!-- 文件上传 -->
                            <template v-if="question.question_type === 6">
                              <el-upload
                                class="upload-demo"
                                drag
                                action="https://jsonplaceholder.typicode.com/posts/"
                                multiple
                                disabled>
                                <i class="el-icon-upload"></i>
                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                <div class="el-upload__tip" slot="tip">只能上传word、excel、pdf、ppt、txt等文件，且不超过2M</div>
                              </el-upload>
                            </template>

                            <!-- 图片上传 -->
                            <template v-if="question.question_type === 5">
                              <el-upload
                                action="https://jsonplaceholder.typicode.com/posts/"
                                list-type="picture-card"
                                disabled
                              >
                                <i class="el-icon-plus"></i>
                              </el-upload>
                            </template>
                          </el-form-item>
                        </div>
                      </div>
                    </el-form>
                  </div>
                </div>
                <el-button slot="reference" type="text">详情</el-button>
              </el-popover>
              <div v-else>{{ "--" }}</div>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
  </div>
</template>

<script>
import { debounce, deepClone } from '@/utils'
import dayjs from 'dayjs'

export default {
  props: ['show'],
  data() {
    return {
      isLoading: false,
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '时间',
          value: [dayjs().subtract(1, 'week').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
          pickerOptions: {
            disabledDate(time) {
              const now = Date.now()
              const oneMonthAgo = now - 30 * 24 * 60 * 60 * 1000
              return time.getTime() > now || time.getTime() < oneMonthAgo
            }
          },
          clearable: false
        },
        username: {
          type: 'input',
          label: '被操作账号',
          value: '',
          placeholder: '请输入',
          maxlength: 20
        }
      },
      tableData: [],
      tableSetting: [
        { label: '操作时间', key: 'operation_time' },
        { label: '操作人', key: 'operator' },
        { label: '被操作问卷', key: 'questionnaire_name' },
        { label: '操作', key: 'operation_alias' },
        { label: '操作内容', key: 'context', type: 'slot', slotName: 'context' }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0
    }
  },
  watch: {
    show: {
      handler(newVal) {
        if (newVal) {
          this.getDataList()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    getDataList() {
      this.isLoading = true
      this.$apis.apiBackgroundFundSupervisionPublicityInfoQuestionnaireOperateLogListPost({
        start_time: this.searchFormSetting.select_time.value[0] + ' 00:00:00',
        end_time: this.searchFormSetting.select_time.value[1] + ' 23:59:59',
        username: this.searchFormSetting.username.value || undefined,
        page: this.currentPage,
        page_size: this.pageSize
      }).then(res => {
        this.isLoading = false
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          if (item.operation === "modify") {
            Object.assign(item, {
              afterForm: { answers: [] },
              beforeForm: { answers: [] }
            })
            if (Object.keys(item.context).length) {
              this.initAnswers(item.beforeForm, item.context.before)
              this.initAnswers(item.afterForm, item.context.after)
            }
          }
          console.log("item", item)
          return item
        })
      })
    },

    initAnswers(form, arr) {
      // 使用 Vue.set 来确保响应性
      form.answers = arr.map((question, index) => {
        let answer
        if (question.question_type === 1) { // 多选题返回空数组
          answer = []
        } else if (question.question_type === 3) { // 评价题返回空对象
          answer = {}
          question.choices.forEach((_, choiceIndex) => {
            answer[choiceIndex] = 0
          })
        } else { // 其他题型返回空值
          answer = ''
        }
        return answer
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    }
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$primary-color: #409eff;
$border-color: #ebeef5;
$text-primary: #303133;
$spacing-base: 8px;

@mixin section-padding {
  padding: $spacing-base * 3 $spacing-base * 2.5;
}

@mixin hover-effect {
  transition: all 0.3s ease;
  &:hover {
    border-color: $primary-color;
    box-shadow: 0 0 8px rgba($primary-color, 0.1);
  }
}

// 重置最后一个元素的边距
@mixin reset-last-margin {
  &:last-child {
    margin-bottom: 0;
  }
}
.mark-topic {
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  &-content {
    margin-left: 23px;
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    &-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      border: 1px solid #E7ECF2;
      border-radius: 6px;
      padding: 5px;
      &-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 5px;
        border-bottom: 1px solid #E7ECF2;
        .point {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      &-bottom {
        margin-top: 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .mark {
          width: 32px;
          height: 32px;
          margin: 0 5px;
          border: 1px solid #E7ECF2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .selectScore {
          background-color: #F5F7FA;
          color: #C0C4CC;
        }
      }
    }
  }
}
.evaluate-item {
  flex-direction: column;
  align-items: flex-start;

  .evaluate-label {
    width: 100%;
    margin-bottom: $spacing-base;
  }
}
.preview-content {
  @include section-padding;

  .question-item {
    margin-bottom: $spacing-base * 3;
    padding: $spacing-base * 2.5;
    border: 1px solid $border-color;
    border-radius: 4px;
    @include hover-effect;
    @include reset-last-margin;
  }
}

.flex-items-center {
  align-items: center;
}
.bg-white {
  background-color: #fff;
}
.pos-sticky {
  position: sticky;
}
.pos-top-none {
  top: 0;
}
</style>
