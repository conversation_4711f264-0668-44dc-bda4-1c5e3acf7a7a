<template>
  <!-- 台账提交记录表 -->
  <div class="assignment-sumit-record-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"
      :autoSearch="false" @reset="resetHandler"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" stripe
          header-row-class-name="ps-table-header-row"
          height="calc(100vh - 640px)" :max-height="600">
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operator_username="{ row }">
              {{ getOperatorUsername(row) }}
            </template>
            <template #confirmer_username="{ row }">
              {{ getConfirmerUsername(row) }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-origin" @click="gotoDetail(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount">
        </pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 查看详情 -->
    <!-- <details-drawer
      :visible.sync="isShowDetailDrawer"
      :ledgerSerialNumber="ledgerSerialNumber"
      :ledgerNo="ledgerNo"
      :reviewPerson="reviewPerson"
      :showFooter="false"
      :id="id"
      >
    <template slot="footer">
      <div class="dialog-footer m-t-20 m-l-20">
        <el-button class="ps-cancel-btn" @click="isShowDetailDrawer = false">关 闭</el-button>
      </div>
    </template>
  </details-drawer> -->
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import { SEARCH_SETTING_MORNING_SUMIT_RECORD_LIST, TABLE_HEAD_DATA_SUMIT_RECORD_LIST } from './constants'
// import detailsDrawer from '@/views/merchant/supervision-and-management/baiyun-excel/all-ledger-details-drawer/index.vue'
import * as dayjs from 'dayjs'
export default {
  name: 'AssignmentSumitRecordList',
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_SUMIT_RECORD_LIST), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_SUMIT_RECORD_LIST), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_MORNING_SUMIT_RECORD_LIST), // 查询表单配置
      printType: 'AssignmentSumitRecordList', // 类型
      isShowRecordDialog: false, // 历史记录
      isShowDetailDrawer: false, // 台账详情抽屉
      ledgerSerialNumber: '', // 26张台账表的序号 定义组件时格式:Table + ledgerSerialNumber + 自定义组件名
      ledgerNo: '', // 台账编号
      reviewPerson: '', // 复核人
      id: '' // 台账id
    }
  },
  created() {
    this.initLoad()
    this.getLedgerTypeList()
  },
  components: {
    // detailsDrawer
  },
  mounted() { },
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (key === 'ledger_type_list') {
              params.ledger_type_list = [data[key].value]
            } else if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取提交记录
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize,
        review_status: 'CONFIRMED',
        is_confirmed: true
      }
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerLedgerReviewListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    async getLedgerTypeList() {
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerLedgerTypeListPost({})
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || []
        this.searchFormSetting.ledger_type_list.dataList = deepClone(data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log('handleSelectionChange', val)
      this.selectedIds = val.map(item => item.id)
      this.chooseData = deepClone(val)
    },
    // 查看详情-修改为跳转对应的台账表
    gotoDetail(data) {
      console.log('gotoDetail', data)
      // 根据后端返回的key值进行判断 待完善
      let key = data.ledger_type
      this.id = data.id
      let path = '' // 跳转台账路径
      let date = dayjs(data.create_time).format('YYYY-MM-DD') // 携带日期跳转对应的台账表
      switch (key) {
        // 表4_厨房油烟机清洗
        case "KITCHEN_RANGE_HOOD_CLEAN":
          // this.ledgerSerialNumber = '4'
          // this.ledgerNo = data.ledger_no
          // this.reviewPerson = (this.tabType === 'CONFIRMED' || this.tabType === 'REJECTED') ? this.getConfirmerUsername(data) : ''
          path = '/chufang_chouyou_management_ledger'
          break;
        // case 'KITCHEN_RANGE_HOOD_CLEAN':
        //   this.ledgerSerialNumber = '24'
        //   this.ledgerNo = data.ledger_no
        //   break;
        // 表2_从业人员晨检表
        case 'MORNING_CHECK':
          // this.ledgerSerialNumber = '2'
          // this.ledgerNo = data.ledger_no
          path = '/congye_management_ledger'
          date = dayjs(data.create_time).format('YYYY-MM')
          break;
        // 表8_食品添加剂使用记录
        case 'FOOD_ADDITIVE':
          // this.ledgerSerialNumber = '8'
          // this.ledgerNo = data.ledger_no
          path = '/shipin_tianjiaji_management_ledger'
          break;
        // 表9_过敏原使用记录表
        case 'ALLERGEN_USE_RECORD':
          // this.ledgerSerialNumber = '9'
          // this.ledgerNo = data.ledger_no
          path = '/guominyuan_management_ledger'
          break;
        // 表22_学校食堂每日食品安全检查记录表
        case 'DAILY_FOOD_SAFETY':
          path = '/xuexiao_shipin_anquan_management_ledger'
          break;
        // 表23_学校每周食品安全排查治理报告
        case 'WEEKLY_FOOD_SAFETY_REPORT':
          path = '/xuexiao_meizhou_shipin_anquan_management_ledger'
          break;
        // 表25 - 学校校园食品安全工作管理清单
        case 'SCHOOL_FOOD_SAFETY_MANAGEMENT_CHECKLIST':
          path = '/xuexiao_shitang_pingjia_management_ledger'
          break;
        // 表11 - 巡检记录表
        case 'INSPECTION_RECORD':
          path = '/xuncha_jilu_management_ledger'
          date = dayjs(data.create_time).format('YYYY-MM')
          break;
        // 表13 - 食品留样记录表
        case 'FOOD_RESERVED_SAMPLE':
          path = '/shipin_liuyang_management_ledger'
          break;
        // 表26_集体用餐配送单位配送记录
        case 'COLLECTIVE_DINING_DELIVERY_RECORD':
          path = '/jiti_yongcan_management_ledger'
          break;
        // 表21_从业人员食品安全培训记录表
        case 'EMPLOYEE_FOOD_SAFETY_TRAINING_RECORD':
          path = '/congye_renyuan_peixun_management_ledger'
          break;
        default:
          break;
      }
      this.$router.push({
        path: '/management_ledger' + path,
        query: {
          date: date
        }
      })
      // this.isShowDetailDrawer = true
    },
    // 获取提交操作员
    getOperatorUsername(row) {
      const operatorUsername = row.operator_username || ''
      const operatorMemberName = row.operator_member_name || ''
      return operatorMemberName + (operatorUsername ? `(${operatorUsername})` : '')
    },
    // 获取复核人
    getConfirmerUsername(row) {
      const confirmerUsername = row.confirmer_username || ''
      const confirmerMemberName = row.confirmer_member_name || ''
      return confirmerMemberName + (confirmerUsername ? `(${confirmerUsername})` : '')
    }
  }
}
</script>
<style lang="scss" scoped>
.assignment-sumit-record-list {
  ::v-deep .el-dialog {
    .el-dialog__body {
      padding: 0 20px !important;
    }
  }

  .dialog-content {
    padding: 10px 0;
    text-align: left;

    .dialog-content-title {
      margin-bottom: 15px;
      font-size: 14px;
    }

    .reject-reason {
      text-align: left;

      .label {
        margin-bottom: 8px;
        font-size: 14px;

        &.required::before {
          content: '*';
          color: #F56C6C;
          margin-right: 4px;
        }
      }
    }
  }
}
</style>
