<template>
  <div class="GoodsCategory container-wrapper">
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="clickDeductStockDialog" v-permission="['background_store.goods.deduct_stock']">出库</button-icon>
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_store.goods_stock.goods_stock_list_export']">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :empty-text="isFirstSearch ? '暂无数据，请查询' : ''"
          header-row-class-name="ps-table-header-row"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="80"
            :reserve-selection="true"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <table-column v-for="item in tableSetting" :key="item.key" :col="item"></table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <deduct-stock-dialog
      v-if="deductStockDialogVisible"
      :isshow.sync="deductStockDialogVisible"
      :selectListId="selectListId"
      @confirm="searchHandle"
      @deductStockSuccess="deductStockSuccess"
    />
  </div>
</template>
<script>
import { debounce, to } from '@/utils'
import { STORE_STOCK } from '../../components/constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import DeductStockDialog from './DeductStockDialog.vue' // 导出混入

export default {
  name: 'StoreStock',
  mixins: [exportExcel],
  components: {
    DeductStockDialog
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '序号', key: 'index', type: 'index', width: '80' },
        { label: '名称', key: 'name' },
        { label: '条码', key: 'barcode' },
        { label: '规格', key: 'spec' },
        { label: '单位', key: 'goods_unit_name' },
        { label: '分类', key: 'goods_category_name' },
        { label: '库存', key: 'stock_num' },
        { label: '销售价', key: 'sales_price', type: 'money' },
        { label: '成本价', key: 'cost_price', type: 'money' },
        { label: '供应商', key: 'supplier_name' }
      ],
      searchFormSetting: STORE_STOCK,
      deductStockDialogVisible: false,
      selectListId: [],
      isFirstSearch: false
    }
  },
  created() {
    this.getApiStoreGoodsUnitList()
    this.getGoodsCategoryList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getGoodsStockList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.isFirstSearch = false
        this.getGoodsStockList()
      }
    }, 300),
    async getApiStoreGoodsUnitList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsUnitListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.goods_unit.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getGoodsCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategoryListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.goods_category_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getGoodsStockList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsStockGoodsStockListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        this.isFirstSearch = false
      } else {
        this.$message.error(res.msg)
      }
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    clickDeductStockDialog() {
      if (!this.selectListId.length) return this.$message.error('请选择数据')
      this.deductStockDialogVisible = true
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
      })
    },
    deductStockSuccess() {
      this.$refs.tableData.clearSelection();
      this.searchHandle()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getGoodsStockList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getGoodsStockList()
    },
    gotoExport() {
      const option = {
        type: 'ExportStoreStock',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss" scoped></style>
