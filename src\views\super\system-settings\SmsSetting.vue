<template>
  <div class="sms-container">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper p-b-30" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">服务商设置</div>
      </div>
      <div>
        <el-form>
          <el-form-item label="默认服务商" label-width="130px">
            <el-select class="search-item-w ps-select" popper-class="ps-popper-select" v-model="configData.sms_type"
              placeholder="请选择短信类型" :clearable="true">
              <el-option v-for="(option, i) in smsTypeList" :key="i" :label="option.name" :value="option.value"
                :disabled="option.disabled"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="腾讯云短信" label-width="130px">
            <el-select class="search-item-w ps-select" popper-class="ps-popper-select"
              v-model="configData.tencent_company" placeholder="请选择" :clearable="true" multiple collapse-tags filterable
              @change="handlerSelect($event, 'tencent')">
              <el-option v-for="(option, i) in orgsList" :key="i" :label="option.name" :value="option.company"
                :disabled="option.guoxunDisabled"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="国讯短信" label-width="130px">
            <el-select class="search-item-w ps-select" popper-class="ps-popper-select"
                v-model="configData.guoxun_company" placeholder="请选择" :clearable="true" multiple collapse-tags filterable
                @change="handlerSelect($event, 'guoxun')">
                <el-option v-for="(option, i) in orgsList" :key="i" :label="option.name" :value="option.company"
                  :disabled="option.tencentDisabled"></el-option>
              </el-select>
          </el-form-item>
          <el-form-item label-width="130px">
            <el-button :disabled="isLoading" class="ps-btn" style="width:100px;" type="primary" @click="saveSetting">
              保存
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import { deepClone, to } from '@/utils'

export default {
  data() {
    return {
      isLoading: false, // 加载中
      configData: {
        tencent_company: [],
        sms_type: '',
        guoxun_company: []
      },
      smsTypeList: [{
        name: '腾讯云短信',
        value: 'SmsSender'
      }, {
        name: '国讯短信',
        value: 'GuoXunSmsSender'
      }],
      orgsList: []
    }
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      this.getSmsSetting()
    },
    refreshHandle() {
      this.getSmsSetting()
    },
    // 获取短信设置
    async getSmsSetting() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminSmsInfoTemplateGetSettingsPost()
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        this.getOrganizationList()
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        if (data) {
          this.configData = deepClone(data)
        }
      } else {
        this.$message.error(res.msg)
      }
      this.getOrganizationList()
    },
    // 获取组织名称 只拿第一级组织
    async getOrganizationList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationTreeListPost())
      this.isLoading = false
      if (err) {
        return
      }
      if (res && res.code === 0) {
        var results = res.data || []
        results = this.setSelectChoose(this.configData.tencent_company, 'tencent', results)
        results = this.setSelectChoose(this.configData.guoxun_company, 'guoxun', results)
        this.orgsList = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 保存
    async saveSetting() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminSmsInfoTemplateAddSettingsPost(this.configData)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.msg || '保存失败')
      }
    },
    // 选择组织改变
    handlerSelect(value, type) {
      console.log("handlerSelect", value, type);
      this.orgsList = this.setSelectChoose(value, type, this.orgsList)
      console.log("this.orgsList", this.orgsList);
    },
    // 设置
    setSelectChoose(val, type, list) {
      // 重置一下
      if (Array.isArray(list)) {
        console.log("222222");
        list.forEach(item => {
          this.$set(item, type + 'Disabled', false)
        })
      }
      // 将选择的值设置选中
      if (val && Array.isArray(val) && val.length > 0 && Array.isArray(list)) {
        console.log("1111111");
        val.forEach(item => {
          var id = item
          var findIndex = list.findIndex(item => item.company === id)
          if (findIndex > -1) {
            this.$set(list[findIndex], type + 'Disabled', true)
          }
        })
      }
      return list
    }
  }
}
</script>
<style scoped></style>
