<template>
  <div class="container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon @click="showDrawer('add')" v-permission="['card_service.user_collect.add']">创建链接</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column v-for="(item, i) in tableSetting" :key="item.key + i" :col="item">
            <template #status="{ row }">
              {{ row.status === 'enable' ? '启用中' : '已过期' }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showDrawer('edit', row)" :disabled="row.status !== 'enable'" v-permission="['card_service.user_collect.modify']">编辑</el-button>
              <el-button id="shareButton" type="text" size="small" class="ps-text" :disabled="row.status !== 'enable'" @click="shareHandle(row)" v-permission="['card_service.user_collect.share']">分享</el-button>
              <el-button type="text" size="small" class="ps-text" @click="gotoExport(row.id)" v-permission="['card_service.user_collect.user_collect_list_export']">下载</el-button>
              <el-button type="text" size="small" class="ps-red" @click="deleteHandle(row.id)" v-permission="['card_service.user_collect.delete']">删除</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- table end -->
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="drawerType === 'add' ? '创建链接' : '编辑链接'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="formRef" :rules="formRule" :model="formData" label-width="80px">
            <el-form-item label="标题名称" prop="title">
              <el-input v-model="formData.title" placeholder="不超过15个字" maxlength="15" class="w-220"></el-input>
            </el-form-item>
            <el-form-item label="截止日期" prop="expiration_date">
              <el-date-picker
                v-model="formData.expiration_date"
                type="date"
                placeholder="选择日期"
                :picker-options="pickerOptions">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="信息配置" prop="messageData">
              <span>请选择需要收集的信息，不勾选则不显示，字段与提示语均可配置</span>
              <div class="drawer-content p-20">
                <el-form ref="messageDataRef" :model="formData.messageData" :rules="messageRules" label-width="0px" :inline-message="true">
                  <el-form-item  prop="name.name">
                    <div class="ps-flex-align-c flex-align-c">
                      <el-checkbox v-model="formData.messageData.name.whether" :disabled="true" />
                      <el-input size="mini" v-model="formData.messageData.name.name" class="w-110 m-l-10 m-r-20" maxlength="6" placeholder="姓名"></el-input>
                      <div class="m-r-10">提示语</div>
                      <el-input size="mini" v-model="formData.messageData.name.prompt" class="w-220 m-r-20" maxlength="12" placeholder="请输入您的姓名"></el-input>
                      <div class="m-r-10">是否必填</div>
                      <el-switch v-model="formData.messageData.name.check" :disabled="true"></el-switch>
                    </div>
                  </el-form-item>
                  <el-form-item  prop="person_no.person_no">
                    <div class="ps-flex-align-c flex-align-c">
                      <el-checkbox v-model="formData.messageData.person_no.whether" :disabled="true" />
                      <el-input size="mini" v-model="formData.messageData.person_no.person_no" class="w-110 m-l-10 m-r-20" maxlength="6" placeholder="人员编号"></el-input>
                      <div class="m-r-10">提示语</div>
                      <el-input size="mini" v-model="formData.messageData.person_no.prompt" class="w-220 m-r-20" maxlength="12" placeholder="请输入您的人员编号"></el-input>
                      <div class="m-r-10">是否必填</div>
                      <el-switch v-model="formData.messageData.person_no.check" :disabled="true"></el-switch>
                    </div>
                  </el-form-item>
                  <el-form-item
                    prop="phone.phone">
                    <div class="ps-flex-align-c flex-align-c">
                      <el-checkbox v-model="formData.messageData.phone.whether" @change="formData.messageData.phone.check = false" />
                      <el-input size="mini" v-model="formData.messageData.phone.phone" class="w-110 m-l-10 m-r-20" maxlength="6" placeholder="手机号"></el-input>
                      <div class="m-r-10">提示语</div>
                      <el-input size="mini" v-model="formData.messageData.phone.prompt" class="w-220 m-r-20" maxlength="12" placeholder="请输入您的手机号"></el-input>
                      <div class="m-r-10">是否必填</div>
                      <el-switch v-model="formData.messageData.phone.check" :disabled="!formData.messageData.phone.whether"></el-switch>
                    </div>
                  </el-form-item>
                  <el-form-item
                    prop="card_user_group_name.card_user_group_name">
                    <div class="ps-flex-align-c flex-align-c">
                      <el-checkbox v-model="formData.messageData.card_user_group_name.whether" @change="formData.messageData.card_user_group_name.check = false" />
                      <el-input size="mini" v-model="formData.messageData.card_user_group_name.card_user_group_name" class="w-110 m-l-10 m-r-20" maxlength="6" placeholder="分组"></el-input>
                      <div class="m-r-10">提示语</div>
                      <el-input size="mini" v-model="formData.messageData.card_user_group_name.prompt" class="w-220 m-r-20" maxlength="12" placeholder="请输入您所在的分组"></el-input>
                      <div class="m-r-10">是否必填</div>
                      <el-switch v-model="formData.messageData.card_user_group_name.check" :disabled="!formData.messageData.card_user_group_name.whether"></el-switch>
                    </div>
                  </el-form-item>
                  <el-form-item
                    prop="card_department_group_name.card_department_group_name">
                    <div class="ps-flex-align-c flex-align-c">
                      <el-checkbox v-model="formData.messageData.card_department_group_name.whether" @change="formData.messageData.card_department_group_name.check = false" />
                      <el-input size="mini" v-model="formData.messageData.card_department_group_name.card_department_group_name" class="w-110 m-l-10 m-r-20" maxlength="6" placeholder="部门"></el-input>
                      <div class="m-r-10">提示语</div>
                      <el-input size="mini" v-model="formData.messageData.card_department_group_name.prompt" class="w-220 m-r-20" maxlength="12" placeholder="请输入您所在的部门"></el-input>
                      <div class="m-r-10">是否必填</div>
                      <el-switch v-model="formData.messageData.card_department_group_name.check" :disabled="!formData.messageData.card_department_group_name.whether"></el-switch>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/utils';
import dayjs from 'dayjs';
import exportExcel from '@/mixins/exportExcel' // 导出混入
import Clipboard from 'clipboard'
import { mapGetters } from 'vuex';
export default {
  mixins: [exportExcel],
  data() {
    const checkPhone = (rule, value, callback) => {
      if (this.formData.messageData.phone.whether) {
        if (!value) {
          callback(new Error('此项不能为空'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    const checkDepartment = (rule, value, callback) => {
      if (this.formData.messageData.card_department_group_name.whether) {
        if (!value) {
          callback(new Error('此项不能为空'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    const checkGroup = (rule, value, callback) => {
      if (this.formData.messageData.card_user_group_name.whether) {
        if (!value) {
          callback(new Error('此项不能为空'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '标题', key: 'title' },
        { label: '已收集人数', key: 'collect_num' },
        { label: '创建时间', key: 'create_time' },
        { label: '截止日期', key: 'expiration_date' },
        { label: '状态', key: 'status_alias', type: 'slot', slotName: 'status' },
        { label: '发布人', key: 'account_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      drawerType: '',

      drawerShow: false,
      defaultFormData: {
        title: '',
        expiration_date: '',
        messageData: {
          name: {
            whether: true,
            name: '姓名',
            prompt: '请输入您的姓名',
            check: true
          },
          person_no: {
            whether: true,
            person_no: '人员编号',
            prompt: '请输入您的人员编号',
            check: true
          },
          phone: {
            whether: false,
            phone: '手机号',
            prompt: '请输入您的手机号',
            check: false
          },
          card_department_group_name: {
            whether: false,
            card_department_group_name: '部门',
            prompt: '请输入您所在的部门',
            check: false
          },
          card_user_group_name: {
            whether: false,
            card_user_group_name: '分组',
            prompt: '请输入您所在的分组',
            check: false
          }
        }
      },
      formData: {
        title: '',
        expiration_date: '',
        messageData: {
          name: {
            whether: true,
            name: '姓名',
            prompt: '请输入您的姓名',
            check: true
          },
          person_no: {
            whether: true,
            person_no: '人员编号',
            prompt: '请输入您的人员编号',
            check: true
          },
          phone: {
            whether: false,
            phone: '手机号',
            prompt: '请输入您的手机号',
            check: false
          },
          card_department_group_name: {
            whether: false,
            card_department_group_name: '部门',
            prompt: '请输入您所在的部门',
            check: false
          },
          card_user_group_name: {
            whether: false,
            card_user_group_name: '分组',
            prompt: '请输入您所在的分组',
            check: false
          }
        }
      },
      phoneRequired: false,
      departmentRequired: false,
      groupRequired: false,
      formRule: {
        title: [{ required: true, message: '请输入标题名称', trigger: ['blur', 'change'] }],
        expiration_date: [{ required: true, message: '请输入截止日期', trigger: ['blur', 'change'] }]
      },
      messageRules: {
        "name.name": [{ required: true, message: '此项不能为空', trigger: ['blur', 'change'] }],
        "person_no.person_no": [{ required: true, message: '此项不能为空', trigger: ['blur', 'change'] }],
        "phone.phone": [{ validator: checkPhone, trigger: ['blur', 'change'] }],
        "card_department_group_name.card_department_group_name": [{ validator: checkDepartment, trigger: ['blur', 'change'] }],
        "card_user_group_name.card_user_group_name": [{ validator: checkGroup, trigger: ['blur', 'change'] }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      editId: ''
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 刷新页面
    refreshHandle() {
      this.getDataList()
    },
    showDrawer(type, data) {
      this.formData = deepClone(this.defaultFormData)
      this.drawerType = type
      this.drawerShow = true
      if (type === 'edit') {
        this.editId = data.id || ''
        this.formData.title = data.title
        this.formData.expiration_date = data.expiration_date
        if (data.input_json.length) {
          data.input_json.forEach(item => {
            for (let key in item) {
              for (let key1 in this.formData.messageData) {
                if (key === key1) {
                  this.formData.messageData[key1] = deepClone(item)
                }
              }
            }
          })
        }
      }
      this.$nextTick(function() {
        this.$refs.formRef.clearValidate()
      })
    },
    getDataList() {
      this.isLoading = true
      this.$apis.apiCardServiceUserCollectListPost().then(res => {
        if (res.code === 0) {
          this.tableData = res.data.results || []
        } else {
          this.$message.error(res.msg)
        }
        this.isLoading = false
      })
    },
    saveHandle() {
      this.$refs.messageDataRef.validate((valid) => {
        if (valid) {
          this.$refs.formRef.validate((valid1) => {
            if (valid1) {
              let params = {
                title: this.formData.title,
                expiration_date: dayjs(this.formData.expiration_date).format('YYYY-MM-DD'),
                input_json: [],
                id: this.drawerType === 'add' ? undefined : this.editId
              }
              // 循环push进数组
              for (let key in this.formData.messageData) {
                params.input_json.push(this.formData.messageData[key])
              }
              if (this.drawerType === 'add') {
                this.addData(params)
              } else {
                this.modifyData(params)
              }
              this.drawerShow = false
            } else {
              this.$message.error('请检查内容是否正确')
            }
          })
        } else {
          this.$message.error('请检查内容是否正确')
        }
      })
    },
    modifyData(params) {
      this.$apis.apiCardServiceUserCollectModifyPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('保存成功')
          this.$refs.formRef.resetFields()
          this.getDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    addData(params) {
      this.$apis.apiCardServiceUserCollectAddPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('保存成功')
          this.$refs.formRef.resetFields()
          this.getDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    cancelHandle() {
      this.$refs.formRef.resetFields()
      this.drawerShow = false
    },
    deleteHandle(id) {
      this.$confirm('删除后不可恢复, 是否要删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$apis.apiCardServiceUserCollectDeletePost({
          id: id
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.getDataList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    gotoExport(id) {
      const option = {
        url: "apiCardServiceUserCollectUserCollectListExportPost",
        params: {
          id: id
        }
      }
      this.exportHandle(option)
    },
    async shareHandle(row) {
      let companyId = this.userInfo.company_id
      this.clipboard = new Clipboard('#shareButton', {
        text: function() {
          return `${process.env.VUE_APP_MINAPP}/pages_info/user_config/user_information_collection/user_info_collection?id=${row.id}&company_id=${companyId}`
        }
      })

      this.clipboard.on('success', e => {
        this.$message.success('链接已复制，请分享给用户进行信息收集。')
        e.clearSelection()
      })

      this.clipboard.on('error', e => {
        this.$message.error('无法复制文本: ', e);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-content {
  background-color: #f8f9fa;
  border-radius: 8px;
}
</style>
