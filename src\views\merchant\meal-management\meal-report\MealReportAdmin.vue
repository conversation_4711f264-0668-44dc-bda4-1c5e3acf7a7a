<template>
  <div class="meal-declaration container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addOrEditMeal('add')" v-permission="['background_report_meal.report_meal_settings.add']">新增报餐</button-icon>
          <button-icon color="plain" type="del" @click="mulOperation('mulDel')" v-permission="['background_report_meal.report_meal_settings.delete']">批量删除</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
        >
          <el-table-column type="selection" width="50" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column prop="name" label="规则名称" width="160"></el-table-column>
          <el-table-column prop="report_card_groups_alias" label="适用分组">
            <template slot-scope="scope">
              <el-tag
                v-for="item in scope.row.report_card_groups_alias"
                style="margin: 0 8px 8px 0"
                :key="item"
              >
                {{ item }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="report_organization_alias" label="适用消费点">
            <template slot-scope="scope">
              <el-tag
                v-for="item in scope.row.report_organization_alias"
                style="margin: 0 8px 8px 0"
                :key="item"
              >
                {{ item }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="meal_type_detail.meal_type_verbose" label="适用餐段">
            <template slot-scope="scope">
              <el-tag
                v-for="item in scope.row.meal_type_detail.meal_type_verbose"
                style="margin: 0 8px 8px 0"
                :key="item"
              >
                {{ item }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="160"></el-table-column>
          <el-table-column prop="is_open" label="状态">
            <template slot-scope="scope">
              <el-switch
                @change="isOpenSetting(scope.row)"
                v-model="scope.row.is_open"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
                :disabled="!allPermissions.includes('background_report_meal.report_meal_settings.modify_open_status')"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-button type="text" size="small" class="ps-text" @click="addOrEditMeal('edit', scope.row)" v-permission="['background_report_meal.report_meal_settings.modify']">编辑</el-button>
              <el-button type="text" size="small" @click="mulOperation('del', scope.row)" v-permission="['background_report_meal.report_meal_settings.delete']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 抽屉弹窗 -->
    <add-meal-report
      :show.sync="showDrawer"
      :infoData="drawerInfoData"
      :type="drawerType"
      :size="560"
      @confirm="confirmHandle"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'
import { mapGetters } from 'vuex'
import AddMealReport from './component/AddMealReport'

export default {
  components: { AddMealReport },
  name: 'MealDeclaration',
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        report_organizations: {
          type: 'organizationSelect',
          label: '组织',
          value: [],
          placeholder: '请选择',
          multiple: true, // 是否多选
          collapseTags: true, // 折叠
          checkStrictly: true,
          dataList: []
        },
        report_card_groups: {
          type: 'groupSelect',
          label: '适用分组',
          value: [],
          placeholder: '请选择',
          multiple: true
        }
      },
      selectListId: [],
      showDrawer: false,
      drawerInfoData: {},
      drawerType: ''
    }
  },
  created() {
    this.initLoad()
  },
  computed: {
    ...mapGetters(['allPermissions'])
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMealReportList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getMealReportList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getMealReportList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportMealReportMealSettingsListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getMealReportList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMealReportList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => { this.selectListId.push(item.id) })
    },
    // 开关事件
    switchFn(e) {
      console.log(e)
    },
    // 各种操作 type表示类型，text是弹窗文字，data是单个操作的时候带过去的数据
    mulOperation(type, data) {
      if (!data && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let title = '提示'
      let content = ''
      switch (type) {
        case "del":
          content = '确定删除该报餐设置？'
          break;
        case "mulDel":
          content = '确定删除所选报餐设置？'
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            switch (type) {
              case "del": // 删除
                this.deleteSetting([data.id])
                break;
              case "mulDel": // 批量删除
                this.deleteSetting(this.selectListId)
                break;
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 删除事件
    async deleteSetting(ids) {
      const res = await this.$apis.apiBackgroundReportMealReportMealSettingsDeletePost({
        ids
      })
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getMealReportList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 开启和关闭报餐设置
    async isOpenSetting(data) {
      let params = {
        id: data.id,
        is_open: data.is_open
      }
      const res = await this.$apis.apiBackgroundReportMealReportMealSettingsModifyOpenStatusPost(params)
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getMealReportList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增or编辑
    addOrEditMeal(type, data) {
      this.showDrawer = true
      this.drawerInfoData = data
      this.drawerType = type
    },
    confirmHandle() {
      this.showDrawer = false
      this.currentPage = 1
      this.getMealReportList()
    }
  }
}
</script>

<style lang="scss" scoped>
// .meal-declaration {
// }
</style>
