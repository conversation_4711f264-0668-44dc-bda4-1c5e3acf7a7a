<template>
  <div class="GoodsInfo container-wrapper">
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="clickDeviceGoodsWarehousingDialog" v-permission="['background_store.goods.add_stock']">
            商品入库
          </button-icon>
          <button-icon color="origin" type="add" @click="addOrEditGoods('add')" v-permission="['background_store.goods.add']">新增</button-icon>
          <button-icon color="origin" type="add" @click="batchAddGoodsDialogVisible = true" v-permission="['background_store.goods.batch_add']">
            批量新增
          </button-icon>
          <button-icon color="plain" class="but-import" @click="importGoodsDialog = true" v-permission="['background_store.goods.batch_import_goods']">
            导入新增
          </button-icon>
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_store.goods.goods_list_export']">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          row-key="id"
          :empty-text="isFirstSearch ? '暂无数据，请查询' : ''"
          header-row-class-name="ps-table-header-row"
          default-expand-all
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          class="table-wrapper"
        >
          <table-column v-for="item in tableSetting" :key="item.key" :col="item">
            <template #goods_image="{ row }">
              <el-image
                style="width: 100px; height: 100px"
                :src="
                  row.goods_image
                    ? row.goods_image
                    : 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'
                "
                fit="cover"
                :preview-src-list="[row.goods_image?row.goods_image:'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png']"
              ></el-image>
            </template>
            <template #sale_status="{ row }">
              <el-switch
                v-model="row.sale_status"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
                :active-value="1"
                :inactive-value="0"
                @change="changeSaleStatus(row)"
                :disabled="!allPermissions.includes('background_store.goods.sale_status_modify')"
              ></el-switch>
            </template>
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                v-if="row.barcode_type === '0'"
                @click="addOrEditGoods('modify', row)"
                v-permission="['background_store.goods.modify']"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                :disabled="row.stock_num > 0"
                class="ps-red"
                @click="clickDelete(row)"
                v-permission="['background_store.goods.delete']"
              >
                删除
              </el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <goods-warehousing-dialog
      v-if="deviceGoodsWarehousingDialogVisible"
      :isshow.sync="deviceGoodsWarehousingDialogVisible"
      @determineStock="determineStock"
    />
    <stock-data-success-dialog
      v-if="stockDataSuccessDialogVisible"
      :isshow.sync="stockDataSuccessDialogVisible"
      @determineStock="determineStock"
      :stockListData="stockListData"
    />
    <add-goods-dialog
      v-if="deviceGoodsVisible"
      :isshow.sync="deviceGoodsVisible"
      :dialogTitle="deviceGoodsDialogTitle"
      :dialogModifyId="dialogModifyId"
      :type="type"
      @confirm="searchHandle"
    />
    <!-- 批量新增 -->
    <batch-add-goods-dialog
      v-if="batchAddGoodsDialogVisible"
      :isshow.sync="batchAddGoodsDialogVisible"
      :goodsCategoryList="goodsCategoryList"
      :supplierList="supplierList"
      :unitList="unitList"
      @confirm="searchHandle"
    />
    <!-- 导入新增 -->
    <el-dialog
      title="导入新增"
      :visible.sync="importGoodsDialog"
      width="700px"
      top="10vh"
      custom-class="ps-dialog"
      show-close
      :close-on-click-modal="false"
      v-if="importGoodsDialog"
    >
    <div>
      <el-form class="import-face-form-wrapper" label-width="140px">
      <el-form-item label="商品导入模版">
        <!-- location.origin + '/api/temporary/template_excel/account_face_import.zip' -->
        <el-link
          type="primary"
          :href="templateUrl"
        >
          点击下载
        </el-link>
      </el-form-item>
      <el-form-item label="上传商品">
        <file-upload
          drag
          :limit="1"
          @fileLists="getSuccessUploadRes"
          :before-upload="beforeUpload"
          prefix="goods_url_zip"
          :on-remove="remove"
        >
          <div class="">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
          </div>
          <div class="el-upload__tip" slot="tip">只能上传zip文件</div>
        </file-upload>
      </el-form-item>
    </el-form>
    </div>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="importGoodsDialog = false">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          @click="importGoodsDetermine"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { debounce, to } from '@/utils'
import { STORE_GOODS_ADMIN_INFO } from '../../../components/constants'
import GoodsWarehousingDialog from './GoodsWarehousingDialog.vue'
import StockDataSuccessDialog from './StockDataSuccessDialog.vue'
import BatchAddGoodsDialog from './BatchAddGoodsDialog.vue'
import { mapGetters } from 'vuex'
import AddGoodsDialog from './AddGoodsDialog.vue'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'goodsInfo',
  mixins: [exportExcel],
  components: {
    GoodsWarehousingDialog,
    StockDataSuccessDialog,
    AddGoodsDialog,
    BatchAddGoodsDialog
  },
  computed: {
    ...mapGetters(['appPermissions'])
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      totalPageSize: 0,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '序号', type: 'index', width: '80' },
        { label: '商品名称', key: 'name' },
        {
          label: '图片',
          key: 'goods_image',
          type: 'slot',
          slotName: 'goods_image',
          width: '160'
        },
        { label: '条码', key: 'barcode' },
        { label: '规格', key: 'spec' },
        { label: '分类', key: 'goods_category_name' },
        { label: '供应商', key: 'supplier_name' },
        { label: '库存', key: 'stock_num' },
        { label: '单位', key: 'goods_unit_name' },
        { label: '销售价', key: 'sales_price', type: 'money' },
        { label: '成本价', key: 'cost_price', type: 'money' },
        { label: '创建时间', key: 'create_time' },
        {
          label: '上下架',
          key: 'sale_status',
          type: 'slot',
          slotName: 'sale_status'
        },
        {
          label: '操作',
          key: 'operation',
          type: 'slot',
          slotName: 'operation',
          fixed: 'right',
          width: '220'
        }
      ],
      searchFormSetting: STORE_GOODS_ADMIN_INFO,
      deviceGoodsWarehousingDialogVisible: false, // 商品入库弹框
      stockDataSuccessDialogVisible: false,
      stockListData: [], // 商品成功之后的
      deviceGoodsVisible: false, // 新建商品
      type: '', // 打开弹窗的类型
      deviceGoodsDialogTitle: '',
      dialogModifyId: -1, // 修改打开弹窗的id
      goodsCategoryList: [],
      supplierList: [],
      unitList: [],
      batchAddGoodsDialogVisible: false, // 批量新建商品
      isFirstSearch: false,
      importGoodsDialog: false,
      goodsUploadUrl: "",
      templateUrl: location.origin + '/api/temporary/template_excel/food_stock/导入商品模板.zip'
    }
  },
  created() {
    this.getFoodIngredientSupplierList()
    this.getGoodsCategoryList()
    this.getApiStoreGoodsUnitList()
    this.getGoodsUnitList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getGoodsList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.isFirstSearch = false
        this.getGoodsList()
      }
    }, 300),
    // 单位
    async getGoodsUnitList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsUnitListPost({
          page: 1,
          page_size: 99999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.unitList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getFoodIngredientSupplierList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundFoodIngredientSupplierListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        // 传给弹窗
        this.supplierList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getGoodsCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategoryListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.goods_category_ids.dataList = res.data.results
        // 传给弹窗
        this.goodsCategoryList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getApiStoreGoodsUnitList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsUnitListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.goods_unit.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改上下架状态
    async setStatusModify(params) {
      const [err, res] = await to(this.$apis.apiBackgroundStoreGoodsSaleStatusModifyPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.getGoodsList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async getGoodsList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.tree_list
        this.totalPageSize = this.$computedTotalPageSize(this.totalCount, this.pageSize)
        this.isFirstSearch = false
      } else {
        this.$message.error(res.msg)
      }
    },
    hasChildren(row) {
      console.log(row)
      // return row.children && row.children.length > 0
    },
    changeSaleStatus(row) {
      let params = {
        id: row.id,
        sale_status: row.sale_status
      }
      this.setStatusModify(params)
    },
    clickDelete(row) {
      this.$confirm('确定删除该条商品信息？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundStoreGoodsDeletePost({
                id: row.id
              })
            )
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                } else if (this.currentPage === this.totalPageSize) {
                  this.currentPage--
                }
              }
              this.getGoodsList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    clickDeviceGoodsWarehousingDialog() {
      this.deviceGoodsWarehousingDialogVisible = true
    },
    addOrEditGoods(type, row) {
      this.type = type
      this.deviceGoodsDialogTitle = '新增商品'
      if (type === 'modify') {
        this.dialogModifyId = row.id
        this.deviceGoodsDialogTitle = '编辑商品'
        console.log(row)
      }
      this.deviceGoodsVisible = true
    },
    // 商品入库
    determineStock(data) {
      this.stockListData = data.map((v, index) => {
        v.index = index + 1
        return v
      })
      this.stockDataSuccessDialogVisible = true
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getGoodsList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getGoodsList()
    },
    gotoExport() {
      const option = {
        type: 'ExportGoodsInfo',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    beforeUpload(file) {
      let reg = /application\/\S*zip\S*/
      if (!reg.test(file.type)) {
        this.$message.error('请上传后缀名为.zip的压缩包文件')
        return false
      }
    },
    remove() {
      this.goodsUploadUrl = ''
    },
    getSuccessUploadRes(res) {
      console.log(res, 'reds')
      this.goodsUploadUrl = res[0].url
    },
    importGoodsDetermine() {
      if (!this.goodsUploadUrl) {
        this.$message.error('压缩包还没上传完毕或未上传')
        return
      }
      const option = {
        type: 'importGoods',
        message: '确定导入商品？',
        url: 'apiBackgroundStoreGoodsBatchImportGoodsPost',
        params: {
          zip_url: this.goodsUploadUrl
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss" scoped>
.GoodsInfo {

  .but-import {
    ::v-deep .ps-plain-btn {
      border-color: #ff9b45;
    }
  }
 ::v-deep .table-wrapper {
    .el-table__cell .cell {
      display: flex;
      align-items: center;
      justify-content: center;
      .el-table__expand-icon {
        font-size: 18px;
      }
    }
  }
}

</style>
