<template>
  <div class="SurveyDetail container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="export" @click="surveyDataTotal()" >汇总数据</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
          <el-table-column prop="create_time" label="提交时间" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="openDialog(scope.row)"
                >查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <el-dialog
      title="查看"
      :visible.sync="dialogVisible"
      width="400px"
      top="30vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false">
      <div v-for="(item, index) in detailData" :key="item.survey_question_id" class="m-b-25">
        <div class="question-name m-b-10">{{index+1}}、{{item.question_content}}</div>
        <div class="m-l-22" v-if="item.type === 'CHOICE'">
          <div
            v-for="(optItem, optIndex) in item.question_options"
            :key="optIndex"
            :class="['m-b-5', item.choice.indexOf(optItem.options) === -1 ? '' : 'activeChoice']"
          >
            {{optItem.options}}、{{optItem.content}}
          </div>
        </div>
        <div class="m-l-22" v-if="item.type === 'ANSWER'">
          {{item.content}}
        </div>
        <div class="m-l-22" v-if="item.type === 'SCORE'">
          {{item.score}}分
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-btn" type="primary" @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from '@/utils'
export default {
  name: 'MerchantSurveyDetail',
  data() {
    return {
      isLoading: false,
      surveyInfoId: '',
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: {
        name: {
          type: 'input',
          label: '姓名',
          value: '',
          maxlength: 20,
          placeholder: '请输入姓名'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          maxlength: 20,
          placeholder: '请输入人员编号'
        }
      },
      detailData: [],
      dialogVisible: false
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.surveyInfoId = this.$route.query.id
      this.getSurveyList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getSurveyList()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getSurveyList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundMarketingSurveyInfoFeedbackPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize,
        survey_info_id: this.surveyInfoId
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results.map(item => {
          item.rules_status = !(item.status === 'disable')
          return item
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSurveyList()
    },
    openDialog(data) {
      this.detailData = data.survey_answers
      this.dialogVisible = true
    },
    // 汇总数据
    surveyDataTotal() {
      this.$router.push({
        name: 'MerchantSurveyDataTotal',
        query: {
          id: this.surveyInfoId,
          num: this.totalCount
        }
      })
    }
  }
}
</script>
<style lang="scss">
.SurveyDetail{
  .activeChoice{
    color: #ff9b45 ;
  }
}
</style>
