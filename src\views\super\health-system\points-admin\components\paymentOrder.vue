<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <search-form
        ref="searchRef"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <!-- <div class="align-r"> -->
          <!-- <button-icon color="plain" type="export" @click="gotoExport">导出Excel</button-icon> -->
          <!-- </div> -->
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <el-table
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
            class="ps-table-tree"
            v-loading="isLoading"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item">
              <template #commodityType="{ row }">
                <div>
                  <div>{{ row.commodity_type_alias }}</div>
                  <div v-if="row.commodity_type === 'virtual'">
                    <span>{{ row.virtual_commodity_type_alias }}</span>
                    <span
                      v-if="row.virtual_commodity_type === 'ai_nutritionist' && row.commodity_extra"
                    >
                      {{ row.commodity_extra.count }}次
                    </span>
                    <span v-if="row.virtual_commodity_type === 'member' && row.commodity_extra">
                      {{ row.commodity_extra.day }}天
                    </span>
                  </div>
                  <div v-if="row.commodity_type === 'physical'">
                    <span>商品编码：{{ row.physical_code }}</span>
                  </div>
                </div>
              </template>
              <template #operation="{ row }">
                <el-button type="text" size="small" class="ps-text" @click="clickOrderDetails(row)">
                  详情
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  :disabled="row.commodity_type === 'virtual' || row.can_refund"
                  @click="clickRefund(row)"
                >
                  退款
                </el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <refund-drawer
      v-if="refundDrawerVisible"
      :isshow.sync="refundDrawerVisible"
      :drawerModifyData="drawerModifyData"
      @clickSaveDrawer="clickSaveDrawer"
    ></refund-drawer>
    <order-details-drawer
      v-if="orderDetailsDrawerVisible"
      :isshow.sync="orderDetailsDrawerVisible"
      :tradeNo="drawerModifyData.trade_no"
    ></order-details-drawer>
  </div>
</template>

<script>
import { to, deepClone, debounce } from '@/utils'
import { PAYMENT_ORDER } from './constantsConfig.js'
import RefundDrawer from './refundDrawer'
import OrderDetailsDrawer from './orderDetailsDrawer'
import report from '@/mixins/report' // 混入
export default {
  name: 'PaymentOrder',
  components: { RefundDrawer, OrderDetailsDrawer },
  mixins: [report],
  data() {
    return {
      isLoading: false,
      tableSettings: [
        { label: '订单号', key: 'trade_no' },
        { label: '商品名称', key: 'commodity_name' },
        {
          label: '商品类型',
          key: 'commodity_type_alias',
          type: 'slot',
          slotName: 'commodityType',
          width: '150px'
        },
        { label: '购买数量', key: 'num' },
        { label: '兑换积分', key: 'points' },
        { label: '支付金额（元）', key: 'origin_fee', type: 'money' },
        { label: '创建时间', key: 'create_time' },
        { label: '支付时间', key: 'pay_time' },
        { label: '订单状态', key: 'order_status_alias' },
        { label: '用户名称', key: 'user_name' },
        { label: '手机号', key: 'user_phone' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      total: 0,
      searchFormSetting: deepClone(PAYMENT_ORDER),
      collect: [
        // 统计
        { key: 'total_origin_fee', value: 0, label: '总金额:', type: 'money' },
        { key: 'total_points', value: '', label: '总积分:' }
      ],
      refundDrawerVisible: false,
      orderDetailsDrawerVisible: false,
      drawerModifyData: {} // 编辑的数据
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getPointsOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getPointsOrderList()
    }, 300),
    // 获取列表数据
    async getPointsOrderList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMemberPointsPointsOrderListPost({
          page: this.currentPage,
          page_size: this.pageSize,
          ...this.formatQueryParams(this.searchFormSetting)
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.total = res.data.count
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    clickOrderDetails(row) {
      this.orderDetailsDrawerVisible = true
      this.drawerModifyData = deepClone(row)
    },
    clickRefund(row) {
      this.refundDrawerVisible = true
      this.drawerModifyData = deepClone(row)
    },
    clickSaveDrawer() {
      this.getPointsOrderList()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getPointsOrderList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPointsOrderList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>
<style lang="scss" scoped></style>
