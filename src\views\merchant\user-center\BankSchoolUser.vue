<template>
  <div class="bank-school container-wrapper">
    <!--车辆绑定 -->
    <!--头部-->
    <refresh-tool @refreshPage="refreshHandler(true)" />
    <!--筛选 -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler" @reset="resetHandler"
      :label-width="labelWidth"></search-form>
    <!--数据列表 -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="handlerCarImport" v-permission="['background_ebank.abc_school_user.bind_abc_school_user_import']">导入绑定</button-icon>
          <button-icon color="origin" @click="handlerPeopleStatus">更新人员状态</button-icon>
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_ebank.abc_school_user.list_export']">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" height="460px" stripe
          header-row-class-name="ps-table-header-row" class="table-fixed">
          <el-table-column label="序号" align="center" header-align="center" width="80px">
            <template slot-scope="scope">
              {{ (scope.$index + 1) + (currentPage - 1) * pageSize }}
            </template>
          </el-table-column>
          <table-column v-for="(item, index) in tableSettings" :key="index" :col="item">
            <template #operationType="{ row }">
              {{ row.operation_type | operationTypeName }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text-blue" @click="handlerEdit(row)" v-permission="['background_ebank.abc_school_user.bind_abc_school_user']">绑定人员编号</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- 统计 end -->
      <!-- 分页 start -->
      <pagination @size-change="handleSizeChange" @current-change="onPaginationChange" :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount"></pagination>
      <!-- 分页 end -->
    </div>
    <!--导入弹窗-->
    <import-dialog :templateUrl="templateUrl" :tableSetting="tableSettingImport" :show.sync="isShowImportDialog"
      title="导入绑定" :openExcelType="openExcelType">
    </import-dialog>
    <!--编辑弹窗-->
    <el-dialog :title="dialogTitle" :visible.sync="isShowEditDialog" width="450px" custom-class="ps-dialog"
      :close-on-click-modal="false" :close-on-press-escape="false" show-close>
      <el-form ref="dialogForm" label-width="100px">
        <el-form-item label="人员编号" prop='person_no'>
          <el-select v-model="dialogEditData.person_no" filterable remote reserve-keyword placeholder="请输入人员编号"
            :remote-method="handlerInputChange" :loading="isLoadingPerson" @change="handlerSelectChange">
            <el-option v-for="item in personList" :key="item.person_no" :label="item.person_no" :value="item.person_no">
              <span style="float: left">{{ item.person_no }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="false">
          <div class="dialog-person-info" v-loading="isLoadingPerson">
            <div class="m-l-20">
              姓名：{{ dialogEditData.name }}
            </div>
            <div class="m-l-20">
              手机号：{{ dialogEditData.phone }}
            </div>
          </div>
        </el-form-item>
      </el-form>
      <!-- 弹窗底部的按钮 -->
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="isShowEditDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="submitEditDialog" v-loading="isBindLoading">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { SEARCH_FORM_SET_DATA_BANK_SCHOOL, TABLE_HEAD_DATA_BANK_SCHOOL, TABLE_HEAD_DATA_IMPORT_SCHOOL, URL_TEMPLATE_MODEL } from './constants/bankSchoolConstants'
import { deepClone, to, debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: "BankSchollUser",
  data() {
    return {
      isLoading: false, // 刷新数据
      labelWidth: '120px', // 筛选label 宽度
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSettings: deepClone(TABLE_HEAD_DATA_BANK_SCHOOL), // 表格头部设置内容
      searchFormSetting: deepClone(SEARCH_FORM_SET_DATA_BANK_SCHOOL), // 搜索设置内容
      dialogTitle: '绑定人员编号', // 弹窗标题
      isShowEditDialog: false, // 是否显示编辑dialog
      isShowImportDialog: false, // 是否显示导入dialog
      templateUrl: '', // 导入模板的链接 to do debug
      tableSettingImport: deepClone(TABLE_HEAD_DATA_IMPORT_SCHOOL), // 导入表格头部设置
      dialogEditData: {}, // 编辑弹窗数据存放
      isLoadingPerson: false, // 是否显示loading
      isBindLoading: false, // 是否显示确认键loading
      openExcelType: 'AbcSchoolUserImport', // 是否显示确认键loading
      isHasPersonInfo: false, // 是否存在改用户
      personList: [], // 人员列表
      collect: [
        // 统计
        {
          key: 'count',
          value: '0',
          label: '总人数：',
          unit: '人'
        }
      ]
    }
  },
  mixins: [exportExcel],
  created() {
    this.initLoad()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  watch: {
    isShowEditDialog(newValue) {
      if (!newValue) {
        this.dialogEditData.person_no = ''
        this.dialogEditData.phone = ''
        this.dialogEditData.name = ''
        this.personList = []
      }
    }
  },
  filters: {
    operationTypeName(value) {
      if (value && value === 'background_import') return '后台导入'
      if (value && value === 'manual_bind') return '手动绑定'
    }

  },
  destroyed() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
  },
  methods: {
    /**
     * 刷新页面
     */
    refreshHandler(flag) {
      // 搜索重置
      this.currentPage = 1;
      if (flag) {
        this.$refs.searchRef.resetForm()
      }
      this.tableData = []
      this.initLoad()
    },
    /**
     * 初始化数据
     */
    initLoad() {
      console.log("bank-school初始化");
      // 获取数据列表
      this.getSchoolUserList()
      // 获取模板链接
      this.templateUrl = this.getTempUrl()
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    onPaginationChange(page) {
      console.log("onPaginationChange", page);
      this.currentPage = page
      this.getSchoolUserList()
    },
    /**
     * 显示条数改变
     * @param {*} pageSize
     */
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getSchoolUserList()
    },
    /**
     * 获取车辆绑定列表
     */
    async getSchoolUserList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundEbankAbcSchoolUserListPost(params))
      console.log("getSchoolUserList", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        var resultList = res.data.results || []
        this.tableData = deepClone(resultList)
        this.totalCount = res.data.count || 0
        this.$set(this.collect[0], 'value', this.totalCount)
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     * 格式化查询参数
     * @param {} data
     */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'select_date') {
            params[key] = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    /**
     * 筛选
     */
    searchHandler: debounce(function () {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.refreshHandler(false)
    },
    /**
     * 导入按钮点击
     */
    handlerCarImport() {
      this.isShowImportDialog = true
    },
    /**
     * 编辑
     * @param {*} itemData  每行数据
     */
    handlerEdit(itemData) {
      console.log("itemData", itemData);
      this.dialogEditData = deepClone(itemData)
      this.isHasPersonInfo = !!itemData.person_no
      this.isShowEditDialog = true
    },
    /**
     * 导入确认
     * @param {*} data
     */
    async confirmImportData(data) {
      var dataList = data.allData || []
      var currentPage = data.currentPage
      console.log("confirmImportData", data, currentPage)
      if (Array.isArray(dataList) && dataList.length > 0) {
        this.isShowImportDialog = false

        const [err, res] = await to(this.$apis.apiBackgroundEbankAbcSchoolUserBindAbcSchoolUserImportPost({ url: dataList }))
        if (err) {
          this.$message.error('导入失败 ' + err.message)
          return
        }
        if (res.code === 0) {
          this.$message.success('导入成功')
          this.getSchoolUserList()
        } else {
          this.$message.error('导入失败 ' + res.msg)
        }
      } else {
        this.$message.error('请先导入数据')
      }
    },
    /**
     * 编辑弹窗确认
     */
    submitEditDialog() {
      if (!this.dialogEditData.person_no) {
        this.$message.error('人员编号不能为空')
        return
      }
      if (!this.isHasPersonInfo) {
        this.$message.error('没有查询到该用户，请重新修改人员编号查询！')
        return
      }
      this.bindCardInfo()
    },
    /**
     * 弹窗
     * @param {*} e
     */
    handlerInputChange(e) {
      console.log("handlerInputChange", e);
      this.isLoadingPerson = true
      if (e.length > 0) {
        if (this.timer) {
          clearTimeout(this.timer)
        }
        this.timer = setTimeout(() => {
          this.getNameAndMobile(e)
        }, 1500)
      }
    },
    /**
     * 获取后台人员的姓名跟手机号
     */
    async getNameAndMobile(personNo) {
      console.log("personNo", personNo);
      this.isLoadingPerson = true
      const [err, res] = await to(this.$apis.apiCardServiceCardUserListPost({ person_no: personNo, page: 1, page_size: 9999, is_self_org: false }))
      this.isLoadingPerson = false
      if (err) {
        this.isHasPersonInfo = false
        this.setNameAndMobile()
        return
      }

      if (res.code === 0) {
        var data = res.data || {}
        var resultData = data.results || []
        this.personList = deepClone(resultData)
      } else {
        this.isHasPersonInfo = false
        this.setNameAndMobile()
      }
    },
    /**
     * 设置姓名与手机号
     * @param {*} name
     * @param {*} mobile
     */
    setNameAndMobile(name, mobile) {
      this.dialogEditData.name = name || ''
      this.dialogEditData.phone = mobile || ''
    },
    /**
     * 编辑绑定信息
     */
    async bindCardInfo() {
      this.isBindLoading = true
      var params = {
        person_no: this.dialogEditData.person_no,
        abc_school_user_id: this.dialogEditData.id
      }
      const [err, res] = await to(this.$apis.apiBackgroundEbankAbcSchoolUserBindAbcSchoolUserPost(params))
      this.isBindLoading = false

      if (err) {
        this.$message.error('修改失败 ' + err.message)
        return
      }

      if (res.code === 0) {
        this.$message.success('修改成功')
        this.isShowEditDialog = false
        this.getSchoolUserList()
      } else {
        this.$message.error('修改失败 ' + res.msg)
      }
    },
    /**
     * 获取模板链接
     */
    getTempUrl() {
      var url = URL_TEMPLATE_MODEL
      url = process.env.NODE_ENV === 'development' ? "https://cashier-v4.debug.packertec.com/" + url : location.origin + url
      console.log("url", url);
      return url
    },
    // 导出
    gotoExport() {
      const option = {
        type: "ExportAbcSchoolUser",
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 选择改变
    handlerSelectChange(chooseData) {
      if (chooseData) {
        console.log("handlerSelectChange", chooseData);
        this.isHasPersonInfo = true
      }
    },
    // 更新人员状态
    async handlerPeopleStatus() {
      this.isLoading = true
      var params = {
      }
      const [err, res] = await to(this.$apis.apiBackgroundEbankAbcSchoolUserGetAbcSchoolUserPost(params))
      console.log("handlerPeopleStatus", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        this.$message.success("更新成功！")
        this.currentPage = 1
        this.getSchoolUserList()
      } else {
        this.$message.error(res.msg)
      }
    },
    handleResize() {
      this.$nextTick(() => {
        if (this.$refs.tableData) {
          console.log("hahhahahahha", this.tableHeight);
          this.$refs.tableData.doLayout();
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.carbinding {
  .el-table {
    border: 1px solid hsl(0, 0%, 100%);
  }

  .dialog-person-info {
    width: 250px;
    height: 100px;
    background-color: #F2F2F2;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

}

// .table-fixed {
  // ::v-deep .el-table__fixed-right {
  //   height: 100% !important;
  // }
  // ::v-deep .el-table__fixed-right .el-table__fixed-body-wrapper{
  //   top: 49px !important;
  // }
  // ::v-deep .el-table__fixed {
  //   height: 100% !important;
  // }
// }
</style>
