<template>
  <!-- eslint-disable vue/no-unused-vars -->
  <div class="appeal-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="125px" :loading="isLoading" :form-setting="searchSetting"
      @search="searchHandle" @reset="resetHandler">
      <template slot="perv">
        <div style="margin-bottom: 20px;">
          <el-radio-group v-model="tabType" :disabled="isLoading" @change="changeTabHandle" class="ps-radio-btn">
            <el-radio-button :label="item.value" v-for="(item, index) in tabList" :key="index">{{ item.name
            }}</el-radio-button>
          </el-radio-group>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport"
            v-permission="['background_order.order_charge.charge_appeal_list_export']">导出报表</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column :index="indexMethod" v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #residual="{ row }">
              <el-statistic ref="statistic" format="HH:mm:ss" :value="getExamineTime(row.apply_time)" time-indices
                :value-style="countDown">
              </el-statistic>
            </template>
            <template #operation="{ row }">
              <el-button v-if="tabType == 'applying'" type="text" size="small" class="ps-text"
                v-permission="['background_order.order_charge.approval_charge_refund']" @click="goToEditDialog(row)"
                :disabled="statusRefund(row)">处理</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <div style="margin-left: 20px; font-size: 14px;">
        <span v-if="tabType == 'applying'">待处理合计：{{ totalCount || 0 }}</span>
        <span v-if="tabType == 'agree'">已同意合计：{{ totalCount || 0 }}笔</span>
        <span v-if="tabType == 'refuse'">已拒绝合计：{{ totalCount || 0 }}笔</span>
        <span v-if="tabType == 'cancel'">已取消合计：{{ totalCount || 0 }}笔</span>
      </div>
      <!-- 分页 start -->
      <pagination :onPaginationChange="onPaginationChange" :current-page.sync="currentPage" :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'" :total="totalCount"></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting v-if="dialogPrintVisible" :extraParams="{ printType: printType }" :tableSetting="tableSetting" :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible" @confirm="confirmPrintDialog"></print-setting>
    <!--弹窗-->
    <recharge-refund-dialog :show="isShowRefundDialog" :dialogType="dialogType" :dialogTitle="dialogTitle"
      ref="refundDialog" @dialogClose="dialogClose" @dialogConfirm="dialogConfirm" @dialogRefuse="dialogRefuse">
    </recharge-refund-dialog>
  </div>
</template>

<script>
import { debounce, to, divide, deepClone, replaceDate, times } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import {
  DIC_EXAMINE_STATUS,
  RECHARGE_REFUNDE_SEARCH_SETTING,
  RECHARGE_REFUNDE_SEARCH_SETTING_AGREE,
  RECHARGE_REFUNDE_TABLE_SETTING_PENDING,
  RECHARGE_REFUNDE_TABLE_SETTING_AGREE,
  RECHARGE_REFUNDE_TABLE_SETTING_REFUND,
  RECHARGE_REFUNDE_TABLE_SETTING_CANCEL,
  DIC_DATE_TYPE_REUND
} from './constants'
import report from '@/mixins/report' // 混入
import RechargeRefundDialog from './component/RechargeRefundDialog'

export default {
  name: 'RechageRefundOrder',
  mixins: [exportExcel, report],
  components: {
    RechargeRefundDialog
  },
  data() {
    return {
      isLoading: false,
      tabList: deepClone(DIC_EXAMINE_STATUS),
      tabType: 'applying',
      searchSetting: deepClone(RECHARGE_REFUNDE_SEARCH_SETTING),
      // 数据列表
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      walletOrgsList: [], // 动账钱包
      levelList: [],
      // 报表设置相关
      tableSetting: deepClone(RECHARGE_REFUNDE_TABLE_SETTING_PENDING),
      currentTableSetting: deepClone(RECHARGE_REFUNDE_TABLE_SETTING_PENDING),
      dialogPrintVisible: false,
      printType: 'rechargeRefundOrderExamieApplying',
      agreeCount: '', // 已同意合计
      withdrawCount: '', // 提现合计
      refundCount: '', // 拒绝合计
      cancelCount: '', // 取消合计
      isShowRefundDialog: false,
      dialogType: '',
      dialogTitle: '退款',
      paywayList: [], // 支付方式列表
      paySubwayList: [], // sub支付渠道
      countDown: {
        fontSize: '13px !important'
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
      // 获取支付方式
      this.getPayList()
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getDataList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    changeTabHandle(e) {
      var dataList = deepClone(DIC_DATE_TYPE_REUND)
      this.searchSetting = e === 'agree' ? deepClone(RECHARGE_REFUNDE_SEARCH_SETTING_AGREE) : deepClone(RECHARGE_REFUNDE_SEARCH_SETTING)
      switch (e) {
        case "applying":
          this.tableSetting = deepClone(RECHARGE_REFUNDE_TABLE_SETTING_PENDING)
          this.currentTableSetting = deepClone(RECHARGE_REFUNDE_TABLE_SETTING_PENDING)
          this.printType = 'rechargeRefundOrderExamieApplying'
          break;
        case "agree":
          this.tableSetting = deepClone(RECHARGE_REFUNDE_TABLE_SETTING_AGREE)
          this.currentTableSetting = deepClone(RECHARGE_REFUNDE_TABLE_SETTING_AGREE)
          this.printType = 'rechargeRefundOrderExamieAgree'
          break;
        case "refuse":
          this.tableSetting = deepClone(RECHARGE_REFUNDE_TABLE_SETTING_REFUND)
          this.currentTableSetting = deepClone(RECHARGE_REFUNDE_TABLE_SETTING_REFUND)
          this.printType = 'rechargeRefundOrderExamieRefuse'
          break;
        case "cancel":
          this.tableSetting = deepClone(RECHARGE_REFUNDE_TABLE_SETTING_CANCEL)
          this.currentTableSetting = deepClone(RECHARGE_REFUNDE_TABLE_SETTING_CANCEL)
          this.printType = 'rechargeRefundOrderExamieCancel'
          break;
        default:
          break;
      }
      this.$set(this.searchSetting.time_type, 'dataList', dataList)
      console.log("this.searchSetting.time_type", this.searchSetting.time_type);
      this.initPrintSetting()
      this.updatePaywayList()
      this.currentPage = 1
      this.getDataList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            if (key === 'payway') {
              var list = data[key].value || []
              list = list.filter(item => {
                return item !== ''
              })
              console.log("key == 'payway'", list);
              params[key] = list
              if (list && list.length === 0) {
                delete params.payway
              }
            } else if (data[key].value instanceof Array) {
              if (data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取待处理订单列表
    async getDataList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderChargeChargeAppealListPost({
        page: this.currentPage,
        page_size: this.pageSize,
        approval_status: this.tabType,
        ...this.formatQueryParams(this.searchSetting)
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('getDataList', res)
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results || []
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      if (this.tabType === 1) {
        this.getDataList()
      } else {
        this.getAppealEealList()
      }
    },
    goToEditDialog(row) {
      if (this.getExamineTime(row.apply_time) <= 0) {
        return this.$message.error('亲，已经超过审批时间了')
      }
      console.log("goToEditDialog", row);
      if (this.$refs.refundDialog) {
        this.$refs.refundDialog.setDialogData(row)
      }
      this.isShowRefundDialog = true
    },
    // 导出报表
    handleExport() {
      const option = {
        type: this.printType,
        params: {
          page: this.currentPage,
          page_size: 999999,
          approval_status: this.tabType,
          ...this.formatQueryParams(this.searchSetting)
        },
        url: 'apiBackgroundOrderOrderChargeChargeAppealListExportPost'
      }
      this.exportHandle(option)
    },
    // 关闭弹窗
    dialogClose() {
      this.isShowRefundDialog = false
    },
    // 同意退款
    dialogConfirm(data) {
      console.log("dialogConfirm", data);
      this.updateOrderStatus('agree', data)
    },
    // 拒绝退款
    dialogRefuse(data) {
      this.updateOrderStatus('refuse', data)
    },
    // 更新订单状态
    async updateOrderStatus(flag, data) {
      var refundMessage = flag === 'refuse' ? "拒绝" : "同意"
      var status = flag
      this.updateDialogBtnStatus(true)
      var params = {
        id: data.id,
        refund_fee: times(data.price),
        approval_status: status,
        process_reason: data.remark,
        trade_no: data.tradeNo
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderChargeApprovalChargeRefundPost(params))
      this.updateDialogBtnStatus(false)
      if (err) {
        return this.$message.error(refundMessage + "失败")
      }

      if (res && res.code === 0) {
        this.isShowRefundDialog = false
        this.$message.success(refundMessage + "成功")
        this.getDataList()
      } else {
        this.$message.error(res.msg ? res.msg : refundMessage + "失败")
      }
    },
    // 更新弹窗按钮状态
    updateDialogBtnStatus(isflag) {
      if (this.$refs.refundDialog) {
        this.$refs.refundDialog.setBtnLoading(isflag)
      }
    },
    // 获取审核倒记时间
    getExamineTime(applyTime) {
      console.log("getExamineTime", applyTime);
      if (!applyTime) {
        return 0
      }
      var applyTimeNow = new Date(replaceDate(applyTime)).getTime() + 48 * 60 * 60 * 1000
      var nowTime = new Date().getTime()
      var count = applyTimeNow - nowTime
      return count > 0 ? Date.now() + count : 0
    },
    // 获取支付方式 / 支付类型
    async getPayList() {
      const [err, res] = await to(this.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost({
        type: 'charge' // 这是这个表特定的参数
      }))
      if (err) {
        return
      }
      if (res && res.code === 0) {
        var result = []
        var subResult = []
        var data = res.data || {}
        var results = data.result || {}
        var payways = results.payways || []
        var subPayways = results.sub_payways || []
        payways.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        subPayways.forEach(item => {
          Object.keys(item).forEach(key => subResult.push({ label: item[key], value: key }))
        })
        // this.paywayList = [{ label: '全部', value: '' }, ...result]
        this.paywayList = result
        this.paySubwayList = subResult
        this.updatePaywayList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 更新支付方式
    updatePaywayList() {
      if (Reflect.has(this.searchSetting, 'payway')) {
        this.searchSetting.payway.dataList = deepClone(this.paywayList)// 支付类型
      }
      if (Reflect.has(this.searchSetting, 'sub_payway')) {
        this.searchSetting.sub_payway.dataList = deepClone(this.paySubwayList)// 支付渠道
      }
      console.log("updatePaywayList", this.searchSetting);
    },
    // 退款按钮禁用
    statusRefund(e) {
      var netFee = e.net_fee || 0
      var rateFee = e.rate_fee || 0
      var canRefundPrice = divide(netFee - rateFee)
      let data
      if (e.net_fee === 0 || (e.refund_count !== 0 && canRefundPrice <= 0.01)) {
        data = true
      }
      return data
    }
  }
}
</script>

<style lang="scss" scoped>
.appeal-order {
  .sumWrapper {
    padding-left: 20px;
    padding-bottom: 20px;

    ul,
    li {
      list-style: none;
    }

    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 13px;
    }
  }
}
</style>
