<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting.sync="searchFormSetting" label-width="105px" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          <span>数据列表</span>
        </div>
        <div class="align-r">
          <button-icon color="origin" @click="goToCheckFrame">学校架构查询</button-icon>
          <button-icon color="origin" @click="synchronizationHandle">批量同步</button-icon>
          <button-icon color="origin" type="Import" @click="handleImport">导入</button-icon>
          <button-icon color="plain" type="export" @click="handleExport">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          :data="tableData"
          v-loading="isLoading"
          ref="tableData"
          stripe
          :row-key="getRowKey"
          reserve-selection
          header-row-class-name="ps-table-header-row"
          @select="selectSelection"
          @select-all="selectSelectionAll">
          <el-table-column type="selection" width="55" :selectable="setSelectable"></el-table-column>
          <table-column :index="indexMethod" v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #schoolDeptName="{ row }">
              <div v-for="(item, index) in row.school_dept_name" :key="index">{{ item }}{{ index < row.school_dept_name.length - 1 ? '、' : '' }}</div>
            </template>
            <template #operation="{ row }">
              <el-button v-if="row.id_type !== 'TEACHER'" type="text" size="small" class="ps-text" @click="synchronizationOrNot(row, !row.is_sync)">{{!row.is_sync ? '同步' : '清除'}}</el-button>
              <el-button v-if="row.id_type === 'TEACHER' && !row.is_sync" type="text" size="small" class="ps-text" @click="synchronizationOrNot(row, true)">同步</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>

    <div class="ps-el-drawer">
      <el-drawer
        title="同步"
        :visible="synchronizationShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="synchronizationFormRef" :model="synchronizationForm" label-width="auto" label-position="right">
            <el-form-item label="身份类型" prop="ID_type" :rules="[{ required: true, message: '', trigger: ['change', 'blur'] }]">
              <el-select v-model="synchronizationForm.ID_type" placeholder="请选择身份类型" @change="changeIDType">
                <el-option
                  v-for="(item, index) in IDTypeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="synchronizationForm.ID_type === 'STUDENT' ? '班级' : '部门'" :rules="[{ required: true, message: '', trigger: ['change', 'blur'] }]">
              <yi-de-school-dept-list-select
                v-if="synchronizationForm.ID_type === 'TEACHER'"
                class="search-item-w ps-input w-220"
                v-model="synchronizationForm.class"
                :clearable="true"
                :placeholder="'请选择'"
                :multiple="true"
                :checkStrictly="true"
                :isLazy="true"
                :disabled="false"
                :collapseTags="true"
                :append-to-body="true"
              >
              </yi-de-school-dept-list-select>
              <yi-de-student-org-list-select
                v-else
                class="search-item-w ps-input w-220"
                v-model="synchronizationForm.class"
                :clearable="true"
                :placeholder="'请选择'"
                :multiple="false"
                :checkStrictly="true"
                :isLazy="true"
                :disabled="false"
                :collapseTags="false"
                :append-to-body="true"
              >
              </yi-de-student-org-list-select>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" type="plain" class="w-100" @click="closeHandle">关闭</el-button>
            <el-button size="small" type="primary" class="w-100" @click="syncHandle">同步</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- 导入数据的弹窗 start -->
    <import-dialog-drawer
      :templateUrl="templateUrl"
      :tableSetting="[
        {
          label: '人员编号',
          key: 'person_no'
        },
        {
          label: '姓名',
          key: 'name'
        },
        {
          label: '手机号',
          key: 'phone'
        },
        {
          label: '性别',
          key: 'gender_name'
        },
        {
          label: '身份类型',
          key: 'id_type_alias'
        },
        {
          label: '班级/部门',
          key: 'orgOrClass'
        },
      ]"
      :show.sync="showImportDialog"
      :importType="'excel'"
      :title="'导入'"
      :openExcelType="openExcelType"
      @cancel="cancelHandle"
    ></import-dialog-drawer>
    <!-- 导入数据的弹窗 end -->
  </div>
</template>

<script>
import { debounce, deepClone, getRequestParams } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import YiDeSchoolDeptListSelect from '@/components/YiDeSchoolDeptListSelect'
import YiDeStudentOrgListSelect from '@/components/YiDeStudentOrgListSelect'
export default {
  mixins: [exportExcel],
  components: {
    YiDeSchoolDeptListSelect,
    YiDeStudentOrgListSelect
  },
  data() {
    return {
      searchFormSetting: {
        name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        phone: {
          type: 'input',
          label: '手机号',
          value: '',
          placeholder: '请输入手机号'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        },
        school_dept_ids: {
          type: 'yiDeSchoolDeptListSelect',
          label: '部门',
          value: [],
          clearable: true,
          placeholder: '请选择部门',
          multiple: true,
          checkStrictly: true,
          isLazy: true,
          collapseTags: true
        },
        student_organization_ids: {
          type: 'yiDeStudentOrgListSelect',
          label: '班级',
          value: [],
          clearable: true,
          placeholder: '请选择班级',
          multiple: true,
          checkStrictly: true,
          isLazy: true,
          collapseTags: true
        },
        id_type: {
          type: 'select',
          label: '身份类型',
          value: '',
          placeholder: '请选择身份类型',
          dataList: [
            {
              label: '全部',
              value: ""
            },
            {
              label: '教职工',
              value: "TEACHER"
            },
            {
              label: '学生',
              value: "STUDENT"
            }
          ]
        }
      },
      userCheckedAll: [],
      tableData: [],
      tableSetting: [
        {
          label: '人员编号',
          key: 'person_no'
        },
        {
          label: '姓名',
          key: 'name'
        },
        {
          label: '手机号',
          key: 'phone'
        },
        {
          label: '部门',
          key: 'school_dept_name',
          type: 'slot',
          slotName: 'schoolDeptName'
        },
        {
          label: '班级',
          key: 'student_organization_name'
        },
        {
          label: '性别',
          key: 'gender_name'
        },
        {
          label: '身份类型',
          key: 'id_type_alias'
        },
        {
          label: '操作',
          key: 'operation',
          type: 'slot',
          slotName: 'operation'
        }
      ],
      isLoading: false,
      page: 1,
      pageSize: 10,
      totalCount: 0,
      synchronizationShow: false,
      synchronizationForm: {
        ID_type: '',
        class: []
      },
      IDTypeList: [
        {
          label: '教职工',
          value: 'TEACHER'
        },
        {
          label: '学生',
          value: 'STUDENT'
        }
      ],
      departmentList: [],
      classList: [],
      selectRowData: {},
      selectedRows: [],
      syncType: '',
      showImportDialog: false,
      importHeaderLen: 2,
      templateUrl: location.origin + '/api/temporary/template_excel/智慧校园导入同步用户模板.xlsx',
      openExcelType: 'ImportCampusManagementInfo'
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getYiDeCardUserList()
    },
    getYiDeCardUserList() {
      this.isLoading = true
      let params = getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      this.$apis.apiCardServiceThirdCardUserYideCardUserListPost(params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.tableData = deepClone(res.data.results)
          this.totalCount = res.data.count
          this.$nextTick(() => {
            this.selectedRows.forEach(row => {
              const targetRow = this.tableData.find(item => item.id === row.id)
              if (targetRow) {
                this.$refs.tableData.toggleRowSelection(targetRow, true)
              }
            })
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.page = 1
        this.userCheckedAll = false
        this.getYiDeCardUserList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.selectListId = []
      this.userCheckedAll = false
      this.$refs.tableData.clearSelection();
      this.initLoad()
    },
    getRowKey(row) {
      return row.id;
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.initLoad()
    },
    // 学校架构查询
    goToCheckFrame() {
      this.$router.push({
        name: 'MerchantArchitectureList'
      })
    },
    // 批量同步
    synchronizationHandle() {
      this.syncType = 'more'
      this.synchronizationShow = true
    },
    // 导入
    handleImport() {
      this.showImportDialog = true
    },
    saveHandle() {

    },
    cancelHandle() {
      this.synchronizationShow = false
    },
    // 导出
    handleExport() {
      const option = {
        url: 'apiCardServiceThirdCardUserYideCardUserListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: 1,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 同步或清除
    synchronizationOrNot(data, isNeedToSync) {
      if (isNeedToSync) {
        this.syncType = 'one'
        this.selectRowData = deepClone(data)
        this.synchronizationShow = true
      } else {
        this.$confirm('清除数据将删除校园系统的对应用户，但不会删除食堂系统用户，是否确认清除？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$apis.apiCardServiceThirdCardUserYideStudentDeletePost({
            card_info_id: data.id
          }).then(res => {
            if (res.code === 0) {
              this.$message.success('清除成功')
              this.initLoad()
            } else {
              this.$message.error(res.msg)
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      }
    },
    closeHandle() {
      this.$refs.synchronizationFormRef.resetFields()
      this.synchronizationShow = false
    },
    syncHandle() {
      this.$refs.synchronizationFormRef.validate((valid) => {
        if (valid) {
          let params = {
            id_type: this.synchronizationForm.ID_type,
            card_info_ids: this.syncType === 'one' ? [this.selectRowData.id] : this.selectedRows,
            school_dept_ids: this.synchronizationForm.ID_type === 'TEACHER' ? this.synchronizationForm.class : undefined,
            student_organization_id: this.synchronizationForm.ID_type === 'STUDENT' ? this.synchronizationForm.class : undefined
          }
          this.$apis.apiCardServiceThirdCardUserYideTeacherStudentSyncPost(params).then(res => {
            if (res.code === 0) {
              this.$message.success('同步成功')
              this.$refs.synchronizationFormRef.resetFields()
              this.synchronizationShow = false
              this.getYiDeCardUserList()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          this.$message.error('请检查表单填写的内容是否正确')
        }
      })
    },
    selectSelection(selection, row) {
      let index = this.selectedRows.findIndex(item => item.id === row.id)
      if (index === -1) {
        this.selectedRows.push(row.id)
      } else {
        this.selectedRows.splice(index, 1)
      }
    },
    selectSelectionAll(selection) {
      this.selectedRows = selection.map(item => {
        return item.id
      })
    },
    setSelectable(row) {
      if (row.is_sync) {
        return false
      } else {
        return true
      }
    },
    changeIDType() {
      this.synchronizationForm.class = []
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
