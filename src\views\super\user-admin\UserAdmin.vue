<template>
  <div class="UserAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="origin" @click="resetConfirm">密码重置</button-icon> -->
          <el-button
            size="mini"
            class="ps-plain-btn"
            @click="gotoExport"
          >
            {{ $t('search.export') }}
          </el-button>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          row-key="id"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >

          <!-- <el-table-column
            type="selection"
            width="55"
            class-name="ps-checkbox"
            align="center"
          ></el-table-column> -->

          <el-table-column prop="user_id" label="用户ID" align="center"></el-table-column>
          <el-table-column prop="source" label="来源渠道" align="center"></el-table-column>
          <el-table-column prop="company_names" label="所属组织" align="center"></el-table-column>
          <el-table-column prop="nickname" label="姓名" align="center"></el-table-column>
          <el-table-column prop="xx" label="标签" align="center" width="220px">
            <template slot-scope="scope">
              <div class="collapse-wrapper">
                <div class="collapse-list hide">
                  <el-tag
                    class="m-r-5 m-t-5 collapse-data"
                    v-for="(item, index) in scope.row.label"
                    :key="index"
                    size="medium"
                    effect="plain"
                    type="light"
                    closable
                  >
                    {{ item.name }}
                  </el-tag>
                  <template v-if="scope.row.label && scope.row.label.length > 3">
                    <span class="collapse-more" @click="showMoreHandler">
                      查看更多
                      <i class="el-icon-arrow-down"></i>
                    </span>
                    <span class="collapse-hide" @click="hideMoreHandler">
                      收起
                      <i class="el-icon-arrow-up"></i>
                    </span>
                  </template>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="appid" label="渠道用户ID" align="center"></el-table-column>
          <el-table-column prop="phone" label="手机号码" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="img_url" label="人脸照片" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.img_url"
                type="text"
                size="small"
                @click="showUserImg(scope.row)"
              >
                查看图片
              </el-button>
              <span v-else>无</span>
            </template>
          </el-table-column>
          <el-table-column
            class-name="tools-row"
            align="center"
            width="50"
            :label="$t('table.operate')"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="gotoDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 查看人脸图片 -->
    <dialog-message
      title="人脸图片"
      :show-footer="false"
      :show.sync="showImgDialog"
      @close="faceImgUrl = ''"
    >
      <div class="">
        <el-image :src="faceImgUrl" lazy></el-image>
      </div>
    </dialog-message>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, to, getSevenDateRange, parseTime } from '@/utils'
import { confirm } from '@/utils/message'
export default {
  name: 'SuperUserAdminList',
  props: {},
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  data() {
    const defaultdate = getSevenDateRange(7)
    const currentTime = parseTime(new Date(), '{h}:{i}')
    return {
      isLoading: false, // 刷新数据
      isRefresh: false,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        select_date: {
          type: 'datetimerange',
          label: '创建时间',
          value: [defaultdate[0] + ' 00:00', defaultdate[1] + ` ${currentTime}`]
        },
        nickname: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: ''
        },
        phone: {
          type: 'input',
          label: '手机号',
          value: '',
          placeholder: ''
        },
        source: {
          type: 'select',
          label: '来源渠道',
          value: '',
          placeholder: '',
          dataList: [
            {
              label: '游客',
              value: 'guest'
            },
            {
              label: '微信公众号',
              value: 'weixin'
            },
            {
              label: 'H5',
              value: 'h5 '
            },
            {
              label: '微信小程序',
              value: 'wx_minapp'
            },
            {
              label: '后台导入',
              value: 'import'
            }
          ]
        },
        organization_id: {
          type: 'organizationSelect',
          value: [],
          label: '可见范围',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: [],
          multiple: true,
          checkStrictly: true,
          collapseTags: true,
          clearable: true,
          role: 'super'
        },
        label_filter: {
          type: 'select',
          label: '', // 空格就是占位
          value: '',
          placeholder: '',
          dataList: [
            {
              label: '请选择',
              value: 'select',
              disabled: true
            },
            {
              label: '包含',
              value: 'Include'
            },
            {
              label: '不包含',
              value: 'Exclude'
            }
          ]
        },
        label_list: {
          type: 'treeselect',
          label: '',
          value: [],
          placeholder: '选择标签',
          multiple: true,
          limit: 1,
          level: 1,
          valueConsistsOf: 'LEAF_PRIORITY',
          normalizer: node => ({
            id: node.id,
            label: node.name,
            children: node.label_list
          }),
          dataList: []
        }
      },
      time: new Date().getTime(),
      faceImgUrl: '',
      showImgDialog: false,
      selectList: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getUserList()
      this.getAllLabelGroupList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getUserList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      // this.currentPage = 1;
      // this.tableData = []
      this.initLoad()
    },
    async getUserList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminUserListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取所有的标签
    async getAllLabelGroupList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyAdminLabelGroupAllLabelGroupListPost({
          page_size: 999999,
          page: 1,
          type: 'user'
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(v => {
          if (!v.label_list.length) {
            v.isDisabled = true
          }
          return v
        })
        this.searchFormSetting.label_list.dataList = res.data.results
      } else {
        // this.$message.error(res.msg)
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表选择
    handleSelectionChange(val) {
      this.selectList = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => { this.selectList.push(item.user_id) })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getUserList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getUserList()
    },
    // 显示图片
    showUserImg(data) {
      this.faceImgUrl = data.img_url
      this.showImgDialog = true
    },
    // 详情
    gotoDetail(data) {
      this.$router.push({
        name: 'SuperUserAdminDetail',
        params: {},
        query: {
          id: data.user_id // data.id
        }
      })
    },
    showMoreHandler(e) {
      e.target.parentNode.classList.remove('hide')
    },
    hideMoreHandler(e) {
      e.target.parentNode.classList.add('hide')
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'SuperUserAdminList',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // resetConfirm() {
    //   if (!this.selectList.length) {
    //     return this.$message.error('请先选择数据！')
    //   }
    //   confirm({ content: '是否重置该用户登录密码？' }, this.resetPassword)
    // },
    async resetPassword() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminUserResetPasswordPost({
        ids: this.selectList
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('重置密码成功')
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.collapse-wrapper {
  .collapse-list {
    // text-align: left;
    .collapse-data {
      display: inline-block;
      height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .collapse-more {
      display: none;
    }
    .food-ellipsis {
      display: none;
    }
    .collapse-hide {
      display: block;
      color: #f3b687;
      font-size: 12px;
      cursor: pointer;
    }
    &.hide {
      .collapse-data:nth-child(n + 4) {
        display: none;
      }
      .collapse-more {
        text-align: center;
        display: block;
        color: #f3b687;
        font-size: 12px;
        cursor: pointer;
      }
      .food-ellipsis {
        display: inline-block;
        width: 100px;
      }
      .collapse-hide {
        display: none;
      }
    }
  }
}
</style>
