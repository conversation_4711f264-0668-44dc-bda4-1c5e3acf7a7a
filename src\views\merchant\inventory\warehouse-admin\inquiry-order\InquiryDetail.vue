<template>
  <div class="InboundOrderDetail container-wrapper">
    <h3 class="m-t-20">仓库管理/询价单/详情</h3>
    <div class="table-wrapper">
      <div id="print-box" class="p-20">
        <div class="text-center m-b-20 font-size-26">询价单</div>
        <div>
          <p class="p">询价对象：{{ detailData.supplier }}</p>
          <p class="p">截止报价日期：{{ detailData.end_time }}</p>
          <p class="p">状态：{{ detailData.status_alias }}</p>
        </div>
        <div class="">
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="detailData.materials_data"
            ref="tableData"
            style="width: 70%"
            stripe
            size="small"
            header-row-class-name="ps-table-header-row"
            max-height="600"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item"></table-column>
          </el-table>
          <!-- table end -->
        </div>
        <div class="m-t-30 m-b-20">
          <p>报价清单</p>
          <span class="m-l-20 m-r-10">供应商名称:</span>
          <el-select v-model="offerSupplier" placeholder="请选择" size="small" class="inline-block w-200" @change="changeOfferSupplier">
            <el-option
              v-for="item in offerSupplierList"
              :key="item.supplier_manage"
              :label="item.supplier_manage_name"
              :value="item.supplier_manage"
            >
            </el-option>
          </el-select>
        </div>
        <el-table
          v-loading="isLoading"
          :data="offertableData"
          ref="tableData"
          style="width: 90%"
          stripe
          size="small"
          header-row-class-name="ps-table-header-row"
          max-height="600"
        >
          <table-column v-for="item in offertableSettings" :key="item.key" :col="item"></table-column>
        </el-table>
        <div class="m-t-20">合计：{{ detailData.total_price | formatMoney }}元</div>
      </div>
      <div class="m-l-20 p-t-20 p-b-20">
        <el-button class="ps-btn w-medium" @click="backHandle">返回</el-button>
        <el-button v-if="detailData.status === 'revocation'" class="ps-btn w-medium" @click="gotoAddInquiry(detailData)">重新编辑</el-button>
        <el-button v-if="detailData.status === 'disable'" class="ps-btn w-medium" @click="clickOperationHandle('enable', detailData)">发起询价</el-button>
        <el-button v-if="detailData.display_to_purchase" class="ps-btn w-medium" @click="clickOperationHandle('toPurchase', detailData)">转采购单</el-button>
        <el-button v-if="detailData.display_to_purchase" class="ps-btn w-medium" @click="handleExport(detailData)">导出报价清单</el-button>
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import printUtils from '@/mixins/printUtils' // 导出混入
// import report from '@/mixins/report' // 混入
import print from 'vue-print-nb'
import NP from 'number-precision'

export default {
  name: 'InquiryDetail',
  mixins: [exportExcel, printUtils],
  directives: {
    print
  },
  components: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      warehouseId: '',
      detailData: {},
      tableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '预计采购量', key: 'act_purchase' },
        { label: '单位', key: 'act_purchase_unit' }
      ],
      offertableData: [], // 报价清单
      offertableSettings: [
        { label: '供应商名称', key: 'supplier_manage_name' },
        { label: '物资名称', key: 'materials_name' },
        { label: '采购数量', key: 'purchase' },
        { label: '单位', key: 'purchase_unit' },
        { label: '参考单价', key: 'unit_price', type: 'money' },
        { label: '总价', key: 'total_price', type: 'money' }
      ],
      offerSupplier: '',
      offerSupplierList: [],
      value: ''
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
    this.initResize()
  },
  methods: {
    async initLoad() {
      this.getInquiryDetail()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getInquiryDetail() {
      if (!this.$route.query.id) return this.$message.error('获取详情id失败！')
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: this.$route.query.id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpInquiryDetailsPost(params))
      this.tableData = {}
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let result = res.data
        // result.status = 'revocation'
        // 兼容处理下
        if (!result.materials_data) result.materials_data = []
        if (!result.offer_ingredient) result.offer_ingredient = []
        result.supplier = result.supplier_manage_data.map(v => v.name).join('，')
        // 遍历报价清单的数据获取供应商
        // 页面上的供应商选择是前端手动处理出来的
        let supplierList = [{
          supplier_manage: '',
          supplier_manage_name: '全部'
        }]
        // 存下id去重
        let supplierKey = {}
        result.total_price = result.offer_ingredient.reduce((prev, next) => {
          if (!supplierKey[next.supplier_manage]) {
            supplierList.push({
              supplier_manage: next.supplier_manage,
              supplier_manage_name: next.supplier_manage_name
            })
            supplierKey[next.supplier_manage] = next.supplier_manage
          }
          return NP.plus(prev, next.total_price)
        }, 0)
        this.offerSupplierList = supplierList
        this.offertableData = result.offer_ingredient
        this.detailData = result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 供应商选择change事件
    changeOfferSupplier(e) {
      if (e) {
        this.offertableData = this.detailData.offer_ingredient.filter(v => v.supplier_manage === e)
      } else {
        this.offertableData = this.detailData.offer_ingredient
      }
      const totalPrice = this.offertableData.reduce((prev, next) => {
        return NP.plus(prev, next.total_price)
      }, 0)
      this.$set(this.detailData, 'total_price', totalPrice)
    },
    // 获取转采购单数据
    getPurchaseData(data) {
      let params = []
      let result = {}
      data.map(v => {
        if (result[v.supplier_manage]) {
          result[v.supplier_manage].push(v.id)
        } else {
          result[v.supplier_manage] = [v.id]
        }
      })
      for (const key in result) {
        params.push({
          supplier_manage_id: +key,
          offer_ingredient_ids: result[key]
        })
      }
      return params
    },
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = ''
      let params = {}
      switch (type) {
        case 'enable':
          params = {
            id: data.id
          }
          title = '确定发起询价吗？'
          apiUrl = 'apiBackgroundDrpInquirySubmitPost'
          break
        case 'toPurchase':
          params = {
            id: data.id,
            warehouse_id: +this.$route.query.warehouse_id
            // purchase_data: this.getPurchaseData(data.offer_ingredient)
          }
          title = '确定转采购单吗？'
          apiUrl = 'apiBackgroundDrpInquiryInquiryToPurchasePost'
          break
      }
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg || '成功')
              this.$backVisitedViewsPath(this.$route.path, 'InquiryOrderList')
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    backHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'InquiryOrderList')
    },
    gotoAddInquiry(data) {
      this.$router.push({
        name: 'AddInquiry',
        params: {
          type: 'modify'
        },
        query: {
          ...this.$route.query,
          id: data.id
        }
      })
    },
    // 导出
    handleExport(row) {
      const option = {
        type: 'InquiryDetail',
        url: 'apiBackgroundDrpInquiryExportInquiryPost',
        params: {
          id: row.id
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/print.scss';
.InboundOrderDetail {
  width: 100%;
  .w-200 {
    width: 200px !important;
  }
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
  table {
    width: 100% !important;
    font-weight: 500;
    th {
      font-weight: 500;
    }
  }
  #print-box {
    width: 100%;
    div {
      width: 100%;
    }
    .el-table thead,
    .ps-table-header-row {
      width: 100%;
    }
    .footer {
      p {
        margin: 10px 0 0;
      }
    }
  }
}
</style>
