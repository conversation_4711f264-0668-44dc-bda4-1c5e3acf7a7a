<template>
  <!--权限与键值新增弹窗 -->
  <dialog-message :show.sync="visible" :title="title" :loading.sync="isLoading" @close="handleClose"
    customClass="ps-dialog" :width="width">
    <el-form :model="dialogForm" @submit.native.prevent status-icon ref="dialogFormRef" :rules="dialogFormRules"
      label-width="120px" class="member-form">
      <div v-if="dialogType === 'permission' && (type === 'add' || type === 'edit')">
        <el-form-item label="权限名称：" prop="name">
          <el-input v-model="dialogForm.name" maxlength="5" class="ps-input w-250" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="图标：" prop="img">
          <div class="ps-flex">
            <el-upload v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload"
              :action="serverUrl" :data="uploadParams" :file-list="fileLists" :on-success="uploadSuccess"
              :before-upload="beforeFoodImgUpload" :limit="1" :multiple="false" :show-file-list="false"
              :headers="headersOpts" accept=".jpeg,.jpg,.png,.bmp">
              <img v-if="dialogForm.img" :src="dialogForm.img" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <div class="inline-block upload-tips m-l-10">
              仅支持jpg,png格式，不超过5MB，长宽比为1:1的图片
            </div>
          </div>
        </el-form-item>
        <el-form-item label="类型：" prop="type">
          <el-select v-model="dialogForm.type" placeholder="请选择类型" class="w-250" @change="chooseTypeChange" :disabled="type === 'edit'">
            <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联链接：" prop="link" v-if="dialogForm.type == 'association'">
          <el-input v-model="dialogForm.link" class="ps-input w-250"></el-input>
        </el-form-item>
        <el-form-item label="键值：" prop="label" v-if="dialogForm.type == 'fixed'">
          <el-select v-model="dialogForm.label" placeholder="请选择标签" class="w-250" :disabled="type === 'edit'">
            <el-option v-for="item in keyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="说明：" prop="remark">
          <el-input class="ps-input w-250" v-model="dialogForm.remark" type="textarea" :rows="3" maxlength="50"
            show-word-limit></el-input>
        </el-form-item>
      </div>
      <div v-if="dialogType === 'key'">
        <el-form-item label="名称：" prop="keyName">
          <el-input v-model="dialogForm.keyName" maxlength="5" class="ps-input w-250" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="键值：" prop="keyValue">
          <!-- <el-select v-model="dialogForm.keyValue" placeholder="请选择键值" class="ps-input w-250" >
            <el-option v-for="item in labelList" :key="item.id" :label="item.name" :value="item.name"></el-option>
          </el-select> -->
          <el-input v-model="dialogForm.keyValue" maxlength="20" class="ps-input w-250" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="说明：" prop="keyRemark">
          <el-input v-model="dialogForm.keyRemark" maxlength="100" class="ps-input w-250" type="textarea" :rows="8"
            show-word-limit></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">
          取消
        </el-button>
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { getToken, getSuffix, deepClone } from '@/utils'
export default {
  name: 'MemberKeyOrPermissDialog',
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: 'permission'
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '500px'
    },
    selectInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isshow: Boolean,
    confirm: Function
  },
  data() {
    return {
      isLoading: false,
      dialogForm: {
        keyName: '', // key名称
        keyValue: '', // key键值
        keyRemark: '', // key说明
        name: '',
        img: '',
        type: '',
        imageList: [],
        link: '',
        label: [],
        remark: ''
      },
      dialogFormClone: {
        keyName: '', // key名称
        keyValue: '', // key键值
        keyRemark: '', // key说明
        name: '',
        img: '',
        type: '',
        imageList: [],
        link: '',
        label: '',
        remark: ''
      },
      dialogFormRules: {
        keyName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        keyValue: [{ required: true, message: '请输入键值', trigger: 'blur' }],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        img: [{ required: true, message: '请选择图标', trigger: 'change' }],
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        link: [{ required: true, message: '请输入链接', trigger: 'blur' }],
        label: [{ required: true, message: '请选择键值', trigger: 'change' }],
        remark: [{ required: true, message: '请输入说明', trigger: 'blur' }]
      },
      labelList: [],
      keyList: [],
      typeList: [
        {
          name: '关联链接',
          id: 'association'
        },
        {
          name: '固定模块',
          id: 'fixed'
        }
      ],
      warnTip: '全部标签满足才触发优惠规则',
      serverUrl: '/api/background/file/upload',
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      fileLists: [],
      uploadParams: { // 上传头
        prefix: 'super_food_img'
      },
      uploading: false // 上传加载中
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        console.log("this.visible", this.visible, this.type);
        this.getMemberLabel()
        this.getMemberKey()
        if (this.type === 'edit') {
          this.dialogForm.keyName = this.dialogType === 'key' ? this.selectInfo.name : ''
          this.dialogForm.keyValue = this.dialogType === 'key' ? this.selectInfo.permission_key : ''
          this.dialogForm.keyRemark = this.dialogType === 'key' ? this.selectInfo.remark : ''
          this.dialogForm.name = this.dialogType === 'permission' ? this.selectInfo.name : ''
          this.dialogForm.img = this.dialogType === 'permission' ? this.selectInfo.icon_url : ''
          this.dialogForm.type = this.dialogType === 'permission' ? this.selectInfo.type : ''
          this.dialogForm.imageList = this.dialogType === 'permission' ? [this.selectInfo.icon_url] : ''
          this.dialogForm.label = this.dialogType === 'permission' ? this.selectInfo.permission_dict : ''
          this.dialogForm.link = this.dialogType === 'permission' ? this.selectInfo.associate_url : ''
          this.dialogForm.remark = this.dialogType === 'permission' ? this.selectInfo.remark : ''
        } else {
          this.$set(this, 'dialogForm', deepClone(this.dialogFormClone))
          if (this.$refs.dialogFormRef) {
            this.$refs.dialogFormRef.resetFields()
          }
          if (this.$refs.fileUpload) {
            this.fileLists = []
            this.$refs.fileUpload.clearFiles()
          }
          console.log("this.visible", this.dialogForm);
        }
      } else {
        this.$set(this, 'dialogForm', deepClone(this.dialogFormClone))
        if (this.$refs.dialogFormRef) {
          this.$refs.dialogFormRef.resetFields()
        }
        if (this.$refs.fileUpload) {
          this.fileLists = []
          this.$refs.fileUpload.clearFiles()
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() { },
  methods: {
    initLoad() {
      this.getMemberLabel()
    },
    clickConfirmHandle() {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          let params = {}
          if (this.dialogType === 'key') {
            params = {
              name: this.dialogForm.keyName,
              permission_key: this.dialogForm.keyValue,
              remark: this.dialogForm.keyRemark
            }
          }
          if (this.dialogType === 'permission') {
            params = {
              name: this.dialogForm.name,
              icon_url: this.dialogForm.img,
              type: this.dialogForm.type,
              remark: this.dialogForm.remark
            }
          }
          if (this.dialogForm.type === 'association') {
            params.associate_url = this.dialogForm.link
          }
          if (this.dialogForm.type === 'fixed') {
            params.permission_dict = this.dialogForm.label
          }
          let api
          switch (this.type) {
            case 'add':
              api = this.dialogType === 'key' ? this.$apis.apiBackgroundMemberPermissionDictAddPost(params) : this.$apis.apiBackgroundMemberMemberPermissionAddPost(params)
              break;
            case 'edit':
              params.id = Number(this.selectInfo.id)
              api = this.dialogType === 'key' ? this.$apis.apiBackgroundMemberPermissionDictModifyPost(params) : this.$apis.apiBackgroundMemberMemberPermissionModifyPost(params)
              break;
          }
          this.confirmOperation(api)
        }
      })
    },
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('成功')
        this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.dialogFormRef.resetFields()
    },
    // 获取会员标签
    async getMemberLabel() {
      const res = await this.$apis.apiBackgroundMemberMemberLabelListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.labelList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取会员Key
    async getMemberKey() {
      const res = await this.$apis.apiBackgroundMemberPermissionDictListPost({
        page: 1,
        page_size: 99999
      })
      if (res && res.code === 0) {
        var data = res.data || {}
        let results = data.results || []
        this.keyList = results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 标签选择变化
    labelTypeChange(value) {
      this.warnTip = value === '1' ? '全部标签满足才触发优惠规则' : '任一标签满足即触发规则'
    },
    // 图片上传前检测
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 5
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      this.uploading = true
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        this.fileLists = fileList
        this.dialogForm.img = res.data.public_url
        this.dialogForm.imageList = [res.data.public_url]
        console.log("this.dialogForm.img", this.dialogForm.img)
      } else {
        this.dialogForm.img = ''
        this.$message.error(res.msg)
      }
    },
    // 移除图片
    removeFoodImg(index) {
      this.dialogForm.imageList.splice(index, 1)
      this.fileLists.splice(index, 1)
    },
    // 类型选择切换
    chooseTypeChange(val) {
      console.log("chooseTypeChange", val);
      // 切换的时候清除隐藏的校验
      if (this.$refs.dialogFormRef) {
        this.$refs.dialogFormRef.clearValidate(['link', 'label'])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';

.member-form {
  .label-list {
    display: flex;
    flex-wrap: wrap;
    color: #fff;

    .label-list-item {
      margin-bottom: 10px;
      line-height: 30px;
      background-color: #ff9b45;
      padding: 0 10px;
      margin-right: 15px;
      border-radius: 5px;

      .del-icon {
        cursor: pointer;
      }
    }
  }

  .m-r-5 {
    margin-right: 5px;
  }

  .limit-style {
    align-items: center;
  }
}

.upload-w ::v-deep.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.upload-w ::v-deep.el-upload:hover {
  border-color: #ff9b45;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 90px;
  height: 90px;
  line-height: 90px;
  text-align: center;
}

.avatar {
  width: 90px;
  height: 90px;
  display: block;
}

.upload-tips {
  width: 150px;
  line-height: 20px;
  font-size: 12px;
  margin-top: 30px;
}
</style>
