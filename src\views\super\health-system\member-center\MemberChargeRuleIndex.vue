<template>
  <div class="charge-rule-index container-wrapper">
    <!--刷新页面-->
    <refresh-tool @refreshPage="refreshHandle" />
    <!--表格-->
    <div class="table-wrapper">
      <!--
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      -->
      <div class="table-content m-t-20">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" height="600" stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #remark="{ row, index }">
              <div>
                <div class="ps-flex flex-center" v-if="!row.isEditRemark">
                  {{ row.remark }}
                  <div class="tag-item" @click="handlerTableItemEdit(row, index, 'isEditRemark')"><i
                      class="el-icon-edit m-l-10"></i></div>
                </div>
                <div class="ps-flex" v-if="row.isEditRemark" v-loading="isEditLoading">
                  <el-input class="el-input w-100" v-model="row.remark" placeholder="请输入" maxlength="50"></el-input>
                  <div class="tag-item" @click="handlerTableItemSave(row, index, 'isEditRemark')"><i
                      class="el-icon-finished m-l-10 m-t-10 color-green"></i></div>
                </div>
              </div>
            </template>
            <template #price="{ row, index }">
              <div>
                <div class="ps-flex flex-center" v-if="!row.isEdit">
                  {{ row.origin_fee }}
                  <div class="tag-item" @click="handlerTableItemEdit(row, index, 'isEdit')"><i
                      class="el-icon-edit m-l-10"></i></div>
                </div>
                <div class="ps-flex flex-center" v-if="row.isEdit" v-loading="isEditLoading">
                  <el-input class="el-input w-100" v-model="row.origin_fee" placeholder="请输入"></el-input>
                  <div class="tag-item" @click="handlerTableItemSave(row, index, 'isEdit')"><i
                      class="el-icon-finished m-l-10 m-t-10 color-green"></i></div>
                </div>
              </div>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text-origin" @click="handlerDetail(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
    </div>
  </div>
</template>
<script>
import { deepClone, divide, to, times } from '@/utils';
import { TABLE_HEAD_CHARGE_INDEX_DATA, TABLE_HEAD_CHARGE_INDEX_CONTENT } from './constants.js'

export default {
  name: 'MemberChargeRuleIndex',
  data() {
    return {
      tableData: deepClone(TABLE_HEAD_CHARGE_INDEX_CONTENT), // 表单数据
      tableSettings: deepClone(TABLE_HEAD_CHARGE_INDEX_DATA), // 表头数据
      isLoading: false,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页数量
      isEditLoading: false
    }
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      this.getDataList()
    },
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      // this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initData()
    },
    // 表格项目编辑
    handlerTableItemEdit(row, index, key) {
      console.log("row", row, index);
      this.$set(this.tableData[index], key, true)
    },
    // 保存
    async handlerTableItemSave(data, index, key) {
      if (key === 'isEdit') {
        var price = data.origin_fee
        var reg = /(^[1-9][0-9]{0,6})$/
        if (!reg.test(price)) {
          return this.$message.error("请输入大于1的正整数,不能大于9999999")
        }
        if (parseFloat(price) < 1) {
          return this.$message.error("不能输入低于1元的值")
        }
      }
      console.log("handlerTableItemSave", data, index, key, price);
      this.isEditLoading = true
      const res = await this.$apis.apiBackgroundMemberMemberChargeRuleModifyPost({
        id: data.id,
        name: data.name,
        member_labels: data.member_labels,
        member_cycle: data.member_cycle,
        is_enable: data.is_enable,
        trigger_type: data.trigger_type,
        discount: data.discount,
        origin_fee: times(data.origin_fee, 100),
        buy_count: data.buy_count,
        remark: data.remark,
        is_base: true
      })
      this.isEditLoading = false
      if (res.code === 0) {
        this.$set(this.tableData[index], key, false)
        this.$message.success('成功')
        this.getDataList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 查看详情
    handlerDetail(row) {
      console.log("handlerDetail", row);
      this.$router.push({
        name: 'SuperMemberChargeRuleDetail',
        query: {
          type: row.member_cycle,
          basePrice: row.origin_fee
        }
      })
    },
    // 获取会员周期
    async getDataList() {
      var parmas = {
        is_base: true,
        page: this.currentPage,
        page_size: this.pageSize
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMemberMemberChargeRuleListPost(parmas))
      console.log("apiBackgroundMemberMemberChargeRuleGetMemberCyclePost", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        var resultList = res.data.results || []
        if (resultList) {
          resultList = resultList.map(item => {
            item.origin_fee = item.origin_fee ? parseInt(divide(item.origin_fee)) : 0
            return item
          })
        }
        this.tableData = deepClone(resultList)
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.charge-rule-index {
  .tag-item {
    cursor: pointer;
  }

  .color-green {
    color: #14ce84;
  }
  .flex-center{
    justify-content: center;
  }
}
</style>
