<template>
  <div class="to-do-list container-wrapper">
    <div class="table-type">
      <el-button :class="[type === index ? 'ps-origin-btn' : '']" v-for="(item, index) in buttonList" :key="index" @click="changeType(index)">{{ item.name }}</el-button>
    </div>
    <div>
      <pending-approval v-if="type === 0"></pending-approval>
      <data-check v-else :selectTab="type"></data-check>
    </div>
  </div>
</template>

<script>
import pendingApproval from './components/pendingApproval.vue'
import dataCheck from './components/dataCheck.vue'
export default {
  components: {
    dataCheck,
    pendingApproval
  },
  data() {
    return {
      type: 0,
      buttonList: [
        {
          index: 0,
          name: '待审批'
        },
        {
          index: 1,
          name: '已同意'
        },
        {
          index: 2,
          name: '已拒绝'
        },
        {
          index: 3,
          name: '已撤销'
        }
      ]
    }
  },
  created() {
    let arr = this.buttonList.filter(item => item.permission !== 'no')
    if (arr.length) {
      this.changeType(arr[0].index)
    } else {
      this.changeType(999)
    }
  },
  methods: {
    changeType(index) {
      this.type = index
    }

  }
}
</script>

<style lang="scss" scoped>
.to-do-list {
  .table-type {
    padding: 20px 0px;
  }
}
</style>
