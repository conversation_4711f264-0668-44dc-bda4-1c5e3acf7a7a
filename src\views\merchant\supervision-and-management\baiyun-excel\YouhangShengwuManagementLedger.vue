<template>
  <div class="container-wrapper">
    <div class="harmful-organism-record">
      <h2 class="table-title">有害生物防治管理记录表</h2>

      <div class="year-row">
        <span class="year-label">2025年</span>
      </div>

      <el-table
        :data="displayData"
        border
        style="width: 100%"
        :cell-style="cellStyle"
        :header-cell-style="headerCellStyle">

        <!-- 时间 -->
        <el-table-column label="时间" align="center" width="200" >
          <template slot-scope="scope">
            <div class="ps-flex flex-center">{{ scope.row.month }} 月 <div><el-input v-model="scope.row.day" style="width: 50px;"></el-input></div> 日</div>
          </template>
        </el-table-column>

        <!-- 地点 -->
        <el-table-column label="地点" align="center" >
          <template>
            <div>食堂/餐厅</div>
          </template>
        </el-table-column>

        <!-- 检查内容 -->
        <el-table-column label="检查内容" align="center">
          <template>
            <div class="check-content">
              <p>1、捕鼠笼、粘鼠板投放情况</p>
              <p>2、灭鼠情况</p>
              <p>3、灭蟑螂情况</p>
              <p>4、灭蚊、灭蝇情况</p>
            </div>
          </template>
        </el-table-column>

        <!-- 检查情况 -->
        <el-table-column label="检查情况" align="center">
          <template>
            <div class="check-situation">
              <p>1、是 ( ) /否 ( ) 按规定放置捕鼠笼、粘鼠板;</p>
              <p>2、是 ( ) /否 ( ) 出现老鼠;</p>
              <p>3、是 ( ) /否 ( ) 出现蟑螂;</p>
              <p>4、苍蝇、蚊子是 ( ) /否 ( ) 超标。</p>
            </div>
          </template>
        </el-table-column>

        <!-- 检查人 -->
        <el-table-column label="检查人" align="center" width="100">
          <template slot-scope="scope">
            <el-input v-model="scope.row.inspector" size="mini" placeholder=""></el-input>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <!-- <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length">
        </el-pagination> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'YouhangShengwuManagementLedger',
  data() {
    return {
      currentPage: 1,
      pageSize: 12,
      tableData: this.generateTableData()
    };
  },
  computed: {
    displayData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    }
  },
  methods: {
    generateTableData() {
      const data = [];

      // 生成12个月的记录，每月1条
      for (let month = 1; month <= 12; month++) {
        data.push({
          month: month,
          day: '',
          inspector: ''
        });
      }

      return data;
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    cellStyle() {
      return {
        padding: '5px',
        fontSize: '14px',
        textAlign: 'center'
      };
    },
    headerCellStyle() {
      return {
        backgroundColor: '#f5f7fa',
        color: '#303133',
        fontWeight: 'bold',
        fontSize: '14px',
        padding: '8px 0',
        textAlign: 'center'
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.harmful-organism-record {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .table-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
  }

  .year-row {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;

    .year-label {
      font-size: 16px;
      font-weight: bold;
      padding: 5px 20px;
      border: 1px solid #DCDFE6;
      background-color: #f5f7fa;
    }
  }

  ::v-deep .el-table .cell {
    padding: 5px;
  }

  ::v-deep .el-input__inner {
    border: none;
    border-bottom: 1px solid #DCDFE6;
    border-radius: 0;
    text-align: center;
  }

  .check-content,
  .check-situation {
    text-align: left;

    p {
      margin: 5px 0;
      line-height: 1.5;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  .flex-center {
    align-items: center;
  }
}
</style>
