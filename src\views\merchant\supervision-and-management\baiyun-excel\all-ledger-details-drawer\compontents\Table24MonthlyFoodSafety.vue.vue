<template>
  <!-- 表24-学校食堂每月食品安全调度会议纪要-详情 -->
  <div class="content">
    <div>
      <span class="color-666">复核人：</span>
      张局
    </div>
    <!-- for具体内容 -->
    <div v-for="(parentsItem, parentsIndex) in detailList" :key="parentsIndex">
      <div class="title">{{ parentsItem.title }}</div>
      <div class="content-item">
        <template v-if="!parentsItem.isSubTitle">
          <div v-for="(item, index) in parentsItem.keyList" :key="index" :style="{ width: item.width }" class="m-b-8">
            <span class="color-666 m-r-5">{{ item.label }}:</span>
            <template v-if="item.type === 'image'">
              <div class="m-t-5">
                <el-image
                  v-for="(url, urlIndex) in resData[item.key]"
                  :key="urlIndex"
                  class="detault-img m-r-10 pointer"
                  :src="url"
                  fit="cover"
                  @click="clickViewerHandler(resData[item.key], urlIndex)"
                ></el-image>
              </div>
            </template>
            <template v-else>
              <span class="color-333">{{ resData[item.key] }}</span>
            </template>
          </div>
        </template>

        <template v-if="parentsItem.isSubTitle">
          <div v-for="(item, index) in parentsItem.keyList" :key="index">
            <div class="m-b-8">{{ item.subtitle }}</div>
            <div class="flex flex-wrap">
              <div v-for="(v, i) in item.keyList" :key="i" :style="{ width: v.width }" class="flex m-b-8">
                <div class="color-666 m-r-5" :style="{ width: v.labelWidth }">{{ v.label }}:</div>
                <div class="color-333" :style="{ width: v.valueWidth }">{{ resData[v.key] }}</div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <!-- 预览图片 -->
    <image-viewer
      v-model="showViewer"
      :initial-index="imgIndex"
      :z-index="3000"
      :on-close="closeViewer"
      :preview-src-list="previewSrcList"
    />
  </div>
</template>
<script>
export default {
  name: 'Table24MonthlyFoodSafety',
  data() {
    return {
      // 具体内容信息集合
      detailList: [
        {
          title: '会议信息',
          isSubTitle: false,
          keyList: [
            { key: 'name', label: '会议名称', width: '50%' },
            { key: 'name', label: '主持人', width: '50%' },
            { key: 'name', label: '会议地点', width: '50%' },
            { key: 'name', label: '记录人', width: '50%' },
            { key: 'date', label: '会议日期', width: '50%' },
            { key: 'name', label: '负责人', width: '50%' },
            { key: 'name', label: '出席人员', width: '100%' },
            { key: 'fileList', label: '校长/法人每月不少于一次察看明厨亮灶的工作照片', width: '100%', type: 'image' }
          ]
        },
        {
          title: '食品安全管理工作情况汇报',
          isSubTitle: true,
          keyList: [
            {
              subtitle: '(一）“互联网+明厨亮灶"智慧系统发现的问题及整改落实情况名称：',
              keyList: [
                { key: 'name', label: '会议名称', width: '50%' },
                { key: 'name', label: '主持人', width: '50%' },
                { key: 'name', label: '会议地点', width: '50%' },
                { key: 'name', label: '记录人', width: '50%' },
                { key: 'date', label: '会议日期', width: '50%' },
                { key: 'name', label: '负责人', width: '50%' },
                { key: 'name', label: '出席人员', width: '100%' }
              ]
            },
            {
              subtitle: '(二）“互联网+明厨亮灶"智慧系统发现的问题及整改落实情况名称：',
              keyList: [
                { key: 'name', label: '会议名称', width: '50%' },
                { key: 'name', label: '主持人', width: '50%' },
                { key: 'name', label: '会议地点', width: '50%' },
                { key: 'name', label: '记录人', width: '50%' },
                { key: 'date', label: '会议日期', width: '50%' },
                { key: 'name', label: '负责人', width: '50%' },
                { key: 'name', label: '出席人员', width: '100%' }
              ]
            },
            {
              subtitle: '(三)学生及家长投诉处理情况：',
              keyList: [
                { key: 'name', label: '会议名称', width: '50%' },
                { key: 'name', label: '主持人', width: '50%' },
                { key: 'name', label: '会议地点', width: '50%' },
                { key: 'name', label: '记录人', width: '50%' },
                { key: 'date', label: '会议日期', width: '50%' },
                { key: 'name', label: '负责人', width: '50%' },
                { key: 'name', label: '出席人员', width: '100%' }
              ]
            },
            {
              subtitle: '(四)供货商管理',
              keyList: [
                { key: 'textsName', label: '本月对供应商审核情况', labelWidth: '24%', valueWidth: '70%' }
              ]
            }
          ]
        }
      ],
      // 后端返回的数据
      resData: {
        name: '测试会议',
        date: '2025-05-05',
        fileList: [
          'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          'https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg'
        ],
        textsName: '大概什么审核情况的这大概什么审核情况的这个请假大概什么审核情况的这个请假大概什么审核情况的这个请假大概什么审核情况的这个请假大概什么审核情况的这个请假'
      },
      // 预览图片
      imgIndex: 0,
      previewSrcList: [],
      showViewer: false
    }
  },
  methods: {
    // 预览图片
    clickViewerHandler(row, index) {
      // don't show viewer when preview is false
      this.previewSrcList = row || []
      this.imgIndex = index
      this.showViewer = true
    },
    // 关闭预览
    closeViewer() {
      this.showViewer = false
    }
  }
}
</script>
<style lang="scss" scoped>
.color-666 {
  color: #999999;
}
.color-333 {
  color: #111111;
}
.w-50 {
  width: 50%;
}
.w-100 {
  width: 1000px;
}
.inline-block{
  display: inline-block;
}
.title {
  position: relative;
  margin: 10px 0;
  font-weight: bold;
  padding-left: 12px;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #ffa558;
    border-radius: 2px;
  }
}
.content-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding:10px 20px;
  background-color: #f2f2f2;
  border-radius: 10px;
  font-size: 16px;
  // 图片
  .detault-img {
    width: 160px;
    height: 92px;
    border: 1px dashed #e1e1e1;
    border-radius: 2px;
  }
}
</style>
