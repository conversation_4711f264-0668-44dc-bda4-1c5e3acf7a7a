<template>
  <div class="p-20">
    <div class="m-b-20">
      <div>
        检查日期：{{ drawerData.check_date }}
      </div>
      <div>
        复核人：{{ reviewPerson }}
      </div>
    </div>
    <el-table :data="drawerData.extra" stripe header-row-class-name="ps-table-header-row">
      <table-column v-for="(item, index) in drawerTableSetting" :key="index" :col="item">
        <template #checkResult="{ row }">
          {{ row.check_result ? '是' : '否' }}
        </template>
      </table-column>
    </el-table>
    <div class="flex m-t-20">
      <div class="m-r-20" style="max-width: 20em;">
        <div>检查人：{{ drawerData.director }}</div>
        <el-image
        :src="drawerData.director_sign"
        :fit="'fill'" style="width: 200px; height: 80px;"></el-image>
      </div>
      <div style="max-width: 20em;">
        <div>负责人：{{ drawerData.examiner }}</div>
        <el-image
        :src="drawerData.examiner_sign"
        :fit="'fill'" style="width: 200px; height: 80px;"></el-image>
      </div>
    </div>
  </div>
</template>

<script>
import { to, deepClone } from '@/utils'

export default {
  name: 'Table22XuexiaoShipinAnquanManagementLedger',
  props: {
    ledgerNo: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      drawerTableSetting: [
        { label: '检查项目', key: 'check_project' },
        { label: '类型', key: 'type' },
        { label: '检查内容', key: 'check_content' },
        { label: '检查结果', key: 'check_result', type: "slot", slotName: "checkResult" },
        { label: '不符合项说明', key: 'describe' },
        { label: '不符合项采取的防范措施', key: 'measure' }
      ],
      drawerData: {
        check_date: '',
        extra: [],
        director: '',
        director_sign: '',
        examiner: '',
        examiner_sign: ''
      },
      reviewPerson: ''
    }
  },
  watch: {
    ledgerNo(val) {
      if (val) {
        this.initData()
      }
    }
  },
  mounted() {
    console.log('ledgerNo', this.ledgerNo)
    this.initData()
  },
  methods: {
    async initData() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerKitchenHygieneDailyFoodSafetyDetailsPost({
          ledger_no: this.ledgerNo
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        this.drawerData = deepClone(data)
        this.reviewPerson = data.confirmer_name || '--'
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
