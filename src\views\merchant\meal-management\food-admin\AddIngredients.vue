<template>
  <div class="super-add-ingredients container-wrapper">
    <el-form
      v-loading="isLoading"
      :rules="formRuls"
      :model="formData"
      ref="formIngredients"
      size="small"
    >
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">基本信息</div>
        </div>
        <div class="" style="padding: 0 20px;">
          <el-form-item label="食材图片" prop="" class="block-label form-content-flex">
            <div class="">
              <div class="inline-block upload-w">
                <el-upload
                  v-loading="uploading"
                  element-loading-text="上传中"
                  class="file-upload"
                  ref="fileUpload"
                  drag
                  :action="serverUrl"
                  :data="uploadParams"
                  :file-list="fileLists"
                  :on-success="uploadSuccess"
                  :before-upload="beforeFoodImgUpload"
                  :limit="1"
                  :multiple="false"
                  :show-file-list="false"
                  :headers="headersOpts"
                  accept=".jpeg,.jpg,.png,.bmp"
                  >
                  <slot>
                    <div class="upload-t" v-if="!formData.imageList.length || formData.imageList.length === 0">
                      <i class="el-icon-circle-plus"></i>
                      <div class="el-upload__text">
                        <span class="bg-text">上传食材图片</span>
                      </div>
                    </div>
                    <el-image
                      class="el-upload-dragger"
                      v-if="formData.imageList && formData.imageList.length > 0"
                      :src="formData.imageList[0]"
                      @click="removeFoodImg"
                      fit="contain">
                    </el-image>
                  </slot>
                </el-upload>
              </div>
              <div class="inline-block upload-tips">
                <span class="bg-text" style="padding-left: 2px;">上传：食材图片。</span><br />
                建议图片需清晰，图片内容与名称相符。<br />
                仅支持jpg、png、bmp格式，大小不超过5M
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="">
          <div class="" style="width: 710px; padding: 0 20px">
            <el-form-item label="食材名称" prop="name" class="block-label form-content-flex">
              <el-input
                v-model="formData.name"
                placeholder="请输入食材名称"
                maxlength="30"
                class="ps-input"
                style="width: 80%"
              ></el-input>
              <el-tooltip effect="dark" content="增加食材别名" placement="top">
                <img class="add-btn-img" @click="addAliasName" src="@/assets/img/plus.png" alt="">
              </el-tooltip>
            </el-form-item>
            <div v-if="formData.aliasName.length">
              <el-form-item label="食材别名" class="block-label">
                <el-form-item
                  :class="[index>0?'m-t-10':'','alias-name-form']"
                  v-for="(item,index) in formData.aliasName"
                  :key="index"
                  :rules="formRuls.aliasName"
                  :prop="`aliasName[${index}]`">
                  <el-input maxlength="20" style="width: 80%" v-model="formData.aliasName[index]" placeholder="请输入食材别名" class="ps-input"></el-input>
                  <img src="@/assets/img/plus.png" @click="addAliasName" alt="">
                  <img src="@/assets/img/reduce.png" @click="delAliasName(index)" alt="">
                </el-form-item>
              </el-form-item>
            </div>
            <el-form-item label="食材分类" prop="sort" class="block-label">
              <el-cascader
                class="ps-select"
                placeholder="请选择或输入食材类别"
                style="width: 80%"
                v-model="formData.sort"
                :options="categoryList"
                :show-all-levels="false"
                :props="cascaderProps"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="食材属性" prop="attribute" class="flex">
              <el-radio-group v-model="formData.attribute">
                <el-radio label="main" name="main" class="ps-checkbox">主食</el-radio>
                <el-radio label="secondary" name="secondary" class="ps-checkbox">副食</el-radio>
              </el-radio-group>
            </el-form-item>
            <div class="flex">
              <!-- <span class="m-t-8 m-r-5 ps-red">*</span> -->
              <el-form-item label="食材生重" prop="rawWeight" class="flex m-r-20">
                <el-input
                  v-model="formData.rawWeight"
                  placeholder=""
                  class="ps-input"
                  style="width: 100px"
                ></el-input>
                g
              </el-form-item>
              <el-form-item label="食材熟重" prop="cookedWeight" class="flex m-r-20">
                <el-input
                  v-model="formData.cookedWeight"
                  placeholder=""
                  class="ps-input"
                  style="width: 100px"
                ></el-input>
                g
              </el-form-item>
              <el-form-item label="生熟比" prop="" class="flex">
                <span>{{ rawCookedRate }} %</span>
                <!-- <el-input
                  v-model="formData.rawCookedRate"
                  placeholder=""
                  class="ps-input"
                  style="width: 100px"
                  :disabled="isDisabledRate"
                ></el-input><span v-if="formData.rawCookedRate">%</span> -->
              </el-form-item>
            </div>
            <el-form-item label="食材应季" prop="isSeasonal" class="flex">
              <el-radio-group v-model="formData.isSeasonal">
                <el-radio :label="true" class="ps-checkbox">是</el-radio>
                <el-radio :label="false" class="ps-checkbox">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="formData.isSeasonal" label="" prop="seasonalMonth" class="block-label">
              <el-date-picker
                v-model="formData.seasonalMonth"
                type="monthrange"
                range-separator="至"
                format="MM月"
                value-format="MM"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                popper-class="custom-ingredient-monthrange"
                >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="标签" prop="" class="">
              <el-button
                class="ps-origin-btn"
                type="primary"
                size="small"
                @click="labelClick"
              >
                选择标签
              </el-button>
            </el-form-item>
            <el-form-item :label="`${groupKey}:`" prop="" class="" v-for="(labelGroupItem,groupKey,labelGroupIndex) in formData.labelGroupInfoList" :key="labelGroupIndex">
              <el-tag
                class="m-r-5 collapse-data"
                v-for="(item, index) in labelGroupItem"
                :key="index"
                size="medium"
                effect="plain"
                type="info"
                color="#fff"
                closable
                @close="closeTag(groupKey,index,item)"
              >
                {{item.name}}
              </el-tag>
            </el-form-item>
          </div>
        </div>
      </div>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            营养信息
            <span class="text-op__7">（每100克所含的营养信息）</span>
            <el-switch
              v-model="formData.is_enable_nutrition"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
            ></el-switch>
          </div>
        </div>
        <div class="table-content" v-if="formData.is_enable_nutrition">
          <template v-for="nutrition in currentNutritionList">
            <div class="nutrition-item" :key="nutrition.key">
              <!-- <div class="nutrition-label">{{nutrition.name+'：'}}</div> -->
              <el-form-item
                :prop="nutrition.key"
                :label="nutrition.name + '：'"
                :rules="formRuls.nutrition"
              >
                <el-input
                  style="width: 120px"
                  v-model="formData[nutrition.key]"
                  class="ps-input"
                  placeholder="请输入"
                ></el-input>
                <span style="margin-left: 10px">{{ nutrition.unit }}</span>
              </el-form-item>
            </div>
          </template>
          <div class="text-center pointer">
            <span @click="showAll = !showAll" style="color:#027DB4;">{{ showAll ? '收起' : '查看更多营养信息' }}</span>
          </div>
        </div>
      </div>
      <div class="footer" style="margin-top: 20px">
        <el-button style="width: 120px" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitHandler">
          {{ type === 'modify' ? '修改' : '添加' }}
        </el-button>
      </div>
    </el-form>
    <select-laber
      v-if="selectLaberDialogVisible"
      :isshow.sync="selectLaberDialogVisible"
      width="600px"
      @selectLaberData="selectLaberData"
      :ruleSingleInfo="ruleSingleInfo"
      ref="selectLaber"
      >
        <div slot="append">
          <div class="tab">
            <div
              :class="['tab-item', ruleSingleInfo.isAdmin ? 'active' : '']"
              @click="tabClick"
            >
              平台标签
            </div>
            <div
              :class="['tab-item', ruleSingleInfo.isAdmin === false ? 'active' : '']"
              @click="tabClick"
            >
              自有标签
            </div>
          </div>
        </div>
    </select-laber>
    <dialog-message width="415px" top="30vh" title="保存成功" :show.sync="showDialog" center>
      <div class="import-food m-b-50">
        前往添加菜品，可快速创建菜品库和食材库，选择系统菜品相关食材会自动创建。
      </div>
      <div slot="tool" class="text-center">
        <el-button class="ps-btn w-110" type="primary" style="margin-right: 20px;" @click="clickConfirmHandle">添加菜品</el-button>
        <el-button class="ps-cancel-btn w-110" @click="clickCancleHandle">我知道了</el-button>
      </div>
    </dialog-message>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getToken, getSuffix } from '@/utils'
import { NUTRITION_LIST } from './constants'
import { confirm } from '@/utils/message'
import selectLaber from '../components/selectLaber.vue'
export default {
  name: 'AddIngredients',
  // mixins: [activatedLoadData, exportExcel],
  data() {
    let validataNutrition = (rule, value, callback) => {
      if (value) {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('营养数据有误，仅支持保留两位小数'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    // 校验生熟数据输入，限制一位小数
    let validataRate = (rule, value, callback) => {
      if (value) {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/
        if (!reg.test(value)) {
          callback(new Error('仅支持数字，且最多保留1位小数'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      type: 'add',
      isLoading: false, // 刷新数据
      formData: {
        id: '', // 编辑时使用
        name: '', // 食材名
        aliasName: [], // 食材别名
        sort: '', // 分类
        is_enable_nutrition: false,
        ingredient_id: '', // 重复的食材id
        selectLabelListData: [], // 标签列表
        selectLabelIdList: [], // 标签id列表
        labelGroupInfoList: {}, // 带标签组名字的数据
        imageList: [],
        attribute: '', // 食材属性
        rawWeight: 100, // 食材生重
        cookedWeight: 100, // 食材熟重
        rawCookedRate: '', // 生熟比
        isSeasonal: '', // 是否应季
        seasonalMonth: [] // 季度
      },
      categoryList: [], // 分类
      cascaderProps: {
        label: 'name',
        value: 'id',
        children: 'sort_list',
        emitPath: false // 绑定的内容只获取最后一级的value值。
      },
      formRuls: {
        name: [{ required: true, message: '食材名称不能为空', trigger: 'blur' }],
        aliasName: [{ required: true, message: '请输入食材别名', trigger: 'blur' }],
        nutrition: [{ validator: validataNutrition, trigger: 'change' }],
        sort: [{ required: true, message: '请选择食材类别', trigger: "blur" }],
        // energy_mj: [{ required: true, message: '请输入', trigger: "blur" }],
        // energy_kcal: [{ required: true, message: '请输入', trigger: "blur" }],
        // carbohydrate: [{ required: true, message: '请输入', trigger: "blur" }],
        // protein: [{ required: true, message: '请输入', trigger: "blur" }],
        // axunge: [{ required: true, message: '请输入', trigger: "blur" }]
        attribute: [{ required: true, message: '请选择食材属性', trigger: "blur" }],
        rawWeight: [
          { required: true, message: '请输入食材生重', trigger: "change" },
          { validator: validataRate, trigger: 'change' }
        ],
        cookedWeight: [
          { required: true, message: '请输入食材熟重', trigger: "change" },
          { validator: validataRate, trigger: 'change' }
        ],
        isSeasonal: [{ required: true, message: '请选择食材应季', trigger: "blur" }],
        seasonalMonth: [{ required: true, message: '请选择应季月份', trigger: "blur" }]
      },
      nutritionList: NUTRITION_LIST,
      selectLaberDialogVisible: false,
      ruleSingleInfo: {
        isAdmin: true, // 是否获取平台标签
        labelType: 'food'
      },
      serverUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      fileLists: [],
      uploadParams: {
        prefix: 'super_food_img'
      },
      showAll: false,
      showDialog: false,
      uploading: false
    }
  },
  computed: {
    currentNutritionList: function () {
      let result = []
      if (!this.showAll) {
        result = this.nutritionList.slice(0, 4)
      } else {
        result = this.nutritionList
      }
      return result
    },
    // 生熟比率
    rawCookedRate: function () {
      let isDisable = this.formData.rawWeight && this.formData.cookedWeight
      let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/
      if (isDisable && reg.test(this.formData.rawWeight) && reg.test(this.formData.cookedWeight)) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.formData.rawCookedRate = parseInt(this.formData.rawWeight / this.formData.cookedWeight * 100)
      } else {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.formData.rawCookedRate = ''
        isDisable = false
      }
      return this.formData.rawCookedRate
    }
  },
  components: { selectLaber },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    // 初始化
    async initLoad() {
      this.type = this.$route.query.type
      await this.getCategoryCategoryNameList()
      if (this.type === 'modify') {
        const data = this.$decodeQuery(this.$route.query.data)
        console.log(1111, data)
        this.formData.name = data.name
        this.formData.aliasName = data.alias_name
        this.formData.attribute = data.attribute
        this.formData.rawWeight = data.raw_weight
        this.formData.cookedWeight = data.cooked_weight
        this.formData.isSeasonal = data.is_seasonal
        if (data.is_seasonal) {
          this.formData.seasonalMonth = [this.padStartNumber(data.seasonal_start), this.padStartNumber(data.seasonal_end)]
        }
        this.formData.sort = data.sort
        if (data.image && data.image.length > 0) {
          console.log("data.image", data.image)
          this.formData.imageList = [data.image]
          this.fileLists = [{
            url: data.image,
            name: data.image,
            status: "success",
            uid: data.image
          }]
        } else {
          this.formData.imageList = []
          this.fileLists = []
        }
        this.categoryList.map(v => {
          if (v.sort_list.length) {
            v.sort_list.map(child => {
              if (child.id && (Number(child.id.split('-')[1]) === data.sort)) {
                this.formData.sort = child.id
              }
            })
          }
        })
        //  回显示标签组名字 {'aa':[{xx:xx}]}
        if (data.label.length) {
          // 格式化标签
          this.initLabelGroup(data.label)
        }
        this.formData.selectLabelListData = data.label
        this.formData.selectLabelIdList = data.label.map(v => { return v.id })
        // end
        this.formData.id = data.id
        this.formData.is_enable_nutrition = !!data.is_enable_nutrition
        if (!data.nutrition) data.nutrition = {}
        let element = data.nutrition.element ? JSON.parse(data.nutrition.element) : {}
        let vitamin = data.nutrition.vitamin ? JSON.parse(data.nutrition.vitamin) : {}
        NUTRITION_LIST.forEach(nutrition => {
          if (nutrition.type === 'default') {
            this.$set(
              this.formData,
              nutrition.key,
              data.nutrition[nutrition.key] ? data.nutrition[nutrition.key] : 0
            )
          }
          if (nutrition.type === 'element') {
            this.$set(
              this.formData,
              nutrition.key,
              element[nutrition.key] ? element[nutrition.key] : 0
            )
          }
          if (nutrition.type === 'vitamin') {
            this.$set(
              this.formData,
              nutrition.key,
              vitamin[nutrition.key] ? vitamin[nutrition.key] : 0
            )
          }
        })
      } else {
        NUTRITION_LIST.forEach(item => {
          this.$set(this.formData, item.key, 0)
        })
      }
    },
    // 数字补零处理
    padStartNumber(num) {
      return num.toString().padStart(2, '0')
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
    }, 300),
    // 获取所有一级食材以及下面的二级食材
    async getCategoryCategoryNameList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSortCategoryNameListPost()
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.categoryList = res.data.map(v => {
          if (v.sort_list.length) {
            v.sort_list = v.sort_list.map(item => {
              item.id = v.id + '-' + item.id
              return item
            })
          }
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化下参数
    formatParams() {
      console.log(this.formData)
      let params = {
        name: this.formData.name,
        label_list: this.formData.selectLabelIdList,
        alias_name: this.formData.aliasName,
        is_enable_nutrition: this.formData.is_enable_nutrition ? 1 : 0,
        attribute: this.formData.attribute, // 食材属性
        raw_weight: parseFloat(this.formData.rawWeight), // 食材生重
        cooked_weight: parseFloat(this.formData.cookedWeight), // 食材熟重
        is_seasonal: this.formData.isSeasonal // 是否应季
      }
      if (this.formData.sort) {
        if (this.formData.sort.indexOf('-') > -1) {
          let sortId = this.formData.sort.split('-')
          params.sort_id = sortId[1]
        } else {
          params.sort_id = this.formData.sort
        }
      }
      if (this.formData.ingredient_id && this.type === 'add') {
        params.ingredient_id = this.formData.ingredient_id
      }
      if (this.formData.is_enable_nutrition) {
        let element = {}
        let vitamin = {}
        NUTRITION_LIST.forEach(nutrition => {
          let value = this.formData[nutrition.key] ? this.formData[nutrition.key] : 0
          if (nutrition.type === 'default') {
            params[nutrition.key] = value
          }
          if (nutrition.type === 'element') {
            element[nutrition.key] = value
          }
          if (nutrition.type === 'vitamin') {
            vitamin[nutrition.key] = value
          }
        })
        params.element = JSON.stringify(element)
        params.vitamin = JSON.stringify(vitamin)
      }
      if (this.formData.imageList.length) {
        params.image = this.formData.imageList[0]
      }
      // 应季月份
      if (this.formData.isSeasonal) {
        params.seasonal_start = this.formData.seasonalMonth[0]
        params.seasonal_end = this.formData.seasonalMonth[1]
      }
      return params
    },
    labelClick() {
      this.ruleSingleInfo = {
        isAdmin: true,
        labelType: 'ingredient',
        selectLabelIdList: this.formData.selectLabelIdList,
        selectLabelListData: this.formData.selectLabelListData
      }
      this.selectLaberDialogVisible = true
    },
    // 删除标签
    closeTag(key, index, item) {
      // 删除
      let idx = this.formData.selectLabelIdList.indexOf(item.id)
      let ids = this.formData.selectLabelListData.indexOf(item)
      this.formData.selectLabelIdList.splice(idx, 1)
      this.formData.selectLabelListData.splice(ids, 1)
      // 重置数据
      this.formData.labelGroupInfoList = {}
      this.initLabelGroup(this.formData.selectLabelListData)
    },
    // 选择标签
    selectLaberData(params) {
      this.formData.selectLabelIdList = params.selectLabelIdList
      this.formData.selectLabelListData = params.selectLabelListData
      this.formData.labelGroupInfoList = {}
      this.initLabelGroup(this.formData.selectLabelListData)
    },
    initLabelGroup(data) {
      data.forEach(v => {
        if (!this.formData.labelGroupInfoList[v.label_group_name]) {
          this.formData.labelGroupInfoList[v.label_group_name] = []
        }
        if (this.formData.labelGroupInfoList[v.label_group_name] && !this.formData.labelGroupInfoList[v.label_group_name].includes(v)) {
          this.formData.labelGroupInfoList[v.label_group_name].push(v)
        }
      })
    },
    tabClick() {
      this.ruleSingleInfo.isAdmin = !this.ruleSingleInfo.isAdmin
      this.$refs.selectLaber.currentPage = 1
      this.$refs.selectLaber.getLabelGroupList()
    },
    // 提交按钮点击事件
    submitHandler() {
      if (this.isLoading) return this.$message.error('请不要重复点击提交！')
      this.$refs.formIngredients.validate(async valid => {
        if (valid) {
          if (this.type === 'modify') {
            this.modifyIngredients()
          } else {
            this.addIngredients()
          }
        }
      })
    },
    // 添加
    async addIngredients() {
      // this.$confirm(`是否确定创建该食材,新建食材如果已存在则会替换原有食材，如有需要更改请联系客服`, {
      //   confirmButtonText: this.$t('dialog.confirm_btn'),
      //   cancelButtonText: this.$t('dialog.cancel_btn'),
      //   closeOnClickModal: false,
      //   customClass: 'ps-confirm',
      //   cancelButtonClass: 'ps-cancel-btn',
      //   confirmButtonClass: 'ps-btn',
      //   center: true,
      //   beforeClose: async (action, instance, done) => {
      //     if (action === 'confirm') {
      //       this.isLoading = true
      //       instance.confirmButtonLoading = true
      //       const [err, res] = await to(this.$apis.apiBackgroundFoodIngredientAddPost(this.formatParams()))
      //       this.isLoading = false
      //       if (err) {
      //         this.$message.error(err.message)
      //         return
      //       }
      //       if (res.code === 0) {
      //         done()
      //         this.$message.success('添加成功')
      //         this.$closeCurrentTab(this.$route.path)
      //       } else {
      //         this.$message.error(res.msg)
      //       }
      //       instance.confirmButtonLoading = false
      //     } else {
      //       if (!instance.confirmButtonLoading) {
      //         done()
      //       }
      //     }
      //   }
      // })
      //   .then(e => {
      //   })
      //   .catch(e => {})
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodIngredientAddPost(this.formatParams()))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('添加成功')
        // this.$closeCurrentTab(this.$route.path)
        // this.showDialog = true
        this.$closeCurrentTab(this.$route.path)
      } else if (res.code === 2) {
        this.formData.ingredient_id = res.data.ingredient_id
        confirm({ content: res.msg }, this.addIngredients).catch(e => {
          this.formData.ingredient_id = ''
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改
    async modifyIngredients() {
      this.$confirm(`确定修改？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundFoodIngredientModifyPost({
                id: this.formData.id,
                ...this.formatParams()
              })
            )
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success('修改成功')
              // this.$closeCurrentTab(this.$route.path)
              // this.showDialog = true
              this.$closeCurrentTab(this.$route.path)
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 取消
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 添加食材别名
    addAliasName() {
      this.formData.aliasName.push('')
    },
    delAliasName(index) {
      this.formData.aliasName.splice(index, 1);
    },
    // 移除图片
    removeFoodImg(index) {
      this.formData.imageList.splice(index, 1)
      this.fileLists.splice(index, 1)
    },
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res.code === 0) {
        this.fileLists = fileList
        this.formData.imageList = [res.data.public_url]
        console.log(this.formData.imageList)
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 2
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },
    async clickConfirmHandle(e) {
      this.clickCancleHandle()
      await this.$sleep(100)
      // this.$router.push({
      //   name: 'MerchantCopyFoods'
      // })
      this.$router.back()
    },
    clickCancleHandle(e) {
      this.$closeCurrentTab(this.$route.path)
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss">
@import './styles/commont.scss';
.super-add-ingredients {
  .flex{
    display: flex;
    flex-wrap: wrap;
  }
  .block-label {
    width: 100%;
    .el-form-item__label {
      display: block;
      text-align: left;
      line-height: 1.5;
      float: none;
    }
  }
  .form-content-flex{
    .el-form-item__content{
      display: flex;
    }
  }
  .add-btn-img{
    width:25px;
    height:25px;
    margin:3px 0 0 10px;
  }
  .alias-name-form{
    // display: block!important;
    margin-bottom: 0px!important;
    .el-form-item__content{
      display: flex;
      // width: 585px;
      img{
        width: 25px;
        height: 25px;
        margin: 5px 0 5px 10px;
      }
    }
  }
  .nutrition-item {
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
    .el-form-item__label {
      display: block;
      text-align: left;
      line-height: 1.5;
      float: none;
    }
  }
.tab {
  margin:10px 0;
  .tab-item {
    display: inline-block;
    width: 90px;
    height: 26px;
    line-height: 26px;
    margin-right: 10px;
    text-align: center;
    border-radius: 14px;
    border: solid 1px #dae1ea;
    font-size: 14px;
    color: #7b7c82;
    vertical-align: middle;
    cursor: pointer;
    &.active {
      color: #ffffff;
      background-color: #fd953c;
      border: solid 1px #fd953c;
    }
  }
  }
  .upload-w{
    width: 224px;
    height: 142px;
    border-radius: 4px;
    border: solid 1px #e0e6eb;
    text-align: center;
    vertical-align: top;
    // display: flex;
    // justify-content: center;
    // align-items: center;
  }
  .el-upload-dragger{
    width: 224px;
    height: 142px;
  }
  .upload-t{
    vertical-align: top;
    margin-top: 35px;
    color: #ff9b45;
    .el-icon-circle-plus{
      font-size: 30px;
      color: #ff9b45;
    }
  }
  .upload-tips{
    margin-top: 30px;
    padding-left: 20px;
    color: #9fa7ad;
    // line-height: 1.5;
  }
  .bg-white{
    padding: 6px;
    background-color: #fff;
  }
  .bg-text{
    padding: 8px 18px;
    // background-color: #f2f2f2;
  }
}
.custom-ingredient-monthrange{
  &.el-date-range-picker {
    width: 300px;
    .el-picker-panel__body {
      min-width: auto;
    }
    .el-date-range-picker__content {
      float: none;
      width: 100%;
    }
  }
  .el-date-range-picker__header {
    display: none;
  }
  .el-picker-panel__content.el-date-range-picker__content.is-right {
    display: none;
  }
  .el-month-table .in-range .cell:hover {
    color: #fff;
  }
}
</style>
