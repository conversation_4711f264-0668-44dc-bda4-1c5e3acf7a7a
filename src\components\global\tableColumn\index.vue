<template>
  <!-- index, selection, expand 要做独立处理，貌似可以抽离动态组件的方式，先这样吧 -->
  <el-table-column
    v-if="
      (col.type === 'index' || col.type === 'selection' || col.type === 'expand') && !col.hidden
    "
    :prop="col.key"
    :label="col.label"
    :align="col.align ? col.align : 'center'"
    :show-overflow-tooltip="col.showTooltip"
    :class-name="col.className"
    :type="col.type"
    :width="col.width"
    :min-width="col.minWidth"
    :fixed="col.fixed"
    :index="index"
    :reserve-selection="col.reserveSelection"
    :selectable="col.selectable"
    >
  </el-table-column>
  <!-- 常规 -->
  <el-table-column
    v-else-if="!col.hidden"
    :prop="col.key"
    :label="col.label"
    :align="col.align ? col.align : 'center'"
    :show-overflow-tooltip="col.showTooltip"
    :class-name="col.className"
    :width="col.width"
    :min-width="col.minWidth"
    :fixed="col.fixed"
    :sortable="col.sortable ? col.sortable : false"
    :label-class-name="col.labelClassName"
  >
    <!-- 多表头模式 start -->
    <template v-if="col.children">
      <table-column v-for="(item, index) in col.children" :key="index" :col="item">
        <!-- 传递所有插槽到子组件 -->
        <template v-for="(_, name) in $scopedSlots" :slot="name" slot-scope="slotData">
          <slot :name="name" v-bind="slotData"></slot>
        </template>
      </table-column>
    </template>
    <!-- end -->
    <template slot-scope="scope">
      <!-- 自定义插槽 start -->
      <slot
        v-if="col.type === 'slot'"
        :col="col"
        :name="col.slotName"
        :row="scope.row"
        :index="scope.$index"
        :column="scope.column"
      ></slot>
      <!-- 自定义插槽 end -->
      <!-- 自定义组件 start -->
      <components
        v-else-if="col.isComponents"
        :row="scope.row"
        :prop="col.key"
        :col="col"
        :is="`column-${col.type}`"
      ></components>
      <!-- 自定义组件 end -->
      <!-- old module -->
      <span v-else>
        <span v-if="col.prefix && scope.row[col.key] !== ''">{{ col.prefix }}</span>
        <span v-if="col.type === 'money' && col.unit">{{ col.unit }}</span>
        <span v-if="col.type === 'money'">
          <!-- 因为可能某些报表不需要合计 比如消费点汇总表中的储值钱包余额 他是返回‘-’,亦或者显示文字 -->
          <template v-if="typeof scope.row[col.key] === 'string'">
            {{ scope.row[col.key] }}
          </template>
          <template v-else>
            {{ scope.row[col.key] | formatMoney }}
          </template>
        </span>
        <span v-else-if="col.type === 'moneyFloat'">
          <span ><span v-if="col.unit">{{col.unit}}</span>{{ scope.row[col.key] | formatMoneyFloat }}</span>
        </span>
        <span v-else-if="col.type === 'date'">{{ scope.row[col.key] | formatDate }}</span>
        <div v-else-if="col.type === 'img'">
          <img :src="scope.row[col.key]" alt class="table-img" />
        </div>
        <span v-else-if="col.type === 'percent'">{{ scope.row[col.key] }}%</span>
        <span v-else-if="col.type === 'count'">{{ scope.row[col.key] }}</span>
        <span v-else>{{ scope.row[col.key] || scope.row[col.key]===0 ? scope.row[col.key] : '--' }}</span>
      </span>
    </template>
  </el-table-column>
</template>

<script>
/**
 * @description tableColumn封装
 * @property {Object} col 组件配置
 */

// col.type 目前特殊类型有：
//          money金额自动格式化处理
//          date日期自动格式化处理
//          img
//          percent百分比

// 提供了插槽和动态组件的用法
// 插槽： col.slot{Boolean}是否开启，slotName{String}插槽的名称
// 动态组件：col.isComponents{Boolean}是否开启， type{String}动态组件的名字，注意组件的name和组件的命名，当前动态组件会自动寻找组件的明并自动拼接`Column${componentName}`的方式，详细情况案例

// 动态表格封装
// https://juejin.cn/post/7131669281758150692
const modules = {};
function capitalizeFirstLetter (str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
function validateFileName (str) {
  return /^\S+\.vue$/.test(str) &&
    str.replace(/^\S+\/(\w+)\.vue$/, (rs, $1) => capitalizeFirstLetter($1))
}
const files = require.context("./components", true, /\.vue$/);
files.keys().forEach((filePath) => {
  const componentConfig = files(filePath)
  const fileName = validateFileName(filePath)
  const componentName = fileName.toLowerCase() === 'index'
    ? capitalizeFirstLetter(componentConfig.default.name)
    : fileName
  modules[`Column${componentName}`] = files(filePath).default || componentName;
});
console.log(modules, "modules");

export default {
  name: 'TableColumn',
  components: {
    // 组件动态注册
    ...modules
  },
  props: {
    col: {
      type: Object
      // default: () => {
      //   return {
      //     key: 'xx',
      //     label: 'xx',
      //     align: 'xx',
      //     type: 'money',
      //     showTooltip: false,
      //     className: 'className',
      //     hidden: true
      //   }
      // }
    },
    index: {
      type: Function,
      default: e => {
        return e + 1
      }
    }
  }
}
</script>

<style scoped>
.table-img{
  max-height: 100px;
}
.multiline-text {
  white-space: pre-wrap;
  text-align: left;
}
</style>
