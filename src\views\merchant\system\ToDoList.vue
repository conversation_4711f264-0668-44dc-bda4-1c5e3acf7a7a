<template>
  <div class="to-do-list container-wrapper">
    <div class="table-type">
      <!-- 因为后续还要加按钮，要记得处理刷新页面的按钮的样式 -->
      <div :class="['table-type-btn', tableType==='EarlyWarningItem'?'active-btn':'']" @click="changeTableType('EarlyWarningItem')">食堂公示</div>
      <refresh-tool @refreshPage="refreshHandle" :showTitle="false" />
    </div>
    <div v-if="tableType === 'EarlyWarningItem'">
      <early-warning-item ref="earlyWarningItem" />
    </div>
    <div v-else>
      <el-empty description="暂无内容"></el-empty>
    </div>
  </div>
</template>

<script>
import earlyWarningItem from './components/earlyWarningItem.vue'
export default {
  components: {
    earlyWarningItem
  },
  data() {
    return {
      tableType: 'EarlyWarningItem'
    }
  },
  methods: {
    changeTableType(type) {
      this.tableType = type
    },
    refreshHandle() {
      this.$refs.earlyWarningItem.getDataList()
    }
  }
}
</script>

<style lang="scss" scoped>
.to-do-list {
  .table-type{
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    .table-type-btn{
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      background-color: #E8F0F8;
      border-radius: 4px;
      margin-right: 20px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn{
      color: #FFF;
      background-color: #ff9b45;
      border: none;
    }
  }
}
</style>
