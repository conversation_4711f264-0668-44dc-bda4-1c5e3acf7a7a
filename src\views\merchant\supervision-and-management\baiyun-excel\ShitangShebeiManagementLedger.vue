<template>
  <div class="ShitangShebeiManagementLedger container-wrapper">
    <refresh-tool @refreshPage="refreshHandle"></refresh-tool>
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="gotoPrint">打印</button-icon>
          <button-icon color="origin" @click="gotoExport" v-permission="['background_fund_supervision.ledger_food_safety.canteen_device_maintenance_record_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="pagedTableData"
          border
          style="width: 100%"
          header-row-class-name="ps-table-header-row"
          >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoHandle('detail', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :pageSizes="[10, 20, 31, 50]"
        :layout="'total, prev, pager, next, sizes, jumper'"
        :total="totalCount"
      />
    </div>
  </div>
</template>

<script>
import exportExcel from '@/mixins/exportExcel'
import { SEARCH_SETTING_SHITANGSHEBEI_LEDGER, TABLE_HEAD_DATA_SHITANGSHEBEI_LEDGER } from './constants'
import { debounce, to, deepClone } from '@/utils'

export default {
  name: 'ShitangShebeiManagementLedger',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false,
      currentPage: 1,
      pageSize: 10,
      totalCount: 31,
      tableData: [],
      searchFormSetting: deepClone(SEARCH_SETTING_SHITANGSHEBEI_LEDGER),
      tableSettings: deepClone(TABLE_HEAD_DATA_SHITANGSHEBEI_LEDGER),
      printType: 'ShitangShebeiManagementLedger'
    }
  },
  created() {
    this.initLoad()
  },
  computed: {
    pagedTableData() {
      const start = (this.currentPage - 1) * this.pageSize
      return this.tableData.slice(start, start + this.pageSize)
    }
  },
  methods: {
    initLoad() {
      this.buildTableByMonth()
      this.getDataList()
    },
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    parseMonthValue(value) {
      if (!value) return new Date()
      if (typeof value === 'string') {
        const [y, m] = value.split('-').map(n => Number(n))
        return new Date(y, m - 1, 1)
      }
      if (value instanceof Date) return value
      return new Date()
    },
    getDaysInMonth(date) {
      const y = date.getFullYear()
      const m = date.getMonth()
      return new Date(y, m + 1, 0).getDate()
    },
    buildTableByMonth() {
      const date = this.parseMonthValue(this.searchFormSetting.month.value)
      const days = this.getDaysInMonth(date)
      const data = []
      for (let i = 1; i <= days; i++) {
        data.push({
          day: `${i}日`,
          xiaodu: '',
          zhengshao: '',
          reshuiqi: '',
          reshuiyou: '',
          bingxiang: '',
          purenweixiang: '',
          weibolu: '',
          baocanto: '',
          chouyouyan: '',
          cleaning: '',
          maintenance: '',
          status: '',
          operator: '',
          confirmer: ''
        })
      }
      this.tableData = data
      this.totalCount = data.length
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取list接口数据
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize,
        // to do debug
        start_date: "2015-8-1",
        end_date: "2015-8-31"
      }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetDeviceMaintenanceCleaningRecord(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.tableData = deepClone(results)
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg || '获取数据失败')
      }
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      let tableSetting = deepClone(this.tableSettings)
      tableSetting = tableSetting.filter(item => item.key !== 'operation')
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: false,
          print_type: this.printType,
          print_title: '食堂设备定期维护清洗管理台账',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetDeviceMaintenanceCleaningRecord', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(tableSetting),
          current_table_setting: JSON.stringify(tableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    },
    // 导出
    gotoExport() {
      const option = {
        url: 'apiBackgroundFundSupervisionLedgerFoodSafetyGetDeviceMaintenanceCleaningRecordExport',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          date_type: this.printType,
          page: this.currentPage,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.ShitangShebeiManagementLedger {
  .table-wrapper {
    background: #fff;
    padding: 12px;
    border-radius: 4px;
  }
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .table-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
