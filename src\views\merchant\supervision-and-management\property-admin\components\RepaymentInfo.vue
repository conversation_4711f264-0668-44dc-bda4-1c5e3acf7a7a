<template>
  <div class="container-wrapper repayment-info">
    <div class="tab-item m-t-20">
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" @click="gotoExport">导出</button-icon>
            <button-icon color="plain" @click="clickRepaymentInfoDrawerShow('add')">添加</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            tooltip-effect="dark repayment-info-tooltips"
            header-row-class-name="ps-table-header-row"
            empty-text="暂无数据，请查询"
          >
            <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
              <template #images="{ row }">
                <el-button
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="handleClick(row)"
                  :disabled="!row.file.length"
                >
                  查看
                </el-button>
              </template>
              <template #remark="{ row }">
                <el-tooltip class="item" :content="row.remark" effect="dark" placement="top">
                  <div class="oneLine">{{ row.remark }}</div>
                </el-tooltip>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- 新建 -->
    <repayment-info-add
      :isShow.sync="repaymentInfoDrawerShow"
      v-if="repaymentInfoDrawerShow"
      @clickConfirm="clickConfirm"
    ></repayment-info-add>
    <!-- 图片预览 -->
    <image-viewer v-model="showImagePreview" :z-index="3000" :on-close="closePreview" :preview-src-list="previewList" />
  </div>
</template>

<script>
import { SEARCH_REPAYMENT_INFO, TABLE_REPAYMENT_INFO } from './constants'
import { debounce, getRequestParams, deepClone } from '@/utils'
import RepaymentInfoAdd from './RepaymentInfoAdd.vue'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'RepaymentInfo',
  mixins: [exportExcel],
  components: {
    RepaymentInfoAdd
  },
  data() {
    return {
      isLoading: false,
      tableSetting: deepClone(TABLE_REPAYMENT_INFO),
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      searchFormSetting: deepClone(SEARCH_REPAYMENT_INFO),
      printType: 'RepaymentInfo',
      repaymentInfoDrawerShow: false,
      showImagePreview: false,
      previewList: []
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getDataList()
    },
    // 筛选
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    // 请求列表数据
    async getDataList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundFundSupervisionBusLiabilityV4RepaymentListPost(params)
      this.isLoading = false
      this.tableData = []
      if (res && res.code === 0) {
        let data = res.data || {}
        this.totalCount = data.count
        this.tableData = deepClone(data.results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    clickConfirm() {
      this.currentPage = 1;
      this.getDataList()
    },
    clickRepaymentInfoDrawerShow(type) {
      this.repaymentInfoDrawerShow = true
    },
    handleClick(row) {
      this.previewList = row.file
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    gotoExport() {
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.totalCount)
      const option = {
        url: 'apiBackgroundFundSupervisionBusLiabilityV4RepaymentExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
.repayment-info {
  .oneLine {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.repayment-info-tooltips {
  white-space: normal; /* 允许换行 */
  word-break: break-all; /* 长单词自动换行 */
  max-width: 200px; /* 设置最大宽度，超出后换行 */
}
</style>
