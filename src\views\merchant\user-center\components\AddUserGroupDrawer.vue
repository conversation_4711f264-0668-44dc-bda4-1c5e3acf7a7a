<template>
  <div class="drawer-box">
    <customDrawer
      :show.sync="visible"
      :loading="isLoading"
      :title="drawerTitle"
      @confirm="saveSetting"
      :cancelClass="'ps-cancel-btn'"
      :cancelText="'取 消'"
      :size="600"
    >
      <div class="drawer-container">
        <el-form ref="drawerFormDataRef" v-loading="isLoading" :rules="drawerFormDataRuls" :model="drawerFormData">
          <el-form-item prop="groupNo" label="分组编号：" label-width="120px">
            <el-input
              class="ps-input w-250"
              v-model="drawerFormData.groupNo"
              placeholder="请输入分组编号，不填则随机生成"
              :disabled="drawerType == 'modify' ? true : false"
            ></el-input>
          </el-form-item>
          <el-form-item prop="groupName" label="分组名称：" label-width="120px">
            <el-input class="ps-input w-250" v-model="drawerFormData.groupName"></el-input>
          </el-form-item>
          <el-form-item label="自动分组：" label-width="120px">
            <template slot="label">
              <div class="ps-flex flex-align-c ps-float-r">
                <el-tooltip placement="top" class="item">
                  <div slot="content">
                    <div v-html="groupTip" style="white-space: pre-line"></div>
                  </div>
                  <i class="el-icon-question" style="font-size: 18px; color: #ff9b45"></i>
                </el-tooltip>
                <span>自动分组：</span>
              </div>
            </template>
            <el-switch
              v-model="drawerFormData.is_auto_group"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
            ></el-switch>
          </el-form-item>

          <el-divider></el-divider>
          <div
            v-for="(groupConditionItem, groupConditionIndex) in drawerFormData.groupConditionList"
            :key="groupConditionIndex"
          >
            <div class="ps-flex-bw">
              <el-form-item label="自动分组条件：" label-width="120px">
                <el-radio-group v-model="groupConditionItem.groupConditionValue">
                  <el-radio class="ps-radio" label="age">年龄</el-radio>
                </el-radio-group>
              </el-form-item>
              <i class="el-icon-delete" @click="clickDelGroupCondition(groupConditionIndex)"></i>
            </div>

            <div
              class="user-group-age"
              v-for="(singleItem, singleIndex) in groupConditionItem.age_extra"
              :key="singleIndex"
            >
              <div class="ps-flex flex-align-c">
                <el-form-item label="">
                  <el-input
                    v-model="singleItem.start_age"
                    size="small"
                    placeholder="请输入年龄"
                    class="w-100 ps-input"
                    maxlength="3"
                    @input="validateInteger($event, singleItem, 'start_age')"
                  ></el-input>
                </el-form-item>
                <span class="p-l-10 p-r-10">~</span>
                <el-form-item label="">
                  <el-input
                    v-model="singleItem.end_age"
                    size="small"
                    placeholder="请输入年龄"
                    class="w-100 ps-input"
                    maxlength="3"
                    @input="validateInteger($event, singleItem, 'end_age')"
                  ></el-input>
                </el-form-item>
                <span class="p-l-10 p-r-10">岁</span>
              </div>

              <img src="@/assets/img/plus.png" class="m-r-10" @click="clickAddSinglEageExtra(groupConditionIndex)" />
              <img
                src="@/assets/img/reduce_red.png"
                v-if="groupConditionItem.age_extra.length > 1"
                @click="delSingleAgeExtra(groupConditionIndex, singleIndex)"
              />
            </div>
            <el-divider></el-divider>
          </div>

          <el-button
            class="ps-btn"
            :disabled="!!drawerFormData.groupConditionList.length"
            type="primary"
            @click="clickAddGroupCondition"
          >
            新增自动分组条件
          </el-button>
        </el-form>
      </div>
    </customDrawer>
  </div>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  props: {
    drawerType: {
      type: String,
      default() {
        return 'add'
      }
    },
    isshow: Boolean,
    drawerModifyData: {
      type: Object,
      default() {
        return {}
      }
    },
    updateInterFace: {
      type: Function
    }
  },
  data() {
    var groupNoValidator = (rule, value, callback) => {
      if (value !== '') {
        if (!/^[0-9]{1,6}$/.test(value)) {
          callback(new Error('请输入由数字组成的编号，长度不超过六位'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      groupTip: '选中时，新建/编辑/导入用户时，无法手动选择该分组，\n满足自动分组条件的用户自动归入该分组。',
      drawerTitle: '新建分组',
      isLoading: false,
      drawerFormData: {
        groupNo: '',
        groupName: '',
        is_auto_group: false,
        groupConditionList: []
      },
      drawerFormDataRuls: {
        groupNo: [{ validator: groupNoValidator, trigger: 'blur' }],
        groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    this.initDrawerForm()
  },
  methods: {
    // 回填数据
    initDrawerForm() {
      if (this.drawerType === 'modify') {
        this.drawerTitle = '修改分组'
        this.drawerFormData.groupName = this.drawerModifyData.group_name
        this.drawerFormData.groupNo = this.drawerModifyData.group_id
        this.drawerFormData.is_auto_group = this.drawerModifyData.is_auto_group
        // 年龄的情况 为什么要这样做，因为是拆开传给后端的 格式不是[{}]这样的
        this.drawerFormData.groupConditionList = []
        if (this.drawerModifyData.age_extra && this.drawerModifyData.age_extra.length) {
          this.drawerFormData.groupConditionList.push({
            groupConditionValue: this.drawerModifyData.is_age_condition ? 'age' : '',
            age_extra: this.drawerModifyData.age_extra
          })
        }
      }
    },
    // 新增条件
    clickAddGroupCondition() {
      this.drawerFormData.groupConditionList.push({
        groupConditionValue: 'age',
        // 年龄
        age_extra: [
          {
            start_age: '',
            end_age: ''
          }
        ]
      })
    },
    // 删除条件
    clickDelGroupCondition(index) {
      this.drawerFormData.groupConditionList.splice(index, 1)
    },
    // 新增年龄
    clickAddSinglEageExtra(index) {
      this.drawerFormData.groupConditionList[index].age_extra.push({
        start_age: '',
        end_age: ''
      })
    },
    // 删除年龄
    delSingleAgeExtra(index, singleIndex) {
      this.drawerFormData.groupConditionList[index].age_extra.splice(singleIndex, 1)
    },
    isValidAgeRange(range) {
      // 检查是否有空值
      if (!range.start_age || !range.end_age) {
        return false
      }
      return range.start_age <= range.end_age
    },

    isAgeRangeInvalidOrOverlapping(ranges) {
      // 验证所有区间是否合法
      for (let i = 0; i < ranges.length; i++) {
        if (!this.isValidAgeRange(ranges[i])) {
          return true // 发现不合法的区间，立即返回true
        }
      }

      // 对区间按照开始年龄进行排序
      ranges.sort((a, b) => a.start_age - b.start_age)

      // 检查相邻区间是否重叠
      for (let i = 0; i < ranges.length - 1; i++) {
        // 如果前一个区间的结束年龄大于等于后一个区间的开始年龄，则有重叠
        if (ranges[i].end_age >= ranges[i + 1].start_age) {
          return true
        }
      }
      return false
    },
    validateInteger(event, item, key) {
      const value = event.replace(/[^0-9]/g, '')
      item[key] = value === '' ? null : parseInt(value, 10)
    },
    initParams() {
      let isAge = false
      let params = {
        group_name: this.drawerFormData.groupName,
        is_auto_group: this.drawerFormData.is_auto_group,
        age_extra: []
      }
      if (this.drawerType === 'add') {
        // 没有就随机 随机生成
        if (!this.drawerFormData.groupNo) {
          this.drawerFormData.groupNo = this.mathRand()
        }
        params.group_id = this.drawerFormData.groupNo
      }
      if (this.drawerFormData.groupConditionList.length) {
        this.drawerFormData.groupConditionList.forEach((v, index) => {
          if (v.groupConditionValue === 'age') {
            params.is_age_condition = true
            if (this.isAgeRangeInvalidOrOverlapping(v.age_extra)) {
              this.$message.error(`分组年龄区间有错误或重叠，请修改`)
              isAge = true
            } else {
              isAge = false
              params.age_extra = v.age_extra
            }
          }
        })
      }
      // if (isAge) return this.$message.error('其他分组年龄区间重叠，请修改')
      return { data: params, isAge: isAge }
    },
    saveSetting() {
      this.$refs.drawerFormDataRef.validate(valid => {
        if (valid) {
          if (this.initParams().isAge) return
          // console.log(this.initParams().data)
          if (this.drawerType === 'modify') {
            this.modifyDrawerForm()
          } else {
            this.addDrawerForm()
          }
        }
      })
    },
    // 随机生成
    mathRand() {
      let Num = ''
      for (var i = 0; i < 6; i++) {
        Num += Math.floor(Math.random() * 10)
      }
      return Num
    },
    // 添加
    async addDrawerForm() {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiCardServiceCardUserGroupAddPost(this.initParams().data))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('修改成功')
        this.updateInterFace()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改
    async modifyDrawerForm() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiCardServiceCardUserGroupModifyPost({
          id: this.drawerModifyData.id,
          ...this.initParams().data
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('修改成功')
        this.updateInterFace()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    padding: 30px;
    .el-icon-delete {
      font-size: 20px;
      color: red;
    }
    .user-group-age {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
}
</style>
