import {
  CLEAN_TYPE_LIST,
  ATTITUDE_TYPE_LIST,
  WASTE_TYPE_LIST,
  OPERATE_TYPE_LIST,
  DISINFECTION_TYPE_LIST,
  OPERATION_TYPE_LIST
} from "./constants.js"
import { deepClone } from "@/utils/index.js"
import { MEAL_TYPES } from '@/utils/constants'

// 获取名字
export const getNameByType = (type, value) => {
  let dicList = []
  console.log("getNameByType", type, value)
  switch (type) {
    case "room_clean_type":
      dicList = deepClone(CLEAN_TYPE_LIST)
      break
    case "room_attitude_type":
      dicList = deepClone(ATTITUDE_TYPE_LIST)
      break
    case "area_clean_type":
      dicList = deepClone(CLEAN_TYPE_LIST)
      break
    case "area_waste_type":
      dicList = deepClone(WASTE_TYPE_LIST)
      break
    case "oa_clean_type":
      dicList = deepClone(CLEAN_TYPE_LIST)
      break
    case "oa_operate_type":
      dicList = deepClone(OPERATE_TYPE_LIST)
      break
    case "tda_clean_type":
      dicList = deepClone(CLEAN_TYPE_LIST)
      break
    case "tda_disinfection_type":
      dicList = deepClone(DISINFECTION_TYPE_LIST)
      break
    case "operation_type":
      dicList = deepClone(OPERATION_TYPE_LIST)
      break

    default:
      break
  }
  let result = dicList.find((item) => item.value === value)
  if (result) {
    return Reflect.has(result, "label") ? result.label : ""
  }
  return ""
}

// 获取餐段名称
export const getMealTypeName = (type) => {
  let dicList = deepClone(MEAL_TYPES)
  let result = dicList.find((item) => item.value === type)
  if (result) {
    return Reflect.has(result, "label") ? result.label : ""
  }
  return ""
}

// 根据列表获取人员名称
export const getPersonNameByList = (list) => {
  if (!list || list.length === 0) {
    return ""
  }
  let result = list.map((item) => {
    let name = item.name
    let identityTypeAlias = item.identity_type_alias
    return name + (identityTypeAlias ? `(${identityTypeAlias})` : "")
  })
  let strList = result.join("、")
  return strList
}
