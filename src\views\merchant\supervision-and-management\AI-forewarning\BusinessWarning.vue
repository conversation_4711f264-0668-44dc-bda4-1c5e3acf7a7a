<template>
  <div class="to-do-list container-wrapper">
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div class="p-t-20 p-b-20 flex flex-col">
          <el-skeleton-item variant="div" style="width: 96px; height: 38px;" class="m-b-20" />
          <el-skeleton-item variant="div" style="height: 132px;" class="m-b-20" />
          <el-skeleton-item variant="div" style="height: 404px;" class="m-b-20" />
        </div>
      </template>
      <template>
        <div class="p-t-20 p-b-20">
          <el-button :class="[type === 0 ? 'ps-origin-btn' : '']" @click="changeType(0)" v-if="isShowTab">风险预测</el-button>
          <el-button :class="[type === 1 ? 'ps-origin-btn' : '']" @click="changeType(1)" v-if="isShowTab">经营预警</el-button>
          <el-button :class="[type === 2 ? 'ps-origin-btn' : '']" @click="changeType(2)" v-if="isShowTab">物资风险</el-button>
          <!-- <el-button :class="[type === 3 ? 'ps-origin-btn' : '']" @click="changeType(3)" v-if="isShowTab">采购质量</el-button> -->
          <el-button :class="[type === 4 ? 'ps-origin-btn' : '']" @click="changeType(4)" v-permission="[`background_fund_supervision.warn_manage.doc_cont_list`]">证件合同</el-button>
          <el-button :class="[type === 5 ? 'ps-origin-btn' : '']" @click="changeType(5)" v-if="isShowTab">其他预警</el-button>
        </div>
        <div>
          <risk-prediction v-if="type === 0" :isShow.sync="isShowTab"></risk-prediction>
          <operation-warning v-else-if="type === 1" :isShow.sync="isShowTab"></operation-warning>
          <material-risk v-else-if="type === 2" :isShow.sync="isShowTab"></material-risk>
          <!-- <purchasing-quality v-else-if="type === 3"></purchasing-quality> -->
          <document-contract v-else-if="type === 4"></document-contract>
          <other-warning v-else-if="type === 5"></other-warning>
          <div v-else>
            <el-empty description="暂无权限"></el-empty>
          </div>
        </div>
      </template>
    </el-skeleton>

  </div>
</template>

<script>
import { to } from '@/utils'
import DocumentContract from './components/DocumentContract.vue'
import MaterialRisk from './components/MaterialRisk.vue'
import OperationWarning from './components/OperationWarning.vue'
import RiskPrediction from './components/RiskPrediction.vue'
import OtherWarning from './components/OtherWarning.vue'
// import PurchasingQuality from './components/PurchasingQuality.vue'

export default {
  components: {
    DocumentContract,
    MaterialRisk,
    OperationWarning,
    // PurchasingQuality,
    RiskPrediction,
    OtherWarning
  },
  data() {
    return {
      type: 0,
      isShowTab: false,
      loading: false
    }
  },
  created() {
    this.isSuperViseOrg()
  },
  watch: {
    '$route'(to, from) {
      this.isSuperViseOrg()
    }
  },
  methods: {
    changeType(index) {
      this.type = index
    },
    async isSuperViseOrg() {
      this.loading = true
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionEarlyWarningGetIsSuperviseOrgPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        this.loading = false
        this.isShowTab = res.data
        if (this.isShowTab) {
          switch (this.$route.query.date_type) {
            case "business": {
              this.changeType(1)
              break
            }
            case "doc_cont": {
              this.changeType(4)
              break
            }
            case "materials": {
              this.changeType(2)
              break
            }
            case "other": {
              this.changeType(5)
              break
            }
          }
        } else {
          this.changeType(4)
        }
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.to-do-list {
  .table-type {
    padding: 20px 0px;
  }
}
</style>
