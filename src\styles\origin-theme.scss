@import './mixin.scss';
$origin: #ff9b45;
$origin-hover: #ffa558;
$origin-active: #e58b3e;
$red: #fd594e;
$red-hover: #fd6a60;
$red-active: #e35046;
$blue: #229bff;
$blue-hover: #39a5ff;
$blue-active: #1f8be5;
$purple: #5d6cfa;
$purple-hover: #6e7bfb;
$purple-active: #5461e1;
$green: #14ce84;
$green-hover: #2cd391;
$green-active: #12b977;
$warn: rgb(251,89,77);
$shallow-purple: #cc94ff;
$shallow-purple-hover: #cc94ff;
$shallow-purple-active: #cc94ff;
$min-btn: 90px;
$left: 10px;
$right: 10px;
$width: 180px;
$report-w: 180px;
$btn-w: 120px;

$btn-color: (
  "blue": ('default':$blue, 'hover': $blue-hover, 'active':$blue-active),
  "purple": ('default':$purple, 'hover': $purple-hover, 'active':$purple-active),
  "green": ('default':$green, 'hover': $green-hover, 'active':$green-active),
  "origin": ('default':$origin, 'hover': $origin-hover, 'active':$origin-active),
  "red": ('default':$red, 'hover': $red-hover, 'active':$red-active),
  "shallow-purple": ('default':$shallow-purple, 'hover': $shallow-purple-hover, 'active':$shallow-purple-active),
);

/*清除浮动*/
.clearfix:after {
  display: block;
  clear: both;
  content: '';
  visibility: hidden;
  height: 0;
}
.clearfix {
  zoom: 1;
}

@mixin origin-border {
  border-color: $origin !important;
  input{
    &:focus{
      border-color: $origin !important;
    }
  }
  .el-input.is-focus .el-input__inner {
    border-color: $origin !important;
  }
}
@mixin btn-img($img) {
  color: transparent !important;;
  background-image: url($img) !important;;
  background-repeat: no-repeat !important;;
  background-size: inherit !important;;
  background-position: center !important;;
}

@mixin btn($color) {
  min-width: $min-btn;
  color: #fff;
  background: $color;
  border: none;
  &:hover{
    background: #{'$'}#{$color}#{'-hover'};
  }
  &:active{
    color: #fff;
    background: $color+'-active';
  }
  &:focus{
    color: #fff;
    background: $color+'-active';
  }
  &.is-disabled{
    opacity: .5;
  }
}

// 全局css

.ps-flex{
  display: flex;
}
.ps-left {
  margin-left: $left !important;
}
.ps-margin-r{
  margin-right: $right;
}
.ps-float-r {
  float: right;
}
.ps-float-l {
  float: left;
}
.ps-center{
  text-align: center;
}


// popper-class

// el-select 选中颜色
.ps-popper-select.el-select-dropdown {
  .selected{
    @include font_color($font-color-theme);
  }
  &.is-multiple .el-select-dropdown__item.selected{
    @include font_color($font-color-theme);
  }
  .el-select-dropdown__item.selected{
    @include font_color($font-color-theme);
  }
}
// cascader 
.ps-popper-cascader{
  .el-cascader-menu{
    // width: $width;
    // min-width: $width;
    // max-width: 230px;
  }
  &.ps-report-w{
    .el-cascader-menu{
      // width: $report-w;
      // min-width: $report-w;
      // min-width: 240px;
    }
  }
  .el-radio__input.is-checked .el-radio__inner, .el-checkbox__input.is-checked .el-checkbox__inner{
    border-color: $origin;
    background: $origin;
  }
  .el-radio__inner:hover, .el-checkbox__inner:hover{
    border-color: $origin;
  }
  .is-selectable.in-checked-path{
    color: $origin;
  }
  .el-checkbox__input.is-focus .el-checkbox__inner {
    border-color: $origin;
  }
  .el-cascader-node.in-active-path, .el-cascader-node.is-active, .el-cascader-node.is-selectable.in-checked-path{
    color: $origin;
  }
}

.ps-dialog {
  border-radius: 10px !important;
  .el-dialog__title{
    font-weight: 600;
  }
  .el-dialog__close{
    color: #cccccc;
    font-size: 20px;
    font-weight: 600;
  }
  .el-dialog__headerbtn:focus .el-dialog__close, .el-dialog__headerbtn:hover .el-dialog__close {
    color: $origin;
  }
  .ps-dialog-down{
    display: flex;
    margin-bottom: 15px;
    .ps-down-title{
      display: inline-block;
      width: 30px;
      height: 30px;
      margin: 0 8px;
      text-align: center;
      line-height: 30px;
      color: #777c81;
      font-size: 15px;
      border-radius: 50%;
      background-color: #edf2f8;
    }
    .ps-down-text{
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      .ps-down-text-l{
        display: inline-block;
        min-width: 120px;
      }
    }
  }
  .ps-table-sum{
    margin: 0 10px 15px;
    text-align: right;
    line-height: 45px;
    color: $origin;
    background-color: #fff5ec;
    .ps-table-sum-span{
      display: inline-block;
      min-width: 150px;
      margin: 0 5px;
      text-align: center;
    }
  }
}
.ps-picker{
  &.el-range-editor.is-active, .el-range-editor.is-active:hover{
    @include border-color($background-color-theme);
  }
  .el-input__inner:focus{
    @include border-color($background-color-theme);
  }
}
.ps-poper-picker{
  .el-date-table td.today span{
    // color: #ffffff ;
  }
  .el-date-table td.end-date span, .el-date-table td.start-date span{
    color: #ffffff !important;
    @include bg-color($background-color-theme);
  }
  .el-date-table td.current:not(.disabled) span{
    color: #ffffff !important;
    @include bg-color($background-color-theme);
  }
 
  .el-date-table td.available:hover{
    @include font_color($font-color-theme);
  }
  .el-picker-panel__shortcut:hover,.el-picker-panel__icon-btn:hover{
    @include font_color($font-color-theme);
  }
  .el-button.is-plain:focus, .el-button.is-plain:hover {
    background: #FFF;
    @include border-color($background-color-theme);
    @include font_color($font-color-theme);
  }
  .el-button--text {
    color: #909090;
    &:hover{
      color: #505050;
    }
  }
  .el-input.is-active .el-input__inner, .el-input__inner:focus{
    border-color: $origin;
    @include border-color($background-color-theme);
  }
  .el-time-panel__btn.confirm{
    color: $origin;
    @include font_color($font-color-theme);
  }
}

.el-date-editor .el-range-separator{
  width: 6%!important;
}

.ps-tree-checkbox{
  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
    background-color: $origin;
    border-color: $origin;
  }
  .el-checkbox__inner:hover{
    @include origin-border;
  }
}

.ps-pagination{
  .el-pagination.is-background .el-pager li:not(.disabled).active{
    @include bg_color($background-color-theme);
    color: #FFF;
  }
  .el-pagination.is-background .el-pager li:not(.disabled):hover{
    @include font_color($font-color-theme);
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active:hover{
    color: #FFF !important;
  }
  .el-pagination__sizes .el-input .el-input__inner:hover{
    @include border_color($font-color-theme);
  }
  .el-pagination__jump .el-input__inner:focus{
    @include font_color($font-color-theme);
    @include border_color($font-color-theme);
  }
  .el-select .el-input.is-focus .el-input__inner{
    @include border_color($font-color-theme);
  }
}

.ps-confirm{
  width: 360px !important;
  border-radius: 10px !important;
  .el-message-box__title{
    font-weight: 600;
    color: #717171;
  }
  .el-message-box__container{
    margin: 15px 0;
    .el-message-box__message{
      font-size: 15px;
    }
  }
  .el-message-box__close{
    color: #cccccc;
    font-size: 20px;
    font-weight: 600;
  }
  .el-message-box__headerbtn:focus .el-message-box__close, .el-message-box__headerbtn:hover .el-message-box__close{
    color: $origin;
  }
  .el-message-box__btns{
    margin: 10px 0;
    .el-button{
      padding: 11px 15px;
    }
    .ps-cancel-btn {
      // min-width: $min-btn;
      color: rgb(160,163,168);
      background: rgb(238, 243, 249);
      border: none;
      &:hover{
        color: #a0a3a8;
        background: #f3f3f3;
      }
      &:active{
        color: rgb(160,163,168);
        background: rgb(238, 243, 249);
      }
      &.is-disabled{
        opacity: .5;
      }
    }
    .ps-btn{
      // min-width: $min-btn;
      color: #fff;
      @include btn_color($background-color-theme);
      border: none;
      margin-left: 16px;
      &:hover{
        background: #f3b16c;
      }
      &:active{
        color: #fff;
        background: $origin;
      }
      &.is-disabled{
        opacity: .5;
      }
    }
    .ps-warn{
      // min-width: $min-btn;
      color: #fff;
      background: $warn;
      border: none;
      margin-left: 16px;
      &:hover{
        background: $warn;
      }
      &:active{
        color: #fff;
        background: $warn;
      }
      &.is-disabled{
        opacity: .5;
      }
    }
  }
}
// 卡务专用btn
// .ps-blue-btn{
//   width: $btn-w;
//   @include btn-img('./assets/img/btn1.png');
//   &:hover{
//     @include btn-img('./assets/img/btn1_hover.png');
//   }
//   &:active{
//     @include btn-img('./assets/img/btn1_pressed.png');
//   }
//   &.is-disabled{
//     @include btn-img('./assets/img/btn1.png');
//     opacity: .5;
//   }
// }
.ps-icon{
  display: inline-block;
  position: relative;
  width: 20px;
  height: 1px;
  vertical-align: middle;
}
.ps-icon-img{
  position: absolute;
  display: inline-block;
  left: 0;
  top: -14px;
}
.svg-icon{
  width: 20px;
  height: 20px;
  vertical-align: bottom;
}

.ps-custom-radio{
  .el-radio__input{
    .el-radio__inner{
      display: inline-block;
      position: relative;
      border: 1px solid #dcdfe6;
      border-radius: 2px;
      box-sizing: border-box;
      width: 14px;
      height: 14px;
      background-color: #fff;
      z-index: 1;
      transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
        background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
      &:after {
        box-sizing: content-box;
        content: ' ';
        border: 1px solid #fff;
        border-left: 0;
        border-top: 0;
        height: 7px;
        left: 4px;
        position: absolute;
        top: 1px;
        transform: rotate(45deg) scaleY(0);
        width: 3px;
        transition: transform 0.15s ease-in 0.05s;
        transform-origin: center;
        border-radius: 0;
        background-color: unset;
      }
    }
    &.is-checked .el-radio__inner{
      background-color: $origin;
      border-color: $origin;
      &:after {
        transform: rotate(45deg) scaleY(1);
      }
    }
    &.is-checked+.el-radio__label{
      color: $origin;
    }
  }
}

// .ps-table-header-row{
//   background-color: #f5f6fa !important;
//   th.el-table__cell {
//     background-color: #f5f6fa !important;
//   }
// }

// 局部 class
// origin-wrapper
.origin-wrapper{
  .ps-circle {
    .el-input__inner{
      border-radius: 20px;
    }
  }
  .ps-i {
    color: $origin;
    @include font_color($font-color-theme);
  }
  .ps-text {
    color: $origin;
    @include font_color($font-color-theme);
    &.el-button--text:focus, &.el-button--text:hover{
      color: $origin;
    }
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-green-text {
    color: $green;
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-black-text {
    color: #000000;
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-warn-text{
    color: $warn;
    // border-color: $warn;
    &:hover{
      color: $warn;
      opacity: .8;
      // border-color: #f7837a;
      // background: #f7837a;
    }
    &:active{
      color: #fff;
      opacity: .9;
      // background: $warn;
    }
    &:focus{
      color: $warn;
    }
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-plain-btn{
    color: #54585c;
    border-color: #e7ecf2;
    background-color: #fff;
    &:hover{
      color: #54585c;
      border-color: #e7ecf2;
      background-color: #fff;
      opacity: .8;
    }
    &:focus{
      color: #54585c;
      border-color: #e7ecf2;
      background-color: #fff;
    }
    &:active{
      color: #54585c;
      border-color: #e7ecf2;
      background-color: #fff;
    }
    &.is-disabled{
      opacity: .5;
    }
  }
  
  .ps-origin {
    color: $origin;
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-green {
    color: $green;
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-red {
    color: $red;
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-blue {
    color: $blue;
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-black {
    color: #23282d;
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-warn{
    color: $warn;
    &:hover{
      color: #f7837a;
    }
    &:active{
      color: $warn;
    }
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-cancel-btn {
    // min-width: $min-btn;
    color: rgb(160,163,168);
    background: rgb(238, 243, 249);
    border: none;
    &:hover{
      color: #a0a3a8;
      background: #f3f3f3;
    }
    &:active{
      color: rgb(160,163,168);
      background: rgb(238, 243, 249);
    }
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-warn-btn {
    min-width: $min-btn;
    color: #fff;
    background: $warn;
    border: none;
    &:hover{
      color: #fff;
      background: #f7837a;
    }
    &:active{
      color: #fff;
      background: $warn;
    }
    &.is-disabled{
      opacity: .5;
    }
  }
  .ps-btn{
    // min-width: $min-btn;
    color: #fff;
    @include btn_color($background-color-theme);
    border: none;
    &:hover{
      background: $origin-hover;
    }
    &:active{
      color: #fff;
      background: $origin-active;
    }
    &.is-disabled{
      opacity: .5;
    }
  }
  @each $key,$item in $btn-color {
    .ps-#{$key}-btn {
      // min-width: $min-btn;
      color: #fff;
      background: map-get($item, 'default');
      border: none;
      &:hover{
        background: map-get($item, 'hover');
        opacity: .8;
      }
      &:active{
        color: #fff;
        background: map-get($item, 'active');
      }
      &:focus{
        color: #fff;
        background: map-get($item, 'active');
      }
      &.is-disabled{
        color: #fff;
        background: map-get($item, 'hover');
        opacity: .5;
        &:hover{
          background: map-get($item, 'hover');
          opacity: .5;
        }
      }
    }
  }
  @each $key,$item in $btn-color {
    .ps-#{$key}-plain-btn {
      min-width: $min-btn;
      color:  map-get($item, 'default');
      border-color:  map-get($item, 'default');
      background-color: #fff;
      &:hover{
        color:  map-get($item, 'hover');
        border-color:  map-get($item, 'hover');
        background-color: #fff;
      }
      &:active{
        color: map-get($item, 'active');
        border-color: map-get($item, 'active');
        background-color: #fff;
      }
      &:focus{
        color: map-get($item, 'active');
        border-color: map-get($item, 'active');
        background-color: #fff;
      }
      &.is-disabled{
        opacity: .5;
      }
    }
  }
  .ps-warn-btn{
    min-width: $min-btn;
    color: #fff;
    background: $warn;
    border: none;
    &:hover{
      background: rgb(251,69,50);
    }
    &:active{
      color: #fff;
      background: $warn;
    }
    &.is-disabled{
      opacity: .5;
    }
  }

  .ps-sj-text{
    padding: 3px 5px;
    color: #feb072;
    background: #feefe2;
    border: none;
    border-radius: 2px;
  }
  .ps-de-text{
    padding: 3px 5px;
    color: #aad3fe;
    background: #ddf0fd;
    border: none;
    border-radius: 2px;
  }
  .ps-dz-text{
    padding: 3px 5px;
    color: #87e2b9;
    background: #dbf7ec;
    border: none;
    border-radius: 2px;
  }
  .ps-ac-text{
    padding: 3px 5px;
    color: #838efa;
    background: #e6e8fe;
    border: none;
    border-radius: 2px;
  }

  .ps-bt-text{
    padding: 3px 5px;
    color: #F56C6C;
    background: #f56c6c38;
    border: none;
    border-radius: 2px;
  }
  
  .ps-checkbox{
    .is-checked  .el-checkbox__inner{
      color: $origin;
      background-color: $origin;
      border-color: $origin;
    }
    .is-focus .el-checkbox__inner{
      border-color: $origin;
    }
    &.is-checked .el-checkbox__label{
      color: $origin;
    }
    .el-checkbox__inner:hover{
      border-color: $origin;
    }
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: $origin;
      border-color: $origin;
    }
    .el-checkbox__input.is-checked+.el-checkbox__label{
      color: $origin;
    }
  }
  .ps-radio{
    .is-checked {
      .el-radio__inner{
        color: $origin;
        background-color: $origin;
        border-color: $origin;
      }
      .el-radio__label{
        color: $origin;
      }
    }
    .is-disabled{
      opacity: .6;
    }
    .el-radio__input.is-checked+.el-radio__label{
      color: $origin;
    }
  }
  .ps-radio-btn {
    .el-radio-button__orig-radio:checked+.el-radio-button__inner{
      background-color: $origin;
      border-color: $origin;
      -webkit-box-shadow: -1px 0 0 0 $origin;
      box-shadow: -1px 0 0 0 $origin;
      color: #fff;
    }
    .el-radio-button__inner:hover {
      color: $origin;
    }
  }

  .ps-green-radio{
    .is-checked {
      .el-radio__inner{
        color: $green;
        background-color: $green;
        border-color: $green;
      }
      .el-radio__label{
        color: $green;
      }
    }
    .is-disabled{
      opacity: .6;
    }
    .el-radio__input.is-checked+.el-radio__label{
      color: $green;
    }
  }
  .ps-green-radio-btn {
    .el-radio-button__orig-radio:checked+.el-radio-button__inner{
      background-color: $green;
      border-color: $green;
      -webkit-box-shadow: -1px 0 0 0 $green;
      box-shadow: -1px 0 0 0 $green;
      color: #fff;
    }
    .el-radio-button__inner:hover {
      color: $green;
    }
  }

  .ps-bg{
    background-color: rgb(254, 168, 81);
  }
  .table-header-row {
    background: #f5f6fa;
    th{
      background: #f5f6fa;
    }
  }
  .table-header-row-span {
    background: #f5f6fa;
    th{
      background: #f5f6fa;
    }
    td{
      border-bottom-width: 0px;
      &:first-child{
        background: #fff;
      }
    }
  }
  .table-header-sum{
    background: #fff5ec;
    td{
      &:first-child{
        background: #fff;
      }
    }
    th{
      background: #fff5ec;
    }
  }
  .ps-no-border{
    td{
      border-bottom-width: 0px;
    }
    th.is-leaf{
      border-bottom: none;
    }
  }

  .warn {
    color: #ff0000a5;
  }
  .loss {
    color: #ff6600a5;
  }
  .success {
    color: #009900a5;
  }
  

  // el-input
  .ps-input {
    // @include origin-border;
    @include border_color($font-color-theme);
    // width: $width;
    .el-input__inner{
      font-family: MicrosoftYaHei;
      color: #23282d;
    }
    &.is-disabled .el-input__inner{
      color: #C0C4CC;
    }
    &.ps-report-w{
      width: $report-w;
    }
  }
  .ps-select {
    // @include origin-border;
    @include border-color($font-color-theme);
    // width: $width;
    &.ps-report-w{
      width: $report-w;
    }
    .el-select__tags-text{
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      max-width: 70px;
    }
    &.small-tags-text .el-select__tags-text{
      max-width: 35px;
    }
  }
  .ps-border{
    // width: $width;
    @include origin-border;
    .el-input .el-input__inner:focus{
      @include origin-border;
    }
    .el-input .el-input__inner:focus, .el-input.is-focus .el-input__inner {
      @include origin-border;
    }
    &.ps-report-w{
      width: $report-w;
    }
    .el-cascader__tags{
      .el-tag--info:first-child{
        // max-width: 62px;
      }
    }
  }
  .ps-tips-i-box{
    .ps-tips-item {
      display: flex;
      line-height: 1.5;
      span{
        display: inline-block;
        min-width: 50px;
        text-align: right;
      }
    }
  }
  .flat-cost-total{
    .flat-cost-list{
      position: relative;
      display: flex;
      justify-content: space-around;
      padding: 10px;
      margin-bottom: 10px;
      background-image: linear-gradient(90deg, #ffba9d 0%, #f59676 100%),
        linear-gradient(#ffffff, #ffffff);
      background-blend-mode: normal, normal;
      box-shadow: 0px 5px 8px 0px #fdeae3;
      border-radius: 4px;
      color: #9b583d;
      .flat-cost-item{
        &:first-child::before{
          content: '';
          position: absolute;
          left: 50%;
          top: 5%;
          bottom: 5%;
          transform: translateX(-50%);
          width: 1px;
          background-color: #f7bfaa;
        }
        p{
          padding: 8px 0;
          color: #9b583d;
          font-size: 20px;
        }
        .flat-cost-item-num{
          font-size: 35px;
        }
      }
    }
  }
  // 账户取款
  .ps-extract-wrapper{
    .ps-extract-bg{
      margin-bottom: 10px;
      background-image: linear-gradient(90deg, #fea548 0%, #ff8347 100%),
        linear-gradient(#ffffff, #ffffff);
      background-blend-mode: normal, normal;
      box-shadow: 0px 5px 8px 0px rgba(254, 174, 60, 0.5);
      border-radius: 4px;
      color: #fff;
      .ps-extract-label{
        padding: 10px 10px 5px 10px;
        font-size: 18px;;
      }
      .ps-extract-blance{
        text-align: right;
        font-size: 25px;
        font-weight: 600;
        padding: 5px 10px 10px;
        letter-spacing: 1px;
      }
    }
  }
  .ps-account-wrapper{
    .ps-account-bg{
      margin-bottom: 10px;
      background-image: linear-gradient(90deg, #fea548 0%, #ff8347 100%),
        linear-gradient(#ffffff, #ffffff);
      background-blend-mode: normal, normal;
      box-shadow: 0px 5px 8px 0px rgba(254, 174, 60, 0.5);
      border-radius: 4px;
      color: #fff;
      .ps-account-label{
        padding: 10px 10px 5px 10px;
        font-size: 18px;;
      }
      .ps-account-blance{
        text-align: right;
        font-size: 25px;
        font-weight: 600;
        padding: 5px 10px 10px;
        letter-spacing: 1px;
      }
    }
  }
}

.ps-btn-theme{
  @include btn_color($background-color-theme);
}
.ps-pagination-theme{
  .el-pagination.is-background .el-pager li:not(.disabled).active{
    @include bg_color($background-color-theme);
    color: #FFF;
  }
  .el-pagination.is-background .el-pager li:not(.disabled):hover{
    @include font_color($font-color-theme);
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active:hover{
    color: #FFF;
  }
  .el-pagination__sizes .el-input .el-input__inner:hover{
    @include border_color($font-color-theme);
  }
  .el-pagination__jump .el-input__inner:focus{
    @include font_color($font-color-theme);
    @include border_color($font-color-theme);
  }
  .el-select .el-input.is-focus .el-input__inner{
    @include border_color($font-color-theme);
  }
}
.ps-popper-select-theme.el-select-dropdown {
  .selected{
    @include font_color($font-color-theme);
  }
  &.is-multiple .el-select-dropdown__item.selected{
    @include font_color($font-color-theme);
  }
  .el-select-dropdown__item.selected{
    @include font_color($font-color-theme);
  }
}
.ps-el-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
  &-footer{
    z-index: 10;
    width: 100%;
    position: sticky;
    bottom: 0px;
    padding: 20px 0px;
    background-color: #fff;
  }
}

.line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}