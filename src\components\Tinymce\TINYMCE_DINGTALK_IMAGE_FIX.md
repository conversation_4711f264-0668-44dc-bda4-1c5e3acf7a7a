# TinyMCE 钉钉文档图片粘贴修复方案

## 🔍 问题描述

从钉钉文档复制图片到 TinyMCE 编辑器时遇到的问题：

1. **复杂HTML结构**：钉钉文档生成的HTML包含大量 `data-*` 属性和嵌套的 `<article>` 标签
2. **临时图片链接**：图片URL包含 `tmpCode` 参数，几个小时后会过期导致图片无法显示
3. **显示异常**：粘贴后图片可能不正常显示或格式混乱

## 🔧 修复方案

### 1. 粘贴预处理
添加了 `paste_preprocess` 配置来清理HTML结构：

```javascript
paste_preprocess: (_, args) => {
  let content = args.content;
  
  // 移除复杂的data属性
  content = content.replace(/data-[^=]*="[^"]*"/g, '');
  
  // 移除article标签，保留内容
  content = content.replace(/<\/?article[^>]*>/g, '');
  
  // 简化img标签
  content = content.replace(/<img[^>]*src="([^"]*)"[^>]*>/g, 
    '<img src="$1" style="max-width: 100%; height: auto;" />');
}
```

### 2. 临时图片检测与处理
自动检测包含 `tmpCode` 或 `alidocs.dingtalk.com` 的临时图片链接：

```javascript
// 检查是否是临时链接
if (imgSrc.includes('tmpCode=') || imgSrc.includes('alidocs.dingtalk.com')) {
  console.log('检测到临时图片链接，需要下载并上传');
  tempImages.push(imgSrc);
}
```

### 3. 图片下载与重新上传
实现了完整的图片处理流程：

- **下载图片**：使用 Canvas 和 CORS 代理下载临时图片
- **上传到服务器**：将下载的图片上传到自己的服务器
- **替换URL**：在编辑器中替换临时链接为永久链接

### 4. 关键方法

#### `downloadAndUploadImages(imageUrls)`
处理临时图片数组，逐个下载并上传

#### `downloadImage(url)` 
使用 fetch API 下载图片，支持 CORS

#### `downloadImageViaProxy(url)`
使用 Canvas 和代理服务下载图片（备用方案）

#### `uploadImageBlob(blob, filename)`
将图片 Blob 上传到服务器

#### `replaceImageUrl(oldUrl, newUrl)`
在编辑器中替换图片URL

## 🎯 使用效果

### 修复前
```html
<article class="4ever-article" data-clipboard-cangjie="...">
  <img src="https://alidocs.dingtalk.com/.../image.png?tmpCode=xxx" />
</article>
```

### 修复后
```html
<img src="https://your-server.com/upload/permanent-image.png" 
     style="max-width: 100%; height: auto;" />
```

## 📝 测试步骤

1. **从钉钉文档复制图片**
2. **粘贴到 TinyMCE 编辑器**
3. **观察控制台输出**：
   ```
   === 粘贴预处理 ===
   发现图片: https://alidocs.dingtalk.com/...?tmpCode=xxx
   检测到临时图片链接，需要下载并上传
   === 开始处理临时图片 ===
   处理第 1 张图片: https://alidocs.dingtalk.com/...
   图片下载成功，大小: 188090
   图片上传成功，新URL: https://your-server.com/upload/...
   图片URL替换成功
   ```

4. **验证结果**：图片显示正常，URL已替换为永久链接

## ⚠️ 注意事项

1. **CORS限制**：某些图片可能因为CORS策略无法直接下载
2. **网络延迟**：图片下载和上传需要时间，可能有短暂的加载过程
3. **文件大小**：大图片的处理时间会更长
4. **代理服务**：如果直接下载失败，会尝试使用CORS代理

## 🔄 备用方案

如果Canvas方法失败，可以考虑：
1. 使用后端代理下载服务
2. 提示用户手动重新上传图片
3. 保留原始链接但添加过期提醒

## 🔒 保存按钮防护机制

### 问题解决
为了防止用户在图片还未处理完成时就点击保存，导致保存临时链接的问题，我们添加了完整的上传状态管理：

#### 涵盖所有上传场景
1. **Base64 图片上传**：直接粘贴或拖拽的图片文件
2. **钉钉临时图片处理**：从钉钉文档复制的图片
3. **手动插入图片**：通过工具栏插入的图片

### 1. 统一上传状态管理
```javascript
// Tinymce 组件中统一管理所有上传状态
data() {
  return {
    uploadingCount: 0,        // base64图片上传计数
    isProcessingImages: false, // 钉钉图片处理状态
    uploadingImages: []       // 正在处理的图片列表
  }
}

// 统一状态更新
updateUploadStatus() {
  const hasUploading = this.uploadingCount > 0 || this.isProcessingImages;
  this.$emit('image-processing-change', hasUploading);
}
```

### 2. 按钮状态管理
```javascript
// 保存按钮会根据任何图片上传状态自动禁用
<el-button
  :disabled="isLoading || isProcessingImages"
  :loading="isProcessingImages"
  type="primary"
  @click="submitForm(3)"
>
  {{ isProcessingImages ? '图片上传中...' : '仅保存' }}
</el-button>
```

### 3. 增强的提交前检查
```javascript
submitForm(startNum) {
  // 检查是否有图片正在处理
  if (this.isProcessingImages) {
    this.$message.warning('图片正在处理中，请稍后再试');
    return;
  }

  // 检查编辑器中的上传状态
  const uploadStatus = this.$refs.tinymceEditor.getUploadStatus();

  // 检查是否有图片正在上传（包括base64上传）
  if (uploadStatus.hasUploading) {
    this.$message.warning('图片正在上传中，请稍后再试');
    return;
  }

  // 检查是否还有临时图片链接
  if (uploadStatus.hasTempImages) {
    this.$message.warning('检测到临时图片链接，请等待图片处理完成后再保存');
    return;
  }
}
```

### 4. 实时状态反馈
- **按钮文字变化**：`仅保存` → `图片上传中...`
- **Loading动画**：任何上传期间显示loading状态
- **按钮禁用**：防止重复点击
- **多重警告提示**：
  - `图片正在处理中，请稍后再试`
  - `图片正在上传中，请稍后再试`
  - `检测到临时图片链接，请等待图片处理完成后再保存`

### 4. 状态监听
```javascript
// Tinymce组件会发出状态变化事件
@image-processing-change="handleImageProcessingChange"

// 父组件监听并更新状态
handleImageProcessingChange(isProcessing) {
  this.isProcessingImages = isProcessing;
}
```

这个方案确保了从钉钉文档复制的图片能够永久保存和正常显示，同时防止了用户误操作导致的临时链接保存问题。
