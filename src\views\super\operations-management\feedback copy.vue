<template>
  <div>
    <div class="container-wrapper">
      <feeback :form-setting="searchFormSetting" type="Super" :api-url="apiUrl" />
    </div>
  </div>
</template>

<script>
import { FEEBACK_LIST } from './constants'
// import { to } from '@/utils'
import feeback from "@/components/feeback"

export default {
  name: 'FeebackList',
  components: { feeback },
  data() {
    return {
      searchFormSetting: FEEBACK_LIST,
      apiUrl: 'apiBackgroundFeedbackSuperFeedbackRecordListPost'
    }
  },
  mounted() {
    // this.initLoad()
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
</style>
