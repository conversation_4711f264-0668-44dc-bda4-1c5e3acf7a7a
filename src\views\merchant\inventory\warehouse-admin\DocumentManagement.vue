<template>
  <div>
    <refresh-tool @refreshPage="refreshHandle">
      <template slot="title">
        <div class="">
          <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
            <el-radio-button v-for="tab in tabTypeList" :key="tab.value" :label="tab.value">{{ tab.label }}</el-radio-button>
          </el-radio-group>
        </div>
      </template>
    </refresh-tool>

    <div class="container-wrapper">
      <PurchaseOrder v-show="tabType === 'purchaseOrder'" ref="purchaseOrder" :showRefresh="false" />
      <!-- <InquiryOrder v-show="tabType === 'inquiryOrder'" ref="inquiryOrder" :showRefresh="false" /> -->
      <DeliveryOrder v-show="tabType === 'deliveryOrder'" ref="deliveryOrder" :showRefresh="false" />
      <ReceiptOrder v-show="tabType === 'receiptOrder'" ref="receiptOrder" :showRefresh="false" />
      <InboundOrder v-show="tabType === 'inboundOrder'" ref="inboundOrder" :showRefresh="false" />
      <OutboundOrder v-show="tabType === 'outboundOrder'" ref="outboundOrder" :showRefresh="false" />
      <SettlementOrder v-show="tabType === 'settlementOrder'" ref="settlementOrder" :showRefresh="false" />
      <ApplyOrder v-show="tabType === 'applyOrder'" ref="applyOrder" :showRefresh="false" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import PurchaseOrder from './purchase-order/PurchaseOrderList'
import InboundOrder from './InboundOrder/InboundOrder'
import OutboundOrder from './OutboundOrder/OutboundOrder'
import DeliveryOrder from './DeliveryOrder'
import ReceiptOrder from './ReceiptOrder'
import SettlementOrder from './SettlementOrder'
import InquiryOrder from './inquiry-order/InquiryOrderList'
import ApplyOrder from './ApplyOrder'

export default {
  name: 'ReceiptManagement',
  components: {
    PurchaseOrder,
    InboundOrder,
    OutboundOrder,
    DeliveryOrder,
    ReceiptOrder,
    SettlementOrder,
    InquiryOrder,
    ApplyOrder
  },
  data() {
    return {
      tabType: 'purchaseOrder',
      tabTypeList: [], // 当前有权限的tab
      allTabTypeList: [ // 所有tab
        {
          label: '采购单',
          value: 'purchaseOrder',
          permissions: ['background_drp.purchase_info.list'] // 不走权限的就注释掉
        },
        // {
        //   label: '询价单',
        //   value: 'inquiryOrder',
        //   permissions: ['background_drp.inquiry.list']
        // },
        {
          label: '配送单',
          value: 'deliveryOrder'
          // permissions: []
        },
        {
          label: '收货单',
          value: 'receiptOrder'
          // permissions: []
        },
        {
          label: '入库单',
          value: 'inboundOrder',
          permissions: ['background_drp.entry_info.list']
        },
        {
          label: '出库单',
          value: 'outboundOrder',
          permissions: ['background_drp.exit_info.list']
        },
        {
          label: '结算单',
          value: 'settlementOrder'
          // permissions: []
        },
        {
          label: '申请单',
          value: 'applyOrder'
          // permissions: []
        }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'allPermissions'
    ])
  },
  watch: {

  },
  created() {
    this.ininPermissions()
  },
  mounted() {
  },
  methods: {
    // 初始化下页面权限
    ininPermissions() {
      let tabList = []
      this.allTabTypeList.forEach(tabItem => {
        let hasPermission = true
        if (tabItem.permissions) {
          hasPermission = tabItem.permissions.some(v => this.allPermissions.includes(v))
        }
        if (hasPermission) {
          tabList.push(tabItem)
        }
      })
      if (this.$route.query.tabType) {
        this.tabType = this.$route.query.tabType
      } else {
        this.tabType = tabList.length > 0 ? tabList[0].value : ''
      }
      this.tabTypeList = tabList
      this.$nextTick(_ => {
        this.changeTabHandle()
      })
    },
    refreshHandle() {
      if (this.$refs[this.tabType]) {
        this.$refs[this.tabType].refreshHandle()
      }
    },
    changeTabHandle(e) {
      this.replaceHash()
      if (this.$refs[this.tabType]) {
        this.$refs[this.tabType].initLoad()
      }
    },
    // 保存下参数
    replaceHash() {
      this.$router.replace({
        name: "DocumentManagement",
        query: {
          tabType: this.tabType,
          warehouse_id: this.$route.query.warehouse_id,
          warehouse_name: this.$route.query.warehouse_name
        }
      })
    },
  }
};
</script>

<style scoped lang="scss">

</style>
