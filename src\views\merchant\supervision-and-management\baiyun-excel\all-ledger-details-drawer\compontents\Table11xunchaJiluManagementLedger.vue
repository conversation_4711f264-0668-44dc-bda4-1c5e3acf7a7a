<template>
  <!-- 表11-巡检记录-详情 -->
  <div class="content" v-loading="isLoading">
    <div v-if="detailInfo">
      <div class="m-b-10">
        <span>复核人：{{ detailInfo.confirmer || '--' }}</span>
      </div>
      <div v-for="(item,index) in detailInfo.data_list" :key="index" class="item-info">
       <div class="m-b-10">
        <span>巡检项目：</span>
        <span class="item-item">{{ item.inspection_item }}</span>
       </div>
       <div>
        <span>巡检结果：</span>
        <span>{{ item.success ? '合格' : '不合格' }}</span>
       </div>
      </div>
    </div>
  </div>
</template>
<script>
import { to } from '@/utils'
export default {
  name: 'Table11xunchaJiluManagementLedger',
  props: {
    ledgerId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      isLoading: false,
      detailInfo: null
    }
  },
  mounted() {},
  watch: {
    // 监听 ledgerId 变化
    ledgerId: {
      handler(newVal) {
        if (newVal) {
          this.getLedgerReviewDetail(newVal)
        }
      },
      immediate: true // 立即执行
    }
  },
  methods: {
    // 获取详情通用接口
    async getLedgerReviewDetail(id) {
      this.isLoading = true
      let [err, res] = await to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyLedgerReviewDetail({ id })
      )
      if (err) {
        this.$message.error(err.message)
        this.isLoading = false
        return
      }
      if (res.code === 0) {
        this.detailInfo = res.data
      } else {
        this.$message.error(res.msg)
      }
      this.isLoading = false
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 0 10px;
}
.color-999{
  color: #999;
}
.item-info{
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 20px;
  background-color: #F2F2F2;
  .item-item{
    word-break: break-all; /* 关键属性：强制英文、数字换行 */
    overflow-wrap: break-word; /* 备用属性：长单词换行 */
    white-space: normal; /* 确保允许换行 */
  }
}
</style>
