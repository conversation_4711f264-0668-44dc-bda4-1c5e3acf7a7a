<template>
  <!-- 晨检记录-->
  <div class="PropertyInfo container-wrapper">
    <div class="tab-box">
      <el-radio-group v-model="tabType" class="ps-radio-btn">
        <el-radio-button
          v-for="tab in tabTypeList"
          :key="tab.value"
          :label="tab.value"
          v-permission="[tab.permissions]"
        >
          {{ tab.label }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <property-info  v-if="tabType=== 'info'"/>
    <property-details  v-if="tabType=== 'details'"/>
    <property-statistics  v-if="tabType=== 'statistics'"/>
  </div>
</template>

<script>
import PropertyInfo from './components/PropertyInfo.vue'
import PropertyDetails from './components/PropertyDetails.vue'
import PropertyStatistics from './components/PropertyStatistics.vue'

export default {
  components: {
    PropertyInfo,
    PropertyDetails,
    PropertyStatistics
  },
  name: 'property-admin-info',
  data() {
    return {
      tabType: 'info',
      tabTypeList: [
        { label: '资金信息表', value: 'info', permissions: 'background_fund_supervision.asset.asset_info_list' },
        { label: '资产明细表', value: 'details', permissions: 'background_fund_supervision.asset.asset_info_details_summary_list' },
        { label: '资产统计表', value: 'statistics', permissions: 'background_fund_supervision.asset.asset_info_statistics_list' }
      ]
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.PropertyInfo {
  padding-top: 20px;
}
</style>
