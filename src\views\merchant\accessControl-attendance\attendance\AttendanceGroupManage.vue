<template>
  <div class="AttendanceGroupManage container-wrapper">
    <div class="table-type">
      <div
        :class="['table-type-btn', tableType === 'manage' ? 'active-btn' : '']"
        @click="changeTableType('manage')"
        v-permission="['background_attendance.group.list']"
      >
        考勤组管理
      </div>
      <div
        :class="['table-type-btn', tableType === 'administrator' ? 'active-btn' : '']"
        @click="changeTableType('administrator')"
        v-permission="['background_attendance.attendance_group_admin.list']"
      >
        考勤组管理员
      </div>
    </div>
    <AttendanceGroupAdmin v-if="tableType === 'manage'" />
    <GroupAdministrators v-if="tableType === 'administrator'" />
  </div>
</template>

<script>
import { debounce } from '@/utils'
import GroupAdministrators from './components/GroupAdministrators'
import AttendanceGroupAdmin from './components/AttendanceGroupAdmin'
export default {
  components: { GroupAdministrators, AttendanceGroupAdmin },
  name: 'MerchantAttendanceGroupManage',
  data() {
    return {
      tableType: 'manage'
    }
  },
  created() {},
  mounted() {},
  methods: {
    initLoad() {},
    changeTableType(type) {
      this.tableType = type
    },

    // 节下流咯
    searchHandle: debounce(function() {}, 300)
  }
}
</script>

<style lang="scss" scoped>
.AttendanceGroupManage {
  .table-type {
    padding: 20px 0;
    display: flex;
    font-size: 16px;
    .table-type-btn {
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      background-color: #e8f0f8;
      border-radius: 40px;
      margin-right: 20px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn {
      color: #fff;
      background-color: #ff9b45;
      border: none;
    }
  }
}
</style>
