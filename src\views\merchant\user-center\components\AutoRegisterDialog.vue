<template>
  <!-- 添加/编辑 -->
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :width="width"
    :footerCenter="true"
    class="FormDialog"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="80px" :size="formSize">
      <div v-if="type === 'approve' || type === 'mul_approve'">
        <el-form-item label="所属组织" class="" prop="orgIds">
          <organization-select
            class="w-260 ps-input"
            v-model="formData.orgIds"
            placeholder="请选择"
            :clearable="true"
            :multiple="false"
            :checkStrictly="true"
            :isLazy="false"
            :collapse-tags="true"
            :append-to-body="true"
            filterable
            @change="changeOrgHandle"
            >
          </organization-select>
        </el-form-item>
        <el-form-item label="所属分组" class="" prop="groupId">
          <el-select
            v-model="formData.groupId"
            class="ps-select w-260"
            popper-class="ps-popper-select"
            filterable
            :disabled="!formData.orgIds"
          >
            <el-option
              v-for="item in groupList"
              :key="item.id"
              :label="item.group_name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属部门" class="" prop="departmentId">
          <select-tree
            v-model="formData.departmentId"
            :treeData="departmentList"
            :treeProps="treeProps"
            :multiple="false"
            :isLazy="false"
            :clearable="true"
            :checkStrictly="true"
            :append-to-body="true"
            class="w-260"
            :disabled="!formData.orgIds"
            filterable
          >
          </select-tree>
        </el-form-item>
      </div>
      <el-form-item v-if="type === 'reject' || type === 'mul_reject'" label="拒绝说明" class="" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="4"
          :maxlength="100"
          show-word-limit
          placeholder=""
        >
        </el-input>
      </el-form-item>
    </el-form>
  </dialog-message>
  <!-- end -->
</template>

<script>
// 草稿箱
import { deepClone } from '@/utils'
import OrganizationSelect from '@/components/OrganizationSelect'
import SelectTree from '@/components/SelectTree'
// import { integer } from '@/utils/validata'

export default {
  name: 'AutoRegisterDialog',
  components: { OrganizationSelect, SelectTree },
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'approve' // approve: 审批，reject: 拒绝
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '460px'
    },
    formSize: {
      type: String,
      default: 'medium'
    },
    infoData: {
      type: Object,
      default() {
        return {}
      }
    },
    // 调用接口的默认参数
    params: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  // mixins: [activatedLoadData],
  data() {
    // const validateCount = (rule, value, callback) => {
    //   if (value) {
    //     if (!integer(value)) {
    //       callback(new Error('格式错误'))
    //     } else {
    //       callback()
    //     }
    //   } else {
    //     callback()
    //   }
    // }
    return {
      isLoading: false,
      selectList: [],
      formData: {
        orgIds: '', // 组织
        groupId: '', // 分组
        departmentId: '', // 部门
        reason: '' // 拒绝原因
      },
      formRules: {
        orgIds: [{ required: true, message: '请选择组织', trigger: 'change' }],
        reason: [{ required: true, message: '请输入拒绝原因', trigger: 'change' }]
      },
      // 分组
      groupList: [],
      // 部门
      departmentList: [],
      treeProps: {
        value: 'id',
        label: 'group_name',
        isLeaf: 'is_leaf',
        children: 'children_list'
      },
      remoteLoading: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    },
    groupParams() {
      return {
        organization: this.formData.cardUserGroupIds
      }
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    // 初始化
    async init() {
      // 初始化
      this.formData = {
        orgIds: '', // 组织
        groupId: '', // 分组
        departmentId: '', // 部门
        reason: '' // 拒绝原因
      }
    },
    clickConfirmHandle() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          // this.sendDataHandle(this.formatParams())
          let formParams = {}
          switch (this.type) {
            case 'approve':
              formParams = {
                source_organization_id: this.formData.orgIds,
                payer_group_id: this.formData.groupId || undefined,
                payer_department_group_id: this.formData.departmentId || undefined
              }
              break;
            case 'mul_approve':
              formParams = {
                source_organization_id: this.formData.orgIds,
                payer_group_id: this.formData.groupId || undefined,
                payer_department_group_id: this.formData.departmentId || undefined
              }
              break;
            case 'reject':
              formParams.reject_reason = this.formData.reason
              break;
            case 'mul_reject':
              formParams.reject_reason = this.formData.reason
              break;
          }
          // this.$emit('update', this.formData)
          this.sendFormData(formParams)
        } else {
          this.isLoading = false
        }
      })
    },
    // 发送请求数据
    async sendFormData(data = {}) {
      let xhrApi
      if (this.type === 'approve') {
        xhrApi = 'apiBackgroundApproveApproveRegisterAgreeApprovePost'
      }
      if (this.type === 'mul_approve') {
        xhrApi = 'apiBackgroundApproveApproveRegisterBulkAgreeApprovePost'
      }
      if (this.type === 'reject' || this.type === 'mul_reject') {
        xhrApi = 'apiBackgroundApproveApproveRegisterBulkRejectApprovePost'
      }
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis[xhrApi]({
          ...data,
          ...this.params
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message || '出错了')
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('confirmForm')
        this.visible = false
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      this.$emit('cancelForm')
    },
    handlerClose(e) {
      console.log(11111, 'ChooseListDialog')
      this.isLoading = false
      if (this.$refs.formRef) {
        // 请除table选中状态
        this.$refs.formRef.resetFields()
      }
      // this.visible = false
      // this.$emit('close')
    },
    // 分组列表
    async getGroupList() {
      this.remoteLoading = true
      let params = {}
      if (this.formData.orgIds) {
        params.organization_id = this.formData.orgIds
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundApproveApproveRegisterSelectCardUserGroupPost(params))
      this.remoteLoading = false
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.groupList = res.data
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 部门列表
    async getDepartmentList() {
      this.remoteLoading = true
      let params = {}
      if (this.formData.orgIds) {
        params.organization_id = this.formData.orgIds
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundApproveApproveRegisterSelectCardDepartmentGroupPost(params))
      this.remoteLoading = false
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.departmentList = res.data
        if (this.infoData && this.infoData.payer_department_group_id) {
          this.formData.departmentId = this.infoData.payer_department_group_id
        }
      } else {
        // this.$message.error(res.msg)
      }
    },
    changeOrgHandle(e) {
      this.formData.departmentId = ''
      console.log(e)
      this.getGroupList()
      this.getDepartmentList()
    }
  }
}
</script>

<style lang="scss" scope>
.FormDialog {
  .w-260 {
    width: 260px;
  }
  .w-136 {
    width: 136px;
  }
  .w-120 {
    width: 120px;
  }
  .w-auto {
    width: auto;
  }
}
</style>
