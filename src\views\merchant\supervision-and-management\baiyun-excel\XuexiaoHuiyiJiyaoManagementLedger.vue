<template>
  <!--表24 学校食堂每月食品安全调度会议纪要 -列表 -->
  <div class="assignment-ledger-permission container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandler"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="handleExport" v-permission="['background_fund_supervision.ledger_food_safety.monthly_food_safety_dispatch_meeting_export']">导出</button-icon>
        </div>
      </div>
      <div class="table-content" style="padding-bottom: 0">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          height="calc(100vh - 570px)"
          :max-height="600"
        >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operator_username="{ row }">
              {{ getOperatorUsername(row) }}
            </template>
            <template #reviewers_list="{ row }">
              {{ getReviewersList(row.reviewers_list) }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" @click="openDetail(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[5, 10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->

      <!-- 详情 -->
      <details-drawer
        :visible.sync="isShowDrawer"
        ledgerSerialNumber="24"
        :confirmShow="false"
        :showFooter="true"
        :printShow="true"
        cancelText="关 闭"
        @print="clickPrint"
      ></details-drawer>
    </div>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import dayjs from 'dayjs'
import { SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN, TABLE_HEAD_DATA_XUE_XIAO_HUI_YI_JI_YAO_GUAN } from './constants'
import detailsDrawer from '@/views/merchant/supervision-and-management/baiyun-excel/all-ledger-details-drawer/index.vue'
import { exportMeetingRecord } from '../baiyun-excel/compontents/exportWordTable24/wordExport'
import resData from '../baiyun-excel/compontents/exportWordTable24/mockData'
export default {
  name: 'XuexiaoHuiyiJiyaoManagementLedger',
  data() {
    return {
      isShowDrawer: false, // 详情抽屉
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_XUE_XIAO_HUI_YI_JI_YAO_GUAN), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_XUE_XIAO_HUI_YI_JI_YAO_GUAN), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN) // 查询表单配置
    }
  },
  created() {
    this.initLoad()
  },
  components: {
    detailsDrawer
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerLedgerPermissionListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 查看详情
    openDetail(data) {
      this.isShowDrawer = true
    },
    // 获取提交操作员
    getOperatorUsername(row) {
      const operatorUsername = row.operator_username || ''
      const operatorMemberName = row.operator_member_name || ''
      return operatorMemberName + (operatorUsername ? `(${operatorUsername})` : '')
    },
    // 获取复核人一
    getConfirmerUsername(row) {
      const confirmerUsername = row.confirmer_username || ''
      const confirmerMemberName = row.confirmer_member_name || ''
      return confirmerMemberName + (confirmerUsername ? `(${confirmerUsername})` : '')
    },
    // 获取复核人二
    getReviewersList(reviewersList) {
      let list = []
      if (reviewersList && reviewersList.length > 0) {
        reviewersList.forEach(item => {
          list.push(item.member_name + '(' + item.username + ' )')
        })
      }
      return list.join(',')
    },
    // 打印
    clickPrint() {
      console.log('打印')
    },
    // word导出
    async handleExport() {
      this.$confirm('确认导出文档吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            // 模拟API请求 - 替换为实际API调用
            await this.$apis.apiBackgroundFundSupervisionLedgerLedgerPermissionListPost().then(() => {
              let data = resData
              // 添加日期格式化
              data.meetingDate = dayjs(data.meetingDate).format('YYYY-MM-DD')
              exportMeetingRecord(data)
            })
          } catch (error) {
            console.error('导出失败:', error)
            this.$message.error('导出失败')
          }
        })
        .catch(() => {
          this.$message.info('已取消')
        })
    }
  }
}
</script>
<style lang="scss" scoped></style>
