<template>
  <div class="permisson-manager container-wrapper">
    <!--刷新页面-->
    <refresh-tool @refreshPage="refreshPage" />
    <!--搜索层-->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandler"
      @reset="resetHandler"></search-form>
    <!--表格-->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="manageProjectSite()">项目点管理</button-icon>
          <button-icon color="origin" @click="handlerDetailKeyManager()">键值管理</button-icon>
          <button-icon color="origin" @click="handlerAddorEditRecord('add')" type="add">新增</button-icon>
        </div>
      </div>
      <div class="table-content m-t-20">
        <!-- table start -->
        <el-table v-loading="isLoading" :data="tableData" ref="tableData" style="width: 100%" height="460" stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #image="{ row }">
              <el-image class="img-item" :src="row.icon_url" fit="fit" :preview-src-list="getPreViewList(row)">
                <div slot="error" class="image-slot m-t-20">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
            <template #status="{ row }">
              <el-switch v-model="row.is_enable" active-color="#ff9b45" inactive-color="#ffcda2"
                v-loading="row.isSwitchLoading" @change="memberStatusChange($event, row, row.index)"></el-switch>
            </template>
            <template #associateUrl="{row}">
              {{ getUrlByType(row) }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text-origin"
                @click="handlerAddorEditRecord('edit', row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-text-warn" @click="handlerDeleteRecord(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination @size-change="handlerSizeChange" @current-change="handlerPageChange" :current-page.sync="currentPage"
          :page-size.sync="pageSize" :layout="'total, prev, pager, next, sizes, jumper'" :total="totalCount"></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 -->
    <member-key-or-permiss-dialog :isshow.sync="dialogVisible" :title="dialogTitle" :type="type" :dialogType="dialogType"
      ref="permissDialog" :selectInfo="selectInfo" :confirm="handlerConfirm" />
    <!-- 项目点管理弹窗 -->
    <dialog-message
      width="470px"
      title="项目点管理"
      :show.sync="manageProjectSiteDialogShow"
      customClass="expire-dialog"
      :showFooter="false"
      @close="handleClose"
    >
      <template class="expire-dialog-content">
        <el-form
          :model="manageProjectSiteForm"
          ref="manageProjectSiteForm"
          label-width="100px"
          inline
        >
        <el-form-item label="项目点名称">
          <company-select
            ref="companySelect"
            class="search-item-w ps-select"
            v-model="manageProjectSiteForm.projectList"
            :options="companyOpts"
            :collapse-tags="true"
            :clearable="true"
            :multiple="true"
            :isSelectAll="isSelectAll"
            :placeholder="'请选择'"
            @selectAll="getSelectAll"
            ></company-select>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="isSelectAll" @change="selectAll">全选</el-checkbox>
        </el-form-item>
        </el-form>
      </template>
      <template #tool>
        <div class="dialog-footer" style="margin-top: 20px; text-align: right;" >
          <el-button class="ps-cancel-btn renew-btn" @click="handleClose">取消</el-button>
          <el-button class="ps-btn" @click="confirm">确定</el-button>
        </div>
      </template>
    </dialog-message>
  </div>
</template>
<script>
import { deepClone, to, debounce } from '@/utils';
import { SEARCH_FORM_PERMISSION_MANAGER_DATA, TABLE_HEAD_PERMISSION_MANAGER_DATA } from './constants.js'
import CompanySelect from '@/components/CompanySelect'
import MemberKeyOrPermissDialog from "./components/MemberKeyOrPermissDialog"

export default {
  name: 'MemberPermissionManager',
  data() {
    return {
      searchFormSetting: deepClone(SEARCH_FORM_PERMISSION_MANAGER_DATA), // 表单数据
      tableSettings: deepClone(TABLE_HEAD_PERMISSION_MANAGER_DATA), // 表头数据
      tableData: [], // 表格数据
      isLoading: false,
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页数量
      totalCount: 0,
      dialogVisible: false, // 弹窗是否可见
      dialogTitle: '', // 弹窗标题
      dialogType: 'permission', // 弹窗类型
      type: 'add', // 类型
      selectInfo: {}, // 选择的数据
      srcList: [],
      manageProjectSiteDialogShow: false, // 控制项目点管理弹窗
      manageProjectSiteForm: {
        projectList: []
      },
      companyOpts: {
        label: 'name',
        value: 'company'
      },
      isSelectAll: false, // 控制全选
      defaultProjectList: []
    }
  },
  components: {
    MemberKeyOrPermissDialog,
    CompanySelect
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      this.getDataList()
      this.getMemberOnDetail()
    },
    // 节下流咯
    searchHandler: debounce(function () {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getDataList()
    }, 300),
    // 刷新页面
    refreshPage() {
      this.dialogVisible = false
      this.currentPage = 1;
      if (this.$refs.searchRef) {
        this.$refs.searchRef.resetForm()
      }
      this.getDataList()
    },
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.currentPage = 1;
      this.getDataList()
    },
    // 表格项目编辑
    handlerTableItemEdit(row, index, key) {
      console.log("row", row, index);
      this.$set(this.tableData[index], key, true)
    },
    // 保存
    async handlerTableItemSave(row, index, key) {
      var params = {
        id: row.id,
        remark: row.remark,
        price: row.price
      }
      const [err, res] = await to(this.$api.save(params))
      if (err) {
        return this.$message.error(err.message || '保存失败')
      }
      if (res && res.code === 0) {
        this.$message.success("保存成功")
        this.$set(this.tableData[index], key, false)
      } else {
        this.$message.error(res.msg || '保存失败')
      }
    },
    // 编辑记录
    handlerEditRecord(row) {
      console.log("handlerDetail", row);
    },
    // 跳转键值管理
    handlerDetailKeyManager() {
      this.$router.push({
        name: 'MemberKeyManager'
      })
    },
    /**
     * 分页页数change事件
     * @param {*} val
     */
    handlerPageChange(val) {
      console.log("handlerPageChange", val);
      this.currentPage = val
      this.getDataList()
    },
    /**
     * 页面条数改变监听
     */
    handlerSizeChange(val) {
      console.log("handlerSizeChange", val);
      this.pageSize = val
      this.getDataList()
    },
    // 删除记录
    handlerDeleteRecord(item) {
      var id = item.id || ''
      if (item && item.type === 'fixed') {
        return this.$message.error('亲，类型是固定模块无法进行删除！')
      }
      // 弹窗二次确认
      this.$confirm(`是否删除这条权限记录？`, '提示', {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundMemberMemberPermissionDeletePost({
                ids: [id]
              })
            )
            instance.confirmButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.searchHandler()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => { })
    },
    /**
   * 获取权限列表
   */
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundMemberMemberPermissionListPost(params))
      console.log("apiBackgroundSubMerchantInfoList", err, res);
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        var resultList = res.data.results || []
        if (Array.isArray(resultList) && resultList.length > 0) {
          // 给列表增加序号
          resultList.map((item, index) => {
            item.index = index + 1
            return item
          })
        }
        this.tableData = deepClone(resultList)
        this.totalCount = res.data.count || -1
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
   * 格式化查询参数
   * @param {} data
   */
    formatQueryParams(data) {
      console.log("data", data);
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'is_passed') {
            params[key] = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    // 新增或者编辑弹窗
    handlerAddorEditRecord(type, itemData) {
      this.type = type
      if (itemData) {
        this.selectInfo = deepClone(itemData)
      }
      this.dialogTitle = type === 'add' ? '新建' : '编辑'
      this.dialogVisible = true
    },
    // 图片预览
    getPreViewList(row) {
      var img = row.icon_url
      if (img) {
        return [img]
      }
      return []
    },
    // 确认
    handlerConfirm() {
      this.dialogVisible = false
      this.getDataList()
    },
    // 是否会员
    async memberStatusChange(val, item, index) {
      console.log("memberStatusChange", val, item, index);
      var currentIndex = index - 1 >= 0 ? index - 1 : 0
      var params = deepClone(item)
      if (params.type === 'association') {
        delete params.permission_dict
        delete params.permission_key
      } else {
        delete params.associate_url
      }
      this.$set(this.tableData[currentIndex], 'isSwitchLoading', true)
      var [err, res] = await to(this.$apis.apiBackgroundMemberMemberPermissionModifyPost(params))
      this.$set(this.tableData[currentIndex], 'isSwitchLoading', false)
      if (err) {
        this.$set(this.tableData[currentIndex], 'is_enable', !item.is_enable)
        return this.$message.error(err.message || '修改失败')
      }
      if (res && res.code === 0) {
        this.$message.success('修改成功')
      } else {
        this.$set(this.tableData[currentIndex], 'is_enable', !item.is_enable)
        this.$message.error(res.msg || '修改失败')
      }
    },
    // 根据类型获取url
    getUrlByType(row) {
      if (!row || !row.associate_url || row.type === 'fixed') {
        return ""
      } else {
        return row.associate_url
      }
    },
    // 项目点管理弹窗相关
    manageProjectSite() {
      this.manageProjectSiteForm.projectList = this.defaultProjectList
      this.manageProjectSiteDialogShow = true
    },
    handleClose() {
      this.isSelectAll = false
      this.manageProjectSiteForm.projectList = this.defaultProjectList
      this.manageProjectSiteDialogShow = false
    },
    // 获取会员功能开启设置
    async getMemberOnDetail() {
      let [err, res] = await to(this.$apis.apiBackgroundMemberMemberPermissionGetMemberOnPost())
      if (err) {
        return this.$message.error(err.message || '获取失败')
      }
      if (res && res.code === 0) {
        this.defaultProjectList = res.data.company ? res.data.company : []
        this.manageProjectSiteForm.projectList = this.defaultProjectList
      } else {
        this.$message.error(res.msg)
      }
    },
    async confirmMemberOnDetail(companyIds) {
      let params = {
        company: companyIds
      }
      let [err, res] = await to(this.$apis.apiBackgroundMemberMemberPermissionAddMemberOnPost(params))
      if (err) {
        this.$message.error(err.message || '设置失败')
      }
      if (res && res.code === 0) {
        this.$message.success('设置成功')
      } else {
        this.$message.error(res.msg)
      }
      this.manageProjectSiteDialogShow = false
    },
    confirm() {
      this.defaultProjectList = this.manageProjectSiteForm.projectList
      this.confirmMemberOnDetail(this.manageProjectSiteForm.projectList)
    },
    selectAll(e) {
      this.$refs.companySelect.getAllSelectData()
    },
    getSelectAll(e) {
      this.manageProjectSiteForm.projectList = e
    }
  }
}
</script>
<style scoped lang="scss">
.permisson-manager {
  .tag-item {
    cursor: pointer;
  }

  .color-green {
    color: #14ce84;
  }

  .img-item {
    width: 60px;
    height: 60px;
  }
}
</style>
