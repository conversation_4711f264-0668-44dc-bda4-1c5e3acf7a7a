<template>
  <!-- 表25-学校校园食品安全工作管理清单-列表 -->
  <div class="assignment-ledger-permission container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      :autoSearch="false"
      @reset="resetHandler"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="origin" @click="gotoExport" v-permission=['background_fund_supervision.ledger_food_safety.food_safety_management_checklist_export']>导出</button-icon> -->
        </div>
      </div>
      <div class="table-content" style="padding-bottom: 0">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          height="calc(100vh - 570px)"
          :max-height="600"
          @selection-change="handleOrderSelectionChange"
        >
          <!-- <el-table-column type="selection" width="55"></el-table-column> -->
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" @click="openDetail(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block" style="text-align: right">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :page-sizes="[5, 10, 20, 50, 100, 500]"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->

      <!-- 详情 -->
      <details-drawer
        :visible.sync="isShowDrawer"
        ledgerSerialNumber="25"
        :confirmShow="false"
        :showFooter="true"
        :cancelShow="true"
        :printShow="true"
        :ledgerId="ledgerId"
        dialogTitle="学校校园食品安全工作管理清单"
        cancelText="关 闭"
        size="50%"
      ></details-drawer>
    </div>
  </div>
</template>
<script>
import { debounce, to, deepClone } from '@/utils'
import { SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN, TABLE_HEAD_DATA_XUE_XIAO_HUI_YI_JI_YAO_GUAN } from './constants'
import detailsDrawer from '@/views/merchant/supervision-and-management/baiyun-excel/all-ledger-details-drawer/index.vue'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report'
export default {
  name: 'XuexiaoHuiyiJiyaoManagementLedger',
  mixins: [exportExcel, report],
  data() {
    return {
      isShowDrawer: false, // 详情抽屉
      ledgerId: '', // 台账id
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      selectedIds: [], // 导出多选
      tableSetting: deepClone(TABLE_HEAD_DATA_XUE_XIAO_HUI_YI_JI_YAO_GUAN), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_XUE_XIAO_HUI_YI_JI_YAO_GUAN), // 当前表格配置
      searchFormSetting: deepClone(SEARCH_SETTING_XUE_XIAO_HUI_YI_JI_YAO_GUAN) // 查询表单配置
    }
  },
  created() {
    const date = this.$route.query.date
    if (date) {
      this.searchFormSetting.select_time.value = [date, date]
    }
    this.initLoad()
  },
  components: {
    detailsDrawer
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getDataList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 重置页面
    resetHandler() {
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (
          data[key].value !== '' &&
          data[key].value !== null &&
          data[key].value.length !== 0 &&
          data[key].value !== 'all'
        ) {
          if (key !== 'select_time') {
            const value = data[key].value
            if (value) {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getDataList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      let [err, res] = await to(this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetSchoolFoodSafetyManagement_checklist(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        const results = data.results || []
        this.totalCount = data.count
        this.tableData = deepClone(results)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataList()
    },
    // 查看详情
    openDetail(data) {
      this.ledgerId = data.ledger_review_id
      this.isShowDrawer = true
    },
    // 多选导出
    handleOrderSelectionChange(val) {
      this.selectedIds = val.map(item => item.id)
    },
    // 导出
    async gotoExport() {
      if (this.selectedIds.length === 0) {
        this.$message.warning('请选择要导出的数据')
        return
      }
      this.$message.warning('等导出功能正在开发中...')
      return
      let ledgerNos = this.tableData.filter(item => this.selectedIds.includes(item.id)).map(item => item.ledger_no)
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount,
        ledger_nos: ledgerNos
      }
      const option = {
        type: this.printType,
        url: 'apiBackgroundFundSupervisionLedgerKitchenRangeHoodCleanExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss" scoped></style>
