<template>
  <div class="container-wrapper">
    <div class="report-wrapper">
      <h2 class="report-title">学校每周食品安全排查治理报告</h2>

      <!-- 报告周期 -->
      <div class="report-period">
        <div class="period-label">报告周期:</div>
        <div class="period-dates">
          <el-date-picker
            v-model="startDate"
            type="date"
            placeholder="开始日期"
            format="yyyy 年 MM 月 dd 日"
            value-format="yyyy-MM-dd"
            size="small"
            style="width: 160px;">
          </el-date-picker>
          <span class="date-separator">—</span>
          <el-date-picker
            v-model="endDate"
            type="date"
            placeholder="结束日期"
            format="yyyy 年 MM 月 dd 日"
            value-format="yyyy-MM-dd"
            size="small"
            style="width: 160px;">
          </el-date-picker>
          <span class="period-note">（时间段为一周）</span>
        </div>
      </div>

      <!-- 第一部分：本周排查发现问题情况 -->
      <div class="report-section">
        <div class="section-title">一、本周排查发现问题情况:</div>
        <div class="section-note">（对照每日管控清单以及本周排查发现问题，存在问题项按序号逐条填报）</div>
        <el-input
          type="textarea"
          v-model="problemsFound"
          :rows="6"
          placeholder="请填写本周排查发现的问题情况...">
        </el-input>
      </div>

      <!-- 第二部分：周排查问题整改情况和措施 -->
      <div class="report-section">
        <div class="section-title">二、周排查问题整改情况和措施:</div>
        <div class="section-note">（根据第一项填报内容逐条对应填报，明确责任人、整改期限及整改完成情况）</div>
        <el-input
          type="textarea"
          v-model="improvementMeasures"
          :rows="6"
          placeholder="请填写问题整改情况和措施...">
        </el-input>
      </div>

      <!-- 第三部分：本周食品安全管理情况评价 -->
      <div class="report-section">
        <div class="section-title">三、本周食品安全管理情况评价</div>
        <div class="evaluation-options">
          <el-radio v-model="evaluationResult" label="1">
            （一）食品安全风险可控，无较大食品安全风险隐患。
          </el-radio>
          <el-radio v-model="evaluationResult" label="2">
            （二）存在食品安全风险隐患，需尽快采取防范措施。
          </el-radio>
          <el-radio v-model="evaluationResult" label="3">
            （三）存在严重食品安全风险隐患，需尽快采取防范措施，请负责人重视。
          </el-radio>
        </div>
      </div>

      <!-- 签名区域 -->
      <div class="signature-area">
        <div class="signature-row">
          <div class="signature-label">报告人：</div>
          <div class="signature-content">
            <span>食品安全总监（1）</span>
            <el-input v-model="inspector1" class="signature-input" size="small"></el-input>
            <el-date-picker
              v-model="inspector1Date"
              type="date"
              placeholder="选择日期"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              size="small"
              style="width: 160px;">
            </el-date-picker>
          </div>
        </div>
        <div class="signature-row">
          <div class="signature-label"></div>
          <div class="signature-content">
            <span>食品安全总监（2）</span>
            <el-input v-model="inspector2" class="signature-input" size="small"></el-input>
            <el-date-picker
              v-model="inspector2Date"
              type="date"
              placeholder="选择日期"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              size="small"
              style="width: 160px;">
            </el-date-picker>
          </div>
        </div>
      </div>

      <!-- 备注 -->
      <div class="note-area">
        备注：食品安全监就学校食堂一周运营情况，如实报告食堂食品安全排查理情况。
      </div>

      <!-- 按钮区域 -->
      <div class="button-area">
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'XuexiaoShitangPingjiaManagementLedger',
  data() {
    return {
      startDate: '',
      endDate: '',
      problemsFound: '',
      improvementMeasures: '',
      evaluationResult: '1',
      inspector1: '',
      inspector2: '',
      inspector1Date: '',
      inspector2Date: ''
    };
  },
  methods: {
    submitForm() {
      console.log('表单数据:', {
        startDate: this.startDate,
        endDate: this.endDate,
        problemsFound: this.problemsFound,
        improvementMeasures: this.improvementMeasures,
        evaluationResult: this.evaluationResult,
        inspector1: this.inspector1,
        inspector2: this.inspector2,
        inspector1Date: this.inspector1Date,
        inspector2Date: this.inspector2Date
      });
      this.$message.success('保存成功');
    },
    resetForm() {
      this.startDate = '';
      this.endDate = '';
      this.problemsFound = '';
      this.improvementMeasures = '';
      this.evaluationResult = '1';
      this.inspector1 = '';
      this.inspector2 = '';
      this.inspector1Date = '';
      this.inspector2Date = '';
      this.$message.info('已重置表单');
    }
  }
};
</script>

<style lang="scss" scoped>
.report-wrapper {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .report-title {
    text-align: center;
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .report-period {
    display: flex;
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    padding: 10px;

    .period-label {
      width: 100px;
      font-weight: bold;
    }

    .period-dates {
      display: flex;
      align-items: center;

      .date-separator {
        margin: 0 10px;
      }

      .period-note {
        margin-left: 15px;
        color: #606266;
      }
    }
  }

  .report-section {
    margin-bottom: 20px;
    border: 1px solid #dcdfe6;
    padding: 10px;

    .section-title {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .section-note {
      color: #f56c6c;
      margin-bottom: 10px;
      font-size: 14px;
    }

    .evaluation-options {
      display: flex;
      flex-direction: column;

      .el-radio {
        margin-bottom: 10px;
        line-height: 1.5;
      }
    }
  }

  .signature-area {
    margin-top: 20px;

    .signature-row {
      display: flex;
      margin-bottom: 15px;

      .signature-label {
        width: 80px;
        text-align: right;
        margin-right: 10px;
      }

      .signature-content {
        display: flex;
        align-items: center;

        span {
          margin-right: 10px;
        }

        .signature-input {
          width: 150px;
          margin-right: 20px;
        }
      }
    }
  }

  .note-area {
    margin: 20px 0;
    color: #606266;
    line-height: 1.5;
  }

  .button-area {
    text-align: center;
    margin-top: 30px;
  }
}
</style>
