<template>
  <div class="ps-drawer-wrapper">
    <el-drawer
      :title="title"
      :visible.sync="drawer"
      :direction="direction"
      :custom-class="'ps-drawer ' + customClass"
      v-bind="$attrs"
      v-on="$listeners"
      :close-on-press-escape="closeOnPressEscape"
      :wrapperClosable="wrapperClosable"
    >
      <el-scrollbar :class="{'max-drawer-height': fixedFooter ? false : showFooter, 'drawer-height': showFooter ? fixedFooter : false}">
        <div class="ps-drawer-scroll"  v-loading="isLoading">
          <slot>
            <div style="height: 9000px;border: 1px solid red;"></div>
          </slot>
        </div>
      </el-scrollbar>
      <slot name="footer">
        <div v-if="showFooter" class="ps-drawer-footer" :class="[align]">
          <!-- ps-cancel-btn -->
          <el-button v-if="cancelShow" :disabled="isLoading" :class="cancelClass" @click="clickCancleHandle">{{cancelText}}</el-button>
          <el-button v-if="confirmShow" :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">{{confirmText}}</el-button>
        </div>
      </slot>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'CustomDrawer',
  props: {
    show: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: '提示'
    },
    // 打开的方向
    direction: {
      type: String,
      default: 'rtl'
    },
    // 自定义样式
    customClass: {
      type: String,
      default() {
        return ''
      }
    },
    // 是否显示底部操作按钮
    showFooter: {
      type: Boolean,
      default: true
    },
    // 固定底部
    fixedFooter: {
      type: Boolean,
      default: false
    },
    // 底部操作按钮的对其方式
    footerAlign: {
      type: String,
      default: 'left'
    },
    // 是否显示取消按钮
    cancelShow: {
      type: Boolean,
      default: true
    },
    // 是否显示确认按钮
    confirmShow: {
      type: Boolean,
      default: true
    },
    // 确定文字
    confirmText: {
      type: String,
      default: '保 存'
    },
    // 取消文字
    cancelText: {
      type: String,
      default: '取 消'
    },
    // 加载
    loading: {
      type: Boolean,
      default: false
    },
    // 取消按钮样式
    cancelClass: {
      type: String,
      default() {
        return 'ps-cancel-btn'
      }
    },
    closeOnPressEscape: {
      type: Boolean,
      default: false
    },
    wrapperClosable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {

    }
  },
  computed: {
    drawer: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    },
    isLoading: {
      get() {
        return this.loading
      },
      set(val) {
        this.$emit('update:loading', val)
      }
    },
    // 设置底部button的对其方式
    align() {
      let alignClass = 'text-right'
      switch (this.footerAlign) {
        case 'left':
          alignClass = 'text-left'
          break;
        case 'right':
          alignClass = 'text-right'
          break;
        case 'center':
          alignClass = 'text-center'
          break;
      }
      return alignClass;
    }
  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {
    // 确定事件
    clickConfirmHandle() {
      // loading状态不允许继续触发事件
      if (this.isLoading) return;
      this.$emit('confirm')
    },
    // 取消事件
    clickCancleHandle() {
      // loading状态不允许继续触发事件
      if (this.isLoading) return
      this.closeHandle()
      this.$emit('cancel')
    },
    // 关闭
    closeHandle() {
      this.drawer = false
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .ps-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
  .ps-drawer-scroll {
    padding: 10px;
    // max-height: calc( 100vh - 72px - 66px );
    // overflow-y: auto;
  }
  .el-button+.el-button {
    margin-left: 20px;
  }
  .ps-drawer-footer {
    margin: 20px 30px;
  }
  .max-drawer-height {
    .el-scrollbar__wrap {
      max-height: calc( 100vh - 72px - 66px );
    }
  }
  .drawer-height {
    .el-scrollbar__wrap {
      height: calc( 100vh - 72px - 66px );
    }
  }
//   ::-webkit-scrollbar {
//     display: none; /* 隐藏滚动条 */
// }
}
</style>
