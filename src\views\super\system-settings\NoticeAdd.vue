<template>
  <!-- 编辑与新建公告抽屉 -->
  <el-drawer
    :title="`${addorEditDrawerType === 'add' ? '新建' : '编辑'}公告`"
    :visible.sync="drawerVisible"
    direction="rtl"
    custom-class="setting-drawer"
    size="70%"
    :wrapperClosable="false"
    :show-close="false"
    @open="openedDialogHandle">
    <div id="NoticeAdd" class="drawer-content-wrapper">
      <div class="form-wrapper">
        <el-form
          :model="noticeForm"
          label-width="80px"
          :rules="noticeInfoRules"
          ref="noticeInfoForm"
        >
          <el-form-item :label="$t('form.notice_title')" prop="title">
            <el-input
              v-model="noticeForm.title"
              @input="searchHandle"
              :placeholder="$t('placeholder.notice_title')"
              class="ps-input"
              style="width:400px;"
              maxlength="40"
            ></el-input>
          </el-form-item>
          <el-form-item label="接收商户" prop="companyId">
            <!-- v-loading="loadingCompanyIdList"
            element-loading-spinner="el-icon-loading" -->
            <el-select
              ref="companyRef"
              v-model="noticeForm.companyId"
              placeholder="请输入关键词"
              multiple
              collapse-tags
              clearable
              filterable
              class="ps-input"
              style="width:400px;"
              @change="searchHandle"
              :disabled="noticeForm.isAll"
            >
              <el-option
                v-for="item in companyIdList"
                :key="item.company"
                :label="item.name"
                :value="item.company"
              ></el-option>
            </el-select>
            <el-checkbox style="margin-left: 30px;" v-model="noticeForm.isAll" @change="changeIsallHandle">全部商户</el-checkbox>
          </el-form-item>
          <el-form-item label="公告属性">
            <el-checkbox style="margin-right: 20px;" v-model="noticeForm.isImportantMsg">重要公告</el-checkbox>
            <span style="color: #999999;">发布后居中弹窗显示重要公告</span>
          </el-form-item>
          <!-- <el-form-item :label="$t('form.notice_type')" prop="type">
            <el-input
              v-model="noticeForm.type"
              @input="searchHandle"
              :placeholder="$t('placeholder.notice_type')"
              class="ps-input"
              style="width:400px;"
            ></el-input>
          </el-form-item> -->
          <el-form-item :label="$t('form.notice_content')" prop="content">
            <TinymceUeditor
              v-if="drawerVisible"
              ref="tinymceEditor"
              v-model="noticeForm.content"
              listener="focus"
              :minHeight="300"
              :maxHeight="9999"
              :tinymceFlag="tinymceFlag"
              :custom-handle="blurSelsectHandle"
              @image-processing-change="handleImageProcessingChange"
            ></TinymceUeditor>
          </el-form-item>
          <el-form-item :label="$t('form.upload_file')">
            <file-upload
              ref="faceFileRef"
              :fileList="noticeForm.fileLists"
              type="enclosure"
              prefix="notice"
              :rename="false"
              :before-upload="beforeUpload"
              :limit="9"
              :isCustomizeTemplateFile="true"
              tips="最多支持上传9个文件，总数不得超过20M"
              @fileLists="getFileLists"
            />
          </el-form-item>
          <!-- <el-form-item :label="$t('form.send_time')">
            <el-radio class="ps-radio" v-model="noticeForm.sendType" label="now">立即发布</el-radio>
            <el-radio class="ps-radio" v-model="noticeForm.sendType" label="time">定时发布</el-radio>
            <el-form-item
              v-if="noticeForm.sendType === 'time'"
              style="display: inline-block;"
              prop="sendTime"
            >
              <el-date-picker
                v-model="noticeForm.sendTime"
                :picker-options="pickerOptions"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                :placeholder="$t('placeholder.date')"
                class="ps-picker"
              ></el-date-picker>
            </el-form-item>
          </el-form-item> -->
          <!-- <el-form-item>
            <el-button
              :disabled="isLoading"
              class="ps-btn"
              style="width:300px;"
              type="primary"
              @click="submitForm"
            >
              {{ $t('form.submit') }}
            </el-button>
          </el-form-item> -->
        </el-form>
      </div>
    </div>
    <slot name="footer">
      <div class="ps-drawer-footer text-left">
        <el-button size="small" @click="closeDrawer">取消</el-button>
        <el-button
          size="small"
          :disabled="isLoading || isProcessingImages"
          :loading="isProcessingImages"
          type="primary"
          @click="submitForm(3)"
        >
          {{ isProcessingImages ? '图片上传中...' : '仅保存' }}
        </el-button>
        <el-button
          size="small"
          :disabled="isLoading || isProcessingImages"
          :loading="isLoading"
          type="primary"
          @click="submitForm(2)"
        >
          {{ isLoading ? '发布中...' : '保存并发布' }}
        </el-button>
      </div>
    </slot>
  </el-drawer>
</template>

<script>
import { to, escapeHTML, unescapeHTML } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import TinymceUeditor from '@/components/Tinymce/Tinymce.vue'
import { mapActions } from 'vuex'
export default {
  name: 'NoticeAdd',
  components: {
    TinymceUeditor
  },
  props: {
    show: {
      type: Boolean,
      required: true,
      default: false
    },
    detailInfo: {
      type: Object,
      default: () => {}
    },
    addorEditDrawerType: {
      type: String,
      default: 'add'
    },
    addorEditDrawerMsgNo: {
      type: [String, Number],
      default: ''
    }
  },
  // mixins: [activatedLoadData],
  data() {
    // var valiSendTime = (rule, value, callback) => {
    //   if (!value) {
    //     callback(new Error(this.$t('placeholder.date')))
    //   } else {
    //     if (new Date(value).getTime() < new Date().getTime()) {
    //       callback(new Error(this.$t('error.send_time')))
    //     } else {
    //       callback()
    //     }
    //   }
    // }
    const valiCompanyIds = (rule, value, callback) => {
      if (this.noticeForm.isAll) {
        callback()
      } else {
        if (!value.length) {
          callback(new Error('请选择接收商户'))
        } else {
          callback()
        }
      }
    }
    return {
      tinymceFlag: 1,
      noticeForm: {
        companyId: [],
        title: '',
        type: '',
        content: '',
        fileLists: [],
        status: 3,
        // sendType: 'now',
        // sendTime: '',
        isAll: false,
        isImportantMsg: false // 公告属性
      },
      companyIdList: [],
      noticeInfoRules: {
        companyId: [
          { required: true, validator: valiCompanyIds, trigger: 'blur' }
        ],
        title: [{ required: true, message: this.$t('placeholder.notice_type'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('placeholder.notice_title'), trigger: 'blur' }],
        content: [
          { required: true, message: this.$t('placeholder.notice_content'), trigger: 'blur' }
        ]
        // sendTime: [{ validator: valiSendTime, trigger: 'blur' }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      type: '',
      msg_id: '',
      isLoading: false,
      loadingCompanyIdList: false, // 商户公司列表加载中
      totalUploadedSize: 0, // 上传文件总大小-kb
      isProcessingImages: false // 是否正在处理图片
    }
  },
  created() {
  },
  computed: {
    drawerVisible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  mounted() {},
  methods: {
    ...mapActions({}),
    // 打开抽屉
    async openedDialogHandle() {
      if (this.$refs.noticeInfoForm) this.$refs.noticeInfoForm.clearValidate()
      await this.getCompanyList()
      this.type = this.addorEditDrawerType
      if (this.addorEditDrawerMsgNo) {
        this.msg_id = this.addorEditDrawerMsgNo
        await this.getMessagesDetails(this.msg_id)
      }
      this.$nextTick(() => {
        // this.$refs.noticeInfoForm.clearValidate()
        this.tinymceFlag++
      })
    },
    // 取消二次提示
    closeDrawer() {
      const confirmAction = () => {
        this.resetForm()
        this.drawerVisible = false
      }

      // 只有新建且有内容时才需要确认
      if (this.addorEditDrawerType === 'add' && this.hasFormContent()) {
        this.showConfirmDialog(confirmAction)
      } else {
        confirmAction()
      }
    },
    // 提取确认对话框逻辑
    showConfirmDialog(confirmCallback) {
      this.$confirm('取消将会丢失所有填写内容, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(confirmCallback)
        .catch(() => {})
    },
    // 检查表单是否有有效内容
    hasFormContent() {
      return (
        this.noticeForm.companyId.length > 0 ||
        this.noticeForm.title.trim() !== '' ||
        this.noticeForm.type.trim() !== '' ||
        this.noticeForm.content.trim() !== '' ||
        this.noticeForm.fileLists.length > 0) ||
        this.noticeForm.isAll === true ||
        this.noticeForm.isImportantMsg === true
      // (this.noticeForm.sendType === 'time' && this.noticeForm.sendTime)
    },
    // 提取重置表单逻辑
    resetForm() {
      this.noticeForm = {
        companyId: [],
        title: '',
        type: '',
        content: '',
        fileLists: [],
        // sendType: 'now',
        // sendTime: '',
        isAll: false,
        isImportantMsg: false
      }
      this.companyIdList = []
      this.$refs.faceFileRef.clearHandle() // 清除文件缓存
    },
    // 创建公告
    async messagesAdd(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminMessagesAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.drawerVisible = false
        this.$emit('updateList')
        this.resetForm()
        // this.$router.push({ name: 'SuperNoticeAdmin' })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑公告
    async messagesEdit(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminMessagesModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.drawerVisible = false
        this.$emit('updateList')
        this.resetForm()
        // this.$router.push({ name: 'SuperNoticeAdmin' })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑获取详情
    async getMessagesDetails(id) {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminMessagesDetailsPost({
          msg_no: id
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data.receiver_type === 'all_company') {
          this.noticeForm.isAll = true
        } else {
          this.noticeForm.isAll = false
          this.noticeForm.companyId = res.data.receivers_name.map(v => {
            return v.id
          })
        }
        this.noticeForm.isImportantMsg = res.data.important_msg
        this.noticeForm.title = res.data.title
        this.noticeForm.content = unescapeHTML(res.data.content)
        // this.noticeForm.sendType = 'now'
        // if (res.data.post_time) {
        // this.noticeForm.sendType = 'time'
        // this.noticeForm.sendTime = res.data.post_time
        // }
        if (res.data.resource && res.data.resource.length) {
          this.totalUploadedSize = 0
          let time = new Date().getTime()
          this.noticeForm.fileLists = res.data.resource.map(v => {
            time += 1
            let fileName = v.url.substring(v.url.lastIndexOf('/') + 1)
            // 后端加了tk校验文件，要把它截掉
            let isSplit = fileName.indexOf('?')
            if (isSplit > -1) {
              fileName = fileName.substring(0, isSplit)
            }
            this.totalUploadedSize += v.file_size
            return {
              name: fileName,
              status: "success",
              uid: time,
              url: v.url,
              size: v.file_size
            }
          })
        } else {
          this.noticeForm.fileLists = []
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取公司
    async getCompanyList() {
      this.loadingCompanyIdList = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationListPost({
        parent__is_null: '1',
        status__in: ['enable', 'disable'],
        page: 1,
        page_size: 999999
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.companyIdList = res.data.results
        this.loadingCompanyIdList = false
      } else {
        this.$message.error(res.msg)
        this.loadingCompanyIdList = false
      }
    },
    blurSelsectHandle() {
      this.$refs.companyRef.blur()
    },
    searchHandle() {},

    // 处理图片处理状态变化
    handleImageProcessingChange(isProcessing) {
      this.isProcessingImages = isProcessing;
      console.log('图片处理状态变化:', isProcessing ? '开始处理' : '处理完成');
    },
    // 发布或保存草稿公告
    submitForm(startNum) {
      // 检查是否有图片正在处理
      if (this.isProcessingImages) {
        this.$message.warning('图片正在处理中，请稍后再试');
        return;
      }

      // 检查编辑器中的上传状态
      if (this.$refs.tinymceEditor) {
        const uploadStatus = this.$refs.tinymceEditor.getUploadStatus();

        // 检查是否有图片正在上传
        if (uploadStatus.hasUploading) {
          this.$message.warning('图片正在上传中，请稍后再试');
          return;
        }

        // 检查是否还有临时图片链接
        if (uploadStatus.hasTempImages) {
          this.$message.warning('检测到临时图片链接，请等待图片处理完成后再保存');
          return;
        }
      }

      this.$refs.noticeInfoForm.validate(valid => {
        if (valid) {
          let params = {
            title: this.noticeForm.title,
            important_msg: this.noticeForm.isImportantMsg,
            status: startNum || null,
            // msg_type: this.noticeForm.type,
            content: escapeHTML(this.noticeForm.content) // 为了安全转下码
          }
          if (this.noticeForm.isAll) {
            params.receiver_type = 'all_company'
          } else {
            params.receiver_type = 'company'
            params.company_ids = this.noticeForm.companyId
          }
          if (this.noticeForm.fileLists.length > 0) {
            // params.resource = this.noticeForm.fileLists.map(v => {
            //   return v.url
            // })
            params.resource = []
            this.noticeForm.fileLists.map(v => {
              params.resource.push({
                url: v.url,
                file_size: v.size
              })
            })
          } else {
            params.resource = []
          }
          // params.use_post_time = false
          // if (this.noticeForm.sendType === 'time') {
          //   params.use_post_time = true
          //   params.post_time = this.noticeForm.sendTime
          // }
          if (this.type === 'modify') {
            params.msg_no = this.msg_id
            this.messagesEdit(params)
          } else {
            this.messagesAdd(params)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getFileLists(fileLists) {
      this.noticeForm.fileLists = fileLists
      if (fileLists.length) {
        this.totalUploadedSize = fileLists.reduce((sum, file) => {
          return sum + file.size
        }, 0)
      }
    },
    // beforeUpload(file) {
    //   let unUploadType = ['.bat', '.sh']
    //   if (unUploadType.includes(this.getSuffix(file.name))) {
    //     this.$message.error('请检查上传文件格式！')
    //     return false
    //   }
    //   const isLt20M = (file.size / 1024 / 1024) <= 20
    //   if (!isLt20M) {
    //     this.$message.error('上传附件大小不能超过 20M')
    //   }
    //   return isLt20M
    // },
    beforeUpload(file) {
      // 1. 校验文件类型
      let unUploadType = ['.bat', '.sh']
      if (unUploadType.includes(this.getSuffix(file.name))) {
        this.$message.error('请检查上传文件格式！')
        return false
      }

      // 2. 校验单个文件大小
      const isLt20M = (file.size / 1024 / 1024) <= 20
      if (!isLt20M) {
        this.$message.error('上传附件大小不能超过 20M')
        return false
      }

      // 3. 校验总文件大小
      const MAX_SIZE = 20 * 1024 * 1024 // 20MB
      const newTotalSize = this.totalUploadedSize + file.size
      if (newTotalSize > MAX_SIZE) {
        this.$message.error('附件总大小不能超过20MB')
        return false
      }
      return true
    },
    // 获取文件后缀名
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    },
    // 全部商户
    changeIsallHandle(e) {
      if (e) {
        this.noticeForm.companyIds = []
      }
      this.$refs.noticeInfoForm.validateField('companyIds');
    }
  }
}
</script>

<style lang="scss" scoped>
/* 可添加此样式使加载动画居中 */
.el-select-dropdown .el-loading-spinner {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}
#NoticeAdd {
  .form-wrapper {
    // margin: 20px;
    padding: 20px 10px;
    background-color: #fff;
    border-radius: 5px;
  }
}
.setting-drawer {
  .el-drawer__header{
    margin-bottom: 0px;
    padding-bottom: 20px;
    background-color: #f4f4f4;
  }
  .el-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .drawer-content-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
  }
  .ps-drawer-footer {
    margin-top: auto;
    padding: 20px;
    border-top: 1px solid #ebeef5;
    background: #fff;
  }
}
</style>
