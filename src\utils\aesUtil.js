import CryptoJS from 'crypto-js';

const secretKey = "X4tdVAibcUbubdbv"
const iv = "X4tdVAibcUbubdbv"

// AES 加密
export const encrypted = (data) => {
  const encryptTxt = CryptoJS.AES.encrypt(data, CryptoJS.enc.Utf8.parse(secretKey), {
    iv: CryptoJS.enc.Utf8.parse(iv), // 使用 UTF-8 格式的 IV
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7 // 使用 PKCS7 填充
  });
  return encryptTxt.toString();
}

// AES 解密
export const decrypted = (data) => {
  const cipherText = CryptoJS.AES.decrypt(data, CryptoJS.enc.Utf8.parse(secretKey), {
    iv: CryptoJS.enc.Utf8.parse(iv), // 使用 UTF-8 格式的 IV
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7 // 使用 PKCS7 填充
  })
  return cipherText.toString(CryptoJS.enc.Utf8)
}
