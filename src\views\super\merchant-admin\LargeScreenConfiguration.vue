<template>
  <div class="container-wrapper screen-config">
    <refresh-tool @refreshPage="initLoad" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <el-button
            size="mini"
            class="ps-origin-btn"
             @click="showScreenDrawer('add')"
          >
            新增
          </el-button>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #imgList="{ row }">
              <div @click="viewPicDetail(row.img_list)" class="ps-origin pointer">查看</div>
            </template>
            <template #defaultType="{ row }">
              <div>{{ row.default_type ? '是' : '否' }}</div>
            </template>
            <template #channel="{ row }">
              <el-button type="text" size="small" class="ps-text" :disabled="row.default_type" @click="getScreenChannel(row)">配置</el-button>
            </template>
            <template #status="{ row }">
              <el-switch v-model="row.show_type" @change="changeShowType(row)"></el-switch>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showScreenDrawer('edit', row)">编辑</el-button>
              <el-button type="text" size="small" class="ps-warn-text" @click="deleteHandle(row)">删除</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="drawerType === 'add' ? '新增数据大屏' : '编辑数据大屏'"
        :visible="screenDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="screenFormRef" :model="screenForm" label-width="80px" label-position="right">
            <el-form-item :label="'大屏名称'" prop="name" :rules="[{ required: true, message: '请输入大屏名称', trigger: ['change', 'blur'] }]">
              <el-input v-model="screenForm.name" class="w-300" placeholder="请输入大屏名称，不超过15个字" maxlength="30"></el-input>
            </el-form-item>
            <el-form-item :label="'链接地址'" prop="address" :rules="[{ required: true, message: '请输入链接地址', trigger: ['change', 'blur'] }]">
              <el-input type="textarea" v-model="screenForm.address" class="w-300" placeholder="请输入大屏链接地址，不超过100个字" maxlength="100" :autosize="{ minRows: 6, maxRows: 8 }" resize="none"></el-input>
            </el-form-item>
            <el-form-item :label="'展示图片'" prop="fileLists" :rules="[{ required: true, message: '请上传图片', trigger: ['change', 'blur'] }]" >
              <div class="img-from">
                <span class="tips ps-red">支持png、jpg格式，图片大小不能超过2MB</span>
                <file-upload
                  ref="pictureRef"
                  :fileList="screenForm.fileLists"
                  :limit="1"
                  type="enclosure"
                  :before-upload="beforeUpload"
                  @fileLists="getFileLists"
                  class="avatar-uploader"
                  :show-file-list="false"
                >
                  <img
                    v-if="screenForm.fileLists.length"
                    :src="screenForm.fileLists[0].url"
                    class="avatar"
                    @click="clearFileHandle"
                  />
                  <span v-else class="icon-tag">
                    <img  src="@/assets/img/add.png"/>
                    <div class="">上传图片</div>
                  </span>
                </file-upload>
              </div>
            </el-form-item>
            <el-form-item :label="'是否默认'" prop="isDefault">
              <div class="ps-flex-align-c flex-align-c">
                <el-switch v-model="screenForm.isDefault" />
                <div class="m-l-10 font-size-14" style="color: #909399;">开启为默认状态，所有渠道默认启用该大屏。</div>
              </div>
            </el-form-item>
            <el-form-item :label="'备注'" prop="remark">
              <el-input type="textarea" v-model="screenForm.remark" class="w-300" placeholder="请填写备注信息，例如：通用大屏/某渠道专用等。不超过100个字" maxlength="100" :autosize="{ minRows: 6, maxRows: 8 }" resize="none"></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('addAndEdit')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('addAndEdit')">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'适用渠道'"
        :visible="applyDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="applyFormRef" :model="applyForm" label-width="80px" label-position="right">
            <el-form-item :label="'大屏名称'" prop="name">
              <div>{{ applyForm.name }}</div>
            </el-form-item>
            <el-form-item :label="'项目地址'" prop="address">
              <el-radio-group v-model="isSelectAll" @input="changIsOpenStatus">
                <el-radio :label="true">全部适用</el-radio>
                <el-radio :label="false">全部不适用</el-radio>
              </el-radio-group>
              <div>
                <el-table
                  :show-header="false"
                  :data="applyForm.data"
                  v-loading="isLoading"
                  stripe
                  header-row-class-name="ps-table-header-row"
                  row-key="id"
                  :lazy="true"
                  :load="loadTree"
                  :tree-props="checkProps">
                  <table-column v-for="(itemIn, indexIn) in applyFormTableSetting" :key="indexIn" :col="itemIn">
                    <template #operation="{ row }">
                      <el-radio-group v-model="row.isOpen" @input="changeStatus(row)">
                        <el-radio :label="true">启用</el-radio>
                        <el-radio :label="false">不适用</el-radio>
                      </el-radio-group>
                    </template>
                  </table-column>
                </el-table>
              </div>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" type="primary" class="w-100" @click="cancelHandle('apply')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle('apply')">保存</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
    <image-view-preview :isshow.sync="isShowPreViewDialog" :title="dialogTitle" :picList="imgUrlList"
      @close="closePreviewDialog"></image-view-preview>
  </div>
</template>

<script>
import { deepClone, to, getSuffix } from '@/utils'
import ImageViewPreview from '@/components/ImageViewPreview/index.vue'

export default {
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '大屏名称', key: 'name' },
        { label: '地址', key: 'link_address' },
        { label: '展示图片', key: 'imgList', type: "slot", slotName: "imgList" },
        { label: '是否默认', key: 'default_type', type: "slot", slotName: "defaultType" },
        { label: '适用渠道', key: 'scree_channel', type: "slot", slotName: "channel" },
        { label: '可见状态（启用/禁用）', key: 'show_type', type: "slot", slotName: "status" },
        { label: '备注', key: 'remark' },
        { label: '操作时间', key: 'update_time' },
        { label: '操作人', key: 'operator_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      drawerType: '',
      selectId: '',
      screenDrawerShow: false,
      screenForm: {
        name: '',
        address: '',
        isDefault: true,
        remark: '',
        fileLists: []
      },
      isSelectAll: '',
      applyDrawerShow: false,
      screenChannel: [],
      applyForm: {
        id: '',
        name: '',
        data: []
      },
      applyFormTableSetting: [
        { label: '操作人', key: 'name', align: 'left' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      checkProps: {
        children: 'children',
        hasChildren: 'has_children'
      },
      imgUrlList: [], // 图片预览列表
      isShowPreViewDialog: false, // 图片预览弹窗
      dialogTitle: '' // 图片预览弹窗标题
    }
  },
  components: {
    ImageViewPreview
  },
  created() {
    this.initLoad()
  },
  watch: {
    applyDrawerShow(newVal) {
      if (newVal) {
        this.applyForm.data.forEach(item => {
          if (this.screenChannel.includes(item.id)) {
            item.isOpen = true
          } else {
            item.isOpen = false
          }
        })
      }
    },
    applyForm: {
      handler: function(newVal) {
        let arr = newVal.data.map(item => {
          return item.isOpen
        })
        let newArr = [...new Set(arr)]
        if (newArr.length === 2) {
          this.isSelectAll = ''
        } else if (newArr.length === 1 && newArr[0]) {
          this.isSelectAll = true
        } else {
          this.isSelectAll = false
        }
      },
      deep: true
    }
  },
  methods: {
    initLoad() {
      this.getScreenData()
      this.getChannelList()
    },
    getScreenData() {
      this.$apis.apiBackgroundFundSupervisionMonitoringScreenListPost().then(res => {
        if (res.code === 0) {
          this.tableData = deepClone(res.data.results || [])
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getChannelList(name, parentId, callback) {
      let params = {
        status__in: ['enable']
      }
      if (parentId) {
        params.parent__in = parentId
      } else {
        params.parent__is_null = '1'
      }
      if (name) {
        params.name__contains = name
      }
      this.$apis.apiBackgroundFundSupervisionSupervisionChannelListPost(params).then(res => {
        if (res.code === 0) {
          this.applyForm.data = res.data.results.map(item => {
            Object.assign(item, { isOpen: false })
            return item
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 懒加载组织结构
    async loadTree(row, treeNode, resolve) {
      let params = {
        status__in: ['enable'],
        page: 1,
        page_size: 99999
      }
      if (row && row.id) {
        params.parent__in = row.id
      } else {
        params.parent__is_null = '1'
      }
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionSupervisionChannelListPost(params))
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let arr = res.data.results.map(item => {
          Object.assign(item, { isOpen: false })
          if (this.screenChannel.includes(item.id)) {
            item.isOpen = true
          } else {
            item.isOpen = false
          }
          return item
        })
        resolve(arr)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    showScreenDrawer(type, data) {
      this.drawerType = type
      if (type === 'edit') {
        this.selectId = data.id
        this.screenForm.name = data.name
        this.screenForm.address = data.link_address
        this.screenForm.isDefault = data.default_type
        this.screenForm.remark = data.remark
        this.screenForm.fileLists = data.img_list || []
      } else {
        this.selectId = ''
        this.screenForm.name = ''
        this.screenForm.address = ''
        this.screenForm.isDefault = true
        this.screenForm.remark = ''
        this.screenForm.fileLists = []
      }
      this.screenDrawerShow = true
      setTimeout(() => {
        this.$refs.screenFormRef.clearValidate()
      }, 100)
    },
    // 抽屉的方法
    cancelHandle(type) {
      if (type === 'addAndEdit') {
        this.clearFileHandle()
        this.$refs.screenFormRef.resetFields()
        this.screenDrawerShow = false
      } else {
        this.applyDrawerShow = false
      }
    },
    saveHandle(type) {
      if (type === 'addAndEdit') {
        this.saveScreenHandle()
      } else {
        this.saveApplyHandle()
      }
    },
    saveApplyHandle() {
      let params = {
        supervision_channel_list: this.screenChannel,
        monitoring_screen_id: this.applyForm.id,
        is_all_bind: typeof this.isSelectAll !== 'boolean' ? false : this.isSelectAll
      }
      this.$apis.apiBackgroundFundSupervisionMonitoringScreenScreeChannelConfigPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('保存成功')
        } else {
          this.$message.error(res.msg)
        }
        this.applyDrawerShow = false
        this.getScreenData()
      })
    },
    saveScreenHandle() {
      this.$refs.screenFormRef.validate((valid) => {
        if (valid) {
          let params = {
            id: this.drawerType === 'add' ? undefined : this.selectId,
            name: this.screenForm.name,
            link_address: this.screenForm.address,
            default_type: this.screenForm.isDefault,
            img_list: this.screenForm.fileLists,
            remark: this.screenForm.remark
          }
          if (this.drawerType === 'add') {
            this.addScreenHandle(params)
          } else {
            this.editScreenHandle(params)
          }
          this.cancelHandle('addAndEdit')
        }
      })
    },
    addScreenHandle(params) {
      this.$apis.apiBackgroundFundSupervisionMonitoringScreenAddPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('新增成功')
          this.getScreenData()
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.screenFormRef.resetFields()
        this.screenDrawerShow = false
      })
    },
    editScreenHandle(params) {
      this.$apis.apiBackgroundFundSupervisionMonitoringScreenModifyPost(params).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
          this.getScreenData()
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.screenFormRef.resetFields()
        this.screenDrawerShow = false
      })
    },
    deleteHandle(data) {
      this.$confirm(`您正在删除数据大屏：${data.name}。删除后不可恢复，请谨慎操作。确定要删除该大屏？`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$apis.apiBackgroundFundSupervisionMonitoringScreenDeletePost({
          ids: [data.id]
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
          } else {
            this.$message.error(res.msg)
          }
          this.getScreenData()
        })
      }).catch(action => {
        this.$message('已取消删除')
      })
    },
    changeShowType(row) {
      this.$apis.apiBackgroundFundSupervisionMonitoringScreenModifyPost({
        id: row.id,
        name: row.name,
        show_type: row.show_type
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('修改成功')
          this.getScreenData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getScreenChannel(row) {
      this.applyForm.name = row.name
      this.applyForm.id = row.id
      this.screenChannel = deepClone(row.config_channel_screen)
      this.applyDrawerShow = true
    },
    changeStatus(row) {
      if (row.isOpen) {
        this.screenChannel.push(row.id)
      } else {
        let index = this.screenChannel.indexOf(row.id)
        this.screenChannel.splice(index, 1)
      }
      this.screenChannel = [...new Set(this.screenChannel)]
    },
    changIsOpenStatus() {
      this.applyForm.data.forEach(item => {
        if (this.isSelectAll) {
          item.isOpen = true
        } else {
          item.isOpen = false
        }
      })
    },
    getFileLists(fileLists) {
      this.screenForm.fileLists = fileLists
      console.log(fileLists)
    },
    beforeUpload(file) {
      let unUploadType = ['.jpg', '.png']
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 jpg/png 格式')
        return false
      }
      const isLt10M = file.size / 1024 / 1024 < 2
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
    },
    clearFileHandle() {
      this.$refs.pictureRef.clearHandle()
      this.screenForm.fileLists = []
    },
    /**
     * 查看图片大图
     * @param {*} imgUrl
     */
    viewPicDetail(imgList) {
      let imgUrl = imgList && Array.isArray(imgList) && imgList.length > 0 ? imgList[0].url : ''
      this.dialogTitle = '查看详情'
      if (imgUrl && imgUrl.length > 0) {
        var picUrl = imgUrl
        // 预览图片
        this.$set(this, 'imgUrlList', [picUrl])
        this.isShowPreViewDialog = true
      } else {
        this.$message.error('亲，没有图片喔！')
      }
      console.log("viewPicDetail", imgUrl);
    },
    // 关闭图片预览
    closePreviewDialog() {
      this.isShowPreViewDialog = false
    }
  }
}
</script>

<style lang="scss">
 .screen-config {
  .img-from {
    display: flex;
    flex-direction: column;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 320px;
    height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    .icon-tag{
      width: 60px;
      height: 60px;
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        width: 30px;
        height: 30px;
      }
    }
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .avatar {
    width: 320px;
    height: 180px;
    display: inline-block;
  }
  .el-upload {
    height: 180px;
  }
  .tips {
    line-height: 18px;
    font-size: 12px;
    color: red;
  }
 }
</style>
