<template>
  <div class="drawer-box">
    <customDrawer :show.sync="visible" :loading="isLoading" :title="'退款'" :size="800" @confirm="saveSetting">
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form
            :model="drawerFormData"
            @submit.native.prevent
            status-icon
            ref="drawerFormDataRef"
            :rules="drawerFormDataRuls"
            label-position="top"
          >
            <el-form-item label="退款原因:" prop="processReason">
              <el-input
                type="textarea"
                class="ps-input"
                size="small"
                maxlength="100"
                :autosize="{ minRows: 4, maxRows: 8 }"
                placeholder="请输入退款的原因，不超过100字"
                v-model="drawerFormData.processReason"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
import NP from 'number-precision'
export default {
  props: {
    isshow: Boolean,
    drawerModifyData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      drawerFormData: {
        processReason: ''
      },
      drawerFormDataRuls: {
        processReason: [{ required: true, message: '请输入退款原因', trigger: 'blur' }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {},
  methods: {
    closeClick() {
      this.visible = false
    },
    async refundDrawerForm() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundMemberPointsPointsOrderRefundPost({
          id: this.drawerModifyData.id,
          process_reason: this.drawerFormData.processReason,
          refund_fee: NP.times(this.drawerModifyData.origin_fee, 100),
          points: this.drawerModifyData.points
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('退款成功')
        this.$emit('clickSaveDrawer')
      } else {
        this.$message.error(res.msg)
      }
    },
    saveSetting() {
      this.$refs.drawerFormDataRef.validate(valid => {
        if (valid) {
          this.refundDrawerForm()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    position: relative;
    height: 100%;
    overflow: hidden;
    .drawer-content {
    }
    .drawer-footer {
      position: absolute;
      bottom: 20px;
      left: 20px;
      width: 100%;
      text-align: left;
    }
  }
  ::v-deep .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
}
</style>
