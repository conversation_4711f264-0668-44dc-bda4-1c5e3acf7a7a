<template>
  <div>
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="showApprovalDrawer" v-permission="['background_fund_supervision.appropriation.appropriation_apply']">拨款申请</button-icon>
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_fund_supervision.appropriation.list_export']">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #appropriationFee="{ row }">
              <div>{{ row.appropriation_fee ? computedPrice(row.appropriation_fee) : '--' }}</div>
            </template>
            <template #applyFee="{ row }">
              <div>{{ computedPrice(row.apply_fee) }}</div>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showDetailDrawer(row, 'detail')">详情</el-button>
              <el-button type="text" size="small" class="ps-text" v-if="row.appropriation_status === 'approving' && row.appropriation_status !== 'revoked'" @click="revokeHandle(row)" v-permission="['background_fund_supervision.appropriation.appropriation_revoke']">撤销</el-button>
              <el-button type="text" size="small" class="ps-text" v-if="row.appropriation_status === 'settle_pending' && row.appropriation_status !== 'revoked'" @click="showDetailDrawer(row, 'count')">结算</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="'拨款申请'"
        :visible="approvalDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="approvalDrawerFormRef" :model="approvalDrawerForm" label-width="100px" label-position="right">
            <el-form-item label="申请人" prop="applicant">
              <el-input v-model="approvalDrawerForm.applicant" class="w-300" :disabled="true"></el-input>
            </el-form-item>
            <el-form-item label="申请内容" prop="applicationContent" :rules="[{required: true, message: '申请内容不能为空', trigger: ['change', 'blur']}]">
              <el-input v-model="approvalDrawerForm.applicationContent" class="w-300" type="textarea" :autosize="{ minRows: 4, maxRows: 6}" resize="none" show-word-limit maxlength="100"></el-input>
            </el-form-item>
            <el-form-item
              label="申请金额"
              prop="amountApplied"
              :rules="[
                { required: true, message: '申请金额不能为空', trigger: ['change', 'blur']},
                { pattern: /^\d*\.?\d{0,2}$/, message: '请输入至多两位小数的数字', trigger: ['change', 'blur']}
              ]">
              <el-input v-model="approvalDrawerForm.amountApplied" class="w-300 m-r-10" type="number"></el-input>元
            </el-form-item>
            <el-form-item label="收款供应商" prop="collectingSupplier">
              <el-select v-model="approvalDrawerForm.collectingSupplier" filterable placeholder="请选择" class="w-300" @change="setSupplierInfo">
                <el-option
                  v-for="(item, index) in supplierList"
                  :key="index"
                  :label="item.recipient_bank"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="收款人户名" prop="beneficiaryAccountName" :rules="[{required: true, message: '收款人户名不能为空', trigger: ['change', 'blur']}]">
              <el-input v-model="approvalDrawerForm.beneficiaryAccountName" class="w-300"></el-input>
            </el-form-item>
            <el-form-item label="收款账号" prop="receivablesAccount" :rules="[{required: true, message: '收款账号不能为空', trigger: ['change', 'blur']}]">
              <el-input v-model="approvalDrawerForm.receivablesAccount" class="w-300"></el-input>
            </el-form-item>
            <el-form-item label="收款银行" prop="receivingBank" :rules="[{required: true, message: '收款银行不能为空', trigger: ['change', 'blur']}]">
              <el-input v-model="approvalDrawerForm.receivingBank" class="w-300"></el-input>
            </el-form-item>
            <div v-if="defaultReceivablesAccount !== approvalDrawerForm.receivablesAccount" class="red font-size-14 m-l-100 m-b-20">当前收款账号非该供应商绑定账号，请注意</div>
            <el-form-item label="申请凭证/附件" prop="applicationDocuments">
              <el-upload
                v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                :action="serverUrl" :data="uploadParams" :file-list="fileListsForApproval" :on-success="uploadSuccessForApproval"
                :before-upload="beforeFileUpload" :limit="5" :multiple="false" :show-file-list="true"
                :headers="headersOpts">
                <div class="flex-center">
                  <el-button class="m-r-20" size="small" icon="el-icon-plus">添加文件</el-button>
                  <div slot="tip" class="el-upload__tip">不超过20M</div>
                </div>
              </el-upload>
            </el-form-item>
            <el-form-item label="申请备注" prop="applicationRemarks" :rules="[{required: true, message: '申请备注不能为空', trigger: ['change', 'blur']}]">
              <el-input v-model="approvalDrawerForm.applicationRemarks" class="w-300" type="textarea" :autosize="{ minRows: 6, maxRows: 8}" resize="none" show-word-limit maxlength="200"></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button class="w-100" @click="cancelHandle('approval')">关闭</el-button>
            <el-button class="w-100 ps-origin-btn" @click="confirmHandle('approval')">确认</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="selectType === 'count' ? '结算' : '详情'"
        :visible="detailDrawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="detailDrawerFormRef" :model="detailDrawerForm" label-width="100px" label-position="top">
            <el-form-item label="申请信息">
              <table class="m-l-30">
                <tr>
                  <td style="width: 80px;">申请人：</td>
                  <td>{{ detailDrawerForm.applicationInfo.applicant }}</td>
                </tr>
                <tr>
                  <td style="width: 80px;">申请来源：</td>
                  <td>{{ detailDrawerForm.applicationInfo.applicationSource }}</td>
                </tr>
                <tr>
                  <td style="width: 80px;">申请内容：</td>
                  <td>{{ detailDrawerForm.applicationInfo.applicationContent }}</td>
                </tr>
                <tr>
                  <td style="width: 80px;">申请金额：</td>
                  <td>{{ computedPrice(detailDrawerForm.applicationInfo.amountApplied) }}</td>
                </tr>
              </table>
            </el-form-item>
            <el-form-item label="单据信息">
              <div class="m-l-30" v-if="detailDrawerForm.documentInfo.length">
                <div v-for="(item, index) in detailDrawerForm.documentInfo" :key="index">{{ item }}</div>
              </div>
              <div v-else class="m-l-30">
                <!-- <el-empty description="暂无内容" :image-size="96"></el-empty> -->
                 <span>暂无内容</span>
              </div>
            </el-form-item>
            <el-form-item label="申请凭证/附件">
              <div class="m-l-30 flex-col" v-if="detailDrawerForm.applicationDocuments.length">
                <div v-for="(item, index) in detailDrawerForm.applicationDocuments" :key="index" class="w-350 flex-b-c m-r-10 m-b-10">
                    <div class="origin">{{ item.name }}</div>
                    <div class="flex">
                      <el-button type="text" size="small" class="ps-text" v-if="computedFileType(item.name)" @click="handleClick(item.url, computedFileType(item.name))">查看</el-button>
                      <el-button type="text" size="small" class="ps-text" @click="downloadFile(item.url, computedFileType(item.name))">下载</el-button>
                    </div>
                </div>
              </div>
              <div v-else class="m-l-30">
                <!-- <el-empty description="暂无凭证/附件" :image-size="96"></el-empty> -->
                 <span>暂无凭证/附件</span>
              </div>
            </el-form-item>
            <el-form-item label="收款信息">
              <table class="m-l-30">
                <tr>
                  <td>收款人：</td>
                  <td>{{ detailDrawerForm.collectionInfo.beneficiaryAccountName }}</td>
                </tr>
                <tr>
                  <td>收款账号：</td>
                  <td>{{ detailDrawerForm.collectionInfo.receivablesAccount }}</td>
                </tr>
                <tr>
                  <td>收款银行：</td>
                  <td>{{ detailDrawerForm.collectionInfo.receivingBank }}</td>
                </tr>
              </table>
            </el-form-item>
            <el-form-item :label="'审批状态'" v-if="selectType !== 'count'">
              <el-timeline class="p-t-10 m-l-5">
                <el-timeline-item
                  :icon="item.icon"
                  :color="item.color"
                  :size="'large'"
                  v-for="(item, index) in detailDrawerForm.approvalStatus"
                  :key="index"
                  :timestamp="item.status_alias"
                  :placement="'top'">
                  <div v-for="(itemIn, indexIn) in item.data" :key="indexIn" :class="[approveMethod === 'and_approve' && index !== 0 ? 'bg-grey' : '', 'm-b-10']">
                    <!--这里做个区别，会签和其他两个区别显示-->
                    <div v-if="approveMethod !== 'and_approve'" class="flex-col">
                      <div class="w-350 flex-b-c">
                        <div>{{ itemIn.operator }}</div>
                        <div class="w-150 flex-b-c" v-if="itemIn.status !== 'PENDING'">
                          <div v-if="itemIn.status !== 'PENDING'">{{ itemIn.timestamp }}</div>
                          <i :class="itemIn.icon" :style="{'color': itemIn.color, 'fontSize': '18px'}"></i>
                        </div>
                      </div>
                      <div v-if="index > 0 && item.status !== 'REVOKE' && itemIn.reason" style="color: #000">
                        审批意见：{{ itemIn.reason }}
                      </div>
                    </div>
                    <div v-else>
                      <div v-for="(childItem, childIndex) in itemIn" :key="childIndex" class="flex-col">
                        <div class="w-350 flex-b-c">
                          <div>{{ childItem.operator }}</div>
                          <div class="w-150 flex-b-c" v-if="childItem.status !== 'PENDING'">
                            <div v-if="childItem.status !== 'PENDING'">{{ childItem.timestamp }}</div>
                            <i :class="[childItem.icon, 'icon']" :style="{'color': childItem.color, 'fontSize': '18px'}"></i>
                          </div>
                        </div>
                        <div v-if="index > 0 && childItem.status !== 'REVOKE' && childItem.reason" style="color: #000">
                          审批意见：{{ childItem.reason }}
                        </div>
                      </div>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </el-form-item>
            <el-form-item label="申请备注">
              <div class="p-l-30">
                <el-input v-model="detailDrawerForm.applicationRemarks" type="textarea" :autosize="{minRows: 6, maxRows: 8}" resize="none" show-word-limit maxlength="200" :disabled="true"></el-input>
              </div>
            </el-form-item>
            <el-form-item label="" prop="uploadBillingVoucher" :rules="[{required: true, message: '请上传结算凭证', trigger: ['change', 'blur']}]">
              <div class="flex-start">
                <div class="f-w-700 m-r-10" style="color: #606266;"><span class="red">*</span>上传结算凭证</div>
                <div>
                  <div class="m-l-30 flex-col" v-if="detailDrawerForm.uploadBillingVoucher.length && selectType !== 'count'">
                    <div v-for="(item, index) in detailDrawerForm.uploadBillingVoucher" :key="index" class="w-350 flex-b-c m-r-10 m-b-10">
                        <div class="origin">{{ item.name }}</div>
                        <div class="flex">
                          <el-button type="text" size="small" class="ps-text" v-if="computedFileType(item.name)" @click="handleClick(item.url, computedFileType(item.name))">查看</el-button>
                          <el-button type="text" size="small" class="ps-text" @click="downloadFile(item.url, computedFileType(item.name))">下载</el-button>
                        </div>
                    </div>
                  </div>
                  <el-upload
                    v-else
                    v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload" :disabled="selectType !== 'count'"
                    :action="serverUrl" :data="uploadParams" :file-list="fileListsForDetail" :on-success="uploadSuccessForDetail"
                    :before-upload="beforeFileUpload" :limit="5" :multiple="false" :show-file-list="true"
                    :headers="headersOpts">
                    <div class="flex-center">
                      <el-button class="m-r-20" size="small" icon="el-icon-plus" :disabled="selectType !== 'count'">添加文件</el-button>
                      <div slot="tip" class="el-upload__tip">不超过20M</div>
                    </div>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button :class="['w-100', selectType === 'count' ? '' : 'ps-origin-btn']" @click="cancelHandle('detail')">{{ selectType === 'count' ? '关闭' : '确认'}}</el-button>
            <el-button v-if="selectType === 'count'" class="w-100 ps-origin-btn" @click="confirmHandle('detail')">确认</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { debounce, deepClone, getRequestParams, divide, times, getToken, to } from '@/utils'
import dayjs from 'dayjs'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { mapGetters } from 'vuex'
import FileSaver from 'file-saver'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  mixins: [exportExcel],
  components: {
    ElImageViewer
  },
  data() {
    const RECENTSEVEN = [
      dayjs()
        .subtract(7, 'day')
        .format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ]
    return {
      isLoading: false,
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'apply',
          dataList: [
            {
              label: '拨款时间',
              value: 'appropriation'
            },
            {
              label: '申请时间',
              value: 'apply'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: false,
          value: RECENTSEVEN
        },
        appropriation_no: {
          type: 'input',
          label: '审批单号',
          value: '',
          placeholder: '请输入审批单号'
        },
        trade_no: {
          type: 'input',
          label: '流水单号',
          value: '',
          placeholder: '请输入流水单号'
        },
        apply_source: {
          type: 'select',
          label: '申请来源',
          value: '',
          placeholder: '请选择申请来源',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '财务申请',
              value: 'cw'
            },
            {
              label: '采购单转化',
              value: 'cgd'
            }
          ]
        },
        appropriation_status: {
          type: 'select',
          label: '拨款状态',
          value: '',
          placeholder: '请选择拨款状态',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '已拨款',
              value: 'appropriated'
            },
            {
              label: '审批中',
              value: 'approving'
            },
            {
              label: '已拒绝',
              value: 'rejected'
            },
            {
              label: '已撤销',
              value: 'revoked'
            },
            {
              label: '拨款中',
              value: 'appropriating'
            },
            {
              label: '待拨款',
              value: 'appropriation_pending'
            },
            {
              label: '待结算',
              value: 'settle_pending'
            }
          ]
        }
      },
      tableData: [],
      tableSetting: [
        { label: '拨款单号', key: 'appropriation_no' },
        { label: '申请时间', key: 'create_time' },
        { label: '拨款时间', key: 'appropriation_time' },
        { label: '交易流水号', key: 'trade_no' },
        { label: '拨款金额', key: 'appropriation_fee', type: "slot", slotName: "appropriationFee" },
        { label: '申请金额', key: 'apply_fee', type: "slot", slotName: "applyFee" },
        { label: '申请人', key: 'operator' },
        { label: '申请来源', key: 'apply_source_alias' },
        { label: '申请内容', key: 'apply_content', showTooltip: true },
        { label: '申请备注', key: 'apply_remark', showTooltip: true },
        { label: '拨款状态', key: 'appropriation_status_alias' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      approvalDrawerShow: false,
      approvalDrawerForm: {
        applicant: '',
        applicationContent: '',
        amountApplied: '',
        collectingSupplier: '',
        collectingSupplier_name: '',
        beneficiaryAccountName: '',
        receivablesAccount: '',
        receivingBank: '',
        applicationDocuments: '',
        applicationRemarks: ''
      },
      defaultReceivablesAccount: '',
      supplierList: [],
      uploading: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'super_food_img'
      },
      fileListsForApproval: [],
      fileListsForDetail: [],
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      selectData: '',
      detailDrawerShow: false,
      detailDrawerForm: {
        applicationInfo: {
          applicant: '',
          applicationSource: '',
          applicationContent: '',
          amountApplied: ''
        },
        documentInfo: [],
        applicationDocuments: [],
        collectionInfo: {
          beneficiaryAccountName: '',
          receivablesAccount: '',
          receivingBank: ''
        },
        approvalStatus: [],
        applicationRemarks: '',
        uploadBillingVoucher: []
      },
      selectType: '',
      approveMethod: '',
      zjApproveMethod: '',
      showImagePreview: false,
      previewList: [],
      toBeAllocated: '' // 待拨款的时间
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'allPermissions', 'permission_routes']),
    computedPrice() {
      return d => {
        return '￥' + divide(d, 100)
      }
    },
    computedFileType() {
      return d => {
        const fileType = ['jpeg', 'jpg', 'png', 'tiff', "JPEG", "PNG", "BMP", "TIFF", "HEIF", "JPG"]
        let result = d.split(".")[1]
        if (!fileType.includes(result)) {
          return false
        } else {
          return true
        }
      }
    }
  },
  created() {
    console.log('this.userInfo', this.allPermissions, this.permission_routes)
    this.getDataList()
    this.getSupplierList()
  },
  methods: {
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getDataList()
      }
    }, 300),
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.getDataList()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList()
    },
    gotoExport() {
      let params = getRequestParams(this.searchFormSetting, this.currentPage, this.totalCount)
      const option = {
        url: 'apiBackgroundFundSupervisionAppropriationListExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    // 文件上传成功
    uploadSuccessForApproval(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        let arr = []
        fileList.forEach(item => {
          let obj = {
            name: item.name,
            url: item.response.data.public_url
          }
          arr.push(obj)
        })
        this.fileListsForApproval = deepClone(fileList)
        this.approvalDrawerForm.applicationDocuments = deepClone(arr)
      } else {
        this.$message.error(res.msg)
      }
    },
    uploadSuccessForDetail(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        let arr = []
        fileList.forEach(item => {
          let obj = {
            name: item.name,
            url: item.response.data.public_url
          }
          arr.push(obj)
        })
        this.fileListsForDetail = deepClone(fileList)
        this.detailDrawerForm.uploadBillingVoucher = deepClone(arr)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 文件上传前检测
    beforeFileUpload(file) {
      // 文件校验
      const fileType = ['jpeg', 'jpg', 'xls', 'xlsx', 'png', 'txt', 'zip', 'docx', 'doc', 'bmp', 'tiff', "JPEG", "PNG", "BMP", "TIFF", "WEBP", "HEIF", "JPG", "exe", "rar", "ZIP", "RAR"]
      const isLt2M = file.size / 1024 / 1024 <= 20
      let result = file.name.split(".")[1]
      if (!fileType.includes(result)) {
        this.$message.error('请上传正确的文件')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },
    getDataList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      this.$apis.apiBackgroundFundSupervisionAppropriationListPost(params).then(res => {
        if (res.code === 0) {
          this.isLoading = false
          this.tableData = deepClone(res.data.results)
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 供应商信息获取
    getSupplierList() {
      this.$apis.apiBackgroundFundSupervisionAppropriationSupplierManageListPost().then(res => {
        if (res.code === 0) {
          this.supplierList = deepClone(res.data)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 反显供应商信息
    setSupplierInfo(e) {
      let arr = this.supplierList.filter(item => item.id === e)
      this.defaultReceivablesAccount = arr[0].account_receivable
      this.approvalDrawerForm.collectingSupplier_name = arr[0].name
      this.approvalDrawerForm.beneficiaryAccountName = arr[0].name_of_payee
      this.approvalDrawerForm.receivablesAccount = arr[0].account_receivable
      this.approvalDrawerForm.receivingBank = arr[0].recipient_bank
    },
    showApprovalDrawer() {
      this.approvalDrawerForm.applicant = this.userInfo.member_name
      this.approvalDrawerShow = true
    },
    showDetailDrawer(data, type) {
      this.selectType = type
      this.selectData = data
      this.approveMethod = data.approve_method
      this.zjApproveMethod = data.zj_approve_method
      let obj = {
        applicationInfo: {
          applicant: data.operator,
          applicationSource: data.apply_source_alias,
          applicationContent: data.apply_content,
          amountApplied: data.apply_fee
        },
        documentInfo: [],
        applicationDocuments: deepClone(data.image_json || []),
        collectionInfo: {
          beneficiaryAccountName: data.account_person,
          receivablesAccount: data.account_number,
          receivingBank: data.account_bank
        },
        approvalStatus: [
          {
            icon: 'el-icon-check',
            color: '#14ce84',
            status_alias: '提交申请',
            status: 'pending',
            data: [
              {
                icon: 'el-icon-success',
                color: '#14ce84',
                status_alias: '提交申请',
                status: 'pending',
                account_id: '',
                timestamp: data.create_time,
                operator: `${data.operator}`
              }
            ]
          }
        ],
        applicationRemarks: data.apply_remark,
        uploadBillingVoucher: type === 'count' ? [] : deepClone(data.settlement_json || [])
      }
      this.detailDrawerForm = deepClone(obj)
      this.fileListsForDetail = deepClone(obj.uploadBillingVoucher)
      this.getApprovalProcess()
      this.detailDrawerShow = true
    },
    async getApprovalProcess() {
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionFinanceApproveApproveAccountsPost({
        id: this.selectData.id
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let result = res.data
        let newStatus = []
        // 先判断一下是不是或签，或签的话全部账号显示在一个流程里就可以了
        switch (this.approveMethod) {
          case 'one_by_one_approve': {
            // 依次审批还是拿回approve_account_info组成数组显示吧
            // 先循环res.data
            result.forEach(item => {
              let obj = {
                icon: 'el-icon-check',
                color: '#ff9b45',
                status_alias: '待审批',
                status: 'pending',
                data: []
              }
              let statusList = []
              if (item.approve_account_info && item.approve_account_info.length) {
                // 将这个审批账号里的全部账号拉出来组成目标数据丢尽obj.data
                item.approve_account_info.forEach(itemIn => {
                  let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                  let child = {
                    icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                    color: this.switchColor(itemIn.approve_status),
                    status_alias: itemIn.approve_status_alias,
                    status: itemIn.approve_status,
                    account_id: itemIn.account_id,
                    timestamp: itemIn.approve_time,
                    operator: `${itemIn.account_name}`,
                    reason: itemIn.approve_reason
                  }
                  statusList.push(itemIn.approve_status)
                  obj.data.push(child)
                })
                let agreeFlag = statusList.some(item => item === 'AGREE')
                let rejectFlag = statusList.some(item => item === 'REJECT')
                // 把上传的obj根据里面的内容重新赋值一下
                obj.icon = agreeFlag ? 'el-icon-check' : (rejectFlag ? 'el-icon-close' : 'el-icon-more')
                obj.color = agreeFlag ? '#14ce84' : (rejectFlag ? '#fd594e' : '#ff9b45')
                obj.status_alias = agreeFlag ? '审批通过' : (rejectFlag ? '拒绝审批' : '待审批')
                obj.status = agreeFlag ? 'AGREE' : (rejectFlag ? 'REJECT' : 'PENDING')
              }
              newStatus.push(obj)
            })

            // 判断是否需要到资金平台
            if (result[0].approve_platform && result[0].approve_platform === 'zj') {
              let obj = {
                icon: 'el-icon-check',
                color: '#14ce84',
                status_alias: '待资金监管平台审批',
                status: 'pending',
                data: []
              }
              newStatus.push(obj)
              await this.getZJApprovalProcess(newStatus)
            }
            break
          }
          case 'and_approve': {
            // 如果是会签，将每个审批账号做成一个数组塞到data里面
            let obj = {
              icon: result[0].approve_status === 'agree' ? 'el-icon-check' : (result[0].approve_status === 'pending' ? 'el-icon-more' : 'el-icon-close'),
              color: this.switchColor(result[0].approve_status),
              status_alias: result[0].approve_status_alias,
              status: result[0].approve_status,
              data: []
            }
            if (result[0].approve_account_info && result[0].approve_account_info.length) {
              result[0].approve_account_info.forEach(item => {
                if (item.length) {
                  let arr = []
                  item.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name}`,
                      reason: itemIn.approve_reason
                    }
                    arr.push(child)
                  })
                  obj.data.push(arr)
                }
              })
              newStatus.push(obj)
              await this.getZJApprovalProcess(newStatus)
            }
            break
          }
          case 'or_approve': {
            // 如果是或签，将所有账号放在一个流程内
            let obj = {
              icon: result[0].approve_status === 'agree' ? 'el-icon-check' : (result[0].approve_status === 'pending' ? 'el-icon-more' : 'el-icon-close'),
              color: this.switchColor(result[0].approve_status),
              status_alias: result[0].approve_status_alias,
              status: result[0].approve_status,
              data: []
            }
            if (result[0].approve_account_info && result[0].approve_account_info.length) {
              result[0].approve_account_info.forEach(item => {
                if (item.length) {
                  item.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name}`,
                      reason: itemIn.approve_reason
                    }
                    obj.data.push(child)
                  })
                }
              })
              newStatus.push(obj)
              await this.getZJApprovalProcess(newStatus)
            }
            break
          }
        }
        this.addRejectStatus(result, newStatus)
        // 如果这时没撤回，再往里塞待拨款状态节点
        if (this.selectData.appropriation_status !== 'revoked' && this.selectData.appropriation_status !== 'pending') {
          this.setAppropriationProcess(newStatus)
        }

        // 塞回去
        if (this.approveMethod !== 'and_approve') {
          this.detailDrawerForm.approvalStatus.push(...newStatus)
        } else {
          let obj = deepClone(this.detailDrawerForm.approvalStatus[0])
          obj.data = [[obj.data[0]]]
          this.detailDrawerForm.approvalStatus = [obj, ...newStatus]
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设置待拨款流程
    setAppropriationProcess(arr) {
      if (this.selectData.zj_approve_status === 'agree') {
        let obj = {
          icon: 'el-icon-check',
          color: '#14ce84',
          status_alias: '待拨款',
          status: 'agree',
          data: this.selectData.approve_method === 'and_approve' ? [
            [{
              icon: 'el-icon-success',
              color: '#14ce84',
              status_alias: '',
              status: 'agree',
              account_id: '',
              timestamp: this.toBeAllocated,
              operator: ``
            }]
          ] : [
            {
              icon: 'el-icon-success',
              color: '#14ce84',
              status_alias: '',
              status: 'agree',
              account_id: '',
              timestamp: this.toBeAllocated,
              operator: ``
            }
          ]
        }
        arr.push(obj)
        if (this.selectData.zj_appropriation_status === 'appropriated') {
          // 如果是已拨款，再插入已拨款的状态
          let obj = {
            icon: 'el-icon-check',
            color: '#14ce84',
            status_alias: '已拨款',
            status: 'agree',
            data: this.selectData.approve_method === 'and_approve' ? [
              [{
                icon: 'el-icon-success',
                color: '#14ce84',
                status_alias: '',
                status: 'agree',
                account_id: '',
                timestamp: this.selectData.appropriation_time
              }]
            ] : [
              {
                icon: 'el-icon-success',
                color: '#14ce84',
                status_alias: '',
                status: 'agree',
                account_id: '',
                timestamp: this.selectData.appropriation_time
              }
            ]
          }
          arr.push(obj)
        }
      }
    },
    // 获取资金平台审批流程
    async getZJApprovalProcess(arr) {
      const [err, res] = await to(this.$apis.apiBackgroundFundSupervisionFinanceApproveZjApproveAccountsPost({
        id: this.selectData.id
      }))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let result = res.data
        let newStatus = []
        // 先判断一下是不是或签，或签的话全部账号显示在一个流程里就可以了
        switch (this.zjApproveMethod) {
          case 'one_by_one_approve': {
            // 依次审批还是拿回approve_account_info组成数组显示吧
            // 先循环res.data
            result.forEach(item => {
              let obj = {
                icon: 'el-icon-check',
                color: '#ff9b45',
                status_alias: '待审批',
                status: 'pending',
                data: []
              }
              let statusList = []
              if (item.approve_account_info && item.approve_account_info.length) {
                // 将这个审批账号里的全部账号拉出来组成目标数据丢尽obj.data
                item.approve_account_info.forEach(itemIn => {
                  let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                  let child = {
                    icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                    color: this.switchColor(itemIn.approve_status),
                    status_alias: itemIn.approve_status_alias,
                    status: itemIn.approve_status,
                    account_id: itemIn.account_id,
                    timestamp: itemIn.approve_time,
                    operator: `${itemIn.account_name}`,
                    reason: itemIn.approve_reason
                  }
                  statusList.push(itemIn.approve_status)
                  obj.data.push(child)
                })
                let agreeFlag = statusList.some(item => item === 'AGREE')
                let rejectFlag = statusList.some(item => item === 'REJECT')
                // 把上传的obj根据里面的内容重新赋值一下
                obj.icon = agreeFlag ? 'el-icon-check' : (rejectFlag ? 'el-icon-close' : 'el-icon-more')
                obj.color = agreeFlag ? '#14ce84' : (rejectFlag ? '#fd594e' : '#ff9b45')
                obj.status_alias = agreeFlag ? '审批通过' : (rejectFlag ? '拒绝审批' : '待审批')
                obj.status = agreeFlag ? 'AGREE' : (rejectFlag ? 'REJECT' : 'PENDING')
              }
              newStatus.push(obj)
            })
            break
          }
          case 'and_approve': {
            // 如果是会签，将每个审批账号做成一个数组塞到data里面
            let obj = {
              icon: result[0].approve_status === 'agree' ? 'el-icon-check' : (result[0].approve_status === 'pending' ? 'el-icon-more' : 'el-icon-close'),
              color: this.switchColor(result[0].approve_status),
              status_alias: result[0].approve_status_alias,
              status: result[0].approve_status,
              data: []
            }
            if (result[0].approve_account_info && result[0].approve_account_info.length) {
              result[0].approve_account_info.forEach(item => {
                if (item.length) {
                  let arr = []
                  item.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name}`,
                      reason: itemIn.approve_reason
                    }
                    arr.push(child)
                  })
                  obj.data.push(arr)
                }
              })
              newStatus.push(obj)
            }
            break
          }
          case 'or_approve': {
            // 如果是或签，将所有账号放在一个流程内
            let obj = {
              icon: result[0].approve_status === 'agree' ? 'el-icon-check' : (result[0].approve_status === 'pending' ? 'el-icon-more' : 'el-icon-close'),
              color: this.switchColor(result[0].approve_status),
              status_alias: result[0].approve_status_alias,
              status: result[0].approve_status,
              data: []
            }
            if (result[0].approve_account_info && result[0].approve_account_info.length) {
              result[0].approve_account_info.forEach(item => {
                if (item.length) {
                  item.forEach(itemIn => {
                    let childStatus = itemIn.approve_status === 'PENDING' || itemIn.approve_status === 'AGREE'
                    let child = {
                      icon: childStatus ? 'el-icon-success' : 'el-icon-error',
                      color: this.switchColor(itemIn.approve_status),
                      status_alias: itemIn.approve_status_alias,
                      status: itemIn.approve_status,
                      account_id: itemIn.account_id,
                      timestamp: itemIn.approve_time,
                      operator: `${itemIn.account_name}`,
                      reason: itemIn.approve_reason
                    }
                    obj.data.push(child)
                  })
                }
              })
              newStatus.push(obj)
            }
            break
          }
        }
        // 将最后一个时间作为待拨款的时间
        if (result[result.length - 1].approve_method === 'and_approve') {
          let time = new Date()
          result[result.length - 1].approve_account_info.forEach(item => {
            let timeIn = item[0].approve_time
            if (dayjs(timeIn) < dayjs(time)) {
              time = timeIn
            }
            this.toBeAllocated = time
          })
        } else {
          result[result.length - 1].approve_account_info.forEach(item => {
            if (item.approve_status === 'AGREE' && item.approve_time) {
              this.toBeAllocated = item.approve_time
            }
          })
        }
        newStatus.forEach(item => {
          arr.push(item)
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    addRejectStatus(data, statusArr) {
      // 处理状态
      if (this.selectData.appropriation_status === 'revoked') {
        let obj = {
          icon: 'el-icon-error',
          color: '#909399',
          status_alias: '撤销申请',
          status: 'REVOKE',
          timestamp: data[0].approve_time,
          operator: `${data[0].operator}`
        }
        let status = {
          icon: 'el-icon-close',
          color: '#909399',
          status_alias: '撤销申请',
          status: 'REVOKE',
          data: []
        }
        // 用历史操作处理旧数据
        let record = []
        if (data[0].approve_record && data[0].approve_record.record && data[0].approve_record.record.length) {
          record = deepClone(data[0].approve_record.record)
        }
        // 如果是撤销的，直接塞
        switch (data[0].approve_method) {
          case 'one_by_one_approve': {
            // 先把最后一个干掉
            statusArr.pop()
            statusArr.forEach(item => {
              let approvalStatusArr = []
              item.data.forEach(itemIn => {
                let obj = record.filter(recordItem => recordItem.account_id === itemIn.account_id)
                if (obj.length) {
                  // 如果有就改
                  let childStatus = obj[0].status === 'PENDING' || obj[0].status === 'AGREE'
                  itemIn.icon = childStatus ? 'el-icon-success' : 'el-icon-error'
                  itemIn.color = this.switchColor(obj[0].status)
                  itemIn.status_alias = obj[0].content
                  itemIn.status = obj[0].status
                  itemIn.timestamp = obj[0].time
                } else {
                  // 没有就置空
                  itemIn.icon = ''
                  itemIn.timestamp = ''
                }
                approvalStatusArr.push(itemIn.status)
              })
              // 根据statusArr里的状态去判断
              let flag = approvalStatusArr.some(item => item === 'REJECT')
              // 审批账号里面的改好了，轮到该审批账号本身的状态了
              item.icon = flag ? 'el-icon-close' : 'el-icon-check'
              item.color = flag ? this.switchColor('') : this.switchColor('AGREE')
              item.status_alias = flag ? '' : '审批通过'
              item.status = flag ? '' : 'AGREE'
            })
            // 依次审批的话因为最后一个审批账号没审就撤销了，把最后那个干掉
            status.data = [{ ...obj }]
            statusArr.push(status)
            break
          }
          case 'and_approve': {
            statusArr[0].data.forEach(item => {
              item.forEach(itemIn => {
                let obj = record.filter(recordItem => recordItem.account_id === itemIn.account_id)
                if (obj.length) {
                  // 如果有就改
                  itemIn.icon = obj[0].status === 'AGREE' ? 'el-icon-success' : 'el-icon-error'
                  itemIn.color = this.switchColor(obj[0].status)
                  itemIn.status_alias = obj[0].content
                  itemIn.status = obj[0].status
                  itemIn.timestamp = obj[0].time
                } else {
                  // 没有就置空
                  itemIn.icon = ''
                  itemIn.timestamp = ''
                }
              })
            })
            // 审批账号里面的改好了，轮到该审批账号本身的状态了
            statusArr[0].icon = 'el-icon-more'
            statusArr[0].color = this.switchColor('PENDING')
            statusArr[0].status_alias = '待审批'
            statusArr[0].status = 'PENDING'
            status.data = [[{ ...obj }]]
            statusArr.push(status)
            break
          }
          case 'or_approve': {
            // 先把最后一个干掉
            statusArr.pop()
            status.data = [{ ...obj }]
            statusArr.push(status)
            break
          }
        }
      }
    },
    switchColor(status) {
      let color = ''
      switch (status) {
        case 'PENDING':
          color = '#ff9b45'
          break
        case 'AGREE':
          color = '#14ce84'
          break
        case 'REJECT':
          color = '#fd594e'
          break
        case 'pending':
          color = '#ff9b45'
          break
        case 'agree':
          color = '#14ce84'
          break
        case 'reject':
          color = '#fd594e'
          break
        default:
          color = '#909399'
      }
      return color
    },
    cancelHandle(type) {
      switch (type) {
        case 'approval': {
          this.$refs.approvalDrawerFormRef.resetFields()
          this.defaultReceivablesAccount = ''
          this.fileListsForApproval = []
          this.approvalDrawerShow = false
          break
        }
        case 'detail': {
          this.$refs.detailDrawerFormRef.resetFields()
          this.fileListsForDetail = []
          this.detailDrawerShow = false
          break
        }
      }
    },
    confirmHandle(type) {
      switch (type) {
        case 'approval': {
          this.$refs.approvalDrawerFormRef.validate((valid) => {
            if (valid) {
              let params = {
                apply_content: this.approvalDrawerForm.applicationContent,
                apply_fee: times(this.approvalDrawerForm.amountApplied, 100),
                supplier_manage_name: this.approvalDrawerForm.collectingSupplier_name,
                supplier_manage_id: this.approvalDrawerForm.collectingSupplier.id,
                account_person: this.approvalDrawerForm.beneficiaryAccountName,
                account_number: this.approvalDrawerForm.receivablesAccount,
                account_bank: this.approvalDrawerForm.receivingBank,
                image_json: this.approvalDrawerForm.applicationDocuments.length ? this.approvalDrawerForm.applicationDocuments : undefined,
                apply_remark: this.approvalDrawerForm.applicationRemarks
              }
              this.appropriationApply(params)
            } else {
              this.$message.error('请检查表单填写是否正确')
            }
          })
          break
        }
        case 'detail': {
          this.$refs.detailDrawerFormRef.validate((valid) => {
            if (valid) {
              let param = {
                id: this.selectData.id,
                settlement_json: this.detailDrawerForm.uploadBillingVoucher
              }
              this.$apis.apiBackgroundFundSupervisionAppropriationAppropriationSettlePost(param).then(res => {
                if (res.code === 0) {
                  this.$message.success('结算成功')
                } else {
                  this.$message.error(res.msg)
                }
                this.$refs.detailDrawerFormRef.resetFields()
                this.detailDrawerShow = false
                this.getDataList()
              })
            } else {
              this.$message.error('请检查表单填写是否正确')
            }
          })
          break
        }
      }
    },
    appropriationApply(param) {
      this.$apis.apiBackgroundFundSupervisionAppropriationAppropriationApplyPost(param).then(res => {
        if (res.code === 0) {
          this.$message.success('申请成功')
          this.$refs.approvalDrawerFormRef.resetFields()
          this.defaultReceivablesAccount = ''
          this.fileListsForApproval = []
          this.approvalDrawerShow = false
          this.getDataList()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    revokeHandle(data) {
      this.$confirm(`是否撤销当前申请？`, '提示', {
        center: true,
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$apis.apiBackgroundFundSupervisionAppropriationAppropriationRevokePost({
          id: data.id
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('撤销成功')
          } else {
            this.$message.error(res.msg)
          }
          this.getDataList()
        })
      }).catch(() => {
        this.$message('已取消撤销')
      })
    },
    handleClick(img) {
      this.previewList = [img]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    downloadFile(url, isImg) {
      this.step = 1
      // window.open(this.templateUrl);
      let spliturl = url.split('/')
      let filsename = spliturl[spliturl.length - 1]
      if (isImg) {
        fetch(url, {
          method: 'GET',
          headers: {
            'TOKEN': getToken() // 如果需要认证头
          }
        })
          .then(response => response.blob())
          .then(blob => {
            this.uploading = false // 关闭加载状态
            let spliturl = url.split('/')
            let filename = spliturl[spliturl.length - 1].split('?')[0]
            FileSaver.saveAs(blob, filename)
          })
          .catch(error => {
            this.uploading = false // 关闭加载状态
            this.$message.error('下载失败，请重试')
          })
      } else {
        FileSaver.saveAs(url, filsename)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bg-grey {
  padding: 10px 20px;
  border: 1px solid #E7E9EF;
  border-radius: 4px;
}
.icon {
  font-size: 18px;
}
::v-deep .el-timeline-item__node--large {
  left: -4px;
  width: 18px !important;
  height: 18px !important;
}
</style>
