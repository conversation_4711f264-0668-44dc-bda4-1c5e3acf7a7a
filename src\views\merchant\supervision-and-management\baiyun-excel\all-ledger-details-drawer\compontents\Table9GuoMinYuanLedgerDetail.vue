<template>
  <div class="container-wrapper">
    <div class="ledger-detail-content">
      <!-- 基本信息 -->
      <div class="basic-info">
        <div class="info-item" v-if="reviewPerson">
          <span class="label">复核人：</span>
          <span class="value">{{ reviewPerson  }}</span>
        </div>
      </div>

      <!-- 疾病特征详情 -->
      <div class="disease-details" v-if="diseaseDetails && diseaseDetails.length > 0">
        <div
          v-for="(detail, index) in diseaseDetails"
          :key="index"
          class=""
        >
         <div class="additive-name">过敏原食材信息： {{detail.allergen}}</div>
         <div class="disease-item m-t-10">
          <div class="disease-personnel">
            <span class="disease-type">供应日期：</span>
            <span class="personnel-names">{{ getUseDate(ledgerData.operate_date)  }}</span>
          </div>
          <div class="disease-personnel">
            <span class="personnel-label">供应餐次：</span>
            <span class="personnel-names">{{ detail.meal_type_alias  }}</span>
          </div>
          <div class="disease-personnel m-t-10">
            <span class="personnel-label">登记人：</span>
            <span class="personnel-names">{{ operatorName  }}</span>
          </div>
          <div class="disease-personnel m-t-10">
            <span class="personnel-label">菜品名称：</span>
            <span class="personnel-names">{{ detail.food_name  }}</span>
          </div>
          <div class="disease-header m-t-10">
            <span class="personnel-label">备注：</span>
            <span class="disease-symptom">{{ detail.remark  }}</span>
          </div>
        </div>
      </div>
      </div>

      <!-- 表格内容 -->
      <div class="table-content" v-if="tableData && tableData.length > 0">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :key="tableKey"
          header-row-class-name="ps-table-header-row"
          height="calc(100vh - 640px)"
          :max-height="600"
        >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
          </table-column>
        </el-table>

        <!-- 分页 -->
        <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
          <pagination
            :onPaginationChange="onPaginationChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :page-sizes="[10, 20, 50, 100, 500]"
            :layout="'total, prev, pager, next, sizes, jumper'"
            :total="totalCount"
          >
          </pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { deepClone } from '@/utils'
import { TABLE_HEAD_DATA_EMPLOYEES_CHEN_JIAN_BIAO } from '../../constants'

export default {
  name: 'Table9GuoMinYuanLedgerDetail',
  props: {
    ledgerNo: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    reviewPerson: {
      type: String,
      default: ''
    },
    operatorName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 表格数据
      tableSetting: deepClone(TABLE_HEAD_DATA_EMPLOYEES_CHEN_JIAN_BIAO), // 表格配置
      currentTableSetting: deepClone(TABLE_HEAD_DATA_EMPLOYEES_CHEN_JIAN_BIAO), // 当前表格配置
      tableKey: 0, // 表格key
      ledgerData: {}, // 台账基本信息
      diseaseDetails: [] // 疾病特征详情
    }
  },
  mounted() {
    console.log('Table9GuoMinYuanLedgerDetail', this.id)
    this.getDataDetail()
  },
  methods: {
    // 获取提交记录
    async getDataDetail() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: this.id
      }
      let [err, res] = await this.$to(
        this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetLedgerReviewDetail(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const data = res.data || {}
        console.log('data', data)
        // 处理基本信息
        this.ledgerData = {
          operate_date: data.operate_date || ''
        }
        // 处理疾病特征详情
        let dataList = data.data_list || []
        this.diseaseDetails = deepClone(dataList) || []
        console.log("this.diseaseDetails", this.diseaseDetails);
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getDataDetail()
    },
    // 获取当前排班人员
    getPersonCount(date) {
      this.$apis.apiBackgroundFundSupervisionLedgerFoodSafetyGetTodayPersonSchedule({
        check_date: date
      }).then(res => {
        if (res.code === 0) {
          const data = res.data || {}
          const jobPersons = data.job_persons || []
          this.$set(this.ledgerData, 'duty_personnel_count', jobPersons.length)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取使用日期
    getUseDate(date) {
      if (date && date.length > 10) {
        return date.substring(0, 10)
      }
      return date
    }

  }
}
</script>
<style scoped lang="scss">
.container-wrapper {
  padding: 20px;
}

.ledger-detail-content {
  .basic-info {
    margin-bottom: 20px;

    .info-item {
      margin-bottom: 10px;
      font-size: 14px;
      line-height: 1.5;

      .label {
        color: #666;
        font-weight: normal;
      }

      .value {
        color: #333;
        font-weight: normal;
      }
    }
  }

  .disease-details {
    margin-bottom: 20px;

    .disease-item {
      background-color: #f5f5f5;
      padding: 15px;
      margin-bottom: 10px;
      border-radius: 4px;
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      .disease-header {
        margin-bottom: 8px;
        font-size: 14px;
        color: #333;
        width: 100%;

        .disease-type {
          font-weight: normal;
        }

        .disease-symptom {
          font-weight: normal;
        }
      }

      .disease-personnel {
        font-size: 14px;
        color: #333;
        width: 50%;

        .personnel-label {
          font-weight: normal;
          // 换行
          word-break: break-all;
        }

        .personnel-names {
          font-weight: normal;
        }
      }
    }
  }

  .table-content {
    .ps-pagination {
      text-align: right;
      padding-top: 20px;
    }
  }
}
</style>
