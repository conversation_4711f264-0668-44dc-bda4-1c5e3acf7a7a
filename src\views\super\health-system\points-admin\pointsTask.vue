<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />

      <search-form
        ref="searchRef"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="origin" :disabled="isLoading" type="add" @click="addAndEditPointsTask('add')">
              新建
            </button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <el-table
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
            class="ps-table-tree"
            v-loading="isLoading"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item">
              <template #condition_type_alias="{ row }">
                <div style="text-align: left;">
                  <div>{{ row.condition_type_alias }}</div>
                  <div v-if="row.condition_type === 'specify_page'" >
                    {{ row.specify_page_type_alias }}
                  </div>
                  <div v-if="row.condition_type === 'specify_action'">
                    {{ row.specify_action_type_alias }}
                  </div>
                  <div v-if="row.condition_type === 'specify_url'">{{ row.specify_url }}</div>
                </div>
              </template>

              <template #status="{ row }">
                <el-switch
                  v-model="row.is_enable"
                  @change="switchStatus(row)"
                  active-color="#ff9b45"
                  inactive-color="#ffcda2"
                ></el-switch>
              </template>
              <template #operation="{ row }">
                <el-button
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="addAndEditPointsTask('details', row)"
                >
                  查看
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="addAndEditPointsTask('modify', row)"
                  :disabled="row.is_enable"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  @click="deleteHandler(row)"
                  :disabled="row.is_enable"
                >
                  删除
                </el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- end -->
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <points-task-drawer
      v-if="pointsTaskDrawerVisible"
      :isshow.sync="pointsTaskDrawerVisible"
      :drawerType="drawerType"
      :drawerModifyData="drawerModifyData"
      :collectData="collectData"
      @clickSaveDrawer="clickSaveDrawer"
    ></points-task-drawer>
  </div>
</template>

<script>
import { to, deepClone, debounce } from '@/utils'
import { POINTS_TASK } from './components/constantsConfig'
import PointsTaskDrawer from './components/pointsTaskDrawer'
export default {
  name: 'PointsTask',
  components: { PointsTaskDrawer },
  data() {
    return {
      isLoading: false,
      tableSettings: [
        { label: '任务名称', key: 'name' },
        { label: '优先级', key: 'priority' },
        {
          label: '完成条件',
          key: 'condition_type_alias',
          type: 'slot',
          slotName: 'condition_type_alias',
          width: '120px'
        },
        { label: '创建时间', key: 'create_time' },
        { label: '操作人', key: 'operator_name' },
        { label: '状态', key: 'status', type: 'slot', slotName: 'status' },
        { label: '操作', key: 'operation', type: 'slot', slotName: 'operation' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      total: 0,
      searchFormSetting: deepClone(POINTS_TASK),
      pointsTaskDrawerVisible: false,
      drawerType: '',
      drawerModifyData: {}, // 编辑的数据
      collectData: {}
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getPointsPointsTaskList()
    },
    /**
     * 刷新页面
     */
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.getPointsPointsTaskList()
    }, 300),
    // 获取列表数据
    async getPointsPointsTaskList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMemberPointsPointsTaskListPost({
          page: this.currentPage,
          page_size: this.pageSize,
          ...this.formatQueryParams(this.searchFormSetting)
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.total = res.data.count
        this.totalCount = res.data.count
        this.tableData = res.data.results
        this.collectData = res.data.collect
      } else {
        this.$message.error(res.msg)
      }
    },
    clickSaveDrawer() {
      this.getPointsPointsTaskList()
    },
    addAndEditPointsTask(type, row) {
      this.drawerType = type
      if (type === 'modify' || type === 'details') {
        this.drawerModifyData = deepClone(row)
      }
      this.pointsTaskDrawerVisible = true
    },
    // 修改状态
    switchStatus(data) {
      this.$confirm(`是否${data.is_enable ? '开启' : '关闭'}该任务？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundMemberPointsPointsTaskModifyPost(data)
            )
            instance.confirmButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getPointsPointsTaskList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              data.is_enable = !data.is_enable
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    deleteHandler(row) {
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundMemberPointsPointsTaskDeletePost({
                ids: [row.id]
              })
            )
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
              if (this.currentPage > 1) {
                if (this.tableData.length === 1) {
                  this.currentPage--
                }
              }
              this.getPointsPointsTaskList()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getPointsPointsTaskList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPointsPointsTaskList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>
<style lang="scss" scoped></style>
