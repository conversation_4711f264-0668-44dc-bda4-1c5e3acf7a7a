<template>
  <dialog-message :show.sync="visible" :title="title" :loading.sync="isLoading" @close="handleClose"
    customClass="ps-dialog" :width="width">
    <el-form :model="dialogForm" @submit.native.prevent status-icon ref="dialogFormRef" :rules="dialogFormRules"
      label-width="120px" class="member-form">
      <div v-if="type === 'add' || type === 'edit'">
        <el-form-item label="名称：" prop="name">
          <el-input v-model="dialogForm.name" maxlength="10" class="ps-input w-250" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="会员标签：" prop="label">
          <el-select v-model="dialogForm.label" placeholder="请选择标签" class="ps-input w-250" multiple>
            <el-option v-for="item in labelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="触发类型：" prop="labelType">
          <el-radio-group v-model="dialogForm.labelType" @change="labelTypeChange">
            <el-radio class="ps-radio" label="all">全部标签</el-radio>
            <el-radio class="ps-radio" label="any">任意标签</el-radio>
          </el-radio-group>
          <div class="ps-warn-text">{{ warnTip }}</div>
        </el-form-item>
        <el-form-item label="折扣：" prop="discount">
          <el-input v-model="dialogForm.discount" maxlength="20" type="number" class="ps-input w-250"
            @input="discountInputChange"></el-input><span class="m-l-10">%</span>
        </el-form-item>
        <el-form-item label="折扣后价格：" prop="discountPrice">
          <div>{{ dialogForm.discountPrice }}元</div>
        </el-form-item>
        <el-form-item label="限购次数：" prop="limitTimes" v-if="memberCycle !=='permanent' && memberCycle !=='week'">
          <div class="ps-flex limit-style">
            <el-radio-group v-model="dialogForm.limitTimes" @change="limitTimesChange">
              <el-radio class="ps-radio" :label="defaultLimit">不限制</el-radio>
              <el-radio class="ps-radio" label="count">任意标签</el-radio>
            </el-radio-group>
            <el-form-item prop="times">
              <div class="ps-flex"><el-input v-model="dialogForm.times" class="w-100 m-l-10" type="number"
                  placeholder="请输入"></el-input><span class="m-l-10">次/人</span></div>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="说明：" prop="remark">
          <el-input class="ps-input w-250" v-model="dialogForm.remark" type="textarea" :rows="3" maxlength="50"
            show-word-limit></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">
          取消
        </el-button>
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { divide, times } from '@/utils'
import NP from 'number-precision'

export default {
  name: 'ForLeaveRuleDialog',
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '500px'
    },
    selectInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    memberCycle: { // 会员周期
      type: String,
      default: ''
    },
    basePrice: { // 基础价格，用于计算折扣
      type: Number,
      default: 0
    },
    isshow: Boolean,
    confirm: Function
  },
  data() {
    let validataNumber = (rule, value, callback, type) => {
      if (type === 'count') {
        if (value === '') {
          return callback(new Error('不能为空'))
        } else {
          let number = /^[1-9][0-9]{0,2}$/
          if (!number.test(value)) {
            callback(new Error('请输入大于零的正整数，上限为999'))
          } else {
            callback()
          }
        }
      } else {
        callback()
      }
    };
    let validateDiscount = (rule, value, callback, type) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        let number = /^[0-9]{0,3}$/
        var result = parseInt(value)
        if (!number.test(value) || result > 100) {
          callback(new Error('请输入大于零的正整数，上限为100,输入100表示不打折'))
        } else {
          callback()
        }
      }
    };
    return {
      isLoading: false,
      dialogForm: {
        name: '',
        label: [],
        labelType: 'all',
        discount: '',
        discountPrice: '',
        limitTimes: -1,
        times: '',
        remark: ''
      },
      dialogFormRules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        labelType: [{ required: true, message: '请选择触发类型', trigger: 'change' }],
        discount: [
          { required: true, message: '请输入折扣', trigger: 'blur' },
          { required: true, validator: validateDiscount, trigger: 'blur' }
        ],
        times: [{ required: true, validator: (e1, e2, e3) => validataNumber(e1, e2, e3, this.dialogForm.limitTimes), trigger: 'blur' }]
      },
      labelList: [],
      cycleList: [],
      warnTip: '全部标签满足才触发优惠规则',
      defaultLimit: -1
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        console.log("this.visible", this.visible);
        this.getMemberLabel()
        this.getMemberCycle()
        if (this.type === 'edit') {
          console.log("this.selectInfo 哈哈哈哈", this.selectInfo);
          this.dialogForm.name = this.selectInfo.name
          this.dialogForm.label = this.selectInfo.member_labels
          this.dialogForm.discount = this.selectInfo.discount
          this.dialogForm.discountPrice = divide(this.selectInfo.origin_fee)
          this.dialogForm.labelType = this.selectInfo.trigger_type || 'all'
          if (this.selectInfo.buy_count !== -1) {
            this.dialogForm.times = this.selectInfo.buy_count
            this.dialogForm.limitTimes = 'count'
          } else {
            this.dialogForm.limitTimes = -1
          }
          this.dialogForm.remark = this.selectInfo.remark
        }
      } else {
        this.$refs.dialogFormRef.resetFields()
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() { },
  methods: {
    initLoad() {
      this.getMemberLabel()
    },
    clickConfirmHandle() {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          let params = {
            name: this.dialogForm.name,
            member_labels: this.dialogForm.label,
            trigger_type: this.dialogForm.labelType,
            member_cycle: this.memberCycle,
            discount: this.dialogForm.discount,
            origin_fee: times(this.dialogForm.discountPrice)
          }
          if (this.dialogForm.limitTimes !== -1) {
            params.buy_count = parseInt(this.dialogForm.times)
          } else {
            params.buy_count = -1
          }
          if (this.dialogForm.remark) {
            params.remark = this.dialogForm.remark
          }
          let api
          switch (this.type) {
            case 'add':
              api = this.$apis.apiBackgroundMemberMemberChargeRuleAddPost(params)
              break;
            case 'edit':
              params.id = Number(this.selectInfo.id)
              api = this.$apis.apiBackgroundMemberMemberChargeRuleModifyPost(params)
              break;
          }
          this.confirmOperation(api)
        } else {
        }
      })
    },
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('成功')
        this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.dialogFormRef.resetFields()
    },
    // 获取会员标签
    async getMemberLabel() {
      const res = await this.$apis.apiBackgroundMemberMemberLabelListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.labelList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取会员周期
    async getMemberCycle() {
      const res = await this.$apis.apiBackgroundMemberMemberChargeRuleGetMemberCyclePost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        let results = []
        for (let key in res.data) {
          results.push({
            value: key,
            label: res.data[key]
          })
        }
        this.cycleList = results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 标签选择变化
    labelTypeChange(value) {
      this.warnTip = value === 'all' ? '全部标签满足才触发优惠规则' : '任一标签满足即触发规则'
    },
    // 折扣价格
    discountInputChange(value) {
      if (value) {
        this.dialogForm.discountPrice = NP.times(NP.divide(this.basePrice, 100), value)
      }
      console.log("this.dialogForm.discountPrice", this.dialogForm.discountPrice, this.basePrice, value);
    },
    // 限购次数切换
    limitTimesChange(value) {
      if (value === -1) {
        this.$set(this.dialogForm, 'times', '')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';

.member-form {
  .label-list {
    display: flex;
    flex-wrap: wrap;
    color: #fff;

    .label-list-item {
      margin-bottom: 10px;
      line-height: 30px;
      background-color: #ff9b45;
      padding: 0 10px;
      margin-right: 15px;
      border-radius: 5px;

      .del-icon {
        cursor: pointer;
      }
    }
  }

  .m-r-5 {
    margin-right: 5px;
  }

  .limit-style {
    align-items: center;
  }

}
</style>
