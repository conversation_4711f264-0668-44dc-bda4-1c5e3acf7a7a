<template>
  <div class="drawer-box">
    <customDrawer
      :show.sync="visible"
      :loading="isLoading"
      :title="'商品详情'"
      :confirmShow="false"
      :cancelText="'关 闭'"
       cancelClass="ps-btn"
       :size="800"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form :model="drawerFormData" ref="drawerFormDataRef" label-width="70px">
            <el-form-item label="商品名称">
              <span>{{ drawerModifyData.name }}</span>
            </el-form-item>
            <el-form-item label="识别图片">
              <div style="display: flex; flex-wrap: wrap">
                <el-image
                  style="width: 100px; height: 100px; margin-right: 10px"
                  :src="item"
                  :preview-src-list="[item]"
                  v-for="(item, index) in drawerModifyData.images_url"
                  :key="index"
                ></el-image>
              </div>
            </el-form-item>
            <el-form-item label="上架时间">
              <div v-if="!drawerModifyData.is_permanent">
                <div>{{ drawerModifyData.start_date }}至 {{ drawerModifyData.end_date }}</div>
              </div>
              <div v-else>永久</div>
            </el-form-item>
            <el-form-item label="商品类型">
              <div style="display: flex">
                <div>{{ drawerModifyData.commodity_type_alias }}</div>
                <div v-if="drawerModifyData.commodity_type === 'virtual'">
                  （
                  <span>{{ drawerModifyData.virtual_commodity_type_alias }},</span>
                  <span
                    v-if="
                      drawerModifyData.virtual_commodity_type === 'ai_nutritionist' &&
                      drawerModifyData.commodity_extra
                    "
                  >
                    {{ drawerModifyData.commodity_extra.count }}次
                  </span>
                  <span
                    v-if="
                      drawerModifyData.virtual_commodity_type === 'member' &&
                      drawerModifyData.commodity_extra
                    "
                  >
                    {{ drawerModifyData.commodity_extra.day }}天
                  </span>
                  ）
                </div>
                <div
                  v-if="drawerModifyData.commodity_type === 'physical'"
                  style="padding-left: 20px"
                >
                  <span>商品编码：（{{ drawerModifyData.physical_code }}）</span>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="商品价格">
              <span
                v-if="
                  drawerModifyData.commodity_price_type === 'money' ||
                  drawerModifyData.commodity_price_type === 'money_points'
                "
              >
                {{ drawerModifyData.fee | formatMoney }}元
              </span>
              <span v-if="drawerModifyData.commodity_price_type === 'money_points'">+</span>
              <span
                v-if="
                  drawerModifyData.commodity_price_type === 'points' ||
                  drawerModifyData.commodity_price_type === 'money_points'
                "
              >
                {{ drawerModifyData.points }}积分
              </span>
            </el-form-item>
            <el-form-item label="库存数量">
              <div v-if="drawerModifyData.buy_stock_num === -1">
                <div>不限制</div>
              </div>
              <div v-else>{{ drawerModifyData.buy_stock_num }}</div>
            </el-form-item>
            <el-form-item label="可兑换数">
              <div>
                <span>{{ drawerModifyData.buy_limit_type_alias }}</span>
                <span v-if="drawerModifyData.buy_limit_type !== 'non'">
                  {{ drawerModifyData.buy_limit_num }}次
                </span>
              </div>
            </el-form-item>
            <el-form-item label="图文详情">
              <TinymceUeditor
                :content="drawerModifyData.details"
                v-model="drawerModifyData.details"
                :disabled="true"
              ></TinymceUeditor>
            </el-form-item>
            <el-form-item label="优先级">
              <div>
                {{ drawerModifyData.priority }}
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
import { to } from '@/utils'
import TinymceUeditor from '@/components/Tinymce/Tinymce.vue'
export default {
  props: {
    isshow: Boolean,
    // drawerModifyData: {
    //   type: Object,
    //   default() {
    //     return {}
    //   }
    // }
    commodiyId: {
      type: Number,
      default() {
        return null
      }
    }
  },
  components: {
    TinymceUeditor
  },
  data() {
    return {
      isLoading: false,
      drawerFormData: {},
      drawerModifyData: {}
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    this.getPointsPointsCommodityDetail()
  },
  methods: {
    // 获取列表数据
    async getPointsPointsCommodityDetail() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMemberPointsPointsCommodityDetailPost({
          page: 1,
          page_size: 99,
          id: this.commodiyId
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.drawerModifyData = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    closeClick() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    padding: 30px;
    .drawer-content {
      // padding-left: 20px;
      // padding-right: 20px;
    }
  }
}
</style>
