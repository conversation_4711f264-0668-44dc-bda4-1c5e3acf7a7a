<template>
  <div class="GoodsStockSummary container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon
            color="plain"
            @click="gotoExport"
            v-permission="['background_store.goods.goods_stock_summary_export']"
          >
            导出EXCEL
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :span-method="spanMethod"
          :empty-text="isFirstSearch ? '暂无数据，请查询' : ''"
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #index="{ row }">
              <div v-if="row.index === 'slot'">{{ row.sumsName }}</div>
              <div v-else>{{ row.create_time }}</div>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>
<script>
import { debounce, to } from '@/utils'
import { GOODS_STOCK_SUMMARY } from '../components/constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import mergeTableMixin from '@/mixins/mergeTable' // 合并混入
export default {
  name: 'GoodsStockSummary',
  mixins: [exportExcel, report, mergeTableMixin],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '商品名称', key: 'name' },
        { label: '商品条码', key: 'barcode', width: 150 },
        { label: '规格', key: 'spec' },
        { label: '单位', key: 'unit_name' },
        { label: '分类', key: 'category_name' },
        {
          label: '入库数量',
          key: 'inbound_stock',
          children: [
            { label: '操作入库', key: 'operate_inbound_stock' },
            { label: '退款入库', key: 'refund_stock' },
            { label: '失败订单入库', key: 'order_fail_stock' }
          ]
        },
        {
          label: '出库数量',
          key: 'outbound_stock',
          children: [
            { label: '销售出库', key: 'sale_stock' },
            { label: '盘点出库', key: 'check_stock' },
            { label: '保质期出库', key: 'expiration_stock' },
            { label: '破损出库', key: 'breakage_stock' },
            { label: '其他', key: 'other_stock' }
          ]
        }
      ],
      currentTableSetting: [],
      searchFormSetting: GOODS_STOCK_SUMMARY,
      printType: 'GoodsStockSummary',
      isFirstSearch: false
    }
  },
  created() {
    this.getGoodsCategoryList()
    this.initPrintSetting()
    // this.initLoad()
  },
  mounted() {},
  methods: {
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    initLoad() {
      this.getGoodsStockSummary()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.isFirstSearch = false
        this.getGoodsStockSummary()
      }
    }, 300),
    async getGoodsCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategoryListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.goods_category_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getGoodsStockSummary() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsGoodsStockSummaryPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        if (res.data.results.length) {
          // 设置合计的值，严格依赖tableSetting，和数据的先后顺序
          this.setSummaryData(res, 'summary_data')
        }
        // 设置合并配置
        this.mergeOpts = {
          // 行合并配置
          rowMergeList: [],
          // 列合并配置
          colMergeList: [
            {
              labels: ['商品名称', '商品条码', '规格', '单位', '分类'],
              subtotals: ['合计'],
              attr: 'name'
            }
          ]
        }
        // 初始化合并数据
        this.initMergeData(this.tableData)
      } else {
        this.$message.error(res.msg)
      }
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getGoodsStockSummary()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getGoodsStockSummary()
    },
    gotoExport() {
      const option = {
        type: 'GoodsStockSummary',
        url: 'apiBackgroundStoreGoodsGoodsStockSummaryExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss" scoped></style>
