<template>
  <div class="container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting.sync="searchFormSetting" label-width="105px" @search="searchHandle" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">
          <span>数据列表</span>
        </div>
      </div>
      <div class="table-content">
        <el-table
          :data="tableData"
          v-loading="isLoading"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #signImgUrl="{ row }">
              <el-button type="text" size="small" @click="handleClick(row.sign_img_url)">查看</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { debounce, deepClone, getRequestParams } from '@/utils'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  components: {
    ElImageViewer
  },
  data() {
    return {
      noticeId: '',
      searchFormSetting: {
        name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名',
          clearable: true
        },
        phone: {
          type: 'input',
          label: '手机号',
          value: '',
          placeholder: '请输入手机号',
          clearable: true
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号',
          clearable: true
        }
      },
      tableData: [],
      tableSetting: [
        {
          label: '姓名',
          key: 'name'
        },
        {
          label: '人员编号',
          key: 'person_no'
        },
        {
          label: '手机号',
          key: 'phone'
        },
        {
          label: '查看时间',
          key: 'view_time'
        },
        {
          label: '签名照片',
          key: 'sign_img_url',
          type: 'slot',
          slotName: 'signImgUrl'
        }
      ],
      isLoading: false,
      page: 1,
      pageSize: 10,
      totalCount: 0,
      showImagePreview: false,
      previewList: []
    }
  },
  created() {
    this.noticeId = this.$route.query.id
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getSignList()
    },
    getSignList() {
      const params = getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      params.notice_id = this.noticeId
      this.isLoading = true
      this.$apis.apiBackgroundMarketingMarketingNoticeSignListPost(params).then(res => {
        if (res.code === 0) {
          this.isLoading = false
          this.tableData = deepClone(res.data.results)
          this.totalCount = res.data.count
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.page = 1
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.initLoad()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.initLoad()
    },
    handleClick(img) {
      this.previewList = [img]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
