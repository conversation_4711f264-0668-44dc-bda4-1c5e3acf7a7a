<template>
  <div class="AddSurvey container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper" style="margin-top: 0px; margin-bottom: 50px">
      <el-form :model="settingForm" :rules="settingFormRule" ref="settingForm">
        <div class="table-header" style="display: flex; justify-content: space-between">
          <div style="display: flex; align-items: center" class="m-t-20">
            <div>
              <el-form-item class="form-inline" label="问卷名称" prop="name" label-width="95px">
                <el-input v-model="settingForm.name" max="20" class="ps-input w-180"></el-input>
              </el-form-item>
              <el-form-item class="form-inline" label="截止时间" prop="endDate" label-width="90px">
                <el-date-picker
                  v-model="settingForm.endDate"
                  type="date"
                  placeholder="选择截止时间"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  class="ps-picker">
                </el-date-picker>
              </el-form-item>
              <el-form-item class="form-inline" label="" prop="anonymous">
                <el-checkbox v-model="settingForm.anonymous" class="ps-checkbox" style="margin-left: 25px;">允许匿名提交</el-checkbox>
              </el-form-item>
              <el-form-item class="form-inline" label="是否启用" prop="isEnable" label-width="90px">
                <el-switch v-model="settingForm.isEnable" active-color="#ff9b45"></el-switch>
              </el-form-item>
            </div>
          </div>
          <div style="padding-right: 20px" class="m-t-20 m-w-160">
            <el-button size="small" type="primary" class="ps-plain-btn" @click="cancelForm()">
              取消
            </el-button>
            <el-button size="small" type="primary" class="ps-origin-btn" @click="checkForm()">
              保存
            </el-button>
            <!-- <el-button size="small" type="primary" class="ps-origin-btn">
              保存并启用
            </el-button> -->
          </div>
        </div>
        <div class="setting-wrap">
          <div class="m-b-30 m-t-20">
            <el-button size="small" type="primary" class="ps-origin-btn" @click="openDialog()">
              添加题目
            </el-button>
            <el-button size="small" type="primary" class="ps-plain-btn" @click="sortQuestions">
              题目排序
            </el-button>
          </div>
          <div v-for="(item, index) in settingForm.surveyQuestions" :key="index">
            <el-form-item :label="'题目内容'+(index+1)" :prop="'surveyQuestions.'+index+'.content'" :rules="settingFormRule.content" label-width="110px">
              <el-input v-model="item.content" max="20" class="ps-input w-300"></el-input>
              <el-button type="text" size="small" class="ps-red m-l-10" @click="delQuestions(item, index)">删除</el-button>
            </el-form-item>
            <div v-if="item.type === 'CHOICE'" class="m-l-80 m-b-8" :key="optkey">
              <div v-for="(optionsItem, optionsIndex) in item.options_data" :key="optionsIndex" class="options-wrap">
                <!-- <img class="drop-img m-b-12 m-r-10" src="@/assets/img/drop.png" alt="" /> -->
                <el-form-item class="form-inline option-form" :label="optionsItem.options"  :prop="'surveyQuestions.'+index+'.options_data.'+optionsIndex+'.content'" :rules="settingFormRule.content" label-width="60px">
                  <el-input v-model="optionsItem.content" max="20" class="ps-input w-300"></el-input>
                  <el-button v-if="item.options_data.length > 2" type="text" size="small" class="ps-red m-l-10" @click="delOptions(index, optionsIndex)">删除</el-button>
                </el-form-item>
              </div>
              <el-button v-if="item.options_data.length < 7" type="text" size="small" @click="addoption(index)">添加选项</el-button>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <survey-dialog
      :isshow.sync="dialogVisible"
      title="添加题目"
      :confirm="dialogConfirm"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import SurveyDialog from './components/SurveyDialog.vue'
const keyList = ['A', 'B', 'C', 'D', 'E', 'F', 'G']
export default {
  name: 'AddSurvey',
  components: {
    SurveyDialog
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      pageType: '',
      surveyInfo: {},
      isLoading: false,
      settingForm: {
        name: '',
        endDate: '',
        anonymous: false,
        isEnable: false,
        surveyQuestions: []
      },
      settingFormRule: {
        name: [{ required: true, message: '问卷名称不能为空', trigger: "blur" }],
        endDate: [{ required: true, message: '截止时间不能为空', trigger: "blur" }],
        content: [{ required: true, message: '内容不能为空', trigger: "blur" }]
      },
      dialogVisible: false,
      optkey: 0
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.$route.query.type === "edit" && this.$route.query.data) {
        this.pageType = this.$route.query.type
        this.surveyInfo = JSON.parse(this.$route.query.data)
        this.settingForm.name = this.surveyInfo.name
        this.settingForm.endDate = this.surveyInfo.end_date
        this.settingForm.anonymous = this.surveyInfo.anonymous
        this.settingForm.isEnable = false
        this.settingForm.surveyQuestions = this.surveyInfo.survey_questions.map(item => {
          item.options_data = item.options
          return item
        })
      }
    },
    // 刷新页面
    refreshHandle() {
      this.initLoad()
    },
    checkForm() {
      if (!this.settingForm.surveyQuestions.length) return this.$message.error('请检查表单信息')
      this.$refs.settingForm.validate(valid => {
        if (valid) {
          let params = {
            name: this.settingForm.name,
            end_date: this.settingForm.endDate,
            anonymous: this.settingForm.anonymous,
            is_enable: this.settingForm.isEnable
          }
          let questions = this.settingForm.surveyQuestions.map((item, index) => {
            item.sort = index + 1
            return item
          })
          params.survey_questions = questions
          this.saveSetting(params)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    async saveSetting(params) {
      let api
      if (this.pageType === 'edit') {
        api = this.$apis.apiBackgroundMarketingSurveyInfoModifyPost
        params.id = this.surveyInfo.id
      } else {
        api = this.$apis.apiBackgroundMarketingSurveyInfoAddPost
      }
      const res = await api(params)
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    cancelForm() {
      this.$closeCurrentTab(this.$route.path)
    },
    openDialog() {
      this.dialogVisible = true
    },
    dialogConfirm(e) {
      this.dialogVisible = false
      this.settingForm.surveyQuestions.push(e)
    },
    sortQuestions() {
      let CHOICEdata = []
      let ANSWERdata = []
      let SCOREdata = []
      this.settingForm.surveyQuestions.map(item => {
        if (item.type === 'CHOICE') {
          CHOICEdata.push(item)
        } else if (item.type === 'ANSWER') {
          ANSWERdata.push(item)
        } else if (item.type === 'SCORE') {
          SCOREdata.push(item)
        }
      })
      this.settingForm.surveyQuestions = [].concat(CHOICEdata).concat(ANSWERdata).concat(SCOREdata)
    },
    delQuestions(item, index) {
      this.settingForm.surveyQuestions.splice(index, 1)
    },
    delOptions(index, optindex) {
      let info = this.settingForm.surveyQuestions[index].options_data
      info.splice(optindex, 1)
      info.map((opt, oindex) => {
        opt.options = keyList[oindex]
      })
      this.optkey = Math.random()
    },
    addoption(index) {
      let info = this.settingForm.surveyQuestions[index].options_data
      console.log(info)
      let lastOptKey = info[info.length - 1].options
      let key = keyList[keyList.indexOf(lastOptKey) + 1]
      info.push({
        options: key,
        content: ''
      })
      this.optkey = Math.random()
    }
  }
}
</script>

<style lang="scss">
.AddSurvey {
  .form-inline{
    display: inline-block;
  }
  .m-w-160{
    min-width: 160px;
  }
  .setting-wrap{
    margin: 0 20px;
    .option-form{
      margin-bottom: 18px;
    }
    .options-wrap{
      display: flex;
      align-items: center;
    }
  }
}
</style>
