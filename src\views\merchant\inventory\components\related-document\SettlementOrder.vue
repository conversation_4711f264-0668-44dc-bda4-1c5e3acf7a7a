<template>
  <div v-loading="isLoading" class="settlement-order-box">
    <div class="info-wrap">
      <div class="title">基本信息</div>
      <div class="form-item"><span class="form-label">单据编号：</span>{{ detailData.trade_no }}</div>
      <div class="form-item"><span class="form-label">创建时间：</span>{{ detailData.create_time }}</div>

      <div class="form-item"><span class="form-label">结算时间：</span>{{ detailData.settle_status === 'settled' ? detailData.settle_time : '--' }}</div>
      <div class="form-item"><span class="form-label">结算状态：</span>{{ detailData.settle_status_alias }}</div>
      <div class="form-item"><span class="form-label">结算金额：</span>{{ detailData.settle_fee | formatMoney }}</div>
      <div class="form-item"><span class="form-label">审批人：</span>{{ detailData.approver ? detailData.approver : '--' }}</div>
      <div class="form-item"><span class="form-label">审批时间：</span>{{ detailData.approval_time ? detailData.approval_time : '--' }}</div>
      <div v-if="type === 'detail'">
        <div class="form-item"><span class="form-label">单据凭证：</span></div>
        <div class="extra" v-for="item in detailData.extra" :key="item">
          <img class="extra-img" :src="item" alt="">
        </div>
        <div class="form-item"><span class="form-label">关联收货单据：</span></div>
        <div v-for="(item, index) in detailData.delivery_info_nos" :key="index">
          <div class="form-item">{{item.trade_no}}<el-button style="margin-left: 15px" type="text" size="small" v-clipboard:copy="item.trade_no" v-clipboard:success='clipboardSuccess'>复制</el-button></div>
        </div>
      </div>
    </div>
  </div>
  <!-- end -->
</template>

<script>
import { deepClone } from '@/utils'

export default {
  name: 'RelatedDocument',
  props: {
    type: { //
      type: String,
      default: 'detail'
    },
    params: {
      type: Object,
      default: () => {}
    },
    api: {
      type: String,
      default: 'apiBackgroundDrpVendorDataVendorDeliveryDetailListPost'
    },
    infoData: {
      type: Object,
      default: () => {}
    }
  },
  components: {
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      tabType: 'settlementOrder',
      tableData: [],
      tableSettings: [
        { label: '物资名称', key: 'name' },
        { label: '收货数量', key: 'name1' },
        { label: '单价', key: 'name2' },
        { label: '合计金额', key: 'name3' },
        { label: '保质期', key: 'name4' }
      ],
      vehicleList: [],
      detailData: {}, // dd
      totalPrice: 0 // 合计金额
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      // 申购单详情直接拿infoData
      if (this.type !== 'detail') {
        this.getDataInfo()
      } else {
        this.detailData = deepClone(this.infoData)
      }
    },
    async getDataInfo() {
      if (this.isLoading) return
      if (!this.api) {
        return this.$message.error('缺少参数，请检查！')
      }
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis[this.api](this.params))
      this.isLoading = false
      this.detailData = {}
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.detailData = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    clipboardSuccess() {
      this.$message({
        message: '复制成功',
        type: 'success',
        duration: 1500
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.settlement-order-box{
  .info-wrap{
    font-size: 14px;
    line-height: 30px;
    .title{
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0 10px;
    }
    .form-item {
      display: flex;
      font-size: 14px;
      line-height: 30px;
    }
    .form-img-box {
      flex: 1;
    }
    .detault-img {
      width: 160px;
      height: 92px;
      border: 1px dashed #e1e1e1;
      border-radius: 2px;
    }
    .extra-img{
      width: 148px;
      height: 148px;
      margin: 10px 10px 10px 0;
    }
  }
}
</style>
