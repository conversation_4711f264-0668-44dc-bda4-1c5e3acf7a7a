<template>
  <div class="ApproveRules container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />

    <search-form
      ref="searchRef"
      @search="searchHandle"
      @reset="onReset"
      :form-setting="searchFormSetting"
    ></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">满意度统计</div>
      </div>

      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column prop="quarter_alias" label="统计周期" align="center"></el-table-column>
          <el-table-column prop="access_type_alias" label="统计终端" align="center"></el-table-column>
          <el-table-column prop="user_counts" label="用户数" align="center"></el-table-column>
          <el-table-column prop="unrated_counts" label="未评价" align="center"></el-table-column>
          <el-table-column prop="satisfied_counts" label="满意" align="center">
            <template slot-scope="scope">
              <div>{{ scope.row.satisfied_counts }}({{ scope.row.satisfied_rate }}%)</div>
            </template>
          </el-table-column>
          <el-table-column prop="unsatisfied_counts" label="不满意" align="center">
            <template slot-scope="scope">
              <div>{{ scope.row.unsatisfied_counts }}({{ scope.row.unsatisfied_rate }}%)</div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button type="text" size="small" class="ps-bule" @click="gotoAddOrEdit(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  name: 'ApproveRulesList',
  data() {
    return {
      isLoading: false,
      tableData: [],
      searchFormSetting: {
        access_type: {
          type: 'select',
          label: '统计终端',
          value: '',
          clearable: true,
          placeholder: '',
          dataList: [
            {
              label: '全部',
              value: ''
            }
          ]
        }
      },
      selectListId: [],
      selectList: [] // 选择的列表
    }
  },
  mounted() {
    this.initLoad()
    this.getAccessTypeList()
  },
  computed: {
    ...mapGetters(['allPermissions', 'userInfo'])
  },
  methods: {
    // 获取统计终端下拉框
    async getAccessTypeList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundAdminSatisfactionAccessTypeListPost({}))
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        const datas = res.data || []
        this.$set(this.searchFormSetting.access_type, 'dataList', [
          {
            label: '全部',
            value: ''
          },
          ...datas.map(item => ({
            label: item.name,
            value: item.key
          }))
        ])
      } else {
        this.$message.error(res.msg)
      }
    },
    initLoad() {
      this.getApproveRulesList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.getApproveRulesList()
    }, 300),
    // 重置
    onReset: debounce(function () {}, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getApproveRulesList() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundAdminSatisfactionStatisticsPost({
          ...this.formatQueryParams(this.searchFormSetting)
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results || []
      } else {
        this.$message.error(res.msg)
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      this.selectList = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
        this.selectList.push(item)
      })
    },
    // 查看
    gotoAddOrEdit(data) {
      if (this.allPermissions.includes('background.admin.satisfaction.detail_list')) {
        let query = {
          data: JSON.stringify(data)
        }
        this.$router.push({
          name: 'MerchantSatisfactionDetail',
          query
        })
      } else {
        this.$message.error("权限不足")
      }
    }
  }
}
</script>
