import * as dayjs from 'dayjs'
export const weekList = [
  {
    key: 'all',
    name: '全部'
  },
  {
    key: '1',
    name: '周一'
  },
  {
    key: '2',
    name: '周二'
  },
  {
    key: '3',
    name: '周三'
  },
  {
    key: '4',
    name: '周四'
  },
  {
    key: '5',
    name: '周五'
  },
  {
    key: '6',
    name: '周六'
  },
  {
    key: '7',
    name: '周日'
  }
]

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const punchStatuaList = [
  {
    key: 'sign_in',
    name: '签到'
  },
  {
    key: 'sign_out',
    name: '签退'
  },
  {
    key: 'be_late',
    name: '迟到'
  },
  {
    key: 'leave_early',
    name: '早退'
  },
  {
    key: 'for_leave',
    name: '请假'
  },
  {
    key: 'absence_work',
    name: '缺卡'
  }
]

export const getRequestParams = (searchFormSetting, page, pageSize) => {
  const searchData = {}
  Object.keys(searchFormSetting).forEach(key => {
    if (
      key !== 'select_time' &&
      searchFormSetting[key].value !== '' &&
      searchFormSetting[key].value
    ) {
      searchData[key] = searchFormSetting[key].value
    } else if (typeof searchFormSetting[key].value === 'boolean') {
      searchData[key] = searchFormSetting[key].value
    }
  })
  const params = {
    page,
    page_size: pageSize,
    ...searchData
  }

  if (searchFormSetting.select_time?.value?.length === 2) {
    params.start_date = searchFormSetting.select_time.value[0] + ' 00:00:00'
    params.end_date = searchFormSetting.select_time.value[1] + ' 23:59:59'
  }

  return params
}
