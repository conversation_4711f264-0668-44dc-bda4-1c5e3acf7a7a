<template>
  <div class="diet-crowd container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">膳食营养表单</div>
      </div>
      <div>
        <el-form
          ref="formData"
          v-loading="formLoading"
          :rules="formDataRuls"
          :model="formData"
          class="dialog-form"
        >
          <el-form-item label="名称：" prop="name" label-width="100px">
            <el-input
              class="ps-input"
              style="width: 190px"
              placeholder="请输入名称"
              v-model="formData.name"
            ></el-input>
          </el-form-item>
          <el-form-item label="人群：" prop="group" label-width="100px">
            <el-input
              class="ps-input"
              style="width: 190px"
              placeholder="请输入人群"
              v-model="formData.group"
            ></el-input>
          </el-form-item>
          <el-form-item label="性别" label-width="100px">
            <el-radio-group v-model="formData.gender">
              <el-radio label="MAN" class="ps-radio">男</el-radio>
              <el-radio label="WOMEN" class="ps-radio">女</el-radio>
              <el-radio label="OTHER" class="ps-radio">其他</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="特殊人群："
            prop=""
            label-width="100px"
            v-if="formData.gender == 'WOMEN'"
          >
            <el-select
              v-model="formData.special_group"
              placeholder="请下拉选择人群"
              class="ps-select"
              popper-class="ps-popper-select"
              collapse-tags
              clearable
            >
              <el-option
                v-for="item in specialGroupList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <div class="age-box">
            <el-form-item label="最小年龄" label-width="100px" prop="min_age">
              <el-input v-model="formData.min_age" class="input"></el-input>
            </el-form-item>
            <el-form-item label="最大年龄" label-width="100px" prop="max_age">
              <el-input v-model="formData.max_age" class="input"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="来源" label-width="100px">
            <el-input
              type="textarea"
              style="width: 300px"
              :autosize="{ minRows: 2, maxRows: 10 }"
              placeholder="请输入内容"
              v-model="formData.source"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">膳食营养设置</div>
        <!-- <div class="align-r">
          <button-icon color="origin" type="" @click="tableDataSplitbb">编辑</button-icon>
        </div> -->
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table :data="tableDataNutrition" ref="tableDataNutrition" style="width: 100%" border>
          <el-table-column
            prop=""
            label="中国居民常量和微量元素的参考摄入量(RNIS)或推荐摄入量(AI)、可耐最高摄入量(UL)"
            align="center"
          >
            <template>
              <column-item
                v-for="item in tableDataNutritionData"
                :key="item.label"
                :col="item"
              ></column-item>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
    </div>
    <div class="table-wrapper">
      <!-- <div class="table-header"> -->
      <!-- <div class="table-title"></div> -->
      <!-- </div> -->
      <div class="align-r diet-btn">
        <button-icon color="plain" type="" @click="cancel">取消</button-icon>
        <button-icon color="origin" type="" :loading="dietCrowdLoading" @click="preservationDiet">
          保存
        </button-icon>
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import columnItem from '../components/ColumnItem' // 组件递归
import { dietCrowdData } from '../components/dietCrowdNutritionData'

export default {
  name: 'DietCrowd',
  components: {
    columnItem
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      dietCrowdLoading: false, // 刷新数据
      tableDataNutrition: [],
      tableDataNutritionData: dietCrowdData,
      formLoading: false,
      formDataRuls: {
        name: [{ required: true, message: '请输入名字', trigger: 'blur' }],
        group: [{ required: true, message: '请输入人群', trigger: 'blur' }],
        min_age: [{ required: true, message: '请输入最小年龄', trigger: 'blur' }],
        max_age: [{ required: true, message: '请输入最大年龄', trigger: 'blur' }]
      },
      formData: {
        name: '',
        group: '',
        gender: 'MAN',
        special_group: '',
        min_age: '',
        max_age: '',
        source: ''
      },
      specialGroupList: [
        { name: '孕妇(早期)', id: 1 },
        { name: '孕妇(中期)', id: 2 },
        { name: '孕妇(晚期)', id: 3 },
        { name: '哺乳期', id: 4 }
      ],
      type: '',
      id: ''
    }
  },
  created() {
    if (this.$route.query.type) {
      this.type = this.$route.query.type
      if (this.$route.query.type === 'edit') {
        this.id = this.$route.query.id
        this.getFoodDietGroupList(this.$route.query.id)
      }
    }
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.initTableDataNutrition()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    initTableDataNutrition() {
      // 拿到营养table数据
      let obj = {}
      this.calleArr(dietCrowdData, obj, 1)
      this.tableDataNutrition.push(obj)
    },
    // 人群新增
    async setFoodDietGroupAdd() {
      this.dietCrowdLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodDietGroupAddPost({
          name: this.formData.name,
          group: this.formData.group,
          gender: this.formData.gender,
          special_group: this.formData.special_group,
          min_age: this.formData.min_age,
          max_age: this.formData.max_age,
          source: this.formData.source,
          ...this.initNutrition()
        })
      )
      this.dietCrowdLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 人群修改
    async setFoodDietGroupModify() {
      this.dietCrowdLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodDietGroupModifyPost({
          id: this.id,
          name: this.formData.name,
          group: this.formData.group,
          gender: this.formData.gender,
          special_group: this.formData.special_group,
          min_age: this.formData.min_age,
          max_age: this.formData.max_age,
          source: this.formData.source,
          ...this.initNutrition()
        })
      )
      this.dietCrowdLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 人群列表
    async getFoodDietGroupList(id) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodDietGroupListPost({
          id: id,
          page: 1,
          page_size: 99
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.formData = {
          name: res.data.results[0].name,
          group: res.data.results[0].group,
          gender: res.data.results[0].gender,
          special_group: res.data.results[0].special_group ? res.data.results[0].special_group : '',
          min_age: res.data.results[0].min_age,
          max_age: res.data.results[0].max_age,
          source: res.data.results[0].source
        }
        this.editRecurrence(res.data.results[0])
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
    },
    calleArr(arr, obj, deep, key) {
      for (var i = 0; i < arr.length; i++) {
        var data = arr[i]

        if (deep === 1) {
          key = data.key
        }

        if (data.children) {
          let deepNum = deep + 1
          // 是否还有下级
          this.calleArr(data.children, obj, deepNum, key) // 自己调用自己
        } else {
          data.prop = key + '_' + data.key
          obj[data.prop] = ''
        }
      }
    },
    editRecurrence(data) {
      let element = JSON.parse(data.element)
      let vitamin = JSON.parse(data.vitamin)
      this.tableDataNutrition[0].energy_mj = data.energy_mj
      this.tableDataNutrition[0].energy_kcal = data.energy_kcal
      this.tableDataNutrition[0].protein_40 = data.protein
      this.tableDataNutrition[0].axunge_20 = data.axunge
      for (const key in element) {
        this.tableDataNutrition[0][key + '_' + 'AI'] = element[key].AI
        this.tableDataNutrition[0][key + '_' + 'UL'] = element[key].UL
      }
      for (const key in vitamin) {
        this.tableDataNutrition[0][key + '_' + 'AI'] = vitamin[key].AI
        this.tableDataNutrition[0][key + '_' + 'UL'] = vitamin[key].UL
      }
    },
    initNutrition() {
      let energy = ['axunge', 'protein', 'energy']
      let element = ['Ca', 'P', 'K', 'Na', 'Mg', 'Fe', 'I', 'Zn', 'Se', 'Cu', 'F', 'Cr', 'Mn', 'Mo']
      let vitamin = [
        'VA',
        'VD',
        'VE',
        'VB1',
        'VB2',
        'VB6',
        'VB12',
        'VC',
        'VB5',
        'VM',
        'VB3',
        'Choline',
        'Nicotinamide',
        'VH'
      ]
      let obj = {
        energy_mj: '',
        energy_kcal: '',
        axunge: '',
        protein: '',
        element: {},
        vitamin: {}
      }
      for (const key in this.tableDataNutrition[0]) {
        let tableDataSplit = key.split('_')
        let value = this.tableDataNutrition[0][key] ? this.tableDataNutrition[0][key] : 0
        if (element.includes(tableDataSplit[0])) {
          if (!obj.element[tableDataSplit[0]]) {
            obj.element[tableDataSplit[0]] = {}
          }
          if (tableDataSplit[1] === 'AI') {
            obj.element[tableDataSplit[0]][tableDataSplit[1]] = value
          }
          if (tableDataSplit[1] === 'UL') {
            obj.element[tableDataSplit[0]][tableDataSplit[1]] = value
          }
        }
        if (vitamin.includes(tableDataSplit[0])) {
          if (!obj.vitamin[tableDataSplit[0]]) {
            obj.vitamin[tableDataSplit[0]] = {}
          }
          if (tableDataSplit[1] === 'AI') {
            obj.vitamin[tableDataSplit[0]][tableDataSplit[1]] = value
          }
          if (tableDataSplit[1] === 'UL') {
            obj.vitamin[tableDataSplit[0]][tableDataSplit[1]] = value
          }
        }
        if (energy.includes(tableDataSplit[0])) {
          if (tableDataSplit[1] === 'mj') {
            obj.energy_mj = value
          }
          if (tableDataSplit[1] === 'kcal') {
            obj.energy_kcal = value
          }
          if (tableDataSplit[0] === 'axunge') {
            obj.axunge = value
          }
          if (tableDataSplit[0] === 'protein') {
            obj.protein = value
          }
        }
      }
      obj.element = JSON.stringify(obj.element)
      obj.vitamin = JSON.stringify(obj.vitamin)
      return obj
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    cancel() {
      this.$closeCurrentTab(this.$route.path)
    },
    preservationDiet() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          if (this.type === 'add') {
            this.setFoodDietGroupAdd()
          } else if (this.type === 'edit') {
            this.setFoodDietGroupModify()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.diet-crowd {
  .age-box {
    display: flex;
    .input {
      width: 130px;
    }
  }
  .diet-btn {
    padding: 20px 20px 10px 0;
  }
}
</style>
