export default {
  // 充值渠道
  Payway(val) {
    if (val === 'PushiPay') {
      return '朴食储值支付'
    } else if (val === 'OCOMPAY') {
      return '一卡通-鑫澳康支付'
    } else if (val === 'SHIYAOPAY') {
      return '一卡通-石药支付'
    } else if (val === 'ABCPay') {
      return '农行支付'
    } else if (val === 'CCBPAY') {
      return '建行支付'
    } else if (val === 'BOCPAY') {
      return '中行支付'
    } else if (val === 'ICBCPAY') {
      return '工行支付'
    } else if (val === 'MEITUANPAY') {
      return '美团支付'
    } else if (val === 'ShouqianbaPay') {
      return '收钱吧支付'
    } else if (val === 'WechatPay') {
      return '微信支付'
    } else if (val === 'UNKNOWN') {
      return '未知'
    } else if (val === 'CashPay') {
      return '现金支付'
    } else if (val === '') {
      return '--'
    }
  },
  // 充值类型
  SubPayway(val) {
    if (val === 'wallet') {
      return '储值钱包支付'
    } else if (val === 'ewallet') {
      return '电子钱包支付'
    } else if (val === 'twallet') {
      return '第三方钱包'
    } else if (val === 'daikou') {
      return '授权代扣支付'
    } else if (val === 'ermb') {
      return '数字人民币支付'
    } else if (val === 'jsapi') {
      return 'JSAPI支付'
    } else if (val === 'h5') {
      return 'H5支付'
    } else if (val === 'wap') {
      return 'WAP支付'
    } else if (val === 'miniapp') {
      return '小程序支付'
    } else if (val === 'cash') {
      return '现金支付'
    } else if (val === 'micropay') {
      return 'B扫C支付'
    } else if (val === 'scanpay') {
      return 'C扫B支付'
    } else if (val === 'cardpay') {
      return '刷卡支付'
    } else if (val === 'facepay') {
      return '刷脸支付'
    } else if (val === 'facecode') {
      return '会员码支付'
    } else if (val === 'jf') {
      return '缴费方式支付'
    } else if (val === 'fastepay') {
      return '快e付支付'
    }
  },
  // 充值/预约 状态
  OrderStatus(val) {
    if (val === 'ORDER_PAYING') {
      return '待支付'
    } else if (val === 'ORDER_FAILED') {
      return '失败'
    } else if (val === 'ORDER_REVERSALING') {
      return '交易冲正中'
    } else if (val === 'ORDER_REVERSA') {
      return '交易冲正'
    } else if (val === 'ORDER_SUCCESS') {
      return '成功'
    } else if (val === 'ORDER_REFUNDING') {
      return '退款中'
    } else if (val === 'ORDER_CLOSE') {
      return '关闭（用户未支付）'
    } else if (val === 'ORDER_UNKNOWN') {
      return '未知'
    }
  },
  PayScene(val) {
    if (val === 'charge') {
      return '线上'
    } else if (val === 'charge_offline') {
      return '线下'
    }
  },
  MealType(val) {
    if (val === 'all') {
      return '全部'
    } else if (val === 'breakfast') {
      return '早餐'
    } else if (val === 'lunch') {
      return '午餐'
    } else if (val === 'afternoon') {
      return '下午茶'
    } else if (val === 'dinner') {
      return '晚餐'
    } else if (val === 'supper') {
      return '夜宵'
    } else if (val === 'morning') {
      return '凌晨餐'
    }
  }
}
