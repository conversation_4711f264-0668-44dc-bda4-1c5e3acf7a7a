<template>
  <div class="PrinterSettings container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <div v-loading="isLoading" class="table-wrapper custom-loading-container" style="margin-top: 0px; margin-bottom: 50px">
      <div class="table-header">
        <div>
          <div
            class="table-title"
            style="display: flex; align-items: center; justify-content: space-between; width: 380px"
          >
            适用组织：
            <organization-select
              class="search-item-w ps-input w-250"
              placeholder="请选择所属组织"
              :isLazy="false"
              :multiple="false"
              :check-strictly="true"
              v-model="organizationId"
              @change="changeOrganization"
              :loading="organizationLoading"
            ></organization-select>
          </div>
        </div>
        <div style="padding-right: 20px">
          <el-button
            size="small"
            type="primary"
            class="ps-origin-btn"
            v-loading="isLoading"
            @click="checkDetermine"
          >
            保存
          </el-button>
        </div>
      </div>
      <div class="setting-wrap">
        <el-form :model="settingForm" ref="settingForm">
          <div class="ps-flex flex-align-c">
            <div class="title">预约订单自动打印</div>
            <el-switch
              v-model="settingForm.reservationAutoPrint"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
            ></el-switch>
          </div>
          <div class="m-l-20">
            <div v-if="settingForm.reservationAutoPrint">
              <div>选择需自动打印的餐段以及时间</div>
              <el-form-item prop="">
                <div class="checkbox-time-wrapp m-l-20">
                  <el-checkbox-group v-model="checkAutoPrintTimeList">
                    <el-checkbox
                      v-for="(item, key) in settingForm.autoPrintMealAndTimeRange"
                      :label="key"
                      :key="key"
                      class="ps-checkbox"
                    >
                      <div class="m-b-10">
                        <span class="m-r-20">{{ formatMealName(key) }}</span>
                        <el-time-picker
                          v-model="settingForm.autoPrintMealAndTimeRange[key]"
                          value-format="HH:mm"
                          format="HH:mm"
                          placeholder="选择时间"
                          class="ps-picker w-150"
                          size="mini"
                        ></el-time-picker>
                      </div>
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </el-form-item>
            </div>
            <div>打印机设置（请选择打印机对应打印的区域）</div>
            <el-form-item prop="">
              <div
                v-for="(printerItem, index) in printerList"
                :key="index"
                class="ps-flex flex-align-c"
              >
                <!-- 打印机 -->
                <el-select
                  v-model="printerItem.sn"
                  placeholder="请选择"
                  class="ps-select m-r-10 w-150"
                  popper-class="ps-popper-select"
                  size="mini"
                  @change="changePrinter(printerItem)"
                >
                  <el-option
                    v-for="item in printDeviceList"
                    :key="item.sn"
                    :label="item.name"
                    :value="item.sn"
                  ></el-option>
                </el-select>
                <el-select
                  v-model="printerItem.printer_type"
                  placeholder="请选择"
                  class="ps-select m-r-10 w-150"
                  popper-class="ps-popper-select"
                  size="mini"
                  :disabled="!printerItem.sn"
                  @change="changePrinterType(printerItem)"
                >
                  <el-option
                    v-for="item in consumeTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
                <!-- 餐段 -->
                <el-select
                  v-model="printerItem.order_type"
                  placeholder="请选择"
                  class="ps-select m-r-10 w-150"
                  popper-class="ps-popper-select"
                  size="mini"
                  :disabled="!printerItem.printer_type"
                  @change="changSelect(printerItem)"
                >
                  <el-option
                    v-for="item in reservationMealMode"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="printerItem.disabledOrderType.includes(item.value)"
                  ></el-option>
                </el-select>
                <!-- 区域 -->
                <el-select
                  v-if="printerItem.order_type === 'waimai'"
                  v-model="printerItem.adders_area"
                  placeholder="请选择"
                  class="ps-select m-r-10"
                  style="width: 190px"
                  popper-class="ps-popper-select"
                  size="mini"
                  multiple
                  collapse-tags
                  @change="changePrinterArea"
                  :disabled="!printerItem.sn"
                >
                  <el-option
                    v-for="item in addersArea"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                    :disabled="printerItem.disabledArea.includes(item.id)"
                  ></el-option>
                </el-select>
                <!-- 取餐柜 -->
                <el-select
                  v-if="printerItem.order_type === 'cupboard'"
                  v-model="printerItem.cupboard_ids"
                  placeholder="请选择"
                  class="ps-select m-r-10"
                  style="width: 190px"
                  popper-class="ps-popper-select"
                  size="mini"
                  multiple
                  collapse-tags
                  @change="changePrinterArea"
                  :disabled="!printerItem.sn"
                >
                  <el-option
                    v-for="item in deviceList"
                    :key="item.device_no"
                    :label="item.device_name"
                    :value="item.device_no"
                    :disabled="printerItem.disabledCupboard.includes(item.device_no)"
                  ></el-option>
                </el-select>
                <img
                  src="@/assets/img/plus.png"
                  @click="clickPlusPrinterSetting(printerItem)"
                  class="m-r-10 pointer"
                />
                <img
                  src="@/assets/img/reduce.png"
                  class="pointer"
                  v-if="printerList.length > 1"
                  @click="clickReducePrinterSetting(index)"
                />
              </div>
            </el-form-item>
          </div>
          <div class="ps-flex flex-align-c">
            <div class="title">卡务操作订单自动打印</div>
            <el-switch
              v-model="settingForm.cardOperateAutoPrint"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
            ></el-switch>
          </div>
          <el-form-item v-if="settingForm.cardOperateAutoPrint">
            <div
              v-for="(cardOperatePrinterItem, index) in cardOperatePrinterList"
              :key="index"
              class="ps-flex flex-align-c"
            >
              <!-- 打印机 -->
              <el-select
                v-model="cardOperatePrinterItem.sn"
                placeholder="请选择"
                class="ps-select m-r-10 w-150"
                popper-class="ps-popper-select"
                size="mini"
                @change="changePrinterCardOperate(cardOperatePrinterItem)"
              >
                <el-option
                  v-for="item in printDeviceList"
                  :key="item.sn"
                  :label="item.name"
                  :value="item.sn"
                ></el-option>
              </el-select>
              <!-- 消费 -->
              <el-select
                v-model="cardOperatePrinterItem.card_operate_type"
                placeholder="请选择"
                class="ps-select m-r-10 w-150"
                popper-class="ps-popper-select"
                size="mini"
                multiple
                collapse-tags
                :disabled="!cardOperatePrinterItem.sn"
                @change="changeCardOperate"
              >
                <el-option
                  v-for="item in cardOperatePrinterItem.mealList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <img
                src="@/assets/img/plus.png"
                class="m-r-10 pointer"
                @click="clickPlusCardOperatePrinter(cardOperatePrinterItem)"
              />
              <img
                src="@/assets/img/reduce.png"
                class="pointer"
                v-if="cardOperatePrinterList.length > 1"
                @click="clickReduceCardOperatePrinter(index)"
              />
            </div>
          </el-form-item>
          <!-- 堂食订单自主打印-->
          <div class="ps-flex flex-align-c">
            <div class="title">堂食订单自动打印</div>
            <el-switch
              v-model="settingForm.onSceneOrderAutoPrint"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
            ></el-switch>
          </div>
          <el-form-item v-if="settingForm.onSceneOrderAutoPrint"  v-loading="isShowLoadingDevice">
            <div
              v-for="(onSceneItem, index) in onScencePrinterList"
              :key="index"
              class="ps-flex flex-align-c"
            >
              <!-- 打印机 -->
              <el-select
                v-model="onSceneItem.sn"
                placeholder="请选择"
                class="ps-select m-r-10 w-150"
                popper-class="ps-popper-select"
                size="mini"
                @change="changePrinterOnScence(onSceneItem,'print',index)"
              >
                <el-option
                  v-for="item in printDeviceList"
                  :key="item.sn"
                  :label="item.name"
                  :value="item.sn"
                ></el-option>
              </el-select>
              <!-- 消费单 -->
              <el-select
                v-model="onSceneItem.consumeType"
                placeholder="请选择"
                class="ps-select m-r-10 w-150"
                popper-class="ps-popper-select"
                size="mini"
                :disabled="!onSceneItem.sn"
                @change="changePrinterOnScence(onSceneItem,'consumeType',index)"
              >
                <el-option
                  v-for="item in consumeTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                ></el-option>
              </el-select>
               <!-- 设备类型 -->
               <el-select
                v-model="onSceneItem.deviceType"
                placeholder="请选择"
                class="ps-select m-r-10 w-150"
                popper-class="ps-popper-select"
                size="mini"
                multiple
                collapse-tags
                :disabled="!onSceneItem.consumeType"
                @change="changePrinterOnScence(onSceneItem,'deviceType',index)"
              >
                <el-option
                  v-for="item in deviceTypeList"
                  :key="item.key"
                  :label="item.name"
                  :value="item.key"
                  :disabled="item.disabled"
                ></el-option>
              </el-select>
               <!-- 具体设备 -->
               <el-select
                v-model="onSceneItem.deviceId"
                placeholder="请选择"
                class="ps-select m-r-10 w-350"
                popper-class="ps-popper-select"
                size="mini"
                multiple
                collapse-tags
                :disabled="(!onSceneItem.deviceType || onSceneItem.deviceType.length == 0)"
                @change="changePrinterOnScence(onSceneItem,'deviceId',index)"
                @focus="onfocusDeviceSelect(onSceneItem.sn,onSceneItem.consumeType,index,onSceneItem.deviceId)"
              >
                <el-option
                  v-for="item in onSceneItem.deviceDetailList"
                  :key="item.device_no"
                  :label="item.device_name"
                  :value="item.device_no"
                  :disabled="item.disabled"
                ></el-option>
              </el-select>
              <img
                src="@/assets/img/plus.png"
                @click="clickPlusOnScencePrinterSetting(onSceneItem)"
                class="m-r-10 pointer"
              />
              <img
                src="@/assets/img/reduce.png"
                class="pointer"
                v-if="onScencePrinterList.length > 1"
                @click="clickReduceOnScencePrinterSetting(index)"
              />
            </div>
          </el-form-item>
          <div class="">
            <div class="title">其它</div>
            <div class="m-l-20">
              <el-checkbox v-model="settingForm.printNutritionInfo" class="ps-checkbox">打印营养信息</el-checkbox>
              <el-checkbox v-model="settingForm.nameMask" class="ps-checkbox">姓名脱敏</el-checkbox>
              <el-checkbox v-model="settingForm.phoneDesensitize" class="ps-checkbox">手机号脱敏</el-checkbox>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
// 打印机设置
// 食堂自取和堂食不用选择区域；
// A打印机选择了堂食 或者 食堂自取 则B打印机不能选择堂食 或者 食堂自取;
// A打印机选择了外卖或者取餐柜的k区域 新增一条数据的A打印机不能选择外卖或者取餐柜的K区域 B打印机可以选择 如此类推
// 卡务操作订单自动打印
// A打印机选择了充值类型 B打印机不能选择充值类型
import { to, deepClone } from '@/utils'
import OrganizationSelect from '@/components/OrganizationSelect'
var cardOperateTypeList = [
  {
    value: 'CHARGE',
    label: '充值',
    disabled: false
  },
  {
    value: 'CHARGE_REFUND',
    label: '充值退款',
    disabled: false
  },
  {
    value: 'WITHDRAW',
    label: '提现',
    disabled: false
  },
  {
    value: 'PUBLISH',
    label: '发卡',
    disabled: false
  },
  // {
  //   value: 'QUIT',
  //   label: '退卡',
  //   disabled: false
  // },
  {
    value: 'SUPPLEMENTARY',
    label: '补卡',
    disabled: false
  },
  {
    value: 'FLAT_COST_REFUND',
    label: '退费',
    disabled: false
  }
]
export default {
  name: 'RoutineSetting',
  components: {
    OrganizationSelect
  },
  data() {
    return {
      checkAutoPrintTimeList: [],
      isLoading: false,
      organizationId: '',
      printDeviceList: [],
      reservationMealMode: [
        {
          label: '预约(堂食)',
          value: 'on_scene',
          disabled: false
        },
        {
          label: '预约(食堂自提)',
          value: 'bale',
          disabled: false
        },
        {
          label: '取餐柜',
          value: 'cupboard',
          disabled: false
        },
        {
          label: '预约(外卖)',
          value: 'waimai',
          disabled: false
        }
      ],
      addersArea: [],
      deviceList: [], // 取餐柜
      // 打印机设置
      printerList: [
        {
          sn: '',
          order_type: '',
          disabledOrderType: [],
          disabledArea: [],
          adders_area: [],
          disabledCupboard: [], // 取餐柜
          cupboard_ids: [], // 取餐柜
          printer_type: ''
        }
      ],
      allSelectMeal: [],
      // 卡务操作订单自动打印
      cardOperatePrinterList: [
        {
          sn: '',
          card_operate_type: [],
          mealList: deepClone(cardOperateTypeList)
        }
      ],
      settingForm: {
        reservationAutoPrint: false,
        printNutritionInfo: false, // 打印营养信息
        autoPrintMealAndTimeRange: {
          breakfast: '',
          lunch: '',
          afternoon: '',
          dinner: '',
          supper: '',
          morning: ''
        },
        cardOperateAutoPrint: false,
        onSceneOrderAutoPrint: false, // 堂食订单自动打印
        phoneDesensitize: false, // 手机号是否脱敏
        nameMask: true // 姓名脱敏
      },
      organizationLoading: false,
      deviceTypeList: [], // 设备类型列表
      deviceDetailList: [], // 详细设备列表
      consumeTypeList: [{ // 消费类型列表
        label: '后厨单',
        value: 'kitchen',
        disabled: false
      },
      {
        label: '结账单',
        value: 'bill',
        disabled: false
      }
      ],
      onScencePrinterList: [
        {
          sn: '', // 打印机
          consumeType: '', // 消费类型
          deviceType: [], // 设备类型
          deviceId: [] // 设备
        }
      ],
      bandingDeviceList: [], // 存放绑定
      isShowLoadingDevice: false // 是否显示设备打印
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.organizationId = Number(sessionStorage.getItem('organization'))
      await this.getPrinterList()
      await this.getDeviceList()
      await this.getAddressAreaList()
      await this.getDeviceTypeList()
      this.getPrinterSettings()
    },
    getInitData() {
      this.checkAutoPrintTimeList = []
      // 打印机设置
      this.printerList = [
        {
          sn: '',
          order_type: '',
          disabledOrderType: [], // 禁止选中项
          disabledArea: [],
          adders_area: [],
          disabledCupboard: [], // 取餐柜
          cupboard_ids: [], // 取餐柜
          printer_type: ''
        }
      ]
      // 卡务操作订单自动打印
      this.cardOperatePrinterList = [
        {
          sn: '',
          card_operate_type: [],
          mealList: deepClone(cardOperateTypeList)
        }
      ]
      this.settingForm = {
        reservationAutoPrint: false,
        printNutritionInfo: false,
        autoPrintMealAndTimeRange: {
          breakfast: '',
          lunch: '',
          afternoon: '',
          dinner: '',
          supper: '',
          morning: ''
        },
        cardOperateAutoPrint: false,
        onSceneOrderAutoPrint: false,
        phoneDesensitize: false,
        nameMask: true
      }

      this.onScencePrinterList = [
        {
          sn: '', // 打印机
          consumeType: '', // 消费类型
          deviceType: [], // 设备类型
          deviceId: [] // 设备
        }
      ]
    },
    async getPrinterList() {
      if (!this.organizationId) return
      this.isLoading = true
      let params = {
        org_ids: [this.organizationId],
        page: 1,
        page_size: 9999
      }
      const [err, res] = await to(this.$apis.apiBackgroundPrinterPrinterListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.printDeviceList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设备列表
    async getDeviceList() {
      if (!this.organizationId) return
      const res = await this.$apis.apiBackgroundDeviceDeviceListPost({
        use_organization_id: this.organizationId,
        activation_status: true,
        device_type: 'QCG',
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.deviceList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getAddressAreaList() {
      if (!this.organizationId) return
      let params = {
        organization: this.organizationId,
        page: 1,
        page_size: 9999
      }
      const [err, res] = await to(this.$apis.apiAddressAddersAreaListPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.addersArea = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 保存设置
    async setPrinterSettings(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundPrinterPrinterAddPrinterSettingsPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('设置成功')
        this.getPrinterSettings()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设置
    async getPrinterSettings() {
      if (!this.organizationId) return
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundPrinterPrinterGetPrinterSettingsPost({
          org_id: this.organizationId
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data) {
          let getPrinterSettingsData = res.data
          this.settingForm.reservationAutoPrint = getPrinterSettingsData.reservation_auto_print
          this.settingForm.printNutritionInfo = getPrinterSettingsData.print_nutrition_info
          this.settingForm.cardOperateAutoPrint = getPrinterSettingsData.card_operate_auto_print
          // 选择需自动打印的餐段以及时间
          if (Object.keys(getPrinterSettingsData.auto_print_meal_and_time).length) {
            for (const key in getPrinterSettingsData.auto_print_meal_and_time) {
              this.checkAutoPrintTimeList.push(key)
              this.settingForm.autoPrintMealAndTimeRange[key] =
                getPrinterSettingsData.auto_print_meal_and_time[key]
            }
          }
          // 打印机设置
          if (getPrinterSettingsData.printer_list.length) {
            const printerListData = getPrinterSettingsData.printer_list.map(v => {
              const printDeviceItem = this.printDeviceList.find(item => item.sn === v.sn)
              if (!printDeviceItem) return null
              const addersArea = v.adders_area ? this.addersArea.filter(areaItem => v.adders_area.includes(areaItem.id)).map(item => item.id) : []
              const cupboardIds = v.cupboard_ids ? this.deviceList.filter(deviceItem => v.cupboard_ids.includes(deviceItem.device_no)).map(item => item.device_no) : []

              return {
                sn: v.sn,
                order_type: v.order_type,
                disabledOrderType: [], // 禁止选中项
                disabledArea: addersArea,
                adders_area: addersArea,
                disabledCupboard: cupboardIds,
                cupboard_ids: cupboardIds,
                printer_type: v.printer_type
              }
            }).filter(Boolean)
            // printerListData 没有值的时候默认一个
            if (printerListData.length) {
              this.printerList = printerListData
            }
          }
          if (getPrinterSettingsData.card_operate_printer_list.length) {
            const cardOperatePrinterData = getPrinterSettingsData.card_operate_printer_list.map(v => {
              const printDeviceItem = this.printDeviceList.find(item => item.sn === v.sn)
              if (!printDeviceItem) return null
              return {
                sn: v.sn,
                card_operate_type: v.card_operate_type ? v.card_operate_type : [],
                mealList: deepClone(cardOperateTypeList)
              }
            }).filter(Boolean)
            this.cardOperatePrinterList = cardOperatePrinterData
          }
          this.isDisabledMeal()
          this.isDisabledArea()
          // this.isDisabledCardOperate()
          // 更新堂食自动打印
          this.updateOnScenceAutoPrinter(getPrinterSettingsData)
          //  如果预约点餐自动打印是false,预设一些餐段的时间可以方便设置
          if (!this.settingForm.reservationAutoPrint) {
            this.setMeanDataDefault()
          }
          // 更新手机是否脱敏
          if (Reflect.has(getPrinterSettingsData, 'phone_mask')) {
            this.settingForm.phoneDesensitize = getPrinterSettingsData.phone_mask
          }
          // 更新姓名是否脱敏
          if (Reflect.has(getPrinterSettingsData, 'name_mask')) {
            this.settingForm.nameMask = getPrinterSettingsData.name_mask
          }
        } else {
          this.getInitData()
          this.setMeanDataDefault()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    formatMealName(key) {
      let name = ''
      switch (key) {
        case 'breakfast':
          name = '早餐'
          break
        case 'lunch':
          name = '午餐'
          break
        case 'afternoon':
          name = '下午茶'
          break
        case 'dinner':
          name = '晚餐'
          break
        case 'supper':
          name = '宵夜'
          break
        case 'morning':
          name = '凌晨餐'
          break
      }
      return name
    },
    // 添加打印机设置
    clickPlusPrinterSetting(item) {
      if (!item.sn) return this.$message.error('请选择打印机')
      if (!item.printer_type) return this.$message.error('请选择单据类型')
      if (!item.order_type) return this.$message.error('请选择取餐方式')
      if (item.order_type === 'waimai' && !item.adders_area.length) {
        return this.$message.error('请选择区域')
      }
      if (item.order_type === 'cupboard' && !item.cupboard_ids.length) {
        return this.$message.error('请选择取餐柜')
      }
      this.printerList.push({
        sn: '',
        order_type: '',
        disabledOrderType: [],
        disabledArea: [],
        adders_area: [],
        disabledCupboard: [],
        cupboard_ids: [],
        printer_type: ''
      })
      this.isDisabledMeal()
      this.isDisabledArea()
    },
    // 删除单条打印机设置
    clickReducePrinterSetting(index) {
      this.printerList.splice(index, 1)
      this.isDisabledMeal()
      this.isDisabledArea()
    },
    changePrinter(data) {
      this.clearOrdertypeAndAddersareaAndCupboardids(data)
      this.isDisabledMeal()
      this.isDisabledArea()
    },
    changePrinterType(data) {
      this.clearOrdertypeAndAddersareaAndCupboardids(data)
      this.isDisabledArea()
      this.isDisabledMeal()
    },
    changePrinterArea() {
      this.isDisabledArea()
    },
    // 选择打印机餐段
    changSelect(data) {
      data.adders_area = []
      this.isDisabledMeal()
      this.isDisabledArea()
    },
    clearOrdertypeAndAddersareaAndCupboardids(data) {
      data.order_type = ''
      data.adders_area = []
      data.cupboard_ids = []
    },
    // 打印机餐段
    isDisabledMeal(current) {
      // 旧版
      // this.reservationMealMode.forEach(ele => {
      //   ele.disabled = false
      //   this.printerList.forEach(element => {
      //     if (element.order_type === ele.value) {
      //       if (element.order_type === 'on_scene' || element.order_type === 'bale') {
      //         ele.disabled = true
      //       }
      //     }
      //   })
      // })

      // 新版
      // 定义个选中数据的对象，方便之后使用
      let selectObj = {}
      // 第一次遍历获取selectObj数据
      this.printerList.forEach((item, index) => {
        // 只有on_scene或bale才有前三级的判断
        // 遍历下已选的，因为数据有先后的，所以能确保每个数据是唯一
        // sn和printer_type必须要有值才能做数据保存
        if ((item.order_type === 'on_scene' || item.order_type === 'bale') && (item.sn && item.printer_type)) {
          // 根据选中的值构建唯一key
          let key = `${item.sn}-${item.printer_type}`
          if (!selectObj[key]) {
            selectObj[key] = [item.order_type]
          } else {
            selectObj[key].push(item.order_type)
          }
        }
      })
      // 第二次遍历，禁选判断
      this.printerList.forEach((item, index) => {
        // item.order_type不是cupboard或waimai走禁用判断
        if (item.order_type !== 'cupboard' || item.order_type !== 'waimai') {
          let key = `${item.sn}-${item.printer_type}`
          if (selectObj[key]) {
            let k = selectObj[key].indexOf(item.order_type)
            if (k > -1) {
              let disabledList = selectObj[key].slice()
              disabledList.splice(k, 1)
              item.disabledOrderType = disabledList
            } else {
              item.disabledOrderType = selectObj[key].slice()
            }
          } else {
            item.disabledOrderType = []
          }
        }
      })
      // console.log(32323232, this.printerList)
    },
    // 打印机区域
    isDisabledArea() {
      this.printerList.map((item, k) => {
        let disabledArea = []
        let disabledCupboard = []
        for (let index = 0; index < this.printerList.length; index++) {
          let current = this.printerList[index]
          if (
            item.order_type === 'waimai' &&
            item.order_type === current.order_type &&
            item.printer_type === current.printer_type &&
            item.sn === current.sn &&
            k !== index
          ) {
            disabledArea.push(...current.adders_area)
          } else if (
            item.order_type === 'cupboard' &&
            item.order_type === current.order_type &&
            item.printer_type === current.printer_type &&
            item.sn === current.sn &&
            k !== index
          ) {
            disabledCupboard.push(...current.cupboard_ids)
          } else {
            continue
          }
        }
        this.$set(item, 'disabledArea', disabledArea)
        this.$set(item, 'disabledCupboard', disabledCupboard)
      })
    },
    // 添加卡务操作
    clickPlusCardOperatePrinter(item) {
      if (!item.sn) return this.$message.error('请选择打印机')
      if (!item.card_operate_type.length) return this.$message.error('请选择餐段')
      this.cardOperatePrinterList.push({
        sn: '',
        card_operate_type: [],
        mealList: deepClone(cardOperateTypeList)
      })
      // this.isDisabledCardOperate()
    },
    // 删除单条卡务操作
    clickReduceCardOperatePrinter(index) {
      this.cardOperatePrinterList.splice(index, 1)
      // this.isDisabledCardOperate()
    },
    changePrinterCardOperate(data) {
      data.card_operate_type = []
    },
    changeCardOperate() {
      // this.isDisabledCardOperate()
    },
    // 卡务操作选择类型
    isDisabledCardOperate() {
      this.allSelectMeal = []
      this.cardOperatePrinterList.map((item, k) => {
        this.allSelectMeal = this.allSelectMeal.concat(item.card_operate_type)
      })
      this.cardOperatePrinterList.forEach((v, k) => {
        v.mealList.forEach(meal => {
          if (
            this.allSelectMeal.includes(meal.value) &&
            !v.card_operate_type.includes(meal.value)
          ) {
            meal.disabled = true
          } else {
            meal.disabled = false
          }
        })
      })
    },
    // 确定
    checkDetermine() {
      let params = {
        organization: this.organizationId,
        reservation_auto_print: this.settingForm.reservationAutoPrint,
        print_nutrition_info: this.settingForm.printNutritionInfo,
        auto_print_meal_and_time: {},
        printer_list: [],
        card_operate_auto_print: this.settingForm.cardOperateAutoPrint,
        card_operate_printer_list: [],
        instore_order_auto_print: this.settingForm.onSceneOrderAutoPrint,
        instore_order_printer_list: [],
        phone_mask: this.settingForm.phoneDesensitize,
        name_mask: this.settingForm.nameMask
      }
      // 选择需自动打印的餐段以及时间 处理数据
      if (this.checkAutoPrintTimeList.length) {
        for (const key in this.settingForm.autoPrintMealAndTimeRange) {
          if (
            this.checkAutoPrintTimeList.includes(key) &&
            this.settingForm.autoPrintMealAndTimeRange[key]
          ) {
            params.auto_print_meal_and_time[key] = this.settingForm.autoPrintMealAndTimeRange[key]
          }
        }
      }
      for (let index = 0; index < this.printerList.length; index++) {
        let printer = this.printerList[index]
        if (printer.sn && printer.order_type === 'cupboard' && !printer.cupboard_ids.length) {
          return this.$message.error(`打印机设置中的第${index + 1}条数据请选择取餐柜`)
        }
        if (printer.sn && printer.order_type === 'waimai' && !printer.adders_area.length) {
          return this.$message.error(`打印机设置中的第${index + 1}条数据请选择区域`)
        }
        if (printer.sn && (printer.order_type === 'cupboard' || printer.order_type === 'waimai')) {
          let objData = {}
          if (printer.order_type === 'cupboard') {
            objData = { cupboard_ids: printer.cupboard_ids }
          } else if (printer.order_type === 'waimai') {
            objData = { adders_area: printer.adders_area }
          }
          params.printer_list.push({
            sn: printer.sn,
            order_type: printer.order_type,
            printer_type: printer.printer_type,
            ...objData
          })
        }
        // 可以把这个判断做成一个 但是push 进去要少一个字段
        if (printer.sn && (printer.order_type === 'on_scene' || printer.order_type === 'bale')) {
          params.printer_list.push({
            sn: printer.sn,
            order_type: printer.order_type,
            printer_type: printer.printer_type
          })
        }
      }

      if (this.settingForm.cardOperateAutoPrint) {
        for (let index = 0; index < this.cardOperatePrinterList.length; index++) {
          let cardOperate = this.cardOperatePrinterList[index]
          if (cardOperate.sn && !cardOperate.card_operate_type.length) {
            return this.$message.error(`卡务操作订单自动打印中的第${index + 1}条数据请选择方式`)
          }
          if (cardOperate.sn) {
            params.card_operate_printer_list.push({
              sn: cardOperate.sn,
              card_operate_type: cardOperate.card_operate_type
            })
          }
        }
      }
      // 堂食订单自动打印
      if (this.settingForm.onSceneOrderAutoPrint) {
        // 检测用户有没有填完整
        for (let i = 0; i < this.onScencePrinterList.length; i++) {
          let onScenceItem = this.onScencePrinterList[i]
          if (!onScenceItem.sn || !onScenceItem.consumeType || !onScenceItem.deviceType || onScenceItem.deviceType.length <= 0 || !onScenceItem.deviceId || onScenceItem.deviceId.length <= 0) {
            return this.$message.error(`堂食订单自动打印中的第${i + 1}条数据请选择方式`)
          }
          // 组参
          params.instore_order_printer_list.push({
            sn: onScenceItem.sn,
            printer_type: onScenceItem.consumeType,
            device_type: onScenceItem.deviceType,
            device_ids: onScenceItem.deviceId
          })
        }
      } else {
        params.instore_order_printer_list = []
      }

      this.setPrinterSettings(params)
    },
    // 刷新页面
    refreshHandle() {},
    async changeOrganization() {
      if (!this.organizationId) return this.$message.error('请先选择组织')
      this.organizationLoading = true
      this.getInitData()
      await this.getPrinterList()
      await this.getDeviceList()
      await this.getAddressAreaList()
      await this.getDeviceTypeList()
      await this.getPrinterSettings()
      this.organizationLoading = false
    },
    /**
     * 选择打印机
     * @param {*} val 选中的值
     * @param type 选择的类型
     * @param index  行标签，从0开始
     */
    changePrinterOnScence(val, type, index) {
      console.log("changePrinterOnScence", val);
      // 切换的时候更新后面数据
      switch (type) {
        // 选择设备类型
        case "deviceType":
          this.getDeviceDetailList(val.deviceType, val.sn, val.consumeType, index, val.deviceId)
          break;
        default:
          break;
      }
      this.resetOnScenceChoosedata(index, type)
    },
    // 更新绑定列表
    updateBandingList() {
      var list = []
      if (Array.isArray(this.onScencePrinterList) && this.onScencePrinterList.length > 0) {
        this.onScencePrinterList.forEach(item => {
          if (item && Array.isArray(item.deviceId) && item.deviceId.length > 0) {
            item.deviceId.forEach(subItem => {
              var itemChoose = {
                sn: item.sn,
                consumeType: item.consumeType
              }
              let deviceId = subItem
              itemChoose.deviceId = deviceId
              console.log("deviceId", deviceId, itemChoose);
              list.push(itemChoose)
            })
          }
        })
        this.bandingDeviceList = deepClone(list)
        console.log("this.bandingDeviceList", this.bandingDeviceList);
      } else {
        this.bandingDeviceList = []
      }
    },
    // 重置信息
    resetOnScenceChoosedata(index, type) {
      var onScenceList = deepClone(this.onScencePrinterList)
      if (index >= 0 && index < onScenceList.length) {
        onScenceList[index].consumeType = type === "print" ? '' : this.onScencePrinterList[index].consumeType
        onScenceList[index].deviceType = (type === "print" || type === "consumeType") ? [] : this.onScencePrinterList[index].deviceType
        onScenceList[index].deviceId = type !== "deviceId" ? [] : this.onScencePrinterList[index].deviceId

        this.$set(this, "onScencePrinterList", onScenceList)
      }
      this.updateBandingList()
    },
    // 获取设备类型
    async getDeviceTypeList() {
      console.log("getDeviceType");
      const [err, res] = await to(this.$apis.apiBackgroundDeviceDeviceDeviceTypePost({ source: 'self' }))
      if (err) {
        return this.$message.error('获取设备类型列表失败！')
      }
      if (res.code === 0) {
        this.deviceTypeList = res.data || []
        console.log("getDeviceType", this.deviceTypeList);
      } else {
        this.$message.error(res.msg || '获取设备类型列表失败！')
      }
    },
    // 根据设备类型获取设备列表
    async getDeviceDetailList(id, sn, consumeType, index, chooseIds) {
      this.isShowLoadingDevice = true
      var param = {
        page: 1,
        page_size: 9999,
        organization_id: this.organizationId
      }
      if (id) {
        param.device_types = id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDeviceDeviceListPost(param))
      this.isShowLoadingDevice = false
      if (err) {
        this.$set(this.onScencePrinterList[index], "deviceDetailList", [])
        return this.$message.error('获取设备列表失败！')
      }
      if (res.code === 0) {
        var data = res.data || {}
        var results = data.results || []
        results = this.updateDeviceListByChoose(results, sn, consumeType, chooseIds)
        if (index >= 0 && index < this.onScencePrinterList.length) {
          this.$set(this.onScencePrinterList[index], "deviceDetailList", deepClone(results))
        }
        console.log("getDeviceDetailList", this.onScencePrinterList[index].deviceDetailList);
      } else {
        this.$message.error(res.msg || '获取设备类型列表失败！')
      }
    },
    //  增加堂食订单自动打印
    clickPlusOnScencePrinterSetting(item) {
      if (!item.sn) {
        return this.$message.error('请选择打印机')
      }
      if (!item.consumeType) {
        return this.$message.error('请选择消费类型')
      }
      if (!item.deviceType || item.deviceType.length === 0) {
        return this.$message.error('请选择设备类型')
      }
      if (!item.deviceId || item.deviceId.length === 0) {
        return this.$message.error('请选择设备')
      }
      var itemNew = {
        sn: '', // 打印机
        consumeType: '', // 消费类型
        deviceType: [], // 设备类型
        deviceId: [] // 设备
      }
      this.onScencePrinterList.push(itemNew)
    },
    // 减少堂食订单自动打印
    clickReduceOnScencePrinterSetting(index) {
      if (this.onScencePrinterList.length <= 1) {
        return this.$message.error("亲，不能再删除了喔！")
      }
      let item = this.onScencePrinterList[index]
      let deviceId = item.deviceId
      let consumeType = item.consumeType
      var bangIndex = this.bandingDeviceList.findIndex(item => {
        return item.deviceId === deviceId
      })
      console.log("index", bangIndex)
      this.bandingDeviceList.splice(bangIndex, 1)
      this.onScencePrinterList.splice(index, 1)
      this.updateBandingList()
      console.log("this.bandingDeviceList", this.bandingDeviceList, consumeType);
      console.log("this.onScencePrinterList", this.onScencePrinterList);
    },
    // 更新选择设备列表
    updateDeviceListByChoose(list, sn, consumeType, chooseIds) {
      if (!list || !Array.isArray(list)) {
        return []
      }
      var newList = deepClone(list)
      newList.forEach(item => {
        var id = item.device_no
        var findItem = this.bandingDeviceList.find(bandItem => {
          return bandItem.sn === sn && bandItem.deviceId === id && bandItem.consumeType === consumeType
        })
        console.log("findItem", findItem);
        item.disabled = !!findItem
        if (chooseIds && chooseIds.toString().indexOf(id) !== -1) {
          // 如果是已经选择的，不能禁用喔
          item.disabled = false
        }
      })
      return newList
    },
    // 选择设备
    onfocusDeviceSelect(sn, consumeType, index, chooseIds) {
      console.log("onfocusDeviceSelect", sn, consumeType, index);
      var resultsList = deepClone(this.onScencePrinterList[index].deviceDetailList)
      resultsList = this.updateDeviceListByChoose(resultsList, sn, consumeType, chooseIds)
      if (resultsList && resultsList.length > 0) {
        this.$set(this.onScencePrinterList[index], "deviceDetailList", deepClone(resultsList))
      }
    },
    // 更新堂食订单自动打印
    updateOnScenceAutoPrinter(getPrinterSettingsData) {
      if (Reflect.has(getPrinterSettingsData, "instore_order_auto_print")) {
        this.settingForm.onSceneOrderAutoPrint = getPrinterSettingsData.instore_order_auto_print
        var printList = getPrinterSettingsData.instore_order_printer_list || []
        var newList = []
        if (Array.isArray(printList) && printList.length > 0) {
          printList.forEach(item => {
            var params = {
              sn: item.sn || '',
              consumeType: item.printer_type || '',
              deviceType: item.device_type || [],
              deviceId: item.device_ids || []
            }
            // 这里要判断，如果是该设备没有在设备列表的不添加进去,有些是用户删除了
            var sn = item.sn
            if (this.printDeviceList) {
              var findItem = this.printDeviceList.find(subItem => {
                return subItem.sn === sn
              })
              if (findItem) {
                newList.push(params)
              }
            }
          })
          // 如果发现 列表是空的，就添加一条默认的
          if (newList.length === 0) {
            newList.push({ sn: '', consumeType: '', deviceType: '', deviceId: '' })
          }
          this.$set(this, "onScencePrinterList", newList)
          // 亲。别忘了设备列表是后台获取的这里要重新获取一遍,上面不能直接调用，列表没有更新
          this.onScencePrinterList.forEach((onScenceItem, index) => {
            if (Array.isArray(onScenceItem.deviceType) && onScenceItem.deviceType.length > 0) {
              this.getDeviceDetailList(onScenceItem.deviceType, onScenceItem.sn, onScenceItem.consumeType, index, onScenceItem.deviceId)
            }
          })
        } else {
          this.$set(this, "onScencePrinterList", [{
            sn: '', // 打印机
            consumeType: '', // 消费类型
            deviceType: [], // 设备类型
            deviceId: [] // 设备
          }])
        }
      } else {
        this.settingForm.onSceneOrderAutoPrint = false
      }
    },
    //  设置预约点餐餐段默认时间
    async setMeanDataDefault() {
      console.log("setMeanDataDefault");
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationGetCommonSettingsPost({ id: this.organizationId }))
      if (res && !err) {
        var data = res.data || {}
        var mealTimeSettings = Reflect.has(data, 'meal_time_settings') ? data.meal_time_settings : {}
        if (mealTimeSettings && Object.keys(mealTimeSettings).length > 0) {
          var autoPrintMealAndTimeRange = deepClone(this.settingForm.autoPrintMealAndTimeRange)
          autoPrintMealAndTimeRange.breakfast = mealTimeSettings.breakfast_start || ''
          autoPrintMealAndTimeRange.lunch = mealTimeSettings.lunch_start || ''
          autoPrintMealAndTimeRange.afternoon = mealTimeSettings.afternoon_start || ''
          autoPrintMealAndTimeRange.dinner = mealTimeSettings.dinner_start || ''
          autoPrintMealAndTimeRange.supper = mealTimeSettings.supper_start || ''
          autoPrintMealAndTimeRange.morning = mealTimeSettings.morning_start || ''
          this.$set(this.settingForm, "autoPrintMealAndTimeRange", autoPrintMealAndTimeRange)
          console.log("this.settingForm", this.settingForm);
        }
      }
    }
  }
}
</script>

<style lang="scss">
.PrinterSettings {
  padding-top: 30px;
  .setting-wrap {
    margin: 20px;
    .checkbox-time-wrapp {
      // width: 800px;
    }
    .title {
      font-size: 16px;
      font-weight: bold;
      border-left: 5px #ff9b45 solid;
      padding: 0 10px;
      margin: 15px 0;
    }
  }
  .w-350{
    width: 350px !important;
  }
}
</style>
