<template>
  <div>
    <div class="table-header">
      <div class="header-left">
        <div class="header-left-title">食品卫生安全管理</div>
        <el-button type="text" size="small" class="ps-text m-r-10" @click="showDrawer('add')" v-permission="['background_fund_supervision.publicity_info.add_security_admin']">添加</el-button>
      </div>
      <div class="header-right">
        <el-button size="mini" @click="showPreviewDrawer">公示预览</el-button>
      </div>
    </div>
    <div class="table-content">
      <!-- table start -->
      <el-table
        v-loading="isLoading"
        :data="tableData"
        ref="tableData"
        style="width: 100%"
        stripe
        header-row-class-name="ps-table-header-row"
      >
        <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
          <template #phone="{ row }">
            <el-tooltip class="item" effect="dark" :content="row.phone" placement="top">
              <div>{{ computedPhone(row.phone) }}</div>
            </el-tooltip>
          </template>
          <template #level="{ row }">
            <div v-for="(item, index) in row.level" :key="index">{{ item }}</div>
          </template>
          <template #img="{ row }">
             <el-image
              :src="row.face_url"
              :preview-src-list="[row.face_url]"
              style="width: 90px; height: 120px"
              fit="cover"
            ></el-image>
          </template>
          <template #operation="{ row }">
            <el-button type="text" size="small" class="ps-text" @click="showDrawer('edit', row)" v-permission="['background_fund_supervision.publicity_info.modify_security_admin']">编辑</el-button>
            <el-button type="text" size="small" class="ps-warn-text" @click="deleteHandle(row)" v-permission="['background_fund_supervision.publicity_info.delete_security_admin']">删除</el-button>
          </template>
        </table-column>
      </el-table>
      <!-- table end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>

    <!-- 弹窗 -->
    <div class="ps-el-drawer">
      <el-drawer
        :title="selectType === 'add' ? '新建信息' : '编辑信息'"
        :visible="drawerShow"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="drawerFormRef" :model="drawerForm" label-width="100px" label-position="right">
            <el-form-item :label="'姓名'" prop="name" :rules="[{ required: true, message: '请输入姓名', trigger: ['change', 'blur'] }]">
              <el-input v-model="drawerForm.name" class="w-300" placeholder="请输入姓名，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item
              :label="'联系电话'"
              prop="phone"
              :rules="[
                { required: true, message: '请输入联系电话', trigger: ['change', 'blur'] },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: ['change', 'blur'] }
              ]">
              <el-input v-model="drawerForm.phone" class="w-300" placeholder="请输入联系电话，不超过11位" maxlength="11"></el-input>
            </el-form-item>
            <el-form-item :label="'所属岗位'" prop="post" :rules="[{ required: true, message: '请输入岗位信息', trigger: ['change', 'blur'] }]">
              <el-input v-model="drawerForm.post" class="w-300" placeholder="请输入岗位信息，不超过20个字" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item :label="'等级管理'" prop="level">
              <el-checkbox-group v-model="drawerForm.level">
                <el-checkbox label="食品安全负责人" :disabled="canBeSelected"></el-checkbox>
                <el-checkbox label="食品安全总监"></el-checkbox>
                <el-checkbox label="食品安全员"></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item :label="'个人照片'" prop="img" :rules="[{ required: true, message: '请上传有效照片', trigger: ['change', 'blur'] }]">
              <div class="certification-info-show-tips">
                图片最大不超过2MB，仅支持jpg,png格式
              </div>
              <el-upload v-loading="uploading" element-loading-text="上传中" class="upload-w" ref="fileUpload"
                :action="serverUrl" :file-list="fileLists" :on-success="uploadSuccess"
                :before-upload="beforeFoodImgUpload" :limit="1" :multiple="false" :show-file-list="false"
                :headers="headersOpts" accept=".jpeg,.jpg,.png,.bmp">
                <!-- <img v-if="drawerForm.img" :src="drawerForm.img" style="width: 100px; height: 100px;"> -->
                <el-image
                  v-if="drawerForm.img"
                  :src="drawerForm.img"
                  style="width: 90px; height: 120px"
                  fit="cover"
                ></el-image>
                <div v-else style="width: 100px; height: 100px; border: 1px dashed #C0C4CC; text-align: center; line-height: 100px;">
                  <i class="el-icon-plus"></i>
                </div>
              </el-upload>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('addOrEdit')">取消</el-button>
            <el-button size="small" type="primary" class="w-100" @click="saveHandle">保存</el-button>
          </div>
        </div>
      </el-drawer>

      <el-drawer
        :title="'公示预览'"
        :visible="previewDrawerShow"
        :show-close="false"
        size="35%">
        <div class="p-20">
          <div class="preview-content">
            <div v-for="(item, index) in previewDrawerList" :key="index" class="preview-content-box">
              <div class="preview-content-box-label">{{ item.label }}</div>
              <div class="preview-content-box-img">
                <div v-for="(itemIn, indexIn) in item.personList" :key="indexIn" class="flex-col flex-align-c">
                  <div class="img-area">
                    <!-- <img :src="itemIn.face_url" alt=""> -->
                    <el-image
                      :src="itemIn.face_url"
                      :preview-src-list="[itemIn.face_url]"
                      style="width: 90px; height: 120px"
                      fit="cover"
                    ></el-image>
                    <div class="img-area-title ellipsis" style="color: #fff; font-size: 14px;">
                      {{ itemIn.job_title }}
                    </div>
                  </div>
                  <div class="ellipsis" style="width: 6em; text-align: center;">{{ itemIn.name }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="ps-el-drawer-footer">
            <el-button size="small" class="w-100" @click="cancelHandle('preview')">关闭</el-button>
          </div>
        </div>
      </el-drawer>
    </div>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImagePreview"
      :url-list="previewList"
      hide-on-click-modal
      teleported
      :on-close="closePreview"
      style="z-index: 3000"
    />
  </div>
</template>

<script>
import { getToken, getSuffix, deepClone } from '@/utils/index'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  components: {
    ElImageViewer
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { label: '个人照片', key: 'face_url', type: "slot", slotName: "img" },
        { label: '姓名', key: 'name' },
        { label: '联系电话', key: 'phone', type: "slot", slotName: "phone" },
        { label: '所属岗位', key: 'job_title' },
        { label: '等级管理', key: 'level', type: 'slot', slotName: "level" },
        { label: '修改时间', key: 'update_time' },
        { label: '操作人', key: 'operator' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", width: "200" }
      ],
      page: 1,
      pageSize: 10,
      totalCount: 0,
      selectType: '',
      drawerShow: false,
      drawerForm: {
        name: '',
        phone: '',
        post: '',
        level: [],
        img: ''
      },
      uploading: false, // 上传加载中
      serverUrl: '/api/background/file/upload',
      uploadParams: { // 上传头
        prefix: 'incumbents',
        key: 'incumbents' + new Date().getTime() + Math.floor(Math.random() * 150)
      },
      fileLists: [],
      headersOpts: { // 上传插入表头
        TOKEN: getToken()
      },
      showImagePreview: false,
      previewList: [],
      previewDrawerShow: false,
      previewDrawerList: [
        {
          label: "食品安全负责人",
          personList: []
        },
        {
          label: "食品安全总监",
          personList: []
        },
        {
          label: "食品安全员",
          personList: []
        },
        {
          label: "无分级",
          personList: []
        }
      ],
      inCharge: {}
    }
  },
  computed: {
    computedPhone() {
      return d => {
        return d.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
      }
    },
    canBeSelected() {
      if (!Object.keys(this.inCharge || {}).length || this.drawerForm.name === this.inCharge.name) {
        return false
      } else {
        return true
      }
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.getDataList()
    },
    // 图片上传成功
    uploadSuccess(res, file, fileList) {
      this.uploading = false
      if (res && res.code === 0) {
        this.fileLists = []
        this.drawerForm.img = res.data.public_url
      } else {
        this.drawerForm.img = ''
        this.$message.error(res.msg)
      }
    },
    // 图片上传前检测
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 <= 2
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.uploading = true
    },
    // 获取数据
    getDataList() {
      this.inCharge = {}
      this.isLoading = true
      let params = {
        page: this.page,
        page_size: this.pageSize
      }
      this.$apis.apiBackgroundFundSupervisionPublicityInfoSecurityAdminListPost(params).then(res => {
        this.isLoading = false
        if (res.code === 0) {
          this.tableData = deepClone(res.data.results)
          this.totalCount = res.data.count
          // 将等级为食品安全负责人的人员单拎出来
          this.tableData.forEach(item => {
            if (item.level && item.level.includes('食品安全负责人')) {
              this.inCharge = deepClone(item)
            }
          })
          console.log('找到了', this.inCharge)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    showDrawer(type, data) {
      this.selectType = type
      console.log('data', data)
      if (type === 'edit') {
        this.selectId = data.id
        this.drawerForm.phone = data.phone
        this.drawerForm.img = data.face_url
        this.drawerForm.name = data.name
        this.drawerForm.post = data.job_title
        this.drawerForm.level = data.level || []
      } else {
        this.selectId = ''
        this.drawerForm.phone = ''
        this.drawerForm.img = ''
        this.drawerForm.name = ''
        this.drawerForm.post = ''
        this.drawerForm.level = []
      }
      this.drawerShow = true
      setTimeout(() => {
        this.$refs.drawerFormRef.clearValidate()
      }, 10)
    },
    handleClick(row) {
      this.previewList = [row.face_url]
      document.body.style.overflow = 'hidden'
      this.showImagePreview = true
    },
    closePreview() {
      this.previewList = []
      this.showImagePreview = false
      document.body.style.overflow = 'auto'
    },
    cancelHandle(type) {
      if (type === 'addOrEdit') {
        this.$refs.drawerFormRef.resetFields()
        this.drawerShow = false
      } else {
        this.previewDrawerList.forEach(item => {
          item.personList = []
        })
        this.previewDrawerShow = false
      }
    },
    saveHandle() {
      this.$refs.drawerFormRef.validate((valid) => {
        if (valid) {
          let params = {
            id: this.selectType === 'add' ? undefined : this.selectId,
            level: this.drawerForm.level,
            phone: this.drawerForm.phone,
            face_url: this.drawerForm.img,
            name: this.drawerForm.name,
            job_title: this.drawerForm.post
          }
          if (this.selectType === 'add') {
            this.addFoodHygieneAndSafetyManagement(params)
          } else {
            this.editFoodHygieneAndSafetyManagement(params)
          }
        } else {
          this.$message.error('请确认表单内容填写是否正确')
        }
      })
    },
    addFoodHygieneAndSafetyManagement(param) {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoAddSecurityAdminPost(param).then(res => {
        if (res.code === 0) {
          this.$message.success('新增成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.drawerFormRef.resetFields()
        this.drawerShow = false
        this.getDataList()
      })
    },
    editFoodHygieneAndSafetyManagement(param) {
      this.$apis.apiBackgroundFundSupervisionPublicityInfoModifySecurityAdminPost(param).then(res => {
        if (res.code === 0) {
          this.$message.success('编辑成功')
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.drawerFormRef.resetFields()
        this.drawerShow = false
        this.getDataList()
      })
    },
    deleteHandle(data) {
      this.$confirm(`确定要删除 ${data.name} 的人员信息？删除后不可恢复，请谨慎操作。`, '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$apis.apiBackgroundFundSupervisionPublicityInfoDeleteSecurityAdminPost({
          id: data.id
        }).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
          } else {
            this.$message.error(res.msg)
          }
          this.getDataList()
        })
      }).catch(action => {
        this.$message('已取消删除')
      })
    },
    showPreviewDrawer() {
      // 将当前的人员根据等级塞进不同数组里
      this.previewDrawerList.forEach(item => {
        this.tableData.forEach(itemIn => {
          console.log('看看itemIn', itemIn)
          if (itemIn.level && itemIn.level.length && itemIn.level.includes(item.label)) {
            item.personList.push(itemIn)
          }
        })
        if (item.label === '无分级') {
          item.personList = this.tableData.filter(itemIn => !itemIn.level || !itemIn.level.length)
        }
      })
      console.log('看看previewDrawerList', this.previewDrawerList)
      this.previewDrawerShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-left {
    padding-left: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    &-title {
      height: 32px;
      line-height: 32px;
      margin-right: 10px;
      font-weight: 700;
    }
  }
  .header-right {
    padding-right: 20px;
  }
}
.preview-content {
  &-box {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-bottom: 20px;
    &-label {
      width: 7em;
      text-align: right;
      margin-right: 20px;
    }
    &-img {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      .img-area {
        position: relative;
        margin-bottom: 10px;
        &-title {
          position: absolute;
          bottom: 0;
          background-color: rgb(255, 155, 69, 0.8);
          width: 100%;
          padding: 5px 10px;
          text-align: center;
        }
      }
    }
  }
}
</style>
