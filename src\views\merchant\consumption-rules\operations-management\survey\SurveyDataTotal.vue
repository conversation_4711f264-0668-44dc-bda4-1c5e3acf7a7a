<template>
  <div class="SurveyDataTotal container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper p-t-20 p-l-20">
      <div v-for="(item, index) in dataTotalList" :key="item.survey_question_id" class="m-b-25">
        <div class="question-name m-b-10">{{index+1}}、{{item.content}}</div>
        <div v-if="item.type === 'CHOICE'">
          <el-table
            v-loading="isLoading"
            :data="item.choiceStatistics"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
          >
            <el-table-column prop="content" label="选项" align="center"></el-table-column>
            <el-table-column prop="count" label="小计" align="center"></el-table-column>
            <el-table-column prop="percent" label="比例" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.percent">
                  <el-progress :percentage="scope.row.percent"></el-progress>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else>
          <el-button class="ps-btn" type="primary" @click="openDialog(item)">详细作答情况</el-button>
        </div>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      top="30vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false">
        <el-table
          v-if="dialogType === 'ANSWER'"
          v-loading="isLoading"
          :data="dialogTable"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          :key="tableKey"
        >
          <el-table-column type="index" :index="indexMethod" label="序号" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="create_time" label="提交答卷时间" align="center"></el-table-column>
          <el-table-column prop="content" label="答案" align="center"></el-table-column>
        </el-table>
        <el-table
          v-if="dialogType === 'SCORE'"
          v-loading="isLoading"
          :data="dialogTable"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          :key="tableKey"
        >
          <el-table-column prop="score" label="评分" align="center"></el-table-column>
          <el-table-column prop="count" label="人数" align="center"></el-table-column>
          <el-table-column prop="percent" label="占比" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.percent">
                  <el-progress :percentage="scope.row.percent"></el-progress>
                </div>
              </template>
            </el-table-column>
        </el-table>
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-btn" type="primary" @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
export default {
  name: 'AddSurvey',
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      surveyInfoId: '',
      surveyTotalCount: 0,
      dataTotalList: [],
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      dialogAllTable: [],
      dialogTable: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      tableKey: 0
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.surveyInfoId = this.$route.query.id
      this.surveyTotalCount = this.$route.query.num
      this.getSurveyDataTotal()
    },
    // 刷新页面
    refreshHandle() {
      this.initLoad()
    },
    async getSurveyDataTotal() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundMarketingSurveyInfoFeedbackCollectPost({
        id: this.surveyInfoId
      })
      this.isLoading = false
      if (res.code === 0) {
        this.dataTotalList = res.data.results.map(item => {
          if (item.type === 'CHOICE') {
            item.choiceStatistics = []
            for (let key in item.choice_statistics) {
              let content
              item.options.map(opt => {
                if (opt.options === key) {
                  content = opt.content
                }
              })
              item.choiceStatistics.push({
                ...item.choice_statistics[key],
                content
              })
            }
            item.choiceStatistics.push({
              content: "本次有效填写人数",
              count: this.surveyTotalCount,
              percent: null
            })
          } else if (item.type === 'SCORE') {
            item.scoreStatistics = []
            for (let key in item.score_statistics) {
              item.scoreStatistics.push({
                ...item.score_statistics[key]
              })
            }
          }
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    openDialog(data) {
      this.dialogVisible = true
      this.dialogTitle = data.content
      this.dialogType = data.type
      if (data.type === 'ANSWER') {
        this.dialogAllTable = data.answer
      } else if (data.type === 'SCORE') {
        this.dialogAllTable = data.scoreStatistics
      }
      this.currentPage = 1
      this.setSelCurrentPageData()
      this.tableKey = Math.random()
    },
    setSelCurrentPageData() {
      let start = (this.currentPage - 1) * this.pageSize
      let end =
        (this.currentPage - 1) * this.pageSize +
        this.pageSize
      this.totalCount = this.dialogAllTable.length
        ? this.dialogAllTable.length
        : 0
      this.dialogTable = [].concat(
        this.dialogAllTable.slice(start, end)
      )
      console.log(this.dialogTable, this.dialogAllTable)
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.setSelCurrentPageData()
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    }
  }
}
</script>

<style lang="scss">
.SurveyDataTotal {
}
</style>
