<template>
  <div class="GoodsCategory container-wrapper">
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper" v-loading="isLoading">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_store.goods.goods_stock_details_add']">导出EXCEL</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :empty-text="isFirstSearch ? '暂无数据，请查询' : ''"
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSetting" :key="item.key" :col="item">
            <template #index="{ row }">
              <div v-if="row.index === 'slot'">{{ row.sumsName }}</div>
              <div v-else>{{ row.create_time }}</div>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>
<script>
import { debounce, to } from '@/utils'
import { ADD_STOCK_DETAILS } from '../../components/constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'AddStockDetails',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '入库时间', key: 'create_time', type: 'slot', slotName: 'index' },
        { label: '商品名称', key: 'goods_name' },
        { label: '商品条码', key: 'barcode' },
        { label: '规格', key: 'spec' },
        { label: '单位', key: 'unit_name' },
        { label: '分类', key: 'category_name' },
        { label: '入库数量', key: 'operate_stock' },
        { label: '入库前库存', key: 'before_stock' },
        { label: '入库后库存', key: 'after_stock' },
        { label: '入库类型', key: 'inbound_type_alias' },
        { label: '关联订单号', key: 'trade_no' },
        { label: '操作员', key: 'operator_name' }
      ],
      searchFormSetting: ADD_STOCK_DETAILS,
      isFirstSearch: false
    }
  },
  created() {
    this.getGoodsCategoryList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getGoodsStockDetails()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.isFirstSearch = false
        this.getGoodsStockDetails()
      }
    }, 300),
    async getGoodsCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsCategoryListPost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.goods_category_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getGoodsStockDetails() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundStoreGoodsGoodsStockDetailsPost({
          type: 'add',
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let summaryData = res.data.summary_data

        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 如果有值直接push 两个合计进去
        if (this.tableData.length) {
          this.tableData.push(
            {
              index: 'slot',
              sumsName: '当页合计',
              operate_stock: summaryData.operate_stock,
              before_stock: summaryData.before_stock,
              after_stock: summaryData.after_stock
            },
            {
              index: 'slot',
              sumsName: '全部合计',
              operate_stock: summaryData.total_operate_stock,
              before_stock: summaryData.total_before_stock,
              after_stock: summaryData.total_after_stock
            }
          )
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getGoodsStockDetails()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getGoodsStockDetails()
    },
    gotoExport() {
      const option = {
        type: 'ExportAddOrDeductStockDetails',
        params: {
          type: 'add',
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss" scoped></style>
