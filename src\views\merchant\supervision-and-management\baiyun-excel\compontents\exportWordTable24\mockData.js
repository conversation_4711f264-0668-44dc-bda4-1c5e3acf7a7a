const resData = {
  recordNumber: 'FY202307-001',
  schoolName: '广州市白云区实验学校',
  meetingDate: '2023-07-15',
  meetingPlace: '学校行政楼301会议室',
  host: '张校长',
  attendees: ['张校长', '李总监', '王主任', '赵监督员'],
  internetIssues: [
    {
      description: '厨房摄像头遮挡',
      solution: '立即清理摄像头，加强日常检查'
    },
    {
      description: '食品留样不规范',
      solution: '重新培训留样流程，增加检查频次'
    }
  ],
  materialStats: {
    totalBatches: 120,
    qualifiedBatches: 118,
    unqualifiedBatches: 2,
    qualifiedRate: 98.3,
    unqualifiedMaterials: ['蔬菜批次A-0721', '肉类批次C-0715'],
    unqualifiedReasons: ['蔬菜部分腐烂', '肉类检验报告不全'],
    handlingMeasures: '退货处理，更换供应商'
  },
  complaintStats: {
    total: 5,
    valid: 3,
    invalid: 2,
    products: ['红烧肉', '青菜'],
    reasons: ['菜品中有异物', '菜品未熟透'],
    improvementMeasures: ['加强食材清洗', '调整烹饪时间']
  },
  supplierManagement: '对3家供应商进行了突击检查，暂停1家不合格供应商合作',
  trainingStats: {
    times: 2,
    hours: 4,
    participants: 35
  },
  dailyCheckStats: {
    totalIssues: 15,
    resolvedIssues: 12,
    pendingIssues: 3,
    planCompletionDate: '2023-07-25',
    cooperationStatus: '各部门积极配合，整改进度良好'
  },
  riskIssues: [
    {
      description: '冷藏设备温度异常',
      level: '高风险',
      solution: '立即维修设备，启用备用冰箱'
    },
    {
      description: '食品添加剂管理不规范',
      level: '中风险',
      solution: '建立专用存放柜，实行双人管理'
    }
  ],
  departmentSpeeches: [
    {
      department: '后勤部',
      content: '建议增加厨房消毒频次'
    },
    {
      department: '学生处',
      content: '学生反馈希望增加菜品种类'
    }
  ],
  // 粘贴校长/法人每月不少于一次察看明厨亮灶的工作照片
  fileList: [
    'https://cashier-v4-dev.packertec.com/cashier_v4/face/jpg/20250721/1753069156063175b06d5f9b1aee.jpg?_tk=66f94794fe1cce3b26e1193ec31bd60b',
    'https://cashier-v4-dev.packertec.com/cashier_v4/face/jpg/20250721/1753069156063175b06fb42b6e9d.jpg?_tk=9f858a63f9c03c4141939e59ffdf3ba1'
  ],
  principal: '张校长',
  principalInstructions:
    '食品安全是学校工作的重中之重，各部门必须严格落实整改措施，确保师生饮食安全。对发现的问题要追根溯源，防止类似问题再次发生。',
  nextMonthPlans: ['开展食品安全月主题活动', '组织供应商评估会议', '升级明厨亮灶监控系统'],
  recorder: '王秘书',
  responsiblePerson: '张校长',
  department: '食堂厨房',
  departmentHead: '李主任',
  filler: '王秘书'
}

export default resData
