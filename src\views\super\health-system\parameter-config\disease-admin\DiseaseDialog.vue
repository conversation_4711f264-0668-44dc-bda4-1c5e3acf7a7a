<template>
  <!-- dialog start -->
  <dialog-message :show.sync="visible" :loading="dialogLoading" :title="dialogTitle" width="470px" @close="closeDialog">
    <el-form ref="dialogFormRef" :model="dialogForm" :rules="dialogrules" :label-width="formLabelWidth" size="medium">
      <div v-if="type === 'addNutrition' || type === 'modifyNutrition'">
        <el-form-item label="选择元素" prop="nutrition_key">
          <el-select
            v-model="dialogForm.nutrition_key"
            placeholder="请选择"
            filterable
            class="ps-select w-auto"
            popper-class="ps-popper-select"
            :disabled="type === 'modify'"
          >
            <el-option
              v-for="(item, index) in nutritionList"
              :key="index"
              :label="`${item.name}(${item.unit})`"
              :value="item.key"
              :disabled="item.disabled"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推荐范围" required :show-message="false">
          <div class="ps-flex ps-align-c">
            <el-form-item label="" label-width="0" prop="range_one">
              <el-input v-model="dialogForm.range_one" class="w-110"></el-input>
            </el-form-item>
            <span class="m-l-20 m-r-20">~</span>
            <el-form-item label="" label-width="0" prop="range_two">
              <el-input v-model="dialogForm.range_two" class="w-110"></el-input>
            </el-form-item>
          </div>
        </el-form-item>
      </div>
      <div v-else>
        <div>添加标签</div>
        <div class="m-t-10 m-b-10">选择自动化标签为少食：已选{{ selectLable.length }}</div>
        <el-form-item label="" label-width="0">
          <div class="lable-box">
            <checkbox-buttons v-model="selectLable" :data-list="labelList" :options="labelOptions" :disabled-list="disabledData" size="small" />
          </div>
        </el-form-item>
      </div>
    </el-form>
    <div slot="tool" class="text-right m-t-40">
      <el-button :disabled="dialogLoading" class="ps-cancel-btn" @click="cancleDialog">取消</el-button>
      <el-button :disabled="dialogLoading" class="ps-btn" type="primary" @click="confirmDialog">确定</el-button>
    </div>
  </dialog-message>
  <!-- end -->
</template>

<script>
// import { NUTRITION_KEY } from '@/constants'
import { deepClone } from '@/utils'
import CheckboxButtons from './CheckboxButtons'
import { positiveMoney } from '@/utils/validata'

export default {
  name: 'MotionDialog',
  components: { CheckboxButtons },
  // 重新定义下v-model的prop和触发的event
  // 为什么要重新定义的为了区别其它嵌套组件使用的v-model
  model: {
    prop: 'showDialog',
    event: 'changeShow'
  },
  props: {
    // 绑定的数据
    // visible: {
    //   required: true
    // },
    showDialog: {
      required: true
    },
    dialogTitle: {
      type: String,
      default: '添加'
    },
    // 类型
    type: {
      type: String,
      default: 'date'
    },
    // 弹窗数据源
    infoData: {
      type: [Object, Array]
    },
    nutritionList: {
      type: Array
    },
    // label宽度
    formLabelWidth: {
      type: [String, Number],
      default: '90px'
    },
    // form size
    formSize: {
      type: String,
      default: 'medium'
    },
    // 关闭的回调
    closehandle: Function,
    // 确定回调
    confirmhandle: Function
  },
  data() {
    let validatorRangeOne = (rule, value, callback) => {
      if (value) {
        if (positiveMoney(value)) {
          if (Number(value) > 9999.99) {
            return callback(new Error('最大值不能超过9999.99'))
          }
          callback()
        } else {
          callback(new Error('格式错误'))
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    let validatorRangeTwo = (rule, value, callback) => {
      if (value) {
        if (positiveMoney(value)) {
          if (Number(value) > 9999.99) {
            return callback(new Error('最大值不能超过9999.99'))
          }
          callback()
        } else {
          callback(new Error('格式错误'))
        }
      } else {
        callback(new Error('请输入'))
      }
    }
    return {
      dialogForm: {
        nutrition_key: '',
        range_one: '',
        range_two: ''
      }, // validator: validateNumber,
      dialogrules: {
        nutrition_key: [{ required: true, message: '请选择元素', trigger: 'change' }],
        range_one: [
          { required: true, message: '请输入', trigger: 'blur' },
          { validator: validatorRangeOne, trigger: 'change' }
        ],
        range_two: [
          { required: true, message: '请输入', trigger: 'blur' },
          { validator: validatorRangeTwo, trigger: 'change' }
        ]
      },
      // 运动类型
      sportTypeList: [
        { label: '运动', value: 'exercise' },
        { label: '休闲', value: 'leisure' },
        { label: '乐器演奏', value: 'play' },
        { label: '体力劳动', value: 'manual' },
        { label: '劳务活动', value: 'labor' }
      ],
      dialogLoading: false,
      // nutritionList: deepClone(NUTRITION_KEY),
      selectLable: [],
      labelList: [],
      labelOptions: {
        value: 'id',
        label: 'label_name'
      },
      disabledData: []
    }
  },
  computed: {
    visible: {
      get() {
        if (this.showDialog) {
          this.resetForm()
          this.init()
        }
        // console.log(1111, this.showDialog)
        return this.showDialog
      },
      set(val) {
        this.$emit('changeShow', val)
      }
    }
  },
  watch: {
    // visible(val) {
    //   console.log(33, val)
    //   if (val) {
    //     console.log(2222, this.showDialog)
    //   }
    // }
  },
  created() {

  },
  mounted() {
  },
  methods: {
    init() {
      if (this.type !== 'modifyNutrition' && this.type !== 'addNutrition') {
        this.getAutoLabelList()
        this.disabledData = []
        this.setDisableData(this.type)
        if (this.infoData[this.type + '_label']) {
          this.selectLable = this.infoData[this.type + '_label'].map(v => {
            return v.id
          })
        }
      }
      if (this.type === 'modifyNutrition') {
        this.dialogForm = {
          nutrition_key: this.infoData.key,
          range_one: this.infoData.range_one,
          range_two: this.infoData.range_two
        }
      }
    },
    // 设置标签禁选
    setDisableData(type) {
      let listKey = ['suitable', 'not_recommend', 'recommend']
      listKey.splice(listKey.indexOf(type), 1)
      listKey.forEach(key => {
        if (this.infoData[key + '_label']) {
          this.infoData[key + '_label'].forEach(v => {
            this.disabledData.push(v.id)
          })
        }
      })
    },
    closeDialog() {
      this.resetForm()
      this.closehandle && this.closehandle()
    },
    cancleDialog() {
      this.closehandle && this.closehandle()
      this.visible = false
    },
    confirmDialog() {
      this.$refs.dialogFormRef.validate((valid) => {
        if (valid) {
          console.log(23232, this.infoData)
          if (this.dialogLoading) return this.$message.error('请不要重复点击！')
          this.dialogLoading = true
          if (this.type === 'addNutrition') {
            if (Number(this.dialogForm.range_one) >= Number(this.dialogForm.range_two)) {
              this.dialogLoading = false
              return this.$message.error('范围开始必须小于结束!')
            }
            let data = {
              id: this.infoData.disease_id,
              nutrition_key: this.dialogForm.nutrition_key,
              range_one: +this.dialogForm.range_one,
              range_two: +this.dialogForm.range_two
            }
            this.addNutritionHandle(data)
          } else if (this.type === 'modifyNutrition') {
            if (Number(this.dialogForm.range_one) >= Number(this.dialogForm.range_two)) {
              this.dialogLoading = false
              return this.$message.error('范围开始必须小于结束!')
            }
            let data = {
              id: this.infoData.disease_id,
              nutrition_key: this.dialogForm.nutrition_key,
              range_one: +this.dialogForm.range_one,
              range_two: +this.dialogForm.range_two
            }
            this.modifyNutritionHandle(data)
          } else {
            let data = {
              id: this.infoData.disease_id,
              label_ids: this.selectLable,
              label_type: this.type
            }
            this.addLableHandle(data)
          }
        }
      })
    },
    // 添加标签
    async addLableHandle(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundHealthyAdminDiseaseAddLabelPost(params))
      this.dialogLoading = false
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.visible = false
        this.confirmhandle && this.confirmhandle()
        this.$emit('confirmdialog')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 添加
    async addNutritionHandle(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundHealthyAdminDiseaseAddNutritionControlPost(params))
      this.dialogLoading = false
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.visible = false
        this.confirmhandle && this.confirmhandle()
        this.$emit('confirmdialog')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑
    async modifyNutritionHandle(params) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundHealthyAdminDiseaseModifyNutritionControlPost(params))
      this.dialogLoading = false
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.visible = false
        this.confirmhandle && this.confirmhandle()
        this.$emit('confirmdialog')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取标签
    async getAutoLabelList() {
      this.isLoading = true
      const params = {
        page: 1,
        page_size: 999999
      }
      let [err, res] = await this.$to(this.$apis.apiBackgroundHealthyAdminAutoLabelListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.labelList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重置表单
    resetForm() {
      this.dialogForm = {
        nutrition_key: '',
        range_one: '',
        range_two: ''
      }
      this.selectLable = []
      this.labelList = []
      const dialogFormRef = this.$refs.dialogFormRef
      // 重置表单数据
      if (dialogFormRef) {
        // 移除校验
        dialogFormRef.clearValidate()
      }
    }
  }
};
</script>

<style scoped lang="scss">
  .w-auto{
    width: 80%;
  }
  .w-140{
    width: 140px;
  }
  .min-w-100{
    min-width: 100px;
  }
  .flex{
    display: flex;
  }
  .text-right{
    text-align: right;
  }
  .lable-box{
    padding: 16px;
    border: 1px solid #d7d7d7;
    border-radius: 6px;
  }
</style>
