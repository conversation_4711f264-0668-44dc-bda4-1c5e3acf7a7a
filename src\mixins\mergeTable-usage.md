# mergeTable.js 使用指南

## 概述
`mergeTable.js` 是一个优化后的表格合并混入，支持多种合并场景，包括：
- 普通行合并
- 列合并
- 小计行合并
- 汇总行合并
- 自定义特殊行合并

## 基本使用

### 1. 导入混入
```javascript
import mergeTableMixin from '@/mixins/mergeTable'

export default {
  mixins: [mergeTableMixin],
  // ...
}
```

### 2. 模板中使用
```html
<custom-table
  :table-data="tableData"
  :span-method="spanMethod"
  :cell-style="cellStyle"
/>
```

## 配置选项

### mergeOpts 配置结构
```javascript
this.mergeOpts = {
  // 基础配置
  useKeyList: {}, // 根据固定key进行合并的配置
  mergeKeyList: [], // 通用的合并字段配置
  colMergeList: [], // 列合并配置
  rowMergeList: [], // 行合并字段列表
  
  // 特殊行配置
  subtotalConfig: {
    mergeColumns: 3, // 小计行合并前几列
    displayColumns: ['count', 'real_fee'] // 需要显示数据的列
  },
  summaryConfig: {
    mergeColumns: 8 // 汇总行合并所有列
  },
  specialRowHandlers: [] // 自定义特殊行处理器
}
```

## 使用场景

### 1. 分组数据合并
```javascript
// 数据结构
const tableData = [
  { group: '分组1', name: '张三', amount: 100 },
  { group: '分组1', name: '李四', amount: 200 },
  { group: '分组2', name: '王五', amount: 150 }
]

// 配置
this.mergeOpts = {
  useKeyList: {
    group: ['group'] // 根据group字段合并
  }
}
```

### 2. 带小计行的分组数据
```javascript
// 数据结构（包含小计行）
const tableData = [
  { group: '分组1', name: '张三', amount: 100 },
  { group: '分组1', name: '李四', amount: 200 },
  { group: '小计', name: '', amount: 300, subtotalClassName: true }, // 小计行
  { group: '分组2', name: '王五', amount: 150 }
]

// 配置
this.mergeOpts = {
  useKeyList: {
    group: ['group']
  },
  subtotalConfig: {
    mergeColumns: 2, // 合并前2列
    displayColumns: ['amount'] // 显示金额列
  }
}
```

### 3. 列合并（分组标题行）
```javascript
// 配置
this.mergeOpts = {
  colMergeList: [
    {
      labels: ['姓名', '年龄', '部门', '职位'], // 表头标签
      subtotals: ['总计', '分组1', '分组2'], // 需要合并的行标识
      attr: 'group' // 判断字段
    }
  ]
}
```

## 特殊行类型

### 1. 小计行
- 标识：`row.subtotalClassName = true`
- 自动合并前N列，显示指定的数据列

### 2. 汇总行
- 标识：`row.summaryClassName = true`
- 默认合并所有列

### 3. 自定义特殊行
```javascript
this.mergeOpts = {
  specialRowHandlers: [
    {
      condition: (row) => row.customType === 'special',
      merge: (row, column, rowIndex, columnIndex) => {
        // 自定义合并逻辑
        return columnIndex === 0 ? [1, 4] : [0, 0]
      }
    }
  ]
}
```

## 完整示例

参考 `DiseaseProducedDetails.vue` 的实现：

```javascript
// 设置合并配置
this.mergeOpts = {
  colMergeList: [
    {
      labels: ['床号', '姓名', '菜品', '数量', '单价', '合计', '手机号', '人员编号'],
      subtotals: [...Array.from(addedNames)], // 分组标题行
      attr: 'bed_no'
    }
  ],
  useKeyList: {
    payer_department_group_id: ['bed_no', 'name', 'phone', 'person_no']
  },
  subtotalConfig: {
    mergeColumns: 3, // 合并前3列
    displayColumns: ['count', 'real_fee'] // 显示数量和金额
  }
}
```

## 注意事项

1. 确保数据中包含正确的标识字段（如 `subtotalClassName`）
2. 合并配置要与表格列结构匹配
3. 特殊行的数据结构要符合合并逻辑
4. 使用 `initMergeData(tableData)` 初始化合并数据
