<template>
  <!-- 超管 -->
  <el-drawer
    title="查看公告"
    :visible.sync="drawerVisible"
    direction="rtl"
    custom-class="setting-drawer"
    size="800px"
    :show-close="false"
    @open="openedDialogHandle">
    <div class="system-notice-detail drawer-content-wrapper" v-loading="isLoading">
      <h1 class="title">
        <el-tag v-if="modifyData && modifyData.important_msg" effect="plain" size="small" style="background-color: #fff;">重 要</el-tag>
        <span class="m-l-10">{{ noticeForm.title }}</span>
      </h1>
      <div class="content margin-b-10" v-html="noticeForm.content"></div>
      <div class="margin-b-10 flex" v-if="noticeForm.fileLists.length">
        <div class="m-r-10 p-top-5">附件</div>
        <div>
          <div v-for="item in noticeForm.fileLists" :key="item.uid" style="height: 30px;">
            <span>{{ truncateFilename(item.name) }}</span>
            <span class="color-gray m-l-10">{{ formatFileSize(item.file_size) }}</span>
            <el-button type="text" @click="downloadHandle(item)" class="m-l-10 font-size-16">下载</el-button>
          </div>
        </div>
      </div>
    </div>
    <slot name="footer">
      <div class="ps-drawer-footer text-left">
        <el-button type="primary" size="small" @click="drawerVisible = false">关 闭</el-button>
        <el-button v-if="noticeForm.status === 1" type="primary" size="small" @click="editHandle">编 辑</el-button>
      </div>
    </slot>
  </el-drawer>
</template>

<script>
import { to, unescapeHTML } from '@/utils'
import FileSaver from 'file-saver'

export default {
  name: '',
  components: {},
  props: {
    show: {
      type: Boolean,
      required: true,
      default: false
    },
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isLoading: false,
      noticeForm: {
        msgNo: '',
        title: '',
        companyIds: [],
        content: '',
        isAll: false,
        fileLists: [],
        status: 2,
        postTime: ''
      },
      modifyData: {},
      fileName: ''
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 查看弹窗打开
    openedDialogHandle() {
      this.noticeForm = {
        msgNo: '',
        title: '',
        companyIds: [],
        content: '',
        isAll: false,
        fileLists: [],
        status: 2,
        postTime: ''
      }
      if (this.detailInfo.msg_no) {
        this.noticeForm.msgNo = this.detailInfo.msg_no
        this.getNoticeContentMsg()
      } else {
        this.$message.error('查看公告出错')
      }
    },
    async getNoticeContentMsg() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminMessagesDetailsPost({
          msg_no: this.noticeForm.msgNo
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.modifyData = res.data
        this.initDefaultInfo()
      } else {
        this.$message.error(res.msg)
      }
    },
    initDefaultInfo() {
      this.noticeForm.status = this.modifyData.status
      let content = unescapeHTML(this.modifyData.content)
      // 系统到期服务通知 在"或联系管理员"前添加换行符和缩进 因后端更改模板风险大 先前端处理
      const modifiedText = content.replace(
        /，或联系管理员/,
        '，\n    或联系管理员'
      );
      const SPECIAL_PATTERN = /^(系统到期提醒通知|用户数量上限通知)$/;
      this.noticeForm.content = SPECIAL_PATTERN.test(this.modifyData.title) ? modifiedText : content;
      this.noticeForm.title = this.modifyData.title
      this.noticeForm.isAll = this.modifyData.receiver_type === 'all_company'
      if (this.modifyData.post_time) {
        this.noticeForm.postTime = this.modifyData.post_time
      }
      this.noticeForm.companyText = ''
      this.modifyData.receivers_name.forEach(v => {
        if (this.noticeForm.companyText) {
          this.noticeForm.companyText += `，${v.name}`
        } else {
          this.noticeForm.companyText = v.name
        }
      })
      if (this.modifyData.resource && this.modifyData.resource.length) {
        let files = this.modifyData.resource
        let time = new Date().getTime()
        this.noticeForm.fileLists = files.map(v => {
          time += 1
          let fileName = v.url.substring(v.url.lastIndexOf('/') + 1)
          // 后端加了tk校验文件，要把它截掉
          let isSplit = fileName.indexOf('?')
          if (isSplit > -1) {
            fileName = fileName.substring(0, isSplit)
          }
          return {
            name: fileName,
            status: 'success',
            uid: time,
            url: v.url,
            file_size: v.file_size
          }
        })
      }
    },
    async downloadHandle(file) {
      const response = await fetch(file.url)
      const blob = await response.blob()
      FileSaver.saveAs(blob, file.name)
    },
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return ''
      const mb = bytes / (1024 * 1024)
      // 如果小于0.01MB则写死0.01MB
      if (mb < 0.01) {
        return '0.01 MB'
      }
      return mb.toFixed(2) + ' MB'
    },
    truncateFilename(name) {
      if (name) {
        return name.length > 30 ? name.substring(0, 30) + '...' : name
      } else {
        return '--'
      }
    },
    editHandle() {
      this.$emit('editNotice', this.noticeForm.msgNo)
      this.drawerVisible = false
      // this.$router.push({
      //   name: 'SuperNoticeAdd',
      //   query: {
      //     msg_no: this.noticeForm.msgNo
      //   },
      //   params: {
      //     type: 'modify'
      //   }
      // })
    }
  },
  beforeDestroy() {}
}
</script>

<style lang="scss">
.setting-drawer {
  .el-drawer__header{
    margin-bottom: 0px;
    padding-bottom: 20px;
    background-color: #f4f4f4;
  }
  .el-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .drawer-content-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
  }
  .ps-drawer-footer {
    margin-top: auto;
    padding: 20px;
    border-top: 1px solid #ebeef5;
    background: #fff;
    .el-button{
      width: 100px;
    }
  }
}
.system-notice-detail {
  word-break: break-all;
  padding: 0 15px 15px 15px;
  .title {
    display: flex;
    align-items: center;
    padding: 20px 0;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }
  // border-radius: 10px;
  // border: 1px solid #d3d3d3;
  .content {
    max-width: 800px;
    padding: 10px;
    // border: 1px solid #d3d3d3;
    word-break: break-all;
    // background-color: rgba(242, 242, 242, 1);
    background-color: #F8F9FA;
    img {
      max-width: 100%;
      height: auto;
    }
    p {
      line-height: 1.5;
    }
  }
  .p-top-5{
    position: relative;
    top: 5px;
    width: 50px;
  }
  .margin-b-10 {
    margin-bottom: 10px;
  }
  .color-gray{
    color: #8c939d;
  }
}
</style>
