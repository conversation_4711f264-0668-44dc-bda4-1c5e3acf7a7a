<template>
  <div class="goods-sales container-wrapper">
    <div class="top-btn">
      <el-radio-group v-model="tabType" class="ps-radio-btn" @change="changeTab">
        <el-radio-button v-for="item in tabTypeList" :key="item.value" :label="item.value" v-permission="[`${item.permission}`]">
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
      <!-- <img @click="refreshHandle" class="refresh" src="@/assets/img/refresh.png" alt="" /> -->
    </div>
    <category-statistics
      ref="categoryStatistics"
      v-if="tabType === 'category_statistics'"
    ></category-statistics>
    <goods-statistics
      ref="goodsStatistics"
      v-if="tabType === 'goods_statistics'"
    ></goods-statistics>
  </div>
</template>

<script>
// import { debounce } from '@/utils'
import CategoryStatistics from './components/CategoryStatistics.vue'
import GoodsStatistics from './components/GoodsStatistics.vue'
export default {
  name: 'goodsSales',
  components: { CategoryStatistics, GoodsStatistics },
  data() {
    return {
      tabType: 'category_statistics',
      tabTypeList: [
        {
          value: 'category_statistics',
          label: '分类统计',
          permission: 'background_store.goods.category_sales_summary'
        },
        {
          value: 'goods_statistics',
          label: '商品统计',
          permission: 'background_store.goods.goods_sales_summary'
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    changeTab() {},
    refreshHandle() {
      // this.$refs.deliverChild.resetSearchHandle()
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.goods-sales {
  .top-btn {
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
  }
  .refresh {
    cursor: pointer;
    padding: 14px 0;
  }
}
</style>
